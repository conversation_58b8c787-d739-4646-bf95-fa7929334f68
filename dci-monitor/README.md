# ELK 集群部署文档

## 项目概述
基于 Kubernetes 的高可用 ELK (Elasticsearch, Logstash, Kibana) 日志收集与分析平台，主要特性包括：
- 三节点 Elasticsearch Master/Data 混合部署模式
- Filebeat 日志采集组件
- NFS 网络存储实现数据持久化
- 私有镜像仓库集成
- Kibana 可视化组件集成

## K8S yaml 目录结构
```bash
▸ ELK/                        # ELK Stack (Elasticsearch, Logstash, Kibana, Beats)
  ▸ E/                          # Elasticsearch核心组件
    ├─ ★ service.yaml           # 服务暴露配置（NodePort）
    └─ ★ elasticsearch.yaml     # StatefulSet工作负载定义(合并elasticsearch.yml)
  ▸ K/                          # Kibana可视化组件
    └─ ★ kibana.yaml            # 可视化界面部署配置
  ▸ L/                          # Logstash数据处理组件
    └─ ★ logstash.yaml          # Logstash部署配置(包含pipeline)
  ▸ B/                          # Beats 数据采集组件
    ▸ filebeat/                 # Filebeat日志采集组件（主要用于非K8S二进制部署）
      ├─ ★ install_filebeat.sh  # 安装脚本
      ├─ ★ filebeat.yml         # 标准配置文件
      └─ ★ filebeat.service     # Systemd服务单元文件
    ▸ metricbeat/               # Metricbeat监控组件（可部署于K8S）
      ├─ ★ metricbeat.yaml      # Kubernetes DaemonSet/Deployment 配置
      └─ ★ metricbeat.yml       # 监控模块配置文件 (ConfigMap)

▸ kafka/                      # Kafka 消息队列集群
  ├─ ★ kafka.yaml             # Kafka StatefulSet & Zookeeper Deployment 定义
  └─ ★ kafka-service.yaml     # Kafka 服务暴露配置

▸ TDengine/                   # TDengine 时序数据库集群
  ├─ ★ tdengine.yaml          # TDengine StatefulSet 定义
  ├─ ★ taosd-service.yaml     # TDengine 服务暴露配置
  └─ ★ taosd-config.yaml      # TDengine 配置 (ConfigMap)

▸ Telegraf/                   # Telegraf Agent 数据采集代理
  └─ ★ telegraf.yaml          # Telegraf DaemonSet & ConfigMap 定义

▸ src/dcimonitor/             # dcimonitor 服务端核心应用
  ├─ ★ dcimonitor-k8s.yaml    # dcimonitor Deployment & ConfigMap 定义
  └─ ★ dcimonitor-service.yaml # dcimonitor 服务暴露配置

▸ nfs_dci/                    # 持久化存储配置 (NFS)
  ├─ ★ pv-template.yaml       # 持久卷模板（NFS路径定义）
  ├─ ★ pvc-template.yaml      # 卷声明模板（存储类绑定）
  └─ ★ storageclass.yaml      # 动态存储类配置
```
## ES K8S快速部署

### 完整部署流程
```bash
# 1. 应用Elasticsearch核心配置
kubectl apply -f E/elasticsearch.yml -n dci
kubectl apply -f E/service.yaml -n dci
kubectl apply -f E/elasticsearch.yaml -n dci

# 2. 部署Kibana
kubectl apply -f K/kibana.yaml -n dci

# 3. 部署Logstash
kubectl apply -f L/logstash.yaml -n dci

# 4. 部署Filebeat日志采集（二进制方式）
# 自动检测本地压缩包存在性，存在则跳过下载
bash B/install_filebeat.sh

# 部署服务单元
cp B/filebeat.service /etc/systemd/system/
systemctl daemon-reload
systemctl enable --now filebeat

# 检查服务状态
systemctl status filebeat
```

### 检查部署状态
```bash
# 检查Elasticsearch状态
kubectl get pods -n dci -l app=elasticsearch
kubectl logs elasticsearch-0 -n dci

# 检查Kibana状态
kubectl get pods -n dci -l app=kibana
kubectl logs -n dci -l app=kibana

```

## 组件说明

### Elasticsearch
- 负责数据存储和搜索
- 采用StatefulSet部署，保证数据持久性
- 通过NFS实现数据持久化

### Filebeat ES sidecar
- 负责 ES 日志采集
- 以 **Sidecar 模式** 集成到 Elasticsearch Pod 中（每个 ES 实例对应 1 个 Filebeat）
- 通过共享卷挂载方式采集容器日志
- 配置自动发现Kubernetes容器日志
- 收集的日志直接发送至Elasticsearch

进入 Filebeat 边车容器的 Shell 环境

```bash
# 1. 查看当前运行的 Elasticsearch Pod
kubectl get pods -n dci -l app=elasticsearch

# 2. 进入指定 Pod 的边车容器（示例使用 elasticsearch-0）
kubectl exec -n dci elasticsearch-0 -c filebeat-es-sidecar -it -- sh

# 若容器包含 bash（非 Alpine 镜像）
kubectl exec -n dci elasticsearch-0 -c filebeat-es-sidecar -it -- bash
```

### Kibana
- Web界面，提供数据可视化
- 资源配置：内存1Gi，CPU 1核
- 通过NodePort (5601) 暴露服务

## 使用指南

### 查看日志数据
1. 访问Kibana: `http://<节点IP>:5601`
2. 创建索引模式
    - 导航至 Management > Stack Management > Kibana > Index Patterns
    - 创建新索引模式 `filebeat-*`
    - 选择时间字段 `@timestamp`
3. 使用Discover功能查看日志
    - 点击左侧导航栏中的 "Discover"
    - 选择之前创建的索引模式
    - 使用过滤器和查询来筛选日志

## 维护操作

### 重启服务
```bash
# 重启Elasticsearch
kubectl rollout restart statefulset elasticsearch -n dci

# 重启Kibana
kubectl rollout restart deployment kibana -n dci

### 维护Filebeat服务
```bash
# 启动/停止服务
systemctl start filebeat
systemctl stop filebeat

# 查看运行状态
systemctl status filebeat

# 查看日志
journalctl -u filebeat -f

# 配置文件路径
/etc/filebeat/filebeat.yml
```

### 强制重启
```bash
# 强制重启Elasticsearch
kubectl replace -f E/elasticsearch.yml -n dci --force
kubectl apply -f E/service.yaml -n dci
kubectl replace -f E/elasticsearch.yaml -n dci --force

# 强制重启Kibana
kubectl replace -f K/kibana.yaml -n dci --force

# 强制重启Filebeat
kubectl replace -f B/filebeat.yaml -n dci --force
```
## Docker镜像

### 当前使用的镜像

```bash
docker pull docker.1ms.run/bitnami/elasticsearch
docker pull docker.1ms.run/bitnami/kibana
docker pull docker.elastic.co/beats/metricbeat:8.17.3
```
当服务器无法直接下载镜像时，先在个人本机下载，再打tag上传 （elasticsearch为例）。

```bash
docker pull docker.1ms.run/bitnami/elasticsearch
docker tag docker.1ms.run/bitnami/elasticsearch:latest 10.249.149.56:6000/elasticsearch:latest
docker login --username=admin 10.249.149.56:6000
docker push 10.249.149.56:6000/elasticsearch:latest
```
yaml 中修改为：10.249.149.56:6000/elasticsearch:latest

## 重要说明

涉及数据，通过命令`kubectl delete -f elasticsearch.yaml -n dci` 删除pod，数据库pvc删除，但pv不删除，实现数据保留。若需要删除，需执行如 :

```bash
# 删除PV
kubectl delete pv pvc-584d5b99-9b75-4f08-a360-ea185fe0c627

# 删除NFS存储目录（请确认目录存在）
ssh root@10.249.149.36 "rm -rf /opt/dci/dci-test-pvc-pvc-584d5b99*"
```
