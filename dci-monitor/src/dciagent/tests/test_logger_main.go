//go:build test
// +build test

package main

import (
	"fmt"
	"os"
	"reflect"

	"common/logger"

	"go.uber.org/zap"
)

// 日志系统测试程序
func main() {
	// 设置测试模式，避免创建真实的日志文件
	os.Setenv("LOGGER_TEST_MODE", "true")

	// 初始化common日志系统
	logger.InitLogger()

	fmt.Println("=== 直接使用common的日志函数 ===")
	// 测试common直接日志接口
	logger.Info("来自common的Info日志")
	logger.Debug("来自common的Debug日志")
	logger.Warn("来自common的Warn日志", zap.String("key", "value"))
	logger.Error("来自common的Error日志", zap.Int("code", 500))

	fmt.Println("\n=== 测试GetCompatLogger返回的实例 ===")
	// 直接从common获取兼容Logger
	directCompatLogger := logger.GetCompatLogger()
	fmt.Printf("directCompatLogger类型: %T\n", directCompatLogger)
	fmt.Printf("directCompatLogger是否为nil: %v\n", directCompatLogger == nil)
	fmt.Printf("directCompatLogger值: %+v\n", directCompatLogger)
	directCompatLogger.Info("直接从common获取的Compatible Logger")
	directCompatLogger.Infof("直接从common获取的格式化日志: %s", "测试")

	fmt.Println("\n=== 使用兼容接口的方法 ===")
	log := logger.GetCompatLogger()
	fmt.Printf("logger.GetCompatLogger()类型: %T\n", log)
	fmt.Printf("logger.GetCompatLogger()是否为nil: %v\n", log == nil)
	fmt.Printf("logger.GetCompatLogger()值: %+v\n", log)
	// 使用反射检查内部字段
	fmt.Printf("logger.GetCompatLogger()反射值: %v\n", reflect.ValueOf(log))

	// 尝试调用方法
	fmt.Println("尝试调用Info方法...")
	log.Info("来自兼容接口的Info日志")
	fmt.Println("Info方法调用完成")

	fmt.Println("尝试调用Infof方法...")
	log.Infof("来自兼容接口的格式化Info日志: %s", "测试")
	fmt.Println("Infof方法调用完成")

	// 同步日志缓冲
	fmt.Println("\n=== 同步日志缓冲 ===")
	err := logger.Sync()
	fmt.Printf("logger.Sync()返回: %v\n", err)

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("1. 成功使用common的日志系统")
	fmt.Println("2. 类型正确，都是*logger.ZapAdapter")
}
