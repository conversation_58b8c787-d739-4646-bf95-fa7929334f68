package config

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
)

// TelegrafConfig 包含与Telegraf相关的配置项
type TelegrafConfig struct {
	// Telegraf主目录路径
	Home string `mapstructure:"home"`

	// Telegraf配置文件路径
	ConfigFile string `mapstructure:"config"`

	// 配置文件目录路径
	ConfigDir string `mapstructure:"config_dir"`

	// 日志目录路径
	LogDir string `mapstructure:"log_dir"`

	// 备份目录路径
	BackupDir string `mapstructure:"backup_dir"`

	// 版本信息目录路径
	VersionsDir string `mapstructure:"versions_dir"`

	// 当前使用的版本
	Version string `mapstructure:"version"`

	// 进程控制超时设置
	StartTimeout string `mapstructure:"start_timeout"`
	StopTimeout  string `mapstructure:"stop_timeout"`
}

// LoadConfig 加载Telegraf配置
func LoadConfig() (*TelegrafConfig, error) {
	cfg := &TelegrafConfig{}

	// 从viper获取配置
	home := viper.GetString("telegraf.home")
	if home == "" {
		home = getDefaultHome()
	}
	cfg.Home = home

	// 根据主目录设置各子目录路径
	cfg.ConfigFile = viper.GetString("telegraf.config")
	if cfg.ConfigFile == "" {
		cfg.ConfigFile = filepath.Join(home, "conf", "telegraf.conf")
	}

	cfg.ConfigDir = viper.GetString("telegraf.config_dir")
	if cfg.ConfigDir == "" {
		cfg.ConfigDir = filepath.Join(home, "conf", "telegraf.d")
	}

	cfg.LogDir = viper.GetString("telegraf.log_dir")
	if cfg.LogDir == "" {
		cfg.LogDir = filepath.Join(home, "logs")
	}

	cfg.BackupDir = viper.GetString("telegraf.backup_dir")
	if cfg.BackupDir == "" {
		cfg.BackupDir = filepath.Join(home, "backup")
	}

	cfg.VersionsDir = viper.GetString("telegraf.versions_dir")
	if cfg.VersionsDir == "" {
		cfg.VersionsDir = filepath.Join(home, "versions")
	}

	cfg.Version = viper.GetString("telegraf.version")
	if cfg.Version == "" {
		cfg.Version = "latest"
	}

	cfg.StartTimeout = viper.GetString("telegraf.start_timeout")
	if cfg.StartTimeout == "" {
		cfg.StartTimeout = "10s"
	}

	cfg.StopTimeout = viper.GetString("telegraf.stop_timeout")
	if cfg.StopTimeout == "" {
		cfg.StopTimeout = "30s"
	}

	return cfg, nil
}

// 获取默认的Telegraf主目录路径
func getDefaultHome() string {
	// 尝试环境变量
	if envHome := os.Getenv("DCI_TELEGRAF_HOME"); envHome != "" {
		return envHome
	}

	// 返回默认路径
	return "/opt/dci/dciagent"
}

// GetBinaryPath 获取Telegraf二进制文件路径
func (c *TelegrafConfig) GetBinaryPath() string {
	return filepath.Join(c.Home, "bin", "telegraf")
}

// ValidateConfig 验证配置是否有效
func (c *TelegrafConfig) ValidateConfig() error {
	// 检查必要的目录和文件
	if c.Home == "" {
		return fmt.Errorf("未指定Telegraf主目录路径")
	}

	// 根据需要可以添加更多验证逻辑...

	return nil
}
