package control

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/shirou/gopsutil/v3/process"

	"common/logger"
	"dciagent/internal/telegraf/config"
)

// Controller 提供Telegraf进程控制功能
type Controller struct {
	Config *config.TelegrafConfig
	Logger logger.Logger
}

// ProcessInfo 存储进程信息
type ProcessInfo struct {
	PID      int
	Status   string
	Command  string
	RunTime  time.Duration
	MemUsage uint64
	CPUUsage float64
}

// NewController 创建一个新的控制器
func NewController(cfg *config.TelegrafConfig, log logger.Logger) *Controller {
	return &Controller{
		Config: cfg,
		Logger: log,
	}
}

// Start 启动Telegraf进程
func (c *Controller) Start() error {
	// 检查是否已在运行
	if running, _ := c.IsRunning(); running {
		return fmt.Errorf("Telegraf已在运行中")
	}

	// 获取二进制路径
	binaryPath := c.Config.GetBinaryPath()

	// 检查二进制文件是否存在
	if _, err := os.Stat(binaryPath); os.IsNotExist(err) {
		return fmt.Errorf("Telegraf二进制文件不存在: %s", binaryPath)
	}

	c.Logger.Info("启动Telegraf进程...")

	// 准备命令
	cmd := exec.Command(binaryPath, "--config", c.Config.ConfigFile, "--config-directory", c.Config.ConfigDir)

	// 设置环境变量
	cmd.Env = os.Environ()

	// 设置标准输出和错误输出
	logFilePath := filepath.Join(c.Config.LogDir, "telegraf.log")
	logFile, err := os.OpenFile(logFilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("无法打开日志文件: %v", err)
	}
	cmd.Stdout = logFile
	cmd.Stderr = logFile

	// 启动进程
	if err := cmd.Start(); err != nil {
		logFile.Close()
		return fmt.Errorf("启动Telegraf失败: %v", err)
	}

	// 将PID写入PID文件
	pidFilePath := filepath.Join(c.Config.Home, "run", "telegraf.pid")
	if err := os.MkdirAll(filepath.Dir(pidFilePath), 0755); err != nil {
		return fmt.Errorf("创建PID文件目录失败: %v", err)
	}
	if err := os.WriteFile(pidFilePath, []byte(strconv.Itoa(cmd.Process.Pid)), 0644); err != nil {
		return fmt.Errorf("写入PID文件失败: %v", err)
	}

	c.Logger.Info("Telegraf已启动，PID: %d", cmd.Process.Pid)

	// 分离进程，让它在后台运行
	if err := cmd.Process.Release(); err != nil {
		return fmt.Errorf("分离Telegraf进程失败: %v", err)
	}

	return nil
}

// Stop 停止Telegraf进程
func (c *Controller) Stop() error {
	pid, err := c.GetPID()
	if err != nil {
		return fmt.Errorf("获取Telegraf PID失败: %v", err)
	}

	if pid <= 0 {
		return fmt.Errorf("Telegraf未运行")
	}

	c.Logger.Info("正在停止Telegraf进程 (PID: %d)...", pid)

	// 尝试发送SIGTERM信号
	proc, err := os.FindProcess(pid)
	if err != nil {
		return fmt.Errorf("查找进程失败: %v", err)
	}

	// 发送SIGTERM信号
	if err := proc.Signal(syscall.SIGTERM); err != nil {
		c.Logger.Warn("发送SIGTERM信号失败，尝试SIGKILL: %v", err)
		if err := proc.Signal(syscall.SIGKILL); err != nil {
			return fmt.Errorf("无法终止Telegraf进程: %v", err)
		}
	}

	// 等待进程退出
	timeout, _ := time.ParseDuration(c.Config.StopTimeout)
	startTime := time.Now()
	for time.Since(startTime) < timeout {
		if running, _ := c.IsRunning(); !running {
			break
		}
		time.Sleep(500 * time.Millisecond)
	}

	// 检查是否仍在运行
	if running, _ := c.IsRunning(); running {
		return fmt.Errorf("停止Telegraf进程超时")
	}

	// 删除PID文件
	pidFilePath := filepath.Join(c.Config.Home, "run", "telegraf.pid")
	if err := os.Remove(pidFilePath); err != nil && !os.IsNotExist(err) {
		c.Logger.Warn("无法删除PID文件: %v", err)
	}

	c.Logger.Info("Telegraf已停止")
	return nil
}

// Restart 重启Telegraf进程
func (c *Controller) Restart() error {
	c.Logger.Info("正在重启Telegraf...")

	// 停止进程
	if running, _ := c.IsRunning(); running {
		if err := c.Stop(); err != nil {
			return fmt.Errorf("停止Telegraf失败: %v", err)
		}
	}

	// 短暂延迟，确保进程完全退出
	time.Sleep(1 * time.Second)

	// 启动进程
	if err := c.Start(); err != nil {
		return fmt.Errorf("启动Telegraf失败: %v", err)
	}

	return nil
}

// Status 获取Telegraf状态信息
func (c *Controller) Status() (*ProcessInfo, error) {
	pid, err := c.GetPID()
	if err != nil || pid <= 0 {
		return nil, fmt.Errorf("Telegraf未运行")
	}

	// 使用gopsutil获取进程信息
	p, err := process.NewProcess(int32(pid))
	if err != nil {
		return nil, fmt.Errorf("获取进程信息失败: %v", err)
	}

	info := &ProcessInfo{
		PID: pid,
	}

	// 获取进程状态
	status, err := p.Status()
	if err == nil && len(status) > 0 {
		// 新版gopsutil返回[]string，取第一个值
		info.Status = status[0]
	}

	// 获取命令行
	cmdline, err := p.Cmdline()
	if err == nil {
		info.Command = cmdline
	}

	// 获取运行时间
	createTime, err := p.CreateTime()
	if err == nil {
		info.RunTime = time.Since(time.Unix(createTime/1000, 0))
	}

	// 获取内存使用
	memInfo, err := p.MemoryInfo()
	if err == nil && memInfo != nil {
		info.MemUsage = memInfo.RSS
	}

	// 获取CPU使用率
	cpuPercent, err := p.CPUPercent()
	if err == nil {
		info.CPUUsage = cpuPercent
	}

	return info, nil
}

// IsRunning 检查Telegraf是否正在运行
func (c *Controller) IsRunning() (bool, error) {
	pid, err := c.GetPID()
	if err != nil || pid <= 0 {
		return false, nil
	}

	// 检查进程是否存在
	proc, err := os.FindProcess(pid)
	if err != nil {
		return false, nil
	}

	// 在Unix/Linux系统上，FindProcess总是成功，需要发送信号0来检查进程是否真的存在
	err = proc.Signal(syscall.Signal(0))
	if err != nil {
		return false, nil
	}

	return true, nil
}

// GetPID 获取Telegraf的PID
func (c *Controller) GetPID() (int, error) {
	pidFilePath := filepath.Join(c.Config.Home, "run", "telegraf.pid")

	// 检查PID文件是否存在
	if _, err := os.Stat(pidFilePath); os.IsNotExist(err) {
		return 0, nil
	}

	// 读取PID文件
	content, err := os.ReadFile(pidFilePath)
	if err != nil {
		return 0, fmt.Errorf("读取PID文件失败: %v", err)
	}

	// 解析PID
	pid, err := strconv.Atoi(strings.TrimSpace(string(content)))
	if err != nil {
		return 0, fmt.Errorf("PID文件内容无效: %v", err)
	}

	return pid, nil
}
