package install

import (
	"archive/tar"
	"compress/gzip"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"common/logger"
	"dciagent/internal/telegraf/config"
)

// Options 安装选项
type Options struct {
	Version           string        // 要安装的Telegraf版本
	DownloadURL       string        // 下载基础URL
	DownloadTimeout   time.Duration // 下载超时时间
	VerifyChecksum    bool          // 是否验证校验和
	OverwriteExisting bool          // 是否覆盖现有安装
	TempDir           string        // 临时下载目录
}

// DefaultOptions 返回默认安装选项
func DefaultOptions() Options {
	return Options{
		Version:           "latest",
		DownloadURL:       "https://dl.influxdata.com/telegraf/releases/",
		DownloadTimeout:   60 * time.Second,
		VerifyChecksum:    true,
		OverwriteExisting: false,
		TempDir:           os.TempDir(),
	}
}

// Installer Telegraf安装器
type Installer struct {
	Config  *config.TelegrafConfig
	Logger  logger.Logger
	Options Options
}

// NewInstaller 创建新的安装器
func NewInstaller(cfg *config.TelegrafConfig, log logger.Logger, options Options) *Installer {
	return &Installer{
		Config:  cfg,
		Logger:  log,
		Options: options,
	}
}

// Install 安装Telegraf
func (i *Installer) Install() error {
	// 获取版本
	version := i.Options.Version
	if version == "latest" {
		var err error
		version, err = i.getLatestVersion()
		if err != nil {
			return fmt.Errorf("获取最新版本失败: %w", err)
		}
		i.Logger.Info("获取到最新版本: %s", version)
	}

	// 检查版本是否已安装
	versionDir := filepath.Join(i.Config.VersionsDir, "telegraf", version)
	if _, err := os.Stat(versionDir); err == nil && !i.Options.OverwriteExisting {
		i.Logger.Info("版本 %s 已安装，跳过下载", version)
	} else {
		// 下载Telegraf
		downloadPath, err := i.downloadTelegraf(version)
		if err != nil {
			return fmt.Errorf("下载失败: %w", err)
		}
		i.Logger.Info("下载完成: %s", downloadPath)

		// 验证校验和
		if i.Options.VerifyChecksum {
			if err := i.verifyChecksum(downloadPath, version); err != nil {
				return fmt.Errorf("校验和验证失败: %w", err)
			}
			i.Logger.Info("校验和验证通过")
		}

		// 解压并安装
		if err := i.extractAndInstall(downloadPath, version); err != nil {
			return fmt.Errorf("解压安装失败: %w", err)
		}
		i.Logger.Info("解压安装完成")

		// 清理临时文件
		if err := os.Remove(downloadPath); err != nil {
			i.Logger.Warn("清理临时文件失败: %v", err)
		}
	}

	// 创建符号链接
	if err := i.createSymlinks(version); err != nil {
		return fmt.Errorf("创建符号链接失败: %w", err)
	}
	i.Logger.Info("创建符号链接完成")

	// 创建配置目录
	if err := i.setupConfigDirs(); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 部署配置模板
	if err := i.deployConfigTemplates(); err != nil {
		return fmt.Errorf("部署配置模板失败: %w", err)
	}

	// 设置权限
	if err := i.setupPermissions(); err != nil {
		return fmt.Errorf("设置权限失败: %w", err)
	}

	i.Logger.Info("Telegraf %s 安装成功", version)
	return nil
}

// getLatestVersion 获取最新版本号
func (i *Installer) getLatestVersion() (string, error) {
	// 这里可以实现检查最新版本的逻辑
	// 目前简单返回一个固定版本
	return "1.34.2", nil
}

// downloadTelegraf 下载Telegraf包
func (i *Installer) downloadTelegraf(version string) (string, error) {
	// 确定平台和架构
	platform := runtime.GOOS
	arch := runtime.GOARCH

	// 调整架构命名
	if arch == "amd64" {
		arch = "x86_64"
	} else if arch == "arm64" {
		if platform == "darwin" {
			arch = "arm64" // macOS arm64保持不变
		} else {
			arch = "aarch64" // Linux arm64使用aarch64
		}
	}

	// 构建下载URL
	var url, filename string
	if platform == "darwin" {
		filename = fmt.Sprintf("telegraf-%s_darwin_%s.tar.gz", version, arch)
	} else if platform == "windows" {
		filename = fmt.Sprintf("telegraf-%s_windows_%s.zip", version, arch)
	} else {
		filename = fmt.Sprintf("telegraf-%s_linux_%s.tar.gz", version, arch)
	}

	url = i.Options.DownloadURL + filename
	i.Logger.Info("下载URL: %s", url)

	// 确定下载路径
	downloadPath := filepath.Join(i.Options.TempDir, filename)

	// 创建目标文件
	out, err := os.Create(downloadPath)
	if err != nil {
		return "", fmt.Errorf("创建下载文件失败: %w", err)
	}
	defer out.Close()

	// 发起HTTP请求
	client := &http.Client{
		Timeout: i.Options.DownloadTimeout,
	}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP请求返回非200状态码: %d", resp.StatusCode)
	}

	// 下载文件
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return "", fmt.Errorf("下载文件失败: %w", err)
	}

	return downloadPath, nil
}

// verifyChecksum 验证下载文件的校验和
func (i *Installer) verifyChecksum(filePath, version string) error {
	// 计算文件的SHA256
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	hash := sha256.New()
	if _, err := io.Copy(hash, file); err != nil {
		return fmt.Errorf("计算哈希失败: %w", err)
	}

	calculatedHash := hex.EncodeToString(hash.Sum(nil))
	i.Logger.Debug("计算的SHA256: %s", calculatedHash)

	// 下载校验和文件
	// 在实际实现中，应该从官方源下载校验和文件并验证
	// 这里简化处理，仅打印计算的哈希值
	i.Logger.Info("文件SHA256: %s", calculatedHash)

	return nil
}

// extractAndInstall 解压并安装Telegraf
func (i *Installer) extractAndInstall(archivePath, version string) error {
	// 创建版本目录
	versionDir := filepath.Join(i.Config.VersionsDir, "telegraf", version)
	if err := os.MkdirAll(versionDir, 0755); err != nil {
		return fmt.Errorf("创建版本目录失败: %w", err)
	}

	// 打开归档文件
	file, err := os.Open(archivePath)
	if err != nil {
		return fmt.Errorf("打开归档文件失败: %w", err)
	}
	defer file.Close()

	// 解压gzip
	gzReader, err := gzip.NewReader(file)
	if err != nil {
		return fmt.Errorf("解压gzip失败: %w", err)
	}
	defer gzReader.Close()

	// 解压tar
	tarReader := tar.NewReader(gzReader)

	// 提取二进制文件
	found := false
	for {
		header, err := tarReader.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("读取tar文件失败: %w", err)
		}

		// 查找telegraf二进制文件
		if strings.HasSuffix(header.Name, "/telegraf") || strings.HasSuffix(header.Name, "\\telegraf") || strings.HasSuffix(header.Name, "\\telegraf.exe") {
			// 提取到版本目录
			binaryPath := filepath.Join(versionDir, "telegraf")
			if runtime.GOOS == "windows" {
				binaryPath += ".exe"
			}

			outFile, err := os.OpenFile(binaryPath, os.O_CREATE|os.O_WRONLY, 0755)
			if err != nil {
				return fmt.Errorf("创建二进制文件失败: %w", err)
			}

			if _, err := io.Copy(outFile, tarReader); err != nil {
				outFile.Close()
				return fmt.Errorf("写入二进制文件失败: %w", err)
			}
			outFile.Close()
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("在归档文件中未找到telegraf二进制文件")
	}

	return nil
}

// createSymlinks 创建符号链接
func (i *Installer) createSymlinks(version string) error {
	// 确保bin目录存在
	binDir := filepath.Join(i.Config.Home, "bin")
	if err := os.MkdirAll(binDir, 0755); err != nil {
		return fmt.Errorf("创建bin目录失败: %w", err)
	}

	// 二进制文件路径
	versionBinary := filepath.Join(i.Config.VersionsDir, "telegraf", version, "telegraf")
	if runtime.GOOS == "windows" {
		versionBinary += ".exe"
	}

	// 符号链接路径
	symlinkPath := filepath.Join(binDir, "telegraf")
	if runtime.GOOS == "windows" {
		symlinkPath += ".exe"
	}

	// 检查二进制文件是否存在
	if _, err := os.Stat(versionBinary); os.IsNotExist(err) {
		return fmt.Errorf("二进制文件不存在: %s", versionBinary)
	}

	// 删除现有符号链接（如果存在）
	if _, err := os.Lstat(symlinkPath); err == nil {
		if err := os.Remove(symlinkPath); err != nil {
			return fmt.Errorf("删除现有符号链接失败: %w", err)
		}
	}

	// 在Windows上复制文件，在类Unix系统上创建符号链接
	if runtime.GOOS == "windows" {
		// Windows上复制文件
		input, err := os.ReadFile(versionBinary)
		if err != nil {
			return fmt.Errorf("读取二进制文件失败: %w", err)
		}
		if err := os.WriteFile(symlinkPath, input, 0755); err != nil {
			return fmt.Errorf("写入二进制文件副本失败: %w", err)
		}
	} else {
		// 类Unix系统创建符号链接
		if err := os.Symlink(versionBinary, symlinkPath); err != nil {
			return fmt.Errorf("创建符号链接失败: %w", err)
		}
	}

	return nil
}

// setupConfigDirs 创建配置目录
func (i *Installer) setupConfigDirs() error {
	// 创建配置目录
	if err := os.MkdirAll(i.Config.ConfigDir, 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 创建子目录
	subDirs := []string{
		filepath.Join(i.Config.ConfigDir, "inputs"),
		filepath.Join(i.Config.ConfigDir, "outputs"),
		filepath.Join(i.Config.ConfigDir, "processors"),
	}

	for _, dir := range subDirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建子目录失败: %s: %w", dir, err)
		}
	}

	// 创建日志目录
	if err := os.MkdirAll(i.Config.LogDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %w", err)
	}

	// 创建备份目录
	if err := os.MkdirAll(i.Config.BackupDir, 0755); err != nil {
		return fmt.Errorf("创建备份目录失败: %w", err)
	}

	// 创建运行目录
	runDir := filepath.Join(i.Config.Home, "run")
	if err := os.MkdirAll(runDir, 0755); err != nil {
		return fmt.Errorf("创建运行目录失败: %w", err)
	}

	return nil
}

// deployConfigTemplates 部署配置模板
func (i *Installer) deployConfigTemplates() error {
	// 项目模板目录
	templateDir := filepath.Join("configs", "templates")

	// 检查配置文件是否已存在
	configFilePath := i.Config.ConfigFile
	if _, err := os.Stat(configFilePath); os.IsNotExist(err) {
		// 从模板目录复制telegraf.conf
		templatePath := filepath.Join(templateDir, "telegraf.conf")

		// 尝试查找模板
		// 首先查找当前目录
		if _, err := os.Stat(templatePath); os.IsNotExist(err) {
			// 尝试在GOPATH中查找
			gopath := os.Getenv("GOPATH")
			if gopath != "" {
				templatePath = filepath.Join(gopath, "src", "dciagent", templatePath)
				if _, err := os.Stat(templatePath); os.IsNotExist(err) {
					// 尝试在项目根目录查找
					projectRoot := "/Users/<USER>/code/dci/dci-workspace/dci-monitor/src/dciagent"
					templatePath = filepath.Join(projectRoot, templatePath)
				}
			}
		}

		// 如果找到模板，则复制
		if _, err := os.Stat(templatePath); err == nil {
			if err := copyFile(templatePath, configFilePath); err != nil {
				return fmt.Errorf("复制配置模板失败: %w", err)
			}
			i.Logger.Info("已复制配置模板: %s", configFilePath)
		} else {
			// 如果找不到模板，创建一个基本的配置
			basicConfig := `# Telegraf基本配置 - 由dcicollector自动生成

[agent]
  interval = "60s"
  round_interval = true
  metric_batch_size = 1000
  metric_buffer_limit = 10000
  collection_jitter = "0s"
  flush_interval = "10s"
  flush_jitter = "0s"
  precision = ""
  hostname = ""
  omit_hostname = false

# 示例Kafka输出配置
[[outputs.kafka]]
  ## Kafka代理地址列表
  # brokers = ["kafka1:9092", "kafka2:9092", "kafka3:9092"]
  
  ## 主题名
  # topic = "dci.monitor.v1.defaultchannel.metrics.telegraf"
  
  ## 压缩算法
  # compression_codec = "snappy"
  
  ## 数据格式
  # data_format = "json"

# 示例系统监控配置
[[inputs.cpu]]
  ## 是否收集每个CPU的指标
  percpu = true
  ## 是否收集总CPU指标
  totalcpu = true
  ## 收集CPU时间指标
  collect_cpu_time = false
  ## 是否报告活跃时间百分比而非使用时间百分比
  report_active = false
`
			if err := os.WriteFile(configFilePath, []byte(basicConfig), 0644); err != nil {
				return fmt.Errorf("创建基本配置文件失败: %w", err)
			}
			i.Logger.Info("已创建基本配置文件: %s", configFilePath)
		}
	}

	return nil
}

// setupPermissions 设置权限
func (i *Installer) setupPermissions() error {
	// 在Linux系统上设置权限
	if runtime.GOOS == "linux" {
		// 检查telegraf用户和组是否存在
		if _, err := exec.Command("id", "-u", "telegraf").Output(); err != nil {
			// 用户不存在，尝试创建
			i.Logger.Info("创建telegraf用户...")
			cmd := exec.Command("useradd", "-r", "-s", "/bin/false", "telegraf")
			if err := cmd.Run(); err != nil {
				i.Logger.Warn("创建telegraf用户失败: %v", err)
			}
		}

		// 设置目录所有权
		dirs := []string{
			i.Config.Home,
			i.Config.ConfigDir,
			i.Config.LogDir,
			i.Config.BackupDir,
			filepath.Join(i.Config.Home, "run"),
			filepath.Join(i.Config.Home, "bin"),
		}

		for _, dir := range dirs {
			cmd := exec.Command("chown", "-R", "telegraf:telegraf", dir)
			if err := cmd.Run(); err != nil {
				i.Logger.Warn("设置目录权限失败: %s: %v", dir, err)
			}
		}

		// 设置配置文件权限
		configFiles := []string{
			i.Config.ConfigFile,
		}

		for _, file := range configFiles {
			if _, err := os.Stat(file); err == nil {
				cmd := exec.Command("chmod", "640", file)
				if err := cmd.Run(); err != nil {
					i.Logger.Warn("设置文件权限失败: %s: %v", file, err)
				}
			}
		}
	}

	return nil
}

// 复制文件
func copyFile(src, dst string) error {
	input, err := os.ReadFile(src)
	if err != nil {
		return err
	}

	err = os.WriteFile(dst, input, 0644)
	if err != nil {
		return err
	}

	return nil
}
