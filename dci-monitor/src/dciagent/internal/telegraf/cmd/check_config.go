package cmd

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"github.com/spf13/cobra"

	"common/logger"
	"dciagent/internal/telegraf/config"
)

var (
	checkLevel  string
	checkFormat string
)

// CheckConfigCmd 检查Telegraf配置文件
var CheckConfigCmd = &cobra.Command{
	Use:   "check-config",
	Short: "检查Telegraf配置文件",
	Long:  `检查Telegraf配置文件的正确性，支持不同的检查级别和输出格式。`,
	RunE: func(cmd *cobra.Command, args []string) error {
		log := logger.GetCompatLogger()

		// 加载配置
		cfg, err := config.LoadConfig()
		if err != nil {
			log.Error("加载配置失败: %v", err)
			return err
		}

		// 获取二进制路径
		binaryPath := cfg.GetBinaryPath()

		// 检查二进制文件是否存在
		if _, err := os.Stat(binaryPath); os.IsNotExist(err) {
			return fmt.Errorf("Telegraf二进制文件不存在: %s", binaryPath)
		}

		// 准备命令参数
		args = []string{"--config", cfg.ConfigFile}

		// 如果有配置目录，也添加
		if _, err := os.Stat(cfg.ConfigDir); !os.IsNotExist(err) {
			args = append(args, "--config-directory", cfg.ConfigDir)
		}

		// 添加--test标志用于测试配置
		args = append(args, "--test")

		// 执行命令
		log.Info("检查Telegraf配置: %s %s", binaryPath, strings.Join(args, " "))

		cmd2 := exec.Command(binaryPath, args...)
		output, err := cmd2.CombinedOutput()

		// 打印输出
		fmt.Println(string(output))

		// 根据检查级别执行额外检查
		if err == nil && (checkLevel == "strict" || checkLevel == "full") {
			// 检查配置文件权限
			if err := checkFilePermissions(cfg.ConfigFile); err != nil {
				log.Warn("配置文件权限检查: %v", err)
				if checkLevel == "strict" {
					return err
				}
			} else {
				fmt.Println("配置文件权限检查: 通过")
			}

			// 检查Kafka输出配置
			if found, err := checkKafkaOutputConfig(cfg.ConfigFile, cfg.ConfigDir); err != nil {
				log.Warn("Kafka输出配置检查: %v", err)
				if checkLevel == "strict" {
					return err
				}
			} else if found {
				fmt.Println("Kafka输出配置检查: 通过")
			} else if checkLevel == "strict" {
				return fmt.Errorf("未找到必要的Kafka输出配置")
			}
		}

		if err != nil {
			log.Error("配置检查失败: %v", err)
			return fmt.Errorf("配置检查失败")
		}

		fmt.Println("配置检查成功")
		return nil
	},
}

// 检查文件权限
func checkFilePermissions(configFile string) error {
	info, err := os.Stat(configFile)
	if err != nil {
		return fmt.Errorf("无法获取配置文件信息: %v", err)
	}

	mode := info.Mode()

	// 检查文件权限，确保其他用户无法写入
	if mode&0002 != 0 {
		return fmt.Errorf("配置文件权限过于开放，其他用户具有写入权限")
	}

	return nil
}

// 检查Kafka输出配置
func checkKafkaOutputConfig(configFile, configDir string) (bool, error) {
	// 读取主配置文件
	content, err := os.ReadFile(configFile)
	if err != nil {
		return false, fmt.Errorf("无法读取配置文件: %v", err)
	}

	// 简单检查主配置文件中是否包含Kafka输出
	if strings.Contains(string(content), "outputs.kafka") {
		return true, nil
	}

	// 检查配置目录中的文件
	if configDir != "" {
		files, err := filepath.Glob(filepath.Join(configDir, "outputs", "*.conf"))
		if err != nil {
			return false, fmt.Errorf("无法读取配置目录: %v", err)
		}

		for _, f := range files {
			content, err := os.ReadFile(f)
			if err != nil {
				continue
			}

			if strings.Contains(string(content), "outputs.kafka") {
				return true, nil
			}
		}
	}

	return false, nil
}

func init() {
	CheckConfigCmd.Flags().StringVarP(&checkLevel, "level", "l", "basic", "检查级别 (basic, full, strict)")
	CheckConfigCmd.Flags().StringVarP(&checkFormat, "format", "f", "text", "输出格式 (text, json)")
}
