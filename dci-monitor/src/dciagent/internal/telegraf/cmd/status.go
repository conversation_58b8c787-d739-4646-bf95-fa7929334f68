package cmd

import (
	"fmt"
	"os"
	"text/tabwriter"
	"time"

	"github.com/spf13/cobra"
)

// StatusCmd 获取Telegraf状态
var StatusCmd = &cobra.Command{
	Use:   "status",
	Short: "检查Telegraf运行状态",
	Long:  `检查Telegraf数据采集进程的运行状态，包括PID、内存使用、CPU使用和运行时间等信息。`,
	RunE: func(cmd *cobra.Command, args []string) error {

		// 使用全局共享的控制器
		// 检查是否在运行
		running, err := ctrl.IsRunning()
		if err != nil {
			log.Error("检查Telegraf状态失败:", err)
			return err
		}

		if !running {
			fmt.Println("Telegraf未运行")
			return nil
		}

		// 获取状态信息
		info, err := ctrl.Status()
		if err != nil {
			log.Error("获取Telegraf状态信息失败:", err)
			return fmt.Errorf("获取Telegraf状态信息失败: %v", err)
		}

		// 显示状态信息
		w := tabwriter.NewWriter(os.Stdout, 0, 0, 2, ' ', 0)
		fmt.Fprintln(w, "状态\tPID\t运行时间\t内存使用\tCPU使用\t")
		fmt.Fprintf(w, "%s\t%d\t%s\t%.2f MB\t%.2f%%\t\n",
			"运行中",
			info.PID,
			formatDuration(info.RunTime),
			float64(info.MemUsage)/(1024*1024),
			info.CPUUsage,
		)
		w.Flush()

		// 显示命令行
		if info.Command != "" {
			fmt.Printf("\n命令: %s\n", info.Command)
		}

		return nil
	},
}

// formatDuration 格式化持续时间
func formatDuration(d time.Duration) string {
	d = d.Round(time.Second)

	days := d / (24 * time.Hour)
	d -= days * 24 * time.Hour

	hours := d / time.Hour
	d -= hours * time.Hour

	minutes := d / time.Minute
	d -= minutes * time.Minute

	seconds := d / time.Second

	if days > 0 {
		return fmt.Sprintf("%d天%d小时%d分", days, hours, minutes)
	}
	if hours > 0 {
		return fmt.Sprintf("%d小时%d分%d秒", hours, minutes, seconds)
	}
	if minutes > 0 {
		return fmt.Sprintf("%d分%d秒", minutes, seconds)
	}
	return fmt.Sprintf("%d秒", seconds)
}
