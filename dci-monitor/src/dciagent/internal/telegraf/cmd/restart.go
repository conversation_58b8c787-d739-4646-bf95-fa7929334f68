package cmd

import (
	"fmt"

	"github.com/spf13/cobra"
)

// RestartCmd 重启Telegraf进程
var RestartCmd = &cobra.Command{
	Use:   "restart",
	Short: "重启Telegraf进程",
	Long:  `重启Telegraf数据采集进程。如果未在运行，则直接启动。`,
	RunE: func(cmd *cobra.Command, args []string) error {
		// 使用全局共享的控制器
		if err := ctrl.Restart(); err != nil {
			log.Error("重启Telegraf失败:", err)
			return fmt.Errorf("重启Telegraf失败: %v", err)
		}

		fmt.Println("Telegraf已成功重启")
		return nil
	},
}
