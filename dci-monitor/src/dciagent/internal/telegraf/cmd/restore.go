package cmd

import (
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/spf13/cobra"

	"common/logger"
	"dciagent/internal/telegraf/config"
)

var (
	backupID string
)

// RestoreCmd 恢复Telegraf配置文件
var RestoreCmd = &cobra.Command{
	Use:   "restore",
	Short: "恢复Telegraf配置",
	Long:  `从指定的备份恢复Telegraf配置文件，支持指定备份ID或使用最新的备份。`,
	RunE: func(cmd *cobra.Command, args []string) error {
		log := logger.GetCompatLogger()

		// 加载配置
		cfg, err := config.LoadConfig()
		if err != nil {
			log.Error("加载配置失败: %v", err)
			return err
		}

		// 检查备份目录
		if _, err := os.Stat(cfg.BackupDir); os.IsNotExist(err) {
			return fmt.Errorf("备份目录不存在: %s", cfg.BackupDir)
		}

		// 确定要恢复的备份路径
		var backupPath string

		if backupID == "" {
			// 如果未指定备份ID，使用最新的备份
			entries, err := os.ReadDir(cfg.BackupDir)
			if err != nil {
				log.Error("读取备份目录失败: %v", err)
				return fmt.Errorf("读取备份目录失败: %v", err)
			}

			// 过滤并排序备份目录
			var backupDirs []string
			for _, entry := range entries {
				if entry.IsDir() && strings.HasPrefix(entry.Name(), "telegraf_backup_") {
					backupDirs = append(backupDirs, entry.Name())
				}
			}

			if len(backupDirs) == 0 {
				return fmt.Errorf("未找到有效的备份")
			}

			// 按名称排序（实际上是按时间戳排序）
			sort.Strings(backupDirs)
			latestBackup := backupDirs[len(backupDirs)-1]
			backupPath = filepath.Join(cfg.BackupDir, latestBackup)

			log.Info("使用最新的备份: %s", latestBackup)
		} else {
			// 使用指定的备份ID
			backupPath = filepath.Join(cfg.BackupDir, backupID)
			if _, err := os.Stat(backupPath); os.IsNotExist(err) {
				return fmt.Errorf("指定的备份不存在: %s", backupID)
			}
		}

		// 检查备份中是否包含必要的文件
		configBackupPath := filepath.Join(backupPath, "telegraf.conf")
		if _, err := os.Stat(configBackupPath); os.IsNotExist(err) {
			return fmt.Errorf("备份中不包含主配置文件: %s", configBackupPath)
		}

		// 确认恢复操作
		fmt.Printf("准备从 %s 恢复配置，将覆盖当前配置\n", backupPath)
		fmt.Print("确认恢复操作？(y/n): ")

		var confirmation string
		fmt.Scanln(&confirmation)

		if strings.ToLower(confirmation) != "y" && strings.ToLower(confirmation) != "yes" {
			fmt.Println("已取消恢复操作")
			return nil
		}

		// 恢复主配置文件
		if err := copyFile(configBackupPath, cfg.ConfigFile); err != nil {
			log.Error("恢复主配置文件失败: %v", err)
			return fmt.Errorf("恢复主配置文件失败: %v", err)
		}

		// 恢复配置目录
		configDirBackupPath := filepath.Join(backupPath, "telegraf.d")
		if _, err := os.Stat(configDirBackupPath); !os.IsNotExist(err) {
			// 先清空现有配置目录
			if err := os.RemoveAll(cfg.ConfigDir); err != nil {
				log.Error("清空现有配置目录失败: %v", err)
				return fmt.Errorf("清空现有配置目录失败: %v", err)
			}

			// 恢复配置目录
			if err := copyDir(configDirBackupPath, cfg.ConfigDir); err != nil {
				log.Error("恢复配置目录失败: %v", err)
				return fmt.Errorf("恢复配置目录失败: %v", err)
			}
		}

		fmt.Println("Telegraf配置已成功恢复")
		return nil
	},
}

func init() {
	RestoreCmd.Flags().StringVarP(&backupID, "backup", "b", "", "要恢复的备份ID (默认使用最新备份)")
}
