package cmd

import (
	"fmt"

	"github.com/spf13/cobra"
)

// StartCmd 启动Telegraf进程
var StartCmd = &cobra.Command{
	Use:   "start",
	Short: "启动Telegraf进程",
	Long:  `启动Telegraf数据采集进程。如果已经在运行，则返回错误。`,
	RunE: func(cmd *cobra.Command, args []string) error {
		// 使用全局共享的控制器
		if err := ctrl.Start(); err != nil {
			log.Error("启动Telegraf失败:", err)
			return fmt.Errorf("启动Telegraf失败: %v", err)
		}

		fmt.Println("Telegraf已成功启动")
		return nil
	},
}
