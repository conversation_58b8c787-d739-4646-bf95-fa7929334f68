package cmd

import (
	"github.com/spf13/cobra"

	"common/logger"
	"dciagent/internal/telegraf/config"
	"dciagent/internal/telegraf/control"
)

var (
	// 全局共享的配置和日志记录器实例
	cfg  *config.TelegrafConfig
	log  logger.Logger
	ctrl *control.Controller
)

// RegisterCommands 注册所有Telegraf子命令
func RegisterCommands(rootCmd *cobra.Command, telegrafConfig *config.TelegrafConfig, telegrafLog logger.Logger) {
	// 存储共享实例
	cfg = telegrafConfig
	log = telegrafLog
	ctrl = control.NewController(cfg, log)

	// 注册进程控制命令
	rootCmd.AddCommand(StartCmd)
	rootCmd.AddCommand(StopCmd)
	rootCmd.AddCommand(RestartCmd)
	rootCmd.AddCommand(StatusCmd)
	rootCmd.AddCommand(CheckConfigCmd)

	// 注册安装命令
	rootCmd.AddCommand(InstallCmd)

	// 注册配置管理命令
	rootCmd.AddCommand(BackupCmd)
	rootCmd.AddCommand(RestoreCmd)
}
