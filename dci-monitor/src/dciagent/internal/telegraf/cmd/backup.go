package cmd

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/spf13/cobra"

	"common/logger"
	"dciagent/internal/telegraf/config"
)

// BackupCmd 备份Telegraf配置文件
var BackupCmd = &cobra.Command{
	Use:   "backup",
	Short: "备份Telegraf配置",
	Long:  `备份Telegraf配置文件和配置目录，以便在出现问题时可以恢复。`,
	RunE: func(cmd *cobra.Command, args []string) error {
		log := logger.GetCompatLogger()

		// 加载配置
		cfg, err := config.LoadConfig()
		if err != nil {
			log.Error("加载配置失败: %v", err)
			return err
		}

		// 创建备份目录
		if err := os.MkdirAll(cfg.BackupDir, 0755); err != nil {
			log.Error("创建备份目录失败: %v", err)
			return fmt.Errorf("创建备份目录失败: %v", err)
		}

		// 创建带时间戳的备份子目录
		timestamp := time.Now().Format("20060102_150405")
		backupPath := filepath.Join(cfg.BackupDir, fmt.Sprintf("telegraf_backup_%s", timestamp))

		if err := os.Mkdir(backupPath, 0755); err != nil {
			log.Error("创建备份子目录失败: %v", err)
			return fmt.Errorf("创建备份子目录失败: %v", err)
		}

		// 备份主配置文件
		if _, err := os.Stat(cfg.ConfigFile); !os.IsNotExist(err) {
			configBackupPath := filepath.Join(backupPath, "telegraf.conf")
			if err := copyFile(cfg.ConfigFile, configBackupPath); err != nil {
				log.Error("备份主配置文件失败: %v", err)
				return fmt.Errorf("备份主配置文件失败: %v", err)
			}
			log.Info("备份主配置文件到 %s", configBackupPath)
		}

		// 备份配置目录
		if _, err := os.Stat(cfg.ConfigDir); !os.IsNotExist(err) {
			configDirBackupPath := filepath.Join(backupPath, "telegraf.d")
			if err := copyDir(cfg.ConfigDir, configDirBackupPath); err != nil {
				log.Error("备份配置目录失败: %v", err)
				return fmt.Errorf("备份配置目录失败: %v", err)
			}
			log.Info("备份配置目录到 %s", configDirBackupPath)
		}

		fmt.Printf("Telegraf配置备份成功，备份路径: %s\n", backupPath)
		return nil
	},
}

// 复制文件
func copyFile(src, dst string) error {
	input, err := os.ReadFile(src)
	if err != nil {
		return err
	}

	return os.WriteFile(dst, input, 0644)
}

// 复制目录
func copyDir(src, dst string) error {
	// 创建目标目录
	if err := os.MkdirAll(dst, 0755); err != nil {
		return err
	}

	// 读取源目录
	entries, err := os.ReadDir(src)
	if err != nil {
		return err
	}

	// 遍历并复制
	for _, entry := range entries {
		srcPath := filepath.Join(src, entry.Name())
		dstPath := filepath.Join(dst, entry.Name())

		if entry.IsDir() {
			// 递归复制子目录
			if err := copyDir(srcPath, dstPath); err != nil {
				return err
			}
		} else {
			// 复制文件
			if err := copyFile(srcPath, dstPath); err != nil {
				return err
			}
		}
	}

	return nil
}
