package cmd

import (
	"time"

	"github.com/spf13/cobra"

	"dciagent/internal/telegraf/install"
)

var (
	// 安装选项
	installVersion         string
	installOverwrite       bool
	installDownloadTimeout string
	installVerifyChecksum  bool
)

// InstallCmd 安装Telegraf
var InstallCmd = &cobra.Command{
	Use:   "install",
	Short: "安装或更新Telegraf",
	Long: `安装或更新Telegraf数据采集组件到指定的主目录。
支持指定版本、验证校验和以及覆盖现有安装。

示例:
  dciagent telegraf install                      # 安装最新版本
  dciagent telegraf install --version 1.34.2     # 安装指定版本
  dciagent telegraf install --overwrite          # 覆盖现有安装
`,
	RunE: func(cmd *cobra.Command, args []string) error {
		// 初始化安装选项
		options := install.DefaultOptions()

		// 应用命令行参数
		if installVersion != "" {
			options.Version = installVersion
		}

		options.OverwriteExisting = installOverwrite
		options.VerifyChecksum = installVerifyChecksum

		if installDownloadTimeout != "" {
			timeout, err := time.ParseDuration(installDownloadTimeout)
			if err != nil {
				log.Error("无效的下载超时时间格式: %v", err)
				return err
			}
			options.DownloadTimeout = timeout
		}

		// 创建安装器
		installer := install.NewInstaller(cfg, log, options)

		// 执行安装
		log.Info("开始安装Telegraf...")
		if err := installer.Install(); err != nil {
			log.Error("安装Telegraf失败: %v", err)
			return err
		}

		log.Info("Telegraf安装成功")
		return nil
	},
}

func init() {
	// 添加安装命令的标志
	InstallCmd.Flags().StringVar(&installVersion, "version", "", "指定要安装的Telegraf版本，默认为最新版本")
	InstallCmd.Flags().BoolVar(&installOverwrite, "overwrite", false, "是否覆盖现有安装")
	InstallCmd.Flags().StringVar(&installDownloadTimeout, "download-timeout", "60s", "下载超时时间")
	InstallCmd.Flags().BoolVar(&installVerifyChecksum, "verify-checksum", true, "是否验证下载文件的校验和")
}
