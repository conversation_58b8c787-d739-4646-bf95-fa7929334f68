package cmd

import (
	"fmt"

	"github.com/spf13/cobra"
)

// StopCmd 停止Telegraf进程
var StopCmd = &cobra.Command{
	Use:   "stop",
	Short: "停止Telegraf进程",
	Long:  `停止正在运行的Telegraf数据采集进程。如果未运行，则返回错误。`,
	RunE: func(cmd *cobra.Command, args []string) error {
		// 使用全局共享的控制器
		if err := ctrl.Stop(); err != nil {
			log.Error("停止Telegraf失败:", err)
			return fmt.Errorf("停止Telegraf失败: %v", err)
		}

		fmt.Println("Telegraf已成功停止")
		return nil
	},
}
