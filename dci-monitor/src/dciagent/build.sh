#!/bin/bash

# 设置构建环境
BINARY_NAME="dciagent"
BUILD_DIR="bin"
VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_DATE=$(date "+%Y-%m-%d %H:%M:%S")
COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 创建构建目录
mkdir -p ${BUILD_DIR}

# 编译
echo "编译 ${BINARY_NAME}..."
go build -o ${BUILD_DIR}/${BINARY_NAME} \
    -ldflags "-X 'main.version=${VERSION}' -X 'main.buildDate=${BUILD_DATE}' -X 'main.commit=${COMMIT}'" \
    main.go

if [ $? -eq 0 ]; then
    echo "编译成功: ${BUILD_DIR}/${BINARY_NAME}"
    echo "版本: ${VERSION}"
    echo "构建时间: ${BUILD_DATE}"
    echo "提交: ${COMMIT}"
    
    # 设置执行权限
    chmod +x ${BUILD_DIR}/${BINARY_NAME}
    
    # 显示帮助信息
    echo -e "\n执行帮助命令:"
    ${BUILD_DIR}/${BINARY_NAME} --help
else
    echo "编译失败"
    exit 1
fi 