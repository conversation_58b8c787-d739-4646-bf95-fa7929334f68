# 系统指标采集配置 - DCI监测系统
# 包含基础系统指标：CPU、内存、磁盘、网络等

###############################################################################
#                            CPU指标采集                                        #
###############################################################################

[[inputs.cpu]]
  ## 是否采集每个CPU核心的数据，否则仅采集聚合数据
  percpu = true
  ## 是否采集总CPU数据
  totalcpu = true
  ## 采集CPU时间指标
  collect_cpu_time = false
  ## 是否收集原始CPU值（而非百分比）
  report_active = false
  ## 标记为guest的CPU时间是否应包含在totalcpu计算中
  core_tags = false

###############################################################################
#                            磁盘指标采集                                       #
###############################################################################

[[inputs.disk]]
  ## 需要忽略的挂载点
  ignore_fs = ["tmpfs", "devtmpfs", "devfs", "iso9660", "overlay", "aufs", "squashfs"]
  ## 是否收集inodes指标
  collect_inodes = true
  ## 磁盘空间使用单位
  unit = "bytes"
  ## 挂载点过滤
  mount_points = ["/*"]

###############################################################################
#                           磁盘IO指标采集                                      #
###############################################################################

[[inputs.diskio]]
  ## 需要采集的设备
  ## 不指定将采集所有设备
  # devices = ["sda", "sdb"]
  ## 采集设备序列号（如果可用）
  skip_serial_number = true
  ## 采集设备标签
  device_tags = ["ID_FS_TYPE", "ID_FS_USAGE"]

###############################################################################
#                            内存指标采集                                       #
###############################################################################

[[inputs.mem]]
  # 无特殊配置

###############################################################################
#                            网络指标采集                                       #
###############################################################################

[[inputs.net]]
  ## 指定要收集的接口
  # interfaces = ["eth0"]
  ## 是否收集网络接口统计信息
  ignore_protocol_stats = false

[[inputs.netstat]]
  # 无特殊配置

###############################################################################
#                            系统指标采集                                       #
###############################################################################

[[inputs.system]]
  ## 收集系统负载、正常运行时间和用户数
  # 无特殊配置

###############################################################################
#                            进程指标采集                                       #
###############################################################################

[[inputs.processes]]
  ## 收集系统进程计数
  # 无特殊配置 