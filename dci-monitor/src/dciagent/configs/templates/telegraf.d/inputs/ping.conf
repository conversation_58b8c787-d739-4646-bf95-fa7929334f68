# Ping 指标采集配置 - DCI监测系统
# 用于基本的网络连通性检测

###############################################################################
#                            Ping 指标采集                                      #
###############################################################################

[[inputs.ping]]
  ## 要ping的终端列表 (可以根据环境调整)
  urls = ["*******"]  # 默认ping公共DNS作为基本网络连通性检查
  
  ## ping 设置
  count = 4  # 每次发送的ping包数量
  timeout = 2.0  # 每个ping的超时时间 (秒)
  ping_interval = 1.0  # ping包之间的间隔 (秒)
  
  ## ping协议 - "tcp" or "udp" or "icmp"
  protocol = "icmp"
  
  ## 是否使用IPv6
  ipv6 = false
  
  ## 结果标签
  ## 将添加ping目标的DNS名称作为标签
  dns_lookup = true 