# Kafka输出配置 - DCI监测系统
# 用于将采集的数据输出到Kafka消息队列

###############################################################################
#                            Kafka输出配置                                      #
###############################################################################

[[outputs.kafka]]
  ## 根据13-DCI-Kafka主题规划及负载均衡连接设计文档配置
  ## Kafka代理地址列表
  brokers = ["kafka1:9092", "kafka2:9092", "kafka3:9092"]
  
  ## 主题名
  topic = "dci.monitor.v1.defaultchannel.metrics.telegraf"
  
  ## 路由主题（可选配置，根据环境或标签选择不同主题）
  # topic = "dci.monitor.v1.${tag:datacenter}.metrics.telegraf"
  
  ## 压缩算法
  compression_codec = "snappy"
  
  ## 确认级别
  ## 0=无确认, 1=等待本地确认, -1=等待所有确认
  required_acks = 1
  
  ## 数据格式
  data_format = "json"
  
  ## 认证（如有需要，可取消注释并配置）
  # sasl_username = "${KAFKA_USER}"
  # sasl_password = "${KAFKA_PASSWORD}"
  
  ## TLS配置（如有需要，可取消注释并配置）
  # tls_ca = "/opt/dci/dciagent/conf/credentials/kafka_ca.pem"
  # tls_cert = "/opt/dci/dciagent/conf/credentials/kafka_cert.pem"
  # tls_key = "/opt/dci/dciagent/conf/credentials/kafka_key.pem"
  
  ## 重试设置
  max_retry = 3
  
  ## 批处理设置
  batch_size = 1000
  
  ## 保留部分标签用于路由和标识
  ## 这些标签将作为消息的一部分传递到Kafka
  tag_keys = ["device_id", "hostname", "agent_id"]
  
  ## 最大消息大小(字节)
  max_message_bytes = 1000000 