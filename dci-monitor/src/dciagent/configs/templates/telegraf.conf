# Telegraf配置 - DCI监测系统
# 主目录部署 - 基础配置文件

###############################################################################
#                            全局代理配置                                       #
###############################################################################

[agent]
  ## 默认数据采集间隔
  interval = "60s"
  
  ## 舍入采集间隔
  round_interval = true
  
  ## 指标发送批处理大小
  metric_batch_size = 1000
  
  ## 指标缓冲区上限
  metric_buffer_limit = 10000
  
  ## 采集时间抖动以避免峰值
  collection_jitter = "5s"
  
  ## 默认数据刷新间隔
  flush_interval = "30s"
  
  ## 刷新时间抖动以避免峰值
  flush_jitter = "5s"
  
  ## 指标精度
  precision = "s"
  
  ## 日志配置
  logfile = "/opt/dci/dciagent/logs/telegraf.log"
  logfile_rotation_interval = "1d"
  logfile_rotation_max_size = "50MB"
  logfile_rotation_max_archives = 7
  
  ## 使用主机名作为标签
  hostname = ""
  omit_hostname = false

###############################################################################
#                            全局标签设置                                       #
###############################################################################

[global_tags]
  ## 全局标签，将添加到所有指标
  agent_type = "telegraf"
  environment = "production"
  
###############################################################################
#                            处理器插件配置                                     #
###############################################################################

# 用于转换标签，确保标签命名一致性
[[processors.rename]]
  [[processors.rename.replace]]
    tag = "host"
    dest = "hostname"

# 添加额外元数据
[[processors.tag_limit]]
  ## 限制每个指标的标签数量
  limit = 25

###############################################################################
#                               配置加载                                        #
###############################################################################
# 注意: 这里不需要定义具体的输入和输出插件，
# 它们将从telegraf.d目录中的配置片段加载 