package cmd

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"

	"common/logger"
)

var (
	cfgFile string
	log     logger.Logger
)

// rootCmd 代表没有子命令调用时的基础命令
var rootCmd = &cobra.Command{
	Use:   "dciagent",
	Short: "DCI数据采集管理工具",
	Long: `dcicollector是DCI监测系统的数据采集管理工具，
负责管理Telegraf等采集模块的安装、配置、运行和更新。

支持的功能:
  - 管理数据采集组件 (如Telegraf)
  - 配置组件参数和插件
  - 监控组件状态
  - 安装和更新组件`,
	// 默认情况下显示帮助
	Run: func(cmd *cobra.Command, args []string) {
		cmd.Help()
	},
}

// Execute 执行根命令
func Execute() error {
	return rootCmd.Execute()
}

func init() {
	cobra.OnInitialize(initConfig, initLogger)

	// 全局标志
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "配置文件路径 (默认 /etc/dci/dciagent.yaml)")
	rootCmd.PersistentFlags().Bool("verbose", false, "启用详细输出")

	// 绑定到viper
	viper.BindPFlag("verbose", rootCmd.PersistentFlags().Lookup("verbose"))
}

// initConfig 读取配置文件和环境变量
func initConfig() {
	if cfgFile != "" {
		// 使用指定的配置文件
		viper.SetConfigFile(cfgFile)
	} else {
		// 搜索默认位置的配置文件
		home, err := os.UserHomeDir()
		if err == nil {
			// 添加用户目录下的配置文件
			viper.AddConfigPath(filepath.Join(home, ".dci"))
		}

		// 添加标准配置路径
		viper.AddConfigPath("/etc/dci")
		viper.SetConfigName("dciagent")
		viper.SetConfigType("yaml")
	}

	// 读取环境变量
	viper.SetEnvPrefix("DCI")
	viper.AutomaticEnv()

	// 设置默认值
	viper.SetDefault("global.log_level", "info")
	viper.SetDefault("telegraf.home", "/opt/dci/dciagent")
	viper.SetDefault("telegraf.start_timeout", "10s")
	viper.SetDefault("telegraf.stop_timeout", "30s")

	// 读取配置文件
	if err := viper.ReadInConfig(); err == nil {
		fmt.Println("使用配置文件:", viper.ConfigFileUsed())
	}
}

// initLogger 初始化日志
func initLogger() {
	// 从配置获取日志级别
	logLevel := viper.GetString("global.log_level")

	// 如果设置了verbose标志，则使用debug级别
	if viper.GetBool("verbose") {
		logLevel = "debug"
	}

	// 初始化logger包（它会自己读取viper配置）
	logger.InitLogger()

	// 获取适配后的logger实例
	log = logger.GetCompatLogger()

	// 记录启动信息
	log.Info("dciagent 启动，日志级别:", logLevel)
}
