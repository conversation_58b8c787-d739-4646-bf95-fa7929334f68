package cmd

import (
	"github.com/spf13/cobra"

	"common/logger"
	"dciagent/internal/telegraf/cmd"
	"dciagent/internal/telegraf/config"
)

var (
	telegrafLog logger.Logger
)

// telegrafCmd 表示telegraf命令
var telegrafCmd = &cobra.Command{
	Use:   "telegraf",
	Short: "管理Telegraf数据采集组件",
	Long: `telegraf子命令用于管理Telegraf数据采集组件，
支持启动、停止、重启、配置和状态查询等功能。

这是DCI监测系统数据采集的核心组件之一。`,
	Run: func(cmd *cobra.Command, args []string) {
		cmd.Help()
	},
}

func init() {
	// 在rootCmd中添加telegraf子命令
	rootCmd.AddCommand(telegrafCmd)

	// 在此处添加子命令的全局标志
	telegrafCmd.PersistentFlags().String("config-file", "", "Telegraf主配置文件路径")
	telegrafCmd.PersistentFlags().String("config-dir", "", "Telegraf配置目录路径")
	telegrafCmd.PersistentFlags().String("home", "", "Telegraf主目录路径")

	// 获取日志记录器
	telegrafLog = logger.GetCompatLogger()

	// 初始化Telegraf配置
	cfg, err := config.LoadConfig()
	if err != nil {
		telegrafLog.Error("加载Telegraf配置失败:", err)
		return
	}

	// 注册telegraf子命令
	cmd.RegisterCommands(telegrafCmd, cfg, telegrafLog)
}
