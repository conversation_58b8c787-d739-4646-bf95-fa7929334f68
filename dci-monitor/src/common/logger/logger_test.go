package logger

import (
	"os"
	"testing"

	"go.uber.org/zap"
)

func TestLoggerCompatibility(t *testing.T) {
	// 设置一个测试标志，避免在测试中创建真实的日志文件
	os.Setenv("LOGGER_TEST_MODE", "true")
	defer os.Unsetenv("LOGGER_TEST_MODE")

	// 初始化日志系统
	InitLogger()

	// 测试标准日志接口
	Info("这是一条标准Info日志")
	Debug("这是一条标准Debug日志")
	Warn("这是一条标准Warn日志", zap.String("key", "value"))
	Error("这是一条标准Error日志", zap.Int("code", 500))

	// 测试兼容接口
	compat := GetCompatLogger()
	compat.Info("这是一条兼容Info日志")
	compat.Infof("这是一条格式化的Info日志: %s", "测试")
	compat.Debug("这是一条兼容Debug日志")
	compat.Debugf("这是一条格式化的Debug日志: %d", 123)
	compat.Warn("这是一条兼容Warn日志")
	compat.Warnf("这是一条格式化的Warn日志: %v", []string{"a", "b"})
	compat.Error("这是一条兼容Error日志")
	compat.Errorf("这是一条格式化的Error日志: %v", map[string]string{"key": "value"})

	// 获取原始logger并使用
	rawLogger := GetLogger()
	rawLogger.Info("使用原始logger", zap.String("test", "value"))
}

func TestGetLogger(t *testing.T) {
	// 测试GetLogger返回非nil
	logger := GetLogger()
	if logger == nil {
		t.Fatal("GetLogger返回了nil")
	}

	// 测试GetCompatLogger返回非nil
	compat := GetCompatLogger()
	if compat == nil {
		t.Fatal("GetCompatLogger返回了nil")
	}
}
