package logger

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	lj "gopkg.in/natefinch/lumberjack.v2"
)

var log *zap.Logger

// InitLogger 初始化 Zap 日志记录器
func InitLogger() {
	// 检查是否处于测试模式
	if os.Getenv("LOGGER_TEST_MODE") == "true" {
		// 在测试模式下使用简单配置
		InitTestLogger()
		return
	}

	fmt.Fprintf(os.Stderr, "开始初始化日志系统...\n")

	logLevel := viper.GetString("logger.level")
	logDir := viper.GetString("logger.dir")
	maxSize := viper.GetInt("logger.maxSize")
	maxBackups := viper.GetInt("logger.maxBackups")
	maxAge := viper.GetInt("logger.maxAge")
	compress := viper.GetBool("logger.compress")

	fmt.Fprintf(os.Stderr, "日志配置: level=%s, dir=%s, maxSize=%d, maxBackups=%d, maxAge=%d, compress=%v\n",
		logLevel, logDir, maxSize, maxBackups, maxAge, compress)

	if logDir == "" {
		// 如果配置为空，则使用默认值或报错
		logDir = "./logs"
		fmt.Fprintf(os.Stderr, "日志目录为空，使用默认目录: %s\n", logDir)
	}

	absLogDir, err := filepath.Abs(logDir) // 获取绝对路径以便清晰显示
	if err != nil {
		fmt.Fprintf(os.Stderr, "无法获取日志目录 '%s' 的绝对路径: %v\n", logDir, err)
		os.Exit(1)
	}
	fmt.Fprintf(os.Stderr, "准备创建日志目录: %s (来自配置 '%s')\n", absLogDir, logDir)

	// 创建日志目录
	if err := os.MkdirAll(absLogDir, os.ModePerm); err != nil {
		// 使用 stderr 输出，因为此时 logger 可能还未完全初始化
		fmt.Fprintf(os.Stderr, "致命错误：无法创建日志目录 %s: %v\n", absLogDir, err)
		os.Exit(1) // 退出程序
	}
	fmt.Fprintf(os.Stderr, "日志目录创建成功\n")

	// 配置日志核心
	fmt.Fprintf(os.Stderr, "配置日志核心...\n")
	cores := getZapCores(absLogDir, logLevel, maxSize, maxBackups, maxAge, compress)
	fmt.Fprintf(os.Stderr, "创建了 %d 个日志核心\n", len(cores))

	// 如果没有有效的日志核心，创建一个默认的控制台输出核心
	if len(cores) == 0 {
		fmt.Fprintf(os.Stderr, "没有有效的日志核心，创建默认控制台输出核心\n")
		encoderConfig := zapcore.EncoderConfig{
			TimeKey:        "time",
			LevelKey:       "level",
			NameKey:        "logger",
			CallerKey:      "caller",
			MessageKey:     "msg",
			StacktraceKey:  "stacktrace",
			LineEnding:     zapcore.DefaultLineEnding,
			EncodeLevel:    zapcore.CapitalLevelEncoder,
			EncodeTime:     zapcore.ISO8601TimeEncoder,
			EncodeDuration: zapcore.SecondsDurationEncoder,
			EncodeCaller:   zapcore.ShortCallerEncoder,
		}
		consoleEncoder := zapcore.NewConsoleEncoder(encoderConfig)
		consoleOutput := zapcore.Lock(os.Stdout)
		level := zap.NewAtomicLevelAt(zap.InfoLevel)
		cores = append(cores, zapcore.NewCore(consoleEncoder, consoleOutput, level))
	}

	combinedCore := zapcore.NewTee(cores...)

	// 创建日志记录器
	fmt.Fprintf(os.Stderr, "创建日志记录器...\n")
	log = zap.New(combinedCore, zap.AddCaller(), zap.AddCallerSkip(1))

	fmt.Fprintf(os.Stderr, "替换全局日志记录器...\n")
	zap.ReplaceGlobals(log)

	// 使用新 logger 记录成功信息
	fmt.Fprintf(os.Stderr, "日志系统初始化成功\n")
	log.Info("Zap Logger 初始化成功", zap.String("level", logLevel), zap.String("dir", absLogDir))
}

// InitTestLogger 初始化测试环境的日志记录器
func InitTestLogger() {
	config := zap.NewDevelopmentConfig()
	// 同时输出到控制台和文件
	config.OutputPaths = []string{"stdout", "stderr"}
	config.ErrorOutputPaths = []string{"stderr"}
	logger, err := config.Build(zap.AddCaller(), zap.AddCallerSkip(1))
	if err != nil {
		fmt.Fprintf(os.Stderr, "初始化测试日志记录器失败: %v\n", err)
		os.Exit(1)
	}
	log = logger
	zap.ReplaceGlobals(log)
	log.Info("测试模式 Zap Logger 初始化成功")
}

// getZapCores 根据级别创建不同的 zapcore.Core
func getZapCores(logDir, level string, maxSize, maxBackups, maxAge int, compress bool) []zapcore.Core {
	fmt.Fprintf(os.Stderr, "开始创建日志核心，目录: %s, 级别: %s\n", logDir, level)

	// 定义JSON编码器配置
	jsonEncoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 定义控制台编码器配置
	consoleEncoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalColorLevelEncoder, // 带颜色的日志级别
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 设置日志级别
	atomicLevel := zap.NewAtomicLevel()
	if err := atomicLevel.UnmarshalText([]byte(level)); err != nil {
		atomicLevel.SetLevel(zap.InfoLevel)
		fmt.Fprintf(os.Stderr, "无效的日志级别 '%s', 使用默认级别 'info'\n", level)
	} else {
		fmt.Fprintf(os.Stderr, "日志级别设置为: %s\n", atomicLevel.String())
	}

	cores := []zapcore.Core{}

	// 控制台输出核心 (用于开发调试)
	serverMode := viper.GetString("server.mode")
	fmt.Fprintf(os.Stderr, "服务器模式: %s\n", serverMode)

	// 始终添加控制台输出，确保日志可见
	fmt.Fprintf(os.Stderr, "添加控制台输出核心\n")
	consoleEncoder := zapcore.NewConsoleEncoder(consoleEncoderConfig)
	consoleDebugging := zapcore.Lock(os.Stdout)
	cores = append(cores, zapcore.NewCore(consoleEncoder, consoleDebugging, atomicLevel))

	// 单一日志文件 - 所有级别写入同一个文件
	fmt.Fprintf(os.Stderr, "创建统一日志文件核心\n")

	// 使用一个通用的日志文件
	logFilePath := filepath.Join(logDir, "application.log")
	fmt.Fprintf(os.Stderr, "创建统一日志文件: %s\n", logFilePath)

	// 确保文件可写
	file, err := os.OpenFile(logFilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		fmt.Fprintf(os.Stderr, "警告: 无法打开日志文件 %s: %v\n", logFilePath, err)
	} else {
		file.Close() // 只是测试文件可写性

		writer := zapcore.AddSync(&lj.Logger{
			Filename:   logFilePath,
			MaxSize:    maxSize,    // M
			MaxBackups: maxBackups, // 个
			MaxAge:     maxAge,     // days
			Compress:   compress,   // 是否压缩
			LocalTime:  true,       // 使用本地时间
		})

		// JSON 格式写入文件
		fileEncoder := zapcore.NewJSONEncoder(jsonEncoderConfig)

		// 创建处理所有级别的核心
		cores = append(cores, zapcore.NewCore(fileEncoder, writer, atomicLevel))
		fmt.Fprintf(os.Stderr, "所有级别的日志将写入同一个文件: %s\n", logFilePath)
	}

	fmt.Fprintf(os.Stderr, "创建了 %d 个日志核心\n", len(cores))
	return cores
}

// GetLogger 返回初始化的日志记录器实例
func GetLogger() *zap.Logger {
	if log == nil {
		// 如果尚未初始化，则使用默认配置进行初始化（可能在测试或独立脚本中使用）
		InitDefaultLogger()
	}
	return log
}

// InitDefaultLogger 使用默认设置初始化日志记录器（主要用于测试或未完整配置时）
func InitDefaultLogger() {
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	core := zapcore.NewCore(
		zapcore.NewJSONEncoder(encoderConfig),
		zapcore.Lock(os.Stdout),
		zap.InfoLevel,
	)
	log = zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))
	zap.ReplaceGlobals(log)
	log.Info("Zap Logger 使用默认配置初始化")
}

// Info logs a message at InfoLevel
func Info(msg string, fields ...zap.Field) {
	log.Info(msg, fields...)
}

// Debug logs a message at DebugLevel
func Debug(msg string, fields ...zap.Field) {
	log.Debug(msg, fields...)
}

// Warn logs a message at WarnLevel
func Warn(msg string, fields ...zap.Field) {
	log.Warn(msg, fields...)
}

// Error logs a message at ErrorLevel
func Error(msg string, fields ...zap.Field) {
	log.Error(msg, fields...)
}

// Fatal logs a message at FatalLevel then the process will exit
func Fatal(msg string, fields ...zap.Field) {
	log.Fatal(msg, fields...)
}

// Panic logs a message at PanicLevel then panics
func Panic(msg string, fields ...zap.Field) {
	log.Panic(msg, fields...)
}

// Sync 同步日志缓冲，应在程序退出前调用
func Sync() error {
	if log != nil {
		return log.Sync()
	}
	return nil
}
