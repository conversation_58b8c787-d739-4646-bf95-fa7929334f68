package logger

import (
	"fmt"
	"os"

	"go.uber.org/zap"
)

// Logger 接口兼容dcicollector中的Logger接口
type Logger interface {
	// 错误级别
	Error(args ...interface{})
	Errorf(format string, args ...interface{})

	// 警告级别
	Warn(args ...interface{})
	Warnf(format string, args ...interface{})

	// 信息级别
	Info(args ...interface{})
	Infof(format string, args ...interface{})

	// 调试级别
	Debug(args ...interface{})
	Debugf(format string, args ...interface{})
}

// ZapAdapter 将zap.Logger适配为Logger接口
type ZapAdapter struct {
	logger *zap.Logger
}

// NewZapAdapter 创建新的ZapAdapter
func NewZapAdapter(logger *zap.Logger) *ZapAdapter {
	// 添加调试输出
	if logger == nil {
		fmt.Fprintf(os.Stderr, "警告: NewZapAdapter接收到nil logger\n")
	}
	return &ZapAdapter{logger: logger}
}

// Error 实现Logger接口
func (z *ZapAdapter) Error(args ...interface{}) {
	if z.logger == nil {
		return
	}

	// 分离消息和字段
	message, fields := extractMessageAndFields(args)
	z.logger.Error(message, fields...)
}

// Errorf 实现Logger接口
func (z *ZapAdapter) Errorf(format string, args ...interface{}) {
	z.logger.Error(fmt.Sprintf(format, args...))
}

// Warn 实现Logger接口
func (z *ZapAdapter) Warn(args ...interface{}) {
	if z.logger == nil {
		return
	}

	// 分离消息和字段
	message, fields := extractMessageAndFields(args)
	z.logger.Warn(message, fields...)
}

// Warnf 实现Logger接口
func (z *ZapAdapter) Warnf(format string, args ...interface{}) {
	z.logger.Warn(fmt.Sprintf(format, args...))
}

// Info 实现Logger接口
func (z *ZapAdapter) Info(args ...interface{}) {
	if z.logger == nil {
		fmt.Fprintf(os.Stderr, "警告: ZapAdapter.Info调用时内部logger为nil\n")
		return
	}

	// 如果只有一个参数且是字符串，直接作为消息
	if len(args) == 1 {
		switch v := args[0].(type) {
		case string:
			z.logger.Info(v)
			return
		}
	}

	// 分离消息和字段
	message, fields := extractMessageAndFields(args)
	z.logger.Info(message, fields...)
}

// Infof 实现Logger接口
func (z *ZapAdapter) Infof(format string, args ...interface{}) {
	if z.logger == nil {
		fmt.Fprintf(os.Stderr, "警告: ZapAdapter.Infof调用时内部logger为nil\n")
		return
	}

	message := fmt.Sprintf(format, args...)
	z.logger.Info(message)
}

// Debug 实现Logger接口
func (z *ZapAdapter) Debug(args ...interface{}) {
	if z.logger == nil {
		return
	}

	// 分离消息和字段
	message, fields := extractMessageAndFields(args)
	z.logger.Debug(message, fields...)
}

// Debugf 实现Logger接口
func (z *ZapAdapter) Debugf(format string, args ...interface{}) {
	z.logger.Debug(fmt.Sprintf(format, args...))
}

// 辅助函数：从参数中提取消息和zap字段
func extractMessageAndFields(args []interface{}) (string, []zap.Field) {
	if len(args) == 0 {
		return "", nil
	}

	var message string
	var zapFields []zap.Field

	// 第一个参数假定为消息
	switch v := args[0].(type) {
	case string:
		message = v
	default:
		message = fmt.Sprint(v)
	}

	// 从第二个参数开始，处理剩余的参数
	for i := 1; i < len(args); i++ {
		// 检查是否为zap.Field
		if field, ok := args[i].(zap.Field); ok {
			zapFields = append(zapFields, field)
			continue
		}

		// 如果有两个连续的参数，且第一个是字符串，尝试将它们解析为键值对
		if i+1 < len(args) {
			if key, ok := args[i].(string); ok {
				// 根据值的类型创建适当的zap.Field
				switch val := args[i+1].(type) {
				case string:
					zapFields = append(zapFields, zap.String(key, val))
				case int:
					zapFields = append(zapFields, zap.Int(key, val))
				case int64:
					zapFields = append(zapFields, zap.Int64(key, val))
				case float64:
					zapFields = append(zapFields, zap.Float64(key, val))
				case bool:
					zapFields = append(zapFields, zap.Bool(key, val))
				case error:
					zapFields = append(zapFields, zap.Error(val))
				default:
					// 对于其他类型，使用reflection尝试处理
					zapFields = append(zapFields, zap.Any(key, val))
				}
				i++ // 跳过已处理的值
				continue
			}
		}

		// 如果不是键值对，将当前参数添加为带索引的字段
		zapFields = append(zapFields, zap.Any(fmt.Sprintf("arg%d", i), args[i]))
	}

	return message, zapFields
}

// GetCompatLogger 获取兼容dcicollector的Logger实例
func GetCompatLogger() Logger {
	if log == nil {
		fmt.Fprintf(os.Stderr, "警告: GetCompatLogger调用时全局log为nil\n")
		InitDefaultLogger()
	}
	return NewZapAdapter(log)
}
