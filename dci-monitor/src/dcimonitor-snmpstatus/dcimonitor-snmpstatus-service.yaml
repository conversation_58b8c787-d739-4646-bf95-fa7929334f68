---
apiVersion: v1
kind: Service
metadata:
  name: dcimonitor-snmpstatus-service
  namespace: dci
  labels:
    app: dcimonitor-snmpstatus
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
    prometheus.io/path: "/metrics"
spec:
  selector:
    app: dcimonitor-snmpstatus
  type: NodePort
  ports:
  - protocol: TCP
    port: 9090
    targetPort: 9090
    nodePort: 30007  # 指定一个节点端口，与主服务30000端口错开
    name: metrics 