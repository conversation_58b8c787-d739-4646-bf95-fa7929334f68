# DCIMonitor-FlowData 服务部署说明

DCIMonitor-FlowData 服务是 DCI 监控系统的一部分，负责从 Kafka 接收 SNMP 流量数据，处理后以 Prometheus 指标格式暴露，由 Prometheus 服务器定期抓取。

## 1. 服务架构

DCIMonitor-FlowData 服务遵循如下数据流向：
- 从 Kafka 接收 SNMP 流量数据（主题：`dci.monitor.v1.defaultchannel.flows.snmp`）
- 查询 MySQL 数据库获取设备接口映射关系
- 解析数据，提取流量计数器值和其他指标
- 将解析后的数据以 Prometheus 指标格式暴露在 HTTP 端点 `/metrics`
- 由 Prometheus 服务器定期（如每15秒）抓取

## 2. 依赖组件

- **Kafka**: 消息队列，传输 SNMP 采集的流量数据
- **MySQL**: 存储设备接口映射信息
- **Prometheus**: 抓取指标数据并提供查询功能

## 3. 构建与部署

### 3.1 本地构建

```bash
# 进入 dcimonitor-snmpstatus 目录
cd dci-monitor/src/dcimonitor-snmpstatus

# 构建二进制文件 (macOS)
./build.sh -p macos

# 构建二进制文件 (Linux)
./build.sh -p linux

# 构建并导出 Docker 镜像
sudo ./build.sh -p linux -d -t 0.0.1
```

### 3.2 Kubernetes 部署

```bash
# 应用 K8s 配置
kubectl apply -f dcimonitor-snmpstatus-k8s.yaml
kubectl apply -f dcimonitor-snmpstatus-service.yaml

# 检查部署状态
kubectl get pods -n dci -l app=dcimonitor-snmpstatus
kubectl get svc -n dci -l app=dcimonitor-snmpstatus
```

### 3.3 配置 Prometheus 抓取

在 Prometheus 配置文件中添加以下 Job：

```yaml
scrape_configs:
  - job_name: 'dcimonitor-snmpstatus'
    scrape_interval: 15s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['dcimonitor-snmpstatus-service.dci.svc.cluster.local:9090']
```

## 4. 监控指标

DCIMonitor-FlowData 服务暴露的主要指标：

- `dci_snmp_flow_in_octets`: 入向流量字节计数器
- `dci_snmp_flow_out_octets`: 出向流量字节计数器
- `dci_snmp_flow_in_discards`: 入向丢弃包计数器
- `dci_snmp_flow_out_discards`: 出向丢弃包计数器
- `dci_snmp_flow_in_errors`: 入向错误包计数器
- `dci_snmp_flow_out_errors`: 出向错误包计数器

## 5. 计算流速示例

通过 PromQL 计算流速：

```
# 计算入向流量比特率（过去5分钟平均值）
rate(dci_snmp_flow_in_octets{device_ip="************",ifName="10GE1/0/1"}[5m]) * 8

# 计算出向流量比特率（过去5分钟平均值）
rate(dci_snmp_flow_out_octets{device_ip="************",ifName="10GE1/0/1"}[5m]) * 8
```

## 6. 日志和故障排查

日志位置：
- K8s容器日志: `/var/log/dcimonitor-snmpstatus/`
- 本地运行日志: `./logs/`

常见问题排查：
1. 检查与 Kafka 连接: `kubectl logs -n dci <pod-name> | grep Kafka`
2. 检查与 MySQL 连接: `kubectl logs -n dci <pod-name> | grep MySQL`
3. 查看指标端点: `kubectl port-forward -n dci service/dcimonitor-snmpstatus-service 30007:9090`

## 7. 实现参考

本服务基于《19-DCI-流量类数据接收及存储技术设计.md》文档实现，完整代码位于 `dci-monitor/src/dcimonitor/internal/snmp_processor/` 目录。 