# dcimonitor-snmp-processor 配置文件

# 基本服务配置
service_name: "dci-monitor-snmp-processor"
version: "1.0.0"

# 处理器配置
processor:
  # 指标前缀
  metric_prefix: "dci_snmp_"
  
  # 流量指标配置
  enable_in_octets: true
  enable_out_octets: true
  enable_errors: true
  enable_discards: true
  
  # 设备状态指标配置
  enable_device_cpu: true
  enable_device_memory: true
  
  # 接口状态指标配置
  enable_interface_status: true
  
  # 指标全局标签
  metric_labels:
    environment: "production"
    region: "default"


# Kafka配置
kafka:
  # Kafka服务器地址列表
  brokers:
    - "dcikafka.intra.citic-x.com:30010"
    - "dcikafka.intra.citic-x.com:30011"
    - "dcikafka.intra.citic-x.com:30012"
  
  # Kafka主题列表 (多个主题)
  topics:
    - "dci.monitor.vtest.defaultchannel.flows.snmp"           # 流量数据主题
    - "dci.monitor.vtest.defaultchannel.status.devices.snmp"  # 设备状态主题
    - "dci.monitor.vtest.defaultchannel.status.interface.snmp" # 接口状态主题
  
  # Kafka消费者组
  consumer_group: "dci-monitor-snmp-processor"
  
  # Kafka安全配置
  security:
    tls:
      enabled: true
      # 注意：请将这里的路径修改为您的证书在本地的实际路径
      ca_file: "/Users/<USER>/code/dci/dci-workspace/dci-monitor/kafka/scripts/dci-kafka-certs/ca-chain.crt"
      cert_file: "/Users/<USER>/code/dci/dci-workspace/dci-monitor/kafka/scripts/dci-kafka-certs/dci-flowdata.client.crt"
      key_file: "/Users/<USER>/code/dci/dci-workspace/dci-monitor/kafka/scripts/dci-kafka-certs/dci-flowdata.client.key"
    sasl:
      enabled: true
      mechanism: "PLAIN"
      username: "dci-flowdata"
      # 本地测试时，可以直接写入密码，或通过设置 KAFKA_SASL_PASSWORD 环境变量
      password: "flowdata-secret"

# Prometheus配置
prometheus:
  # Prometheus服务器地址
  server: "dciprometheus.intro.citic-x.com:30006"
  # 指标路径
  metrics_path: "/metrics"
  # 指标端口
  metrics_port: 30006
 
# 数据库配置
database:
  driver: "mysql"
  # 数据库主机
  host: "rm-el5i5532025ja5639.mysql.rds.pcloud-ops.citic.com"
  # 数据库端口
  port: 3306
  # 数据库用户名
  username: "root"
  # 数据库密码
  password: "FJkhJ0GBPKZva1w"
  # 数据库名称
  database: "dci"
  # 字符集
  charset: "utf8mb4"
  # 数据库连接字符串 (可选，如果不指定则根据上述参数自动构建)
  # dsn: "user:password@tcp(db-server:3306)/dci_monitor?charset=utf8mb4&parseTime=true"
  # 最大连接数
  max_open_conns: 10
  # 最大空闲连接数
  max_idle_conns: 5
  # 连接最大生命周期（秒）
  conn_max_lifetime: 3600

# 日志配置
logger:
  # 日志级别: debug, info, warn, error
  level: "debug"
  # 日志编码格式: json, console
  encoding: "json"
  # 日志目录
  dir: "./logs"
  # 单个日志文件最大大小(MB)
  maxSize: 100
  # 最大日志文件保留天数
  maxAge: 30
  # 是否压缩旧日志文件
  compress: true

# Mapper配置 端口映射查询Mysql缓存TTL，若空缺则默认5min
mapper:
  cache_ttl_minutes: 30