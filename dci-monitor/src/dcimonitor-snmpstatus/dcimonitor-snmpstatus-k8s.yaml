---
apiVersion: v1
kind: Secret
metadata:
  name: dcimonitor-snmpstatus-sasl-secret
  namespace: dci
type: Opaque
stringData:
  password: "flowdata-secret"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dcimonitor-snmpstatus
  namespace: dci
  labels:
    app: dcimonitor-snmpstatus
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dcimonitor-snmpstatus
  template:
    metadata:
      labels:
        app: dcimonitor-snmpstatus
    spec:
      imagePullSecrets:
        - name: dci-images-key
      containers:
      - name: dcimonitor-snmpstatus
        image: cr.registry.pcloud.citic.com/dci/dcimonitor-snmpstatus:dcimonitor-snmpstatus-0.2.0
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 9090
          name: metrics
        env:
        - name: TZ
          value: "Asia/Shanghai"
        - name: KAFKA_SASL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dcimonitor-snmpstatus-sasl-secret
              key: password
        volumeMounts:
        - name: config-volume
          mountPath: /app/config  # 将配置挂载到 /app/config
        - name: log-volume
          mountPath: /var/log/dcimonitor-snmpstatus
        - name: kafka-client-certs-volume
          mountPath: "/etc/kafka/certs"
          readOnly: true
        command: ["/app/dcimonitor-snmpstatus", "--config", "/app/config/dcimonitor-snmpstatus.yaml"] # 指定配置文件路径
        readinessProbe:
          httpGet:
            path: /metrics
            port: 9090
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /metrics
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 20
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            cpu: "100m"
            memory: "128Mi"
          limits:
            cpu: "2000m"
            memory: "4Gi"
      volumes:
      - name: config-volume
        configMap:
          name: dcimonitor-snmpstatus-config
      - name: log-volume
        emptyDir: {}
      - name: kafka-client-certs-volume
        secret:
          secretName: kafka-client-certs 