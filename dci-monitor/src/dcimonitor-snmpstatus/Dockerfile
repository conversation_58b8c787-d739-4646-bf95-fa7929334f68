# --- Stage 1: Build ---
# 使用官方 Go 镜像作为构建环境
FROM golang:1.24-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制 common 模块，这是本地依赖
# build.sh 脚本会确保在构建时 common 目录存在于上下文中
COPY common /app/common

# 复制 Go 模块相关文件
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY . .

# 编译应用程序，禁用 CGO，使用静态链接
# -ldflags="-s -w" 用于减小二进制文件大小
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags="-s -w" -o /app/dcimonitor-snmpstatus ./cmd/snmpstatus

# --- Stage 2: Final ---
# 使用一个轻量级的 Alpine 镜像作为最终镜像
FROM alpine:latest

# 安装 ca-certificates 以支持 TLS
RUN apk --no-cache add ca-certificates

# 设置工作目录
WORKDIR /app

# 从 builder 阶段复制编译好的二进制文件
COPY --from=builder /app/dcimonitor-snmpstatus .

# 暴露 Prometheus 指标端口
EXPOSE 9090

# 设置容器启动时执行的命令
# 默认配置文件路径现在由 k8s.yaml 中的 command 覆盖
ENTRYPOINT ["/app/dcimonitor-snmpstatus"]
CMD ["--config", "/app/config/dcimonitor-snmp-processor.yaml"] 