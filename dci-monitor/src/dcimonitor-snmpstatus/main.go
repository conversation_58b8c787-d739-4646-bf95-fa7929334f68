package main

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"common/logger"
	sp "snmpstatus/internal/snmp_processor"
	impl "snmpstatus/internal/snmp_processor/impl"

	"github.com/IBM/sarama"
	"go.uber.org/zap"
)

// Consumer 实现sarama.ConsumerGroupHandler接口
type Consumer struct {
	ready   chan bool
	handler func(msg *sarama.ConsumerMessage) error
	logger  logger.Logger
}

// Setup 在消费者会话开始时调用
func (c *Consumer) Setup(sarama.ConsumerGroupSession) error {
	close(c.ready)
	return nil
}

// Cleanup 在消费者会话结束时调用
func (c *Consumer) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim 消费分配给消费者的消息
func (c *Consumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	// NOTE:
	// Do not move the code below to a goroutine.
	// The `ConsumeClaim` itself is called in a goroutine, see:
	// https://github.com/Shopify/sarama/blob/main/consumer_group.go#L27-L29
	//
	// 不要将下面的代码移动到goroutine中。
	// `ConsumeClaim`本身在goroutine中调用，请参阅：
	// https://github.com/Shopify/sarama/blob/main/consumer_group.go#L27-L29
	for msg := range claim.Messages() {
		c.logger.Debug("接收到消息",
			zap.String("topic", msg.Topic),
			zap.Int32("partition", msg.Partition),
			zap.Int64("offset", msg.Offset))

		if err := c.handler(msg); err != nil {
			c.logger.Error("处理消息失败",
				zap.String("topic", msg.Topic),
				zap.Int32("partition", msg.Partition),
				zap.Int64("offset", msg.Offset),
				zap.Error(err))
		} else {
			session.MarkMessage(msg, "") // 标记消息已消费
		}
	}
	return nil
}

func main() {
	// 解析命令行参数
	configFile := flag.String("config", "config/snmpstatus.yaml", "配置文件路径")
	flag.Parse()

	// 1. 加载配置
	config, err := sp.LoadConfig(*configFile)
	if err != nil {
		// 此时logger还未初始化，直接输出到stderr
		fmt.Fprintf(os.Stderr, "加载配置失败: %v\n", err)
		os.Exit(1)
	}

	// 2. 根据配置初始化日志记录器
	logger.InitLogger()
	defer logger.Sync()

	log := logger.GetCompatLogger()
	log.Info("配置加载成功", zap.String("config_file", *configFile))

	// 验证配置
	if err := config.Validate(); err != nil {
		log.Error("配置验证失败", zap.Error(err))
		os.Exit(1)
	}

	// 创建上下文，用于优雅关闭
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建设备端口映射器
	log.Info("正在初始化设备端口映射器...")
	zapLogger := logger.GetLogger()

	mapper, err := sp.NewDevicePortMapper(config.Database, config.Mapper, zapLogger)
	if err != nil {
		log.Error("创建设备端口映射器失败", zap.Error(err))
		os.Exit(1)
	}
	defer mapper.Close()

	// 创建指标导出器
	log.Info("正在初始化Prometheus指标导出器...")
	exporter := sp.NewMetricExporter(zapLogger)
	exporter.RegisterMetrics(config.Processor.MetricPrefix)

	// 创建SNMP状态数据处理器
	log.Info("正在初始化SNMP状态监控处理器...")
	statusHandler := impl.NewProcessStatusHandler(&config.Processor, mapper, exporter, zapLogger)

	// 启动Prometheus指标服务
	log.Info("正在启动Prometheus指标服务...",
		zap.Int("port", config.Prometheus.MetricsPort),
		zap.String("path", config.Prometheus.MetricsPath))

	// 配置Prometheus指标HTTP服务
	http.Handle(config.Prometheus.MetricsPath, exporter.Handler())

	// 打印Prometheus服务访问信息
	prometheusEndpoint := fmt.Sprintf("http://localhost:%d%s", config.Prometheus.MetricsPort, config.Prometheus.MetricsPath)
	log.Info("Prometheus指标将在以下端点提供",
		zap.String("endpoint", prometheusEndpoint),
		zap.String("metric_prefix", config.Processor.MetricPrefix))

	// 如果配置了Prometheus服务器地址，则输出提示
	if config.Prometheus.Server != "" {
		log.Info("已配置Prometheus服务器地址，指标将被远程抓取",
			zap.String("server", config.Prometheus.Server))
	}

	go func() {
		addr := fmt.Sprintf(":%d", config.Prometheus.MetricsPort)
		log.Info("启动Prometheus指标HTTP服务", zap.String("addr", addr))
		if err := http.ListenAndServe(addr, nil); err != nil {
			log.Error("启动Prometheus指标服务失败", zap.Error(err))
		}
	}()

	// 创建Kafka消费者配置
	log.Info("正在配置Kafka消费者...")
	kafkaConfig := sarama.NewConfig()
	kafkaConfig.Version = sarama.V3_0_0_0 // 设置Kafka版本，根据实际情况调整
	kafkaConfig.Consumer.Offsets.Initial = sarama.OffsetNewest
	kafkaConfig.Consumer.Group.Rebalance.Strategy = sarama.NewBalanceStrategyRange()
	kafkaConfig.Consumer.Return.Errors = true
	// 设置全面的元数据，确保客户端能正确解析和使用所有Broker的advertised.listeners
	kafkaConfig.Metadata.Full = true

	// 配置TLS安全设置
	if config.Kafka.Security.TLS.Enabled {
		log.Info("启用Kafka TLS安全连接")
		kafkaConfig.Net.TLS.Enable = true
		tlsConfig := &tls.Config{} // 初始化一个空的 tls.Config

		// 如果提供了CA文件，则配置CA验证 (Server verification)
		if config.Kafka.Security.TLS.CAFile != "" {
			caCert, err := os.ReadFile(config.Kafka.Security.TLS.CAFile)
			if err != nil {
				log.Error("加载CA证书文件失败", zap.Error(err))
				os.Exit(1)
			}
			caCertPool := x509.NewCertPool()
			caCertPool.AppendCertsFromPEM(caCert)
			tlsConfig.RootCAs = caCertPool
			log.Info("已加载CA证书用于服务器验证")
		}

		// 如果提供了证书文件，则配置客户端证书 (Client authentication)
		if config.Kafka.Security.TLS.CertFile != "" && config.Kafka.Security.TLS.KeyFile != "" {
			cert, err := tls.LoadX509KeyPair(config.Kafka.Security.TLS.CertFile, config.Kafka.Security.TLS.KeyFile)
			if err != nil {
				log.Error("加载TLS客户端证书失败", zap.Error(err))
				os.Exit(1)
			}
			tlsConfig.Certificates = []tls.Certificate{cert}
			log.Info("已加载客户端TLS证书")
		}
		kafkaConfig.Net.TLS.Config = tlsConfig
	}

	// 配置SASL安全设置
	if config.Kafka.Security.SASL.Enabled {
		log.Info("启用Kafka SASL认证", zap.String("mechanism", config.Kafka.Security.SASL.Mechanism))
		kafkaConfig.Net.SASL.Enable = true
		kafkaConfig.Net.SASL.Mechanism = sarama.SASLMechanism(config.Kafka.Security.SASL.Mechanism)
		kafkaConfig.Net.SASL.User = config.Kafka.Security.SASL.Username
		kafkaConfig.Net.SASL.Password = config.Kafka.Security.SASL.Password
	}

	// 创建消费者组
	log.Info("正在创建消费者组...",
		zap.Strings("brokers", config.Kafka.Brokers),
		zap.String("group", config.Kafka.ConsumerGroup),
		zap.Strings("topics", config.Kafka.Topics))

	consumerGroup, err := sarama.NewConsumerGroup(
		config.Kafka.Brokers,
		config.Kafka.ConsumerGroup,
		kafkaConfig,
	)
	if err != nil {
		log.Error("创建消费者组失败", zap.Error(err))
		os.Exit(1)
	}
	defer consumerGroup.Close()

	// 创建消费者组处理器
	consumer := &Consumer{
		ready: make(chan bool),
		handler: func(msg *sarama.ConsumerMessage) error {
			return statusHandler.HandleMessage(msg.Value)
		},
		logger: log,
	}

	// 启动消费者组
	log.Info("正在启动Kafka消费者...")
	go func() {
		for {
			// `Consume` 会启动一个循环，直到上下文被取消
			if err := consumerGroup.Consume(ctx, config.Kafka.Topics, consumer); err != nil {
				log.Error("消费者组错误", zap.Error(err))
			}
			// 检查上下文是否已取消，如果已取消，则跳出循环
			if ctx.Err() != nil {
				log.Info("消费者已停止")
				return
			}
			consumer.ready = make(chan bool)
		}
	}()

	// 等待消费者准备就绪
	<-consumer.ready
	log.Info("消费者已准备就绪，开始处理消息")

	// 启动清理过期缓存的定时任务
	go func() {
		ticker := time.NewTicker(5 * time.Minute) // 每5分钟清理一次过期缓存
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				cleaned := mapper.CleanExpiredCache()
				if cleaned > 0 {
					log.Info("已清理过期缓存", zap.Int("cleaned", cleaned))
				}
			case <-ctx.Done():
				return
			}
		}
	}()

	// 等待终止信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	sig := <-sigChan
	timeout := 1 * time.Second
	log.Infof("接收到退出信号 %s，准备%d秒后关闭", sig.String(), timeout/time.Second)

	// 优雅地关闭消费者
	cancel()

	// 等待处理完当前消息
	time.Sleep(timeout)
	log.Info("服务已关闭")
}
