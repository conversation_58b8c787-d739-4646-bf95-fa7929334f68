package snmp_processor

import (
	"math/rand"
	"net/http"
	"sync"
	"time"

	"common/logger"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"go.uber.org/zap"
)

// MetricExporter 指标导出器，负责创建和管理Prometheus指标
type MetricExporter struct {
	registry   *prometheus.Registry
	gauges     map[string]*prometheus.GaugeVec
	counters   map[string]*prometheus.CounterVec
	histograms map[string]*prometheus.HistogramVec
	mutex      sync.RWMutex
	logger     *zap.Logger
	// 存储每个指标的标签名
	labelNames map[string][]string
}

// NewMetricExporter 创建指标导出器
func NewMetricExporter(zapLogger *zap.Logger) *MetricExporter {
	if zapLogger == nil {
		zapLogger = logger.GetLogger()
	}

	registry := prometheus.NewRegistry()
	return &MetricExporter{
		registry:   registry,
		gauges:     make(map[string]*prometheus.GaugeVec),
		counters:   make(map[string]*prometheus.CounterVec),
		histograms: make(map[string]*prometheus.HistogramVec),
		labelNames: make(map[string][]string),
		logger:     zapLogger,
	}
}

// RegisterGauge 注册一个新的Gauge指标
func (m *MetricExporter) RegisterGauge(name string, help string, labelNames []string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.gauges[name]; exists {
		m.logger.Debug("Gauge已存在，跳过注册", zap.String("name", name))
		return
	}

	gauge := prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: name,
			Help: help,
		},
		labelNames,
	)

	m.registry.MustRegister(gauge)
	m.gauges[name] = gauge
	m.labelNames[name] = labelNames

	m.logger.Debug("注册Gauge指标", zap.String("name", name))
}

// RegisterCounter 注册一个新的Counter指标
func (m *MetricExporter) RegisterCounter(name string, help string, labelNames []string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.counters[name]; exists {
		m.logger.Debug("Counter已存在，跳过注册", zap.String("name", name))
		return
	}

	counter := prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: name,
			Help: help,
		},
		labelNames,
	)

	m.registry.MustRegister(counter)
	m.counters[name] = counter
	m.labelNames[name] = labelNames

	m.logger.Debug("注册Counter指标", zap.String("name", name))
}

// RegisterHistogram 注册一个新的Histogram指标
func (m *MetricExporter) RegisterHistogram(name string, help string, buckets []float64, labelNames []string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.histograms[name]; exists {
		m.logger.Debug("Histogram已存在，跳过注册", zap.String("name", name))
		return
	}

	if buckets == nil {
		buckets = prometheus.DefBuckets
	}

	histogram := prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    name,
			Help:    help,
			Buckets: buckets,
		},
		labelNames,
	)

	m.registry.MustRegister(histogram)
	m.histograms[name] = histogram
	m.labelNames[name] = labelNames

	m.logger.Debug("注册Histogram指标", zap.String("name", name))
}

// SetGaugeValue 设置Gauge指标值
func (m *MetricExporter) SetGaugeValue(name string, value float64, labels map[string]string, timestamp ...time.Time) {
	m.mutex.RLock()
	gauge, exists := m.gauges[name]
	labelNames, hasLabels := m.labelNames[name]
	m.mutex.RUnlock()

	if !exists {
		// 如果指标未被预注册，则记录警告并跳过
		m.logger.Warn("尝试设置一个未注册的Gauge指标，已跳过", zap.String("name", name))
		return
	}

	if !hasLabels {
		// 如果没有标签名，直接设置值
		gauge.WithLabelValues().Set(value)
		return
	}

	// 按照注册时的标签顺序提取值
	labelValues := make([]string, len(labelNames))
	for i, name := range labelNames {
		if val, ok := labels[name]; ok {
			labelValues[i] = val
		}
	}

	// 设置指标值
	gauge.WithLabelValues(labelValues...).Set(value)

	// 记录日志 - 使用随机抽样，避免记录过多日志
	// 每1000次调用记录一条日志，或者第一次设置该指标时记录
	if !exists || (rand.Intn(1000) == 0) {
		deviceInfo := ""
		portInfo := ""
		if device, ok := labels["device_name"]; ok {
			deviceInfo = device
		}
		if port, ok := labels["port_name"]; ok {
			portInfo = port
		}

		m.logger.Info("设置Gauge指标值",
			zap.String("metric", name),
			zap.Float64("value", value),
			zap.String("device", deviceInfo),
			zap.String("port", portInfo))
	}

	// 注意：Prometheus客户端库不直接支持设置指标时间戳
	// 时间戳由Prometheus服务器在抓取时设置
	// 如果需要自定义时间戳，需要使用远程写入API或其他方式
}

// IncrementCounter 增加Counter指标值
func (m *MetricExporter) IncrementCounter(name string, value float64, labels map[string]string) {
	m.mutex.RLock()
	counter, exists := m.counters[name]
	labelNames, hasLabels := m.labelNames[name]
	m.mutex.RUnlock()

	if !exists {
		// 如果该指标不存在，则创建一个
		labelNames = make([]string, 0, len(labels))
		for k := range labels {
			labelNames = append(labelNames, k)
		}
		m.RegisterCounter(name, name+" metric", labelNames)
		m.mutex.RLock()
		counter = m.counters[name]
		m.mutex.RUnlock()
	}

	if !hasLabels {
		// 如果没有标签名，直接设置值
		counter.WithLabelValues().Add(value)
		return
	}

	// 按照注册时的标签顺序提取值
	labelValues := make([]string, len(labelNames))
	for i, name := range labelNames {
		if val, ok := labels[name]; ok {
			labelValues[i] = val
		}
	}

	counter.WithLabelValues(labelValues...).Add(value)
}

// SetCounterValue 设置Counter指标值（用于存储原始计数器值）
func (m *MetricExporter) SetCounterValue(name string, value float64, labels map[string]string, timestamp time.Time) {
	// 由于Counter只能增加不能设置，我们使用Gauge来存储原始计数器值
	// 创建一个带有_raw后缀的Gauge指标
	rawName := name + "_raw"
	m.SetGaugeValue(rawName, value, labels)

	// 每1000次调用记录一条INFO日志
	if rand.Intn(1000) == 0 {
		deviceInfo := ""
		portInfo := ""
		if device, ok := labels["device_name"]; ok {
			deviceInfo = device
		}
		if port, ok := labels["port_name"]; ok {
			portInfo = port
		}

		m.logger.Info("存储原始计数器值",
			zap.String("metric", name),
			zap.String("raw_gauge", rawName),
			zap.Float64("value", value),
			zap.Time("timestamp", timestamp),
			zap.String("device", deviceInfo),
			zap.String("port", portInfo))
	} else {
		// Debug级别日志
		m.logger.Debug("存储原始计数器值",
			zap.String("name", name),
			zap.String("raw_gauge", rawName),
			zap.Float64("value", value),
			zap.Time("timestamp", timestamp))
	}
}

// ObserveHistogram 观察Histogram指标值
func (m *MetricExporter) ObserveHistogram(name string, value float64, labels map[string]string) {
	m.mutex.RLock()
	histogram, exists := m.histograms[name]
	labelNames, hasLabels := m.labelNames[name]
	m.mutex.RUnlock()

	if !exists {
		// 如果该指标不存在，则创建一个
		labelNames = make([]string, 0, len(labels))
		for k := range labels {
			labelNames = append(labelNames, k)
		}
		m.RegisterHistogram(name, name+" metric", nil, labelNames)
		m.mutex.RLock()
		histogram = m.histograms[name]
		m.mutex.RUnlock()
	}

	if !hasLabels {
		// 如果没有标签名，直接设置值
		histogram.WithLabelValues().Observe(value)
		return
	}

	// 按照注册时的标签顺序提取值
	labelValues := make([]string, len(labelNames))
	for i, name := range labelNames {
		if val, ok := labels[name]; ok {
			labelValues[i] = val
		}
	}

	histogram.WithLabelValues(labelValues...).Observe(value)
}

// Handler 返回HTTP处理程序，用于暴露指标
func (m *MetricExporter) Handler() http.Handler {
	return promhttp.HandlerFor(m.registry, promhttp.HandlerOpts{})
}

// kafka发来的原始数据
// {
// 	"fields": {
// 		"ifConnectorPresent": 1,
// 		"ifCounterDiscontinuityTime": 0,
// 		"ifHCInBroadcastPkts": 0,
// 		"ifHCInMulticastPkts": 0,
// 		"ifHCInOctets": 0,
// 		"ifHCInUcastPkts": 0,
// 		"ifHCOutBroadcastPkts": 0,
// 		"ifHCOutMulticastPkts": 0,
// 		"ifHCOutOctets": 0,
// 		"ifHCOutUcastPkts": 0,
// 		"ifHighSpeed": 10000,
// 		"ifInBroadcastPkts": 0,
// 		"ifInMulticastPkts": 0,
// 		"ifLinkUpDownTrapEnable": 1,
// 		"ifName": "10GE1/0/27",
// 		"ifOutBroadcastPkts": 0,
// 		"ifOutMulticastPkts": 0,
// 		"ifPromiscuousMode": 2,
// 		"interface_speed": "10G"
// 	},
// 	"name": "snmp_HCinterface",
// 	"tags": {
// 		"agent_id": "dci-agent",
// 		"agent_ip": "127.0.0.1",
// 		"device_ip": "************",
// 		"host": "dci-agent",
// 		"ifIndex": "37",
// 		"sysName": "SW1",
// 		"traffic_unit": "bytes"
// 	},
// 	"timestamp": 1749632306
// }

// RegisterMetrics 注册所有预定义指标
func (m *MetricExporter) RegisterMetrics(prefix string) {
	// 记录指标前缀
	m.logger.Info("注册Prometheus指标", zap.String("prefix", prefix))

	// 定义所有流量相关的指标名称列表
	flowMetricFields := []string{
		"ifHCInOctets",
		"ifHCOutOctets",
	}

	// 定义设备状态相关的指标名称
	statusMetricFields := []string{
		"hwEntityCpuUsage", // CPU使用率
		"hwEntityMemUsage", // 内存使用率
		"ifOperStatus",     // 接口操作状态
		"ifAdminStatus",    // 接口管理状态
	}

	// 定义指标标签
	commonLabels := []string{
		"device_id", "device_name", "device_ip", "agent_id", "agent_ip",
	}

	// 流量指标特有标签
	flowMetricLabels := append(commonLabels,
		"port_id", "logic_port_id", "port_name", "if_index", "vni_id",
		"ifName", "interface_speed", "ifHighSpeed",
	)

	// CPU/内存指标特有标签
	deviceStatusLabels := append(commonLabels,
		"entity_index", "entity_name", "entity_type",
	)

	// 接口状态指标特有标签
	interfaceStatusLabels := append(commonLabels,
		"port_id", "port_name", "if_index", "ifName",
	)

	// 注册所有流量指标
	for _, field := range flowMetricFields {
		metricName := prefix + "flow_" + field
		m.RegisterGauge(
			metricName,
			"SNMP interface flow metric for "+field,
			flowMetricLabels,
		)
	}

	// 注册所有设备状态指标
	// CPU使用率
	m.RegisterGauge(
		prefix+"status_cpu_usage",
		"SNMP device CPU usage metric (percent)",
		deviceStatusLabels,
	)

	// 内存使用率
	m.RegisterGauge(
		prefix+"status_memory_usage",
		"SNMP device memory usage metric (percent)",
		deviceStatusLabels,
	)

	// 接口操作状态
	m.RegisterGauge(
		prefix+"status_interface",
		"SNMP interface operational status (1=up, 2=down)",
		interfaceStatusLabels,
	)

	// 接口管理/物理状态
	m.RegisterGauge(
		prefix+"status_interface_admin",
		"SNMP interface administrative status (1=up, 2=down, 3=testing)",
		interfaceStatusLabels,
	)

	// 保存注册的所有指标名称，用于最后打印摘要
	metricNames := []string{}

	// 注册处理器统计指标
	procMsgTotalName := prefix + "processor_messages_total"
	metricNames = append(metricNames, procMsgTotalName)
	m.RegisterCounter(
		procMsgTotalName,
		"Total number of messages processed",
		[]string{},
	)

	procMsgSuccessName := prefix + "processor_messages_success"
	metricNames = append(metricNames, procMsgSuccessName)
	m.RegisterCounter(
		procMsgSuccessName,
		"Number of messages successfully processed",
		[]string{},
	)

	procMsgErrorName := prefix + "processor_messages_error"
	metricNames = append(metricNames, procMsgErrorName)
	m.RegisterCounter(
		procMsgErrorName,
		"Number of messages with processing errors",
		[]string{},
	)

	// 打印注册的所有指标名称摘要
	m.logger.Info("已注册核心Prometheus指标", zap.Strings("metrics", metricNames))
	m.logger.Info("已预注册所有流量相关指标", zap.Int("count", len(flowMetricFields)))
	m.logger.Info("已预注册所有状态相关指标", zap.Int("count", len(statusMetricFields)))
	m.logger.Info("可用的PromQL查询示例",
		zap.String("入向流量(bps)", "rate("+prefix+"flow_ifHCInOctets[5m]) * 8"),
		zap.String("出向流量(bps)", "rate("+prefix+"flow_ifHCOutOctets[5m]) * 8"),
		zap.String("CPU使用率", prefix+"status_cpu_usage"),
		zap.String("内存使用率", prefix+"status_memory_usage"),
		zap.String("接口操作状态", prefix+"status_interface"),
		zap.String("接口管理状态", prefix+"status_interface_admin"))
}
