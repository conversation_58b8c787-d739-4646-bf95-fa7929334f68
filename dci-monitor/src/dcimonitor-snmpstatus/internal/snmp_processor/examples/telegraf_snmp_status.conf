# Telegraf 配置示例 - SNMP监控 (流量+状态)

# 全局配置
[agent]
  interval = "60s"
  round_interval = true
  metric_batch_size = 1000
  metric_buffer_limit = 10000
  collection_jitter = "0s"
  flush_interval = "10s"
  flush_jitter = "0s"
  precision = ""
  hostname = "dci-agent"
  omit_hostname = false

# 设备流量采集 - SNMP HCinterface流量数据
[[inputs.snmp]]
  # 采集频率相关设置
  interval = "60s"
  
  # 设备连接信息
  agents = ["************"]
  version = 2
  community = "public"
  retries = 3
  timeout = "5s"
  
  # 接口流量数据采集
  [[inputs.snmp.table]]
    name = "snmp_HCinterface"
    inherit_tags = ["hostname", "agent_address", "agent_id"]
    
    # 获取ifName信息
    [[inputs.snmp.table.field]]
      oid = "IF-MIB::ifName"
      name = "ifName"
      is_tag = true
      
    # 获取接口流量信息
    [[inputs.snmp.table.field]]
      oid = "IF-MIB::ifHCInOctets"
      name = "ifHCInOctets"
    [[inputs.snmp.table.field]]
      oid = "IF-MIB::ifHCOutOctets"
      name = "ifHCOutOctets"
    [[inputs.snmp.table.field]]
      oid = "IF-MIB::ifHighSpeed"
      name = "ifHighSpeed"
      
  [inputs.snmp.tagpass]
    data_source_type = ["snmp_flow"]
    
  # 添加必要标签
  [inputs.snmp.tags]
    agent_id = "dci-agent-01"
    data_source_type = "snmp_flow"
    traffic_unit = "bytes"


# 设备状态采集 - CPU和内存使用率
[[inputs.snmp]]
  # 采集频率可以与流量不同
  interval = "30s"
  
  # 设备连接信息
  agents = ["************"]
  version = 2
  community = "public"
  retries = 3
  timeout = "5s"
  
  # CPU使用率表格采集
  [[inputs.snmp.table]]
    name = "device_status"
    inherit_tags = ["hostname", "agent_address", "agent_id"]
    
    # 实体索引
    [[inputs.snmp.table.field]]
      oid = "HUAWEI-ENTITY-EXT-MIB::hwEntityIndex"
      name = "entity_index"
      is_tag = true
    
    # 实体名称
    [[inputs.snmp.table.field]]
      oid = "HUAWEI-ENTITY-EXT-MIB::hwEntityName"
      name = "entity_name"
      is_tag = true
      
    # 实体类型
    [[inputs.snmp.table.field]]
      oid = "HUAWEI-ENTITY-EXT-MIB::hwEntityType"
      name = "entity_type" 
      is_tag = true
      
    # CPU使用率
    [[inputs.snmp.table.field]]
      oid = "HUAWEI-ENTITY-EXT-MIB::hwEntityCpuUsage"
      name = "hwEntityCpuUsage"
      
    # 内存使用率
    [[inputs.snmp.table.field]]
      oid = "HUAWEI-ENTITY-EXT-MIB::hwEntityMemUsage"
      name = "hwEntityMemUsage"

  [inputs.snmp.tagpass]
    data_source_type = ["snmp_status"]
    
  # 添加必要标签
  [inputs.snmp.tags]
    agent_id = "dci-agent-01"
    data_source_type = "snmp_status"
    

# 接口状态采集 - 端口Up/Down状态 
[[inputs.snmp]]
  # 采集频率
  interval = "30s"
  
  # 设备连接信息
  agents = ["************"]
  version = 2
  community = "public"
  retries = 3
  timeout = "5s"
  
  # 接口状态表格
  [[inputs.snmp.table]]
    name = "interface_status"
    inherit_tags = ["hostname", "agent_address", "agent_id"]
    
    # 获取ifIndex
    [[inputs.snmp.table.field]]
      oid = "IF-MIB::ifIndex"
      name = "ifIndex"
      is_tag = true
      
    # 获取ifName
    [[inputs.snmp.table.field]]
      oid = "IF-MIB::ifName"
      name = "ifName"
    
    # 获取接口操作状态 (1=up, 2=down)
    [[inputs.snmp.table.field]]
      oid = "IF-MIB::ifOperStatus"
      name = "ifOperStatus"
      
    # 获取接口管理状态
    [[inputs.snmp.table.field]]
      oid = "IF-MIB::ifAdminStatus"
      name = "ifAdminStatus"

  [inputs.snmp.tagpass]
    data_source_type = ["snmp_status"]
    
  # 添加必要标签
  [inputs.snmp.tags]
    agent_id = "dci-agent-01"
    data_source_type = "snmp_status"


# Kafka输出 - 流量数据专用主题
[[outputs.kafka]]
  # Kafka集群配置
  brokers = ["kafka-broker1:9092", "kafka-broker2:9092"]
  topic = "dci.monitor.v1.defaultchannel.flow"
  
  # 数据格式
  data_format = "json"
  
  # 安全设置
  sasl_username = "${KAFKA_USERNAME}"
  sasl_password = "${KAFKA_PASSWORD}"
  sasl_mechanism = "PLAIN"
  tls_ca = "/etc/dciagent/kafka/ca.pem"
  tls_cert = "/etc/dciagent/kafka/client.pem"
  tls_key = "/etc/dciagent/kafka/client.key"
  
  # 只发送流量数据
  [outputs.kafka.tagpass]
    data_source_type = ["snmp_flow"]


# Kafka输出 - 设备状态专用主题
[[outputs.kafka]]
  # Kafka集群配置
  brokers = ["kafka-broker1:9092", "kafka-broker2:9092"]
  topic = "dci.monitor.v1.defaultchannel.status.device"
  
  # 数据格式
  data_format = "json"
  
  # 安全设置 - 与上面保持一致
  sasl_username = "${KAFKA_USERNAME}"
  sasl_password = "${KAFKA_PASSWORD}"
  sasl_mechanism = "PLAIN"
  tls_ca = "/etc/dciagent/kafka/ca.pem"
  tls_cert = "/etc/dciagent/kafka/client.pem"
  tls_key = "/etc/dciagent/kafka/client.key"
  
  # 只发送设备状态数据
  [outputs.kafka.tagpass]
    data_source_type = ["snmp_status"]
    name = ["device_status"]


# Kafka输出 - 接口状态专用主题
[[outputs.kafka]]
  # Kafka集群配置
  brokers = ["kafka-broker1:9092", "kafka-broker2:9092"]
  topic = "dci.monitor.v1.defaultchannel.status.interface"
  
  # 数据格式
  data_format = "json"
  
  # 安全设置 - 与上面保持一致
  sasl_username = "${KAFKA_USERNAME}"
  sasl_password = "${KAFKA_PASSWORD}"
  sasl_mechanism = "PLAIN"
  tls_ca = "/etc/dciagent/kafka/ca.pem"
  tls_cert = "/etc/dciagent/kafka/client.pem"
  tls_key = "/etc/dciagent/kafka/client.key"
  
  # 只发送接口状态数据
  [outputs.kafka.tagpass]
    data_source_type = ["snmp_status"]
    name = ["interface_status"] 