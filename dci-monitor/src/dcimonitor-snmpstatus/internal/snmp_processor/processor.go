package snmp_processor

import (
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"common/logger"

	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
)

// DeviceMapper 设备映射接口
type DeviceMapper interface {
	GetPortMapping(deviceIP, portName, vniID string) (*PortMapping, error)
	GetDeviceIDByIP(deviceIP, sysName, ifIndex string) (string, string, error)
	GetStats() map[string]int64
}

// MetricsExporter 指标导出接口
type MetricsExporter interface {
	SetGaugeValue(name string, value float64, labels map[string]string, timestamp ...time.Time)
	IncrementCounter(name string, value float64, labels map[string]string)
}

// SNMPProcessor SNMP数据处理器基类
type SNMPProcessor struct {
	mapper      DeviceMapper
	exporter    MetricsExporter
	logger      *zap.Logger
	config      *ProcessorConfig
	countersMtx sync.RWMutex
	counters    struct {
		total        int64
		success      int64
		parseError   int64
		processError int64
		noMapping    int64
	}
}

// SNMPFlowProcessor 流量数据处理器，继承自SNMPProcessor
type SNMPFlowProcessor struct {
	processor *SNMPProcessor
}

// SNMPStatusProcessor 状态数据处理器，继承自SNMPProcessor
type SNMPStatusProcessor struct {
	processor *SNMPProcessor
}

// FlowDataProcessor 流量数据处理器 (为保持向后兼容性)
type FlowDataProcessor struct {
	mapper      DeviceMapper
	exporter    MetricsExporter
	logger      *zap.Logger
	config      *ProcessorConfig
	countersMtx sync.RWMutex
	counters    struct {
		total        int64
		success      int64
		parseError   int64
		processError int64
		noMapping    int64
	}
}

// NewSNMPProcessor 创建基础SNMP处理器
func NewSNMPProcessor(config *ProcessorConfig, mapper DeviceMapper, exporter MetricsExporter, zapLogger *zap.Logger) *SNMPProcessor {
	if zapLogger == nil {
		zapLogger = logger.GetLogger()
	}

	if config == nil {
		config = &ProcessorConfig{
			MetricPrefix:          "dci_snmp_",
			EnableInOctets:        true,
			EnableOutOctets:       true,
			EnableErrors:          true,
			EnableDiscards:        true,
			EnableDeviceCPU:       true,
			EnableDeviceMemory:    true,
			EnableInterfaceStatus: true,
			MetricLabels:          make(map[string]string),
		}
	}

	return &SNMPProcessor{
		mapper:      mapper,
		exporter:    exporter,
		logger:      zapLogger,
		config:      config,
		countersMtx: sync.RWMutex{},
		counters: struct {
			total        int64
			success      int64
			parseError   int64
			processError int64
			noMapping    int64
		}{},
	}
}

// NewSNMPFlowProcessor 创建流量数据处理器
func NewSNMPFlowProcessor(config *ProcessorConfig, mapper DeviceMapper, exporter MetricsExporter, zapLogger *zap.Logger) *SNMPFlowProcessor {
	baseProcessor := NewSNMPProcessor(config, mapper, exporter, zapLogger)
	return &SNMPFlowProcessor{
		processor: baseProcessor,
	}
}

// NewSNMPStatusProcessor 创建状态数据处理器
func NewSNMPStatusProcessor(config *ProcessorConfig, mapper DeviceMapper, exporter MetricsExporter, zapLogger *zap.Logger) *SNMPStatusProcessor {
	baseProcessor := NewSNMPProcessor(config, mapper, exporter, zapLogger)
	return &SNMPStatusProcessor{
		processor: baseProcessor,
	}
}

// NewFlowDataProcessor 创建流量数据处理器 (向后兼容函数)
func NewFlowDataProcessor(config *ProcessorConfig, mapper DeviceMapper, exporter MetricsExporter, zapLogger *zap.Logger) *FlowDataProcessor {
	if zapLogger == nil {
		zapLogger = logger.GetLogger()
	}

	if config == nil {
		config = &ProcessorConfig{
			MetricPrefix:    "dci_snmp_flow_",
			EnableInOctets:  true,
			EnableOutOctets: true,
			EnableErrors:    true,
			EnableDiscards:  true,
			MetricLabels:    make(map[string]string),
		}
	}

	return &FlowDataProcessor{
		mapper:      mapper,
		exporter:    exporter,
		logger:      zapLogger,
		config:      config,
		countersMtx: sync.RWMutex{},
		counters: struct {
			total        int64
			success      int64
			parseError   int64
			processError int64
			noMapping    int64
		}{},
	}
}

// ProcessSNMPData 处理SNMP数据
func (p *SNMPProcessor) ProcessSNMPData(data []byte) error {
	// 增加计数器
	p.countersMtx.Lock()
	p.counters.total++
	p.countersMtx.Unlock()

	// 记录原始数据进行调试
	p.logger.Debug("收到原始数据", zap.String("data", string(data)))

	// 首先解析为通用SNMPData结构
	var snmpData SNMPData
	if err := jsoniter.Unmarshal(data, &snmpData); err != nil {
		p.countersMtx.Lock()
		p.counters.parseError++
		p.countersMtx.Unlock()
		p.logger.Error("解析SNMP数据失败", zap.Error(err))

		// 更新处理错误指标
		p.exporter.IncrementCounter(p.config.MetricPrefix+"processor_messages_error", 1, nil)
		return err
	}

	// 根据数据类型分发到不同的处理函数
	dataType := p.determineDataType(data)

	switch dataType {
	case "flow":
		return p.processFlowData(data)
	case "device_status":
		return p.processDeviceStatusData(data)
	case "interface_status":
		return p.processInterfaceStatusData(data)
	default:
		p.logger.Warn("未知的SNMP数据类型，默认作为流量数据处理", zap.String("data_type", dataType))
		return p.processFlowData(data)
	}
}

// determineDataType 确定数据类型
func (p *SNMPProcessor) determineDataType(data []byte) string {
	// 先解析为通用结构，检查name字段和特征字段
	var rawData map[string]interface{}
	if err := jsoniter.Unmarshal(data, &rawData); err != nil {
		p.logger.Error("解析数据失败", zap.Error(err))
		return "unknown"
	}

	// 检查name字段
	if name, ok := rawData["name"].(string); ok {
		// 根据name字段判断
		if name == "device_status" || name == "entity_status" {
			return "device_status"
		}
		if name == "interface_status" {
			return "interface_status"
		}
		// 流量数据通常为snmp
		if name == "snmp" {
			return "flow"
		}
	}

	// 检查fields字段中的特征
	if fields, ok := rawData["fields"].(map[string]interface{}); ok {
		// CPU或内存使用率存在时判断为设备状态
		if _, hasCPU := fields["hwEntityCpuUsage"]; hasCPU {
			return "device_status"
		}
		if _, hasMemory := fields["hwEntityMemUsage"]; hasMemory {
			return "device_status"
		}
		// 接口状态字段存在时判断为接口状态
		if _, hasIfStatus := fields["ifOperStatus"]; hasIfStatus {
			return "interface_status"
		}
		// 特征字段为流量计数器时判断为流量数据
		if _, hasInOctets := fields["ifHCInOctets"]; hasInOctets {
			return "flow"
		}
		if _, hasOutOctets := fields["ifHCOutOctets"]; hasOutOctets {
			return "flow"
		}
	}

	// 默认返回未知类型
	return "unknown"
}

// processFlowData 处理流量数据
func (p *SNMPProcessor) processFlowData(data []byte) error {
	var flowData FlowData
	if err := jsoniter.Unmarshal(data, &flowData); err != nil {
		p.countersMtx.Lock()
		p.counters.parseError++
		p.countersMtx.Unlock()
		p.logger.Error("解析流量数据失败", zap.Error(err))
		return err
	}

	// 提取设备IP
	deviceIP := p.extractDeviceIP(flowData)
	if deviceIP == "" {
		p.logProcessingError("无法提取有效的设备IP")
		return fmt.Errorf("无法提取有效的设备IP")
	}
	flowData.DeviceIP = deviceIP

	// 提取Agent信息
	if flowData.AgentID == "" {
		if agentID, ok := flowData.Tags["agent_id"]; ok {
			flowData.AgentID = agentID
		} else if hostName, ok := flowData.Tags["host"]; ok {
			flowData.AgentID = hostName
		} else {
			return fmt.Errorf("无法提取有效的AgentID")
		}
	}

	if flowData.AgentIP == "" {
		if agentIP, ok := flowData.Tags["agent_ip"]; ok {
			flowData.AgentIP = agentIP
		}
	}

	// 提取时间戳
	var telegrafTimestamp time.Time
	if flowData.Timestamp > 0 {
		telegrafTimestamp = time.Unix(flowData.Timestamp, 0)
	} else {
		return fmt.Errorf("无法提取有效的采集Timestamp")
	}

	// 提取接口信息
	var ifNameStr, ifIndexStr string
	var ok bool

	// 提取接口名称
	ifNameStr, ok = flowData.Fields["ifName"].(string)
	if !ok {
		ifNameStr, ok = flowData.Tags["ifName"]
		if !ok {
			p.logProcessingError("缺少必要的ifName字段")
			return fmt.Errorf("缺少必要的ifName字段")
		}
	}

	// 提取接口索引
	ifIndexStr, ok = flowData.Tags["ifIndex"]
	if !ok {
		ifIndexStr = ""
	}

	if ifNameStr == "" {
		p.logProcessingError("缺少必要的ifName字段")
		return fmt.Errorf("缺少必要的ifName字段")
	}

	// 处理接口流量数据
	return p.processInterfaceFlowData(flowData, ifNameStr, ifIndexStr, telegrafTimestamp)
}

// processDeviceStatusData 处理设备状态数据
func (p *SNMPProcessor) processDeviceStatusData(data []byte) error {
	// 解析为DeviceStatusData结构
	var statusData DeviceStatusData
	if err := jsoniter.Unmarshal(data, &statusData); err != nil {
		p.countersMtx.Lock()
		p.counters.parseError++
		p.countersMtx.Unlock()
		p.logger.Error("解析设备状态数据失败", zap.Error(err))
		return err
	}

	// 提取设备IP
	var ok bool
	statusData.DeviceIP, ok = statusData.Tags["device_ip"]
	if !ok || !isValidIP(statusData.DeviceIP) {
		p.logProcessingError("无法提取有效的设备IP")
		return fmt.Errorf("无法提取有效的设备IP")
	}

	// 提取Agent信息
	if agentID, ok := statusData.Tags["agent_id"]; ok {
		statusData.AgentID = agentID
	} else if hostName, ok := statusData.Tags["host"]; ok {
		statusData.AgentID = hostName
	} else {
		statusData.AgentID = "unknown-agent"
	}

	if agentIP, ok := statusData.Tags["agent_ip"]; ok {
		statusData.AgentIP = agentIP
	}

	// 提取实体信息
	statusData.EntityIndex = statusData.Tags["entity_index"]
	statusData.EntityName = statusData.Tags["entity_name"]
	statusData.EntityType = statusData.Tags["entity_type"]

	// 获取设备系统名称
	sysName := statusData.Tags["sysName"]

	// 获取设备ID
	deviceID, deviceName, err := p.mapper.GetDeviceIDByIP(statusData.DeviceIP, sysName, "")
	if err != nil {
		p.logger.Warn("通过设备IP查询设备ID失败",
			zap.String("device_ip", statusData.DeviceIP),
			zap.String("sysName", sysName),
			zap.Error(err))
		// 使用IP作为临时设备ID
		deviceID = statusData.DeviceIP
		deviceName = sysName
		if deviceName == "" {
			deviceName = "UnknownDevice"
		}
	}

	// 处理CPU使用率
	if p.config.EnableDeviceCPU {
		// 尝试读取原始字段名，如果不存在则尝试读取重命名后的字段名
		cpuUsage, ok := p.extractFloat64(statusData.Fields, "hwEntityCpuUsage")
		if !ok {
			cpuUsage, ok = p.extractFloat64(statusData.Fields, "dci_switch_cpu_usage_percent")
		}

		if ok && cpuUsage > 0 { // 只处理大于0的CPU值，忽略无效数据
			// 获取实体索引
			entityIndex := statusData.Tags["entPhysicalIndex"]
			entityName := statusData.Tags["entPhysicalName"]
			entityClass := statusData.Tags["entPhysicalClass"]

			p.logger.Debug("处理设备CPU数据",
				zap.String("device_ip", statusData.DeviceIP),
				zap.String("entity_index", entityIndex),
				zap.String("entity_name", entityName),
				zap.String("entity_class", entityClass),
				zap.Float64("cpu_usage", cpuUsage))

			// 准备标签
			labels := map[string]string{
				"device_id":    deviceID,
				"device_name":  deviceName,
				"device_ip":    statusData.DeviceIP,
				"agent_id":     statusData.AgentID,
				"agent_ip":     statusData.AgentIP,
				"entity_index": entityIndex,
				"entity_name":  entityName,
				"entity_type":  "cpu",
			}

			// 记录并导出CPU使用率指标
			metricName := p.config.MetricPrefix + "status_cpu_usage"
			timestamp := time.Unix(statusData.Timestamp, 0)
			p.exporter.SetGaugeValue(metricName, cpuUsage, labels, timestamp)

			p.logger.Debug("处理CPU使用率指标",
				zap.String("device_name", deviceName),
				zap.String("entity_name", statusData.EntityName),
				zap.Float64("value", cpuUsage))
		}
	}

	// 处理内存使用率
	if p.config.EnableDeviceMemory {
		// 尝试读取原始字段名，如果不存在则尝试读取重命名后的字段名
		memUsage, ok := p.extractFloat64(statusData.Fields, "hwEntityMemUsage")
		if !ok {
			memUsage, ok = p.extractFloat64(statusData.Fields, "dci_switch_memory_usage_percent")
		}

		if ok && memUsage > 0 { // 只处理大于0的内存值，忽略无效数据
			// 获取实体索引
			entityIndex := statusData.Tags["entPhysicalIndex"]
			entityName := statusData.Tags["entPhysicalName"]
			entityClass := statusData.Tags["entPhysicalClass"]

			p.logger.Debug("处理设备内存数据",
				zap.String("device_ip", statusData.DeviceIP),
				zap.String("entity_index", entityIndex),
				zap.String("entity_name", entityName),
				zap.String("entity_class", entityClass),
				zap.Float64("mem_usage", memUsage))

			// 准备标签
			labels := map[string]string{
				"device_id":    deviceID,
				"device_name":  deviceName,
				"device_ip":    statusData.DeviceIP,
				"agent_id":     statusData.AgentID,
				"agent_ip":     statusData.AgentIP,
				"entity_index": entityIndex,
				"entity_name":  entityName,
				"entity_type":  "memory",
			}

			// 记录并导出内存使用率指标
			metricName := p.config.MetricPrefix + "status_memory_usage"
			timestamp := time.Unix(statusData.Timestamp, 0)
			p.exporter.SetGaugeValue(metricName, memUsage, labels, timestamp)

			p.logger.Debug("处理内存使用率指标",
				zap.String("device_name", deviceName),
				zap.String("entity_name", statusData.EntityName),
				zap.Float64("value", memUsage))
		}
	}

	// 更新计数器和指标
	p.countersMtx.Lock()
	p.counters.success++
	p.countersMtx.Unlock()
	p.exporter.IncrementCounter(p.config.MetricPrefix+"processor_messages_total", 1, nil)
	p.exporter.IncrementCounter(p.config.MetricPrefix+"processor_messages_success", 1, nil)

	return nil
}

// processInterfaceStatusData 处理接口状态数据
func (p *SNMPProcessor) processInterfaceStatusData(data []byte) error {
	// 解析为InterfaceStatusData结构
	var statusData InterfaceStatusData
	if err := jsoniter.Unmarshal(data, &statusData); err != nil {
		p.countersMtx.Lock()
		p.counters.parseError++
		p.countersMtx.Unlock()
		p.logger.Error("解析接口状态数据失败", zap.Error(err))
		return err
	}

	// 提取设备IP
	var ok bool
	statusData.DeviceIP, ok = statusData.Tags["device_ip"]
	if !ok || !isValidIP(statusData.DeviceIP) {
		p.logProcessingError("接口状态数据中无法提取有效的设备IP")
		return fmt.Errorf("接口状态数据中无法提取有效的设备IP")
	}

	// 提取Agent信息
	if agentID, ok := statusData.Tags["agent_id"]; ok {
		statusData.AgentID = agentID
	} else if hostName, ok := statusData.Tags["host"]; ok {
		statusData.AgentID = hostName
	} else {
		statusData.AgentID = "unknown-agent"
	}

	if agentIP, ok := statusData.Tags["agent_ip"]; ok {
		statusData.AgentIP = agentIP
	}

	// 提取接口名称与索引
	ifIndexStr := statusData.Tags["ifIndex"]
	ifNameStr, ok := statusData.Fields["ifName"].(string)
	if !ok {
		ifNameStr, ok = statusData.Tags["ifName"]
		if !ok {
			p.logProcessingError("接口状态数据中缺少必要的ifName字段")
			return fmt.Errorf("接口状态数据中缺少必要的ifName字段")
		}
	}

	// 从ifName中分离物理端口和VNI
	basePortName := ifNameStr
	vniID := extractVNIFromPortName(ifNameStr)
	if vniID != "" {
		lastIndex := strings.LastIndex(ifNameStr, ".")
		if lastIndex != -1 {
			basePortName = ifNameStr[:lastIndex]
		}
	}

	p.logger.Debug("开始处理接口状态数据",
		zap.String("device_ip", statusData.DeviceIP),
		zap.String("if_index", ifIndexStr),
		zap.String("if_name", ifNameStr),
		zap.String("base_port_name", basePortName),
		zap.String("vni_id", vniID))

	// 获取设备系统名称
	sysName := statusData.Tags["sysName"]

	// 获取端口映射
	deviceID, deviceName, err := p.mapper.GetDeviceIDByIP(statusData.DeviceIP, sysName, ifIndexStr)
	if err != nil {
		p.logger.Warn("通过设备IP查询设备ID失败",
			zap.String("device_ip", statusData.DeviceIP),
			zap.String("sysName", sysName),
			zap.Error(err))
		deviceID = statusData.DeviceIP
		deviceName = sysName
		if deviceName == "" {
			deviceName = "UnknownDevice"
		}
	}

	// 查询设备端口映射
	var portID, portName string
	portMapping, err := p.mapper.GetPortMapping(statusData.DeviceIP, basePortName, vniID)
	if err != nil {
		p.logger.Warn("获取端口映射查询出错，portName 使用默认值",
			zap.String("device_ip", statusData.DeviceIP),
			zap.String("port_name", ifNameStr),
			zap.Error(err))
		portID = ""
		portName = ifNameStr
	} else if portMapping == nil {
		p.logger.Debug("端口映射记录不存在，portName 使用默认值",
			zap.String("device_ip", statusData.DeviceIP),
			zap.String("port_name", ifNameStr))
		portID = ""
		portName = ifNameStr
	} else {
		deviceID = portMapping.DeviceID
		deviceName = portMapping.DeviceName
		portID = portMapping.PortID
		portName = portMapping.PortName
	}

	// 处理接口状态
	if p.config.EnableInterfaceStatus {
		// 准备共用标签
		labels := map[string]string{
			"device_id":   deviceID,
			"device_name": deviceName,
			"device_ip":   statusData.DeviceIP,
			"agent_id":    statusData.AgentID,
			"agent_ip":    statusData.AgentIP,
			"port_id":     portID,
			"port_name":   portName,
			"if_index":    ifIndexStr,
			"ifName":      ifNameStr,
		}

		timestamp := time.Unix(statusData.Timestamp, 0)

		// 处理操作状态 (ifOperStatus)
		if ifOperStatus, ok := p.extractInt(statusData.Fields, "ifOperStatus"); ok {
			// 记录并导出接口操作状态指标
			metricName := p.config.MetricPrefix + "status_interface"
			p.exporter.SetGaugeValue(metricName, float64(ifOperStatus), labels, timestamp)

			p.logger.Debug("处理接口操作状态指标",
				zap.String("device_name", deviceName),
				zap.String("port_name", portName),
				zap.Int("oper_status", ifOperStatus))
		}

		// 处理管理/物理状态 (ifAdminStatus)
		if ifAdminStatus, ok := p.extractInt(statusData.Fields, "ifAdminStatus"); ok {
			// 记录并导出接口管理状态指标
			metricName := p.config.MetricPrefix + "status_interface_admin"
			p.exporter.SetGaugeValue(metricName, float64(ifAdminStatus), labels, timestamp)

			p.logger.Debug("处理接口管理状态指标",
				zap.String("device_name", deviceName),
				zap.String("port_name", portName),
				zap.Int("admin_status", ifAdminStatus))
		}
	}

	// 更新计数器和指标
	p.countersMtx.Lock()
	p.counters.success++
	p.countersMtx.Unlock()
	p.exporter.IncrementCounter(p.config.MetricPrefix+"processor_messages_total", 1, nil)
	p.exporter.IncrementCounter(p.config.MetricPrefix+"processor_messages_success", 1, nil)

	return nil
}

// processInterfaceFlowData 处理接口流量数据
func (p *SNMPProcessor) processInterfaceFlowData(flowData FlowData, ifNameStr, ifIndexStr string, timestamp time.Time) error {
	// 获取设备系统名称
	sysName := flowData.Tags["sysName"]

	// 尝试通过IP查询设备ID
	deviceID, deviceName, err := p.mapper.GetDeviceIDByIP(flowData.DeviceIP, sysName, ifIndexStr)
	if err != nil {
		p.logger.Warn("通过设备IP查询设备ID失败",
			zap.String("device_ip", flowData.DeviceIP),
			zap.String("sysName", sysName),
			zap.Error(err))
	}

	// 从ifName中分离物理端口和VNI
	basePortName := ifNameStr
	vniID := extractVNIFromPortName(ifNameStr)
	if vniID != "" {
		lastIndex := strings.LastIndex(ifNameStr, ".")
		if lastIndex != -1 {
			basePortName = ifNameStr[:lastIndex]
		}
	}

	p.logger.Debug("开始处理接口流量FlowData数据",
		zap.String("device_ip", flowData.DeviceIP),
		zap.String("if_index", ifIndexStr),
		zap.String("if_name", ifNameStr),
		zap.String("base_port_name", basePortName),
		zap.String("vni_id", vniID))

	// 查询设备端口映射
	portMapping, err := p.mapper.GetPortMapping(flowData.DeviceIP, basePortName, vniID)
	if err != nil {
		p.logProcessingError(fmt.Sprintf("获取端口映射失败: %v", err))
		return err
	}

	// 创建临时端口映射或使用查询结果
	if portMapping == nil {
		p.countersMtx.Lock()
		p.counters.noMapping++
		p.countersMtx.Unlock()

		// 构造临时映射
		portMapping = &PortMapping{
			DeviceIP:   flowData.DeviceIP,
			DeviceName: sysName,
			IfIndex:    sql.NullString{String: ifIndexStr, Valid: true},
			PortName:   ifNameStr,
			PortID:     "",
			VNIID:      vniID,
		}

		// 使用查询到的设备ID或临时ID
		if deviceID != "" {
			portMapping.DeviceID = deviceID
			portMapping.DeviceName = deviceName
		} else {
			portMapping.DeviceID = flowData.DeviceIP
		}

		// 确保DeviceName有值
		if portMapping.DeviceName == "" {
			portMapping.DeviceName = "UnknownDevice"
		}
	} else {
		// 更新完整端口名
		portMapping.PortName = ifNameStr
	}

	// 准备指标标签
	labels := map[string]string{
		"device_id":     portMapping.DeviceID,
		"device_name":   portMapping.DeviceName,
		"device_ip":     portMapping.DeviceIP,
		"agent_id":      flowData.AgentID,
		"agent_ip":      flowData.AgentIP,
		"port_id":       portMapping.PortID,
		"logic_port_id": portMapping.LogicPortID,
		"port_name":     portMapping.PortName,
	}

	// 设置ifIndex标签
	if portMapping.IfIndex.Valid {
		labels["if_index"] = portMapping.IfIndex.String
	} else {
		labels["if_index"] = ifIndexStr
	}

	// 添加额外标签
	addExtraLabels(labels, flowData, portMapping)

	// 导出流量指标
	if p.config.EnableInOctets {
		if inOctets, ok := p.extractUint64(flowData.Fields, "ifHCInOctets"); ok {
			metricName := p.config.MetricPrefix + "flow_ifHCInOctets"
			p.exporter.SetGaugeValue(metricName, float64(inOctets), labels, timestamp)
		}
	}

	if p.config.EnableOutOctets {
		if outOctets, ok := p.extractUint64(flowData.Fields, "ifHCOutOctets"); ok {
			metricName := p.config.MetricPrefix + "flow_ifHCOutOctets"
			p.exporter.SetGaugeValue(metricName, float64(outOctets), labels, timestamp)
		}
	}

	// 更新计数器和指标
	p.countersMtx.Lock()
	p.counters.success++
	p.countersMtx.Unlock()

	p.exporter.IncrementCounter(p.config.MetricPrefix+"processor_messages_total", 1, nil)
	p.exporter.IncrementCounter(p.config.MetricPrefix+"processor_messages_success", 1, nil)

	return nil
}

// logProcessingError 记录处理错误
func (p *SNMPProcessor) logProcessingError(message string) {
	p.countersMtx.Lock()
	p.counters.processError++
	p.countersMtx.Unlock()
	p.logger.Error(message)

	// 更新处理错误指标
	p.exporter.IncrementCounter(p.config.MetricPrefix+"processor_messages_error", 1, nil)
}

// GetStats 获取处理统计信息
func (p *SNMPProcessor) GetStats() map[string]int64 {
	p.countersMtx.RLock()
	defer p.countersMtx.RUnlock()

	return map[string]int64{
		"total":         p.counters.total,
		"success":       p.counters.success,
		"parse_error":   p.counters.parseError,
		"process_error": p.counters.processError,
		"no_mapping":    p.counters.noMapping,
	}
}

// 辅助函数

// extractDeviceIP 提取有效的设备IP地址
func (p *SNMPProcessor) extractDeviceIP(flowData FlowData) string {
	// 优先使用tags中的device_ip字段
	if deviceIP, ok := flowData.Tags["device_ip"]; ok && isValidIP(deviceIP) {
		return deviceIP
	}

	// 返回空字符串表示未找到有效IP
	p.logger.Error("无法提取有效的设备IP地址")
	return ""
}

// extractAgentID 从标签中提取代理ID
// func extractAgentID(tags map[string]string) string {
// 	if agentID, ok := tags["agent_id"]; ok {
// 		return agentID
// 	} else if hostName, ok := tags["host"]; ok {
// 		return hostName
// 	}
// 	return "unknown-agent"
// }

// extractAgentIP 从标签中提取代理IP
// func extractAgentIP(tags map[string]string) string {
// 	if agentIP, ok := tags["agent_ip"]; ok {
// 		return agentIP
// 	}
// 	return ""
// }

// extractUint64 从map中提取uint64值
func (p *SNMPProcessor) extractUint64(data map[string]interface{}, key string) (uint64, bool) {
	val, exists := data[key]
	if !exists {
		p.logger.Debug("字段不存在", zap.String("key", key))
		return 0, false
	}

	p.logger.Debug("提取uint64值",
		zap.String("key", key),
		zap.Any("value", val),
		zap.String("type", fmt.Sprintf("%T", val)))

	switch v := val.(type) {
	case float64:
		return uint64(v), true
	case int:
		return uint64(v), true
	case int64:
		return uint64(v), true
	case uint64:
		return v, true
	case string:
		if value, err := strconv.ParseUint(v, 10, 64); err == nil {
			return value, true
		} else {
			p.logger.Debug("无法将字符串转换为uint64",
				zap.String("key", key),
				zap.String("value", v),
				zap.Error(err))
		}
	default:
		p.logger.Debug("不支持的类型",
			zap.String("key", key),
			zap.String("type", fmt.Sprintf("%T", val)))
	}

	return 0, false
}

// extractFloat64 从map中提取float64值
func (p *SNMPProcessor) extractFloat64(data map[string]interface{}, key string) (float64, bool) {
	val, exists := data[key]
	if !exists {
		p.logger.Debug("字段不存在", zap.String("key", key))
		return 0, false
	}

	p.logger.Debug("提取float64值",
		zap.String("key", key),
		zap.Any("value", val),
		zap.String("type", fmt.Sprintf("%T", val)))

	switch v := val.(type) {
	case float64:
		return v, true
	case int:
		return float64(v), true
	case int64:
		return float64(v), true
	case string:
		if f, err := strconv.ParseFloat(v, 64); err == nil {
			return f, true
		} else {
			p.logger.Debug("无法将字符串转换为float64",
				zap.String("key", key),
				zap.String("value", v),
				zap.Error(err))
		}
	default:
		p.logger.Debug("不支持的类型",
			zap.String("key", key),
			zap.String("type", fmt.Sprintf("%T", val)))
	}

	return 0, false
}

// extractInt 从map中提取int值
func (p *SNMPProcessor) extractInt(data map[string]interface{}, key string) (int, bool) {
	val, exists := data[key]
	if !exists {
		p.logger.Debug("字段不存在", zap.String("key", key))
		return 0, false
	}

	p.logger.Debug("提取int值",
		zap.String("key", key),
		zap.Any("value", val),
		zap.String("type", fmt.Sprintf("%T", val)))

	switch v := val.(type) {
	case int:
		return v, true
	case int64:
		return int(v), true
	case float64:
		return int(v), true
	case string:
		if i, err := strconv.Atoi(v); err == nil {
			return i, true
		} else {
			p.logger.Debug("无法将字符串转换为int",
				zap.String("key", key),
				zap.String("value", v),
				zap.Error(err))
		}
	default:
		p.logger.Debug("不支持的类型",
			zap.String("key", key),
			zap.String("type", fmt.Sprintf("%T", val)))
	}

	return 0, false
}

// addExtraLabels 添加额外的标签
func addExtraLabels(labels map[string]string, flowData FlowData, portMapping *PortMapping) {
	// 从fields中提取额外的标签
	if ifName, ok := flowData.Fields["ifName"].(string); ok {
		labels["ifName"] = ifName
	}

	// 添加接口速率信息
	if interfaceSpeed, ok := flowData.Fields["interface_speed"].(string); ok {
		labels["interface_speed"] = interfaceSpeed
	}

	// 添加接口高速率信息
	if ifHighSpeed, ok := flowData.Fields["ifHighSpeed"]; ok {
		if strVal, err := convertToString(ifHighSpeed); err == nil {
			labels["ifHighSpeed"] = strVal
		}
	}

	// 如果有VNI信息，添加到标签
	if portMapping.VNIID != "" {
		labels["vni_id"] = portMapping.VNIID
	}
}

// convertToString 将任意类型转换为字符串
func convertToString(value interface{}) (string, error) {
	switch v := value.(type) {
	case string:
		return v, nil
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64), nil
	case int:
		return strconv.Itoa(v), nil
	case int64:
		return strconv.FormatInt(v, 10), nil
	case uint64:
		return strconv.FormatUint(v, 10), nil
	case bool:
		return strconv.FormatBool(v), nil
	default:
		return "", fmt.Errorf("无法转换类型 %T 为字符串", value)
	}
}

// isValidIP 检查字符串是否为有效IP地址
func isValidIP(ip string) bool {
	// 简单验证，检查字符串是否包含点且不是localhost/本地地址
	return strings.Contains(ip, ".") &&
		!strings.Contains(ip, "localhost") &&
		ip != "127.0.0.1" &&
		!strings.Contains(ip, "0.0.0.0") &&
		!strings.Contains(ip, "dci-agent") &&
		!strings.Contains(strings.ToLower(ip), "macbook") &&
		!strings.Contains(strings.ToLower(ip), "local")
}

// extractVNIFromPortName 从端口名称中提取VNI信息
func extractVNIFromPortName(portName string) string {
	// 检查是否包含点符号，例如"10GE1/0/1.6005002"
	dot := -1
	for i := len(portName) - 1; i >= 0; i-- {
		if portName[i] == '.' {
			dot = i
			break
		}
	}

	if dot == -1 {
		return "" // 未找到点符号
	}

	// 提取点符号后的部分
	if dot < len(portName)-1 {
		vniPart := portName[dot+1:]
		// 检查是否为数字，并且大于 0（VNI编号规则）
		if vni, err := strconv.Atoi(vniPart); err == nil && vni > 0 {
			return vniPart
		}
	}

	return ""
}

// ProcessData 在SNMPProcessor上实现ProcessData方法，用于向后兼容
func (p *SNMPProcessor) ProcessData(data []byte) error {
	return p.ProcessSNMPData(data)
}

// ProcessData 在SNMPFlowProcessor上实现ProcessData方法
func (p *SNMPFlowProcessor) ProcessData(data []byte) error {
	return p.processor.ProcessSNMPData(data)
}

// ProcessSNMPData 在SNMPFlowProcessor上实现ProcessSNMPData方法
func (p *SNMPFlowProcessor) ProcessSNMPData(data []byte) error {
	return p.processor.ProcessSNMPData(data)
}

// GetStats 在SNMPFlowProcessor上实现GetStats方法
func (p *SNMPFlowProcessor) GetStats() map[string]int64 {
	return p.processor.GetStats()
}

// ProcessData 在SNMPStatusProcessor上实现ProcessData方法
func (p *SNMPStatusProcessor) ProcessData(data []byte) error {
	return p.processor.ProcessSNMPData(data)
}

// ProcessSNMPData 在SNMPStatusProcessor上实现ProcessSNMPData方法
func (p *SNMPStatusProcessor) ProcessSNMPData(data []byte) error {
	return p.processor.ProcessSNMPData(data)
}

// GetStats 在SNMPStatusProcessor上实现GetStats方法
func (p *SNMPStatusProcessor) GetStats() map[string]int64 {
	return p.processor.GetStats()
}
