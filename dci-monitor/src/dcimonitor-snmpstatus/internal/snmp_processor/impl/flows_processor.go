// Package impl 实现流量监控功能

package impl

import (
	"common/logger"

	sp "snmpstatus/internal/snmp_processor"

	"go.uber.org/zap"
)

// ProcessFlowsHandler 处理流量数据的处理器
type ProcessFlowsHandler struct {
	processor *sp.SNMPFlowProcessor
	logger    *zap.Logger
}

// NewProcessFlowsHandler 创建流量数据处理器
func NewProcessFlowsHandler(config *sp.ProcessorConfig, mapper sp.DeviceMapper, exporter sp.MetricsExporter, zapLogger *zap.Logger) *ProcessFlowsHandler {
	if zapLogger == nil {
		zapLogger = logger.GetLogger()
	}

	// 创建基础处理器
	processor := sp.NewSNMPFlowProcessor(config, mapper, exporter, zapLogger)

	return &ProcessFlowsHandler{
		processor: processor,
		logger:    zapLogger,
	}
}

// HandleMessage 处理Kafka消息
func (h *ProcessFlowsHandler) HandleMessage(msg []byte) error {
	h.logger.Debug("收到流量数据消息，准备处理")

	// 判断消息是否为有效JSON
	if !isJSON(msg) {
		h.logger.Error("收到非JSON格式消息，无法解析")
		return nil // 跳过非JSON消息
	}

	// 使用处理器处理数据
	err := h.processor.ProcessSNMPData(msg)
	if err != nil {
		h.logger.Error("处理流量数据失败", zap.Error(err))
		return err
	}

	return nil
}

// GetStats 获取处理统计信息
func (h *ProcessFlowsHandler) GetStats() map[string]int64 {
	return h.processor.GetStats()
}
