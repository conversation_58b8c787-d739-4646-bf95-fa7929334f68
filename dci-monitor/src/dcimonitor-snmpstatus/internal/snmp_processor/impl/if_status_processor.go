// Package impl 实现接口状态监控功能

package impl

import (
	"common/logger"

	sp "snmpstatus/internal/snmp_processor"

	"go.uber.org/zap"
)

// ProcessInterfaceStatusHandler 处理接口状态数据的处理器
type ProcessInterfaceStatusHandler struct {
	processor *sp.SNMPStatusProcessor // 使用同样的状态处理器基类
	logger    *zap.Logger
}

// NewProcessInterfaceStatusHandler 创建接口状态数据处理器
func NewProcessInterfaceStatusHandler(config *sp.ProcessorConfig, mapper sp.DeviceMapper, exporter sp.MetricsExporter, zapLogger *zap.Logger) *ProcessInterfaceStatusHandler {
	if zapLogger == nil {
		zapLogger = logger.GetLogger()
	}

	// 创建基础处理器
	processor := sp.NewSNMPStatusProcessor(config, mapper, exporter, zapLogger)

	return &ProcessInterfaceStatusHandler{
		processor: processor,
		logger:    zapLogger,
	}
}

// HandleMessage 处理Kafka消息
func (h *ProcessInterfaceStatusHandler) HandleMessage(msg []byte) error {
	h.logger.Debug("收到接口状态数据消息，准备处理")

	// 判断消息是否为有效JSON
	if !isJSON(msg) {
		h.logger.Error("收到非JSON格式消息，无法解析")
		return nil // 跳过非JSON消息
	}

	// 使用处理器处理数据
	err := h.processor.ProcessSNMPData(msg)
	if err != nil {
		h.logger.Error("处理接口状态数据失败", zap.Error(err))
		return err
	}

	return nil
}

// GetStats 获取处理统计信息
func (h *ProcessInterfaceStatusHandler) GetStats() map[string]int64 {
	return h.processor.GetStats()
}
