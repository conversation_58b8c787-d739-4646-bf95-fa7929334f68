// Package impl 提供SNMP处理器的工厂方法

package impl

import (
	"common/logger"
	sp "snmpstatus/internal/snmp_processor"

	"go.uber.org/zap"
)

// ProcessorFactory 处理器工厂，用于创建各种类型的SNMP处理器
type ProcessorFactory struct {
	mapper       sp.DeviceMapper
	exporter     sp.MetricsExporter
	logger       *zap.Logger
	flowConfig   *sp.ProcessorConfig
	statusConfig *sp.ProcessorConfig
}

// NewProcessorFactory 创建处理器工厂
func NewProcessorFactory(mapper sp.DeviceMapper, exporter sp.MetricsExporter, zapLogger *zap.Logger) *ProcessorFactory {
	if zapLogger == nil {
		zapLogger = logger.GetLogger()
	}

	// 创建流量处理器的配置
	flowConfig := &sp.ProcessorConfig{
		MetricPrefix:    "dci_snmp_",
		EnableInOctets:  true,
		EnableOutOctets: true,
		EnableErrors:    true,
		EnableDiscards:  true,
		MetricLabels:    make(map[string]string),
	}

	// 创建状态处理器的配置
	statusConfig := &sp.ProcessorConfig{
		MetricPrefix:          "dci_snmp_",
		EnableDeviceCPU:       true,
		EnableDeviceMemory:    true,
		EnableInterfaceStatus: true,
		MetricLabels:          make(map[string]string),
	}

	return &ProcessorFactory{
		mapper:       mapper,
		exporter:     exporter,
		logger:       zapLogger,
		flowConfig:   flowConfig,
		statusConfig: statusConfig,
	}
}

// CreateFlowsHandler 创建流量处理器
func (f *ProcessorFactory) CreateFlowsHandler() *ProcessFlowsHandler {
	return NewProcessFlowsHandler(f.flowConfig, f.mapper, f.exporter, f.logger)
}

// CreateStatusHandler 创建设备状态处理器
func (f *ProcessorFactory) CreateStatusHandler() *ProcessStatusHandler {
	return NewProcessStatusHandler(f.statusConfig, f.mapper, f.exporter, f.logger)
}

// CreateInterfaceStatusHandler 创建接口状态处理器
func (f *ProcessorFactory) CreateInterfaceStatusHandler() *ProcessInterfaceStatusHandler {
	return NewProcessInterfaceStatusHandler(f.statusConfig, f.mapper, f.exporter, f.logger)
}

// GetMessageHandler 根据类型获取适当的消息处理器
func (f *ProcessorFactory) GetMessageHandler(handlerType string) MessageHandler {
	switch handlerType {
	case "flow", "flows":
		return f.CreateFlowsHandler()
	case "status", "device_status":
		return f.CreateStatusHandler()
	case "interface_status", "if_status":
		return f.CreateInterfaceStatusHandler()
	default:
		// 默认返回流量处理器
		f.logger.Warn("未知的处理器类型，使用默认流量处理器", zap.String("type", handlerType))
		return f.CreateFlowsHandler()
	}
}
