// Package impl 提供SNMP处理器的具体实现

package impl

import (
	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
)

// 判断消息是否为有效的JSON
func isJSON(data []byte) bool {
	var js map[string]interface{}
	return jsoniter.Unmarshal(data, &js) == nil
}

// 创建通用的消息处理器接口
type MessageHandler interface {
	HandleMessage(msg []byte) error
	GetStats() map[string]int64
}

// LogMessageReceived 日志记录收到消息
func LogMessageReceived(logger *zap.Logger, topic string, partition int, offset int64) {
	logger.Debug("接收到消息",
		zap.String("topic", topic),
		zap.Int("partition", partition),
		zap.Int64("offset", offset))
}
