// Package impl 实现状态监控功能

package impl

import (
	"common/logger"

	sp "snmpstatus/internal/snmp_processor"

	// 保留用于类型导入
	"go.uber.org/zap"
)

// ProcessStatusHandler 处理状态数据的处理器
type ProcessStatusHandler struct {
	processor *sp.SNMPStatusProcessor
	logger    *zap.Logger
}

// NewProcessStatusHandler 创建状态数据处理器
func NewProcessStatusHandler(config *sp.ProcessorConfig, mapper sp.DeviceMapper, exporter sp.MetricsExporter, zapLogger *zap.Logger) *ProcessStatusHandler {
	if zapLogger == nil {
		zapLogger = logger.GetLogger()
	}

	// 创建基础处理器
	processor := sp.NewSNMPStatusProcessor(config, mapper, exporter, zapLogger)

	return &ProcessStatusHandler{
		processor: processor,
		logger:    zapLogger,
	}
}

// HandleMessage 处理Kafka消息
func (h *ProcessStatusHandler) HandleMessage(msg []byte) error {
	h.logger.Debug("收到状态数据消息，准备处理")

	// 判断消息是否为有效JSON
	if !isJSON(msg) {
		h.logger.Error("收到非JSON格式消息，无法解析")
		return nil // 跳过非JSON消息
	}

	// 使用处理器处理数据
	err := h.processor.ProcessSNMPData(msg)
	if err != nil {
		h.logger.Error("处理状态数据失败", zap.Error(err))
		return err
	}

	return nil
}

// isJSON 函数已移至 common.go

// GetStats 获取处理统计信息
func (h *ProcessStatusHandler) GetStats() map[string]int64 {
	return h.processor.GetStats()
}
