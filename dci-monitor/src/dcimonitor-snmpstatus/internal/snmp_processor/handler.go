package snmp_processor

import (
	"encoding/json"
	"time"

	"common/logger"

	"github.com/IBM/sarama"
	"go.uber.org/zap"
)

// DataProcessor 定义数据处理接口
type DataProcessor interface {
	ProcessData([]byte) error
	GetStats() map[string]int64
}

// FlowDataHandler 流量数据处理器
type FlowDataHandler struct {
	processor DataProcessor
	logger    *zap.Logger
	// 统计信息
	stats struct {
		TotalMessages    int64
		SuccessMessages  int64
		FailedMessages   int64
		ProcessingTimeNs int64
	}
}

// NewFlowDataHandler 创建流量数据处理器
func NewFlowDataHandler(processor DataProcessor, zapLogger *zap.Logger) *FlowDataHandler {
	if zapLogger == nil {
		zapLogger = logger.GetLogger() // 使用common logger中的GetLogger方法获取zap.Logger
	}

	return &FlowDataHandler{
		processor: processor,
		logger:    zapLogger,
	}
}

// HandleMessage 处理消息，实现Kafka消息处理接口
func (h *FlowDataHandler) HandleMessage(msg *sarama.ConsumerMessage) error {
	h.stats.TotalMessages++

	// 打印消息原文（修改为Info级别以便检查问题）
	h.logger.Debug("收到流量数据消息原文",
		zap.String("topic", msg.Topic),
		zap.Int32("partition", msg.Partition),
		zap.Int64("offset", msg.Offset),
		zap.String("value", string(msg.Value)))

	// 尝试解析设备和代理信息（仅用于日志记录）
	var logData map[string]interface{}
	if err := json.Unmarshal(msg.Value, &logData); err == nil {
		if tags, ok := logData["tags"].(map[string]interface{}); ok {
			deviceIP, _ := tags["device_ip"].(string)
			sysName, _ := tags["sysName"].(string)
			agentID, _ := tags["agent_id"].(string)

			// 记录关键信息
			h.logger.Debug("消息标签信息",
				zap.String("device_ip", deviceIP),
				zap.String("sysName", sysName),
				zap.String("agent_id", agentID))

			// 验证设备IP是否可能是无效值
			if deviceIP == "dci-agent" || deviceIP == "localhost" || deviceIP == "127.0.0.1" {
				if sysName != "" {
					h.logger.Info("检测到无效的device_ip，将优先使用sysName获取设备信息",
						zap.String("device_ip", deviceIP),
						zap.String("sysName", sysName))
				} else {
					h.logger.Warn("检测到无效的device_ip，且缺少sysName，可能导致设备映射失败",
						zap.String("device_ip", deviceIP))
				}
			}
		}
	}

	h.logger.Debug("处理流量数据消息",
		zap.String("topic", msg.Topic),
		zap.Int32("partition", msg.Partition),
		zap.Int64("offset", msg.Offset),
		zap.Int("value_size", len(msg.Value)))

	// 交由处理器处理消息内容
	startTime := time.Now()
	err := h.processor.ProcessData(msg.Value)
	elapsedNs := time.Since(startTime).Nanoseconds()
	h.stats.ProcessingTimeNs += elapsedNs

	if err != nil {
		h.stats.FailedMessages++
		h.logger.Error("处理流量数据失败",
			zap.String("topic", msg.Topic),
			zap.Int32("partition", msg.Partition),
			zap.Int64("offset", msg.Offset),
			zap.Error(err))
		return err
	}

	h.stats.SuccessMessages++
	h.logger.Debug("成功处理流量数据",
		zap.String("topic", msg.Topic),
		zap.Int32("partition", msg.Partition),
		zap.Int64("offset", msg.Offset),
		zap.Int64("elapsed_ns", elapsedNs))

	return nil
}

// GetStats 获取处理统计信息
func (h *FlowDataHandler) GetStats() map[string]int64 {
	stats := map[string]int64{
		"total_messages":     h.stats.TotalMessages,
		"success_messages":   h.stats.SuccessMessages,
		"failed_messages":    h.stats.FailedMessages,
		"processing_time_ns": h.stats.ProcessingTimeNs,
	}

	// 如果处理器可用，添加处理器的统计信息
	if h.processor != nil {
		processorStats := h.processor.GetStats()
		for k, v := range processorStats {
			stats["processor_"+k] = v
		}
	}

	return stats
}
