package snmp_processor

import (
	"database/sql"
	"fmt"
	"sync"
	"time"

	"common/logger"

	_ "github.com/go-sql-driver/mysql"
	"go.uber.org/zap"
)

// PortMapping 定义设备端口映射信息
// 在models.go中已定义

// DevicePortMapper 设备端口映射器
type DevicePortMapper struct {
	db              *sql.DB
	logger          *zap.Logger
	cache           map[string]*PortMapping
	cacheMtx        sync.RWMutex
	cacheTTL        time.Duration
	cacheExpiration map[string]time.Time
	stopCh          chan struct{} // 用于停止后台清理任务的通道
	stats           struct {
		CacheHits      int64
		CacheMisses    int64
		DBQueries      int64
		DBErrors       int64
		LastUpdateTime time.Time
	}
}

// NewDevicePortMapper 创建设备端口映射器
func NewDevicePortMapper(dbConfig DatabaseConfig, mapperConfig MapperConfig, zapLogger *zap.Logger) (*DevicePortMapper, error) {
	if zapLogger == nil {
		zapLogger = logger.GetLogger()
	}

	// 获取数据库连接字符串
	dsn := dbConfig.GetDSN()
	zapLogger.Debug("数据库连接配置", zap.String("dsn", dsn))

	// 连接数据库
	db, err := sql.Open(dbConfig.Driver, dsn)
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(dbConfig.MaxOpenConns)
	db.SetMaxIdleConns(dbConfig.MaxIdleConns)
	db.SetConnMaxLifetime(time.Duration(dbConfig.ConnMaxLifetime) * time.Second)

	// 测试连接
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("测试数据库连接失败: %w", err)
	}

	// 从配置中获取缓存TTL
	cacheTTL := time.Duration(mapperConfig.CacheTTLMinutes) * time.Minute
	zapLogger.Info("端口映射缓存TTL已配置", zap.Duration("ttl", cacheTTL))

	mapper := &DevicePortMapper{
		db:              db,
		logger:          zapLogger,
		cache:           make(map[string]*PortMapping),
		cacheExpiration: make(map[string]time.Time),
		cacheTTL:        cacheTTL,
		stopCh:          make(chan struct{}),
	}

	// 启动后台缓存清理器
	go mapper.startCacheCleaner()

	return mapper, nil
}

// GetPortMapping 获取设备端口映射
func (m *DevicePortMapper) GetPortMapping(deviceIP, basePortName, vniID string) (*PortMapping, error) {
	// 生成缓存键
	cacheKey := fmt.Sprintf("%s:%s:%s", deviceIP, basePortName, vniID)

	// 先查询缓存
	m.cacheMtx.RLock()
	mapping, exists := m.cache[cacheKey]
	expired := false
	if exists {
		expiration, has := m.cacheExpiration[cacheKey]
		if !has || time.Now().After(expiration) {
			expired = true
		}
	}
	m.cacheMtx.RUnlock()

	// 如果缓存命中且未过期，直接返回
	if exists && !expired {
		m.stats.CacheHits++
		m.logger.Debug("端口映射缓存命中", zap.String("key", cacheKey))
		return mapping, nil
	}

	// 缓存未命中或已过期，查询数据库
	m.logger.Debug("缓存未命中，查询数据库", zap.String("key", cacheKey))
	m.stats.CacheMisses++
	m.stats.DBQueries++

	// 统一查询，通过LEFT JOIN关联业务信息
	// 如果vniID为空，则nb.vni != NULL的记录不会被匹配，从而logic_port_id和vni都为NULL
	// 如果vniID不为空，则仅匹配该VNI的业务记录
	query := `
		SELECT 
			d.id as device_id, 
			d.device_name,
			d.device_management_ip as device_ip,
			p.id as port_id, 
			p.port as port_name,
			nb.logic_port_id,
			nb.vni
		FROM 
			dci_device d
		JOIN 
			dci_logic_port_device p ON d.id = p.device_id
		LEFT JOIN 
			dci_node n ON d.id = n.device_id
		LEFT JOIN 
			dci_node_business nb ON n.id = nb.node_id AND nb.vni = ?
		WHERE 
			d.device_management_ip = ? AND p.port = ?
		LIMIT 1
	`

	m.logger.Debug("正在执行端口映射查询",
		zap.String("query", query),
		zap.String("device_ip", deviceIP),
		zap.String("base_port_name", basePortName),
		zap.String("vni_id", vniID))

	var result PortMapping
	var logicPortID, vni sql.NullString
	err := m.db.QueryRow(query, vniID, deviceIP, basePortName).Scan(
		&result.DeviceID,
		&result.DeviceName,
		&result.DeviceIP,
		&result.PortID,
		&result.PortName,
		&logicPortID,
		&vni,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			// 未找到映射，返回nil但不返回错误
			m.logger.Debug("数据库中未找到设备端口映射",
				zap.String("device_ip", deviceIP),
				zap.String("base_port_name", basePortName),
				zap.String("vni_id", vniID))
			return nil, nil
		}
		m.stats.DBErrors++
		m.logger.Error("查询设备端口映射失败",
			zap.String("device_ip", deviceIP),
			zap.String("base_port_name", basePortName),
			zap.String("vni_id", vniID),
			zap.Error(err))
		return nil, err
	} else {
		m.logger.Debug("数据库中存在设备端口映射",
			zap.String("device_ip", deviceIP),
			zap.String("device_name", result.DeviceName),
			zap.String("device_id", result.DeviceID),
			zap.String("port_id", result.PortID),
			zap.String("port_name", result.PortName),
			zap.String("base_port_name", basePortName),
			zap.String("vni_id", vniID),
			zap.String("logic_port_id", logicPortID.String),
			zap.String("vni", vni.String))
	}

	if logicPortID.Valid {
		result.LogicPortID = logicPortID.String
	}
	if vni.Valid {
		result.VNIID = vni.String
	}

	// 我们不再从数据库获取if_index
	// 默认设置IfIndex为无效值
	result.IfIndex = sql.NullString{String: "", Valid: false}

	// 更新缓存
	m.cacheMtx.Lock()
	m.cache[cacheKey] = &result
	m.cacheExpiration[cacheKey] = time.Now().Add(m.cacheTTL)
	m.stats.LastUpdateTime = time.Now()
	m.cacheMtx.Unlock()

	return &result, nil
}

// GetStats 获取统计信息
func (m *DevicePortMapper) GetStats() map[string]int64 {
	m.cacheMtx.RLock()
	defer m.cacheMtx.RUnlock()

	return map[string]int64{
		"cache_hits":       m.stats.CacheHits,
		"cache_misses":     m.stats.CacheMisses,
		"db_queries":       m.stats.DBQueries,
		"db_errors":        m.stats.DBErrors,
		"cache_size":       int64(len(m.cache)),
		"last_update_time": m.stats.LastUpdateTime.Unix(),
	}
}

// Close 关闭数据库连接和后台任务
func (m *DevicePortMapper) Close() error {
	if m.stopCh != nil {
		close(m.stopCh)
	}
	if m.db != nil {
		return m.db.Close()
	}
	return nil
}

// ClearCache 清除缓存
func (m *DevicePortMapper) ClearCache() {
	m.cacheMtx.Lock()
	defer m.cacheMtx.Unlock()

	m.cache = make(map[string]*PortMapping)
	m.cacheExpiration = make(map[string]time.Time)
	m.logger.Info("已清除端口映射缓存")
}

// startCacheCleaner 启动一个后台goroutine，定期清理过期的缓存
func (m *DevicePortMapper) startCacheCleaner() {
	// 清理间隔可以基于TTL或者是一个固定的值，这里使用1分钟
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	m.logger.Info("启动端口映射缓存清理器")

	for {
		select {
		case <-ticker.C:
			m.CleanExpiredCache()
		case <-m.stopCh:
			m.logger.Info("端口映射缓存清理器已停止")
			return
		}
	}
}

// CleanExpiredCache 清除过期的缓存项
func (m *DevicePortMapper) CleanExpiredCache() int {
	m.cacheMtx.Lock()
	defer m.cacheMtx.Unlock()

	now := time.Now()
	expiredKeys := []string{}

	// 找出所有过期的缓存项
	for key, expiration := range m.cacheExpiration {
		if now.After(expiration) {
			expiredKeys = append(expiredKeys, key)
		}
	}

	// 删除过期的缓存项
	for _, key := range expiredKeys {
		delete(m.cache, key)
		delete(m.cacheExpiration, key)
	}

	m.logger.Debug("清理过期缓存", zap.Int("cleaned", len(expiredKeys)))
	return len(expiredKeys)
}

// GetDeviceIDByIP 通过设备IP查询设备ID
func (m *DevicePortMapper) GetDeviceIDByIP(deviceIP, sysName, ifIndex string) (string, string, error) {
	// 生成缓存键
	cacheKey := "device:" + deviceIP

	// 先查询缓存
	m.cacheMtx.RLock()
	mapping, exists := m.cache[cacheKey]
	expired := false
	if exists {
		expiration, has := m.cacheExpiration[cacheKey]
		if !has || time.Now().After(expiration) {
			expired = true
		}
	}
	m.cacheMtx.RUnlock()

	// 如果缓存命中且未过期，直接返回
	if exists && !expired {
		m.stats.CacheHits++
		return mapping.DeviceID, mapping.DeviceName, nil
	}

	// 缓存未命中或已过期，查询数据库
	m.stats.CacheMisses++
	m.stats.DBQueries++

	// 首先通过设备IP查询
	query := `
		SELECT 
			id, 
			device_name
		FROM 
			dci_device
		WHERE 
			device_management_ip = ?
		LIMIT 1
	`

	var deviceID, deviceName string
	err := m.db.QueryRow(query, deviceIP).Scan(&deviceID, &deviceName)

	// 如果通过提供的IP未找到设备，尝试使用SNMP查询其他可能的IP
	if err == sql.ErrNoRows {
		// 先使用sysName查询
		if sysName != "" {
			m.logger.Info("通过IP未找到设备，尝试使用系统名称查询",
				zap.String("device_ip", deviceIP),
				zap.String("sysName", sysName))

			nameQuery := `
				SELECT 
					id, 
					device_name,
					device_management_ip
				FROM 
					dci_device
				WHERE 
					device_name = ?
				LIMIT 1
			`

			var deviceManagementIP string
			err = m.db.QueryRow(nameQuery, sysName).Scan(&deviceID, &deviceName, &deviceManagementIP)
			if err == nil {
				m.logger.Info("通过系统名称查询到设备",
					zap.String("device_id", deviceID),
					zap.String("device_name", deviceName),
					zap.String("device_ip", deviceManagementIP))

				// 使用实际的管理IP更新deviceIP
				deviceIP = deviceManagementIP
			}
		}

		// 如果通过sysName未找到，尝试通过ifIndex查询
		if err == sql.ErrNoRows && ifIndex != "" {
			m.logger.Info("通过IP和系统名称未找到设备，尝试使用ifIndex查询",
				zap.String("device_ip", deviceIP),
				zap.String("if_index", ifIndex))

			// 先尝试通过IP地址范围查询
			ipRangeQuery := `
				SELECT 
					d.id, 
					d.device_name,
					d.device_management_ip
				FROM 
					dci_device d
				WHERE 
					d.device_management_ip LIKE CONCAT(SUBSTRING_INDEX(?, '.', 3), '.%')
				LIMIT 1
			`

			var deviceManagementIP string
			err = m.db.QueryRow(ipRangeQuery, deviceIP).Scan(&deviceID, &deviceName, &deviceManagementIP)

			// 如果通过IP范围未找到，尝试通过接口索引查询
			if err == sql.ErrNoRows {
				ifIndexQuery := `
					SELECT 
						d.id, 
						d.device_name,
						d.device_management_ip
					FROM 
						dci_device d
					JOIN 
						dci_logic_port_device p ON d.id = p.device_id
					WHERE 
						p.if_index = ?
					LIMIT 1
				`

				err = m.db.QueryRow(ifIndexQuery, ifIndex).Scan(&deviceID, &deviceName, &deviceManagementIP)
				if err == nil {
					m.logger.Info("通过ifIndex查询到设备",
						zap.String("device_id", deviceID),
						zap.String("device_name", deviceName),
						zap.String("device_ip", deviceManagementIP))

					// 使用实际的管理IP更新deviceIP
					deviceIP = deviceManagementIP
				}
			} else if err == nil {
				m.logger.Info("通过IP范围查询到设备",
					zap.String("device_id", deviceID),
					zap.String("device_name", deviceName),
					zap.String("device_ip", deviceManagementIP))

				// 使用实际的管理IP更新deviceIP
				deviceIP = deviceManagementIP
			}
		}
	}

	if err != nil {
		if err == sql.ErrNoRows {
			// 未找到设备
			m.logger.Warn("未找到设备",
				zap.String("device_ip", deviceIP),
				zap.String("sysName", sysName),
				zap.String("if_index", ifIndex))
			return "", "", nil
		}
		m.stats.DBErrors++
		m.logger.Error("查询设备ID失败",
			zap.String("device_ip", deviceIP),
			zap.Error(err))
		return "", "", err
	}

	// 创建一个简化的PortMapping对象，只包含设备信息
	result := &PortMapping{
		DeviceID:   deviceID,
		DeviceName: deviceName,
		DeviceIP:   deviceIP,
	}

	// 更新缓存
	m.cacheMtx.Lock()
	m.cache[cacheKey] = result
	m.cacheExpiration[cacheKey] = time.Now().Add(m.cacheTTL)
	m.stats.LastUpdateTime = time.Now()
	m.cacheMtx.Unlock()

	m.logger.Debug("查询设备成功",
		zap.String("device_ip", deviceIP),
		zap.String("device_id", deviceID),
		zap.String("device_name", deviceName))

	return deviceID, deviceName, nil
}
