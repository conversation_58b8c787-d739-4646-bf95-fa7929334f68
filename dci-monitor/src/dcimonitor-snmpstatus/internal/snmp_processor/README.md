# SNMP处理器 - 流量与设备状态监控

本模块负责处理从设备采集的SNMP数据，包括：

1. **接口流量数据**：接口的输入/输出字节计数等
2. **设备状态数据**：CPU使用率、内存使用率 
3. **接口状态数据**：接口的Up/Down状态

## 架构说明

整体数据流向：

```
                                      ┌─────────────────┐
                                      │                 │
                                      │    Prometheus   │◄──┐
                                      │                 │   │
                                      └─────────────────┘   │
                                                            │
┌────────────┐     ┌────────────┐     ┌─────────────────┐   │
│            │     │            │     │                 │   │
│   设备     ├────►│  Telegraf  ├────►│  Kafka Topics   ├───┤
│            │     │            │     │                 │   │
└────────────┘     └────────────┘     └─────────────────┘   │
                                                            │
                                      ┌─────────────────┐   │
                                      │                 │   │
                                      │ SNMP Processor ├───┘
                                      │                 │
                                      └─────────────────┘
```

## 不同数据类型与Kafka主题

为了更好地处理不同类型的SNMP数据，我们按数据类型分离Kafka主题：

1. **流量数据主题**: `dci.monitor.v1.defaultchannel.flow`
2. **设备状态主题**: `dci.monitor.v1.defaultchannel.status.device`  
3. **接口状态主题**: `dci.monitor.v1.defaultchannel.status.interface`

## 指标定义

### 流量指标

流量指标的命名格式为：`dci_snmp_flow_{指标名}`

- `dci_snmp_flow_ifHCInOctets`: 接口入向总字节数
- `dci_snmp_flow_ifHCOutOctets`: 接口出向总字节数

### 状态指标 

状态指标的命名格式为：`dci_snmp_status_{指标名}`

- `dci_snmp_status_cpu_usage`: CPU使用率
- `dci_snmp_status_memory_usage`: 内存使用率
- `dci_snmp_status_interface`: 接口操作状态 (1=up, 2=down)

## 配置说明

在`config.yaml`文件中进行以下配置：

```yaml
processor:
  # 指标前缀
  metric_prefix: "dci_snmp_"
  
  # 流量指标启用配置
  enable_in_octets: true
  enable_out_octets: true
  enable_errors: true
  enable_discards: true
  
  # 设备状态指标启用配置
  enable_device_cpu: true
  enable_device_memory: true
  
  # 接口状态指标启用配置
  enable_interface_status: true
```

## Telegraf配置

请参考[样例配置文件](./examples/telegraf_snmp_status.conf)，了解如何配置Telegraf采集不同类型的SNMP数据并发送到不同Kafka主题。

## 使用方法

创建处理器并处理数据：

```go
// 创建流量处理器
flowProcessor := snmp_processor.NewSNMPFlowProcessor(config.Processor, mapper, metricExporter, logger)

// 创建状态处理器
statusProcessor := snmp_processor.NewSNMPStatusProcessor(config.Processor, mapper, metricExporter, logger)

// 处理流量数据
err := flowProcessor.ProcessData(kafkaMessage.Value)

// 处理状态数据
err := statusProcessor.ProcessData(kafkaMessage.Value)
``` 