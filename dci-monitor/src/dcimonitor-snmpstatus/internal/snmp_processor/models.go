// Package snmp_processor 实现SNMP数据处理功能，包括流量数据和设备状态数据
package snmp_processor

import (
	"database/sql"
	"time"
)

// SNMPData 定义从Telegraf接收的SNMP数据通用格式
type SNMPData struct {
	Name      string                 `json:"name"`
	Tags      map[string]string      `json:"tags"`
	Fields    map[string]interface{} `json:"fields"`
	Timestamp int64                  `json:"timestamp"`
	DeviceIP  string                 // 从tags中提取的设备IP，经过验证的真实设备IP
	AgentID   string                 // 从tags中提取的代理ID
	AgentIP   string                 // 从tags中提取的代理IP
	RealIP    string                 // 通过SNMP查询获取的真实IP (ipAdEntAddr)
	DataType  string                 // 数据类型: "flow", "device_status", "interface_status"
}

// FlowData 定义从Telegraf接收的SNMP流量数据格式，继承SNMPData
// 示例:
//
//	{
//	  "device_ip": "***********",
//	  "timestamp": 1685501234,
//	  "name": "interface",
//	  "tags": {
//	    "agent_id": "dciagent-01",
//	    "agent_ip": "********",
//	    "device_ip": "***********",
//	    "device_type": "switch",
//	    "data_source_type": "snmp_flow",
//	    "sysName": "SWITCH-1"
//	  },
//	  "fields": {
//	    "if_index": "10",
//	    "ifHCInOctets": 1024567890,
//	    "ifHCOutOctets": 2048123456,
//	    "ifInDiscards": 123,
//	    "ifOutDiscards": 456,
//	    "ifInErrors": 12,
//	    "ifOutErrors": 34
//	  }
//	}
type FlowData struct {
	SNMPData
}

// DeviceStatusData 定义从Telegraf接收的设备CPU和内存数据格式
// 示例:
//
//	{
//	  "device_ip": "***********",
//	  "timestamp": 1685501234,
//	  "name": "device_status",
//	  "tags": {
//	    "agent_id": "dciagent-01",
//	    "agent_ip": "********",
//	    "device_ip": "***********",
//	    "device_type": "switch",
//	    "data_source_type": "snmp_status",
//	    "sysName": "SWITCH-1",
//	    "entity_index": "1",
//	    "entity_name": "CPU",
//	    "entity_type": "cpu"
//	  },
//	  "fields": {
//	    "hwEntityCpuUsage": 45.5,
//	    "hwEntityMemUsage": 65.2
//	  }
//	}
type DeviceStatusData struct {
	SNMPData
	EntityIndex string // 设备实体索引
	EntityName  string // 设备实体名称
	EntityType  string // 设备实体类型 (cpu, memory)
}

// InterfaceStatusData 定义从Telegraf接收的接口状态数据格式
// 示例:
//
//	{
//	  "device_ip": "***********",
//	  "timestamp": 1685501234,
//	  "name": "interface_status",
//	  "tags": {
//	    "agent_id": "dciagent-01",
//	    "agent_ip": "********",
//	    "device_ip": "***********",
//	    "device_type": "switch",
//	    "data_source_type": "snmp_status",
//	    "sysName": "SWITCH-1",
//	    "ifIndex": "10"
//	  },
//	  "fields": {
//	    "ifName": "GigabitEthernet1/0/1",
//	    "ifOperStatus": 1,
//	    "ifAdminStatus": 1
//	  }
//	}
type InterfaceStatusData struct {
	SNMPData
}

// PortMapping 设备端口映射信息
type PortMapping struct {
	DeviceID    string // 设备ID
	DeviceName  string // 设备名称
	DeviceIP    string // 设备IP地址
	PortID      string // 端口ID
	PortName    string // 端口名称
	IfIndex     sql.NullString
	VNIID       string // VNI ID (可选)
	LogicPortID string // 逻辑端口ID (可选)
	RealIP      string // 真实设备IP (ipAdEntAddr)
}

// FlowMetric 流量指标结构体
type FlowMetric struct {
	DeviceID   string `json:"device_id"`
	DeviceName string `json:"device_name"`
	DeviceIP   string `json:"device_ip"`
	AgentID    string `json:"agent_id"` // 添加代理ID字段
	AgentIP    string `json:"agent_ip"` // 添加代理IP字段
	PortID     string `json:"port_id"`
	PortName   string `json:"port_name"`
	IfIndex    string `json:"if_index"`
	VNIID      string `json:"vni_id,omitempty"`
	InOctets   uint64 `json:"in_octets,omitempty"`  // 原始入向字节计数器
	OutOctets  uint64 `json:"out_octets,omitempty"` // 原始出向字节计数器
	Timestamp  int64  `json:"timestamp"`
}

// StatusMetric 设备状态指标结构体
type StatusMetric struct {
	DeviceID    string  `json:"device_id"`
	DeviceName  string  `json:"device_name"`
	DeviceIP    string  `json:"device_ip"`
	AgentID     string  `json:"agent_id"`
	AgentIP     string  `json:"agent_ip"`
	EntityIndex string  `json:"entity_index,omitempty"` // 设备实体索引
	EntityName  string  `json:"entity_name,omitempty"`  // 设备实体名称
	EntityType  string  `json:"entity_type,omitempty"`  // 设备实体类型
	PortID      string  `json:"port_id,omitempty"`      // 接口状态时使用
	PortName    string  `json:"port_name,omitempty"`    // 接口状态时使用
	IfIndex     string  `json:"if_index,omitempty"`     // 接口状态时使用
	CPUUsage    float64 `json:"cpu_usage,omitempty"`    // CPU使用率
	MemoryUsage float64 `json:"memory_usage,omitempty"` // 内存使用率
	IfStatus    int     `json:"if_status,omitempty"`    // 接口状态
	Timestamp   int64   `json:"timestamp"`
}

// MetricValue 定义指标值
type MetricValue struct {
	Name   string            // 指标名称
	Value  float64           // 指标值
	Tags   map[string]string // 指标标签
	Labels map[string]string // Prometheus标签
}

// MetricFamily 定义指标族
type MetricFamily struct {
	Name    string         // 指标族名称
	Help    string         // 指标帮助信息
	Type    string         // 指标类型: counter, gauge, histogram, summary
	Metrics []*MetricValue // 指标值列表
}

// ProcessorStats 定义处理器统计信息
type ProcessorStats struct {
	MessagesReceived  uint64    // 收到的消息数
	MessagesProcessed uint64    // 处理成功的消息数
	ProcessingErrors  uint64    // 处理失败的消息数
	LastProcessedAt   time.Time // 最后处理时间
}
