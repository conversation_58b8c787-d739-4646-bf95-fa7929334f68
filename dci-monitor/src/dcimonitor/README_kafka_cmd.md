# DCI监控系统 - Kafka命令使用说明

## 概述

DCI监控系统的Kafka命令是一组用于管理、测试和诊断Kafka集群连接的工具集。这些命令提供了丰富的功能，包括初始化主题、测试连接、生产和消费消息、元数据查询以及网络诊断等功能，帮助用户快速排查Kafka相关问题。

## 主要功能

- **主题管理**：初始化、创建和管理Kafka主题
- **连接测试**：验证Kafka集群连接状态和网络可达性
- **消息收发**：作为生产者发送测试消息或作为消费者接收消息
- **元数据查询**：查看集群元数据，包括Broker信息、主题分区等
- **网络诊断**：执行DNS解析和网络连通性检查
- **元数据监控**：持续监控元数据变化，检测集群状态变更
- **一致性验证**：验证多Broker元数据一致性，发现潜在配置问题

## 命令结构

DCI监控系统的Kafka命令主要包含以下子命令：

```
dcimonitor kafka              # Kafka基础管理命令
  |- init-topics              # 初始化Kafka主题
  
dcimonitor kafka-client       # Kafka客户端工具
  |- --producer               # 生产者模式
  |- --consumer               # 消费者模式
  |- --metadata               # 元数据查询模式
  |- --dnscheck               # DNS解析检查
  |- --netcheck               # 网络连通性检查
  |- --metadatamonitor        # 元数据监控模式
  |- --validatemetadata       # 元数据一致性验证
```

## 使用方法

### 初始化Kafka主题

用于创建DCI监控系统所需的Kafka主题。

```bash
# 初始化所有主题
./dcimonitor kafka init-topics --all

# 初始化单个主题
./dcimonitor kafka init-topics --topic=dci.monitor.v1.defaultchannel.topology.lldp

# 演示模式（不实际执行操作）
./dcimonitor kafka init-topics --all --dry-run

# 指定Kafka集群地址
./dcimonitor kafka init-topics --all --brokers=host1:9092,host2:9092
```

### Kafka客户端工具

#### 元数据查询

查询Kafka集群元数据，包括Broker信息、主题和分区状态等。

```bash
# 查询元数据并显示详细信息
./dcimonitor kafka-client --bootstrap=************:30002 --metadata --verbose

# 设置元数据刷新间隔
./dcimonitor kafka-client --bootstrap=************:30002 --metadata --refresh=10
```

#### 消息生产与消费

测试Kafka的消息收发功能。

```bash
# 发送测试消息
./dcimonitor kafka-client --bootstrap=************:30002 --producer --topic=test --message="测试消息"

# 消费消息
./dcimonitor kafka-client --bootstrap=************:30002 --consumer --topic=test
```

#### 网络诊断

检查网络连接和DNS解析问题，帮助排查Kafka连接问题。

```bash
# 同时执行DNS解析和网络连通性检查
./dcimonitor kafka-client --bootstrap=************:30002 --dnscheck --netcheck

# 仅执行DNS解析检查
./dcimonitor kafka-client --bootstrap=************:30002 --dnscheck
```

#### 元数据监控

持续监控Kafka元数据变化，检测Broker上下线、主题变更等事件。

```bash
# 监控元数据变化，每10秒检查一次
./dcimonitor kafka-client --bootstrap=************:30002 --metadatamonitor --interval=10
```

#### 元数据一致性验证

验证Kafka集群中多个Broker的元数据一致性，发现潜在配置问题。

```bash
# 验证元数据一致性
./dcimonitor kafka-client --bootstrap=************:30002 --validatemetadata
```

## 常见问题排查

### Kafka连接失败

- 检查Broker地址是否正确
- 验证网络连通性（使用`--netcheck`选项）
- 检查DNS解析是否正常（使用`--dnscheck`选项）
- 确认防火墙规则是否允许连接

### 元数据不一致

- 使用`--validatemetadata`选项检查元数据一致性
- 检查advertised.listeners配置是否正确
- 确认所有Broker配置一致

### 消费者无法接收消息

- 确认主题存在且有数据（使用`--metadata`选项查看）
- 检查消费者组配置
- 验证主题分区是否有Leader（使用`--metadata`选项）

### 生产者发送消息失败

- 检查主题是否存在
- 确认Broker可连接（使用`--netcheck`选项）
- 验证权限配置是否正确

## 高级用法

### 组合命令

可以组合多个选项执行更复杂的操作：

```bash
# 先检查网络连通性，再查询元数据
./dcimonitor kafka-client --bootstrap=************:30002 --netcheck --metadata

# 监控元数据变化并验证一致性
./dcimonitor kafka-client --bootstrap=************:30002 --metadatamonitor --validatemetadata --interval=30
```

### 调试模式

使用`--verbose`选项获取更详细的日志输出：

```bash
./dcimonitor kafka-client --bootstrap=************:30002 --consumer --topic=test --verbose
```

## 注意事项

1. 部分命令需要Kafka集群管理员权限
2. 元数据监控可能会增加Kafka集群负载，生产环境谨慎使用
3. 使用`--dry-run`选项可以预览命令执行效果而不实际执行
4. 对于大型集群，元数据验证可能需要较长时间

## 配置文件

Kafka命令可以通过配置文件设置默认参数，配置文件路径为`./config/kafka.yaml`。也可以通过`--kafka-config`选项指定自定义配置文件：

```bash
./dcimonitor kafka init-topics --all --kafka-config=/path/to/custom-kafka.yaml
```

## 相关资源

- [Kafka官方文档](https://kafka.apache.org/documentation/)
- [DCI监控系统架构设计](../../documents/designs/01-dci_系统架构设计.md)
- [Kafka故障排查手册](../../documents/knowledge/kafka_troubleshooting.md)
