#!/bin/bash

# 设置 -e 以在任何命令失败时退出
set -e

# 定义常量
NAMESPACE="dci"
PVC_NAME="dcimonitor-binary-pvc"
DEPLOYMENT_NAME="dcimonitor"
TEMP_POD_NAME="binary-uploader"
BACKUP_DIR="/lib/dci/backups"
ORIGINAL_BACKUP="$BACKUP_DIR/original.bak"
TEMP_DIR="/tmp/dcimonitor-pv"

# 获取当前部署的版本
get_current_version() {
  kubectl get deployment $DEPLOYMENT_NAME -n $NAMESPACE -o jsonpath='{.spec.template.metadata.annotations.dcimonitor\.version}' 2>/dev/null || echo "unknown"
}

# 定义函数：显示帮助信息
show_help() {
  echo "用法: $0 [选项]"
  echo "选项:"
  echo "  -u, --update     更新PVC中的二进制文件（必须与-v/--version一起使用）"
  echo "  -r, --restore    恢复原始备份的二进制文件"
  echo "  -c, --check      检查PVC中的二进制文件"
  echo "  -C, --clear      清空PVC中的二进制文件，强制使用镜像版本"
  echo "  -h, --help       显示此帮助信息"
  echo "  -i, --init       初始化PVC（首次部署时使用）"
  echo "  -t, --test       发送测试告警到dcimonitor webhook端点"
  echo "  -f, --file-info  显示二进制文件详细信息"
  echo "  详细使用见 README-binary-update.md"
  echo "重要提示: PVC中所运行的二进制必须放置于 /tmp/dcimonitor"
  echo "示例:"
  echo "  $0                            显示帮助信息（默认）"
  echo "  $0 --update --version 0.3.0   更新PVC中的二进制文件到0.3.0版本"
  echo "  $0 --version 0.3.0            更新PVC中的二进制文件到0.3.0版本（同上）"
  echo "  $0 --restore                  恢复到原始备份"
  echo "  $0 --check                    检查PVC中的二进制文件状态"
  echo "  $0 --clear                    清空PVC，强制回退到镜像版本"
  echo "  $0 --init                     初始化PVC（首次部署时使用）"
  echo "  $0 --test                     发送测试告警到dcimonitor webhook端点"
  echo "  $0 --file-info                显示二进制文件的详细信息"
}

# 检查环境
check_env() {
  # 检查是否在 Kubernetes 集群中
  if ! kubectl get pods -n$NAMESPACE > /dev/null 2>&1; then
    echo "错误：请确保在 Kubernetes 集群中运行"
    exit 1
  fi
  
  # 获取 Pod 名称
  POD_NAME=$(kubectl get pods -n $NAMESPACE -l app=$DEPLOYMENT_NAME -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
  if [ -z "$POD_NAME" ]; then
    echo "警告：未找到 $DEPLOYMENT_NAME Pod，可能是首次部署"
  fi
}

# 初始化 PVC
init_pvc() {
  check_env
  
  # 检查 PVC 是否存在
  if ! kubectl get pvc $PVC_NAME -n $NAMESPACE > /dev/null 2>&1; then
    echo "PVC $PVC_NAME 不存在，正在创建..."
    
    # 应用 PVC 定义
    if [ -f "dcimonitor-pvc.yaml" ]; then
      kubectl apply -f dcimonitor-pvc.yaml
    else
      echo "错误：找不到 dcimonitor-pvc.yaml 文件"
      echo "尝试在当前目录和上级目录中查找..."
      
      # 在当前目录的父目录中查找
      if [ -f "../dcimonitor-pvc.yaml" ]; then
        kubectl apply -f ../dcimonitor-pvc.yaml
      else
        echo "错误：无法找到 dcimonitor-pvc.yaml 文件"
        exit 1
      fi
    fi
    
    echo "等待 PVC 绑定..."
    kubectl wait --for=condition=Bound pvc/$PVC_NAME -n $NAMESPACE --timeout=60s || {
      echo "错误：PVC 未能在规定时间内绑定，请检查存储类是否可用"
      exit 1
    }
  else
    echo "PVC $PVC_NAME 已存在"
  fi
  
  # 获取 PVC 的路径信息
  PV_NAME=$(kubectl get pvc $PVC_NAME -n $NAMESPACE -o jsonpath='{.spec.volumeName}')
  echo "PVC 绑定到持久卷: $PV_NAME"
  
  # 对于 NFS 类型的 PV，可能需要手动创建目录
  PV_TYPE=$(kubectl get pv $PV_NAME -o jsonpath='{.spec.nfs}' 2>/dev/null)
  if [ ! -z "$PV_TYPE" ]; then
    NFS_SERVER=$(kubectl get pv $PV_NAME -o jsonpath='{.spec.nfs.server}')
    NFS_PATH=$(kubectl get pv $PV_NAME -o jsonpath='{.spec.nfs.path}')
    echo "NFS 信息: 服务器=$NFS_SERVER, 路径=$NFS_PATH"
    echo "注意: 如果 Pod 无法启动，可能需要在 NFS 服务器上手动创建目录: $NFS_PATH"
  fi
  
  echo "PVC 初始化完成"
}

# 创建临时 Pod 来访问 PVC
create_temp_pod() {
  echo "创建临时 Pod 来访问 PVC..."
  mkdir -p $TEMP_DIR
  
  cat > $TEMP_DIR/temp-pod.yaml << EOF
apiVersion: v1
kind: Pod
metadata:
  name: $TEMP_POD_NAME
  namespace: $NAMESPACE
spec:
  containers:
  - name: uploader
    image: busybox:latest
    command: ["sleep", "300"]
    volumeMounts:
    - name: binary-storage
      mountPath: /data
  volumes:
  - name: binary-storage
    persistentVolumeClaim:
      claimName: $PVC_NAME
EOF
  
  kubectl apply -f $TEMP_DIR/temp-pod.yaml
  
  echo "等待临时 Pod 运行..."
  kubectl wait --for=condition=Ready pod/$TEMP_POD_NAME -n $NAMESPACE --timeout=60s || {
    echo "错误: 临时 Pod 未能正常启动，请检查 PVC 状态"
    kubectl describe pod $TEMP_POD_NAME -n $NAMESPACE
    exit 1
  }
}

# 清理资源
cleanup() {
  if kubectl get pod $TEMP_POD_NAME -n $NAMESPACE > /dev/null 2>&1; then
    echo "删除临时 Pod..."
    kubectl delete pod $TEMP_POD_NAME -n $NAMESPACE --wait=false
  fi
  
  [ -d "$TEMP_DIR" ] && rm -rf $TEMP_DIR
}

# 显示二进制文件详细信息
show_file_info() {
  check_env
  
  if [ -z "$POD_NAME" ]; then
    echo "错误：未找到运行中的 $DEPLOYMENT_NAME Pod"
    exit 1
  fi
  
  echo "====== 正在运行的二进制文件信息 ======"
  kubectl exec -n $NAMESPACE $POD_NAME -- ls -la /lib/dci/dcimonitor
  
  # 检查构建信息
  if kubectl exec -n $NAMESPACE $POD_NAME -- test -f /lib/dci/build_info 2>/dev/null; then
    echo "构建信息:"
    kubectl exec -n $NAMESPACE $POD_NAME -- cat /lib/dci/build_info
  else
    echo "未找到构建信息文件"
  fi
  
  # 检查标记文件
  if kubectl exec -n $NAMESPACE $POD_NAME -- test -f /lib/dci/using_pvc_binary 2>/dev/null; then
    echo "使用PVC中的二进制文件，版本: $(kubectl exec -n $NAMESPACE $POD_NAME -- cat /lib/dci/using_pvc_binary)"
  else
    echo "使用镜像中的原始二进制文件"
  fi
  
  # 检查PVC中的二进制文件
  create_temp_pod
  
  echo "====== PVC中的二进制文件信息 ======"
  if kubectl exec -n $NAMESPACE $TEMP_POD_NAME -- test -f /data/dcimonitor 2>/dev/null; then
    kubectl exec -n $NAMESPACE $TEMP_POD_NAME -- ls -la /data/dcimonitor
    
    # 检查版本信息
    if kubectl exec -n $NAMESPACE $TEMP_POD_NAME -- test -f /data/version 2>/dev/null; then
      echo "版本: $(kubectl exec -n $NAMESPACE $TEMP_POD_NAME -- cat /data/version)"
    else
      echo "未找到版本信息"
    fi
    
    # 检查构建信息
    if kubectl exec -n $NAMESPACE $TEMP_POD_NAME -- test -f /data/build_info 2>/dev/null; then
      echo "构建信息:"
      kubectl exec -n $NAMESPACE $TEMP_POD_NAME -- cat /data/build_info
    else
      echo "未找到构建信息文件"
    fi
    
    # 比较运行中的文件和PVC中的文件
    echo "====== 文件对比 ======"
    RUNNING_SIZE=$(kubectl exec -n $NAMESPACE $POD_NAME -- ls -l /lib/dci/dcimonitor | awk '{print $5}')
    PVC_SIZE=$(kubectl exec -n $NAMESPACE $TEMP_POD_NAME -- ls -l /data/dcimonitor | awk '{print $5}')
    
    if [ "$RUNNING_SIZE" = "$PVC_SIZE" ]; then
      echo "文件大小匹配: $RUNNING_SIZE 字节"
    else
      echo "文件大小不匹配! 运行中: $RUNNING_SIZE 字节, PVC中: $PVC_SIZE 字节"
    fi
  else
    echo "PVC中不存在二进制文件"
  fi
}

# 检查二进制文件
check_binary() {
  check_env
  
  # PVC 必须已存在
  if ! kubectl get pvc $PVC_NAME -n $NAMESPACE > /dev/null 2>&1; then
    echo "PVC $PVC_NAME 不存在，请先运行: $0 --init"
    exit 1
  fi
  
  create_temp_pod
  
  echo "检查 PVC 中的二进制文件..."
  if kubectl exec -n $NAMESPACE $TEMP_POD_NAME -- test -f /data/dcimonitor 2>/dev/null; then
    echo "PVC 中的二进制文件存在"
    echo "文件信息:"
    kubectl exec -n $NAMESPACE $TEMP_POD_NAME -- ls -lh /data/dcimonitor
    
    # 检查版本信息
    if kubectl exec -n $NAMESPACE $TEMP_POD_NAME -- test -f /data/version 2>/dev/null; then
      VERSION=$(kubectl exec -n $NAMESPACE $TEMP_POD_NAME -- cat /data/version)
      echo "二进制文件版本: $VERSION"
      
      # 获取当前部署版本
      CURRENT_VERSION=$(get_current_version)
      if [ "$VERSION" = "$CURRENT_VERSION" ]; then
        echo "版本匹配当前部署版本"
      else
        echo "警告: 版本与当前部署版本 $CURRENT_VERSION 不匹配"
      fi
    else
      echo "警告: 二进制文件没有版本信息"
    fi
    
    # 检查构建信息
    if kubectl exec -n $NAMESPACE $TEMP_POD_NAME -- test -f /data/build_info 2>/dev/null; then
      echo "构建信息:"
      kubectl exec -n $NAMESPACE $TEMP_POD_NAME -- cat /data/build_info
    else
      echo "未找到构建信息文件"
    fi
  else
    echo "PVC 中不存在二进制文件"
  fi
}

# 清空 PVC 中的二进制文件
clear_pvc() {
  check_env
  
  # PVC 必须已存在
  if ! kubectl get pvc $PVC_NAME -n $NAMESPACE > /dev/null 2>&1; then
    echo "PVC $PVC_NAME 不存在，无需清理。"
    exit 0
  fi
  
  create_temp_pod
  
  echo "正在检查并清空 PVC ($PVC_NAME) 中的内容..."
  if kubectl exec -n $NAMESPACE $TEMP_POD_NAME -- test -f /data/dcimonitor 2>/dev/null; then
    kubectl exec -n $NAMESPACE $TEMP_POD_NAME -- rm -f /data/dcimonitor /data/version /data/build_info
    echo "PVC 中的 dcimonitor, version, 和 build_info 文件已成功删除。"
  else
    echo "PVC 中未找到 'dcimonitor' 文件，无需清理。"
  fi
  
  # 检查 deployment 是否存在
  if kubectl get deployment $DEPLOYMENT_NAME -n $NAMESPACE &> /dev/null; then
    echo "重启 $DEPLOYMENT_NAME 部署以立即应用更改..."
    kubectl patch deployment $DEPLOYMENT_NAME -n $NAMESPACE -p \
      "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"kubectl.kubernetes.io/restartedAt\":\"$(date +%Y-%m-%dT%H:%M:%S%z)\"}}}}}"
    echo "部署已触发重启，将回退使用镜像自带的二进制文件。"
  else
    echo "未找到 $DEPLOYMENT_NAME 部署，无需重启。"
  fi
  
  echo "PVC 清理操作完成。"
}

# 发送测试告警
test_webhook() {
  check_env

  # 创建临时目录
  mkdir -p $TEMP_DIR
  
  # 获取dcimonitor服务地址
  SERVICE_HOST="dcimonitor-service.dci.svc.cluster.local"
  SERVICE_PORT="8080"
  
  # 获取webhook认证令牌
  CONFIG_MAP=$(kubectl get configmap dcimonitor-config -n $NAMESPACE -o jsonpath='{.data.config\.yaml}')
  WEBHOOK_TOKEN=$(echo "$CONFIG_MAP" | grep webhook_auth_token | awk -F'"' '{print $2}')
  
  if [ -z "$WEBHOOK_TOKEN" ]; then
    echo "警告：未找到webhook认证令牌，使用默认值"
    WEBHOOK_TOKEN="dci-webhook-secret-token-2025"
  fi
  
  echo "使用令牌: $WEBHOOK_TOKEN"
  
  # 创建临时Pod发送测试告警
  echo "创建临时Pod发送测试告警..."
  
  cat > $TEMP_DIR/webhook-test-pod.yaml << EOF
apiVersion: v1
kind: Pod
metadata:
  name: webhook-test
  namespace: $NAMESPACE
spec:
  containers:
  - name: webhook-test
    image: curlimages/curl:latest
    command: ["/bin/sh", "-c"]
    args:
    - |
      echo '发送测试告警到 http://$SERVICE_HOST:$SERVICE_PORT/api/v1/alerts/webhook/prometheus'
      curl -v -H "Authorization: Bearer $WEBHOOK_TOKEN" -H "Content-Type: application/json" -X POST http://$SERVICE_HOST:$SERVICE_PORT/api/v1/alerts/webhook/prometheus -d '{
        "version": "4",
        "groupKey": "test_group",
        "status": "firing",
        "receiver": "default-receiver",
        "groupLabels": {
          "alertname": "TestAlert"
        },
        "commonLabels": {
          "alertname": "TestAlert",
          "severity": "critical",
          "instance": "test-instance"
        },
        "commonAnnotations": {
          "summary": "测试告警",
          "description": "这是一个从替换脚本发送的测试告警"
        },
        "externalURL": "http://alertmanager:9093",
        "alerts": [
          {
            "status": "firing",
            "labels": {
              "alertname": "TestAlert",
              "severity": "critical",
              "instance": "test-instance"
            },
            "annotations": {
              "summary": "测试告警",
              "description": "这是一个从替换脚本发送的测试告警"
            },
            "startsAt": "'"$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")"'",
            "endsAt": "0001-01-01T00:00:00Z",
            "generatorURL": "http://prometheus:9090/graph"
          }
        ]
      }'
      echo '测试完成，等待5秒...'
      sleep 5
  restartPolicy: Never
EOF
  
  kubectl apply -f $TEMP_DIR/webhook-test-pod.yaml
  
  echo "等待测试完成并获取日志..."
  kubectl wait --for=condition=Ready pod/webhook-test -n $NAMESPACE --timeout=60s
  sleep 10
  kubectl logs webhook-test -n $NAMESPACE
  
  echo "清理测试Pod..."
  kubectl delete pod webhook-test -n $NAMESPACE --wait=false
  
  echo "请检查dcimonitor日志以确认是否收到测试告警:"
  echo "kubectl logs -f deployment/$DEPLOYMENT_NAME -n $NAMESPACE | grep -i webhook"
}

# 恢复原始备份
restore_backup() {
  check_env
  
  # 必须有运行中的 Pod
  if [ -z "$POD_NAME" ]; then
    echo "错误：未找到运行中的 $DEPLOYMENT_NAME Pod，无法恢复"
    exit 1
  fi
  
  echo "检查原始备份文件是否存在..."
  if kubectl exec -n $NAMESPACE $POD_NAME -- test -f $ORIGINAL_BACKUP 2>/dev/null; then
    echo "从原始备份恢复二进制文件..."
    kubectl exec -n $NAMESPACE $POD_NAME -- sh -c "cp $ORIGINAL_BACKUP /lib/dci/dcimonitor && chmod +x /lib/dci/dcimonitor"
    
    echo "==== 重启 $DEPLOYMENT_NAME 部署... ===="
    kubectl patch deployment $DEPLOYMENT_NAME -n $NAMESPACE -p \
      "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"kubectl.kubernetes.io/restartedAt\":\"$(date +%Y-%m-%dT%H:%M:%S%z)\"}}}}}"
    
    echo "恢复完成！已恢复到原始版本。"
  else
    echo "错误：未找到原始备份文件 $ORIGINAL_BACKUP"
    exit 1
  fi
}

# 更新二进制文件
update_binary() {
  # 检查本地二进制文件
  if [ ! -f "/tmp/dcimonitor" ]; then
    echo "错误：/tmp/dcimonitor 不存在"
    echo "请先将二进制文件复制到 /tmp/dcimonitor"
    echo "例如：cp ./dcimonitor /tmp/dcimonitor"
    exit 1
  fi
  
  # 验证二进制文件是否可执行
  if [ ! -x "/tmp/dcimonitor" ]; then
    echo "警告：二进制文件不可执行，正在添加执行权限..."
    chmod +x /tmp/dcimonitor || {
      echo "错误：无法为二进制文件添加执行权限"
      exit 1
    }
    echo "已添加执行权限" 
  fi
  
  check_env
  
  # 确保 PVC 存在
  if ! kubectl get pvc $PVC_NAME -n $NAMESPACE > /dev/null 2>&1; then
    echo "错误：PVC $PVC_NAME 不存在，请先运行: $0 --init"
    exit 1
  fi
  
  # 创建原始备份（如果 Pod 存在且备份不存在）
  if [ ! -z "$POD_NAME" ]; then
    echo "检查是否存在原始备份..."
    if ! kubectl exec -n $NAMESPACE $POD_NAME -- test -f $ORIGINAL_BACKUP 2>/dev/null; then
      echo "创建原始备份..."
      kubectl exec -n $NAMESPACE $POD_NAME -- sh -c "mkdir -p $BACKUP_DIR && cp /lib/dci/dcimonitor $ORIGINAL_BACKUP" || {
        echo "警告：无法创建原始备份，但将继续执行"
      }
    fi
  fi
  
  create_temp_pod
  
  # 复制二进制文件到 PVC
  echo "复制二进制文件到 PVC..."
  kubectl cp /tmp/dcimonitor $NAMESPACE/$TEMP_POD_NAME:/data/dcimonitor
  kubectl exec -n $NAMESPACE $TEMP_POD_NAME -- chmod +x /data/dcimonitor
  
  # 获取并存储文件大小和时间戳
  FILE_SIZE=$(stat -c %s /tmp/dcimonitor 2>/dev/null || stat -f %z /tmp/dcimonitor)
  BUILD_TIME=$(date "+%Y-%m-%d %H:%M:%S")
  BUILD_HOST=$(hostname)
  
  # 输出文件哈希值
  echo "文件SHA256哈希值:"
  sha256sum /tmp/dcimonitor
  # 创建构建信息文件
  echo "创建构建信息文件..."
  cat > /tmp/build_info << EOF
版本: $VERSION
构建时间: $BUILD_TIME
文件大小: $FILE_SIZE 字节
构建主机: $BUILD_HOST
随机标识: $(date +%s)-$(cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 8 | head -n 1)
EOF
  
  kubectl cp /tmp/build_info $NAMESPACE/$TEMP_POD_NAME:/data/build_info
  echo "构建信息:"
  cat /tmp/build_info
  
  # 创建版本文件
  echo "创建版本文件..."
  echo "$VERSION" | kubectl exec -i -n $NAMESPACE $TEMP_POD_NAME -- sh -c "cat > /data/version"
  echo "已设置版本为: $VERSION"
  
  # 如果 Pod 存在，重启部署
  if [ ! -z "$POD_NAME" ]; then
    echo "重启 $DEPLOYMENT_NAME 部署..."
    kubectl patch deployment $DEPLOYMENT_NAME -n $NAMESPACE -p \
      "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"kubectl.kubernetes.io/restartedAt\":\"$(date +%Y-%m-%dT%H:%M:%S%z)\"}}}}}"
  else
    echo "未找到运行中的 $DEPLOYMENT_NAME Pod，请手动部署应用"
  fi
  
  echo "更新完成！dcimonitor 二进制文件（版本 $VERSION）已更新并持久化到 PVC。"
  echo "Pod 重启后将自动使用 PVC 中的二进制文件。"
  echo "如需恢复到原始版本，请运行: $0 --restore"
  echo "如需验证更新是否成功，请运行: $0 --file-info"
}

# 添加参数验证逻辑，update模式必须有指定版本
validate_parameters() {
  if [ "$MODE" = "update" ] && [ "$VERSION_SPECIFIED" != "true" ]; then
    echo "错误: 使用 -u/--update 选项时必须通过 -v/--version 指定版本"
    echo "例如: $0 --update --version 0.3.0"
    exit 1
  fi
}

# 解析命令行参数
MODE="help"
VERSION=$(get_current_version)
VERSION_SPECIFIED=false

for arg in "$@"; do
  case $arg in
    -u|--update)
      MODE="update"
      shift
      ;;
    -r|--restore)
      MODE="restore"
      shift
      ;;
    -c|--check)
      MODE="check"
      shift
      ;;
    -C|--clear)
      MODE="clear"
      shift
      ;;
    -i|--init)
      MODE="init"
      shift
      ;;
    -h|--help)
      MODE="help"
      shift
      ;;
    -t|--test)
      MODE="test"
      shift
      ;;
    -f|--file-info)
      MODE="file-info"
      shift
      ;;
    -v|--version)
      VERSION="$2"
      VERSION_SPECIFIED=true
      shift 2
      ;;
    *)
      # 未知参数
      ;;
  esac
done

# 解析完参数后添加验证
# 如果指定了版本但没有指定操作模式，默认执行更新操作
if [ "$VERSION_SPECIFIED" = true ] && [ "$MODE" = "help" ]; then
  MODE="update"
  echo "已指定版本 $VERSION，将执行更新操作"
fi

# 验证参数
validate_parameters

# 如果没有指定版本且无法获取部署版本，使用默认版本
if [ "$VERSION" = "unknown" ]; then
  VERSION="0.1.1"
  echo "警告: 无法获取部署版本，使用默认版本 $VERSION"
else
  echo "使用版本: $VERSION"
fi

# 主逻辑
trap cleanup EXIT

case $MODE in
  "restore")
    restore_backup
    ;;
  "check")
    check_binary
    ;;
  "clear")
    clear_pvc
    ;;
  "init")
    init_pvc
    ;;
  "test")
    test_webhook
    ;;
  "file-info")
    show_file_info
    ;;
  "update")
    update_binary
    ;;
  "help"|*)
    show_help
    ;;
esac