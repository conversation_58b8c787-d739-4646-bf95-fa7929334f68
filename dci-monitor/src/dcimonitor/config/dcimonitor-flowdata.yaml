# 流量数据处理器配置
service_name: "dci-dcimonitor-flowdata-local"
version: "1.0.0"

# Kafka配置
kafka:
  # Kafka服务器地址列表
  brokers:
    - "dcikafka.intra.citic-x.com:30010"
    - "dcikafka.intra.citic-x.com:30011"
    - "dcikafka.intra.citic-x.com:30012"
  # 订阅的主题列表，根据Kafka主题设计文档使用标准主题名称
  topics:
    - "dci.monitor.vtest.defaultchannel.flows.snmp"
    # - "dci.monitor.v1.defaultchannel.flows.snmp"
  consumer_group: "dci-flow-processor"
  security:
    tls:
      enabled: true
      # 注意：请将这里的路径修改为您的证书在本地的实际路径
      ca_file: "/Users/<USER>/code/dci/dci-workspace/dci-monitor/kafka/scripts/dci-kafka-certs/ca-chain.crt"
      cert_file: "/Users/<USER>/code/dci/dci-workspace/dci-monitor/kafka/scripts/dci-kafka-certs/dci-flowdata.client.crt"
      key_file: "/Users/<USER>/code/dci/dci-workspace/dci-monitor/kafka/scripts/dci-kafka-certs/dci-flowdata.client.key"
    sasl:
      enabled: true
      mechanism: "PLAIN"
      username: "dci-flowdata"
      # 本地测试时，可以直接写入密码，或通过设置 KAFKA_SASL_PASSWORD 环境变量
      password: "flowdata-secret"

# 处理器配置
processor:
  # 指标前缀
  metric_prefix: "dci_snmp_flow_"
  # 启用入向字节计数器
  enable_in_octets: true
  # 启用出向字节计数器
  enable_out_octets: true
  # 启用错误计数器
  enable_errors: true
  # 启用丢弃计数器
  enable_discards: true
  # 缓存TTL（秒）
  cache_ttl_seconds: 300
  # 处理间隔
  process_interval: "10s"
  # 附加的标签
  metric_labels:
    kubernetes_namespace: "dci"
    kubernetes_service_name: "dcimonitor-flowdata-service"
    # 标记代理和设备的区别
    data_source_type: "snmp_flow"

# 数据库配置
database:
  driver: "mysql"
  # 数据库主机
  host: "rm-el5i5532025ja5639.mysql.rds.pcloud-ops.citic.com"
  # 数据库端口
  port: 3306
  # 数据库用户名
  username: "root"
  # 数据库密码
  password: "FJkhJ0GBPKZva1w"
  # 数据库名称
  database: "dci_monitor"
  # 字符集
  charset: "utf8mb4"
  # 数据库连接字符串 (可选，如果不指定则根据上述参数自动构建)
  # dsn: "user:password@tcp(db-server:3306)/dci_monitor?charset=utf8mb4&parseTime=true"
  # 最大连接数
  max_open_conns: 10
  # 最大空闲连接数
  max_idle_conns: 5
  # 连接最大生命周期（秒）
  conn_max_lifetime: 3600

# Prometheus配置
prometheus:
  # Prometheus服务器地址
  server: "dciprometheus.intro.citic-x.com:30006"
  # 指标路径
  metrics_path: "/metrics"
  # 指标端口
  metrics_port: 30006

# 日志配置
logger:
  # 日志级别: debug, info, warn, error
  level: "debug"
  # 日志编码格式: json, console
  encoding: "json"
  # 日志目录
  dir: "./logs"
  # 单个日志文件最大大小(MB)
  maxSize: 100
  # 最大日志文件保留天数
  maxAge: 30
  # 是否压缩旧日志文件
  compress: true

# Mapper配置 端口映射查询Mysql缓存TTL，若空缺则默认5min
mapper:
  cache_ttl_minutes: 30