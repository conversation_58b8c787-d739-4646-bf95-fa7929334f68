# DCI监控系统配置示例 - 任务协同监控模块
# 此文件展示如何配置任务协同监控模块

# 服务器配置
server:
  port: 8080
  mode: debug  # debug, release, test

# 数据库配置
database:
  host: localhost
  port: 3306
  username: dci_user
  password: dci_password
  dbname: dci_monitor

# Prometheus配置
prometheus:
  address: http://localhost:9090
  username: ""
  password: ""

# 任务协同监控模块配置
task_collaboration:
  # 启用任务协同监控模块
  enabled: true
  
  # 任务信号处理器配置
  signal_processor:
    # Kafka集群地址
    brokers:
      - "localhost:9092"
      - "kafka1:9092"
      - "kafka2:9092"
    
    # 任务信号主题
    topic: "dci.task.signals"
    
    # 消费者组ID
    consumer_group_id: "dci-task-collaboration"
    
    # 会话超时时间
    session_timeout: "10s"
    
    # 心跳间隔
    heartbeat_interval: "3s"
  
  # 数据收集器配置
  data_collector:
    # 数据收集间隔
    collect_interval: "30s"
    
    # 启用的数据类型
    metrics_enabled: true
    logs_enabled: true
    alerts_enabled: true
    events_enabled: true
    
    # 最大并发收集数
    max_concurrent_collections: 10
    
    # 外部系统端点
    prometheus_endpoint: "http://localhost:9090"
    elasticsearch_endpoint: "http://localhost:9200"

# ============= 生产环境配置示例 =============
# 生产环境中的任务协同监控模块配置
# 
# task_collaboration:
#   enabled: true
#   signal_processor:
#     brokers:
#       - "kafka-prod-1:9092"
#       - "kafka-prod-2:9092" 
#       - "kafka-prod-3:9092"
#     topic: "dci.task.signals"
#     consumer_group_id: "dci-task-collaboration-prod"
#     session_timeout: "30s"
#     heartbeat_interval: "10s"
#   data_collector:
#     collect_interval: "60s"
#     metrics_enabled: true
#     logs_enabled: true
#     alerts_enabled: true
#     events_enabled: false  # 生产环境可能不需要事件收集
#     max_concurrent_collections: 50
#     prometheus_endpoint: "http://prometheus-prod:9090"
#     elasticsearch_endpoint: "http://elasticsearch-prod:9200"

# ============= 开发环境配置示例 =============
# 开发环境中可以禁用某些功能以简化调试
#
# task_collaboration:
#   enabled: true
#   signal_processor:
#     brokers:
#       - "localhost:9092"
#     topic: "dci.task.signals.dev"
#     consumer_group_id: "dci-task-collaboration-dev"
#     session_timeout: "5s"
#     heartbeat_interval: "2s"
#   data_collector:
#     collect_interval: "10s"  # 更短的收集间隔用于测试
#     metrics_enabled: true
#     logs_enabled: false      # 开发时可能不需要日志收集
#     alerts_enabled: true
#     events_enabled: false
#     max_concurrent_collections: 5
#     prometheus_endpoint: "http://localhost:9090"
#     elasticsearch_endpoint: "http://localhost:9200"

# ============= 禁用模块示例 =============
# 如果不需要任务协同监控功能，可以禁用模块
#
# task_collaboration:
#   enabled: false

# ============= 环境变量配置示例 =============
# 也可以通过环境变量配置（优先级高于配置文件）：
#
# export TASK_COLLABORATION_ENABLED=true
# export TASK_COLLABORATION_SIGNAL_PROCESSOR_BROKERS=kafka1:9092,kafka2:9092
# export TASK_COLLABORATION_SIGNAL_PROCESSOR_TOPIC=dci.task.signals
# export TASK_COLLABORATION_DATA_COLLECTOR_COLLECT_INTERVAL=30s 