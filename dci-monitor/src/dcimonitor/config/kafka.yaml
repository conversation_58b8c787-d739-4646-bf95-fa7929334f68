kafka:
  # Kafka 集群连接信息
  brokers:
    - "dcikafka.intra.citic-x.com:30002"
  
  # 安全配置（可选）
  security:
    tls:
      enabled: false
      # certFile: /path/to/cert.pem
      # keyFile: /path/to/key.pem
      # caFile: /path/to/ca.pem
    sasl:
      enabled: false
      # mechanism: PLAIN
      # username: user
      # password: pass
  
  # 主题配置
  topics:
    - name: "dci.monitor.v1.defaultchannel.topology.lldp"
      partitions: 3
      replicationFactor: 2
      retentionHours: 24  # 1天
      config:
        # 额外的主题级别配置
        "cleanup.policy": "delete"
        
    - name: "dci.monitor.v1.defaultchannel.metrics.telegraf"
      partitions: 6
      replicationFactor: 2
      retentionHours: 168  # 7天
      
    - name: "dci.monitor.v1.defaultchannel.logs.syslog"
      partitions: 3
      replicationFactor: 2
      retentionHours: 720  # 30天
      
    - name: "dci.monitor.v1.defaultchannel.tasks.control"
      partitions: 3
      replicationFactor: 2
      retentionHours: 24  # 1天 