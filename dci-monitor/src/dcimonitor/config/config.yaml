# DCI监控系统通用配置文件

# MySQL数据库配置
database:
  driver: mysql
  host: rm-el5i5532025ja5639.mysql.rds.pcloud-ops.citic.com
  port: 3306
  username: root
  password: FJkhJ0GBPKZva1w
  dbname: dci
  charset: utf8mb4

# Kafka配置
kafka:
  brokers:
    - dcikafka.intra.citic-x.com:30010
    - dcikafka.intra.citic-x.com:30011
    - dcikafka.intra.citic-x.com:30012
  security:
    tls:
      enabled: false
      certFile: ""
      keyFile: ""
      caFile: ""
    sasl:
      enabled: false
      mechanism: PLAIN
      username: ""
      password: ""
  topics:
    # 原始数据主题
    rawTopology: dci.monitor.v1.defaultchannel.topology.lldp
    rawMetrics: dci.monitor.v1.defaultchannel.metrics.telegraf
    rawLogs: dci.monitor.v1.defaultchannel.logs.syslog
    rawTasks: dci.monitor.v1.defaultchannel.tasks.control
    # 处理后数据主题
    processedTopology: dci.monitor.v1.defaultchannel.topology.processed
    processedEvents: dci.monitor.v1.defaultchannel.events
  consumerGroups:
    topologyProcessor: dci-topology-processor
    metricsProcessor: dci-metrics-processor
    logsProcessor: dci-logs-processor

# Kafka 输入配置
kafkaInput:
  brokers:
    - dcikafka.intra.citic-x.com:30010
    - dcikafka.intra.citic-x.com:30011
    - dcikafka.intra.citic-x.com:30012
  consumerGroup: dci-topology-processor
  topics:
    - dci.monitor.v1.defaultchannel.topology.lldp
  autoCommit: true
  commitInterval: 5s
  initialOffset: newest
  tls:
    enabled: false
    certFile: ""
    keyFile: ""
    caFile: ""

# Kafka 输出配置
kafkaOutput:
  brokers:
    - dcikafka.intra.citic-x.com:30010
    - dcikafka.intra.citic-x.com:30011
    - dcikafka.intra.citic-x.com:30012
  requiredAcks: local
  compression: none
  flushInterval: 1s
  tls:
    enabled: false
    certFile: ""
    keyFile: ""
    caFile: ""

# 拓扑处理器配置
topology:
  snapshotInterval: 1h
  healthCheckInterval: 30s

server:
  port: 8080
  mode: "debug" # gin mode: debug, release, test，debug模式下，会启用Swagger UI

logger:
  level: "debug" # debug, info, warn, error, dpanic, panic, fatal
  dir: "./logs"   # 日志文件存放目录
  maxSize: 2  # M
  maxBackups: 30 # 个
  maxAge: 30    # days
  compress: true # 是否压缩

# Prometheus 配置 
prometheus:
  address: "http://dciprometheus.intro.citic-x.com:30006" # Prometheus NodePort服务地址
  timeout: 30s # 查询超时时间
  username: "dciadmin"
  password: "c8PI3huRud8tW" 
  rule_file: "./config/prometheus/rules/dci_alerts.yml" # 本地规则文件路径
  reload_enabled: false # 本地开发环境不启用重新加载功能，因为Prometheus在K8s中

# AlertManager 配置 当前本地无法测试
alertmanager:
  address: "http://alertmanager.dci.svc.cluster.local:9093" # K8s中AlertManager服务地址
  webhook_auth_token: "your-webhook-auth-token" # 与K8s环境中配置一致的令牌

# 设备IP地址映射配置（启动时从配置文件加载）
device_mapping:
  enabled: true
  config_file: "./config/device_mapping.yaml"

# 任务协同监控模块配置
task_collaboration:
  enabled: true
  data_collector:
    collect_interval: "30s"
    metrics_enabled: true
    logs_enabled: false
    alerts_enabled: true
    events_enabled: false
    max_concurrent_collections: 10
    elasticsearch_endpoint: "http://localhost:9200"

# 指标监测模块配置
metrics_monitoring:
  enabled: true
  baseline_duration: "30m"      # 基线期时长：任务开始前的数据分析时间段
  extended_duration: "1h"       # 延展监测时长：任务结束后的持续监测时间
  query_interval: "1m"          # 指标查询间隔
  batch_size: 100               # 批量处理大小
  timeout: "30s"                # 查询超时时间
  supported_metrics:            # 支持的指标类型
    - "cpu"                     # CPU使用率监测
    - "memory"                  # 内存使用率监测  
    - "traffic"                 # 网络流量监测
