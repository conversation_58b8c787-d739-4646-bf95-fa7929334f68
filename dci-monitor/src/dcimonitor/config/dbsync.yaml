# 数据库同步服务配置

# 源数据库配置（dci）
source_db:
  driver: "mysql"
  host: "rm-el5i5532025ja5639.mysql.rds.pcloud-ops.citic.com"
  port: 3306
  username: "root"
  password: "FJkhJ0GBPKZva1w"
  dbname: "dci"
  charset: "utf8mb4"
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime: 3600

# 目标数据库配置（dci_monitor）
target_db:
  driver: "mysql"
  host: "rm-el5i5532025ja5639.mysql.rds.pcloud-ops.citic.com"
  port: 3306
  username: "root"
  password: "FJkhJ0GBPKZva1w"
  dbname: "dci_monitor"
  charset: "utf8mb4"
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime: 3600

# 同步配置
sync_interval: 10   # 同步间隔（分钟），0表示不启用定时同步
sync_on_start: true # 是否在启动时执行一次同步
max_retries: 3      # 最大重试次数

# 同步表配置
tables:
  - name: "dci_device"
    target: "dci_device"
    primary_key: "id"
  - name: "dci_node"
    target: "dci_node"
    primary_key: "id"
  - name: "dci_node_business"
    target: "dci_node_business"
    primary_key: "id"
  - name: "dci_vni"
    target: "dci_vni"
    primary_key: "id"
  - name: "dci_logic_port"
    target: "dci_logic_port"
    primary_key: "id"

# 特殊映射配置
special_mappings:
  - source_table: "dci_logic_port_device"
    target_table: "dci_logic_port_device"
    primary_key: "id"
    mapping_columns:
      - source: "id"
        target: "id"
      - source: "device_id"
        target: "device_id"
      - source: "port"
        target: "port"
      - source: "logic_port_id"
        target: "logic_port_id"
      - source: "physical_port_state"
        target: "physical_port_state"
      - source: "description"
        target: "description"
      - source: "create_time"
        target: "create_time"
      - source: "update_time"
        target: "update_time"

# Kafka配置
kafka:
  enabled: true
  brokers:
    - "dcikafka.intra.citic-x.com:30010"
    - "dcikafka.intra.citic-x.com:30011"
    - "dcikafka.intra.citic-x.com:30012"
  topics:
    - "telegraf_snmp"
  consumer_group: "dbsync-consumer"
  security:
    tls:
      enabled: false
    sasl:
      enabled: false
      mechanism: "PLAIN"
      username: "admin"
      password: "admin123"

# API服务配置
api:
  enabled: true
  port: 8090
  endpoints:
    - path: "/api/sync/status"
      method: "GET"
    - path: "/api/sync/trigger"
      method: "POST"
    - path: "/api/mapping/port"
      method: "GET"
    - path: "/api/mapping/interfaces"
      method: "GET"

# 日志配置
log:
  level: "info"
  encoding: "json"
  dir: "logs"
  maxSize: 100  # MB
  maxAge: 30    # 天
  compress: true 