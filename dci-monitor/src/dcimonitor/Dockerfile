# syntax=docker/dockerfile:1

# --- Build Stage ---
FROM golang:1.24-alpine AS builder

# -- Build Arguments --
# 控制是否使用预编译的二进制文件 (由 build.sh 脚本设置)
ARG USE_PREBUILT=false
# 预编译二进制在构建上下文中的临时文件名 (由 build.sh 脚本设置)
ARG PREBUILT_TEMP_NAME=dcimonitor_prebuilt_for_docker
# 最终二进制文件在 builder 阶段的目标路径
ARG TARGET_BIN_PATH=/dcimonitor
# ---------------------

# 设置工作目录
WORKDIR /lib/dci

# 复制 Go 模块文件
COPY go.mod go.sum ./

# 修复：将本地依赖 'common' 模块复制到 Docker 构建环境中
# 'go mod download' 依赖此模块来解析 'replace' 指令
COPY common ../common

# 下载依赖
RUN go mod download

# 复制源代码和潜在的预编译文件
COPY . .

# -- 条件化编译步骤 --
RUN <<EOF
set -e # 确保任何命令失败时退出

# 检查是否需要使用预编译，并且预编译文件存在于上下文中（已被复制到 /app）
if [ "$USE_PREBUILT" = "true" ] && [ -f "$PREBUILT_TEMP_NAME" ]; then
    echo "--> Dockerfile: 使用预编译的二进制文件 ($PREBUILT_TEMP_NAME)..."
    # 将预编译文件移动到目标路径
    mv "$PREBUILT_TEMP_NAME" "$TARGET_BIN_PATH"
else
    # 如果不使用预编译 或 预编译文件不存在，则执行构建
    echo "--> Dockerfile: 在 Docker 内部构建二进制文件..."
    CGO_ENABLED=0 GOOS=linux go build -ldflags="-s -w" -o "$TARGET_BIN_PATH" ./main.go
fi

# 验证最终二进制文件是否存在且不为空
if [ ! -s "$TARGET_BIN_PATH" ]; then
    echo "错误：Dockerfile: 最终二进制文件 $TARGET_BIN_PATH 未找到或大小为0！构建失败。"
    exit 1
fi
echo "--> Dockerfile: 最终二进制文件位于 $TARGET_BIN_PATH"
EOF
# ---------------------

# --- Final Stage ---
FROM alpine:latest

# 设置工作目录
WORKDIR /lib/dci

# 从 builder 阶段复制编译后的二进制文件
COPY --from=builder /dcimonitor /lib/dci/dcimonitor

# 复制配置文件目录 (假设配置文件在 config/ 目录下)
# 注意：实际的配置文件内容将通过 Kubernetes ConfigMap 挂载
COPY config /lib/dci/config

# 暴露应用程序端口 (假设配置文件中定义了 8080)
EXPOSE 8080

# 设置时区
RUN apk add --no-cache tzdata

# 设置时区环境变量
ENV TZ=Asia/Shanghai

# 将入口点分为 ENTRYPOINT 和 CMD
# ENTRYPOINT 定义主可执行文件
ENTRYPOINT ["/lib/dci/dcimonitor"]

# CMD 提供默认命令和参数，可以被 `docker run` 的参数覆盖
# 默认启动 server 子命令
CMD ["server", "--config=/lib/dci/config/config.yaml"]
