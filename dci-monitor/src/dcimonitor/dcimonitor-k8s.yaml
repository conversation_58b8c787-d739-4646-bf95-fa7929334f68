# -----------------------------------------------------------------------------
# Secret for dci-monitor service credentials
#
# IMPORTANT: Before applying, you must create the 'dci-monitor' client
# certificate and password using the provided script:
#
#   cd dci-monitor/kafka/scripts
#   ./issue_new_client_cert.sh dci-monitor
#
# Then, use the generated password to create the secret. It's recommended to create
# one single secret for all credentials of this service.
#
#   kubectl create secret generic dcimonitor-credentials -n dci \
#     --from-literal=db-username='root' \
#     --from-literal=db-password='YOUR_DB_PASSWORD' \
#     --from-literal=kafka-username='dci-monitor' \
#     --from-literal=kafka-password='<password_from_issue_script>'
#
# -----------------------------------------------------------------------------
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dcimonitor
  namespace: dci
  labels:
    app: dcimonitor
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dcimonitor
  template:
    metadata:
      labels:
        app: dcimonitor
    spec:
      imagePullSecrets:
        - name: dci-images-key
      containers:
      - name: dcimonitor
        image: cr.registry.pcloud.citic.com/dci/dcimonitor:dcimonitor-0.4.0
        imagePullPolicy: Always
        command: ["/bin/sh", "-c"]
        args:
          - |
            set -e
            IMAGE_BINARY="/lib/dci/dcimonitor"
            PVC_BINARY="/data/dcimonitor"
            PVC_VERSION_FILE="/data/version"
            echo "--------------- VERSION -----------------"
            EXPECTED_VERSION="${IMAGE_VERSION:-0.2.0}"
            echo "        IMAGE_VERSION: $IMAGE_VERSION"
            echo "-----------------------------------------"
            BINARY_TO_EXEC="$IMAGE_BINARY"
            
            echo "--- DCI Monitor Dynamic Binary Loader ---"
            echo "Image Version (Expected): $EXPECTED_VERSION"
            
            if [ -f "$PVC_BINARY" ] && [ -x "$PVC_BINARY" ] && [ -f "$PVC_VERSION_FILE" ]; then
              PVC_VERSION=$(cat "$PVC_VERSION_FILE")
              echo "Found binary in PVC. Version: $PVC_VERSION"
              
              if [ "$PVC_VERSION" = "$EXPECTED_VERSION" ]; then
                echo "✅ PVC version matches. Will use binary from PVC."
                BINARY_TO_EXEC="$PVC_BINARY"
              else
                echo "⚠️ PVC version ($PVC_VERSION) mismatches, expected: $EXPECTED_VERSION"
                echo "✅ Using binary from Image."
              fi
            else
              echo "ℹ️ No valid binary found in PVC. "
              echo "✅ Using binary from Image."
            fi
            echo "-----------------------------------------"
            echo "🚀 Executing: $BINARY_TO_EXEC ✅"
            echo "-----------------------------------------"
            
            exec "$BINARY_TO_EXEC" server --config=/lib/dci/config/config.yaml
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: TZ
          value: "Asia/Shanghai"
        - name: IMAGE_VERSION
          value: "0.4.0"
        volumeMounts:
        - name: config-volume
          mountPath: /lib/dci/config
        - name: kafka-client-certs
          mountPath: /etc/kafka/certs
          readOnly: true
        - name: log-volume
          mountPath: /var/log/dcimonitor
        - name: binary-storage
          mountPath: /data
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 20
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            cpu: "100m"
            memory: "128Mi"
          limits:
            cpu: "1G"
            memory: "2Gi"
      volumes:
      - name: config-volume
        configMap:
          name: dcimonitor-config
      - name: kafka-client-certs
        secret:
          secretName: kafka-client-certs # This secret is created by 'apply_secrets.sh'
      - name: log-volume
        emptyDir: {}
      - name: binary-storage
        persistentVolumeClaim:
          claimName: dcimonitor-binary-pvc
