# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace history
.history/

# Build output
/dci-monitor
/dcimonitor
/main

# Log files
/logs/
*.log

# Config files that might contain secrets (if you add a local config)
config/config.local.yaml

# Build output directory from build.sh
bin/
dcimonitor_prebuilt_for_docker