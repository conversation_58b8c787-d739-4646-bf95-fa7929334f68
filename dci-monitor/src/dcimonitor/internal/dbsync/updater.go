package dbsync

import (
	"context"
	"fmt"
	"time"

	"dcimonitor/internal/kafka"

	"github.com/IBM/sarama"
	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
)

// InterfaceIndexUpdater 接口索引更新器
type InterfaceIndexUpdater struct {
	config        *Config
	syncService   *DBSyncService
	logger        *zap.Logger
	kafkaConsumer *kafka.Consumer
	stopChan      chan struct{}
}

// NewInterfaceIndexUpdater 创建接口索引更新器
func NewInterfaceIndexUpdater(config *Config, syncService *DBSyncService, logger *zap.Logger) (*InterfaceIndexUpdater, error) {
	if logger == nil {
		var err error
		logger, err = zap.NewProduction()
		if err != nil {
			return nil, fmt.Errorf("创建日志记录器失败: %w", err)
		}
	}

	return &InterfaceIndexUpdater{
		config:      config,
		syncService: syncService,
		logger:      logger,
		stopChan:    make(chan struct{}),
	}, nil
}

// Start 启动接口索引更新器
func (u *InterfaceIndexUpdater) Start(ctx context.Context) error {
	u.logger.Info("启动接口索引更新器")

	// 创建Kafka消费者配置
	consumerConfig := &kafka.ConsumerConfig{
		Brokers:        u.config.Kafka.Brokers,
		ConsumerGroup:  u.config.Kafka.ConsumerGroup,
		Topics:         u.config.Kafka.Topics,
		AutoCommit:     true,
		CommitInterval: time.Second * 5, // 设置自动提交间隔为5秒
		InitialOffset:  sarama.OffsetNewest,
		MaxRetries:     3,
		RetryInterval:  time.Second * 5,
	}

	// 设置TLS和SASL配置
	if u.config.Kafka.Security.TLS.Enabled {
		consumerConfig.TLS = &kafka.KafkaTLSConfig{
			Enabled: true,
		}
	}

	if u.config.Kafka.Security.SASL.Enabled {
		consumerConfig.SASL = &kafka.KafkaSASLConfig{
			Enabled:   true,
			Mechanism: u.config.Kafka.Security.SASL.Mechanism,
			Username:  u.config.Kafka.Security.SASL.Username,
			Password:  u.config.Kafka.Security.SASL.Password,
		}
	}

	// 创建Kafka消费者
	consumer, err := kafka.NewConsumer(consumerConfig)
	if err != nil {
		return fmt.Errorf("创建Kafka消费者失败: %w", err)
	}
	u.kafkaConsumer = consumer

	// 创建消息处理器
	handler := func(msg *sarama.ConsumerMessage) error {
		return u.handleMessage(msg)
	}

	// 启动消费者
	go func() {
		if err := consumer.Start(ctx, handler); err != nil {
			u.logger.Error("Kafka消费者启动失败", zap.Error(err))
		}
	}()

	return nil
}

// Stop 停止接口索引更新器
func (u *InterfaceIndexUpdater) Stop() error {
	close(u.stopChan)

	if u.kafkaConsumer != nil {
		return u.kafkaConsumer.Stop()
	}

	return nil
}

// handleMessage 处理Kafka消息
func (u *InterfaceIndexUpdater) handleMessage(msg *sarama.ConsumerMessage) error {
	u.logger.Debug("接收到Kafka消息",
		zap.String("topic", msg.Topic),
		zap.Int32("partition", msg.Partition),
		zap.Int64("offset", msg.Offset))

	// 解析消息内容
	var telegrafMsg TelegrafMessage
	if err := jsoniter.Unmarshal(msg.Value, &telegrafMsg); err != nil {
		u.logger.Error("解析Telegraf消息失败", zap.Error(err))
		return err
	}

	// 提取设备IP、接口名称和接口索引
	deviceIP := telegrafMsg.Tags.DeviceIP
	ifIndex := telegrafMsg.Tags.IfIndex
	ifName := telegrafMsg.Fields.IfName

	// 验证必要字段
	if deviceIP == "" || ifIndex == "" || ifName == "" {
		u.logger.Warn("Telegraf消息缺少必要字段",
			zap.String("device_ip", deviceIP),
			zap.String("if_index", ifIndex),
			zap.String("if_name", ifName))
		return nil
	}

	u.logger.Debug("处理接口索引更新",
		zap.String("device_ip", deviceIP),
		zap.String("if_index", ifIndex),
		zap.String("if_name", ifName))

	// 更新接口索引映射
	if err := u.syncService.UpdateInterfaceIndex(deviceIP, ifName, ifIndex); err != nil {
		u.logger.Error("更新接口索引失败",
			zap.String("device_ip", deviceIP),
			zap.String("if_name", ifName),
			zap.String("if_index", ifIndex),
			zap.Error(err))
		return err
	}

	// 判断接口名称是否包含VNI信息
	// VNI解析规则：当接口名称包含点号(.)时，点号后的数字若大于5000000通常表示VNI ID
	// 例如: "10GE1/0/1.6005002" 中的 6005002 是VNI ID
	// 此处仅记录日志，不进行进一步处理，VNI信息已经在数据库同步中处理
	if isVNIInterface(ifName) {
		u.logger.Info("检测到VNI接口",
			zap.String("device_ip", deviceIP),
			zap.String("if_name", ifName),
			zap.String("if_index", ifIndex))
	}

	u.logger.Debug("接口索引更新成功",
		zap.String("device_ip", deviceIP),
		zap.String("if_name", ifName),
		zap.String("if_index", ifIndex))

	return nil
}

// isVNIInterface 判断接口名称是否包含VNI信息
func isVNIInterface(ifName string) bool {
	// 简单实现：检查是否包含点号，实际应该使用正则表达式或更复杂的逻辑
	for i := 0; i < len(ifName); i++ {
		if ifName[i] == '.' {
			// 检查点号后是否有数字，且大于 0
			var vniID int
			if _, err := fmt.Sscanf(ifName[i+1:], "%d", &vniID); err == nil {
				return vniID > 0
			}
			return false
		}
	}
	return false
}
