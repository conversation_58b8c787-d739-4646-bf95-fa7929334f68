package dbsync

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
)

// APIServer HTTP API服务
type APIServer struct {
	config      *Config
	syncService *DBSyncService
	scheduler   *SyncScheduler
	dataMapper  *DataMapper
	logger      *zap.Logger
	server      *http.Server
}

// NewAPIServer 创建HTTP API服务
func NewAPIServer(config *Config, syncService *DBSyncService, scheduler *SyncScheduler, dataMapper *DataMapper, logger *zap.Logger) *APIServer {
	if logger == nil {
		logger, _ = zap.NewProduction()
	}

	return &APIServer{
		config:      config,
		syncService: syncService,
		scheduler:   scheduler,
		dataMapper:  dataMapper,
		logger:      logger,
	}
}

// Start 启动HTTP API服务
func (s *APIServer) Start() error {
	if !s.config.API.Enabled {
		s.logger.Info("HTTP API服务已禁用")
		return nil
	}

	mux := http.NewServeMux()

	// 注册API路由
	mux.HandleFunc("/api/sync/status", s.handleSyncStatus)
	mux.HandleFunc("/api/sync/trigger", s.handleTriggerSync)
	mux.HandleFunc("/api/mapping/port", s.handlePortMapping)
	mux.HandleFunc("/api/mapping/interfaces", s.handleDeviceInterfaces)

	// 创建HTTP服务器
	s.server = &http.Server{
		Addr:         fmt.Sprintf(":%d", s.config.API.Port),
		Handler:      mux,
		ReadTimeout:  10 * time.Second,
		WriteTimeout: 10 * time.Second,
		IdleTimeout:  30 * time.Second,
	}

	s.logger.Info("启动HTTP API服务", zap.Int("port", s.config.API.Port))

	// 在新的goroutine中启动服务器
	go func() {
		if err := s.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			s.logger.Error("HTTP API服务启动失败", zap.Error(err))
		}
	}()

	return nil
}

// Stop 停止HTTP API服务
func (s *APIServer) Stop() error {
	if s.server == nil {
		return nil
	}

	s.logger.Info("停止HTTP API服务")
	return s.server.Close()
}

// handleSyncStatus 处理同步状态请求
func (s *APIServer) handleSyncStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	status := s.syncService.GetSyncStatus()
	s.jsonResponse(w, APIResponse{
		Success: true,
		Message: "获取同步状态成功",
		Data:    status,
	})
}

// handleTriggerSync 处理触发同步请求
func (s *APIServer) handleTriggerSync(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求参数
	isFullSync := false
	if fullSyncParam := r.URL.Query().Get("full"); fullSyncParam != "" {
		var err error
		isFullSync, err = strconv.ParseBool(fullSyncParam)
		if err != nil {
			s.jsonResponse(w, APIResponse{
				Success: false,
				Message: "参数解析失败: " + err.Error(),
			})
			return
		}
	}

	// 触发同步
	go func() {
		if err := s.scheduler.TriggerSync(isFullSync); err != nil {
			s.logger.Error("触发同步失败", zap.Error(err))
		}
	}()

	s.jsonResponse(w, APIResponse{
		Success: true,
		Message: fmt.Sprintf("触发%s同步成功", map[bool]string{true: "全量", false: "增量"}[isFullSync]),
	})
}

// handlePortMapping 处理端口映射请求
func (s *APIServer) handlePortMapping(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求参数
	deviceIP := r.URL.Query().Get("device_ip")
	ifIndex := r.URL.Query().Get("if_index")

	if deviceIP == "" || ifIndex == "" {
		s.jsonResponse(w, APIResponse{
			Success: false,
			Message: "缺少必要参数: device_ip 和 if_index",
		})
		return
	}

	// 查询端口映射
	mapping, err := s.dataMapper.MapInterfaceToVNI(deviceIP, ifIndex)
	if err != nil {
		s.jsonResponse(w, APIResponse{
			Success: false,
			Message: "查询端口映射失败: " + err.Error(),
		})
		return
	}

	if mapping == nil {
		s.jsonResponse(w, APIResponse{
			Success: false,
			Message: "未找到端口映射",
		})
		return
	}

	s.jsonResponse(w, APIResponse{
		Success: true,
		Message: "查询端口映射成功",
		Data:    mapping,
	})
}

// handleDeviceInterfaces 处理设备接口请求
func (s *APIServer) handleDeviceInterfaces(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求参数
	deviceIP := r.URL.Query().Get("device_ip")
	if deviceIP == "" {
		s.jsonResponse(w, APIResponse{
			Success: false,
			Message: "缺少必要参数: device_ip",
		})
		return
	}

	// 查询设备接口
	interfaces, err := s.dataMapper.GetDeviceInterfaces(deviceIP)
	if err != nil {
		s.jsonResponse(w, APIResponse{
			Success: false,
			Message: "查询设备接口失败: " + err.Error(),
		})
		return
	}

	if len(interfaces) == 0 {
		s.jsonResponse(w, APIResponse{
			Success: false,
			Message: "未找到设备接口",
		})
		return
	}

	s.jsonResponse(w, APIResponse{
		Success: true,
		Message: "查询设备接口成功",
		Data:    interfaces,
	})
}

// jsonResponse 发送JSON响应
func (s *APIServer) jsonResponse(w http.ResponseWriter, resp APIResponse) {
	w.Header().Set("Content-Type", "application/json")

	jsonData, err := jsoniter.Marshal(resp)
	if err != nil {
		s.logger.Error("JSON编码失败", zap.Error(err))
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	w.Write(jsonData)
}
