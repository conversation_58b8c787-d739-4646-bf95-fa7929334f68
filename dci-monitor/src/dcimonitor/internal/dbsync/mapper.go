package dbsync

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"
)

// DataMapper 数据映射器
type DataMapper struct {
	sourceDB *sql.DB
	targetDB *sql.DB
	logger   *zap.Logger
}

// NewDataMapper 创建数据映射器
func NewDataMapper(sourceDB, targetDB *sql.DB, logger *zap.Logger) *DataMapper {
	return &DataMapper{
		sourceDB: sourceDB,
		targetDB: targetDB,
		logger:   logger,
	}
}

// MapInterfaceToVNI 通过设备IP和接口索引查询端口映射信息
func (m *DataMapper) MapInterfaceToVNI(deviceIP, ifIndex string) (*PortMapping, error) {
	// 检查设备接口是否存在
	query := `
		SELECT 
			d.id, d.device_name, d.device_management_ip,
			p.id, p.port, p.if_index
		FROM 
			dci_device d
		JOIN 
			dci_logic_port_device p ON d.id = p.device_id
		WHERE 
			d.device_management_ip = ? AND p.if_index = ?
		LIMIT 1
	`

	var (
		deviceID, deviceName, deviceManagementIP string
		portID, portName, portIfIndex            string
	)

	err := m.targetDB.QueryRow(query, deviceIP, ifIndex).Scan(
		&deviceID, &deviceName, &deviceManagementIP,
		&portID, &portName, &portIfIndex,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("未找到设备接口: %s/%s", deviceIP, ifIndex)
		}
		return nil, fmt.Errorf("查询设备接口失败: %w", err)
	}

	// 查询该设备接口关联的VNI
	vniQuery := `
		SELECT 
			v.vni_id
		FROM 
			dci_vni v
		JOIN 
			dci_node_business nb ON v.vni_id = nb.vni
		JOIN 
			dci_logic_port lp ON nb.logic_port_id = lp.id
		JOIN 
			dci_node n ON nb.node_id = n.id
		JOIN 
			dci_device d ON n.device_id = d.id
		JOIN 
			dci_logic_port_device p ON d.id = p.device_id
		WHERE 
			d.device_management_ip = ? AND p.if_index = ?
		LIMIT 1
	`

	var vniID sql.NullString
	err = m.targetDB.QueryRow(vniQuery, deviceIP, ifIndex).Scan(&vniID)

	// 即使未找到VNI，也返回设备接口信息
	portMapping := &PortMapping{
		DeviceID:   deviceID,
		DeviceIP:   deviceManagementIP,
		DeviceName: deviceName,
		PortID:     portID,
		PortName:   portName,
		IfIndex:    portIfIndex,
	}

	if err == nil && vniID.Valid {
		portMapping.VNIID = vniID.String
	}

	return portMapping, nil
}

// FindMissingInterfaces 查找缺失接口映射的设备
func (m *DataMapper) FindMissingInterfaces() ([]InterfaceInfo, error) {
	// 查询有流量数据但没有接口映射的设备
	query := `
		SELECT DISTINCT
			d.device_management_ip
		FROM
			dci_device d
		WHERE
			EXISTS (
				SELECT 1 FROM dci_logic_port_device p 
				WHERE p.device_id = d.id AND p.if_index IS NULL
			)
	`

	rows, err := m.targetDB.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询缺失接口映射的设备失败: %w", err)
	}
	defer rows.Close()

	var deviceIPs []string
	for rows.Next() {
		var deviceIP string
		if err := rows.Scan(&deviceIP); err != nil {
			return nil, fmt.Errorf("扫描设备IP失败: %w", err)
		}
		deviceIPs = append(deviceIPs, deviceIP)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历设备IP时发生错误: %w", err)
	}

	// 如果没有设备缺失接口映射，直接返回
	if len(deviceIPs) == 0 {
		return nil, nil
	}

	// 构建IN查询条件
	placeholders := strings.Repeat("?,", len(deviceIPs))
	placeholders = placeholders[:len(placeholders)-1] // 去掉最后的逗号

	// 查询缺失接口映射的端口
	portQuery := fmt.Sprintf(`
		SELECT
			d.device_management_ip,
			p.port
		FROM
			dci_device d
		JOIN
			dci_logic_port_device p ON d.id = p.device_id
		WHERE
			d.device_management_ip IN (%s)
			AND p.if_index IS NULL
	`, placeholders)

	// 转换参数列表
	args := make([]interface{}, len(deviceIPs))
	for i, ip := range deviceIPs {
		args[i] = ip
	}

	// 执行查询
	portRows, err := m.targetDB.Query(portQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("查询缺失接口映射的端口失败: %w", err)
	}
	defer portRows.Close()

	var interfaces []InterfaceInfo
	for portRows.Next() {
		var deviceIP, portName string
		if err := portRows.Scan(&deviceIP, &portName); err != nil {
			return nil, fmt.Errorf("扫描端口信息失败: %w", err)
		}

		interfaces = append(interfaces, InterfaceInfo{
			DeviceIP:  deviceIP,
			IfName:    portName,
			Timestamp: time.Now().Unix(),
		})
	}

	if err := portRows.Err(); err != nil {
		return nil, fmt.Errorf("遍历端口信息时发生错误: %w", err)
	}

	return interfaces, nil
}

// FindVNIForInterfaces 查找接口关联的VNI
func (m *DataMapper) FindVNIForInterfaces(deviceIP string) ([]InterfaceMapping, error) {
	// 查询设备ID
	var deviceID string
	err := m.targetDB.QueryRow("SELECT id FROM dci_device WHERE device_management_ip = ?", deviceIP).Scan(&deviceID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("未找到设备: %s", deviceIP)
		}
		return nil, fmt.Errorf("查询设备ID失败: %w", err)
	}

	// 查询设备的端口及关联的VNI
	query := `
		SELECT
			p.port, p.if_index, nb.vni
		FROM
			dci_logic_port_device p
		JOIN
			dci_node n ON p.device_id = n.device_id
		JOIN
			dci_node_business nb ON n.id = nb.node_id
		WHERE
			p.device_id = ?
	`

	rows, err := m.targetDB.Query(query, deviceID)
	if err != nil {
		return nil, fmt.Errorf("查询端口VNI映射失败: %w", err)
	}
	defer rows.Close()

	var mappings []InterfaceMapping
	for rows.Next() {
		var portName, ifIndex, vniID sql.NullString
		if err := rows.Scan(&portName, &ifIndex, &vniID); err != nil {
			return nil, fmt.Errorf("扫描端口VNI映射失败: %w", err)
		}

		// 只添加有有效ifIndex和VNI的映射
		if portName.Valid && ifIndex.Valid && vniID.Valid {
			mappings = append(mappings, InterfaceMapping{
				DeviceIP: deviceIP,
				PortName: portName.String,
				IfIndex:  ifIndex.String,
				VNIID:    vniID.String,
			})
		}
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历端口VNI映射时发生错误: %w", err)
	}

	return mappings, nil
}

// GetDeviceInterfaces 获取设备的所有接口
func (m *DataMapper) GetDeviceInterfaces(deviceIP string) ([]string, error) {
	// 查询设备ID
	var deviceID string
	err := m.targetDB.QueryRow("SELECT id FROM dci_device WHERE device_management_ip = ?", deviceIP).Scan(&deviceID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("未找到设备: %s", deviceIP)
		}
		return nil, fmt.Errorf("查询设备ID失败: %w", err)
	}

	// 查询设备的所有接口
	query := "SELECT port FROM dci_logic_port_device WHERE device_id = ?"
	rows, err := m.targetDB.Query(query, deviceID)
	if err != nil {
		return nil, fmt.Errorf("查询设备接口失败: %w", err)
	}
	defer rows.Close()

	var interfaces []string
	for rows.Next() {
		var ifName string
		if err := rows.Scan(&ifName); err != nil {
			return nil, fmt.Errorf("扫描接口名称失败: %w", err)
		}
		interfaces = append(interfaces, ifName)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历接口时发生错误: %w", err)
	}

	return interfaces, nil
}
