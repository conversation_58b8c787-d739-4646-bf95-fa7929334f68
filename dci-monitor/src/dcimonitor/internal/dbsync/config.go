package dbsync

import (
	"fmt"

	"github.com/spf13/viper"
)

// Config 数据库同步服务配置
type Config struct {
	SourceDB        DatabaseConfig   `yaml:"source_db" json:"source_db"`
	TargetDB        DatabaseConfig   `yaml:"target_db" json:"target_db"`
	SyncInterval    int              `yaml:"sync_interval" json:"sync_interval"`
	SyncOnStart     bool             `yaml:"sync_on_start" json:"sync_on_start"`
	MaxRetries      int              `yaml:"max_retries" json:"max_retries"`
	Tables          []TableConfig    `yaml:"tables" json:"tables"`
	SpecialMappings []SpecialMapping `yaml:"special_mappings" json:"special_mappings"`
	Kafka           *KafkaConfig     `yaml:"kafka" json:"kafka"`
	API             APIConfig        `yaml:"api" json:"api"`
	Log             LogConfig        `yaml:"log" json:"log"`
	TestMode        bool             `yaml:"-" json:"-"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver          string `yaml:"driver" json:"driver"`
	Host            string `yaml:"host" json:"host"`
	Port            int    `yaml:"port" json:"port"`
	Username        string `yaml:"username" json:"username"`
	Password        string `yaml:"password" json:"password"`
	DBName          string `yaml:"dbname" json:"dbname"`
	Charset         string `yaml:"charset" json:"charset"`
	MaxOpenConns    int    `yaml:"max_open_conns" json:"max_open_conns"`
	MaxIdleConns    int    `yaml:"max_idle_conns" json:"max_idle_conns"`
	ConnMaxLifetime int    `yaml:"conn_max_lifetime" json:"conn_max_lifetime"`
	DSN             string `yaml:"dsn" json:"dsn"`
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	if c.DSN != "" {
		return c.DSN
	}

	// 构建DSN
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=true&loc=Local",
		c.Username, c.Password, c.Host, c.Port, c.DBName, c.Charset)
}

// TableConfig 表配置
type TableConfig struct {
	Name       string `mapstructure:"name" json:"name"`
	Target     string `mapstructure:"target" json:"target"`
	PrimaryKey string `mapstructure:"primary_key" json:"primary_key"`
}

// SpecialMapping 特殊表映射配置
type SpecialMapping struct {
	SourceTable    string          `yaml:"source_table" json:"source_table"`
	TargetTable    string          `yaml:"target_table" json:"target_table"`
	MappingColumns []ColumnMapping `yaml:"mapping_columns" json:"mapping_columns"`
}

// ColumnMapping 列映射配置
type ColumnMapping struct {
	Source string `mapstructure:"source" json:"source"`
	Target string `mapstructure:"target" json:"target"`
}

// KafkaConfig Kafka配置
type KafkaConfig struct {
	Enabled       bool     `yaml:"enabled" json:"enabled"`
	Brokers       []string `yaml:"brokers" json:"brokers"`
	Topics        []string `yaml:"topics" json:"topics"`
	ConsumerGroup string   `yaml:"consumer_group" json:"consumer_group"`
	Security      struct {
		TLS struct {
			Enabled bool `yaml:"enabled" json:"enabled"`
		} `yaml:"tls" json:"tls"`
		SASL struct {
			Enabled   bool   `yaml:"enabled" json:"enabled"`
			Mechanism string `yaml:"mechanism" json:"mechanism"`
			Username  string `yaml:"username" json:"username"`
			Password  string `yaml:"password" json:"password"`
		} `yaml:"sasl" json:"sasl"`
	} `yaml:"security" json:"security"`
}

// APIConfig API服务配置
type APIConfig struct {
	Enabled   bool `mapstructure:"enabled" json:"enabled"`
	Port      int  `mapstructure:"port" json:"port"`
	Endpoints []struct {
		Path   string `mapstructure:"path" json:"path"`
		Method string `mapstructure:"method" json:"method"`
	} `mapstructure:"endpoints" json:"endpoints"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level    string `yaml:"level" json:"level"`
	Encoding string `yaml:"encoding" json:"encoding"`
	Dir      string `yaml:"dir" json:"dir"`
	MaxSize  int    `yaml:"maxSize" json:"maxSize"`
	MaxAge   int    `yaml:"maxAge" json:"maxAge"`
	Compress bool   `yaml:"compress" json:"compress"`
}

// LoadConfig 加载配置
func LoadConfig(configFile string) (*Config, error) {
	v := viper.New()
	v.SetConfigFile(configFile)

	// 设置默认值
	v.SetDefault("source_db.driver", "mysql")
	v.SetDefault("source_db.charset", "utf8mb4")
	v.SetDefault("source_db.max_open_conns", 10)
	v.SetDefault("source_db.max_idle_conns", 5)
	v.SetDefault("source_db.conn_max_lifetime", 3600)

	v.SetDefault("target_db.driver", "mysql")
	v.SetDefault("target_db.charset", "utf8mb4")
	v.SetDefault("target_db.max_open_conns", 10)
	v.SetDefault("target_db.max_idle_conns", 5)
	v.SetDefault("target_db.conn_max_lifetime", 3600)

	v.SetDefault("sync_interval", 10)
	v.SetDefault("sync_on_start", true)
	v.SetDefault("max_retries", 3)

	v.SetDefault("api.enabled", false)
	v.SetDefault("api.port", 8090)

	v.SetDefault("log.level", "info")
	v.SetDefault("log.encoding", "json")
	v.SetDefault("log.dir", "logs")
	v.SetDefault("log.maxSize", 100)
	v.SetDefault("log.maxAge", 30)
	v.SetDefault("log.compress", true)

	// 读取配置文件
	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 直接使用手动解析方法，与测试模式一致
	sourceHost := v.GetString("source_db.host")
	sourcePort := v.GetInt("source_db.port")
	sourceUsername := v.GetString("source_db.username")
	sourceDBName := v.GetString("source_db.dbname")

	// 创建手动配置
	config := &Config{
		SourceDB: DatabaseConfig{
			Driver:          v.GetString("source_db.driver"),
			Host:            sourceHost,
			Port:            sourcePort,
			Username:        sourceUsername,
			Password:        v.GetString("source_db.password"),
			DBName:          sourceDBName,
			Charset:         v.GetString("source_db.charset"),
			MaxOpenConns:    v.GetInt("source_db.max_open_conns"),
			MaxIdleConns:    v.GetInt("source_db.max_idle_conns"),
			ConnMaxLifetime: v.GetInt("source_db.conn_max_lifetime"),
		},
		TargetDB: DatabaseConfig{
			Driver:          v.GetString("target_db.driver"),
			Host:            v.GetString("target_db.host"),
			Port:            v.GetInt("target_db.port"),
			Username:        v.GetString("target_db.username"),
			Password:        v.GetString("target_db.password"),
			DBName:          v.GetString("target_db.dbname"),
			Charset:         v.GetString("target_db.charset"),
			MaxOpenConns:    v.GetInt("target_db.max_open_conns"),
			MaxIdleConns:    v.GetInt("target_db.max_idle_conns"),
			ConnMaxLifetime: v.GetInt("target_db.conn_max_lifetime"),
		},
		SyncInterval: v.GetInt("sync_interval"),
		SyncOnStart:  v.GetBool("sync_on_start"),
		MaxRetries:   v.GetInt("max_retries"),
	}

	// 读取表配置
	var tables []TableConfig
	if err := v.UnmarshalKey("tables", &tables); err != nil {
		return nil, fmt.Errorf("解析表配置失败: %w", err)
	}
	config.Tables = tables

	// 从YAML文件读取特殊映射配置
	var specialMappingsFromYAML []struct {
		SourceTable    string `mapstructure:"source_table" json:"source_table"`
		TargetTable    string `mapstructure:"target_table" json:"target_table"`
		PrimaryKey     string `mapstructure:"primary_key" json:"primary_key"`
		MappingColumns []struct {
			Source string `mapstructure:"source" json:"source"`
			Target string `mapstructure:"target" json:"target"`
		} `mapstructure:"mapping_columns" json:"mapping_columns"`
	}

	// 读取special_mappings部分
	if err := v.UnmarshalKey("special_mappings", &specialMappingsFromYAML); err != nil {
		return nil, fmt.Errorf("解析 'special_mappings' 配置失败, 请检查配置文件: %w", err)
	}

	// 将读取的配置转换为SpecialMapping结构
	specialMappings := make([]SpecialMapping, 0, len(specialMappingsFromYAML))
	for i, mapping := range specialMappingsFromYAML {
		if mapping.SourceTable == "" || mapping.TargetTable == "" {
			return nil, fmt.Errorf("'special_mappings' 配置项索引 %d 的 'source_table' 或 'target_table' 不能为空", i)
		}

		// 转换列映射
		columns := make([]ColumnMapping, 0, len(mapping.MappingColumns))
		for _, col := range mapping.MappingColumns {
			if col.Source != "" && col.Target != "" {
				columns = append(columns, ColumnMapping{
					Source: col.Source,
					Target: col.Target,
				})
			}
		}

		if len(columns) == 0 {
			return nil, fmt.Errorf("'special_mappings' 配置项 %s -> %s 的 'mapping_columns' 不能为空或无效", mapping.SourceTable, mapping.TargetTable)
		}

		// 添加到最终配置
		specialMappings = append(specialMappings, SpecialMapping{
			SourceTable:    mapping.SourceTable,
			TargetTable:    mapping.TargetTable,
			MappingColumns: columns,
		})
	}

	// 如果未能成功从配置文件读取，或者没有有效的特殊映射，则报错并停止运行
	if len(specialMappings) == 0 {
		return nil, fmt.Errorf("配置文件中未找到任何有效的 'special_mappings' 配置，或该配置项为空。服务无法在没有此配置的情况下运行，请检查并修复配置文件 'dbsync.yaml'")
	}

	// 打印读取到的特殊映射配置
	fmt.Printf("特殊映射数量: %d\n", len(specialMappings))
	for i, mapping := range specialMappings {
		fmt.Printf("映射[%d]: 源表=%s, 目标表=%s, 列数=%d\n",
			i, mapping.SourceTable, mapping.TargetTable, len(mapping.MappingColumns))
	}

	config.SpecialMappings = specialMappings

	// 读取Kafka配置
	kafkaEnabled := v.GetBool("kafka.enabled")
	if kafkaEnabled {
		kafkaConfig := &KafkaConfig{
			Enabled:       kafkaEnabled,
			Brokers:       v.GetStringSlice("kafka.brokers"),
			Topics:        v.GetStringSlice("kafka.topics"),
			ConsumerGroup: v.GetString("kafka.consumer_group"),
		}

		// 安全配置
		kafkaConfig.Security.TLS.Enabled = v.GetBool("kafka.security.tls.enabled")
		kafkaConfig.Security.SASL.Enabled = v.GetBool("kafka.security.sasl.enabled")
		kafkaConfig.Security.SASL.Mechanism = v.GetString("kafka.security.sasl.mechanism")
		kafkaConfig.Security.SASL.Username = v.GetString("kafka.security.sasl.username")
		kafkaConfig.Security.SASL.Password = v.GetString("kafka.security.sasl.password")

		config.Kafka = kafkaConfig
	}

	// 读取API配置
	apiEnabled := v.GetBool("api.enabled")
	if apiEnabled {
		var endpoints []struct {
			Path   string `mapstructure:"path" json:"path"`
			Method string `mapstructure:"method" json:"method"`
		}
		if err := v.UnmarshalKey("api.endpoints", &endpoints); err != nil {
			return nil, fmt.Errorf("解析API端点配置失败: %w", err)
		}

		config.API = APIConfig{
			Enabled:   apiEnabled,
			Port:      v.GetInt("api.port"),
			Endpoints: endpoints,
		}
	}

	// 读取日志配置
	config.Log = LogConfig{
		Level:    v.GetString("log.level"),
		Encoding: v.GetString("log.encoding"),
		Dir:      v.GetString("log.dir"),
		MaxSize:  v.GetInt("log.maxSize"),
		MaxAge:   v.GetInt("log.maxAge"),
		Compress: v.GetBool("log.compress"),
	}

	// 验证配置
	if err := validateConfig(config); err != nil {
		return nil, err
	}

	return config, nil
}

// LoadConfigWithTestMode 加载测试模式的配置
func LoadConfigWithTestMode(configFile string) (*Config, error) {
	v := viper.New()
	v.SetConfigFile(configFile)

	// 设置默认值
	v.SetDefault("source_db.driver", "mysql")
	v.SetDefault("source_db.host", "localhost")
	v.SetDefault("source_db.port", 3306)
	v.SetDefault("source_db.username", "root")
	v.SetDefault("source_db.password", "password")
	v.SetDefault("source_db.dbname", "dci")
	v.SetDefault("source_db.charset", "utf8mb4")
	v.SetDefault("source_db.max_open_conns", 10)
	v.SetDefault("source_db.max_idle_conns", 5)
	v.SetDefault("source_db.conn_max_lifetime", 3600)

	v.SetDefault("target_db.driver", "mysql")
	v.SetDefault("target_db.host", "localhost")
	v.SetDefault("target_db.port", 3306)
	v.SetDefault("target_db.username", "root")
	v.SetDefault("target_db.password", "password")
	v.SetDefault("target_db.dbname", "dci_monitor")
	v.SetDefault("target_db.charset", "utf8mb4")
	v.SetDefault("target_db.max_open_conns", 10)
	v.SetDefault("target_db.max_idle_conns", 5)
	v.SetDefault("target_db.conn_max_lifetime", 3600)

	v.SetDefault("sync_interval", 10)
	v.SetDefault("sync_on_start", true)
	v.SetDefault("max_retries", 3)

	v.SetDefault("api.enabled", false)
	v.SetDefault("api.port", 8090)

	v.SetDefault("log.level", "info")
	v.SetDefault("log.encoding", "json")
	v.SetDefault("log.dir", "logs")
	v.SetDefault("log.maxSize", 100)
	v.SetDefault("log.maxAge", 30)
	v.SetDefault("log.compress", true)

	// 读取配置文件
	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 手动获取值验证读取是否正确
	sourceHost := v.GetString("source_db.host")
	sourcePort := v.GetInt("source_db.port")
	sourceUsername := v.GetString("source_db.username")
	sourceDBName := v.GetString("source_db.dbname")

	// 创建手动配置
	config := &Config{
		SourceDB: DatabaseConfig{
			Driver:          v.GetString("source_db.driver"),
			Host:            sourceHost,
			Port:            sourcePort,
			Username:        sourceUsername,
			Password:        v.GetString("source_db.password"),
			DBName:          sourceDBName,
			Charset:         v.GetString("source_db.charset"),
			MaxOpenConns:    v.GetInt("source_db.max_open_conns"),
			MaxIdleConns:    v.GetInt("source_db.max_idle_conns"),
			ConnMaxLifetime: v.GetInt("source_db.conn_max_lifetime"),
		},
		TargetDB: DatabaseConfig{
			Driver:          v.GetString("target_db.driver"),
			Host:            v.GetString("target_db.host"),
			Port:            v.GetInt("target_db.port"),
			Username:        v.GetString("target_db.username"),
			Password:        v.GetString("target_db.password"),
			DBName:          v.GetString("target_db.dbname"),
			Charset:         v.GetString("target_db.charset"),
			MaxOpenConns:    v.GetInt("target_db.max_open_conns"),
			MaxIdleConns:    v.GetInt("target_db.max_idle_conns"),
			ConnMaxLifetime: v.GetInt("target_db.conn_max_lifetime"),
		},
		SyncInterval: v.GetInt("sync_interval"),
		SyncOnStart:  v.GetBool("sync_on_start"),
		MaxRetries:   v.GetInt("max_retries"),
		TestMode:     true,
	}

	// 读取表配置
	var tables []TableConfig
	if err := v.UnmarshalKey("tables", &tables); err != nil {
		return nil, fmt.Errorf("解析表配置失败: %w", err)
	}
	config.Tables = tables

	return config, nil
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	// 测试模式下跳过验证
	if config.TestMode {
		return nil
	}

	// 验证源数据库配置
	if config.SourceDB.Host == "" {
		return fmt.Errorf("源数据库主机不能为空")
	}
	if config.SourceDB.Port == 0 {
		return fmt.Errorf("源数据库端口不能为0")
	}
	if config.SourceDB.Username == "" {
		return fmt.Errorf("源数据库用户名不能为空")
	}
	if config.SourceDB.DBName == "" {
		return fmt.Errorf("源数据库名不能为空")
	}

	// 验证目标数据库配置
	if config.TargetDB.Host == "" {
		return fmt.Errorf("目标数据库主机不能为空")
	}
	if config.TargetDB.Port == 0 {
		return fmt.Errorf("目标数据库端口不能为0")
	}
	if config.TargetDB.Username == "" {
		return fmt.Errorf("目标数据库用户名不能为空")
	}
	if config.TargetDB.DBName == "" {
		return fmt.Errorf("目标数据库名不能为空")
	}

	// 验证同步表配置
	if len(config.Tables) == 0 {
		return fmt.Errorf("同步表列表不能为空")
	}

	// 验证Kafka配置
	if config.Kafka != nil && config.Kafka.Enabled {
		if len(config.Kafka.Brokers) == 0 {
			return fmt.Errorf("Kafka代理列表不能为空")
		}
		if len(config.Kafka.Topics) == 0 {
			return fmt.Errorf("Kafka主题列表不能为空")
		}
		if config.Kafka.ConsumerGroup == "" {
			return fmt.Errorf("Kafka消费者组不能为空")
		}
	}

	return nil
}
