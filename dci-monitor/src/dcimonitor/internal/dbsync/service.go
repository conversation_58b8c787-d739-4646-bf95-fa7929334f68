package dbsync

import (
	"database/sql"
	"fmt"
	"strings"
	"sync"
	"time"

	_ "github.com/go-sql-driver/mysql" // 注册MySQL驱动
	"go.uber.org/zap"
)

// DBSyncService 数据库同步服务
type DBSyncService struct {
	sourceDB *sql.DB       // dci数据库连接
	targetDB *sql.DB       // dci_monitor数据库连接
	config   *Config       // 配置信息
	logger   *zap.Logger   // 日志记录器
	mu       sync.RWMutex  // 用于同步状态的互斥锁
	status   SyncStatus    // 同步状态
	running  bool          // 是否正在运行同步任务
	stopChan chan struct{} // 停止通道
}

// NewDBSyncService 创建数据库同步服务
func NewDBSyncService(config *Config, logger *zap.Logger) (*DBSyncService, error) {
	if logger == nil {
		var err error
		logger, err = zap.NewProduction()
		if err != nil {
			return nil, fmt.Errorf("创建日志记录器失败: %w", err)
		}
	}

	// 连接源数据库
	sourceDB, err := sql.Open(config.SourceDB.Driver, config.SourceDB.GetDSN())
	if err != nil {
		return nil, fmt.Errorf("连接源数据库失败: %w", err)
	}

	// 设置连接池参数
	sourceDB.SetMaxOpenConns(config.SourceDB.MaxOpenConns)
	sourceDB.SetMaxIdleConns(config.SourceDB.MaxIdleConns)
	sourceDB.SetConnMaxLifetime(time.Duration(config.SourceDB.ConnMaxLifetime) * time.Second)

	// 测试源数据库连接
	if err := sourceDB.Ping(); err != nil {
		sourceDB.Close()
		return nil, fmt.Errorf("测试源数据库连接失败: %w", err)
	}

	// 连接目标数据库
	targetDB, err := sql.Open(config.TargetDB.Driver, config.TargetDB.GetDSN())
	if err != nil {
		sourceDB.Close()
		return nil, fmt.Errorf("连接目标数据库失败: %w", err)
	}

	// 设置连接池参数
	targetDB.SetMaxOpenConns(config.TargetDB.MaxOpenConns)
	targetDB.SetMaxIdleConns(config.TargetDB.MaxIdleConns)
	targetDB.SetConnMaxLifetime(time.Duration(config.TargetDB.ConnMaxLifetime) * time.Second)

	// 测试目标数据库连接
	if err := targetDB.Ping(); err != nil {
		sourceDB.Close()
		targetDB.Close()
		return nil, fmt.Errorf("测试目标数据库连接失败: %w", err)
	}

	// 初始化同步状态
	tables := make([]string, 0, len(config.Tables))
	for _, table := range config.Tables {
		tables = append(tables, table.Name)
	}

	service := &DBSyncService{
		sourceDB: sourceDB,
		targetDB: targetDB,
		config:   config,
		logger:   logger,
		status: SyncStatus{
			LastSyncTime:     time.Time{},
			TotalSyncCount:   0,
			SuccessSyncCount: 0,
			ErrorSyncCount:   0,
			Tables:           tables,
			LastError:        "",
			IsRunning:        false,
		},
		running:  false,
		stopChan: make(chan struct{}),
	}

	return service, nil
}

// Close 关闭服务
func (s *DBSyncService) Close() error {
	var err1, err2 error
	if s.sourceDB != nil {
		err1 = s.sourceDB.Close()
	}
	if s.targetDB != nil {
		err2 = s.targetDB.Close()
	}

	if err1 != nil {
		return err1
	}
	return err2
}

// GetSourceDB 获取源数据库连接
func (s *DBSyncService) GetSourceDB() *sql.DB {
	return s.sourceDB
}

// GetTargetDB 获取目标数据库连接
func (s *DBSyncService) GetTargetDB() *sql.DB {
	return s.targetDB
}

// SyncAll 同步所有表数据
func (s *DBSyncService) SyncAll() error {
	s.mu.Lock()
	if s.running {
		s.mu.Unlock()
		return fmt.Errorf("同步任务已在运行中")
	}
	s.running = true
	s.status.IsRunning = true
	s.mu.Unlock()

	defer func() {
		s.mu.Lock()
		s.running = false
		s.status.IsRunning = false
		s.mu.Unlock()
	}()

	s.logger.Info("开始全量同步")

	// 同步所有配置的表
	for _, tableConfig := range s.config.Tables {
		s.logger.Info("同步表",
			zap.String("table", tableConfig.Name),
			zap.String("target", tableConfig.Target))

		result, err := s.syncTable(tableConfig.Name, tableConfig.Target, tableConfig.PrimaryKey, "")
		if err != nil {
			s.mu.Lock()
			s.status.ErrorSyncCount++
			s.status.LastError = err.Error()
			s.mu.Unlock()
			s.logger.Error("同步表失败",
				zap.String("table", tableConfig.Name),
				zap.Error(err))
			continue
		}

		s.logger.Info("同步表成功",
			zap.String("table", tableConfig.Name),
			zap.Int("total", result.TotalRows),
			zap.Int("inserted", result.InsertedRows),
			zap.Int("updated", result.UpdatedRows))
	}

	// 处理特殊映射表
	for _, mapping := range s.config.SpecialMappings {
		if mapping.SourceTable == "" || mapping.TargetTable == "" {
			s.logger.Error("特殊映射表配置错误，源表或目标表为空")
			continue
		}

		s.logger.Info("处理特殊映射",
			zap.String("source", mapping.SourceTable),
			zap.String("target", mapping.TargetTable))

		result, err := s.syncSpecialTable(mapping, "")
		if err != nil {
			s.mu.Lock()
			s.status.ErrorSyncCount++
			s.status.LastError = err.Error()
			s.mu.Unlock()
			s.logger.Error("同步特殊映射表失败",
				zap.String("source", mapping.SourceTable),
				zap.String("target", mapping.TargetTable),
				zap.Error(err))
			continue
		}

		s.logger.Info("同步特殊映射表成功",
			zap.String("source", mapping.SourceTable),
			zap.String("target", mapping.TargetTable),
			zap.Int("total", result.TotalRows),
			zap.Int("inserted", result.InsertedRows),
			zap.Int("updated", result.UpdatedRows))
	}

	// 更新同步状态
	s.mu.Lock()
	s.status.LastSyncTime = time.Now()
	s.status.TotalSyncCount++
	s.status.SuccessSyncCount++
	s.mu.Unlock()

	s.logger.Info("全量同步完成")
	return nil
}

// SyncIncremental 增量同步
func (s *DBSyncService) SyncIncremental() error {
	s.mu.Lock()
	if s.running {
		s.mu.Unlock()
		return fmt.Errorf("同步任务已在运行中")
	}
	s.running = true
	s.status.IsRunning = true

	// 获取上次同步时间
	var whereClause string
	if !s.status.LastSyncTime.IsZero() {
		whereClause = fmt.Sprintf("WHERE update_time > '%s'", s.status.LastSyncTime.Format("2006-01-02 15:04:05"))
	}
	s.mu.Unlock()

	defer func() {
		s.mu.Lock()
		s.running = false
		s.status.IsRunning = false
		s.mu.Unlock()
	}()

	s.logger.Info("开始增量同步", zap.String("since", whereClause))

	// 同步所有配置的表
	for _, tableConfig := range s.config.Tables {
		s.logger.Info("增量同步表",
			zap.String("table", tableConfig.Name),
			zap.String("target", tableConfig.Target),
			zap.String("where", whereClause))

		result, err := s.syncTable(tableConfig.Name, tableConfig.Target, tableConfig.PrimaryKey, whereClause)
		if err != nil {
			s.mu.Lock()
			s.status.ErrorSyncCount++
			s.status.LastError = err.Error()
			s.mu.Unlock()
			s.logger.Error("增量同步表失败",
				zap.String("table", tableConfig.Name),
				zap.Error(err))
			continue
		}

		if result.TotalRows > 0 {
			s.logger.Info("增量同步表成功",
				zap.String("table", tableConfig.Name),
				zap.Int("total", result.TotalRows),
				zap.Int("inserted", result.InsertedRows),
				zap.Int("updated", result.UpdatedRows))
		} else {
			s.logger.Debug("增量同步表无变化",
				zap.String("table", tableConfig.Name))
		}
	}

	// 处理特殊映射表
	for _, mapping := range s.config.SpecialMappings {
		if mapping.SourceTable == "" || mapping.TargetTable == "" {
			s.logger.Error("特殊映射表配置错误，源表或目标表为空")
			continue
		}

		s.logger.Info("增量处理特殊映射",
			zap.String("source", mapping.SourceTable),
			zap.String("target", mapping.TargetTable),
			zap.String("where", whereClause))

		result, err := s.syncSpecialTable(mapping, whereClause)
		if err != nil {
			s.mu.Lock()
			s.status.ErrorSyncCount++
			s.status.LastError = err.Error()
			s.mu.Unlock()
			s.logger.Error("增量同步特殊映射表失败",
				zap.String("source", mapping.SourceTable),
				zap.String("target", mapping.TargetTable),
				zap.Error(err))
			continue
		}

		if result.TotalRows > 0 {
			s.logger.Info("增量同步特殊映射表成功",
				zap.String("source", mapping.SourceTable),
				zap.String("target", mapping.TargetTable),
				zap.Int("total", result.TotalRows),
				zap.Int("inserted", result.InsertedRows),
				zap.Int("updated", result.UpdatedRows))
		} else {
			s.logger.Debug("增量同步特殊映射表无变化",
				zap.String("source", mapping.SourceTable),
				zap.String("target", mapping.TargetTable))
		}
	}

	// 更新同步状态
	s.mu.Lock()
	s.status.LastSyncTime = time.Now()
	s.status.TotalSyncCount++
	s.status.SuccessSyncCount++
	s.mu.Unlock()

	s.logger.Info("增量同步完成")
	return nil
}

// syncTable 同步单个表
func (s *DBSyncService) syncTable(sourceTable, targetTable, primaryKey, whereClause string) (*SyncResult, error) {
	result := &SyncResult{
		TableName: sourceTable,
		Success:   false,
	}

	// 1. 获取源表结构
	sourceColumns, err := s.getTableColumns(s.sourceDB, sourceTable)
	if err != nil {
		return result, fmt.Errorf("获取源表结构失败: %w", err)
	}

	// 2. 获取目标表结构
	targetColumns, err := s.getTableColumns(s.targetDB, targetTable)
	if err != nil {
		return result, fmt.Errorf("获取目标表结构失败: %w", err)
	}

	// 3. 找出两个表的共同列
	commonColumns := s.getCommonColumns(sourceColumns, targetColumns)
	if len(commonColumns) == 0 {
		return result, fmt.Errorf("源表和目标表没有共同的列")
	}

	// 4. 构建查询SQL，只查询目标表中存在的列
	query := fmt.Sprintf("SELECT %s FROM %s %s", strings.Join(commonColumns, ", "), sourceTable, whereClause)
	s.logger.Debug("执行查询", zap.String("query", query))

	// 5. 查询数据
	rows, err := s.sourceDB.Query(query)
	if err != nil {
		return result, fmt.Errorf("查询数据失败: %w", err)
	}
	defer rows.Close()

	// 6. 开启事务
	tx, err := s.targetDB.Begin()
	if err != nil {
		return result, fmt.Errorf("开启事务失败: %w", err)
	}
	defer func() {
		if !result.Success {
			tx.Rollback()
		}
	}()

	// 7. 准备占位符
	placeholders := strings.Repeat("?, ", len(commonColumns))
	placeholders = placeholders[:len(placeholders)-2] // 去掉最后的", "

	// 8. 构建列更新部分
	updateParts := make([]string, 0, len(commonColumns))
	for _, col := range commonColumns {
		if col != primaryKey && col != "create_time" {
			updateParts = append(updateParts, fmt.Sprintf("%s = VALUES(%s)", col, col))
		}
	}
	updateClause := ""
	if len(updateParts) > 0 {
		updateClause = strings.Join(updateParts, ", ")
	} else {
		// 如果没有可更新的列，至少更新一个主键字段本身
		updateClause = fmt.Sprintf("%s = VALUES(%s)", primaryKey, primaryKey)
	}

	// 9. 构建插入或更新SQL
	insertSQL := fmt.Sprintf(
		"INSERT INTO %s (%s) VALUES (%s) ON DUPLICATE KEY UPDATE %s",
		targetTable,
		strings.Join(commonColumns, ", "),
		placeholders,
		updateClause,
	)
	s.logger.Debug("准备执行插入", zap.String("sql", insertSQL))

	// 10. 准备插入语句
	stmt, err := tx.Prepare(insertSQL)
	if err != nil {
		return result, fmt.Errorf("准备SQL语句失败: %w", err)
	}
	defer stmt.Close()

	// 11. 遍历数据行
	for rows.Next() {
		result.TotalRows++

		// 11.1 准备扫描目标
		scanArgs := make([]interface{}, len(commonColumns))
		values := make([]interface{}, len(commonColumns))
		for i := range values {
			scanArgs[i] = &values[i]
		}

		// 11.2 扫描数据
		if err := rows.Scan(scanArgs...); err != nil {
			result.ErrorRows++
			s.logger.Error("扫描数据行失败",
				zap.String("table", sourceTable),
				zap.Error(err))
			continue
		}

		// 11.3 处理可能的nil值
		for i := range values {
			if values[i] == nil {
				values[i] = sql.NullString{}
			}
		}

		// 11.4 执行插入或更新
		_, err := stmt.Exec(values...)
		if err != nil {
			result.ErrorRows++
			s.logger.Error("执行插入或更新失败",
				zap.String("table", targetTable),
				zap.Error(err))
			continue
		}

		// 判断是插入还是更新（简化处理，实际上无法精确判断）
		result.InsertedRows++
	}

	// 检查遍历错误
	if err := rows.Err(); err != nil {
		return result, fmt.Errorf("遍历数据行时发生错误: %w", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return result, fmt.Errorf("提交事务失败: %w", err)
	}

	result.Success = true
	return result, nil
}

// getCommonColumns 获取两个列列表的交集
func (s *DBSyncService) getCommonColumns(sourceColumns, targetColumns []string) []string {
	// 创建目标列的映射，便于查找
	targetMap := make(map[string]struct{}, len(targetColumns))
	for _, col := range targetColumns {
		targetMap[col] = struct{}{}
	}

	// 找出共同的列
	var commonColumns []string
	for _, col := range sourceColumns {
		if _, exists := targetMap[col]; exists {
			commonColumns = append(commonColumns, col)
		}
	}

	return commonColumns
}

// syncSpecialTable 同步特殊映射表
func (s *DBSyncService) syncSpecialTable(mapping SpecialMapping, whereClause string) (*SyncResult, error) {
	result := &SyncResult{
		TableName: mapping.SourceTable,
		Success:   false,
	}

	// 检查源表和目标表是否为空
	if mapping.SourceTable == "" || mapping.TargetTable == "" {
		return result, fmt.Errorf("源表或目标表名称为空")
	}

	// 构建映射关系
	sourceToTarget := make(map[string]string)
	columnNames := make([]string, 0, len(mapping.MappingColumns))
	targetColumns := make([]string, 0, len(mapping.MappingColumns))

	for _, col := range mapping.MappingColumns {
		sourceToTarget[col.Source] = col.Target
		columnNames = append(columnNames, col.Source)
		targetColumns = append(targetColumns, col.Target)
	}

	// 对于dci_logic_port_device到dci_logic_port_device的特殊处理
	if mapping.SourceTable == "dci_logic_port_device" && mapping.TargetTable == "dci_logic_port_device" {
		// 查询包含设备信息的SQL
		query := fmt.Sprintf(`
			SELECT d.id AS device_id, d.device_name, d.device_management_ip,
				   p.id, p.device_id, p.logic_port_id, p.port, p.physical_port_state, p.description,
				   p.create_time, p.update_time
			FROM %s p
			JOIN dci_device d ON p.device_id = d.id
			%s
		`, mapping.SourceTable, whereClause)

		s.logger.Debug("执行查询", zap.String("query", query))

		// 查询数据
		rows, err := s.sourceDB.Query(query)
		if err != nil {
			return result, fmt.Errorf("查询数据失败: %w", err)
		}
		defer rows.Close()

		// 开启事务
		tx, err := s.targetDB.Begin()
		if err != nil {
			return result, fmt.Errorf("开启事务失败: %w", err)
		}
		defer func() {
			if !result.Success {
				tx.Rollback()
			}
		}()

		// 准备插入语句
		insertSQL := `
			INSERT INTO dci_logic_port_device (
				id, device_id, logic_port_id, port, physical_port_state, description, create_time, update_time
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
			ON DUPLICATE KEY UPDATE
				device_id = VALUES(device_id),
				logic_port_id = VALUES(logic_port_id),
				port = VALUES(port),
				physical_port_state = VALUES(physical_port_state),
				description = VALUES(description),
				update_time = VALUES(update_time)
		`

		stmt, err := tx.Prepare(insertSQL)
		if err != nil {
			return result, fmt.Errorf("准备SQL语句失败: %w", err)
		}
		defer stmt.Close()

		// 遍历数据行
		for rows.Next() {
			result.TotalRows++

			var (
				deviceID, deviceName, deviceIP                     string
				id, portDeviceID, portName, portState, description string
				logicPortID                                        sql.NullInt64
				createTime, updateTime                             time.Time
			)

			// 扫描数据
			if err := rows.Scan(
				&deviceID, &deviceName, &deviceIP,
				&id, &portDeviceID, &logicPortID, &portName, &portState, &description,
				&createTime, &updateTime,
			); err != nil {
				result.ErrorRows++
				s.logger.Error("扫描数据行失败", zap.Error(err))
				continue
			}

			// 执行插入或更新
			_, err := stmt.Exec(
				id, portDeviceID, logicPortID, portName, portState, description, createTime, updateTime,
			)
			if err != nil {
				result.ErrorRows++
				s.logger.Error("执行插入或更新失败", zap.Error(err))
				continue
			}

			result.InsertedRows++
		}

		// 检查遍历错误
		if err := rows.Err(); err != nil {
			return result, fmt.Errorf("遍历数据行时发生错误: %w", err)
		}

		// 提交事务
		if err := tx.Commit(); err != nil {
			return result, fmt.Errorf("提交事务失败: %w", err)
		}

		result.Success = true
		return result, nil
	}

	// 如果不是特殊处理的表，使用通用方法
	return s.syncTable(mapping.SourceTable, mapping.TargetTable, "id", whereClause)
}

// getTableColumns 获取表的列名
func (s *DBSyncService) getTableColumns(db *sql.DB, tableName string) ([]string, error) {
	// 查询表结构
	query := fmt.Sprintf("SHOW COLUMNS FROM %s", tableName)
	rows, err := db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询表结构失败: %w", err)
	}
	defer rows.Close()

	// 提取列名
	var columns []string
	for rows.Next() {
		var field, fieldType, null, key, extra string
		var defaultValue sql.NullString
		if err := rows.Scan(&field, &fieldType, &null, &key, &defaultValue, &extra); err != nil {
			return nil, fmt.Errorf("扫描列信息失败: %w", err)
		}
		columns = append(columns, field)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历列信息时发生错误: %w", err)
	}

	return columns, nil
}

// UpdateInterfaceIndex 更新接口索引映射
func (s *DBSyncService) UpdateInterfaceIndex(deviceIP, portName, ifIndex string) error {
	// 先根据设备IP查询设备ID
	var deviceID string
	query := "SELECT id FROM dci_device WHERE device_management_ip = ?"
	err := s.targetDB.QueryRow(query, deviceIP).Scan(&deviceID)
	if err != nil {
		if err == sql.ErrNoRows {
			return fmt.Errorf("未找到设备记录: %s", deviceIP)
		}
		return fmt.Errorf("查询设备ID失败: %w", err)
	}

	// 更新端口的接口索引
	updateQuery := "UPDATE dci_logic_port_device SET if_index = ? WHERE device_id = ? AND port = ?"
	result, err := s.targetDB.Exec(updateQuery, ifIndex, deviceID, portName)
	if err != nil {
		return fmt.Errorf("更新接口索引失败: %w", err)
	}

	// 检查是否更新了记录
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		// 如果没有更新记录，可能是端口记录不存在，尝试插入
		s.logger.Debug("未找到端口记录，尝试创建",
			zap.String("device_ip", deviceIP),
			zap.String("device_id", deviceID),
			zap.String("port_name", portName),
			zap.String("if_index", ifIndex))

		// 生成唯一ID
		var maxID int
		err := s.targetDB.QueryRow("SELECT MAX(id) FROM dci_logic_port_device").Scan(&maxID)
		if err != nil && err != sql.ErrNoRows {
			return fmt.Errorf("获取最大ID失败: %w", err)
		}
		newID := maxID + 1

		// 插入新记录
		insertQuery := `
			INSERT INTO dci_logic_port_device (id, device_id, port, if_index, create_time, update_time)
			VALUES (?, ?, ?, ?, NOW(), NOW())
		`
		_, err = s.targetDB.Exec(insertQuery, newID, deviceID, portName, ifIndex)
		if err != nil {
			return fmt.Errorf("插入端口记录失败: %w", err)
		}

		s.logger.Info("创建新端口记录",
			zap.String("device_ip", deviceIP),
			zap.String("device_id", deviceID),
			zap.String("port_name", portName),
			zap.String("if_index", ifIndex),
			zap.Int("port_id", newID))
	} else {
		s.logger.Debug("更新接口索引成功",
			zap.String("device_ip", deviceIP),
			zap.String("port_name", portName),
			zap.String("if_index", ifIndex),
			zap.Int64("rows_affected", rowsAffected))
	}

	return nil
}

// GetPortMapping 获取端口映射信息
func (s *DBSyncService) GetPortMapping(deviceIP, ifIndex string) (*PortMapping, error) {
	query := `
		SELECT
			d.id as device_id, 
			d.device_name as device_name, 
			d.device_management_ip as device_ip,
			p.id as port_id, 
			p.port as port_name, 
			p.if_index as if_index,
			v.vni_id as vni_id
		FROM 
			dci_device d
		JOIN 
			dci_port p ON d.id = p.device_id
		LEFT JOIN 
			dci_node n ON d.id = n.device_id
		LEFT JOIN 
			dci_node_business nb ON n.id = nb.node_id
		LEFT JOIN 
			dci_vni v ON nb.vni = v.vni_id
		WHERE 
			d.device_management_ip = ? AND p.if_index = ?
		LIMIT 1
	`

	var mapping PortMapping
	err := s.targetDB.QueryRow(query, deviceIP, ifIndex).Scan(
		&mapping.DeviceID,
		&mapping.DeviceName,
		&mapping.DeviceIP,
		&mapping.PortID,
		&mapping.PortName,
		&mapping.IfIndex,
		&mapping.VNIID,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("查询端口映射失败: %w", err)
	}

	return &mapping, nil
}

// GetSyncStatus 获取同步状态
func (s *DBSyncService) GetSyncStatus() SyncStatus {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.status
}
