package dbsync

import (
	"time"
)

// Device 设备信息
type Device struct {
	ID           string    `db:"id"`
	DeviceName   string    `db:"device_name"`
	DeviceIP     string    `db:"device_management_ip"`
	DeviceModel  string    `db:"device_model"`
	DeviceType   string    `db:"device_type"`
	DeviceStatus string    `db:"device_status"`
	CreateTime   time.Time `db:"create_time"`
	UpdateTime   time.Time `db:"update_time"`
}

// Port 端口信息
type Port struct {
	ID                string    `db:"id"`
	DeviceID          string    `db:"device_id"`
	PortName          string    `db:"port"`
	IfIndex           string    `db:"if_index"`
	PhysicalPortState string    `db:"physical_port_state"`
	Description       string    `db:"description"`
	CreateTime        time.Time `db:"create_time"`
	UpdateTime        time.Time `db:"update_time"`
}

// Node 节点信息
type Node struct {
	ID          string    `db:"id"`
	RoomID      string    `db:"room_id"`
	DeviceID    string    `db:"device_id"`
	Name        string    `db:"name"`
	State       string    `db:"state"`
	CabinetNo   string    `db:"cabinet_no"`
	Description string    `db:"description"`
	CreateTime  time.Time `db:"create_time"`
	UpdateTime  time.Time `db:"update_time"`
}

// NodeBusiness 节点业务关系
type NodeBusiness struct {
	ID          string    `db:"id"`
	RoomID      string    `db:"room_id"`
	NodeID      string    `db:"node_id"`
	LogicPortID string    `db:"logic_port_id"`
	VNI         string    `db:"vni"`
	Bandwidth   string    `db:"bandwidth"`
	CreateTime  time.Time `db:"create_time"`
	UpdateTime  time.Time `db:"update_time"`
}

// VNI 虚拟网络标识符
type VNI struct {
	ID           string    `db:"id"`
	VNIID        string    `db:"vni_id"`
	CircuitNo    string    `db:"circuit_no"`
	AssignStatus string    `db:"assign_status"`
	Remarks      string    `db:"remarks"`
	CreateTime   time.Time `db:"create_time"`
	UpdateTime   time.Time `db:"update_time"`
}

// LogicPort 逻辑端口
type LogicPort struct {
	ID          string    `db:"id"`
	Name        string    `db:"name"`
	PortState   string    `db:"port_state"`
	CreateState string    `db:"create_state"`
	RoomID      string    `db:"room_id"`
	NodeID      string    `db:"node_id"`
	PortCount   int       `db:"port_count"`
	ConnectType string    `db:"connect_type"`
	Description string    `db:"description"`
	CreateTime  time.Time `db:"create_time"`
	UpdateTime  time.Time `db:"update_time"`
}

// InterfaceInfo 接口索引信息
type InterfaceInfo struct {
	DeviceIP  string `json:"device_ip"`
	IfIndex   string `json:"ifIndex"`
	IfName    string `json:"ifName"`
	Timestamp int64  `json:"timestamp"`
}

// SyncStatus 同步状态
type SyncStatus struct {
	LastSyncTime     time.Time `json:"last_sync_time"`
	TotalSyncCount   int64     `json:"total_sync_count"`
	SuccessSyncCount int64     `json:"success_sync_count"`
	ErrorSyncCount   int64     `json:"error_sync_count"`
	Tables           []string  `json:"tables"`
	LastError        string    `json:"last_error"`
	IsRunning        bool      `json:"is_running"`
}

// PortMapping 端口映射
type PortMapping struct {
	DeviceID   string `json:"device_id"`
	DeviceIP   string `json:"device_ip"`
	DeviceName string `json:"device_name"`
	IfIndex    string `json:"if_index"`
	PortID     string `json:"port_id"`
	PortName   string `json:"port_name"`
	VNIID      string `json:"vni_id"`
	VNIName    string `json:"vni_name"`
}

// InterfaceMapping 接口映射信息
type InterfaceMapping struct {
	DeviceIP string `json:"device_ip"`
	PortName string `json:"port_name"`
	IfIndex  string `json:"if_index"`
	VNIID    string `json:"vni_id"`
}

// SyncResult 同步结果
type SyncResult struct {
	TableName    string `json:"table_name"`
	TotalRows    int    `json:"total_rows"`
	InsertedRows int    `json:"inserted_rows"`
	UpdatedRows  int    `json:"updated_rows"`
	ErrorRows    int    `json:"error_rows"`
	Success      bool   `json:"success"`
	ErrorMessage string `json:"error_message"`
}

// APIResponse API响应
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// TelegrafMessage Telegraf消息
type TelegrafMessage struct {
	Fields struct {
		IfName string `json:"ifName"`
	} `json:"fields"`
	Tags struct {
		DeviceIP string `json:"device_ip"`
		IfIndex  string `json:"ifIndex"`
	} `json:"tags"`
	Name      string `json:"name"`
	Timestamp int64  `json:"timestamp"`
}
