package dbsync

import (
	"context"
	"sync"
	"time"

	"go.uber.org/zap"
)

// SyncScheduler 数据库同步调度器
type SyncScheduler struct {
	config      *Config
	syncService *DBSyncService
	logger      *zap.Logger
	stopChan    chan struct{}
	wg          sync.WaitGroup
	running     bool
	mu          sync.Mutex
}

// NewSyncScheduler 创建数据库同步调度器
func NewSyncScheduler(config *Config, syncService *DBSyncService, logger *zap.Logger) *SyncScheduler {
	if logger == nil {
		logger, _ = zap.NewProduction()
	}

	return &SyncScheduler{
		config:      config,
		syncService: syncService,
		logger:      logger,
		stopChan:    make(chan struct{}),
		running:     false,
	}
}

// Start 启动调度器
func (s *SyncScheduler) Start(ctx context.Context) error {
	s.mu.Lock()
	if s.running {
		s.mu.Unlock()
		return nil
	}
	s.running = true
	s.mu.Unlock()

	s.logger.Info("启动数据库同步调度器",
		zap.Int("sync_interval", s.config.SyncInterval),
		zap.Bool("sync_on_start", s.config.SyncOnStart))

	s.wg.Add(1)
	go func() {
		defer s.wg.Done()

		// 如果配置了启动时同步，先执行一次全量同步
		if s.config.SyncOnStart {
			s.logger.Info("执行启动时同步")
			if err := s.syncService.SyncAll(); err != nil {
				s.logger.Error("启动时同步失败", zap.Error(err))
			}
		}

		// 如果同步间隔为0，则不启动定时同步
		if s.config.SyncInterval <= 0 {
			s.logger.Info("同步间隔为0，不启动定时同步")
			return
		}

		// 创建定时器
		ticker := time.NewTicker(time.Duration(s.config.SyncInterval) * time.Minute)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				// 执行增量同步
				s.logger.Info("执行定时增量同步")
				if err := s.syncService.SyncIncremental(); err != nil {
					s.logger.Error("增量同步失败", zap.Error(err))
				}

			case <-ctx.Done():
				s.logger.Info("上下文取消，停止调度器")
				return

			case <-s.stopChan:
				s.logger.Info("接收到停止信号，停止调度器")
				return
			}
		}
	}()

	return nil
}

// Stop 停止调度器
func (s *SyncScheduler) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return
	}

	s.logger.Info("停止数据库同步调度器")
	close(s.stopChan)
	s.wg.Wait()
	s.running = false
}

// TriggerSync 手动触发同步
func (s *SyncScheduler) TriggerSync(isFullSync bool) error {
	s.logger.Info("手动触发同步", zap.Bool("is_full_sync", isFullSync))

	if isFullSync {
		return s.syncService.SyncAll()
	}
	return s.syncService.SyncIncremental()
}

// IsRunning 检查调度器是否运行中
func (s *SyncScheduler) IsRunning() bool {
	s.mu.Lock()
	defer s.mu.Unlock()
	return s.running
}
