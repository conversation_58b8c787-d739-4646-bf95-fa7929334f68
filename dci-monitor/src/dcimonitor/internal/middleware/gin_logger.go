package middleware

import (
	"net"
	"net/http"
	"os"
	"strings"
	"time"

	"common/logger"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// 定义一个白名单列表，包含不需要记录日志的路径
var logSkipPaths = map[string]bool{
	"/health": true,
	// 可以在此添加更多需要屏蔽日志的路径
	// "/metrics": true,
	// "/ready": true,
}

// LogSkipper 判断是否跳过日志记录
func LogSkipper(path string) bool {
	return logSkipPaths[path]
}

// AddLogSkipPath 添加需要跳过日志记录的路径
func AddLogSkipPath(path string) {
	logSkipPaths[path] = true
}

// GinLogger 返回一个 Gin 中间件，用于记录请求日志
func GinLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		query := c.Request.URL.RawQuery

		// 处理请求
		c.Next()

		// 检查是否在白名单中，如果是则跳过日志记录
		if LogSkipper(path) {
			return
		}

		cost := time.Since(start)

		requestID := GetRequestID(c)

		logger.Info(path,
			zap.Int("status", c.Writer.Status()),
			zap.String("method", c.Request.Method),
			zap.String("path", path),
			zap.String("query", query),
			zap.String("ip", c.ClientIP()),
			zap.String("user-agent", c.Request.UserAgent()),
			zap.String("errors", c.Errors.ByType(gin.ErrorTypePrivate).String()),
			zap.Duration("cost", cost),
			zap.Any("requestID", requestID),
		)
	}
}

// GinRecovery 返回一个 Gin 中间件，用于恢复 panic 并记录错误日志
func GinRecovery(stack bool) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// Check for a broken connection, as it is not really a
				// condition that warrants a panic stack trace.
				var brokenPipe bool
				if ne, ok := err.(*net.OpError); ok {
					if se, ok := ne.Err.(*os.SyscallError); ok {
						if strings.Contains(strings.ToLower(se.Error()), "broken pipe") || strings.Contains(strings.ToLower(se.Error()), "connection reset by peer") {
							brokenPipe = true
						}
					}
				}

				requestID := GetRequestID(c)

				if brokenPipe {
					logger.Error(c.Request.URL.Path,
						zap.Any("error", err),
						zap.String("request", c.Request.Method+" "+c.Request.URL.Path),
						zap.String("requestID", requestID),
					)
					// If the connection is dead, we can't write a status to it.
					c.Error(err.(error)) // nolint: errcheck
					c.Abort()
					return
				}

				if stack {
					logger.Error("[Recovery from panic]",
						zap.Time("time", time.Now()),
						zap.Any("error", err),
						zap.String("request", c.Request.Method+" "+c.Request.URL.Path),
						zap.String("ip", c.ClientIP()),
						zap.String("user-agent", c.Request.UserAgent()),
						zap.String("requestID", requestID),
						zap.Stack("stack"),
					)
				} else {
					logger.Error("[Recovery from panic]",
						zap.Time("time", time.Now()),
						zap.Any("error", err),
						zap.String("request", c.Request.Method+" "+c.Request.URL.Path),
						zap.String("requestID", requestID),
					)
				}
				c.AbortWithStatus(http.StatusInternalServerError)
			}
		}()
		c.Next()
	}
}
