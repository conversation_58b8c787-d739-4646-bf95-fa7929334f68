package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// RequestIDMiddleware 为每个请求生成唯一的请求ID
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 优先使用请求头中的ID
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = uuid.New().String()
		}

		// 在Context中设置请求ID
		c.Set("X-Request-ID", requestID)
		// 同时设置旧键名以兼容现有代码
		c.Set("requestID", requestID)

		// 在响应头中设置请求ID
		c.Header("X-Request-ID", requestID)

		c.Next()
	}
}

// GetRequestID 从Context中获取请求ID，如果不存在则生成新ID
func GetRequestID(c *gin.Context) string {
	// 优先尝试从X-Request-ID获取
	reqID, exists := c.Get("X-Request-ID")
	if exists && reqID != nil {
		if id, ok := reqID.(string); ok {
			return id
		}
	}

	// 尝试从旧键名获取
	reqID, exists = c.Get("requestID")
	if exists && reqID != nil {
		if id, ok := reqID.(string); ok {
			return id
		}
	}

	// 生成新ID
	newID := uuid.New().String()
	c.Set("X-Request-ID", newID)
	c.Set("requestID", newID)
	return newID
}
