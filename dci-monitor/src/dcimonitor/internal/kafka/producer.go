package kafka

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"common/logger"

	"github.com/IBM/sarama"
	"go.uber.org/zap"
)

// ProducerConfig 定义Kafka生产者配置
type ProducerConfig struct {
	Brokers       []string                // Kafka服务器地址列表
	RequiredAcks  sarama.RequiredAcks     // 消息确认机制: 0=不等待确认, 1=等待leader确认, -1=等待所有副本确认
	Compression   sarama.CompressionCodec // 压缩编解码器: 0=none, 1=gzip, 2=snappy, 3=lz4, 4=zstd
	MaxRetries    int                     // 最大重试次数
	RetryInterval time.Duration           // 重试间隔
	FlushInterval time.Duration           // 批量发送间隔（异步模式）
	FlushBytes    int                     // 批量发送大小阈值（异步模式）
	FlushMessages int                     // 批量发送消息数阈值（异步模式）
	TLS           *KafkaTLSConfig         // TLS配置（可选）
	SASL          *KafkaSASLConfig        // SASL配置（可选）
}

// Producer 定义Kafka生产者结构
type Producer struct {
	config        *ProducerConfig
	saramaConfig  *sarama.Config
	asyncProducer sarama.AsyncProducer
	syncProducer  sarama.SyncProducer
	isAsync       bool
	wg            sync.WaitGroup
	stopChan      chan struct{}
	sendCount     int64
	sendErrors    int64
	mutex         sync.Mutex
}

// NewAsyncProducer 创建新的异步Kafka生产者
func NewAsyncProducer(config *ProducerConfig) (*Producer, error) {
	producer, err := newProducer(config, true)
	if err != nil {
		return nil, err
	}
	return producer, nil
}

// NewSyncProducer 创建新的同步Kafka生产者
func NewSyncProducer(config *ProducerConfig) (*Producer, error) {
	producer, err := newProducer(config, false)
	if err != nil {
		return nil, err
	}
	return producer, nil
}

// newProducer 创建新的Kafka生产者（内部方法）
func newProducer(config *ProducerConfig, isAsync bool) (*Producer, error) {
	if config == nil {
		return nil, errors.New("producer config cannot be nil")
	}

	if len(config.Brokers) == 0 {
		return nil, errors.New("brokers list cannot be empty")
	}

	// 创建Sarama配置
	saramaConfig := sarama.NewConfig()
	saramaConfig.Version = sarama.V2_8_0_0 // 设置Kafka版本，根据实际环境调整

	// 配置生产者行为
	saramaConfig.Producer.Return.Successes = true
	saramaConfig.Producer.Return.Errors = true
	saramaConfig.Producer.RequiredAcks = config.RequiredAcks
	if saramaConfig.Producer.RequiredAcks == 0 {
		saramaConfig.Producer.RequiredAcks = sarama.WaitForLocal // 默认等待leader确认
	}
	saramaConfig.Producer.Compression = config.Compression
	saramaConfig.Producer.Retry.Max = config.MaxRetries
	saramaConfig.Producer.Retry.Backoff = config.RetryInterval

	// 配置异步生产者的批处理参数
	if isAsync {
		if config.FlushInterval > 0 {
			saramaConfig.Producer.Flush.Frequency = config.FlushInterval
		}
		if config.FlushBytes > 0 {
			saramaConfig.Producer.Flush.Bytes = config.FlushBytes
		}
		if config.FlushMessages > 0 {
			saramaConfig.Producer.Flush.Messages = config.FlushMessages
		}
	}

	// 配置TLS（如果启用）
	if config.TLS != nil && config.TLS.Enabled {
		tlsConfig, err := NewTLSConfig(config.TLS)
		if err != nil {
			return nil, fmt.Errorf("failed to create TLS config: %w", err)
		}
		saramaConfig.Net.TLS.Enable = true
		saramaConfig.Net.TLS.Config = tlsConfig
	}

	// 配置SASL（如果启用）
	if config.SASL != nil && config.SASL.Enabled {
		saramaConfig.Net.SASL.Enable = true
		saramaConfig.Net.SASL.Mechanism = sarama.SASLMechanism(config.SASL.Mechanism)
		saramaConfig.Net.SASL.User = config.SASL.Username
		saramaConfig.Net.SASL.Password = config.SASL.Password
	}

	// 确保获取完整的元数据，以便客户端能够正确路由到各个Broker
	saramaConfig.Metadata.Full = true

	producer := &Producer{
		config:       config,
		saramaConfig: saramaConfig,
		isAsync:      isAsync,
		stopChan:     make(chan struct{}),
	}

	return producer, nil
}

// Start 启动生产者
func (p *Producer) Start() error {
	var err error

	if p.isAsync {
		// 创建异步生产者
		p.asyncProducer, err = sarama.NewAsyncProducer(p.config.Brokers, p.saramaConfig)
		if err != nil {
			return fmt.Errorf("failed to create async producer: %w", err)
		}

		// 启动消息处理协程
		p.wg.Add(2)
		go p.handleSuccesses()
		go p.handleErrors()
	} else {
		// 创建同步生产者
		p.syncProducer, err = sarama.NewSyncProducer(p.config.Brokers, p.saramaConfig)
		if err != nil {
			return fmt.Errorf("failed to create sync producer: %w", err)
		}
	}

	logger.Info("Kafka producer started",
		zap.Bool("isAsync", p.isAsync),
		zap.Strings("brokers", p.config.Brokers))

	return nil
}

// Stop 停止生产者
func (p *Producer) Stop() error {
	logger.Info("Stopping Kafka producer")

	// 关闭停止通道
	close(p.stopChan)

	// 创建一个带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 创建一个通道用于等待关闭完成
	done := make(chan struct{})

	go func() {
		// 关闭生产者
		var err error
		if p.isAsync && p.asyncProducer != nil {
			err = p.asyncProducer.Close()
		} else if !p.isAsync && p.syncProducer != nil {
			err = p.syncProducer.Close()
		}

		if err != nil {
			logger.Error("Failed to close producer", zap.Error(err))
		}

		// 等待所有协程结束
		p.wg.Wait()

		close(done)
	}()

	// 等待关闭完成或超时
	select {
	case <-done:
		logger.Info("Producer stopped gracefully")
	case <-ctx.Done():
		logger.Warn("Producer stop timed out, forcing shutdown")
	}

	logger.Info("Kafka producer stopped")
	return nil
}

// SendMessage 发送消息到指定主题
func (p *Producer) SendMessage(ctx context.Context, topic string, key, value []byte, headers []sarama.RecordHeader) error {
	msg := &sarama.ProducerMessage{
		Topic:   topic,
		Key:     sarama.ByteEncoder(key),
		Value:   sarama.ByteEncoder(value),
		Headers: headers,
	}

	if p.isAsync {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case p.asyncProducer.Input() <- msg:
			// 消息已提交到异步队列
			return nil
		}
	} else {
		partition, offset, err := p.syncProducer.SendMessage(msg)
		if err != nil {
			p.mutex.Lock()
			p.sendErrors++
			p.mutex.Unlock()
			return fmt.Errorf("failed to send message: %w", err)
		}

		logger.Debug("Message sent successfully",
			zap.String("topic", topic),
			zap.Int32("partition", partition),
			zap.Int64("offset", offset))

		p.mutex.Lock()
		p.sendCount++
		p.mutex.Unlock()

		return nil
	}
}

// SendMessages 批量发送消息到指定主题
func (p *Producer) SendMessages(ctx context.Context, messages []*sarama.ProducerMessage) error {
	if len(messages) == 0 {
		return nil
	}

	if p.isAsync {
		for _, msg := range messages {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case p.asyncProducer.Input() <- msg:
				// 消息已提交到异步队列
			}
		}
		return nil
	} else {
		err := p.syncProducer.SendMessages(messages)
		if err != nil {
			p.mutex.Lock()
			p.sendErrors += int64(len(messages))
			p.mutex.Unlock()
			return fmt.Errorf("failed to send messages: %w", err)
		}

		logger.Debug("Batch messages sent successfully",
			zap.Int("count", len(messages)))

		p.mutex.Lock()
		p.sendCount += int64(len(messages))
		p.mutex.Unlock()

		return nil
	}
}

// handleSuccesses 处理异步生产者的成功消息
func (p *Producer) handleSuccesses() {
	defer p.wg.Done()
	for {
		select {
		case <-p.stopChan:
			return
		case msg, ok := <-p.asyncProducer.Successes():
			if !ok {
				return
			}
			logger.Debug("Message sent successfully",
				zap.String("topic", msg.Topic),
				zap.Int32("partition", msg.Partition),
				zap.Int64("offset", msg.Offset))

			p.mutex.Lock()
			p.sendCount++
			p.mutex.Unlock()
		}
	}
}

// handleErrors 处理异步生产者的错误消息
func (p *Producer) handleErrors() {
	defer p.wg.Done()
	for {
		select {
		case <-p.stopChan:
			return
		case err, ok := <-p.asyncProducer.Errors():
			if !ok {
				return
			}
			logger.Error("Failed to send message",
				zap.String("topic", err.Msg.Topic),
				zap.Error(err))

			p.mutex.Lock()
			p.sendErrors++
			p.mutex.Unlock()
		}
	}
}

// GetStats 获取发送统计信息
func (p *Producer) GetStats() (int64, int64) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	return p.sendCount, p.sendErrors
}
