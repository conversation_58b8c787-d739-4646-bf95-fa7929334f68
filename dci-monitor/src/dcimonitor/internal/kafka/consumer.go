package kafka

import (
	"context"
	"errors"
	"fmt"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"common/logger"

	"github.com/IBM/sarama"
	"go.uber.org/zap"
)

// MessageHandler 定义消息处理函数类型
type MessageHandler func(msg *sarama.ConsumerMessage) error

// ConsumerConfig 定义Kafka消费者配置
type ConsumerConfig struct {
	Brokers        []string         // Kafka服务器地址列表
	ConsumerGroup  string           // 消费者组ID
	Topics         []string         // 订阅的主题列表
	AutoCommit     bool             // 是否自动提交offset
	CommitInterval time.Duration    // 自动提交间隔（如果启用自动提交）
	InitialOffset  int64            // 初始offset: sarama.OffsetNewest 或 sarama.OffsetOldest
	MaxRetries     int              // 最大重试次数
	RetryInterval  time.Duration    // 重试间隔
	TLS            *KafkaTLSConfig  // TLS配置（可选）
	SASL           *KafkaSASLConfig // SASL配置（可选）
}

// Consumer 定义Kafka消费者结构
type Consumer struct {
	config        *ConsumerConfig
	saramaConfig  *sarama.Config
	client        sarama.ConsumerGroup
	handler       MessageHandler
	ready         chan bool
	stopChan      chan struct{}
	errorChan     chan error
	wg            sync.WaitGroup
	consumeCount  int64
	consumeErrors int64
	mutex         sync.Mutex
}

// NewConsumer 创建新的Kafka消费者
func NewConsumer(config *ConsumerConfig) (*Consumer, error) {
	if config == nil {
		return nil, errors.New("consumer config cannot be nil")
	}

	if len(config.Brokers) == 0 {
		return nil, errors.New("brokers list cannot be empty")
	}

	if config.ConsumerGroup == "" {
		return nil, errors.New("consumer group cannot be empty")
	}

	if len(config.Topics) == 0 {
		return nil, errors.New("topics list cannot be empty")
	}

	// 创建Sarama配置
	saramaConfig := sarama.NewConfig()
	saramaConfig.Version = sarama.V2_8_0_0 // 设置Kafka版本，根据实际环境调整

	// 配置消费者行为
	saramaConfig.Consumer.Return.Errors = true
	saramaConfig.Consumer.Offsets.AutoCommit.Enable = config.AutoCommit
	if config.AutoCommit {
		saramaConfig.Consumer.Offsets.AutoCommit.Interval = config.CommitInterval
	}
	saramaConfig.Consumer.Offsets.Initial = config.InitialOffset
	if saramaConfig.Consumer.Offsets.Initial == 0 {
		saramaConfig.Consumer.Offsets.Initial = sarama.OffsetNewest
	}

	// 配置重试策略
	saramaConfig.Metadata.Retry.Max = config.MaxRetries
	saramaConfig.Metadata.Retry.Backoff = config.RetryInterval

	// 配置TLS（如果启用）
	if config.TLS != nil && config.TLS.Enabled {
		tlsConfig, err := NewTLSConfig(config.TLS)
		if err != nil {
			return nil, fmt.Errorf("failed to create TLS config: %w", err)
		}
		saramaConfig.Net.TLS.Enable = true
		saramaConfig.Net.TLS.Config = tlsConfig
	}

	// 配置SASL（如果启用）
	if config.SASL != nil && config.SASL.Enabled {
		saramaConfig.Net.SASL.Enable = true
		saramaConfig.Net.SASL.Mechanism = sarama.SASLMechanism(config.SASL.Mechanism)
		saramaConfig.Net.SASL.User = config.SASL.Username
		saramaConfig.Net.SASL.Password = config.SASL.Password
	}

	// 确保获取完整的元数据，以便客户端能够正确路由到各个Broker
	saramaConfig.Metadata.Full = true

	return &Consumer{
		config:       config,
		saramaConfig: saramaConfig,
		ready:        make(chan bool),
		stopChan:     make(chan struct{}),
		errorChan:    make(chan error, 1),
	}, nil
}

// Start 启动消费者并开始消费消息
func (c *Consumer) Start(ctx context.Context, handler MessageHandler) error {
	if handler == nil {
		return errors.New("message handler cannot be nil")
	}
	c.handler = handler

	client, err := sarama.NewConsumerGroup(c.config.Brokers, c.config.ConsumerGroup, c.saramaConfig)
	if err != nil {
		return fmt.Errorf("failed to create consumer group: %w", err)
	}
	c.client = client

	// 监听消费者组错误
	go func() {
		for err := range client.Errors() {
			logger.Error("Consumer group error", zap.Error(err))
			c.errorChan <- err
		}
	}()

	// 启动消费循环
	c.wg.Add(1)
	go func() {
		defer c.wg.Done()
		for {
			// 检查是否收到停止信号
			select {
			case <-c.stopChan:
				logger.Info("Stopping consumer loop")
				return
			case <-ctx.Done():
				logger.Info("Context cancelled, stopping consumer loop")
				return
			default:
				// 继续消费
			}

			// 消费消息
			logger.Info("Starting consumer session",
				zap.Strings("topics", c.config.Topics),
				zap.String("group", c.config.ConsumerGroup))

			// 如果Consume返回，表示会话已结束，需要重新启动
			if err := client.Consume(ctx, c.config.Topics, c); err != nil {
				logger.Error("Consumer session error", zap.Error(err))
				time.Sleep(time.Second) // 避免过快重试
				continue
			}

			// 检查消费者是否准备好
			if !<-c.ready {
				logger.Info("Consumer not ready, restarting")
				continue
			}
		}
	}()

	// 等待消费者准备好
	<-c.ready
	logger.Info("Consumer is ready")

	return nil
}

// Stop 停止消费者
func (c *Consumer) Stop() error {
	logger.Info("Stopping consumer")

	// 关闭停止通道，通知所有协程停止
	close(c.stopChan)

	// 创建一个带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 创建一个通道用于等待关闭完成
	done := make(chan struct{})

	go func() {
		// 等待消费循环结束
		c.wg.Wait()

		// 关闭消费者组
		if c.client != nil {
			if err := c.client.Close(); err != nil {
				logger.Error("Failed to close consumer group", zap.Error(err))
			}
		}

		close(done)
	}()

	// 等待关闭完成或超时
	select {
	case <-done:
		logger.Info("Consumer stopped gracefully")
	case <-ctx.Done():
		logger.Warn("Consumer stop timed out, forcing shutdown")
	}

	logger.Info("Consumer stopped")
	return nil
}

// Setup 实现ConsumerGroupHandler接口，当消费者会话开始时调用
func (c *Consumer) Setup(sarama.ConsumerGroupSession) error {
	// 标记消费者已准备好
	close(c.ready)
	c.ready = make(chan bool)
	return nil
}

// Cleanup 实现ConsumerGroupHandler接口，当消费者会话结束时调用
func (c *Consumer) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim 实现ConsumerGroupHandler接口，处理分配给消费者的消息
func (c *Consumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for {
		select {
		case message, ok := <-claim.Messages():
			if !ok {
				logger.Info("Message channel closed")
				return nil
			}

			// 处理消息
			logger.Debug("Received message",
				zap.String("topic", message.Topic),
				zap.Int32("partition", message.Partition),
				zap.Int64("offset", message.Offset))

			if err := c.processMessage(message); err != nil {
				logger.Error("Failed to process message",
					zap.String("topic", message.Topic),
					zap.Int32("partition", message.Partition),
					zap.Int64("offset", message.Offset),
					zap.Error(err))

				c.mutex.Lock()
				c.consumeErrors++
				c.mutex.Unlock()

				// 如果不是自动提交，这里可以选择不提交有问题的消息
				if !c.config.AutoCommit {
					continue
				}
			}

			// 如果不是自动提交，手动提交offset
			if !c.config.AutoCommit {
				session.MarkMessage(message, "")
			}

			c.mutex.Lock()
			c.consumeCount++
			c.mutex.Unlock()

		case <-session.Context().Done():
			logger.Info("Consumer session context done")
			return nil
		case <-c.stopChan:
			logger.Info("Consumer received stop signal")
			return nil
		}
	}
}

// processMessage 处理单条消息
func (c *Consumer) processMessage(msg *sarama.ConsumerMessage) error {
	if c.handler != nil {
		return c.handler(msg)
	}
	return nil
}

// GetStats 获取消费统计信息
func (c *Consumer) GetStats() (int64, int64) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	return c.consumeCount, c.consumeErrors
}

// RunWithSignalHandling 启动消费者并处理系统信号
func (c *Consumer) RunWithSignalHandling(ctx context.Context, handler MessageHandler) error {
	// 创建一个带取消的上下文
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// 启动消费者
	if err := c.Start(ctx, handler); err != nil {
		return err
	}

	// 监听系统信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待信号或错误
	select {
	case <-sigChan:
		logger.Info("Received termination signal")
	case err := <-c.errorChan:
		logger.Error("Consumer error", zap.Error(err))
		return err
	}

	// 停止消费者
	return c.Stop()
}
