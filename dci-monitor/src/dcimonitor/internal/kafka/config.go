package kafka

// KafkaConfig 定义了 Kafka 配置
type KafkaConfig struct {
	// Kafka 集群连接信息
	Brokers []string `json:"brokers" yaml:"brokers"`

	// 安全配置
	Security SecurityConfig `json:"security" yaml:"security"`

	// 主题配置
	Topics []TopicConfig `json:"topics" yaml:"topics"`
}

// SecurityConfig 定义了 Kafka 安全配置
type SecurityConfig struct {
	TLS  TLSConfig  `json:"tls" yaml:"tls"`
	SASL SASLConfig `json:"sasl" yaml:"sasl"`
}

// TLSConfig 定义了 TLS 配置
type TLSConfig struct {
	Enabled  bool   `json:"enabled" yaml:"enabled"`
	CertFile string `json:"certFile" yaml:"certFile"`
	KeyFile  string `json:"keyFile" yaml:"keyFile"`
	CAFile   string `json:"caFile" yaml:"caFile"`
}

// SASLConfig 定义了 SASL 配置
type SASLConfig struct {
	Enabled   bool   `json:"enabled" yaml:"enabled"`
	Mechanism string `json:"mechanism" yaml:"mechanism"`
	Username  string `json:"username" yaml:"username"`
	Password  string `json:"password" yaml:"password"`
}

// TopicConfig 定义了 Kafka 主题配置
type TopicConfig struct {
	Name              string            `json:"name" yaml:"name"`
	Partitions        int               `json:"partitions" yaml:"partitions"`
	ReplicationFactor int               `json:"replicationFactor" yaml:"replicationFactor"`
	RetentionHours    int               `json:"retentionHours" yaml:"retentionHours"`
	Config            map[string]string `json:"config" yaml:"config"`
}

// DefaultKafkaConfig 返回默认的 Kafka 配置
func DefaultKafkaConfig() *KafkaConfig {
	return &KafkaConfig{
		Brokers: []string{"dcikafka.intra.citic-x.com:30002"},
		Security: SecurityConfig{
			TLS: TLSConfig{
				Enabled: false,
			},
			SASL: SASLConfig{
				Enabled: false,
			},
		},
		Topics: []TopicConfig{
			{
				Name:              "dci.monitor.v1.defaultchannel.topology.lldp",
				Partitions:        3,
				ReplicationFactor: 2,
				RetentionHours:    24,
				Config: map[string]string{
					"cleanup.policy": "delete",
				},
			},
			{
				Name:              "dci.monitor.v1.defaultchannel.metrics.telegraf",
				Partitions:        6,
				ReplicationFactor: 2,
				RetentionHours:    168,
			},
			{
				Name:              "dci.monitor.v1.defaultchannel.logs.syslog",
				Partitions:        3,
				ReplicationFactor: 2,
				RetentionHours:    720,
			},
			{
				Name:              "dci.monitor.v1.defaultchannel.tasks.control",
				Partitions:        3,
				ReplicationFactor: 2,
				RetentionHours:    24,
			},
		},
	}
}
