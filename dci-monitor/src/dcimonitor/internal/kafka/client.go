package kafka

import (
	"fmt"
	"time"

	"github.com/IBM/sarama"
	"go.uber.org/zap"
)

// KafkaClient 封装与 Kafka 集群交互的方法
type KafkaClient struct {
	admin  sarama.ClusterAdmin
	client sarama.Client
	logger *zap.Logger
}

// NewKafkaClient 创建新的 Kafka 客户端
func NewKafkaClient(brokers []string, config *KafkaConfig, logger *zap.Logger) (*KafkaClient, error) {
	if logger == nil {
		return nil, fmt.Errorf("logger 不能为 nil")
	}

	// 创建 Sarama 配置
	saramaConfig := sarama.NewConfig()
	saramaConfig.Version = sarama.V2_6_0_0 // 使用较新的版本

	// 生产者配置
	saramaConfig.Producer.RequiredAcks = sarama.WaitForAll
	saramaConfig.Producer.Return.Successes = true
	saramaConfig.Producer.Timeout = 10 * time.Second

	// 配置 TLS
	if config != nil && config.Security.TLS.Enabled {
		// TODO: 实现 TLS 配置
		logger.Info("TLS 配置尚未实现")
	}

	// 配置 SASL
	if config != nil && config.Security.SASL.Enabled {
		// TODO: 实现 SASL 配置
		logger.Info("SASL 配置尚未实现")
	}

	// 创建 Sarama 客户端
	client, err := sarama.NewClient(brokers, saramaConfig)
	if err != nil {
		return nil, fmt.Errorf("创建 Sarama 客户端失败: %w", err)
	}

	// 创建 Sarama 集群管理员
	admin, err := sarama.NewClusterAdminFromClient(client)
	if err != nil {
		client.Close()
		return nil, fmt.Errorf("创建 Sarama 集群管理员失败: %w", err)
	}

	return &KafkaClient{
		admin:  admin,
		client: client,
		logger: logger,
	}, nil
}

// CreateTopic 创建 Kafka 主题
func (c *KafkaClient) CreateTopic(config TopicConfig) error {
	// 转换配置
	topicDetail := &sarama.TopicDetail{
		NumPartitions:     int32(config.Partitions),
		ReplicationFactor: int16(config.ReplicationFactor),
		ConfigEntries:     make(map[string]*string),
	}

	// 设置额外配置
	for key, value := range config.Config {
		valueCopy := value // 创建一个副本，避免循环中的引用问题
		topicDetail.ConfigEntries[key] = &valueCopy
	}

	// 设置消息保留时间（如果指定）
	if config.RetentionHours > 0 {
		retentionMs := fmt.Sprintf("%d", config.RetentionHours*60*60*1000)
		topicDetail.ConfigEntries["retention.ms"] = &retentionMs
	}

	// 检查主题是否存在
	topics, err := c.ListTopics()
	if err != nil {
		return fmt.Errorf("获取主题列表失败: %w", err)
	}

	for _, t := range topics {
		if t == config.Name {
			c.logger.Info("主题已存在，跳过创建", zap.String("主题", config.Name))
			return nil
		}
	}

	// 创建主题
	err = c.admin.CreateTopic(config.Name, topicDetail, false)
	if err != nil {
		return fmt.Errorf("创建主题失败: %w", err)
	}

	c.logger.Info("成功创建主题",
		zap.String("主题", config.Name),
		zap.Int("分区数", config.Partitions),
		zap.Int("副本因子", config.ReplicationFactor),
	)
	return nil
}

// ListTopics 列出所有主题
func (c *KafkaClient) ListTopics() ([]string, error) {
	topicsMap, err := c.admin.ListTopics()
	if err != nil {
		return nil, fmt.Errorf("列出主题失败: %w", err)
	}

	topics := make([]string, 0, len(topicsMap))
	for topic := range topicsMap {
		topics = append(topics, topic)
	}

	return topics, nil
}

// DescribeTopic 获取主题详情
func (c *KafkaClient) DescribeTopic(topicName string) (*sarama.TopicDetail, error) {
	topicsMap, err := c.admin.DescribeTopics([]string{topicName})
	if err != nil {
		return nil, fmt.Errorf("获取主题详情失败: %w", err)
	}

	if len(topicsMap) == 0 {
		return nil, fmt.Errorf("主题 %s 不存在", topicName)
	}

	topicMetadata := topicsMap[0]
	if topicMetadata.Err != sarama.ErrNoError {
		return nil, fmt.Errorf("获取主题详情失败: %w", topicMetadata.Err)
	}

	// 获取主题配置
	configEntries, err := c.admin.DescribeConfig(sarama.ConfigResource{
		Type:        sarama.TopicResource,
		Name:        topicName,
		ConfigNames: []string{},
	})
	if err != nil {
		return nil, fmt.Errorf("获取主题配置失败: %w", err)
	}

	// 构建主题详情
	topicDetail := &sarama.TopicDetail{
		NumPartitions:     int32(len(topicMetadata.Partitions)),
		ReplicationFactor: int16(len(topicMetadata.Partitions[0].Replicas)),
		ConfigEntries:     make(map[string]*string),
	}

	for _, entry := range configEntries {
		if !entry.Default {
			valueCopy := entry.Value
			topicDetail.ConfigEntries[entry.Name] = &valueCopy
		}
	}

	return topicDetail, nil
}

// Close 关闭客户端连接
func (c *KafkaClient) Close() error {
	var errs []error

	if err := c.admin.Close(); err != nil {
		errs = append(errs, fmt.Errorf("关闭 admin 失败: %w", err))
	}

	if err := c.client.Close(); err != nil {
		errs = append(errs, fmt.Errorf("关闭 client 失败: %w", err))
	}

	if len(errs) > 0 {
		return fmt.Errorf("关闭 Kafka 客户端时发生错误: %v", errs)
	}

	return nil
}
