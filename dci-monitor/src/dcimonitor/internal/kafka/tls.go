package kafka

import (
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"io/ioutil"
)

// KafkaTLSConfig 定义TLS配置参数
type KafkaTLSConfig struct {
	Enabled  bool   // 是否启用TLS
	CertFile string // 客户端证书文件路径
	KeyFile  string // 客户端私钥文件路径
	CAFile   string // CA证书文件路径
}

// NewTLSConfig 创建TLS配置
func NewTLSConfig(config *KafkaTLSConfig) (*tls.Config, error) {
	if config == nil || !config.Enabled {
		return nil, nil
	}

	tlsConfig := &tls.Config{
		InsecureSkipVerify: false,
	}

	// 如果提供了CA证书，加载它
	if config.CAFile != "" {
		caCert, err := ioutil.ReadFile(config.CAFile)
		if err != nil {
			return nil, fmt.Errorf("failed to read CA certificate: %w", err)
		}

		caCertPool := x509.NewCertPool()
		if !caCertPool.AppendCertsFromPEM(caCert) {
			return nil, fmt.Errorf("failed to parse CA certificate")
		}
		tlsConfig.RootCAs = caCertPool
	}

	// 如果提供了客户端证书和私钥，加载它们
	if config.CertFile != "" && config.KeyFile != "" {
		cert, err := tls.LoadX509KeyPair(config.CertFile, config.KeyFile)
		if err != nil {
			return nil, fmt.Errorf("failed to load client certificate: %w", err)
		}
		tlsConfig.Certificates = []tls.Certificate{cert}
	}

	return tlsConfig, nil
}

// KafkaSASLConfig 定义SASL配置参数
type KafkaSASLConfig struct {
	Enabled   bool   // 是否启用SASL
	Mechanism string // SASL机制：PLAIN、SCRAM-SHA-256、SCRAM-SHA-512等
	Username  string // 用户名
	Password  string // 密码
}
