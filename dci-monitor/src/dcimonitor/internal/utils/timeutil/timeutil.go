package timeutil

import (
	"time"
)

// 全局变量，存储本地时区
var localLocation *time.Location

// init 初始化本地时区
func init() {
	var err error
	// 默认使用系统本地时区
	localLocation, err = time.LoadLocation("Local")
	if err != nil {
		// 如果无法获取本地时区，则使用北京时间(Asia/Shanghai)
		localLocation, _ = time.LoadLocation("Asia/Shanghai")
	}
}

// ToLocalTime 将时间转换为本地时区
func ToLocalTime(t time.Time) time.Time {
	if t.IsZero() {
		return t
	}
	return t.In(localLocation)
}

// NowInLocalTime 获取当前本地时区的时间
func NowInLocalTime() time.Time {
	return time.Now().In(localLocation)
}

// SetLocalTimeZone 设置本地时区
func SetLocalTimeZone(timezone string) error {
	location, err := time.LoadLocation(timezone)
	if err != nil {
		return err
	}
	localLocation = location
	return nil
}

// GetLocalTimeZone 获取当前使用的本地时区
func GetLocalTimeZone() *time.Location {
	return localLocation
}

// FormatLocalTime 将时间格式化为本地时区的字符串
func FormatLocalTime(t time.Time, layout string) string {
	if t.IsZero() {
		return ""
	}
	return ToLocalTime(t).Format(layout)
}

// ParseInLocalTime 将字符串按照指定格式解析为本地时区的时间
func ParseInLocalTime(layout, value string) (time.Time, error) {
	return time.ParseInLocation(layout, value, localLocation)
}

// IsZeroOrNull 检查时间是否为零值或空
func IsZeroOrNull(t *time.Time) bool {
	return t == nil || t.IsZero()
}
