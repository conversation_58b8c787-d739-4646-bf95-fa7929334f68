package utils

import (
	"fmt"
	"strings"
	"time"

	"common/logger"

	"github.com/IBM/sarama"
	"go.uber.org/zap"
)

const (
	// Kafka客户端连接超时时间
	kafkaConnectTimeout = 30 * time.Second
)

// KafkaClientConfig 保存Kafka客户端配置
type KafkaClientConfig struct {
	// Kafka服务器地址，格式：host1:port1,host2:port2
	BootstrapServers string
	// 客户端ID
	ClientID string
	// 是否启用详细日志
	Debug bool
	// 连接超时时间
	ConnectTimeout time.Duration
	// 元数据刷新间隔
	RefreshMetadataInterval time.Duration
}

// NewDefaultKafkaConfig 创建默认的Kafka配置
func NewDefaultKafkaConfig(bootstrapServers string) *KafkaClientConfig {
	return &KafkaClientConfig{
		BootstrapServers:        bootstrapServers,
		ClientID:                "dci-kafka-client",
		Debug:                   false,
		ConnectTimeout:          kafkaConnectTimeout,
		RefreshMetadataInterval: 30 * time.Second,
	}
}

// CreateKafkaClient 创建Kafka客户端连接
func CreateKafkaClient(config *KafkaClientConfig) (sarama.Client, error) {
	// 创建Sarama配置
	saramaConfig := sarama.NewConfig()
	saramaConfig.ClientID = config.ClientID
	saramaConfig.Version = sarama.V3_0_0_0 // 使用Kafka 3.0版本特性

	// 关键：获取完整的元数据，帮助客户端正确选择监听器
	saramaConfig.Metadata.Full = true

	// 设置网络超时参数
	saramaConfig.Net.DialTimeout = config.ConnectTimeout
	saramaConfig.Net.ReadTimeout = config.ConnectTimeout
	saramaConfig.Net.WriteTimeout = config.ConnectTimeout

	// 禁用SASL（默认）
	saramaConfig.Net.SASL.Enable = false

	// 设置元数据超时
	saramaConfig.Metadata.Timeout = config.ConnectTimeout

	// 优化元数据缓存和更新策略
	// 减少元数据最大有效期，设置更频繁的刷新间隔
	saramaConfig.Metadata.RefreshFrequency = config.RefreshMetadataInterval
	saramaConfig.Metadata.Retry.Max = 10                         // 增加元数据重试次数，默认为5
	saramaConfig.Metadata.Retry.Backoff = 200 * time.Millisecond // 减小元数据重试间隔

	// 配置生产者
	saramaConfig.Producer.Return.Successes = true
	saramaConfig.Producer.Return.Errors = true

	// 开启详细日志（如果需要）
	if config.Debug {
		sarama.Logger = &zapSaramaLogger{logger: logger.GetLogger()}
		logger.GetLogger().Info("Sarama verbose logging enabled")
	}

	// 创建客户端连接
	// 确保BootstrapServers被正确分割成字符串切片
	brokers := strings.Split(config.BootstrapServers, ",")
	if len(brokers) == 0 || brokers[0] == "" {
		return nil, fmt.Errorf("kafka bootstrap servers地址不能为空")
	}

	client, err := sarama.NewClient(brokers, saramaConfig)
	if err != nil {
		logger.Error("创建Kafka客户端失败",
			zap.String("bootstrapServers", config.BootstrapServers),
			zap.Error(err))
		return nil, fmt.Errorf("创建Kafka客户端失败: %w", err)
	}

	logger.Info("已成功连接到Kafka集群",
		zap.String("bootstrapServers", config.BootstrapServers),
		zap.String("clientID", config.ClientID),
		zap.Duration("metadataRefreshInterval", config.RefreshMetadataInterval),
		zap.Int("metadataRetryMax", saramaConfig.Metadata.Retry.Max))

	return client, nil
}

// GetKafkaMetadata 获取Kafka集群元数据信息
func GetKafkaMetadata(client sarama.Client) (map[string]interface{}, error) {
	// 创建结果Map
	result := make(map[string]interface{})

	// 获取Broker信息
	brokers := client.Brokers()
	brokerInfos := make([]map[string]interface{}, 0, len(brokers))

	for _, broker := range brokers {
		connected, _ := broker.Connected()
		brokerInfo := map[string]interface{}{
			"id":        broker.ID(),
			"addr":      broker.Addr(),
			"connected": connected,
		}
		brokerInfos = append(brokerInfos, brokerInfo)
	}
	result["brokers"] = brokerInfos

	// 获取主题信息
	topics, err := client.Topics()
	if err != nil {
		return nil, fmt.Errorf("获取主题列表失败: %w", err)
	}

	topicInfos := make(map[string]map[string]interface{})
	for _, topic := range topics {
		partitions, err := client.Partitions(topic)
		if err != nil {
			logger.Warn("获取主题分区失败",
				zap.String("topic", topic),
				zap.Error(err))
			continue
		}

		partitionInfos := make([]map[string]interface{}, 0, len(partitions))
		for _, partition := range partitions {
			partitionInfo := make(map[string]interface{})
			partitionInfo["id"] = partition

			leader, err := client.Leader(topic, partition)
			if err == nil {
				partitionInfo["leader"] = map[string]interface{}{
					"id":   leader.ID(),
					"addr": leader.Addr(),
				}
			}

			replicas, err := client.Replicas(topic, partition)
			if err == nil {
				partitionInfo["replicas"] = replicas
			}

			isr, err := client.InSyncReplicas(topic, partition)
			if err == nil {
				partitionInfo["isr"] = isr
			}

			partitionInfos = append(partitionInfos, partitionInfo)
		}

		topicInfos[topic] = map[string]interface{}{
			"partitions": partitionInfos,
		}
	}
	result["topics"] = topicInfos

	return result, nil
}

// 用于记录Sarama日志的适配器
type zapSaramaLogger struct {
	logger *zap.Logger
}

func (s *zapSaramaLogger) Print(v ...interface{}) {
	s.logger.Info(fmt.Sprint(v...))
}

func (s *zapSaramaLogger) Printf(format string, v ...interface{}) {
	s.logger.Info(fmt.Sprintf(format, v...))
}

func (s *zapSaramaLogger) Println(v ...interface{}) {
	s.logger.Info(fmt.Sprintln(v...))
}
