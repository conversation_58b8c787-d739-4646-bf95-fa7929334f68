package alert

import (
	"dcimonitor/internal/middleware"
	"dcimonitor/internal/models"
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Handler 处理告警相关的API请求
type Handler struct {
	service *Service
	logger  *zap.Logger
}

// NewHandler 创建一个新的Handler实例
func NewHandler(service *Service, logger *zap.Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(router *gin.Engine) {
	alertGroup := router.Group("/api/v1/alerts")
	{
		alertGroup.GET("", h.ListAlerts)
		alertGroup.GET("/:id", h.GetAlertByID)
		alertGroup.POST("/webhook/prometheus", h.ProcessPrometheusWebhook)
		alertGroup.PUT("/:id/acknowledge", h.AcknowledgeAlert)
		alertGroup.GET("/statistics", h.GetAlertStatistics)
	}

	ruleGroup := router.Group("/api/v1/alert-rules")
	{
		ruleGroup.GET("", h.ListAlertRules)
		ruleGroup.POST("", h.CreateAlertRule)
		ruleGroup.GET("/:id", h.GetAlertRuleByID)
		ruleGroup.PUT("/:id", h.UpdateAlertRule)
		ruleGroup.DELETE("/:id", h.DeleteAlertRule)
		ruleGroup.PUT("/:id/toggle", h.ToggleAlertRule)
	}
}

// 从上下文中获取请求ID，委托给middleware包中的函数
func getRequestID(c *gin.Context) string {
	return middleware.GetRequestID(c)
}

// @Summary 处理Prometheus告警Webhook
// @Description 接收并处理Prometheus AlertManager发送的告警通知
// @Tags 告警管理
// @Accept json
// @Produce json
// @Param webhook body PrometheusWebhookRequest true "Prometheus告警数据"
// @Success 200 {object} Response "成功处理"
// @Failure 400 {object} Response "请求格式错误"
// @Router /api/v1/alerts/webhook/prometheus [post]
func (h *Handler) ProcessPrometheusWebhook(c *gin.Context) {
	// 在请求上下文中记录函数名，方便排查问题
	h.logger.Debug("处理请求",
		zap.String("function", "ProcessPrometheusWebhook"),
		zap.String("request_id", getRequestID(c)))

	resp, err := h.service.ProcessPrometheusWebhook(c)
	if err != nil {
		// 错误已在服务层处理并记录，这里只需返回响应
		c.JSON(getStatusCode(resp.Code), resp)
		return
	}

	c.JSON(http.StatusOK, resp)
}

// @Summary 获取告警列表
// @Description 获取符合筛选条件的告警列表，支持分页
// @Tags 告警管理
// @Produce json
// @Param source query string false "告警来源"
// @Param level query string false "告警级别(critical/warning/info)"
// @Param status query string false "告警状态(firing/acknowledged/resolved)"
// @Param device_id query string false "设备ID"
// @Param page query int false "页码(默认1)"
// @Param page_size query int false "每页数量(默认20，最大100)"
// @Success 200 {object} Response{data=AlertListResponse} "成功返回"
// @Failure 500 {object} Response "服务器错误"
// @Router /api/v1/alerts [get]
func (h *Handler) ListAlerts(c *gin.Context) {
	requestID := getRequestID(c)

	h.logger.Debug("处理请求",
		zap.String("function", "ListAlerts"),
		zap.String("request_id", requestID))

	resp, err := h.service.ListAlerts(c)
	if err != nil {
		h.logger.Error("获取告警列表失败",
			zap.Error(err),
			zap.String("request_id", requestID))
		// 错误已在服务层处理并记录
		c.JSON(getStatusCode(resp.Code), resp)
		return
	}

	c.JSON(http.StatusOK, resp)
}

// @Summary 获取告警详情
// @Description 根据告警ID获取详细信息
// @Tags 告警管理
// @Produce json
// @Param id path string true "告警ID"
// @Success 200 {object} Response{data=Alert} "成功返回"
// @Failure 400 {object} Response "参数错误"
// @Failure 404 {object} Response "告警不存在"
// @Failure 500 {object} Response "服务器错误"
// @Router /api/v1/alerts/{id} [get]
func (h *Handler) GetAlertByID(c *gin.Context) {
	requestID := getRequestID(c)

	h.logger.Debug("处理请求",
		zap.String("function", "GetAlertByID"),
		zap.String("request_id", requestID),
		zap.String("alert_id", c.Param("id")))

	resp, err := h.service.GetAlertByID(c)
	if err != nil {
		h.logger.Error("获取告警时发生错误",
			zap.Error(err),
			zap.String("request_id", requestID))
		c.JSON(http.StatusInternalServerError, Response{
			Code: http.StatusInternalServerError,
			Data: models.ErrorResponse{
				Error:     "获取告警失败: " + err.Error(),
				RequestID: requestID,
			},
		})
		return
	}

	// 使用服务返回的状态码
	c.JSON(resp.Code, resp)
}

// @Summary 确认告警
// @Description 标记告警为已确认状态，表明有人正在处理该告警
// @Tags 告警管理
// @Accept json
// @Produce json
// @Param id path string true "告警ID" example("a1b2c3d4-e5f6-7890-abcd-ef1234567890")
// @Param request body alert.AlertAcknowledgeRequest true "告警确认请求" example({"acknowledged_by":"张三","note":"正在联系网络组排查问题"})
// @Success 200 {object} alert.AlertAcknowledgeResponse "告警确认成功" example({"code":200,"message":"告警已确认"})
// @Failure 400 {object} models.Response "请求参数错误" example({"code":400,"data":{"message":"请求体不能为空"}})
// @Failure 404 {object} models.Response "告警不存在" example({"code":404,"data":{"message":"告警不存在"}})
// @Failure 500 {object} models.Response "服务器内部错误" example({"code":500,"data":{"message":"确认告警失败: 数据库错误"}})
// @Router /api/v1/alerts/{id}/acknowledge [put]
func (h *Handler) AcknowledgeAlert(c *gin.Context) {
	requestID := getRequestID(c)

	h.logger.Debug("处理请求",
		zap.String("function", "AcknowledgeAlert"),
		zap.String("request_id", requestID),
		zap.String("alert_id", c.Param("id")))

	resp, err := h.service.AcknowledgeAlert(c)
	if err != nil {
		h.logger.Error("确认告警失败",
			zap.Error(err),
			zap.String("alert_id", c.Param("id")),
			zap.String("request_id", requestID))
		c.JSON(getStatusCode(resp.Code), resp)
		return
	}

	c.JSON(http.StatusOK, resp)
}

// @Summary 获取告警统计
// @Description 获取告警的各种统计数据，包括按状态、级别和时间的统计
// @Tags 告警管理
// @Produce json
// @Success 200 {object} Response "统计数据"
// @Failure 500 {object} Response "服务器错误"
// @Router /api/v1/alerts/statistics [get]
func (h *Handler) GetAlertStatistics(c *gin.Context) {
	requestID := getRequestID(c)

	h.logger.Debug("处理请求",
		zap.String("function", "GetAlertStatistics"),
		zap.String("request_id", requestID))

	resp, err := h.service.GetAlertStatistics(c)
	if err != nil {
		h.logger.Error("获取告警统计失败",
			zap.Error(err),
			zap.String("request_id", requestID))
		c.JSON(getStatusCode(resp.Code), resp)
		return
	}

	c.JSON(http.StatusOK, resp)
}

// @Summary 获取告警规则列表
// @Description 获取系统中配置的告警规则列表
// @Tags 告警规则
// @Produce json
// @Param source query string false "规则来源"
// @Param enabled query bool false "是否启用"
// @Success 200 {object} Response "规则列表"
// @Failure 500 {object} Response "服务器错误"
// @Router /api/v1/alert-rules [get]
func (h *Handler) ListAlertRules(c *gin.Context) {
	requestID := getRequestID(c)

	h.logger.Debug("处理请求",
		zap.String("function", "ListAlertRules"),
		zap.String("request_id", requestID))

	resp, err := h.service.ListAlertRules(c)
	if err != nil {
		h.logger.Error("获取告警规则列表失败",
			zap.Error(err),
			zap.String("request_id", requestID))
		c.JSON(getStatusCode(resp.Code), resp)
		return
	}

	c.JSON(http.StatusOK, resp)
}

// @Summary 获取告警规则详情
// @Description 根据规则ID获取告警规则详细信息
// @Tags 告警规则
// @Produce json
// @Param id path string true "规则ID"
// @Success 200 {object} Response{data=AlertRule} "规则详情"
// @Failure 400 {object} Response "参数错误"
// @Failure 404 {object} Response "规则不存在"
// @Failure 500 {object} Response "服务器错误"
// @Router /api/v1/alert-rules/{id} [get]
func (h *Handler) GetAlertRuleByID(c *gin.Context) {
	h.logger.Debug("处理请求",
		zap.String("function", "GetAlertRuleByID"),
		zap.String("request_id", getRequestID(c)),
		zap.String("rule_id", c.Param("id")))

	resp, err := h.service.GetAlertRuleByID(c)
	if err != nil {
		c.JSON(getStatusCode(resp.Code), resp)
		return
	}

	c.JSON(http.StatusOK, resp)
}

// @Summary 启用/禁用告警规则
// @Description 切换告警规则的启用状态
// @Tags 告警规则
// @Accept json
// @Produce json
// @Param id path string true "规则ID"
// @Param toggle body AlertRuleToggleRequest true "状态设置"
// @Success 200 {object} Response "操作成功"
// @Failure 400 {object} Response "参数错误"
// @Failure 404 {object} Response "规则不存在"
// @Failure 500 {object} Response "服务器错误"
// @Router /api/v1/alert-rules/{id}/toggle [put]
func (h *Handler) ToggleAlertRule(c *gin.Context) {
	h.logger.Debug("处理请求",
		zap.String("function", "ToggleAlertRule"),
		zap.String("request_id", getRequestID(c)),
		zap.String("rule_id", c.Param("id")))

	resp, err := h.service.ToggleAlertRule(c)
	if err != nil {
		c.JSON(getStatusCode(resp.Code), resp)
		return
	}

	c.JSON(http.StatusOK, resp)
}

// @Summary 创建告警规则
// @Description 创建新的告警规则
// @Tags 告警规则
// @Accept json
// @Produce json
// @Param rule body AlertRule true "规则定义"
// @Success 200 {object} Response{data=AlertRule} "创建成功"
// @Failure 400 {object} Response "参数错误"
// @Failure 409 {object} Response "同名规则已存在"
// @Failure 500 {object} Response "服务器错误"
// @Router /api/v1/alert-rules [post]
func (h *Handler) CreateAlertRule(c *gin.Context) {
	h.logger.Debug("处理请求",
		zap.String("function", "CreateAlertRule"),
		zap.String("request_id", getRequestID(c)))

	resp, err := h.service.CreateAlertRule(c)
	if err != nil {
		c.JSON(getStatusCode(resp.Code), resp)
		return
	}

	c.JSON(http.StatusOK, resp)
}

// @Summary 更新告警规则
// @Description 更新现有告警规则
// @Tags 告警规则
// @Accept json
// @Produce json
// @Param id path string true "规则ID"
// @Param rule body AlertRule true "规则定义"
// @Success 200 {object} Response{data=AlertRule} "更新成功"
// @Failure 400 {object} Response "参数错误"
// @Failure 404 {object} Response "规则不存在"
// @Failure 409 {object} Response "同名规则已存在"
// @Failure 500 {object} Response "服务器错误"
// @Router /api/v1/alert-rules/{id} [put]
func (h *Handler) UpdateAlertRule(c *gin.Context) {
	h.logger.Debug("处理请求",
		zap.String("function", "UpdateAlertRule"),
		zap.String("request_id", getRequestID(c)),
		zap.String("rule_id", c.Param("id")))

	resp, err := h.service.UpdateAlertRule(c)
	if err != nil {
		c.JSON(getStatusCode(resp.Code), resp)
		return
	}

	c.JSON(http.StatusOK, resp)
}

// @Summary 删除告警规则
// @Description 删除现有告警规则
// @Tags 告警规则
// @Produce json
// @Param id path string true "规则ID"
// @Success 200 {object} Response "删除成功"
// @Failure 400 {object} Response "参数错误"
// @Failure 404 {object} Response "规则不存在"
// @Failure 500 {object} Response "服务器错误"
// @Router /api/v1/alert-rules/{id} [delete]
func (h *Handler) DeleteAlertRule(c *gin.Context) {
	h.logger.Debug("处理请求",
		zap.String("function", "DeleteAlertRule"),
		zap.String("request_id", getRequestID(c)),
		zap.String("rule_id", c.Param("id")))

	resp, err := h.service.DeleteAlertRule(c)
	if err != nil {
		c.JSON(getStatusCode(resp.Code), resp)
		return
	}

	c.JSON(http.StatusOK, resp)
}

// getStatusCode 根据业务状态码返回适当的HTTP状态码
func getStatusCode(code int) int {
	switch code {
	case 200:
		return http.StatusOK
	case 201:
		return http.StatusCreated
	case 400:
		return http.StatusBadRequest
	case 401:
		return http.StatusUnauthorized
	case 403:
		return http.StatusForbidden
	case 404:
		return http.StatusNotFound
	case 409:
		return http.StatusConflict
	case 422:
		return http.StatusUnprocessableEntity
	default:
		return http.StatusInternalServerError
	}
}
