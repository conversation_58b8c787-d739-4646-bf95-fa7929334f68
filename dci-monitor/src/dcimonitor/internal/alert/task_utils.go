package alert

import (
	"net"
	"regexp"
	"strings"
)

// DeviceExtractor 设备ID提取器，支持配置驱动
type DeviceExtractor struct {
	config *DeviceMappingConfig
}

// NewDeviceExtractor 创建设备ID提取器
func NewDeviceExtractor(config *DeviceMappingConfig) *DeviceExtractor {
	return &DeviceExtractor{
		config: config,
	}
}

// ExtractDeviceIDWithConfig 使用配置提取设备ID
func (e *DeviceExtractor) ExtractDeviceIDWithConfig(labels map[string]string) string {
	if e.config == nil {
		// 如果没有配置，使用默认逻辑
		return ExtractDeviceID(labels)
	}

	// 先尝试使用全局优先级配置
	deviceID := e.extractByGlobalConfig(labels)
	if deviceID != "" {
		return deviceID
	}

	// 再尝试使用特定映射规则
	return e.extractByMappingRules(labels)
}

// extractByGlobalConfig 使用全局配置提取设备ID
func (e *DeviceExtractor) extractByGlobalConfig(labels map[string]string) string {
	globalConfig := e.config.GlobalDeviceExtraction

	// 按优先级标签提取
	for _, label := range globalConfig.PriorityLabels {
		if value, exists := labels[label]; exists && value != "" {
			return value
		}
	}

	// 处理instance标签
	if globalConfig.InstanceParsing.Enabled {
		if instance, exists := labels["instance"]; exists && instance != "" {
			deviceID := e.parseInstance(instance, globalConfig.InstanceParsing.RemovePort)
			if deviceID != "" {
				return deviceID
			}
		}
	}

	// 使用备用标签
	for _, label := range globalConfig.FallbackLabels {
		if value, exists := labels[label]; exists && value != "" {
			return value
		}
	}

	return ""
}

// extractByMappingRules 使用映射规则提取设备ID
func (e *DeviceExtractor) extractByMappingRules(labels map[string]string) string {
	for _, mapping := range e.config.DeviceIDMapping {
		// 检查是否匹配源模式
		if e.matchesSourcePattern(labels, mapping.SourcePattern) {
			// 按映射规则的设备ID标签提取
			for _, label := range mapping.DeviceIDLabels {
				if value, exists := labels[label]; exists && value != "" {
					return value
				}
			}

			// 使用实例模式提取
			if mapping.InstancePattern != "" {
				if instance, exists := labels["instance"]; exists && instance != "" {
					deviceID := e.parseInstanceWithPattern(instance, mapping.InstancePattern, mapping.InstanceDeviceGroup)
					if deviceID != "" {
						return deviceID
					}
				}
			}
		}
	}

	return ""
}

// matchesSourcePattern 检查标签是否匹配源模式
func (e *DeviceExtractor) matchesSourcePattern(labels map[string]string, pattern string) bool {
	// 简单的模式匹配，可以扩展为正则表达式
	for key := range labels {
		if matched, _ := regexp.MatchString(pattern, key); matched {
			return true
		}
	}
	return false
}

// parseInstance 解析instance标签
func (e *DeviceExtractor) parseInstance(instance string, removePort bool) string {
	if isValidIP(instance) {
		return instance
	}

	if removePort && strings.Contains(instance, ":") {
		parts := strings.Split(instance, ":")
		if len(parts) > 0 && isValidIP(parts[0]) {
			return parts[0]
		}
	}

	return ""
}

// parseInstanceWithPattern 使用模式解析instance
func (e *DeviceExtractor) parseInstanceWithPattern(instance, pattern string, deviceGroup int) string {
	re, err := regexp.Compile(pattern)
	if err != nil {
		return ""
	}

	matches := re.FindStringSubmatch(instance)
	if len(matches) > deviceGroup {
		return matches[deviceGroup]
	}

	return ""
}

// ExtractDeviceID 从告警Labels中提取设备ID (保持向后兼容)
// 按照24-02文档设计，优先级顺序为：device_id > instance > device_ip > host > node
func ExtractDeviceID(labels map[string]string) string {
	// 优先级1: 直接的device_id标签
	if deviceID, exists := labels["device_id"]; exists && deviceID != "" {
		return deviceID
	}

	// 优先级2: 从instance标签解析设备信息
	if instance, exists := labels["instance"]; exists && instance != "" {
		// 如果instance是IP地址格式，直接返回
		if isValidIP(instance) {
			return instance
		}

		// 如果instance包含端口，去除端口部分
		if strings.Contains(instance, ":") {
			parts := strings.Split(instance, ":")
			if len(parts) > 0 && isValidIP(parts[0]) {
				return parts[0]
			}
		}
	}

	// 优先级3: 其他可能的设备标识标签
	for _, field := range []string{"device_ip", "host", "node"} {
		if value, exists := labels[field]; exists && value != "" {
			return value
		}
	}

	return ""
}

// isValidIP 验证IP地址有效性
func isValidIP(ip string) bool {
	return net.ParseIP(ip) != nil
}

// EnhanceAlertMetadata 增强告警元数据
// 根据24-02文档设计，为告警添加设备ID和任务关联信息
func EnhanceAlertMetadata(alert *Alert, deviceID string, taskResult *TaskAssociationResult) {
	// 确保annotations字段存在
	if alert.Annotations == nil {
		alert.Annotations = make(map[string]string)
	}

	// 设置设备ID字段(直接存储到表字段中)
	if deviceID != "" {
		alert.DeviceID = &deviceID // 设置为字符串指针
		alert.Annotations["device_id"] = deviceID
		alert.Annotations["extracted_device"] = "true"
	}

	// 添加任务关联信息(通过关联表管理)
	if taskResult != nil && taskResult.TaskID != "" {
		alert.Annotations["task_associated"] = "true"

		// 标记任务后续影响
		if taskResult.AlertType == "post_task" {
			alert.Annotations["task_relation"] = "任务后续影响"
			alert.Annotations["monitoring_phase"] = "post_completion"
		} else {
			alert.Annotations["task_relation"] = "任务执行期间"
			alert.Annotations["monitoring_phase"] = "active_execution"
		}

		// 保存告警后，在关联表中创建关联记录
		// 这将在AlertService中处理
		alert.TaskAssociation = &TaskAssociation{
			TaskID:          taskResult.TaskID,
			AssociationType: taskResult.AlertType,
		}
	}
}

// CreateTaskAlertAssociationFromAlert 从告警创建任务关联记录
func CreateTaskAlertAssociationFromAlert(alert *Alert) *TaskAlertAssociation {
	if alert.TaskAssociation == nil || alert.TaskAssociation.TaskID == "" {
		return nil
	}

	// 获取设备ID，处理指针类型
	deviceID := ""
	if alert.DeviceID != nil {
		deviceID = *alert.DeviceID
	}

	return &TaskAlertAssociation{
		TaskID:          alert.TaskAssociation.TaskID,
		AlertID:         alert.ID,
		AssociationType: alert.TaskAssociation.AssociationType,
		DeviceID:        deviceID, // 从告警中获取设备ID
	}
}
