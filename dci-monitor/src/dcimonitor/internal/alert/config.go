package alert

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// Config 告警模块配置
type Config struct {
	TaskMonitoring        TaskMonitoringConfig        `yaml:"task_monitoring"`
	AlertAssociation      AlertAssociationConfig      `yaml:"alert_association"`
	PrometheusIntegration PrometheusIntegrationConfig `yaml:"prometheus_integration"`
	Cache                 CacheConfig                 `yaml:"cache"`
}

// TaskMonitoringConfig 任务协同监控配置
type TaskMonitoringConfig struct {
	Enabled            bool                     `yaml:"enabled"`
	ExtendedMonitoring ExtendedMonitoringConfig `yaml:"extended_monitoring"`
	SessionManagement  SessionManagementConfig  `yaml:"session_management"`
}

// ExtendedMonitoringConfig 延展监测配置
type ExtendedMonitoringConfig struct {
	Enabled  bool          `yaml:"enabled"`
	Duration time.Duration `yaml:"duration"`
}

// SessionManagementConfig 会话管理配置
type SessionManagementConfig struct {
	CleanupInterval time.Duration `yaml:"cleanup_interval"`
	MaxSessions     int           `yaml:"max_sessions"`
}

// AlertAssociationConfig 告警关联配置
type AlertAssociationConfig struct {
	Enabled            bool                     `yaml:"enabled"`
	DeviceIDExtraction DeviceIDExtractionConfig `yaml:"device_id_extraction"`
	AssociationQuery   AssociationQueryConfig   `yaml:"association_query"`
}

// DeviceIDExtractionConfig 设备ID提取配置
type DeviceIDExtractionConfig struct {
	PriorityLabels  []string              `yaml:"priority_labels"`
	InstanceParsing InstanceParsingConfig `yaml:"instance_parsing"`
	FallbackLabels  []string              `yaml:"fallback_labels"`
}

// InstanceParsingConfig instance标签解析配置
type InstanceParsingConfig struct {
	Enabled    bool `yaml:"enabled"`
	RemovePort bool `yaml:"remove_port"`
}

// AssociationQueryConfig 任务关联查询配置
type AssociationQueryConfig struct {
	CacheEnabled bool          `yaml:"cache_enabled"`
	CacheTTL     time.Duration `yaml:"cache_ttl"`
	BatchSize    int           `yaml:"batch_size"`
}

// PrometheusIntegrationConfig Prometheus集成配置
type PrometheusIntegrationConfig struct {
	WebhookProcessing WebhookProcessingConfig `yaml:"webhook_processing"`
	AlertEnhancement  AlertEnhancementConfig  `yaml:"alert_enhancement"`
}

// WebhookProcessingConfig WebHook处理配置
type WebhookProcessingConfig struct {
	Async     bool `yaml:"async"`
	QueueSize int  `yaml:"queue_size"`
	Workers   int  `yaml:"workers"`
}

// AlertEnhancementConfig 告警增强配置
type AlertEnhancementConfig struct {
	AddDeviceAnnotations bool `yaml:"add_device_annotations"`
	AddTaskContext       bool `yaml:"add_task_context"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Redis  RedisConfig  `yaml:"redis"`
	Memory MemoryConfig `yaml:"memory"`
}

// RedisConfig Redis缓存配置
type RedisConfig struct {
	Enabled  bool   `yaml:"enabled"`
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	DB       int    `yaml:"db"`
	Password string `yaml:"password"`
}

// MemoryConfig 内存缓存配置
type MemoryConfig struct {
	Enabled bool          `yaml:"enabled"`
	MaxSize int           `yaml:"max_size"`
	TTL     time.Duration `yaml:"ttl"`
}

// DeviceMappingConfig 设备映射配置
type DeviceMappingConfig struct {
	DeviceIDMapping        map[string]DeviceMapping     `yaml:"device_id_mapping"`
	GlobalDeviceExtraction GlobalDeviceExtractionConfig `yaml:"global_device_extraction"`
}

// DeviceMapping 设备映射规则
type DeviceMapping struct {
	SourcePattern       string   `yaml:"source_pattern"`
	DeviceIDLabels      []string `yaml:"device_id_labels"`
	InstancePattern     string   `yaml:"instance_pattern"`
	InstanceDeviceGroup int      `yaml:"instance_device_group"`
}

// GlobalDeviceExtractionConfig 全局设备提取配置
type GlobalDeviceExtractionConfig struct {
	PriorityLabels  []string              `yaml:"priority_labels"`
	InstanceParsing InstanceParsingConfig `yaml:"instance_parsing"`
	FallbackLabels  []string              `yaml:"fallback_labels"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 设置默认值
	setDefaultConfig(&config)

	return &config, nil
}

// LoadDeviceMappingConfig 加载设备映射配置
func LoadDeviceMappingConfig(configPath string) (*DeviceMappingConfig, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取设备映射配置文件失败: %w", err)
	}

	var config DeviceMappingConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析设备映射配置文件失败: %w", err)
	}

	return &config, nil
}

// setDefaultConfig 设置默认配置值
func setDefaultConfig(config *Config) {
	// 任务监控默认配置
	if config.TaskMonitoring.ExtendedMonitoring.Duration == 0 {
		config.TaskMonitoring.ExtendedMonitoring.Duration = time.Hour
	}
	if config.TaskMonitoring.SessionManagement.CleanupInterval == 0 {
		config.TaskMonitoring.SessionManagement.CleanupInterval = 24 * time.Hour
	}
	if config.TaskMonitoring.SessionManagement.MaxSessions == 0 {
		config.TaskMonitoring.SessionManagement.MaxSessions = 1000
	}

	// 告警关联默认配置
	if config.AlertAssociation.AssociationQuery.CacheTTL == 0 {
		config.AlertAssociation.AssociationQuery.CacheTTL = 5 * time.Minute
	}
	if config.AlertAssociation.AssociationQuery.BatchSize == 0 {
		config.AlertAssociation.AssociationQuery.BatchSize = 100
	}

	// Prometheus集成默认配置
	if config.PrometheusIntegration.WebhookProcessing.QueueSize == 0 {
		config.PrometheusIntegration.WebhookProcessing.QueueSize = 1000
	}
	if config.PrometheusIntegration.WebhookProcessing.Workers == 0 {
		config.PrometheusIntegration.WebhookProcessing.Workers = 5
	}

	// 缓存默认配置
	if config.Cache.Memory.MaxSize == 0 {
		config.Cache.Memory.MaxSize = 1000
	}
	if config.Cache.Memory.TTL == 0 {
		config.Cache.Memory.TTL = 5 * time.Minute
	}
}
