package alert

import (
	"database/sql"
	"dcimonitor/internal/utils/timeutil"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

// CacheEntry 缓存条目
type CacheEntry struct {
	Data      *TaskAssociationResult
	ExpiresAt time.Time
}

// TaskDAO 提供对任务监控会话数据的访问
type TaskDAO struct {
	db     *sql.DB
	logger *zap.Logger
	// 缓存相关字段
	cache  *sync.Map
	config *Config
}

// NewTaskDAO 创建一个新的TaskDAO实例
func NewTaskDAO(db *sql.DB, logger *zap.Logger, config *Config) *TaskDAO {
	dao := &TaskDAO{
		db:     db,
		logger: logger,
		cache:  &sync.Map{},
		config: config,
	}

	// 如果启用了缓存，启动缓存清理器
	if config != nil && config.AlertAssociation.AssociationQuery.CacheEnabled {
		go dao.startCacheCleaner()
	}

	return dao
}

// startCacheCleaner 启动缓存清理器
func (dao *TaskDAO) startCacheCleaner() {
	ticker := time.NewTicker(5 * time.Minute) // 每5分钟清理一次过期缓存
	defer ticker.Stop()

	for range ticker.C {
		dao.cleanExpiredCache()
	}
}

// cleanExpiredCache 清理过期缓存
func (dao *TaskDAO) cleanExpiredCache() {
	now := time.Now()
	dao.cache.Range(func(key, value interface{}) bool {
		if entry, ok := value.(*CacheEntry); ok {
			if now.After(entry.ExpiresAt) {
				dao.cache.Delete(key)
			}
		}
		return true
	})
}

// getFromCache 从缓存获取数据
func (dao *TaskDAO) getFromCache(deviceID string) (*TaskAssociationResult, bool) {
	if dao.config == nil || !dao.config.AlertAssociation.AssociationQuery.CacheEnabled {
		return nil, false
	}

	if value, ok := dao.cache.Load(deviceID); ok {
		entry := value.(*CacheEntry)
		if time.Now().Before(entry.ExpiresAt) {
			return entry.Data, true
		}
		// 过期则删除
		dao.cache.Delete(deviceID)
	}
	return nil, false
}

// setToCache 设置数据到缓存
func (dao *TaskDAO) setToCache(deviceID string, result *TaskAssociationResult) {
	if dao.config == nil || !dao.config.AlertAssociation.AssociationQuery.CacheEnabled {
		return
	}

	ttl := dao.config.AlertAssociation.AssociationQuery.CacheTTL
	if ttl == 0 {
		ttl = 5 * time.Minute // 默认5分钟TTL
	}

	entry := &CacheEntry{
		Data:      result,
		ExpiresAt: time.Now().Add(ttl),
	}
	dao.cache.Store(deviceID, entry)
}

// CreateTaskSession 创建任务监控会话
func (dao *TaskDAO) CreateTaskSession(session *TaskSession) error {
	if session.SessionID == "" {
		session.SessionID = uuid.New().String()
	}

	query := `
		INSERT INTO monitor_network_auto_task_monitoring_sessions (
			session_id, task_id, device_ids, start_time, end_time, status, created_at
		) VALUES (?, ?, ?, ?, ?, ?, ?)
	`

	now := timeutil.NowInLocalTime()
	session.CreatedAt = now

	_, err := dao.db.Exec(
		query,
		session.SessionID, session.TaskID, session.DeviceIDs, session.StartTime,
		session.EndTime, session.Status, now,
	)

	if err != nil {
		dao.logger.Error("创建任务监控会话失败", zap.Error(err),
			zap.String("task_id", session.TaskID), zap.String("session_id", session.SessionID))
		return err
	}

	return nil
}

// UpdateTaskSession 更新任务监控会话
func (dao *TaskDAO) UpdateTaskSession(session *TaskSession) error {
	query := `
		UPDATE monitor_network_auto_task_monitoring_sessions SET
			device_ids = ?, start_time = ?, end_time = ?, status = ?
		WHERE session_id = ?
	`

	_, err := dao.db.Exec(
		query,
		session.DeviceIDs, session.StartTime, session.EndTime, session.Status,
		session.SessionID,
	)

	if err != nil {
		dao.logger.Error("更新任务监控会话失败", zap.Error(err),
			zap.String("session_id", session.SessionID))
		return err
	}

	return nil
}

// GetTaskSessionByID 根据会话ID获取任务监控会话
func (dao *TaskDAO) GetTaskSessionByID(sessionID string) (*TaskSession, error) {
	query := `
		SELECT session_id, task_id, device_ids, start_time, end_time, status, created_at
		FROM monitor_network_auto_task_monitoring_sessions
		WHERE session_id = ?
	`

	var session TaskSession
	err := dao.db.QueryRow(query, sessionID).Scan(
		&session.SessionID, &session.TaskID, &session.DeviceIDs, &session.StartTime,
		&session.EndTime, &session.Status, &session.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		dao.logger.Error("获取任务监控会话失败", zap.Error(err), zap.String("session_id", sessionID))
		return nil, err
	}

	return &session, nil
}

// GetTaskSessionByTaskID 根据任务ID获取任务监控会话
func (dao *TaskDAO) GetTaskSessionByTaskID(taskID string) (*TaskSession, error) {
	query := `
		SELECT session_id, task_id, device_ids, start_time, end_time, status, created_at
		FROM monitor_network_auto_task_monitoring_sessions
		WHERE task_id = ?
		ORDER BY created_at DESC
		LIMIT 1
	`

	var session TaskSession
	err := dao.db.QueryRow(query, taskID).Scan(
		&session.SessionID, &session.TaskID, &session.DeviceIDs, &session.StartTime,
		&session.EndTime, &session.Status, &session.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		dao.logger.Error("根据任务ID获取任务监控会话失败", zap.Error(err), zap.String("task_id", taskID))
		return nil, err
	}

	return &session, nil
}

// GetTaskByDeviceWithExtendedMonitoring 查询设备相关的任务(包含延展监测)
// 实现17号文档中的任务关联查询逻辑
func (dao *TaskDAO) GetTaskByDeviceWithExtendedMonitoring(deviceID string) (*TaskAssociationResult, error) {
	if deviceID == "" {
		return &TaskAssociationResult{}, nil
	}

	// 尝试从缓存获取
	if result, ok := dao.getFromCache(deviceID); ok {
		RecordCacheHit("task_association", "hit")
		return result, nil
	}
	RecordCacheHit("task_association", "miss")

	// 查询活跃任务会话
	activeTask, err := dao.queryActiveTaskSession(deviceID)
	if err != nil {
		dao.logger.Error("查询活跃任务会话失败", zap.Error(err), zap.String("device_id", deviceID))
		return &TaskAssociationResult{}, err
	}

	if activeTask != nil {
		result := &TaskAssociationResult{
			TaskID:    activeTask.TaskID,
			AlertType: "active",
		}
		dao.setToCache(deviceID, result)
		return result, nil
	}

	// 查询任务结束后延展监测期
	extendedTask, err := dao.queryExtendedMonitoringSession(deviceID)
	if err != nil {
		dao.logger.Error("查询延展监测会话失败", zap.Error(err), zap.String("device_id", deviceID))
		return &TaskAssociationResult{}, err
	}

	if extendedTask != nil {
		result := &TaskAssociationResult{
			TaskID:    extendedTask.TaskID,
			AlertType: "post_task",
		}
		dao.setToCache(deviceID, result)
		return result, nil
	}

	result := &TaskAssociationResult{}
	dao.setToCache(deviceID, result)
	return result, nil
}

// queryActiveTaskSession 查询活跃任务会话
func (dao *TaskDAO) queryActiveTaskSession(deviceID string) (*TaskSession, error) {
	query := `
		SELECT session_id, task_id, device_ids, start_time, end_time, status, created_at
		FROM monitor_network_auto_task_monitoring_sessions 
		WHERE status = 'active' 
		AND JSON_CONTAINS(device_ids, JSON_QUOTE(?))
		AND start_time <= NOW()
		LIMIT 1
	`

	var session TaskSession
	err := dao.db.QueryRow(query, deviceID).Scan(
		&session.SessionID, &session.TaskID, &session.DeviceIDs, &session.StartTime,
		&session.EndTime, &session.Status, &session.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		dao.logger.Error("查询活跃任务会话失败", zap.Error(err), zap.String("device_id", deviceID))
		return nil, err
	}

	return &session, nil
}

// queryExtendedMonitoringSession 查询延展监测期任务会话
func (dao *TaskDAO) queryExtendedMonitoringSession(deviceID string) (*TaskSession, error) {
	// 获取延展监测配置
	duration := time.Hour // 默认1小时
	if dao.config != nil && dao.config.TaskMonitoring.ExtendedMonitoring.Duration > 0 {
		duration = dao.config.TaskMonitoring.ExtendedMonitoring.Duration
	}

	// 将duration转换为小时数，用于SQL查询
	hours := int(duration.Hours())

	query := `
		SELECT session_id, task_id, device_ids, start_time, end_time, status, created_at
		FROM monitor_network_auto_task_monitoring_sessions 
		WHERE status = 'completed' 
		AND JSON_CONTAINS(device_ids, JSON_QUOTE(?))
		AND end_time IS NOT NULL
		AND NOW() BETWEEN end_time AND DATE_ADD(end_time, INTERVAL ? HOUR)
		LIMIT 1
	`

	var session TaskSession
	err := dao.db.QueryRow(query, deviceID, hours).Scan(
		&session.SessionID, &session.TaskID, &session.DeviceIDs, &session.StartTime,
		&session.EndTime, &session.Status, &session.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		dao.logger.Error("查询延展监测会话失败", zap.Error(err), zap.String("device_id", deviceID))
		return nil, err
	}

	return &session, nil
}

// StartTaskMonitoring 启动任务监控
func (dao *TaskDAO) StartTaskMonitoring(taskID string, deviceIDs []string) (*TaskSession, error) {
	// 检查任务ID是否已存在
	existingSession, err := dao.GetTaskSessionByTaskID(taskID)
	if err != nil {
		return nil, err
	}

	if existingSession != nil {
		// 如果任务已存在，返回错误
		return nil, fmt.Errorf("任务ID %s 已存在，无法创建重复的监控会话", taskID)
	}

	session := &TaskSession{
		SessionID: uuid.New().String(),
		TaskID:    taskID,
		DeviceIDs: JSONArray(deviceIDs),
		StartTime: timeutil.NowInLocalTime(),
		Status:    "active",
	}

	err = dao.CreateTaskSession(session)
	if err != nil {
		return nil, err
	}

	return session, nil
}

// StopTaskMonitoring 停止任务监控
func (dao *TaskDAO) StopTaskMonitoring(taskID string, endMethod string) error {
	query := `
		UPDATE monitor_network_auto_task_monitoring_sessions SET
			end_time = ?, status = 'completed', end_method = ?
		WHERE task_id = ? AND status = 'active'
	`

	now := timeutil.NowInLocalTime()

	_, err := dao.db.Exec(query, now, endMethod, taskID)
	if err != nil {
		dao.logger.Error("停止任务监控失败", zap.Error(err), zap.String("task_id", taskID))
		return err
	}

	return nil
}

// ListTaskSessions 获取任务监控会话列表
func (dao *TaskDAO) ListTaskSessions(limit, offset int) ([]*TaskSession, error) {
	query := `
		SELECT session_id, task_id, device_ids, start_time, end_time, status, created_at
		FROM monitor_network_auto_task_monitoring_sessions
		ORDER BY created_at DESC
		LIMIT ? OFFSET ?
	`

	rows, err := dao.db.Query(query, limit, offset)
	if err != nil {
		dao.logger.Error("查询任务监控会话列表失败", zap.Error(err))
		return nil, err
	}
	defer rows.Close()

	var sessions []*TaskSession
	for rows.Next() {
		var session TaskSession
		err := rows.Scan(
			&session.SessionID, &session.TaskID, &session.DeviceIDs, &session.StartTime,
			&session.EndTime, &session.Status, &session.CreatedAt,
		)
		if err != nil {
			dao.logger.Error("扫描任务监控会话记录失败", zap.Error(err))
			return nil, err
		}

		sessions = append(sessions, &session)
	}

	if err = rows.Err(); err != nil {
		dao.logger.Error("遍历任务监控会话记录失败", zap.Error(err))
		return nil, err
	}

	return sessions, nil
}
