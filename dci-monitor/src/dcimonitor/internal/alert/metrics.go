package alert

// metrics.go 提供统一的指标打点函数。
// 说明：
// - 当前实现为 no-op（空实现），用于在未集成 Prometheus/监控系统时避免编译/链接问题。
// - 业务侧可无条件调用这些函数；后续接入真实监控实现时，仅需替换本文件的函数体即可。

// RecordAlertProcessingDuration 记录告警处理相关步骤的耗时（单位：秒）。
// 参数：
// - source: 告警来源（例如 "prometheus"）
// - status: 步骤/类别（例如 "total"、"extract_device_id"、"task_query"）
// - phase: 流程阶段（例如 "webhook"、"processing"、"update"）
// - duration: 耗时（秒）
// 说明：当前为 no-op，占位实现。
func RecordAlertProcessingDuration(source, status, phase string, duration float64) {}

// RecordTaskAssociation 记录任务关联查询的结果。
// 参数：
// - result: 结果（例如 "success"、"no_match"、"error"）
// - alertType: 告警归属类型（例如 "active"、"post_task"、"unknown"）
// 说明：当前为 no-op，占位实现。
func RecordTaskAssociation(result, alertType string) {}

// RecordDeviceIdExtraction 记录设备ID提取的结果。
// 参数：
// - result: 结果（例如 "success"、"failed"）
// - method: 提取方法/场景（例如 "configured"、"configured_update"）
// 说明：当前为 no-op，占位实现。
func RecordDeviceIdExtraction(result, method string) {}

// RecordWebhookProcessing 记录 WebHook 处理结果。
// 参数：
// - source: 告警来源（例如 "prometheus"）
// - status: 处理状态（例如 "success"、"parse_error"）
// 说明：当前为 no-op，占位实现。
func RecordWebhookProcessing(source, status string) {}

// RecordCacheHit 记录缓存命中情况。
// 参数：
// - cacheType: 缓存类别（例如 "task_association"）
// - result: 命中结果（"hit" 或 "miss"）
// 说明：当前为 no-op，占位实现。
func RecordCacheHit(cacheType, result string) {}

// UpdateActiveTaskSessions 设置当前活跃任务会话数量（Gauge）。
// 参数：
// - count: 活跃会话数量
// 说明：当前为 no-op，占位实现。
func UpdateActiveTaskSessions(count float64) {}

// UpdateAlertQueueLength 设置当前告警处理队列长度（Gauge）。
// 参数：
// - length: 队列长度
// 说明：当前为 no-op，占位实现。
func UpdateAlertQueueLength(length float64) {}

// RecordExtendedMonitoringMatch 记录延展监测（任务结束后）匹配计数。
// 参数：
// - deviceType: 设备类型或自定义维度（例如 "network_device"）
// 说明：当前为 no-op，占位实现。
func RecordExtendedMonitoringMatch(deviceType string) {}
