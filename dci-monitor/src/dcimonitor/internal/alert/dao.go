package alert

import (
	"database/sql"
	"dcimonitor/internal/utils/timeutil"
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

// DAO 提供对告警数据的访问
type DAO struct {
	db     *sql.DB
	logger *zap.Logger
}

// NewDAO 创建一个新的DAO实例
func NewDAO(db *sql.DB, logger *zap.Logger) *DAO {
	return &DAO{
		db:     db,
		logger: logger,
	}
}

// CreateAlert 创建一个新的告警记录
func (dao *DAO) CreateAlert(alert *Alert) error {
	if alert.ID == "" {
		alert.ID = uuid.New().String()
	}

	query := `
		INSERT INTO monitor_alert (
			id, name, source, level, status, description, 
			labels, annotations, starts_at, ends_at, notification_due_at, resolved_at, updated_at, 
			generator_url, rule_id, acknowledged_by, acknowledged_at, note, device_id
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	// 使用本地时区的当前时间作为更新时间
	now := timeutil.NowInLocalTime()

	_, err := dao.db.Exec(
		query,
		alert.ID, alert.Name, alert.Source, alert.Level, alert.Status, alert.Description,
		alert.Labels, alert.Annotations, alert.StartsAt, alert.EndsAt, alert.NotificationDueAt, alert.ResolvedAt, now,
		alert.GeneratorURL, alert.RuleID, alert.AcknowledgedBy, alert.AcknowledgedAt, alert.Note, alert.DeviceID,
	)

	if err != nil {
		dao.logger.Error("创建告警记录失败", zap.Error(err), zap.String("alert_id", alert.ID))
		return err
	}

	return nil
}

// UpdateAlert 更新现有的告警记录
func (dao *DAO) UpdateAlert(alert *Alert) error {
	query := `
		UPDATE monitor_alert SET
			name = ?, source = ?, level = ?, status = ?, description = ?,
			labels = ?, annotations = ?, starts_at = ?, ends_at = ?, notification_due_at = ?, resolved_at = ?, updated_at = ?,
			generator_url = ?, rule_id = ?, acknowledged_by = ?, acknowledged_at = ?, note = ?, device_id = ?
		WHERE id = ?
	`

	// 使用本地时区的当前时间作为更新时间
	now := timeutil.NowInLocalTime()

	_, err := dao.db.Exec(
		query,
		alert.Name, alert.Source, alert.Level, alert.Status, alert.Description,
		alert.Labels, alert.Annotations, alert.StartsAt, alert.EndsAt, alert.NotificationDueAt, alert.ResolvedAt, now,
		alert.GeneratorURL, alert.RuleID, alert.AcknowledgedBy, alert.AcknowledgedAt, alert.Note, alert.DeviceID,
		alert.ID,
	)

	if err != nil {
		dao.logger.Error("更新告警记录失败", zap.Error(err), zap.String("alert_id", alert.ID))
		return err
	}

	return nil
}

// GetAlertByID 根据ID获取告警记录
func (dao *DAO) GetAlertByID(id string) (*Alert, error) {
	query := `
		SELECT 
			id, name, source, level, status, description, 
			labels, annotations, starts_at, ends_at, notification_due_at, resolved_at, updated_at, 
			generator_url, rule_id, acknowledged_by, acknowledged_at, note, device_id
		FROM monitor_alert
		WHERE id = ?
	`

	var alert Alert
	err := dao.db.QueryRow(query, id).Scan(
		&alert.ID, &alert.Name, &alert.Source, &alert.Level, &alert.Status, &alert.Description,
		&alert.Labels, &alert.Annotations, &alert.StartsAt, &alert.EndsAt, &alert.NotificationDueAt, &alert.ResolvedAt, &alert.UpdatedAt,
		&alert.GeneratorURL, &alert.RuleID, &alert.AcknowledgedBy, &alert.AcknowledgedAt, &alert.Note, &alert.DeviceID,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		dao.logger.Error("获取告警记录失败", zap.Error(err), zap.String("alert_id", id))
		return nil, err
	}

	return &alert, nil
}

// GetAlertByFingerprintAndSource 根据指纹和来源获取告警记录
func (dao *DAO) GetAlertByFingerprintAndSource(fingerprint string, source string) (*Alert, error) {
	query := `
		SELECT 
			id, name, source, level, status, description, 
			labels, annotations, starts_at, ends_at, notification_due_at, resolved_at, updated_at, 
			generator_url, rule_id, acknowledged_by, acknowledged_at, note, device_id
		FROM monitor_alert
		WHERE JSON_EXTRACT(labels, '$.fingerprint') = ? AND source = ? AND status != 'resolved'
		LIMIT 1
	`

	var alert Alert
	err := dao.db.QueryRow(query, fingerprint, source).Scan(
		&alert.ID, &alert.Name, &alert.Source, &alert.Level, &alert.Status, &alert.Description,
		&alert.Labels, &alert.Annotations, &alert.StartsAt, &alert.EndsAt, &alert.NotificationDueAt, &alert.ResolvedAt, &alert.UpdatedAt,
		&alert.GeneratorURL, &alert.RuleID, &alert.AcknowledgedBy, &alert.AcknowledgedAt, &alert.Note, &alert.DeviceID,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		dao.logger.Error("根据指纹获取告警记录失败", zap.Error(err), zap.String("fingerprint", fingerprint))
		return nil, err
	}

	return &alert, nil
}

// ListAlerts 获取告警记录列表
func (dao *DAO) ListAlerts(filters map[string]interface{}, limit, offset int) ([]*Alert, error) {
	// 构建查询条件
	whereClause := ""
	var params []interface{}

	if len(filters) > 0 {
		whereClause = "WHERE "
		conditions := []string{}

		for key, value := range filters {
			switch key {
			case "source":
				conditions = append(conditions, "source = ?")
				params = append(params, value)
			case "level":
				conditions = append(conditions, "level = ?")
				params = append(params, value)
			case "status":
				conditions = append(conditions, "status = ?")
				params = append(params, value)
			case "device_id":
				conditions = append(conditions, "JSON_EXTRACT(labels, '$.device_id') = ?")
				params = append(params, value)
			case "start_time":
				conditions = append(conditions, "starts_at >= ?")
				params = append(params, value)
			case "end_time":
				conditions = append(conditions, "starts_at <= ?")
				params = append(params, value)
			}
		}

		if len(conditions) > 0 {
			whereClause += conditions[0]
			for i := 1; i < len(conditions); i++ {
				whereClause += fmt.Sprintf(" AND %s", conditions[i])
			}
		} else {
			whereClause = "" // 如果没有有效条件，则不使用WHERE子句
		}
	}

	// 构建完整查询
	query := fmt.Sprintf(`
		SELECT 
			id, name, source, level, status, description, 
			labels, annotations, starts_at, ends_at, notification_due_at, resolved_at, updated_at, 
			generator_url, rule_id, acknowledged_by, acknowledged_at, note, device_id
		FROM monitor_alert
		%s
		ORDER BY starts_at DESC
		LIMIT ? OFFSET ?
	`, whereClause)

	// 添加分页参数
	params = append(params, limit, offset)

	// 执行查询
	rows, err := dao.db.Query(query, params...)
	if err != nil {
		dao.logger.Error("查询告警记录列表失败", zap.Error(err))
		return nil, err
	}
	defer rows.Close()

	// 处理结果
	alerts := []*Alert{}
	for rows.Next() {
		var alert Alert
		err := rows.Scan(
			&alert.ID, &alert.Name, &alert.Source, &alert.Level, &alert.Status, &alert.Description,
			&alert.Labels, &alert.Annotations, &alert.StartsAt, &alert.EndsAt, &alert.NotificationDueAt, &alert.ResolvedAt, &alert.UpdatedAt,
			&alert.GeneratorURL, &alert.RuleID, &alert.AcknowledgedBy, &alert.AcknowledgedAt, &alert.Note, &alert.DeviceID,
		)
		if err != nil {
			dao.logger.Error("扫描告警记录失败", zap.Error(err))
			return nil, err
		}

		alerts = append(alerts, &alert)
	}

	if err = rows.Err(); err != nil {
		dao.logger.Error("遍历告警记录失败", zap.Error(err))
		return nil, err
	}

	return alerts, nil
}

// CountAlerts 计算符合条件的告警记录数量
func (dao *DAO) CountAlerts(filters map[string]interface{}) (int, error) {
	// 构建查询条件
	whereClause := ""
	var params []interface{}

	if len(filters) > 0 {
		whereClause = "WHERE "
		conditions := []string{}

		for key, value := range filters {
			switch key {
			case "source":
				conditions = append(conditions, "source = ?")
				params = append(params, value)
			case "level":
				conditions = append(conditions, "level = ?")
				params = append(params, value)
			case "status":
				conditions = append(conditions, "status = ?")
				params = append(params, value)
			case "device_id":
				conditions = append(conditions, "JSON_EXTRACT(labels, '$.device_id') = ?")
				params = append(params, value)
			case "start_time":
				conditions = append(conditions, "starts_at >= ?")
				params = append(params, value)
			case "end_time":
				conditions = append(conditions, "starts_at <= ?")
				params = append(params, value)
			}
		}

		if len(conditions) > 0 {
			whereClause += conditions[0]
			for i := 1; i < len(conditions); i++ {
				whereClause += fmt.Sprintf(" AND %s", conditions[i])
			}
		} else {
			whereClause = "" // 如果没有有效条件，则不使用WHERE子句
		}
	}

	// 构建完整查询
	query := fmt.Sprintf(`
		SELECT COUNT(*) FROM monitor_alert %s
	`, whereClause)

	// 执行查询
	var count int
	err := dao.db.QueryRow(query, params...).Scan(&count)
	if err != nil {
		dao.logger.Error("计算告警记录数量失败", zap.Error(err))
		return 0, err
	}

	return count, nil
}

// AcknowledgeAlert 确认告警
func (dao *DAO) AcknowledgeAlert(id, acknowledgedBy, note string) error {
	query := `
		UPDATE monitor_alert 
		SET status = 'acknowledged', acknowledged_by = ?, acknowledged_at = ?, note = ?, updated_at = ?
		WHERE id = ?
	`

	// 使用本地时区的当前时间
	now := timeutil.NowInLocalTime()
	_, err := dao.db.Exec(query, acknowledgedBy, now, note, now, id)
	if err != nil {
		dao.logger.Error("确认告警失败", zap.Error(err), zap.String("alert_id", id))
		return err
	}

	return nil
}

// ResolveAlert 解决告警
func (dao *DAO) ResolveAlert(id string) error {
	query := `
		UPDATE monitor_alert 
		SET status = 'resolved', ends_at = ?, resolved_at = ?, updated_at = ?
		WHERE id = ?
	`

	// 获取当前本地时区的时间
	now := timeutil.NowInLocalTime()
	_, err := dao.db.Exec(query, now, now, now, id)
	if err != nil {
		dao.logger.Error("解决告警失败", zap.Error(err), zap.String("alert_id", id))
		return err
	}

	return nil
}

// CreateAlertRule 创建告警规则
func (dao *DAO) CreateAlertRule(rule *AlertRule) error {
	if rule.ID == "" {
		rule.ID = uuid.New().String()
	}

	now := time.Now()
	rule.CreatedAt = now
	rule.UpdatedAt = now

	query := `
		INSERT INTO monitor_alert_rule (
			id, name, description, source, expression, level, 
			duration, labels, annotations, enabled, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := dao.db.Exec(
		query,
		rule.ID, rule.Name, rule.Description, rule.Source, rule.Expression, rule.Level,
		rule.Duration, rule.Labels, rule.Annotations, rule.Enabled, rule.CreatedAt, rule.UpdatedAt,
	)

	if err != nil {
		dao.logger.Error("创建告警规则失败", zap.Error(err), zap.String("rule_id", rule.ID))
		return err
	}

	return nil
}

// UpdateAlertRule 更新告警规则
func (dao *DAO) UpdateAlertRule(rule *AlertRule) error {
	rule.UpdatedAt = time.Now()

	query := `
		UPDATE monitor_alert_rule SET
			name = ?, description = ?, source = ?, expression = ?, level = ?,
			duration = ?, labels = ?, annotations = ?, enabled = ?, updated_at = ?
		WHERE id = ?
	`

	_, err := dao.db.Exec(
		query,
		rule.Name, rule.Description, rule.Source, rule.Expression, rule.Level,
		rule.Duration, rule.Labels, rule.Annotations, rule.Enabled, rule.UpdatedAt,
		rule.ID,
	)

	if err != nil {
		dao.logger.Error("更新告警规则失败", zap.Error(err), zap.String("rule_id", rule.ID))
		return err
	}

	return nil
}

// GetAlertRuleByID 根据ID获取告警规则
func (dao *DAO) GetAlertRuleByID(id string) (*AlertRule, error) {
	query := `
		SELECT 
			id, name, description, source, expression, level, 
			duration, labels, annotations, enabled, created_at, updated_at
		FROM monitor_alert_rule
		WHERE id = ?
	`

	var rule AlertRule
	err := dao.db.QueryRow(query, id).Scan(
		&rule.ID, &rule.Name, &rule.Description, &rule.Source, &rule.Expression, &rule.Level,
		&rule.Duration, &rule.Labels, &rule.Annotations, &rule.Enabled, &rule.CreatedAt, &rule.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		dao.logger.Error("获取告警规则失败", zap.Error(err), zap.String("rule_id", id))
		return nil, err
	}

	return &rule, nil
}

// GetAlertRuleByName 根据名称和来源获取告警规则
func (dao *DAO) GetAlertRuleByName(name, source string) (*AlertRule, error) {
	query := `
		SELECT 
			id, name, description, source, expression, level, 
			duration, labels, annotations, enabled, created_at, updated_at
		FROM monitor_alert_rule
		WHERE name = ? AND source = ?
	`

	var rule AlertRule
	err := dao.db.QueryRow(query, name, source).Scan(
		&rule.ID, &rule.Name, &rule.Description, &rule.Source, &rule.Expression, &rule.Level,
		&rule.Duration, &rule.Labels, &rule.Annotations, &rule.Enabled, &rule.CreatedAt, &rule.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		dao.logger.Error("根据名称获取告警规则失败", zap.Error(err), zap.String("name", name), zap.String("source", source))
		return nil, err
	}

	return &rule, nil
}

// ListAlertRules 获取告警规则列表
func (dao *DAO) ListAlertRules(source string, enabled *bool) ([]*AlertRule, error) {
	// 构建查询条件
	whereClause := ""
	var params []interface{}

	if source != "" {
		whereClause = "WHERE source = ?"
		params = append(params, source)

		if enabled != nil {
			whereClause += " AND enabled = ?"
			params = append(params, *enabled)
		}
	} else if enabled != nil {
		whereClause = "WHERE enabled = ?"
		params = append(params, *enabled)
	}

	// 构建完整查询
	query := fmt.Sprintf(`
		SELECT 
			id, name, description, source, expression, level, 
			duration, labels, annotations, enabled, created_at, updated_at
		FROM monitor_alert_rule
		%s
		ORDER BY name
	`, whereClause)

	// 执行查询
	rows, err := dao.db.Query(query, params...)
	if err != nil {
		dao.logger.Error("查询告警规则列表失败", zap.Error(err))
		return nil, err
	}
	defer rows.Close()

	// 处理结果
	rules := []*AlertRule{}
	for rows.Next() {
		var rule AlertRule
		err := rows.Scan(
			&rule.ID, &rule.Name, &rule.Description, &rule.Source, &rule.Expression, &rule.Level,
			&rule.Duration, &rule.Labels, &rule.Annotations, &rule.Enabled, &rule.CreatedAt, &rule.UpdatedAt,
		)
		if err != nil {
			dao.logger.Error("扫描告警规则失败", zap.Error(err))
			return nil, err
		}
		rules = append(rules, &rule)
	}

	if err = rows.Err(); err != nil {
		dao.logger.Error("遍历告警规则失败", zap.Error(err))
		return nil, err
	}

	return rules, nil
}

// DeleteAlertRule 删除告警规则
func (dao *DAO) DeleteAlertRule(id string) error {
	query := "DELETE FROM monitor_alert_rule WHERE id = ?"

	_, err := dao.db.Exec(query, id)
	if err != nil {
		dao.logger.Error("删除告警规则失败", zap.Error(err), zap.String("rule_id", id))
		return err
	}

	return nil
}

// ToggleAlertRule 启用/禁用告警规则
func (dao *DAO) ToggleAlertRule(id string, enabled bool) error {
	query := "UPDATE monitor_alert_rule SET enabled = ?, updated_at = ? WHERE id = ?"

	_, err := dao.db.Exec(query, enabled, time.Now(), id)
	if err != nil {
		dao.logger.Error("切换告警规则状态失败", zap.Error(err), zap.String("rule_id", id), zap.Bool("enabled", enabled))
		return err
	}

	return nil
}

// CreateTaskAlertAssociation 创建任务告警关联记录
func (dao *DAO) CreateTaskAlertAssociation(association *TaskAlertAssociation) error {
	if association.ID == "" {
		association.ID = uuid.New().String()
	}

	query := `
		INSERT INTO monitor_task_alert_associations (
			id, task_id, alert_id, association_type, device_id, created_at
		) VALUES (?, ?, ?, ?, ?, ?)
	`

	now := timeutil.NowInLocalTime()
	association.CreatedAt = now

	_, err := dao.db.Exec(
		query,
		association.ID, association.TaskID, association.AlertID, association.AssociationType, association.DeviceID, now,
	)

	if err != nil {
		dao.logger.Error("创建任务告警关联记录失败", zap.Error(err),
			zap.String("task_id", association.TaskID), zap.String("alert_id", association.AlertID))
		return err
	}

	return nil
}

// BatchCreateTaskAlertAssociations 批量创建任务告警关联记录
func (dao *DAO) BatchCreateTaskAlertAssociations(associations []*TaskAlertAssociation) error {
	if len(associations) == 0 {
		return nil
	}

	tx, err := dao.db.Begin()
	if err != nil {
		dao.logger.Error("开启事务失败", zap.Error(err))
		return err
	}
	defer tx.Rollback()

	query := `
		INSERT INTO monitor_task_alert_associations (
			id, task_id, alert_id, association_type, device_id, created_at
		) VALUES (?, ?, ?, ?, ?, ?)
	`

	stmt, err := tx.Prepare(query)
	if err != nil {
		dao.logger.Error("准备批量插入语句失败", zap.Error(err))
		return err
	}
	defer stmt.Close()

	now := timeutil.NowInLocalTime()

	for _, association := range associations {
		if association.ID == "" {
			association.ID = uuid.New().String()
		}
		association.CreatedAt = now

		_, err = stmt.Exec(
			association.ID, association.TaskID, association.AlertID, association.AssociationType, association.DeviceID, now,
		)
		if err != nil {
			dao.logger.Error("批量插入任务告警关联记录失败", zap.Error(err),
				zap.String("task_id", association.TaskID), zap.String("alert_id", association.AlertID))
			return err
		}
	}

	if err = tx.Commit(); err != nil {
		dao.logger.Error("提交批量插入事务失败", zap.Error(err))
		return err
	}

	return nil
}

// GetTaskAlertAssociations 获取任务的告警关联记录
func (dao *DAO) GetTaskAlertAssociations(taskID string) ([]*TaskAlertAssociation, error) {
	query := `
		SELECT id, task_id, alert_id, association_type, created_at
		FROM monitor_task_alert_associations
		WHERE task_id = ?
		ORDER BY created_at DESC
	`

	rows, err := dao.db.Query(query, taskID)
	if err != nil {
		dao.logger.Error("查询任务告警关联记录失败", zap.Error(err), zap.String("task_id", taskID))
		return nil, err
	}
	defer rows.Close()

	var associations []*TaskAlertAssociation
	for rows.Next() {
		var association TaskAlertAssociation
		err := rows.Scan(
			&association.ID, &association.TaskID, &association.AlertID,
			&association.AssociationType, &association.CreatedAt,
		)
		if err != nil {
			dao.logger.Error("扫描任务告警关联记录失败", zap.Error(err))
			return nil, err
		}

		associations = append(associations, &association)
	}

	if err = rows.Err(); err != nil {
		dao.logger.Error("遍历任务告警关联记录失败", zap.Error(err))
		return nil, err
	}

	return associations, nil
}

// GetTaskRelatedAlerts 获取任务相关的告警列表(通过关联表JOIN查询)
func (dao *DAO) GetTaskRelatedAlerts(taskID string, limit, offset int) ([]*Alert, error) {
	query := `
		SELECT 
			a.id, a.name, a.source, a.level, a.status, a.description, 
			a.labels, a.annotations, a.starts_at, a.ends_at, a.notification_due_at, a.resolved_at, a.updated_at, 
			a.generator_url, a.rule_id, a.acknowledged_by, a.acknowledged_at, a.note, a.device_id,
			taa.association_type, taa.created_at as association_time
		FROM monitor_alert a
		INNER JOIN monitor_task_alert_associations taa ON a.id = taa.alert_id
		WHERE taa.task_id = ?
		ORDER BY a.starts_at DESC
		LIMIT ? OFFSET ?
	`

	rows, err := dao.db.Query(query, taskID, limit, offset)
	if err != nil {
		dao.logger.Error("查询任务相关告警失败", zap.Error(err), zap.String("task_id", taskID))
		return nil, err
	}
	defer rows.Close()

	var alerts []*Alert
	for rows.Next() {
		var alert Alert
		var associationType string
		var associationTime time.Time

		err := rows.Scan(
			&alert.ID, &alert.Name, &alert.Source, &alert.Level, &alert.Status, &alert.Description,
			&alert.Labels, &alert.Annotations, &alert.StartsAt, &alert.EndsAt, &alert.NotificationDueAt, &alert.ResolvedAt, &alert.UpdatedAt,
			&alert.GeneratorURL, &alert.RuleID, &alert.AcknowledgedBy, &alert.AcknowledgedAt, &alert.Note, &alert.DeviceID,
			&associationType, &associationTime,
		)
		if err != nil {
			dao.logger.Error("扫描任务相关告警记录失败", zap.Error(err))
			return nil, err
		}

		// 设置关联信息
		alert.TaskAssociation = &TaskAssociation{
			TaskID:          taskID,
			AssociationType: associationType,
			AssociationTime: associationTime,
		}

		alerts = append(alerts, &alert)
	}

	if err = rows.Err(); err != nil {
		dao.logger.Error("遍历任务相关告警记录失败", zap.Error(err))
		return nil, err
	}

	return alerts, nil
}

// CountTaskRelatedAlerts 统计任务相关的告警数量
func (dao *DAO) CountTaskRelatedAlerts(taskID string) (int, error) {
	query := `
		SELECT COUNT(*)
		FROM monitor_task_alert_associations
		WHERE task_id = ?
	`

	var count int
	err := dao.db.QueryRow(query, taskID).Scan(&count)
	if err != nil {
		dao.logger.Error("统计任务相关告警数量失败", zap.Error(err), zap.String("task_id", taskID))
		return 0, err
	}

	return count, nil
}
