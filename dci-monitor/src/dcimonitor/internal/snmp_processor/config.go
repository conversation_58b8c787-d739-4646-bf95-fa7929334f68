package snmp_processor

import (
	"fmt"
	"os"
	"strings"

	"github.com/spf13/viper"
)

// Config 配置结构体
type Config struct {
	// 基本配置
	ServiceName string `yaml:"service_name" mapstructure:"service_name"`
	Version     string `yaml:"version" mapstructure:"version"`

	// 处理器配置
	Processor ProcessorConfig `yaml:"processor" mapstructure:"processor"`

	// 映射器配置 (包含缓存设置)
	Mapper MapperConfig `yaml:"mapper" mapstructure:"mapper"`

	// Kafka配置
	Kafka KafkaConfig `yaml:"kafka" mapstructure:"kafka"`

	// 数据库配置
	Database DatabaseConfig `yaml:"database" mapstructure:"database"`

	// Prometheus配置
	Prometheus PrometheusConfig `yaml:"prometheus" mapstructure:"prometheus"`

	// 日志配置
	Logger LoggerConfig `yaml:"logger" mapstructure:"logger"`
}

// ProcessorConfig 处理器配置
type ProcessorConfig struct {
	MetricPrefix string `yaml:"metric_prefix" mapstructure:"metric_prefix"`

	// 流量指标配置
	EnableInOctets  bool `yaml:"enable_in_octets" mapstructure:"enable_in_octets"`
	EnableOutOctets bool `yaml:"enable_out_octets" mapstructure:"enable_out_octets"`
	EnableErrors    bool `yaml:"enable_errors" mapstructure:"enable_errors"`
	EnableDiscards  bool `yaml:"enable_discards" mapstructure:"enable_discards"`

	// 设备状态指标配置
	EnableDeviceCPU    bool `yaml:"enable_device_cpu" mapstructure:"enable_device_cpu"`
	EnableDeviceMemory bool `yaml:"enable_device_memory" mapstructure:"enable_device_memory"`

	// 接口状态指标配置
	EnableInterfaceStatus bool `yaml:"enable_interface_status" mapstructure:"enable_interface_status"`

	// 标签配置
	MetricLabels map[string]string `yaml:"metric_labels" mapstructure:"metric_labels"`
}

// MapperConfig 映射器配置
type MapperConfig struct {
	CacheTTLMinutes int `yaml:"cache_ttl_minutes" mapstructure:"cache_ttl_minutes"`
}

// KafkaConfig Kafka配置
type KafkaConfig struct {
	Brokers       []string      `yaml:"brokers" mapstructure:"brokers"`
	Topics        []string      `yaml:"topics" mapstructure:"topics"`
	ConsumerGroup string        `yaml:"consumer_group" mapstructure:"consumer_group"`
	Security      KafkaSecurity `yaml:"security" mapstructure:"security"`
}

// KafkaSecurity Kafka安全配置
type KafkaSecurity struct {
	TLS  TLSConfig  `yaml:"tls" mapstructure:"tls"`
	SASL SASLConfig `yaml:"sasl" mapstructure:"sasl"`
}

// TLSConfig TLS配置
type TLSConfig struct {
	Enabled  bool   `yaml:"enabled" mapstructure:"enabled"`
	CAFile   string `yaml:"ca_file" mapstructure:"ca_file"`
	CertFile string `yaml:"cert_file" mapstructure:"cert_file"`
	KeyFile  string `yaml:"key_file" mapstructure:"key_file"`
}

// SASLConfig SASL配置
type SASLConfig struct {
	Enabled   bool   `yaml:"enabled" mapstructure:"enabled"`
	Mechanism string `yaml:"mechanism" mapstructure:"mechanism"`
	Username  string `yaml:"username" mapstructure:"username"`
	Password  string `yaml:"password" mapstructure:"password"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver          string `yaml:"driver" mapstructure:"driver"`
	Host            string `yaml:"host" mapstructure:"host"`
	Port            int    `yaml:"port" mapstructure:"port"`
	Username        string `yaml:"username" mapstructure:"username"`
	Password        string `yaml:"password" mapstructure:"password"`
	Database        string `yaml:"database" mapstructure:"database"`
	MaxOpenConns    int    `yaml:"max_open_conns" mapstructure:"max_open_conns"`
	MaxIdleConns    int    `yaml:"max_idle_conns" mapstructure:"max_idle_conns"`
	ConnMaxLifetime int    `yaml:"conn_max_lifetime" mapstructure:"conn_max_lifetime"` // 秒
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	switch strings.ToLower(c.Driver) {
	case "mysql":
		return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			c.Username, c.Password, c.Host, c.Port, c.Database)
	default:
		return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s",
			c.Username, c.Password, c.Host, c.Port, c.Database)
	}
}

// PrometheusConfig Prometheus配置
type PrometheusConfig struct {
	MetricsPath string `yaml:"metrics_path" mapstructure:"metrics_path"`
	MetricsPort int    `yaml:"metrics_port" mapstructure:"metrics_port"`
	Server      string `yaml:"server" mapstructure:"server"`
}

// LoggerConfig 日志配置
type LoggerConfig struct {
	Level    string `yaml:"level" mapstructure:"level"`
	Encoding string `yaml:"encoding" mapstructure:"encoding"`
	Dir      string `yaml:"dir" mapstructure:"dir"`
	MaxSize  int    `yaml:"maxSize" mapstructure:"maxSize"`
	MaxAge   int    `yaml:"maxAge" mapstructure:"maxAge"`
	Compress bool   `yaml:"compress" mapstructure:"compress"`
}

// LoadConfig 从文件加载配置 (使用Viper)
func LoadConfig(filename string) (*Config, error) {
	viper.SetConfigFile(filename)
	viper.SetConfigType("yaml")

	// 设置所有可有默认值的配置项
	viper.SetDefault("processor.metric_prefix", "dci_snmp_")
	viper.SetDefault("processor.enable_in_octets", true)
	viper.SetDefault("processor.enable_out_octets", true)
	viper.SetDefault("processor.enable_errors", true)
	viper.SetDefault("processor.enable_discards", true)
	viper.SetDefault("processor.enable_device_cpu", true)
	viper.SetDefault("processor.enable_device_memory", true)
	viper.SetDefault("processor.enable_interface_status", true)
	viper.SetDefault("mapper.cache_ttl_minutes", 5)
	viper.SetDefault("prometheus.metrics_path", "/metrics")
	viper.SetDefault("prometheus.metrics_port", 9090)
	viper.SetDefault("database.max_open_conns", 10)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.conn_max_lifetime", 3600)
	viper.SetDefault("logger.level", "info")
	viper.SetDefault("logger.encoding", "json")
	viper.SetDefault("logger.dir", "./logs")
	viper.SetDefault("logger.maxSize", 100)
	viper.SetDefault("logger.maxAge", 30)
	viper.SetDefault("logger.compress", true)

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		// 即便文件不存在，只要关键配置能从环境变量等其他地方获取，也可能成功
		// 但在此场景下，我们强依赖于文件
		return nil, fmt.Errorf("使用Viper读取配置文件失败: %w", err)
	}

	// 将配置解析到结构体中
	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("使用Viper解析配置文件失败: %w", err)
	}

	// 从环境变量中读取Kafka密码以覆盖配置文件中的值
	if password := os.Getenv("KAFKA_SASL_PASSWORD"); password != "" {
		config.Kafka.Security.SASL.Password = password
	}

	// 环境变量也可以覆盖其他配置，例如：
	// export KAFKA_BROKERS="host1:9092,host2:9092"
	if brokers := os.Getenv("KAFKA_BROKERS"); brokers != "" {
		config.Kafka.Brokers = strings.Split(brokers, ",")
	}

	return &config, nil
}

// Validate 验证配置 (现在只负责验证，不再设置默认值)
func (c *Config) Validate() error {
	// 验证Kafka配置
	if len(c.Kafka.Brokers) == 0 {
		return fmt.Errorf("未配置Kafka代理服务器")
	}
	if len(c.Kafka.Topics) == 0 {
		return fmt.Errorf("未配置Kafka主题")
	}
	if c.Kafka.ConsumerGroup == "" {
		return fmt.Errorf("未配置Kafka消费者组")
	}

	// 验证数据库配置
	if c.Database.Driver == "" {
		return fmt.Errorf("未配置数据库驱动")
	}
	if c.Database.Host == "" {
		return fmt.Errorf("未配置数据库主机")
	}
	if c.Database.Port == 0 {
		return fmt.Errorf("未配置数据库端口")
	}
	if c.Database.Username == "" {
		return fmt.Errorf("未配置数据库用户名")
	}
	if c.Database.Database == "" {
		return fmt.Errorf("未配置数据库名称")
	}

	// 验证并设置映射器默认值
	// 不再需要，Viper已处理
	// if c.Mapper.CacheTTLMinutes <= 0 {
	// 	c.Mapper.CacheTTLMinutes = 5 // 默认缓存5分钟
	// }

	// 验证Prometheus配置
	// 不再需要，Viper已处理
	// if c.Prometheus.MetricsPath == "" {
	// 	c.Prometheus.MetricsPath = "/metrics"
	// }
	// if c.Prometheus.MetricsPort == 0 {
	// 	c.Prometheus.MetricsPort = 9090
	// }

	return nil
}
