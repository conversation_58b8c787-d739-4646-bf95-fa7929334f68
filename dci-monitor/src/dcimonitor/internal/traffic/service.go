package traffic

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"go.uber.org/zap"

	"dcimonitor/internal/models"
)

// formatLabels 将标签映射转换为Prometheus查询字符串格式
func formatLabels(labels map[string]string) string {
	if len(labels) == 0 {
		return ""
	}

	var result string
	first := true
	for k, v := range labels {
		if v == "" {
			continue // 跳过空值
		}
		if !first {
			result += ","
		}
		result += fmt.Sprintf(`%s="%s"`, k, v)
		first = false
	}
	return result
}

// Service 定义了流量相关业务逻辑
type Service struct {
	mappingDAO PortMappingDAO
	trafficDAO TrafficDAO
	dao        *DAO
	logger     *zap.Logger
}

// NewService 创建一个新的Service实例
func NewService(dao *DAO, mappingDAO PortMappingDAO, trafficDAO TrafficDAO, logger *zap.Logger) *Service {
	return &Service{
		dao:        dao,
		mappingDAO: mappingDAO,
		trafficDAO: trafficDAO,
		logger:     logger,
	}
}

// GetAZLinkTraffic 实现了A-Z链路流量查询的业务逻辑
func (s *Service) GetAZLinkTraffic(ctx context.Context, params *AZLinkQueryParams) (*TrafficChartResponse, error) {
	s.logger.Info("GetAZLinkTraffic service called", zap.Any("params", params))

	// 解析端口ID (URL解码)
	var decodeErr error
	params.APortID, decodeErr = url.QueryUnescape(params.APortIDRaw)
	if decodeErr != nil {
		return nil, fmt.Errorf("无法解码 a_port_id: %w", decodeErr)
	}
	params.ZPortID, decodeErr = url.QueryUnescape(params.ZPortIDRaw)
	if decodeErr != nil {
		return nil, fmt.Errorf("无法解码 z_port_id: %w", decodeErr)
	}

	// 解析和设置时间参数
	var startTime, endTime time.Time
	var step time.Duration

	if params.TimeRange != "" {
		// 如果指定了预设时间范围
		endTime = time.Now().UTC()
		switch params.TimeRange {
		case "1h":
			startTime = endTime.Add(-1 * time.Hour)
			step = 1 * time.Minute
		case "6h":
			startTime = endTime.Add(-6 * time.Hour)
			step = 5 * time.Minute
		case "12h":
			startTime = endTime.Add(-12 * time.Hour)
			step = 10 * time.Minute
		case "1d":
			startTime = endTime.Add(-24 * time.Hour)
			step = 30 * time.Minute
		case "7d":
			startTime = endTime.Add(-7 * 24 * time.Hour)
			step = 2 * time.Hour
		case "30d":
			startTime = endTime.Add(-30 * 24 * time.Hour)
			step = 8 * time.Hour
		default:
			startTime = endTime.Add(-1 * time.Hour)
			step = 1 * time.Minute
		}
	} else {
		// 使用明确指定的时间范围
		startTime, endTime = params.StartTime, params.EndTime
		if endTime.IsZero() {
			endTime = time.Now().UTC()
		}
		if startTime.IsZero() {
			startTime = endTime.Add(-1 * time.Hour)
		}

		// 根据时间跨度自动确定步长
		duration := endTime.Sub(startTime)
		switch {
		case duration <= 2*time.Hour:
			step = 1 * time.Minute
		case duration <= 12*time.Hour:
			step = 5 * time.Minute
		case duration <= 24*time.Hour:
			step = 10 * time.Minute
		case duration <= 7*24*time.Hour:
			step = 30 * time.Minute
		default:
			step = 1 * time.Hour
		}
	}
	stepStr := step.String()

	// 用于并发获取数据的结果结构体
	type endpointResult struct {
		series []models.TimeValuePair
		err    error
	}

	aSideChan := make(chan endpointResult, 1)
	zSideChan := make(chan endpointResult, 1)

	// 并发获取A端出向流量
	go func() {
		deviceIP, err := s.mappingDAO.GetDeviceIPForPort(ctx, params.ASwitchID, params.APortID, params.VNI)
		if err != nil {
			aSideChan <- endpointResult{err: fmt.Errorf("设备%s A端端口%s VNI%s 参数验证失败: %w", params.ASwitchID, params.APortID, params.VNI, err)}
			return
		}
		labels := map[string]string{"device_ip": deviceIP, "vni": params.VNI}
		labelStr := formatLabels(labels)
		query := fmt.Sprintf(`rate(dci_snmp_flow_out_octets{%s}[%s]) * 8`, labelStr, stepStr)
		series, err := s.trafficDAO.QueryVectorOverTime(ctx, query, startTime, endTime, step)
		if err != nil {
			aSideChan <- endpointResult{err: fmt.Errorf("A端Prometheus查询失败: %w", err)}
			return
		}
		aSideChan <- endpointResult{series: series}
	}()

	// 并发获取Z端出向流量
	go func() {
		deviceIP, err := s.mappingDAO.GetDeviceIPForPort(ctx, params.ZSwitchID, params.ZPortID, params.VNI)
		if err != nil {
			zSideChan <- endpointResult{err: fmt.Errorf("设备%s Z端端口%s VNI%s 参数验证失败: %w", params.ZSwitchID, params.ZPortID, params.VNI, err)}
			return
		}
		labels := map[string]string{"device_ip": deviceIP, "vni": params.VNI}
		labelStr := formatLabels(labels)
		query := fmt.Sprintf(`rate(dci_snmp_flow_out_octets{%s}[%s]) * 8`, labelStr, stepStr)
		series, err := s.trafficDAO.QueryVectorOverTime(ctx, query, startTime, endTime, step)
		if err != nil {
			zSideChan <- endpointResult{err: fmt.Errorf("Z端Prometheus查询失败: %w", err)}
			return
		}
		zSideChan <- endpointResult{series: series}
	}()

	// 等待并处理结果
	aResult := <-aSideChan
	zResult := <-zSideChan

	if aResult.err != nil {
		s.logger.Error("获取A端流量失败", zap.Error(aResult.err))
		return nil, aResult.err
	}
	if zResult.err != nil {
		s.logger.Error("获取Z端流量失败", zap.Error(zResult.err))
		return nil, zResult.err
	}

	// 整合数据到响应模型
	// Prometheus保证在相同的查询参数下，时间戳是对齐的。我们以A端的时间戳为基准。
	timestamps := make([]string, len(aResult.series))
	aData := make([]float64, len(aResult.series))
	zData := make([]float64, len(zResult.series)) // 长度应与aResult相同

	for i, v := range aResult.series {
		timestamps[i] = v.Timestamp.Format(time.RFC3339)
		aData[i] = v.Value
	}
	// 假设zResult.series与aResult.series长度和时间戳对齐
	for i, v := range zResult.series {
		if i < len(zData) {
			zData[i] = v.Value
		}
	}

	reqID, _ := ctx.Value("requestID").(string)
	resp := &TrafficChartResponse{
		RequestID: reqID,
		QueryDetails: AZLinkQueryDetails{
			DeviceA:   params.ASwitchID,
			PortA:     params.APortID,
			DeviceZ:   params.ZSwitchID,
			PortZ:     params.ZPortID,
			VNI:       params.VNI,
			StartTime: startTime.Format(time.RFC3339),
			EndTime:   endTime.Format(time.RFC3339),
			Step:      step.String(),
		},
		ChartData: TrafficChartData{
			Unit:       "Mbps",
			Timestamps: timestamps,
			Series: []TrafficSeries{
				{Name: fmt.Sprintf("%s -> %s", params.ASwitchID, params.ZSwitchID), Data: aData},
				{Name: fmt.Sprintf("%s -> %s", params.ZSwitchID, params.ASwitchID), Data: zData},
			},
		},
	}

	return resp, nil
}

// GetSinglePortFlow 实现了单端口实时流量查询的逻辑
func (s *Service) GetSinglePortFlow(ctx context.Context, params *SinglePortFlowQueryParams) (*PortFlowResponse, error) {
	s.logger.Info("GetSinglePortFlow service called", zap.Any("params", params))

	// 根据Prometheus实际数据结构构造查询条件
	labels := map[string]string{
		"device_id": params.DeviceID,
		"ifName":    params.IfName,
	}
	// 如果有VNI参数，添加到查询条件
	if params.VNI != "" {
		labels["vni"] = params.VNI
	}
	labelStr := formatLabels(labels)

	startTime, endTime := params.StartTime, params.EndTime
	if endTime.IsZero() {
		endTime = time.Now().UTC()
	}
	if startTime.IsZero() {
		startTime = endTime.Add(-15 * time.Minute)
	}
	timeRange := fmt.Sprintf("%.0fs", endTime.Sub(startTime).Seconds())

	type queryResult struct {
		value float64
		err   error
	}
	inRateCh, outRateCh, inTotalCh, outTotalCh := make(chan queryResult, 1), make(chan queryResult, 1), make(chan queryResult, 1), make(chan queryResult, 1)

	// 使用正确的指标名称：dci_snmp_flow_ifHCInOctets 替代 dci_snmp_flow_in_octets
	// 构造并记录入向速率查询
	inRateQuery := fmt.Sprintf(`rate(dci_snmp_flow_ifHCInOctets{%s}[%s]) * 8`, labelStr, timeRange)
	s.logger.Info("入向流量速率查询", zap.String("query", inRateQuery))
	go func() {
		val, err := s.trafficDAO.QueryRate(ctx, inRateQuery, endTime)
		inRateCh <- queryResult{value: val, err: err}
	}()

	// 构造并记录出向速率查询 - 同样使用正确的指标名称
	outRateQuery := fmt.Sprintf(`rate(dci_snmp_flow_ifHCOutOctets{%s}[%s]) * 8`, labelStr, timeRange)
	s.logger.Info("出向流量速率查询", zap.String("query", outRateQuery))
	go func() {
		val, err := s.trafficDAO.QueryRate(ctx, outRateQuery, endTime)
		outRateCh <- queryResult{value: val, err: err}
	}()

	// 构造并记录入向总量查询
	inTotalQuery := fmt.Sprintf(`increase(dci_snmp_flow_ifHCInOctets{%s}[%s])`, labelStr, timeRange)
	s.logger.Info("入向流量总量查询", zap.String("query", inTotalQuery))
	go func() {
		val, err := s.trafficDAO.QueryIncrease(ctx, inTotalQuery, endTime)
		inTotalCh <- queryResult{value: val, err: err}
	}()

	// 构造并记录出向总量查询
	outTotalQuery := fmt.Sprintf(`increase(dci_snmp_flow_ifHCOutOctets{%s}[%s])`, labelStr, timeRange)
	s.logger.Info("出向流量总量查询", zap.String("query", outTotalQuery))
	go func() {
		val, err := s.trafficDAO.QueryIncrease(ctx, outTotalQuery, endTime)
		outTotalCh <- queryResult{value: val, err: err}
	}()

	inRateRes, outRateRes, inTotalRes, outTotalRes := <-inRateCh, <-outRateCh, <-inTotalCh, <-outTotalCh

	// 记录查询结果
	s.logger.Info("流量查询结果",
		zap.Float64("in_rate", inRateRes.value),
		zap.Float64("out_rate", outRateRes.value),
		zap.Float64("in_total", inTotalRes.value),
		zap.Float64("out_total", outTotalRes.value))

	if inRateRes.err != nil {
		s.logger.Warn("入向流量速率查询失败", zap.Error(inRateRes.err))
		return nil, inRateRes.err
	}
	if outRateRes.err != nil {
		s.logger.Warn("出向流量速率查询失败", zap.Error(outRateRes.err))
		return nil, outRateRes.err
	}
	if inTotalRes.err != nil {
		s.logger.Warn("入向流量总量查询失败", zap.Error(inTotalRes.err))
		return nil, inTotalRes.err
	}
	if outTotalRes.err != nil {
		s.logger.Warn("出向流量总量查询失败", zap.Error(outTotalRes.err))
		return nil, outTotalRes.err
	}

	// 自动调整单位：如果速率超过1000Kbps，则转为Mbps
	unitRate := "Kbps"
	inRate, outRate := inRateRes.value/1000, outRateRes.value/1000 // 默认转换为Kbps
	if inRate > 1000 || outRate > 1000 {
		unitRate = "Mbps"
		inRate, outRate = inRate/1000, outRate/1000 // 再转为Mbps
	}

	// 自动调整总量单位：如果总字节数超过1MB，则转为MB，超过1GB则转为GB
	unitTotal := "KB"
	inTotal, outTotal := inTotalRes.value/1024, outTotalRes.value/1024 // 默认转为KB
	if inTotal > 1024 || outTotal > 1024 {
		unitTotal = "MB"
		inTotal, outTotal = inTotal/1024, outTotal/1024 // 转为MB
	}
	if inTotal > 1024 || outTotal > 1024 {
		unitTotal = "GB"
		inTotal, outTotal = inTotal/1024, outTotal/1024 // 转为GB
	}

	reqID, _ := ctx.Value("requestID").(string)
	resp := &PortFlowResponse{
		RequestID: reqID,
		QueryDetails: PortFlowQueryDetails{
			DeviceID:  params.DeviceID,
			PortID:    params.IfName,
			VNI:       params.VNI,
			StartTime: startTime.Format(time.RFC3339),
			EndTime:   endTime.Format(time.RFC3339),
		},
		FlowData: PortFlowData{
			UnitRate:  unitRate,
			UnitTotal: unitTotal,
			InRate:    inRate,
			OutRate:   outRate,
			InTotal:   inTotal,
			OutTotal:  outTotal,
		},
	}

	return resp, nil
}

// GetSinglePortHistory 实现了单端口历史流量查询的逻辑
func (s *Service) GetSinglePortHistory(ctx context.Context, params *SinglePortHistoryQueryParams) (*PortHistoryResponse, error) {
	s.logger.Info("GetSinglePortHistory service called", zap.Any("params", params))

	// 解析和设置时间参数
	endTime := params.EndTime
	startTime := params.StartTime

	// 解析步长参数
	stepStr := params.Step
	if stepStr == "" {
		stepStr = "1m" // 默认1分钟步长
	}
	step, err := time.ParseDuration(stepStr)
	if err != nil {
		s.logger.Warn("Invalid step provided, falling back to 1m",
			zap.String("step", stepStr),
			zap.Error(err))
		step = 1 * time.Minute
		stepStr = "1m"
	}

	// 根据Prometheus实际数据结构构造查询条件
	labels := map[string]string{
		"device_id": params.DeviceID,
		"ifName":    params.IfName,
	}
	// 如果有VNI参数，添加到查询条件
	if params.VNI != "" {
		labels["vni"] = params.VNI
	}
	labelStr := formatLabels(labels)

	// 用于并发获取数据的结果结构体
	type seriesResult struct {
		series []models.TimeValuePair
		err    error
	}

	inRateCh, outRateCh := make(chan seriesResult, 1), make(chan seriesResult, 1)

	// 构造并记录入向速率查询
	inRateQuery := fmt.Sprintf(`rate(dci_snmp_flow_ifHCInOctets{%s}[%s]) * 8`, labelStr, stepStr)
	s.logger.Info("入向历史流量查询", zap.String("query", inRateQuery))
	go func() {
		series, err := s.trafficDAO.QueryVectorOverTime(ctx, inRateQuery, startTime, endTime, step)
		if err != nil {
			inRateCh <- seriesResult{err: fmt.Errorf("入向流量历史数据查询失败: %w", err)}
			return
		}
		inRateCh <- seriesResult{series: series}
	}()

	// 构造并记录出向速率查询
	outRateQuery := fmt.Sprintf(`rate(dci_snmp_flow_ifHCOutOctets{%s}[%s]) * 8`, labelStr, stepStr)
	s.logger.Info("出向历史流量查询", zap.String("query", outRateQuery))
	go func() {
		series, err := s.trafficDAO.QueryVectorOverTime(ctx, outRateQuery, startTime, endTime, step)
		if err != nil {
			outRateCh <- seriesResult{err: fmt.Errorf("出向流量历史数据查询失败: %w", err)}
			return
		}
		outRateCh <- seriesResult{series: series}
	}()

	// 获取查询结果
	inRateRes, outRateRes := <-inRateCh, <-outRateCh

	if inRateRes.err != nil {
		s.logger.Warn("入向流量历史查询失败", zap.Error(inRateRes.err))
		return nil, inRateRes.err
	}
	if outRateRes.err != nil {
		s.logger.Warn("出向流量历史查询失败", zap.Error(outRateRes.err))
		return nil, outRateRes.err
	}

	// 整合数据到响应模型
	// 以入向流量的时间戳为基准
	timestamps := make([]string, len(inRateRes.series))
	inData := make([]float64, len(inRateRes.series))
	outData := make([]float64, len(outRateRes.series))

	// 自动选择合适的单位
	maxRate := float64(0)
	for _, v := range inRateRes.series {
		if v.Value > maxRate {
			maxRate = v.Value
		}
	}
	for _, v := range outRateRes.series {
		if v.Value > maxRate {
			maxRate = v.Value
		}
	}

	unit := "bps"
	divider := float64(1)
	if maxRate > 1000 {
		unit = "Kbps"
		divider = 1000
	}
	if maxRate > 1000000 {
		unit = "Mbps"
		divider = 1000000
	}
	if maxRate > 1000000000 {
		unit = "Gbps"
		divider = 1000000000
	}

	// 填充数据
	for i, v := range inRateRes.series {
		timestamps[i] = v.Timestamp.Format(time.RFC3339)
		inData[i] = v.Value / divider
	}
	// 假设outRateRes.series与inRateRes.series长度和时间戳对齐
	for i, v := range outRateRes.series {
		if i < len(outData) {
			outData[i] = v.Value / divider
		}
	}

	reqID, _ := ctx.Value("requestID").(string)
	resp := &PortHistoryResponse{
		RequestID: reqID,
		QueryDetails: PortHistoryQueryDetails{
			DeviceID:  params.DeviceID,
			PortID:    params.IfName,
			VNI:       params.VNI,
			StartTime: startTime.Format(time.RFC3339),
			EndTime:   endTime.Format(time.RFC3339),
			Step:      stepStr,
		},
		HistoryData: PortHistoryData{
			Unit:       unit,
			Timestamps: timestamps,
			Series: []TrafficSeries{
				{Name: "入向流量", Data: inData},
				{Name: "出向流量", Data: outData},
			},
		},
	}

	return resp, nil
}
