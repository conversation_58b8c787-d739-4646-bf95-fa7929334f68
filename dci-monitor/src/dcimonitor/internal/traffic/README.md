# 流量模块重构

本目录实现了流量模块的重构，按照DCI项目API代码规范指南进行了代码组织调整，将原有代码按照功能模块划分为：

- `model.go`：定义流量相关的数据模型
- `service.go`：提供业务逻辑层
- `dao.go`：负责数据访问
- `handler.go`：处理HTTP请求和响应
- `adapter.go`：提供旧接口到新接口的适配器

## 重构内容

1. 从原 `models` 包中提取并迁移了流量相关的数据结构到 `model.go`
   - 提取了AZLinkQueryParams、TrafficChartData、SinglePortFlowQueryParams等模型
   - 增加了TimeValuePair用于代替Prometheus包中的模型

2. 从原 `services/traffic_service.go` 中迁移了流量服务到 `service.go`
   - 实现了GetAZLinkTraffic、GetSinglePortFlow、GetSinglePortHistory等方法
   - 移动并优化了formatLabels等辅助函数

3. 创建了流量数据访问层 `dao.go`
   - 实现了TrafficDAO接口及其具体实现
   - 提供了对Prometheus数据的查询功能
   - 实现了对端口映射数据的访问

4. 从原 `monitors/traffic_monitor.go` 和 `monitors/single_port_monitor.go` 中迁移了API处理函数到 `handler.go`
   - 实现了GetAverageTrafficChart、GetMaximumTrafficChart等AZ链路流量接口
   - 实现了GetPortFlow、GetPortFlowHistory等单端口流量接口
   - 添加了RegisterRoutes方法用于注册所有流量相关路由

5. 创建了适配器层 `adapter.go`
   - 提供LegacyTrafficDAOAdapter，将services.TrafficDAO适配为traffic.TrafficDAO
   - 提供PortMappingDAOAdapter，将services.PortMappingDAO适配为traffic.PortMappingDAO
   - 解决了新旧接口之间的类型兼容性问题

## 测试结果

- 已通过代码编译测试，确认重构后的代码没有语法错误
- 在`cmd/server.go`中添加了测试路由，通过以下方式测试新模块：
  - `/api/v1/switches/v2/{deviceID}/ports/{ifName}/flow` - 测试获取单端口实时流量
  - `/api/v1/switches/v2/{deviceID}/ports/{ifName}/flow/history` - 测试获取单端口历史流量
  - `/api/v1/traffic/v2/chart/average` - 测试获取A-Z链路平均流量图
  - `/api/v1/traffic/v2/chart/maximum` - 测试获取A-Z链路最大流量图
  - `/api/v1/traffic/v2/chart/minimum` - 测试获取A-Z链路最小流量图
  - `/api/v1/traffic/v2/summary` - 测试获取流量摘要

## 下一步工作

1. 功能测试
   - 使用测试路由确保新模块功能与原有实现一致
   - 比较新旧接口的返回结果，确保数据一致性

2. 完全迁移
   - 完成测试后，可以替换原有路由，使用新模块处理
   - 删除原有的`monitors/traffic_monitor.go`、`monitors/single_port_monitor.go`和`services/traffic_service.go`文件
   - 更新依赖关系，移除对旧模块的依赖

3. 持续优化
   - 根据性能测试结果，优化流量查询逻辑
   - 考虑添加缓存机制，减少对Prometheus的查询频率
   - 进一步优化错误处理和日志记录

## 接口说明

Traffic模块提供以下API接口：

1. A-Z链路流量接口
   - `GET /api/v1/traffic/chart/average` - 获取平均流量图数据
   - `GET /api/v1/traffic/chart/maximum` - 获取最大流量图数据
   - `GET /api/v1/traffic/chart/minimum` - 获取最小流量图数据
   - `GET /api/v1/traffic/summary` - 获取流量摘要数据

2. 单端口流量接口
   - `GET /api/v1/switches/{deviceID}/ports/{ifName}/flow` - 获取单端口实时流量
   - `GET /api/v1/switches/{deviceID}/ports/{ifName}/flow/history` - 获取单端口历史流量

## 注意事项

1. 由于接口兼容性问题，保留了原有的数据结构和命名，未对API进行破坏性更改
2. 使用了适配器模式处理新旧接口的兼容性问题，确保平滑过渡
3. 在完全迁移前，建议保留原有的路由和处理函数，确保系统稳定运行
4. 测试通过后，可以逐步替换原有代码，完成完整迁移 