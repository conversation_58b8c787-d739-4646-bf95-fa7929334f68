package traffic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/prometheus/client_golang/api"
	v1 "github.com/prometheus/client_golang/api/prometheus/v1"
	pModel "github.com/prometheus/common/model"
	"go.uber.org/zap"

	"dcimonitor/internal/models"
)

// PortMappingDAO 端口映射数据访问接口
type PortMappingDAO interface {
	// GetDeviceIPForPort 根据设备ID、逻辑端口ID和VNI，验证参数并获取设备IP
	GetDeviceIPForPort(ctx context.Context, deviceID, portID, vni string) (deviceIP string, err error)

	// GetDeviceIPForIfName 根据设备ID、接口名称和VNI，验证参数并获取设备IP
	// 适用于新的API接口，使用ifName代替portID
	GetDeviceIPForIfName(ctx context.Context, deviceID, ifName, vni string) (deviceIP string, err error)
}

// TrafficDAO 流量数据访问接口
type TrafficDAO interface {
	// QueryRate 执行一个rate()或类似的即时查询，返回单个浮点数结果
	QueryRate(ctx context.Context, query string, t time.Time) (float64, error)
	// QueryIncrease 执行一个increase()查询，返回单个浮点数结果
	QueryIncrease(ctx context.Context, query string, t time.Time) (float64, error)
	// QueryVectorOverTime 执行一个范围查询，返回一个时间序列
	QueryVectorOverTime(ctx context.Context, query string, start, end time.Time, step time.Duration) ([]models.TimeValuePair, error)
}

// DAO 流量模块的数据访问对象
type DAO struct {
	db      *sql.DB
	promAPI v1.API
	logger  *zap.Logger
}

// NewDAO 创建一个新的DAO实例
func NewDAO(db *sql.DB, promClient api.Client, logger *zap.Logger) *DAO {
	return &DAO{
		db:      db,
		promAPI: v1.NewAPI(promClient),
		logger:  logger,
	}
}

// QueryRate 执行Prometheus即时查询，返回单个浮点数结果
func (d *DAO) QueryRate(ctx context.Context, query string, t time.Time) (float64, error) {
	result, warnings, err := d.promAPI.Query(ctx, query, t)
	if err != nil {
		return 0, fmt.Errorf("prometheus query failed: %w", err)
	}
	if len(warnings) > 0 {
		d.logger.Warn("Prometheus query returned warnings", zap.Strings("warnings", warnings))
	}

	vec, ok := result.(pModel.Vector)
	if !ok || len(vec) == 0 {
		return 0, nil // No data found is not an error, return 0
	}

	return float64(vec[0].Value), nil
}

// QueryIncrease 执行Prometheus increase查询，返回单个浮点数结果
func (d *DAO) QueryIncrease(ctx context.Context, query string, t time.Time) (float64, error) {
	// For increase, the logic is the same as QueryRate for instant vectors
	return d.QueryRate(ctx, query, t)
}

// QueryVectorOverTime 执行Prometheus范围查询，返回时间序列
func (d *DAO) QueryVectorOverTime(ctx context.Context, query string, start, end time.Time, step time.Duration) ([]models.TimeValuePair, error) {
	r := v1.Range{Start: start, End: end, Step: step}
	result, warnings, err := d.promAPI.QueryRange(ctx, query, r)
	if err != nil {
		return nil, fmt.Errorf("prometheus query_range failed: %w", err)
	}
	if len(warnings) > 0 {
		d.logger.Warn("Prometheus range query returned warnings", zap.Strings("warnings", warnings))
	}

	matrix, ok := result.(pModel.Matrix)
	if !ok || len(matrix) == 0 {
		return []models.TimeValuePair{}, nil // No data
	}

	// Assuming the query returns a single time series for simplicity
	// A more robust implementation might handle multiple series
	pairList := make([]models.TimeValuePair, len(matrix[0].Values))
	for i, v := range matrix[0].Values {
		pairList[i] = models.TimeValuePair{
			Timestamp: v.Timestamp.Time().UTC(),
			Value:     float64(v.Value),
		}
	}

	return pairList, nil
}

// GetDeviceIPForPort 根据设备ID、端口ID和VNI获取设备IP
func (d *DAO) GetDeviceIPForPort(ctx context.Context, deviceID, portID, vni string) (string, error) {
	// 该查询通过JOIN验证了 device, logic_port_device 和 node_business 之间基于ID和VNI的关联关系
	query := `
        SELECT d.device_management_ip
        FROM dci_logic_port_device AS lpd
        JOIN dci_device AS d ON lpd.device_id = d.id
        JOIN dci_node_business AS nb ON lpd.logic_port_id = nb.logic_port_id
        WHERE d.id = ? AND lpd.logic_port_id = ? AND nb.vni = ?
        LIMIT 1;
    `
	args := []interface{}{deviceID, portID, vni}

	var deviceIP string
	err := d.db.QueryRowContext(ctx, query, args...).Scan(&deviceIP)

	d.logger.Debug("从数据库查询device_ip",
		zap.String("deviceID", deviceID),
		zap.String("portID", portID),
		zap.String("vni", vni),
		zap.String("deviceIP", deviceIP),
		zap.Error(err))

	if err != nil {
		if err == sql.ErrNoRows {
			// 如果没有找到行，说明这组参数的组合是无效的
			return "", fmt.Errorf("验证失败：提供的device_id, port_id和vni组合不匹配或不存在")
		}
		return "", fmt.Errorf("数据库查询失败: %w", err)
	}

	if deviceIP == "" {
		return "", fmt.Errorf("数据不一致：找到了匹配的端口，但其设备IP为空")
	}

	return deviceIP, nil
}

// GetDeviceIPForIfName 根据设备ID、接口名称和VNI获取设备IP
func (d *DAO) GetDeviceIPForIfName(ctx context.Context, deviceID, ifName, vni string) (string, error) {
	// 查询设备的IP地址，使用deviceID作为主要标识
	query := `
        SELECT d.device_management_ip
        FROM dci_device AS d
        WHERE d.id = ?
        LIMIT 1;
    `
	args := []interface{}{deviceID}

	var deviceIP string
	err := d.db.QueryRowContext(ctx, query, args...).Scan(&deviceIP)

	d.logger.Debug("基于ifName从数据库查询device_ip",
		zap.String("deviceID", deviceID),
		zap.String("ifName", ifName),
		zap.String("vni", vni),
		zap.String("deviceIP", deviceIP),
		zap.Error(err))

	if err != nil {
		if err == sql.ErrNoRows {
			// 如果没有找到行，设备ID无效
			return "", fmt.Errorf("验证失败：提供的device_id %s 不存在", deviceID)
		}
		return "", fmt.Errorf("数据库查询失败: %w", err)
	}

	if deviceIP == "" {
		return "", fmt.Errorf("数据不一致：找到了匹配的设备，但其IP为空")
	}

	return deviceIP, nil
}
