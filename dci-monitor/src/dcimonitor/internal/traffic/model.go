package traffic

import "time"

// SinglePortFlowQueryParams 定义了单端口流量查询参数结构
type SinglePortFlowQueryParams struct {
	DeviceID  string    // 从路径参数获取
	IfName    string    // 从路径参数获取，已解码的接口名称
	VNI       string    `form:"vni"`
	StartTime time.Time `form:"start_time" time_format:"2006-01-02T15:04:05Z07:00"`
	EndTime   time.Time `form:"end_time" time_format:"2006-01-02T15:04:05Z07:00"`
}

// SinglePortHistoryQueryParams 定义了单端口历史流量查询参数结构
type SinglePortHistoryQueryParams struct {
	DeviceID  string    // 从路径参数获取
	IfName    string    // 从路径参数获取，已解码的接口名称
	VNI       string    `form:"vni"`
	StartTime time.Time `form:"start_time" time_format:"2006-01-02T15:04:05Z07:00" binding:"required"`
	EndTime   time.Time `form:"end_time" time_format:"2006-01-02T15:04:05Z07:00" binding:"required"`
	Step      string    `form:"step"`
}

// AZLinkQueryParams 定义了所有A-Z链路图表和摘要接口共用的查询参数结构
type AZLinkQueryParams struct {
	Granularity string    `form:"granularity" binding:"required,oneof=1m 5m 1h 1d"`
	TimeRange   string    `form:"time_range"`
	StartTime   time.Time `form:"start_time" time_format:"2006-01-02T15:04:05Z07:00"`
	EndTime     time.Time `form:"end_time" time_format:"2006-01-02T15:04:05Z07:00"`
	ASwitchID   string    `form:"a_switch_id" binding:"required"`
	APortIDRaw  string    `form:"a_port_id" binding:"required"`
	ZSwitchID   string    `form:"z_switch_id" binding:"required"`
	ZPortIDRaw  string    `form:"z_port_id" binding:"required"`
	VNI         string    `form:"vni"`
	APortID     string    // 解码后的 Port ID
	ZPortID     string    // 解码后的 Port ID
}

// TrafficChartData 包含图表所需的时间戳、单位和数据系列。
type TrafficChartData struct {
	Unit       string          `json:"unit"`       // 数据单位 (e.g., "Mbps", "Kbps")
	Timestamps []string        `json:"timestamps"` // RFC3339 UTC 格式的时间戳数组
	Series     []TrafficSeries `json:"series"`     // 数据系列数组
}

// TrafficSeries 代表一个数据系列，例如 "A端入流量"。
type TrafficSeries struct {
	Name string    `json:"name"` // 系列名称 (e.g., "A端 (CE1-GE1/0/1) 平均入流量")
	Data []float64 `json:"data"` // 与 Timestamps 对应的数值数组
}

// TrafficSummaryData 包含流量摘要的单位和条目列表。
type TrafficSummaryData struct {
	Unit  string               `json:"unit"`  // 所有流量值的单位 (e.g., "Mbps")
	Items []TrafficSummaryItem `json:"items"` // 摘要信息条目数组
}

// TrafficSummaryItem 代表摘要表格中的一行数据。
type TrafficSummaryItem struct {
	Name    string   `json:"name"`             // 行名称 (e.g., "A端入流量")
	Latest  *float64 `json:"latest,omitempty"` // 最近流量速率 (nullable)
	Minimum float64  `json:"minimum"`          // 时间段内最小值
	Average float64  `json:"average"`          // 时间段内平均值
	Maximum float64  `json:"maximum"`          // 时间段内最大值
}

// PortFlowQueryDetails 包含单端口流量查询的请求详情
type PortFlowQueryDetails struct {
	DeviceID  string `json:"device_id"`
	PortID    string `json:"port_id"`
	VNI       string `json:"vni,omitempty"`
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
}

// PortFlowData 包含单个端口的计算后流量数据
type PortFlowData struct {
	UnitRate  string  `json:"unit_rate"`  // 速率单位, e.g., "Mbps"
	UnitTotal string  `json:"unit_total"` // 总量单位, e.g., "MB", "GB"
	InRate    float64 `json:"in_rate"`
	OutRate   float64 `json:"out_rate"`
	InTotal   float64 `json:"in_total"`
	OutTotal  float64 `json:"out_total"`
}

// PortHistoryQueryDetails 包含单端口历史流量查询的请求详情
type PortHistoryQueryDetails struct {
	DeviceID  string `json:"device_id"`
	PortID    string `json:"port_id"`
	VNI       string `json:"vni,omitempty"`
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	Step      string `json:"step"`
}

// PortHistoryData 包含图表所需的时间戳、单位和数据系列
type PortHistoryData struct {
	Unit       string          `json:"unit"`       // 数据单位 (e.g., "Mbps")
	Timestamps []string        `json:"timestamps"` // RFC3339 UTC 格式的时间戳数组
	Series     []TrafficSeries `json:"series"`     // 数据系列数组
}

// AZLinkQueryDetails 封装了A-Z链路查询的详细参数
type AZLinkQueryDetails struct {
	DeviceA   string `json:"device_a"`
	PortA     string `json:"port_a"`
	DeviceZ   string `json:"device_z"`
	PortZ     string `json:"port_z"`
	VNI       string `json:"vni,omitempty"`
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	Step      string `json:"step"`
}

// TrafficChartResponse 是A-Z流量和历史流量查询的通用响应结构
type TrafficChartResponse struct {
	RequestID    string           `json:"request_id"`
	QueryDetails any              `json:"query_details"` // 可容纳 AZLinkQueryDetails 或 PortHistoryQueryDetails
	ChartData    TrafficChartData `json:"chart_data"`
}

// PortFlowResponse 是单端口流量的响应结构
type PortFlowResponse struct {
	RequestID    string               `json:"request_id"`
	QueryDetails PortFlowQueryDetails `json:"query_details"`
	FlowData     PortFlowData         `json:"flow_data"`
}

// PortHistoryResponse 是单端口历史流量的响应结构
type PortHistoryResponse struct {
	RequestID    string                  `json:"request_id"`
	QueryDetails PortHistoryQueryDetails `json:"query_details"`
	HistoryData  PortHistoryData         `json:"history_data"`
}
