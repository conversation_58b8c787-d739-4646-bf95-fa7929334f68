package traffic

import (
	"encoding/base64"
	"fmt"
	"net/http"
	"strings"

	"common/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
)

// Handler 处理流量相关的HTTP请求
type Handler struct {
	service *Service
	logger  *zap.Logger
}

// NewHandler 创建一个新的Handler实例
func NewHandler(service *Service, logger *zap.Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// parseAndValidateParams 解析和验证A-Z链路查询参数
func (h *Handler) parseAndValidateParams(ctx *gin.Context) (*AZLinkQueryParams, error) {
	var params AZLinkQueryParams
	if err := ctx.ShouldBindQuery(&params); err != nil {
		return nil, fmt.Errorf("无效的查询参数: %w", err)
	}

	// URL解码PortID已移至Service层处理

	return &params, nil
}

// GetAverageTrafficChart 获取平均流量图数据
// @Summary 获取A-Z链路平均流量图
// @Description 查询A-Z链路在指定时间范围内的平均流量速率时间序列。支持通过VNI进行筛选。
// @Description **注意: `a_port_id` 和 `z_port_id` 中的特殊字符 (如 '/') 需要进行 URL 编码。**
// @Tags 06-性能管理-A-Z链路流量
// @Accept json
// @Produce json
// @Param a_switch_id query string true "A端设备ID" example(CE1)
// @Param a_port_id query string true "A端端口ID (URL Encoded)" example(GE1%2F0%2F1)
// @Param z_switch_id query string true "Z端设备ID" example(CE2)
// @Param z_port_id query string true "Z端端口ID (URL Encoded)" example(GE1%2F0%2F1)
// @Param granularity query string true "查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。" example(1m)
// @Param vni query string false "VNI (Virtual Network Identifier) 标识符" example(6005002)
// @Success 200 {object} TrafficChartData "成功返回平均流量数据"
// @Failure 400 {object} models.ErrorResponse "请求参数无效"
// @Failure 500 {object} models.ErrorResponse "服务器内部错误"
// @Router /api/v1/traffic/chart/average [get]
func (h *Handler) GetAverageTrafficChart(ctx *gin.Context) {
	reqID := uuid.New().String()
	ctx.Set("requestID", reqID)

	params, err := h.parseAndValidateParams(ctx)
	if err != nil {
		logger.Error("参数校验失败", zap.String("requestID", reqID), zap.Error(err))
		ctx.JSON(http.StatusBadRequest, gin.H{
			"request_id": reqID,
			"error":      "参数校验失败",
			"details":    err.Error(),
		})
		return
	}

	logger.Info("收到获取A-Z链路流量图数据请求", zap.String("requestID", reqID), zap.Any("params", params))

	response, err := h.service.GetAZLinkTraffic(ctx, params)
	if err != nil {
		logger.Error("GetAZLinkTraffic 服务调用失败", zap.Error(err), zap.String("requestID", reqID))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"request_id": reqID,
			"error":      "获取A-Z链路流量失败",
			"details":    err.Error(),
		})
		return
	}

	jsonData, err := jsoniter.Marshal(response)
	if err != nil {
		logger.Error("JSON 序列化失败", zap.String("requestID", reqID), zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"request_id": reqID,
			"error":      "内部序列化错误",
		})
		return
	}

	ctx.Data(http.StatusOK, "application/json; charset=utf-8", jsonData)
}

// GetMaximumTrafficChart 获取最大流量图数据
// @Summary 获取A-Z链路最大(峰值)流量图
// @Description 查询A-Z链路在指定时间范围内的峰值流量速率时间序列。支持通过VNI进行筛选。
// @Description **注意: `a_port_id` 和 `z_port_id` 中的特殊字符 (如 '/') 需要进行 URL 编码。**
// @Tags 06-性能管理-A-Z链路流量
// @Accept json
// @Produce json
// @Param a_switch_id query string true "A端设备ID" example(CE1)
// @Param a_port_id query string true "A端端口ID (URL Encoded)" example(GE1%2F0%2F1)
// @Param z_switch_id query string true "Z端设备ID" example(CE2)
// @Param z_port_id query string true "Z端端口ID (URL Encoded)" example(GE1%2F0%2F1)
// @Param granularity query string true "查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。" example(1m)
// @Param vni query string false "VNI (Virtual Network Identifier) 标识符" example(6005002)
// @Success 200 {object} TrafficChartData "成功返回峰值流量数据"
// @Failure 400 {object} models.ErrorResponse "请求参数无效"
// @Failure 500 {object} models.ErrorResponse "服务器内部错误"
// @Router /api/v1/traffic/chart/maximum [get]
func (h *Handler) GetMaximumTrafficChart(ctx *gin.Context) {
	h.GetAverageTrafficChart(ctx) // 重用GetAverageTrafficChart的逻辑
}

// GetMinimumTrafficChart 获取最小流量图数据
// @Summary 获取A-Z链路最小(谷值)流量图
// @Description 查询A-Z链路在指定时间范围内的谷值流量速率时间序列。支持通过VNI进行筛选。
// @Description **注意: `a_port_id` 和 `z_port_id` 中的特殊字符 (如 '/') 需要进行 URL 编码。**
// @Tags 06-性能管理-A-Z链路流量
// @Accept json
// @Produce json
// @Param a_switch_id query string true "A端设备ID" example(CE1)
// @Param a_port_id query string true "A端端口ID (URL Encoded)" example(GE1%2F0%2F1)
// @Param z_switch_id query string true "Z端设备ID" example(CE2)
// @Param z_port_id query string true "Z端端口ID (URL Encoded)" example(GE1%2F0%2F1)
// @Param granularity query string true "查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。" example(1m)
// @Param vni query string false "VNI (Virtual Network Identifier) 标识符" example(6005002)
// @Success 200 {object} TrafficChartData "成功返回谷值流量数据"
// @Failure 400 {object} models.ErrorResponse "请求参数无效"
// @Failure 500 {object} models.ErrorResponse "服务器内部错误"
// @Router /api/v1/traffic/chart/minimum [get]
func (h *Handler) GetMinimumTrafficChart(ctx *gin.Context) {
	h.GetAverageTrafficChart(ctx) // 重用GetAverageTrafficChart的逻辑
}

// GetTrafficSummary 获取流量摘要数据
// @Summary 获取A-Z链路流量摘要
// @Description 获取A-Z链路在指定时间范围内的流量摘要信息，如总流量、平均/峰值/谷值速率等。 **注意：此接口当前尚未实现。**
// @Description **注意: `a_port_id` 和 `z_port_id` 中的特殊字符 (如 '/') 需要进行 URL 编码。**
// @Tags 06-性能管理-A-Z链路流量
// @Accept json
// @Produce json
// @Param a_switch_id query string true "A端设备ID" example(CE1)
// @Param a_port_id query string true "A端端口ID (URL Encoded)" example(GE1%2F0%2F1)
// @Param z_switch_id query string true "Z端设备ID" example(CE2)
// @Param z_port_id query string true "Z端端口ID (URL Encoded)" example(GE1%2F0%2F1)
// @Param granularity query string true "查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。" example(1m)
// @Param vni query string false "VNI (Virtual Network Identifier) 标识符" example(6005002)
// @Failure 501 {object} models.ErrorResponse "接口未实现"
// @Router /api/v1/traffic/summary [get]
func (h *Handler) GetTrafficSummary(ctx *gin.Context) {
	reqID := uuid.New().String()
	ctx.Set("requestID", reqID)
	logger.Warn("GetTrafficSummary service logic not implemented yet", zap.String("requestID", reqID))
	ctx.JSON(http.StatusNotImplemented, gin.H{
		"request_id": reqID,
		"error":      "Not Implemented",
		"details":    "摘要查询功能尚未实现",
	})
}

// GetPortFlow 处理获取单个端口实时流量的请求
// @Summary 查询单端口实时流量
// @Description 查询单个端口在指定时间范围内的平均速率和总流量。
// @Description 时间范围通过 `start_time` 和 `end_time` 指定，格式为 RFC3339 UTC。如果未提供，则默认为最近15分钟。
// @Description 注意: ifName 使用 Base64 编码，不再支持特殊字符的 URL 编码方式。
// @Tags 07-性能管理-单端口流量查询
// @Accept json
// @Produce json
// @Param deviceID path string true "设备ID" example(210)
// @Param ifName path string true "端口名称，使用Base64编码" example(MTAwR0UxLzAvNg==)
// @Param vni query string false "VNI (Virtual Network Identifier) 标识符，用于进一步筛选流量" example(6005002)
// @Param start_time query string false "查询起始时间 (RFC3339 UTC 格式)。默认为15分钟前" example(2025-06-04T10:00:00Z)
// @Param end_time query string false "查询结束时间 (RFC3339 UTC 格式)。默认为当前时间" example(2025-06-06T10:15:00Z)
// @Success 200 {object} PortFlowResponse "成功返回端口流量数据"
// @Failure 400 {object} models.ErrorResponse "请求参数无效"
// @Failure 500 {object} models.ErrorResponse "服务器内部错误"
// @Router /api/v1/switches/{deviceID}/ports/{ifName}/flow [get]
func (h *Handler) GetPortFlow(c *gin.Context) {
	reqID := uuid.New().String()
	c.Set("requestID", reqID)

	// 从路径获取参数
	deviceID := c.Param("deviceID")
	encodedIfName := c.Param("ifName")

	// Base64解码ifName
	decoded, err := base64.StdEncoding.DecodeString(encodedIfName)
	if err != nil {
		h.logger.Error("解码端口名称失败",
			zap.String("requestID", reqID),
			zap.String("deviceID", deviceID),
			zap.String("encodedIfName", encodedIfName),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, gin.H{
			"request_id": reqID,
			"error":      "无效的端口名称编码",
			"details":    err.Error(),
		})
		return
	}
	ifName := string(decoded)

	// 绑定查询参数
	var params SinglePortFlowQueryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		h.logger.Error("绑定 GetPortFlow 查询参数失败", zap.Error(err), zap.String("requestID", reqID))
		c.JSON(http.StatusBadRequest, gin.H{
			"request_id": reqID,
			"error":      "无效的参数",
			"details":    err.Error(),
		})
		return
	}

	// 设置路径参数
	params.DeviceID = deviceID
	params.IfName = ifName

	h.logger.Info("收到获取单端口流量请求",
		zap.String("requestID", reqID),
		zap.String("deviceID", deviceID),
		zap.String("ifName", ifName),
	)

	resp, err := h.service.GetSinglePortFlow(c, &params)
	if err != nil {
		h.logger.Error("GetSinglePortFlow 服务调用失败", zap.Error(err), zap.String("requestID", reqID))
		if strings.Contains(err.Error(), "不存在") {
			c.JSON(http.StatusNotFound, gin.H{
				"request_id": reqID,
				"error":      "获取端口流量失败",
				"details":    err.Error(),
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"request_id": reqID,
				"error":      "获取端口流量失败",
				"details":    err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetPortFlowHistory 处理获取单个端口历史流量的请求
// @Summary 查询单端口历史流量
// @Description 查询单个端口在指定时间范围内的历史流量速率时间序列。
// @Description 时间范围通过 `start_time` 和 `end_time` 指定，格式为 RFC3339 UTC。
// @Description `step` 参数定义了数据点之间的时间间隔。
// @Description 注意: ifName 使用 Base64 编码，不再支持特殊字符的 URL 编码方式。
// @Tags 07-性能管理-单端口流量查询
// @Accept json
// @Produce json
// @Param deviceID path string true "设备ID" example(210)
// @Param ifName path string true "端口名称，使用Base64编码" example({{'10GE1/0/1'|base64}})
// @Param vni query string false "VNI (Virtual Network Identifier) 标识符，用于进一步筛选流量" example(6005002)
// @Param start_time query string true "查询起始时间 (RFC3339 UTC 格式)" example(2025-06-18T15:00:00Z)
// @Param end_time query string true "查询结束时间 (RFC3339 UTC 格式)" example(2025-06-18T16:15:00Z)
// @Param step query string false "查询步长, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。默认为'1m'" example(5m)
// @Success 200 {object} PortHistoryResponse "成功返回端口历史流量数据"
// @Failure 400 {object} models.ErrorResponse "请求参数无效"
// @Failure 500 {object} models.ErrorResponse "服务器内部错误"
// @Router /api/v1/switches/{deviceID}/ports/{ifName}/flow/history [get]
func (h *Handler) GetPortFlowHistory(c *gin.Context) {
	reqID := uuid.New().String()
	c.Set("requestID", reqID)

	// 从路径获取参数
	deviceID := c.Param("deviceID")
	encodedIfName := c.Param("ifName")

	// Base64解码ifName
	decoded, err := base64.StdEncoding.DecodeString(encodedIfName)
	if err != nil {
		h.logger.Error("解码端口名称失败",
			zap.String("requestID", reqID),
			zap.String("deviceID", deviceID),
			zap.String("encodedIfName", encodedIfName),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, gin.H{
			"request_id": reqID,
			"error":      "无效的端口名称编码",
			"details":    err.Error(),
		})
		return
	}
	ifName := string(decoded)

	// 绑定查询参数
	var params SinglePortHistoryQueryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		h.logger.Error("绑定 GetPortFlowHistory 查询参数失败", zap.Error(err), zap.String("requestID", reqID))
		c.JSON(http.StatusBadRequest, gin.H{
			"request_id": reqID,
			"error":      "无效的参数",
			"details":    err.Error(),
		})
		return
	}

	// 设置路径参数
	params.DeviceID = deviceID
	params.IfName = ifName

	h.logger.Info("收到获取单端口历史流量请求",
		zap.String("requestID", reqID),
		zap.String("deviceID", deviceID),
		zap.String("ifName", ifName),
	)

	resp, err := h.service.GetSinglePortHistory(c, &params)
	if err != nil {
		h.logger.Error("GetPortFlowHistory 服务调用失败", zap.Error(err), zap.String("requestID", reqID))
		if strings.Contains(err.Error(), "不存在") {
			c.JSON(http.StatusNotFound, gin.H{
				"request_id": reqID,
				"error":      "获取端口历史流量失败",
				"details":    err.Error(),
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"request_id": reqID,
				"error":      "获取端口历史流量失败",
				"details":    err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, resp)
}

// RegisterRoutes 注册流量相关的路由
func (h *Handler) RegisterRoutes(router *gin.Engine) {
	apiV1 := router.Group("/api/v1")
	{
		// 流量图和摘要相关的路由
		trafficGroup := apiV1.Group("/traffic")
		{
			trafficGroup.GET("/chart/average", h.GetAverageTrafficChart)
			trafficGroup.GET("/chart/maximum", h.GetMaximumTrafficChart)
			trafficGroup.GET("/chart/minimum", h.GetMinimumTrafficChart)
			trafficGroup.GET("/summary", h.GetTrafficSummary)
		}

		// 单端口流量查询路由
		switchesGroup := apiV1.Group("/switches")
		{
			// 单端口流量查询路由
			switchesGroup.GET("/:deviceID/ports/:ifName/flow", h.GetPortFlow)
			switchesGroup.GET("/:deviceID/ports/:ifName/flow/history", h.GetPortFlowHistory)
		}
	}
}
