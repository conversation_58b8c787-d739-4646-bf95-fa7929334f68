package device

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"sync"
	"time"

	"common/logger"
	"dcimonitor/internal/models"

	"go.uber.org/zap"
)

// PortMappingDAO 是端口映射的数据访问接口
type PortMappingDAO interface {
	// GetDeviceIPForPort 根据设备ID、逻辑端口ID和VNI，验证参数并获取设备IP
	GetDeviceIPForPort(ctx context.Context, deviceID, portID, vni string) (deviceIP string, err error)

	// GetDeviceIPForIfName 根据设备ID、接口名称和VNI，验证参数并获取设备IP
	// 适用于新的API接口，使用ifName代替portID
	GetDeviceIPForIfName(ctx context.Context, deviceID, ifName, vni string) (deviceIP string, err error)
}

// InterfaceStatusDAO 是接口状态的数据访问接口
type InterfaceStatusDAO interface {
	// GetInterfaceStatus 查询接口的操作状态(dci_snmp_status_interface)
	GetInterfaceStatus(ctx context.Context, deviceID, ifName string) (int, time.Time, error)
	// GetInterfaceAdminStatus 查询接口的管理状态(dci_snmp_status_interface_admin)
	GetInterfaceAdminStatus(ctx context.Context, deviceID, ifName string) (int, time.Time, error)
}

// TrafficDAO 是流量数据访问接口
type TrafficDAO interface {
	// QueryRate 执行一个rate()或类似的即时查询，返回单个浮点数结果
	QueryRate(ctx context.Context, query string, t time.Time) (float64, error)
	// QueryIncrease 执行一个increase()查询，返回单个浮点数结果
	QueryIncrease(ctx context.Context, query string, t time.Time) (float64, error)
	// QueryVectorOverTime 执行一个范围查询，返回一个时间序列
	QueryVectorOverTime(ctx context.Context, query string, start, end time.Time, step time.Duration) ([]models.TimeValuePair, error)
}

// Service 定义设备服务层
type Service struct {
	mu             sync.RWMutex
	switches       map[string]*SwitchStatus // 使用指针，方便更新
	portMappingDAO PortMappingDAO           // 端口映射数据访问对象
	statusDAO      InterfaceStatusDAO       // 接口状态数据访问对象
	trafficDAO     TrafficDAO               // 流量数据访问对象
	logger         *zap.Logger              // 日志记录器
	dao            *DAO                     // 数据库访问对象
}

// NewService 创建一个新的设备服务实例
func NewService(dao *DAO) *Service {
	return &Service{
		switches: make(map[string]*SwitchStatus),
		logger:   logger.GetLogger(),
		dao:      dao,
	}
}

// SetDAOs 设置数据访问层组件
func (s *Service) SetDAOs(portMappingDAO PortMappingDAO, statusDAO InterfaceStatusDAO, trafficDAO TrafficDAO) {
	s.portMappingDAO = portMappingDAO
	s.statusDAO = statusDAO
	s.trafficDAO = trafficDAO
}

// GenerateGenericPortName 生成通用端口名称
func GenerateGenericPortName(portID string) string {
	// Simple example: assumes format like GEa/b/c
	parts := strings.Split(portID, "/")
	if len(parts) > 0 && strings.HasPrefix(parts[0], "GE") {
		return "GigabitEthernet" + strings.TrimPrefix(parts[0], "GE") + "/" + strings.Join(parts[1:], "/")
	} else if len(parts) > 0 && strings.HasPrefix(parts[0], "Eth") {
		return "Ethernet" + strings.TrimPrefix(parts[0], "Eth") + "/" + strings.Join(parts[1:], "/")
	}
	return portID // Fallback to original ID if format is unexpected
}

// LoadSwitchesFromConfig 不再从配置文件加载交换机列表，因为现在直接从Prometheus获取数据
func (s *Service) LoadSwitchesFromConfig() {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 创建空的交换机映射，所有数据将从Prometheus动态获取
	s.switches = make(map[string]*SwitchStatus)

	s.logger.Info("使用Prometheus作为接口状态数据源")
}

// GetSwitchStatus 获取指定设备的状态
func (s *Service) GetSwitchStatus(deviceID string) (SwitchStatus, error) {
	s.mu.RLock()
	sw, exists := s.switches[deviceID]
	s.mu.RUnlock()

	if exists {
		statusCopy := *sw
		return statusCopy, nil
	}

	// 交换机不在内存中存在，创建一个基于设备ID的新状态
	s.logger.Info("创建基于Prometheus的交换机状态", zap.String("deviceID", deviceID))

	// 创建最小的SwitchInfo
	demoInfo := SwitchInfo{
		ID:        deviceID,
		Name:      fmt.Sprintf("Device-%s", deviceID),
		IP:        "", // 我们不再依赖IP
		Community: "",
	}

	// 创建交换机状态，不包含端口列表（端口将根据请求按需从Prometheus获取）
	demoStatus := SwitchStatus{
		SwitchInfo:  demoInfo,
		Status:      "online", // 默认在线
		LastUpdated: time.Now(),
		Ports:       []Port{}, // 空端口列表
	}

	return demoStatus, nil
}

// GetPortStatus 获取指定设备的特定端口状态
func (s *Service) GetPortStatus(deviceID, portID string) (Port, error) {
	// 处理特殊字符（如"/"）的端口ID
	normalPortID, err := url.QueryUnescape(portID)
	if err != nil {
		// 解码失败，使用原始portID
		normalPortID = portID
	}

	s.mu.RLock()
	sw, exists := s.switches[deviceID]
	s.mu.RUnlock()

	// 首先检查设备是否存在
	if !exists {
		// 如果配置中没有此设备，我们仍尝试从Prometheus获取数据
		// deviceID直接作为查询参数
		if s.statusDAO != nil {
			return s.getPortStatusFromPrometheus(deviceID, normalPortID)
		}
		return Port{}, fmt.Errorf("设备 %s 不存在", deviceID)
	}

	// 检查是否配置了接口状态DAO
	if s.statusDAO == nil {
		// 如果未配置DAO，回退到内存中的静态数据
		s.logger.Warn("接口状态DAO未配置，使用静态数据",
			zap.String("deviceID", deviceID),
			zap.String("portID", normalPortID))

		// 从内存中查找端口
		for _, port := range sw.Ports {
			if port.PortID == normalPortID {
				return port, nil
			}
		}

		// 如果在内存中没有找到，返回错误
		return Port{}, fmt.Errorf("端口 %s 不存在", normalPortID)
	}

	// 如果配置了DAO，从Prometheus获取真实数据
	// 现在直接使用deviceID查询
	return s.getPortStatusFromPrometheus(deviceID, normalPortID)
}

// getPortStatusFromPrometheus 从Prometheus获取端口状态
func (s *Service) getPortStatusFromPrometheus(deviceID, portID string) (Port, error) {
	s.logger.Debug("从Prometheus获取接口状态",
		zap.String("deviceID", deviceID),
		zap.String("portID", portID))

	// 从接口状态DAO获取操作状态
	opStatus, timestamp, err := s.statusDAO.GetInterfaceStatus(context.Background(), deviceID, portID)
	if err != nil {
		s.logger.Warn("获取接口操作状态失败",
			zap.String("deviceID", deviceID),
			zap.String("portID", portID),
			zap.Error(err))

		// 如果查询失败，返回错误，不再生成默认值
		return Port{}, fmt.Errorf("获取接口操作状态失败: %w", err)
	}

	// 获取管理状态
	adminStatus, _, err := s.statusDAO.GetInterfaceAdminStatus(context.Background(), deviceID, portID)
	if err != nil {
		s.logger.Warn("获取接口管理状态失败",
			zap.String("deviceID", deviceID),
			zap.String("portID", portID),
			zap.Error(err))

		// 如果查询失败，返回错误，不再使用默认值
		return Port{}, fmt.Errorf("获取接口管理状态失败: %w", err)
	}

	// 转换操作状态和管理状态为字符串
	var physicalState, protocolState string

	// 根据管理状态设置物理状态
	switch adminStatus {
	case 1:
		physicalState = "up"
	case 2:
		physicalState = "down"
	case 3:
		physicalState = "testing"
	default:
		physicalState = "unknown"
	}

	// 根据操作状态设置协议状态
	switch opStatus {
	case 1:
		protocolState = "up"
	case 2:
		protocolState = "down"
	default:
		protocolState = "unknown"
	}

	// 如果配置了TrafficDAO，获取流量数据
	var inBytes, outBytes, inPkts, outPkts int64
	var inRate, outRate *int64
	var rateUnit string
	var trafficTimestamp time.Time

	if s.trafficDAO != nil {
		// 构建查询
		now := time.Now()
		inBytesQuery := fmt.Sprintf(`dci_snmp_ifHCInOctets{device_id="%s", ifName="%s"}`, deviceID, portID)
		outBytesQuery := fmt.Sprintf(`dci_snmp_ifHCOutOctets{device_id="%s", ifName="%s"}`, deviceID, portID)
		inPktsQuery := fmt.Sprintf(`dci_snmp_ifHCInUcastPkts{device_id="%s", ifName="%s"}`, deviceID, portID)
		outPktsQuery := fmt.Sprintf(`dci_snmp_ifHCOutUcastPkts{device_id="%s", ifName="%s"}`, deviceID, portID)

		// 执行查询
		inBytesVal, err1 := s.trafficDAO.QueryRate(context.Background(), inBytesQuery, now)
		outBytesVal, err2 := s.trafficDAO.QueryRate(context.Background(), outBytesQuery, now)
		inPktsVal, err3 := s.trafficDAO.QueryRate(context.Background(), inPktsQuery, now)
		outPktsVal, err4 := s.trafficDAO.QueryRate(context.Background(), outPktsQuery, now)

		if err1 != nil || err2 != nil || err3 != nil || err4 != nil {
			s.logger.Warn("获取接口流量数据失败",
				zap.String("deviceID", deviceID),
				zap.String("portID", portID),
				zap.Error(err1),
				zap.Error(err2),
				zap.Error(err3),
				zap.Error(err4))
			// 流量数据失败不影响端口状态返回，使用默认值
			inBytes, outBytes, inPkts, outPkts = 0, 0, 0, 0
			rateUnit = "bps"
			trafficTimestamp = timestamp
		} else {
			// 转换查询结果
			inBytes = int64(inBytesVal)
			outBytes = int64(outBytesVal)
			inPkts = int64(inPktsVal)
			outPkts = int64(outPktsVal)

			// 计算速率
			inRateVal := int64(inBytesVal * 8)   // 转换为bps
			outRateVal := int64(outBytesVal * 8) // 转换为bps
			inRate = &inRateVal
			outRate = &outRateVal

			rateUnit = "bps"
			trafficTimestamp = now
		}
	} else {
		s.logger.Debug("TrafficDAO未配置，使用默认流量数据",
			zap.String("deviceID", deviceID),
			zap.String("portID", portID))
		inBytes, outBytes, inPkts, outPkts = 0, 0, 0, 0
		rateUnit = "bps"
		trafficTimestamp = timestamp
	}

	// 创建端口对象
	port := Port{
		PortID:        portID,
		Name:          portID, // 使用portID作为名称
		PhysicalState: physicalState,
		ProtocolState: protocolState,
		InErrors:      0, // 暂不支持错误计数
		OutErrors:     0,
		Description:   "", // 暂无描述
		PortTraffic: PortTraffic{
			TotalInBytes:  inBytes,
			TotalOutBytes: outBytes,
			TotalInPkts:   inPkts,
			TotalOutPkts:  outPkts,
			InRate:        inRate,
			OutRate:       outRate,
			RateUnit:      rateUnit,
			Timestamp:     trafficTimestamp,
		},
	}

	return port, nil
}

// GetPortAdminStatus 获取端口管理状态
func (s *Service) GetPortAdminStatus(deviceID, portID string) (PortAdminStatus, error) {
	s.logger.Info("从Prometheus获取接口管理状态",
		zap.String("deviceID", deviceID),
		zap.String("portID", portID))

	// 检查是否配置了接口状态DAO
	if s.statusDAO == nil {
		return PortAdminStatus{}, fmt.Errorf("接口状态DAO未配置，无法获取管理状态")
	}

	// 从接口状态DAO获取管理状态
	status, timestamp, err := s.statusDAO.GetInterfaceAdminStatus(context.Background(), deviceID, portID)
	if err != nil {
		s.logger.Error("获取接口管理状态失败",
			zap.String("deviceID", deviceID),
			zap.String("portID", portID),
			zap.Error(err))
		return PortAdminStatus{}, fmt.Errorf("Prometheus中未找到设备%s的接口%s的管理状态数据", deviceID, portID)
	}

	// 创建响应对象
	adminStatus := PortAdminStatus{
		DeviceID: deviceID,
		PortID:   portID,
		PortName: portID,
		Status:   status,
		UpdateAt: timestamp,
	}

	return adminStatus, nil
}

// GetPortUpstreamTraffic 获取端口上行流量
func (s *Service) GetPortUpstreamTraffic(deviceID, portID string) (PortUpstreamTraffic, error) {
	// 如果配置了TrafficDAO，获取流量数据
	if s.trafficDAO == nil {
		return PortUpstreamTraffic{}, fmt.Errorf("流量DAO未配置，无法获取上行流量数据")
	}

	return s.getUpstreamTrafficFromPrometheus(deviceID, portID)
}

// getUpstreamTrafficFromPrometheus 从Prometheus获取上行流量数据
func (s *Service) getUpstreamTrafficFromPrometheus(deviceID, portID string) (PortUpstreamTraffic, error) {
	s.logger.Debug("从Prometheus获取接口流量数据",
		zap.String("deviceID", deviceID),
		zap.String("portID", portID))

	// 构建查询
	now := time.Now()
	inBytesQuery := fmt.Sprintf(`dci_snmp_ifHCInOctets{device_id="%s", ifName="%s"}`, deviceID, portID)
	inPktsQuery := fmt.Sprintf(`dci_snmp_ifHCInUcastPkts{device_id="%s", ifName="%s"}`, deviceID, portID)

	// 执行查询
	inBytesVal, err1 := s.trafficDAO.QueryRate(context.Background(), inBytesQuery, now)
	inPktsVal, err2 := s.trafficDAO.QueryRate(context.Background(), inPktsQuery, now)

	if err1 != nil || err2 != nil {
		s.logger.Error("获取接口流量数据失败",
			zap.String("deviceID", deviceID),
			zap.String("portID", portID),
			zap.Error(err1),
			zap.Error(err2))
		return PortUpstreamTraffic{}, fmt.Errorf("获取上行流量数据失败: %w", err1)
	}

	// 转换查询结果
	inBytes := int64(inBytesVal)
	inPkts := int64(inPktsVal)

	// 计算速率
	inRateVal := int64(inBytesVal * 8) // 转换为bps
	inRate := &inRateVal

	// 创建上行流量对象
	upstreamTraffic := PortUpstreamTraffic{
		DeviceID:     deviceID,
		PortID:       portID,
		PortName:     portID,
		TotalInBytes: inBytes,
		TotalInPkts:  inPkts,
		InRate:       inRate,
		RateUnit:     "bps",
		Timestamp:    now,
	}

	return upstreamTraffic, nil
}

// GetPortDownstreamTraffic 获取端口下行流量
func (s *Service) GetPortDownstreamTraffic(deviceID, portID string) (PortDownstreamTraffic, error) {
	// 如果配置了TrafficDAO，获取流量数据
	if s.trafficDAO == nil {
		return PortDownstreamTraffic{}, fmt.Errorf("流量DAO未配置，无法获取下行流量数据")
	}

	return s.getDownstreamTrafficFromPrometheus(deviceID, portID)
}

// getDownstreamTrafficFromPrometheus 从Prometheus获取下行流量数据
func (s *Service) getDownstreamTrafficFromPrometheus(deviceID, portID string) (PortDownstreamTraffic, error) {
	s.logger.Debug("从Prometheus获取接口流量数据",
		zap.String("deviceID", deviceID),
		zap.String("portID", portID))

	// 构建查询
	now := time.Now()
	outBytesQuery := fmt.Sprintf(`dci_snmp_ifHCOutOctets{device_id="%s", ifName="%s"}`, deviceID, portID)
	outPktsQuery := fmt.Sprintf(`dci_snmp_ifHCOutUcastPkts{device_id="%s", ifName="%s"}`, deviceID, portID)

	// 执行查询
	outBytesVal, err1 := s.trafficDAO.QueryRate(context.Background(), outBytesQuery, now)
	outPktsVal, err2 := s.trafficDAO.QueryRate(context.Background(), outPktsQuery, now)

	if err1 != nil || err2 != nil {
		s.logger.Error("获取接口流量数据失败",
			zap.String("deviceID", deviceID),
			zap.String("portID", portID),
			zap.Error(err1),
			zap.Error(err2))
		return PortDownstreamTraffic{}, fmt.Errorf("获取下行流量数据失败: %w", err1)
	}

	// 转换查询结果
	outBytes := int64(outBytesVal)
	outPkts := int64(outPktsVal)

	// 计算速率
	outRateVal := int64(outBytesVal * 8) // 转换为bps
	outRate := &outRateVal

	// 创建下行流量对象
	downstreamTraffic := PortDownstreamTraffic{
		DeviceID:      deviceID,
		PortID:        portID,
		PortName:      portID,
		TotalOutBytes: outBytes,
		TotalOutPkts:  outPkts,
		OutRate:       outRate,
		RateUnit:      "bps",
		Timestamp:     now,
	}

	return downstreamTraffic, nil
}
