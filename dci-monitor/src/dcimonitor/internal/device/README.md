# 设备模块重构

本目录实现了设备模块的重构，按照DCI项目API代码规范指南进行了代码组织调整，将原有代码按照功能模块划分为：

- `model.go`：定义设备相关的数据模型
- `service.go`：提供业务逻辑层
- `dao.go`：负责数据访问
- `handler.go`：处理HTTP请求和响应

## 重构内容

1. 从原 `models` 包中提取并迁移了设备相关的数据结构到 `model.go`
2. 从原 `services/switch_service.go` 中迁移了设备服务到 `service.go`
3. 创建了设备数据访问对象 `DAO` 结构体
4. 从原 `monitors/switch_monitor.go` 中迁移了设备监控处理器到 `handler.go`
5. 调整了DAO接口定义，使其与原有服务兼容
6. 修改了Service实现，适配新的DAO接口

## 测试方法

已在`cmd/server.go`中添加了测试路由，可以通过以下方式测试新模块：

- `GET /api/v1/switches/v2/{deviceID}` - 测试获取设备状态
- `GET /api/v1/switches/v2/{deviceID}/ports/{ifName}` - 测试获取端口数据
- `GET /api/v1/switches/v2/{deviceID}/ports/{ifName}/admin` - 测试获取端口管理状态

这些路由使用新的device模块处理，但不影响原有路由的功能，可以并行测试比较结果。

## 下一步工作

1. 测试新模块功能是否正常，比较与原有接口的返回结果
2. 如果测试通过，可以替换原有路由，使用新模块处理：

```go
// 修改路由设置部分
switchesGroup := apiV1.Group("/switches")
{
    // 使用新模块处理原有路由
    switchesGroup.GET("/:deviceID", deviceHandler.GetSwitchStatus)
    switchesGroup.GET("/:deviceID/ports/:ifName", deviceHandler.GetPortData)
    switchesGroup.GET("/:deviceID/ports/:ifName/admin", deviceHandler.GetPortAdminStatus)
    
    // 移除测试路由
    // switchesGroup.GET("/v2/:deviceID", deviceHandler.GetSwitchStatus)
    // switchesGroup.GET("/v2/:deviceID/ports/:ifName", deviceHandler.GetPortData)
    // switchesGroup.GET("/v2/:deviceID/ports/:ifName/admin", deviceHandler.GetPortAdminStatus)

    // 整合单端口流量查询路由
    switchesGroup.GET("/:deviceID/ports/:ifName/flow", singlePortMonitor.GetPortFlow)
    switchesGroup.GET("/:deviceID/ports/:ifName/flow/history", singlePortMonitor.GetPortFlowHistory)
}
```

3. 删除原有的`monitors/switch_monitor.go`和`services/switch_service.go`文件

## 接口说明

Device模块提供以下API接口：

- `GET /api/v1/switches/{deviceID}` - 获取设备状态
- `GET /api/v1/switches/{deviceID}/ports/{ifName}` - 获取端口数据
- `GET /api/v1/switches/{deviceID}/ports/{ifName}/admin` - 获取端口管理状态 

## 注意事项

1. 由于接口兼容性问题，我们调整了DAO接口定义，确保新模块可以使用原有的数据访问实现
2. 在完全迁移前，保留了原有的路由和处理函数，确保系统稳定运行
3. 测试通过后，可以逐步替换原有代码，完成完整迁移 