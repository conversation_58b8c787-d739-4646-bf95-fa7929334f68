package device

import (
	"encoding/base64"
	"net/http"
	"strings"

	"common/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
)

// Handler 处理与设备监控相关的HTTP请求
type Handler struct {
	service *Service
}

// NewHandler 创建一个新的设备处理器实例
func NewHandler(service *Service) *Handler {
	return &Handler{
		service: service,
	}
}

// addRequestID 为每个请求添加一个唯一的 ID
func (h *Handler) addRequestID(ctx *gin.Context) string {
	reqID := uuid.New().String()
	ctx.Set("requestID", reqID) // 存储在 context 中，方便日志记录
	return reqID
}

// GetSwitchStatus 获取指定交换机的状态
// @Summary 获取交换机完整状态
// @Description 根据提供的设备 ID，获取该交换机的当前详细状态信息，包括 CPU 利用率、内存利用率以及所有端口的基本状态列表。
// @Tags 05-设备监控
// @Accept json
// @Produce json
// @Param deviceID path string true "要查询状态的设备 ID" example(CE1)
// @Success 200 {object} SwitchStatus "成功获取交换机状态"
// @Failure 404 {object} models.ErrorResponse "交换机未找到"
// @Failure 500 {object} models.ErrorResponse "服务器内部错误"
// @Router /api/v1/switches/{deviceID} [get]
func (h *Handler) GetSwitchStatus(ctx *gin.Context) {
	reqID := h.addRequestID(ctx)
	deviceID := ctx.Param("deviceID")

	logger.Info("收到获取交换机状态请求", zap.String("requestID", reqID), zap.String("deviceID", deviceID))

	status, err := h.service.GetSwitchStatus(deviceID)
	if err != nil {
		logger.Error("获取交换机状态失败", zap.String("requestID", reqID), zap.String("deviceID", deviceID), zap.Error(err))
		if err.Error() == "交换机不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{"requestID": reqID, "error": err.Error()})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{"requestID": reqID, "error": "获取状态时发生内部错误"})
		}
		return
	}

	// 使用 jsoniter 序列化
	jsonData, err := jsoniter.Marshal(status)
	if err != nil {
		logger.Error("JSON 序列化失败 (jsoniter)", zap.String("requestID", reqID), zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{"requestID": reqID, "error": "内部序列化错误"})
		return
	}

	ctx.Data(http.StatusOK, "application/json; charset=utf-8", jsonData)
}

// GetPortData 处理获取端口状态或流量的统一请求
// @Summary 获取端口数据 (状态/上行/下行)
// @Description 根据路径参数 `deviceID`, `ifName` 和查询参数 `data` 获取特定端口的信息。
// @Description 注意: `ifName` 参数使用Base64编码，是为了安全地在URL路径中传递包含特殊字符（如'/'）的接口名称。
// @Description 例如，接口 "10GE1/0/1" 应当编码为 "MTBHRS8wLzE="。
// @Tags 05-设备监控
// @Accept json
// @Produce json
// @Param deviceID path string true "端口所属的设备 ID" example(210)
// @Param ifName path string true "要查询的端口名称，使用Base64编码。例如，将 '10GE1/0/1' 编码为 'MTBHRS8wLzE='" example({{'10GE1/0/1'|base64}})
// @Param data query string false "请求的数据类型。如果省略或提供无效值, 默认为 status。" Enums(status, upstream, downstream) default(status)
// @Success 200 {object} PortStatusResponse "当 data=status 时的响应，包含设备ID和端口完整状态，包括流量数据"
// @Success 200 {object} PortUpstreamTraffic "当 data=upstream 时的响应，包含上行流量数据：totalInBytes, totalInPkts, inUtil等"
// @Success 200 {object} PortDownstreamTraffic "当 data=downstream 时的响应，包含下行流量数据：totalOutBytes, totalOutPkts, outUtil等"
// @Failure 400 {object} models.ErrorResponse "无效的请求参数"
// @Failure 404 {object} models.ErrorResponse "指定的设备或端口未找到"
// @Failure 500 {object} models.ErrorResponse "服务器内部错误"
// @Router /api/v1/switches/{deviceID}/ports/{ifName} [get]
func (h *Handler) GetPortData(ctx *gin.Context) {
	reqID := h.addRequestID(ctx)
	deviceID := ctx.Param("deviceID")
	encodedIfName := ctx.Param("ifName")
	dataType := ctx.DefaultQuery("data", "status")

	// Base64解码ifName
	ifName, err := decodeIfName(encodedIfName)
	if err != nil {
		logger.Error("解码端口名称失败",
			zap.String("requestID", reqID),
			zap.String("deviceID", deviceID),
			zap.String("encodedIfName", encodedIfName),
			zap.Error(err),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"requestID": reqID, "error": "无效的端口名称编码"})
		return
	}

	logger.Info("收到获取端口数据请求",
		zap.String("requestID", reqID),
		zap.String("deviceID", deviceID),
		zap.String("ifName", ifName),
		zap.String("dataType", dataType),
	)

	var responseData any
	var dataErr error // 声明 dataErr 变量

	switch dataType {
	case "upstream":
		var traffic PortUpstreamTraffic
		traffic, dataErr = h.service.GetPortUpstreamTraffic(deviceID, ifName)
		responseData = traffic
	case "downstream":
		var traffic PortDownstreamTraffic
		traffic, dataErr = h.service.GetPortDownstreamTraffic(deviceID, ifName)
		responseData = traffic
	case "status":
		fallthrough
	default:
		if dataType != "status" {
			logger.Warn("无效的 data 查询参数，将返回端口状态", zap.String("requestID", reqID), zap.String("dataType", dataType))
			dataType = "status"
		}
		var portData Port
		portData, dataErr = h.service.GetPortStatus(deviceID, ifName)
		responseData = PortStatusResponse{
			DeviceID: deviceID,
			Port:     portData,
		}
	}

	// 检查获取数据时是否发生错误
	if dataErr != nil {
		logger.Error("获取端口数据失败",
			zap.String("requestID", reqID),
			zap.String("deviceID", deviceID),
			zap.String("ifName", ifName),
			zap.String("dataType", dataType),
			zap.Error(dataErr),
		)
		if strings.Contains(dataErr.Error(), "不存在") {
			ctx.JSON(http.StatusNotFound, gin.H{"requestID": reqID, "error": dataErr.Error()})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{"requestID": reqID, "error": "获取端口数据时发生内部错误"})
		}
		return
	}

	// 使用 jsoniter 序列化
	jsonData, jsonErr := jsoniter.Marshal(responseData)
	if jsonErr != nil {
		logger.Error("JSON 序列化失败 (jsoniter)", zap.String("requestID", reqID), zap.Error(jsonErr))
		ctx.JSON(http.StatusInternalServerError, gin.H{"requestID": reqID, "error": "内部序列化错误"})
		return
	}

	ctx.Data(http.StatusOK, "application/json; charset=utf-8", jsonData)
}

// GetPortAdminStatus 获取端口管理状态
// @Summary 获取端口管理状态
// @Description 根据路径参数 `deviceID` 和 `ifName` 获取特定端口的管理状态信息。
// @Description 注意: ifName 使用 Base64 编码，不再支持特殊字符的 URL 编码方式。
// @Description 例如："100GE1/0/6" 编码为 "MTAwR0UxLzAvNg=="
// @Tags 05-设备监控
// @Accept json
// @Produce json
// @Param deviceID path string true "端口所属的设备 ID" example(210)
// @Param ifName path string true "要查询的端口名称，使用Base64编码" example(MTAwR0UxLzAvNg==)
// @Success 200 {object} PortAdminStatus "成功获取端口管理状态"
// @Failure 400 {object} models.ErrorResponse "无效的请求参数"
// @Failure 404 {object} models.ErrorResponse "指定的设备或端口未找到"
// @Failure 500 {object} models.ErrorResponse "服务器内部错误"
// @Router /api/v1/switches/{deviceID}/ports/{ifName}/admin [get]
func (h *Handler) GetPortAdminStatus(ctx *gin.Context) {
	reqID := h.addRequestID(ctx)
	deviceID := ctx.Param("deviceID")
	encodedIfName := ctx.Param("ifName")

	// Base64解码ifName
	ifName, err := decodeIfName(encodedIfName)
	if err != nil {
		logger.Error("解码端口名称失败",
			zap.String("requestID", reqID),
			zap.String("deviceID", deviceID),
			zap.String("encodedIfName", encodedIfName),
			zap.Error(err),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"requestID": reqID, "error": "无效的端口名称编码"})
		return
	}

	logger.Info("收到获取端口管理状态请求",
		zap.String("requestID", reqID),
		zap.String("deviceID", deviceID),
		zap.String("ifName", ifName),
	)

	// 调用服务层获取端口管理状态
	adminStatus, dataErr := h.service.GetPortAdminStatus(deviceID, ifName)
	if dataErr != nil {
		logger.Error("获取端口管理状态失败",
			zap.String("requestID", reqID),
			zap.String("deviceID", deviceID),
			zap.String("ifName", ifName),
			zap.Error(dataErr),
		)
		if strings.Contains(dataErr.Error(), "不存在") {
			ctx.JSON(http.StatusNotFound, gin.H{"requestID": reqID, "error": dataErr.Error()})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{"requestID": reqID, "error": "获取端口管理状态时发生内部错误"})
		}
		return
	}

	// 使用 jsoniter 序列化
	jsonData, jsonErr := jsoniter.Marshal(adminStatus)
	if jsonErr != nil {
		logger.Error("JSON 序列化失败 (jsoniter)", zap.String("requestID", reqID), zap.Error(jsonErr))
		ctx.JSON(http.StatusInternalServerError, gin.H{"requestID": reqID, "error": "内部序列化错误"})
		return
	}

	ctx.Data(http.StatusOK, "application/json; charset=utf-8", jsonData)
}

// decodeIfName 将Base64编码的接口名称解码回原始形式
func decodeIfName(encodedIfName string) (string, error) {
	decoded, err := base64.StdEncoding.DecodeString(encodedIfName)
	if err != nil {
		return "", err
	}
	return string(decoded), nil
}
