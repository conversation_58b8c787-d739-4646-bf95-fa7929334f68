package device

import "time"

// SwitchInfo 定义了配置文件中的交换机基本信息
type SwitchInfo struct {
	ID        string `yaml:"id"`
	Name      string `yaml:"name"`
	IP        string `yaml:"ip"`
	Community string `yaml:"community"`
}

// SwitchStatus 表示交换机的整体状态 (存储在内存中)
type SwitchStatus struct {
	SwitchInfo
	Status      string    `json:"status"` // 整体状态: "online", "offline", "unknown"
	LastUpdated time.Time `json:"lastUpdated"`
	Ports       []Port    `json:"ports"`
}

// Port 表示交换机端口的状态
type Port struct {
	PortID        string `json:"portId"`        // Changed field name to PortID
	Name          string `json:"name"`          // 例如 "GigabitEthernet1/0/0"
	PhysicalState string `json:"physicalState"` // 物理状态: "up", "down", "admin_down"
	ProtocolState string `json:"protocolState"` // 协议状态: "up", "down"
	InErrors      int64  `json:"inErrors"`
	OutErrors     int64  `json:"outErrors"`
	Description   string `json:"description"`
	// 端口流量信息
	PortTraffic
}

// PortTraffic 表示端口流量信息
type PortTraffic struct {
	TotalInBytes  int64     `json:"totalInBytes"`      // 上行字节总量
	TotalOutBytes int64     `json:"totalOutBytes"`     // 下行字节总量
	TotalInPkts   int64     `json:"totalInPkts"`       // 上行包总量
	TotalOutPkts  int64     `json:"totalOutPkts"`      // 下行包总量
	InRate        *int64    `json:"inRate,omitempty"`  // 上行速率
	OutRate       *int64    `json:"outRate,omitempty"` // 下行速率
	RateUnit      string    `json:"rateUnit"`          // 速率单位 (e.g., "bps")
	Timestamp     time.Time `json:"timestamp"`         // 流量数据获取时间
}

// PortUpstreamTraffic 用于仅返回上行流量
type PortUpstreamTraffic struct {
	DeviceID     string    `json:"deviceId"`
	PortID       string    `json:"portId"`
	PortName     string    `json:"portName"`
	TotalInBytes int64     `json:"totalInBytes"`
	TotalInPkts  int64     `json:"totalInPkts"`
	InRate       *int64    `json:"inRate,omitempty"`
	RateUnit     string    `json:"rateUnit"`
	Timestamp    time.Time `json:"timestamp"`
}

// PortDownstreamTraffic 用于仅返回下行流量
type PortDownstreamTraffic struct {
	DeviceID      string    `json:"deviceId"`
	PortID        string    `json:"portId"`
	PortName      string    `json:"portName"`
	TotalOutBytes int64     `json:"totalOutBytes"`
	TotalOutPkts  int64     `json:"totalOutPkts"`
	OutRate       *int64    `json:"outRate,omitempty"`
	RateUnit      string    `json:"rateUnit"`
	Timestamp     time.Time `json:"timestamp"`
}

// PortStatusResponse 用于组合端口状态和交换机ID
type PortStatusResponse struct {
	DeviceID string `json:"deviceId"`
	Port            // Embed the Port struct
}

// PortAdminStatus 表示端口的管理状态
type PortAdminStatus struct {
	DeviceID string    `json:"deviceId"`
	PortID   string    `json:"portId"`
	PortName string    `json:"portName"`
	Status   int       `json:"status"` // 1:up, 2:down
	UpdateAt time.Time `json:"updateAt"`
}

// SinglePortFlowQueryParams 定义了端口流量查询参数结构
type SinglePortFlowQueryParams struct {
	VNI       string `form:"vni" binding:"omitempty"`
	StartTime string `form:"start_time" binding:"required"`
	EndTime   string `form:"end_time" binding:"required"`
}

// SinglePortHistoryQueryParams 定义了端口历史流量查询参数结构
type SinglePortHistoryQueryParams struct {
	VNI       string `form:"vni" binding:"omitempty"`
	StartTime string `form:"start_time" binding:"required"`
	EndTime   string `form:"end_time" binding:"required"`
	Step      string `form:"step" binding:"required"`
}

// PortFlowResponse 是端口流量接口的响应结构
type PortFlowResponse struct {
	RequestID    string               `json:"request_id"`
	QueryDetails PortFlowQueryDetails `json:"query_details"`
	FlowData     PortFlowData         `json:"flow_data"`
}

// PortFlowQueryDetails 包含单端口流量查询的请求详情
type PortFlowQueryDetails struct {
	DeviceID  string `json:"device_id"`
	PortID    string `json:"port_id"`
	VNI       string `json:"vni,omitempty"`
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
}

// PortFlowData 包含单个端口的计算后流量数据
type PortFlowData struct {
	UnitRate  string  `json:"unit_rate"`  // 速率单位, e.g., "Mbps"
	UnitTotal string  `json:"unit_total"` // 总量单位, e.g., "MB", "GB"
	InRate    float64 `json:"in_rate"`
	OutRate   float64 `json:"out_rate"`
	InTotal   float64 `json:"in_total"`
	OutTotal  float64 `json:"out_total"`
}

// PortHistoryResponse 是端口历史流量接口的响应结构
type PortHistoryResponse struct {
	RequestID    string                  `json:"request_id"`
	QueryDetails PortHistoryQueryDetails `json:"query_details"`
	HistoryData  PortHistoryData         `json:"history_data"`
}

// PortHistoryQueryDetails 包含单端口历史流量查询的请求详情
type PortHistoryQueryDetails struct {
	DeviceID  string `json:"device_id"`
	PortID    string `json:"port_id"`
	VNI       string `json:"vni,omitempty"`
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	Step      string `json:"step"`
}

// PortHistoryData 包含图表所需的时间戳、单位和数据系列
type PortHistoryData struct {
	Unit       string          `json:"unit"`       // 数据单位 (e.g., "Mbps")
	Timestamps []string        `json:"timestamps"` // RFC3339 UTC 格式的时间戳数组
	Series     []TrafficSeries `json:"series"`     // 数据系列数组
}

// TrafficSeries 代表一个数据系列，例如 "A端入流量"。
type TrafficSeries struct {
	Name string    `json:"name"` // 系列名称 (e.g., "A端 (CE1-GE1/0/1) 平均入流量")
	Data []float64 `json:"data"` // 与 Timestamps 对应的数值数组
}
