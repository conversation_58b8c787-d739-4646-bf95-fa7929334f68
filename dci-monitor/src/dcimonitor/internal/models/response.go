package models

// Response 通用API响应结构
type Response struct {
	Code int         `json:"code"` // 状态码
	Data interface{} `json:"data"` // 响应数据
}

// PaginatedResponse 分页响应
type PaginatedResponse struct {
	RequestID string      `json:"request_id"`         // 请求ID
	Total     int         `json:"total"`              // 总记录数
	Page      int         `json:"page"`               // 当前页码
	PageSize  int         `json:"page_size"`          // 每页记录数
	Data      interface{} `json:"data"`               // 数据
	HasMore   bool        `json:"has_more,omitempty"` // 是否有更多数据
}

// SuccessResponse 通用成功响应
type SuccessResponse struct {
	RequestID string `json:"request_id"` // 请求ID
	Message   string `json:"message"`    // 成功消息
}

// ErrorResponse 是标准的 API 错误响应结构。
type ErrorResponse struct {
	RequestID string `json:"request_id"`        // 请求ID
	Error     string `json:"error"`             // 错误信息
	Details   string `json:"details,omitempty"` // 错误详情
}
