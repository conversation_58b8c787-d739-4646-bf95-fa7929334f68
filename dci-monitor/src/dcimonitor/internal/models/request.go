package models

import "time"

// SinglePortFlowQueryParams 定义了 /api/v1/switches/{deviceID}/ports/{ifName}/flow 接口的查询参数结构
type SinglePortFlowQueryParams struct {
	// 路径参数由路由处理函数注入，不需要在form中绑定
	DeviceID  string    // 从路径参数获取
	IfName    string    // 从路径参数获取，已解码的接口名称
	VNI       string    `form:"vni"`
	StartTime time.Time `form:"start_time" time_format:"2006-01-02T15:04:05Z07:00"`
	EndTime   time.Time `form:"end_time" time_format:"2006-01-02T15:04:05Z07:00"`
}

// SinglePortHistoryQueryParams 定义了 /api/v1/switches/{deviceID}/ports/{ifName}/flow/history 接口的查询参数结构
type SinglePortHistoryQueryParams struct {
	// 路径参数由路由处理函数注入，不需要在form中绑定
	DeviceID  string    // 从路径参数获取
	IfName    string    // 从路径参数获取，已解码的接口名称
	VNI       string    `form:"vni"`
	StartTime time.Time `form:"start_time" time_format:"2006-01-02T15:04:05Z07:00" binding:"required"`
	EndTime   time.Time `form:"end_time" time_format:"2006-01-02T15:04:05Z07:00" binding:"required"`
	Step      string    `form:"step"`
}

// AZLinkQueryParams 定义了所有A-Z链路图表和摘要接口共用的查询参数结构
type AZLinkQueryParams struct {
	Granularity string    `form:"granularity" binding:"required,oneof=1m 5m 1h 1d"`
	TimeRange   string    `form:"time_range"`
	StartTime   time.Time `form:"start_time" time_format:"2006-01-02T15:04:05Z07:00"`
	EndTime     time.Time `form:"end_time" time_format:"2006-01-02T15:04:05Z07:00"`
	ASwitchID   string    `form:"a_switch_id" binding:"required"`
	APortIDRaw  string    `form:"a_port_id" binding:"required"`
	ZSwitchID   string    `form:"z_switch_id" binding:"required"`
	ZPortIDRaw  string    `form:"z_port_id" binding:"required"`
	VNI         string    `form:"vni"` // 新增VNI参数
	APortID     string    // 解码后的 Port ID
	ZPortID     string    // 解码后的 Port ID
}

// 通用请求参数结构，如分页、排序等
// 目前没有通用的请求结构，将在需要时添加
