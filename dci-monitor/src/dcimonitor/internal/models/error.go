package models

import "fmt"

// APIError 定义了 API 调用中可能发生的业务逻辑错误，包含 HTTP 状态码。
type APIError struct {
	Code      int    `json:"-"` // HTTP Status Code (不在 JSON 中显示)
	Message   string `json:"error"`
	Details   string `json:"details,omitempty"`
	RequestID string `json:"request_id,omitempty"` // 请求ID，方便排查问题
}

// Error 实现 error 接口。
func (e *APIError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s", e.Message, e.Details)
	}
	return e.Message
}

// NewAPIError 创建一个新的 APIError 实例。
func NewAPIError(code int, message, details string) *APIError {
	return &APIError{Code: code, Message: message, Details: details}
}

// WithRequestID 添加请求ID
func (e *APIError) WithRequestID(requestID string) *APIError {
	e.RequestID = requestID
	return e
}

// NewAPIErrorWithRequestID 创建带请求ID的API错误
func NewAPIErrorWithRequestID(code int, message, details, requestID string) *APIError {
	return &APIError{
		Code:      code,
		Message:   message,
		Details:   details,
		RequestID: requestID,
	}
}
