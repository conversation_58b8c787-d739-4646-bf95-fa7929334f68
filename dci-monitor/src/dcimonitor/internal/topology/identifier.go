package topology

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"go.uber.org/zap"
)

// IdentifierService 提供设备和端口标识符映射功能
type IdentifierService struct {
	db     *sql.DB
	logger *zap.Logger
}

// NewIdentifierService 创建一个新的IdentifierService实例
func NewIdentifierService(db *sql.DB, logger *zap.Logger) *IdentifierService {
	return &IdentifierService{
		db:     db,
		logger: logger,
	}
}

// MappingResult 表示映射结果的结构体
type MappingResult struct {
	DeviceID      string
	InterfaceID   string
	MappingStatus string
}

// MapLLDPNeighbor 映射LLDP邻居信息到设备和接口标识符
func (s *IdentifierService) MapLLDPNeighbor(chassisID, portID string) (*MappingResult, error) {
	result := &MappingResult{
		MappingStatus: MappingStatusFailed,
	}

	// 尝试通过MAC地址映射设备
	deviceID, err := s.mapDeviceByMAC(chassisID)
	if err != nil {
		s.logger.Error("映射设备失败", zap.Error(err), zap.String("chassisID", chassisID))
		return result, err
	}

	if deviceID == "" {
		s.logger.Warn("设备映射未找到结果", zap.String("chassisID", chassisID))
		return result, nil
	}

	// 设备找到，更新结果
	result.DeviceID = deviceID
	result.MappingStatus = MappingStatusPartial

	// 尝试映射接口
	interfaceID, err := s.mapInterface(deviceID, portID)
	if err != nil {
		s.logger.Error("映射接口失败", zap.Error(err),
			zap.String("deviceID", deviceID), zap.String("portID", portID))
		return result, nil
	}

	if interfaceID == "" {
		s.logger.Warn("接口映射未找到结果",
			zap.String("deviceID", deviceID), zap.String("portID", portID))
		return result, nil
	}

	// 接口也找到了，更新为完全映射
	result.InterfaceID = interfaceID
	result.MappingStatus = MappingStatusMapped

	return result, nil
}

// mapDeviceByMAC 通过MAC地址映射设备ID
func (s *IdentifierService) mapDeviceByMAC(mac string) (string, error) {
	// 标准化MAC地址格式
	normalizedMAC := s.normalizeMAC(mac)
	if normalizedMAC == "" {
		return "", errors.New("无效的MAC地址格式")
	}

	// 查询数据库
	var deviceID string
	query := `
		SELECT device_id 
		FROM device_mapping 
		WHERE mac_address = ? 
		LIMIT 1
	`
	err := s.db.QueryRow(query, normalizedMAC).Scan(&deviceID)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", nil // 没找到，但不是错误
		}
		return "", fmt.Errorf("查询设备映射失败: %w", err)
	}

	return deviceID, nil
}

// mapInterface 映射接口ID
func (s *IdentifierService) mapInterface(deviceID, portID string) (string, error) {
	// 标准化端口ID
	normalizedPortID := s.normalizePortID(portID)

	// 查询数据库
	var interfaceID string
	query := `
		SELECT interface_id 
		FROM interface_mapping 
		WHERE device_id = ? AND (port_id = ? OR port_name = ?) 
		LIMIT 1
	`
	err := s.db.QueryRow(query, deviceID, normalizedPortID, portID).Scan(&interfaceID)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", nil // 没找到，但不是错误
		}
		return "", fmt.Errorf("查询接口映射失败: %w", err)
	}

	return interfaceID, nil
}

// normalizeMAC 将MAC地址标准化为统一格式
func (s *IdentifierService) normalizeMAC(mac string) string {
	// 移除所有非十六进制字符
	mac = strings.ToLower(mac)
	var result strings.Builder
	for _, c := range mac {
		if (c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') {
			result.WriteRune(c)
		}
	}

	normalized := result.String()
	// 检查MAC地址是否是12位十六进制数
	if len(normalized) != 12 {
		return ""
	}

	// 转为标准格式（冒号分隔）
	formattedMAC := ""
	for i := 0; i < 12; i += 2 {
		formattedMAC += normalized[i : i+2]
		if i < 10 {
			formattedMAC += ":"
		}
	}

	return formattedMAC
}

// normalizePortID 将端口ID标准化
func (s *IdentifierService) normalizePortID(portID string) string {
	// 移除常见的端口ID前缀
	prefixes := []string{"ifIndex.", "ifName.", "ifDescr."}
	for _, prefix := range prefixes {
		if strings.HasPrefix(portID, prefix) {
			return portID[len(prefix):]
		}
	}
	return portID
}
