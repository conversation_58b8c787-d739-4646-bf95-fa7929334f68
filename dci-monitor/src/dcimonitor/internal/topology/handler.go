package topology

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// Handler 处理拓扑相关的HTTP请求
type Handler struct {
	service *Service
	logger  *zap.Logger
}

// NewHandler 创建一个新的Handler实例
func NewHandler(service *Service, logger *zap.Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// SwaggerTopologyNode 用于Swagger文档的TopologyNode简化版本
// @Description 拓扑图中的节点（设备）
type SwaggerTopologyNode struct {
	ID       string      `json:"id"`       // 节点ID，与设备ID相同
	Type     string      `json:"type"`     // 节点类型：switch、router、server等
	Name     string      `json:"name"`     // 节点名称
	Data     interface{} `json:"data"`     // 节点附加数据，JSON格式
	Position interface{} `json:"position"` // 节点位置（可选）
}

// SwaggerTopologyEdge 用于Swagger文档的TopologyEdge简化版本
// @Description 拓扑图中的边（连接）
type SwaggerTopologyEdge struct {
	ID         string      `json:"id"`         // 边ID
	Source     string      `json:"source"`     // 源节点ID
	Target     string      `json:"target"`     // 目标节点ID
	SourcePort string      `json:"sourcePort"` // 源端口ID
	TargetPort string      `json:"targetPort"` // 目标端口ID
	Type       string      `json:"type"`       // 连接类型：physical、logical等
	Data       interface{} `json:"data"`       // 连接附加数据，JSON格式
}

// SwaggerTopologyGraph 用于Swagger文档的TopologyGraph简化版本
// @Description 完整的拓扑图
type SwaggerTopologyGraph struct {
	ID        string                `json:"id"`        // 拓扑图ID
	Name      string                `json:"name"`      // 拓扑图名称
	CreatedAt time.Time             `json:"createdAt"` // 创建时间
	Nodes     []SwaggerTopologyNode `json:"nodes"`     // 节点列表
	Edges     []SwaggerTopologyEdge `json:"edges"`     // 边列表
}

// TopologyResponse 定义了拓扑API的响应结构
type TopologyResponse struct {
	RequestID string      `json:"request_id"` // 请求ID
	Data      interface{} `json:"data"`       // 响应数据
}

// GetTopology 获取当前拓扑图
// @Summary 获取当前拓扑图
// @Description 获取当前网络拓扑图，包括所有设备节点和连接
// @Tags 拓扑管理
// @Accept json
// @Produce json
// @Success 200 {object} SwaggerTopologyGraph "成功获取拓扑图"
// @Failure 500 {object} models.ErrorResponse "服务器内部错误"
// @Router /api/v1/topology [get]
func (h *Handler) GetTopology(c *gin.Context) {
	// 获取或生成请求ID
	requestID, exists := c.Get("requestID")
	if !exists || requestID == nil {
		requestID = uuid.New().String()
		c.Set("requestID", requestID)
	}

	h.logger.Debug("处理请求",
		zap.String("function", "GetTopology"),
		zap.Any("request_id", requestID))

	// 获取拓扑数据
	topology := h.service.GetTopology()

	// 返回标准响应结构
	c.JSON(http.StatusOK, TopologyResponse{
		RequestID: requestID.(string),
		Data:      topology,
	})
}

// GetTopologyStats 获取拓扑统计信息
// @Summary 获取拓扑统计信息
// @Description 获取当前拓扑图的统计信息，如节点数量和连接数量
// @Tags 拓扑管理
// @Accept json
// @Produce json
// @Success 200 {object} TopologyStats "成功获取拓扑统计信息"
// @Failure 500 {object} models.ErrorResponse "服务器内部错误"
// @Router /api/v1/topology/stats [get]
func (h *Handler) GetTopologyStats(c *gin.Context) {
	// 获取或生成请求ID
	requestID, exists := c.Get("requestID")
	if !exists || requestID == nil {
		requestID = uuid.New().String()
		c.Set("requestID", requestID)
	}

	h.logger.Debug("处理请求",
		zap.String("function", "GetTopologyStats"),
		zap.Any("request_id", requestID))

	// 获取统计数据
	stats := h.service.GetTopologyStats()

	// 返回标准响应结构
	c.JSON(http.StatusOK, TopologyResponse{
		RequestID: requestID.(string),
		Data:      stats,
	})
}

// GetTopologyHealth 获取拓扑服务健康状态
// @Summary 获取拓扑服务健康状态
// @Description 获取拓扑服务的健康状态，包括数据库连接、Kafka连接等
// @Tags 拓扑管理
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "成功获取健康状态"
// @Failure 500 {object} models.ErrorResponse "服务器内部错误"
// @Router /api/v1/topology/health [get]
func (h *Handler) GetTopologyHealth(c *gin.Context) {
	// 获取或生成请求ID
	requestID, exists := c.Get("requestID")
	if !exists || requestID == nil {
		requestID = uuid.New().String()
		c.Set("requestID", requestID)
	}

	h.logger.Debug("处理请求",
		zap.String("function", "GetTopologyHealth"),
		zap.Any("request_id", requestID))

	// 检查服务健康状态
	isHealthy := h.service.HealthCheck()
	status := "healthy"
	if !isHealthy {
		status = "unhealthy"
	}

	// 返回标准响应结构
	c.JSON(http.StatusOK, TopologyResponse{
		RequestID: requestID.(string),
		Data: map[string]interface{}{
			"status":     status,
			"is_healthy": isHealthy,
			"check_time": time.Now().Format(time.RFC3339),
			"node_count": h.service.GetTopologyStats().NodeCount,
			"edge_count": h.service.GetTopologyStats().EdgeCount,
		},
	})
}

// RegisterRoutes 注册拓扑相关的路由
func (h *Handler) RegisterRoutes(router *gin.Engine) {
	apiV1 := router.Group("/api/v1")
	{
		// 拓扑相关的路由
		topologyGroup := apiV1.Group("/topology")
		{
			topologyGroup.GET("", h.GetTopology)
			topologyGroup.GET("/stats", h.GetTopologyStats)
			topologyGroup.GET("/health", h.GetTopologyHealth)
		}
	}
}
