package topology

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

// DAO 提供对拓扑数据的访问
type DAO struct {
	db     *sql.DB
	logger *zap.Logger
}

// NewDAO 创建一个新的DAO实例
func NewDAO(db *sql.DB, logger *zap.Logger) *DAO {
	return &DAO{
		db:     db,
		logger: logger,
	}
}

// GetCurrentSnapshotID 获取当前活动的快照ID
func (d *DAO) GetCurrentSnapshotID(ctx context.Context) (string, error) {
	var snapshotID string
	err := d.db.QueryRowContext(ctx, `
		SELECT id FROM monitor_topology_snapshot 
		WHERE is_current = 1 
		ORDER BY create_time DESC 
		LIMIT 1
	`).Scan(&snapshotID)

	if err != nil {
		return "", err
	}

	return snapshotID, nil
}

// CreateSnapshot 创建新的拓扑快照
func (d *DAO) CreateSnapshot(ctx context.Context, name, description string) (string, error) {
	// 生成新的快照ID
	snapshotID := uuid.New().String()
	now := time.Now()

	// 开始事务
	tx, err := d.db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开始事务失败: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// 插入快照记录
	_, err = tx.ExecContext(ctx, `
		INSERT INTO monitor_topology_snapshot 
		(id, name, description, node_count, edge_count, is_current, created_by, create_time, update_time) 
		VALUES (?, ?, ?, ?, ?, 1, 'system', ?, ?)
	`, snapshotID, name, description, 0, 0, now, now)
	if err != nil {
		return "", fmt.Errorf("创建快照失败: %w", err)
	}

	// 将之前的快照标记为非当前
	_, err = tx.ExecContext(ctx, `
		UPDATE monitor_topology_snapshot 
		SET is_current = 0 
		WHERE id != ? AND is_current = 1
	`, snapshotID)
	if err != nil {
		return "", fmt.Errorf("更新之前的快照状态失败: %w", err)
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %w", err)
	}

	return snapshotID, nil
}

// GetSnapshot 获取指定的拓扑快照信息
func (d *DAO) GetSnapshot(ctx context.Context, snapshotID string) (*TopologySnapshot, error) {
	var snapshot TopologySnapshot
	err := d.db.QueryRowContext(ctx, `
		SELECT id, name, description, node_count, edge_count, is_current, created_by, create_time, update_time
		FROM monitor_topology_snapshot
		WHERE id = ?
	`, snapshotID).Scan(
		&snapshot.ID, &snapshot.Name, &snapshot.Description,
		&snapshot.NodeCount, &snapshot.EdgeCount, &snapshot.IsCurrent,
		&snapshot.CreatedBy, &snapshot.CreateTime, &snapshot.UpdateTime,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("快照 %s 不存在", snapshotID)
		}
		return nil, fmt.Errorf("获取快照信息失败: %w", err)
	}

	return &snapshot, nil
}

// GetSnapshotNodes 获取指定快照的所有节点
func (d *DAO) GetSnapshotNodes(ctx context.Context, snapshotID string) ([]TopologyNode, error) {
	rows, err := d.db.QueryContext(ctx, `
		SELECT id, node_type, properties 
		FROM monitor_topology_node 
		WHERE snapshot_id = ?
	`, snapshotID)
	if err != nil {
		return nil, fmt.Errorf("查询节点失败: %w", err)
	}
	defer rows.Close()

	var nodes []TopologyNode
	for rows.Next() {
		var node TopologyNode
		var propertiesJSON []byte

		if err := rows.Scan(&node.ID, &node.Type, &propertiesJSON); err != nil {
			return nil, fmt.Errorf("扫描节点数据失败: %w", err)
		}

		if len(propertiesJSON) > 0 {
			node.Data = propertiesJSON
		}

		// 查询设备名称
		var deviceName string
		err := d.db.QueryRowContext(ctx, `
			SELECT device_name FROM monitor_device_info WHERE id = ?
		`, node.ID).Scan(&deviceName)
		if err == nil {
			node.Name = deviceName
		} else if !errors.Is(err, sql.ErrNoRows) {
			d.logger.Warn("获取设备名称失败",
				zap.String("deviceID", node.ID),
				zap.Error(err))
		}

		nodes = append(nodes, node)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历节点数据失败: %w", err)
	}

	return nodes, nil
}

// GetSnapshotEdges 获取指定快照的所有边
func (d *DAO) GetSnapshotEdges(ctx context.Context, snapshotID string) ([]TopologyEdge, error) {
	rows, err := d.db.QueryContext(ctx, `
		SELECT id, source_node_id, target_node_id, source_port_id, target_port_id, edge_type, properties 
		FROM monitor_topology_edge 
		WHERE snapshot_id = ?
	`, snapshotID)
	if err != nil {
		return nil, fmt.Errorf("查询边失败: %w", err)
	}
	defer rows.Close()

	var edges []TopologyEdge
	for rows.Next() {
		var edge TopologyEdge
		var propertiesJSON []byte

		if err := rows.Scan(&edge.ID, &edge.Source, &edge.Target, &edge.SourcePort, &edge.TargetPort, &edge.Type, &propertiesJSON); err != nil {
			return nil, fmt.Errorf("扫描边数据失败: %w", err)
		}

		if len(propertiesJSON) > 0 {
			edge.Data = propertiesJSON
		}

		edges = append(edges, edge)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历边数据失败: %w", err)
	}

	return edges, nil
}

// SaveNodeToSnapshot 保存节点到快照
func (d *DAO) SaveNodeToSnapshot(ctx context.Context, snapshotID string, node *TopologyNode) error {
	// 检查节点是否已存在
	var count int
	err := d.db.QueryRowContext(ctx, `
		SELECT COUNT(*) FROM monitor_topology_node 
		WHERE snapshot_id = ? AND id = ?
	`, snapshotID, node.ID).Scan(&count)
	if err != nil {
		return fmt.Errorf("检查节点是否存在失败: %w", err)
	}

	if count > 0 {
		// 更新现有节点
		_, err = d.db.ExecContext(ctx, `
			UPDATE monitor_topology_node 
			SET node_type = ?, properties = ? 
			WHERE snapshot_id = ? AND id = ?
		`, node.Type, node.Data, snapshotID, node.ID)
		if err != nil {
			return fmt.Errorf("更新节点失败: %w", err)
		}
	} else {
		// 插入新节点
		_, err = d.db.ExecContext(ctx, `
			INSERT INTO monitor_topology_node 
			(snapshot_id, id, device_id, node_type, properties) 
			VALUES (?, ?, ?, ?, ?)
		`, snapshotID, node.ID, node.ID, node.Type, node.Data)
		if err != nil {
			return fmt.Errorf("插入节点失败: %w", err)
		}

		// 更新快照节点计数
		_, err = d.db.ExecContext(ctx, `
			UPDATE monitor_topology_snapshot 
			SET node_count = node_count + 1, update_time = ? 
			WHERE id = ?
		`, time.Now(), snapshotID)
		if err != nil {
			return fmt.Errorf("更新快照节点计数失败: %w", err)
		}
	}

	return nil
}

// SaveEdgeToSnapshot 保存边到快照
func (d *DAO) SaveEdgeToSnapshot(ctx context.Context, snapshotID string, edge *TopologyEdge) error {
	// 检查边是否已存在
	var count int
	err := d.db.QueryRowContext(ctx, `
		SELECT COUNT(*) FROM monitor_topology_edge 
		WHERE snapshot_id = ? AND id = ?
	`, snapshotID, edge.ID).Scan(&count)
	if err != nil {
		return fmt.Errorf("检查边是否存在失败: %w", err)
	}

	if count > 0 {
		// 更新现有边
		_, err = d.db.ExecContext(ctx, `
			UPDATE monitor_topology_edge 
			SET source_node_id = ?, target_node_id = ?, source_port_id = ?, target_port_id = ?, edge_type = ?, properties = ? 
			WHERE snapshot_id = ? AND id = ?
		`, edge.Source, edge.Target, edge.SourcePort, edge.TargetPort, edge.Type, edge.Data, snapshotID, edge.ID)
		if err != nil {
			return fmt.Errorf("更新边失败: %w", err)
		}
	} else {
		// 插入新边
		_, err = d.db.ExecContext(ctx, `
			INSERT INTO monitor_topology_edge 
			(snapshot_id, id, source_node_id, target_node_id, source_port_id, target_port_id, edge_type, properties) 
			VALUES (?, ?, ?, ?, ?, ?, ?, ?)
		`, snapshotID, edge.ID, edge.Source, edge.Target, edge.SourcePort, edge.TargetPort, edge.Type, edge.Data)
		if err != nil {
			return fmt.Errorf("插入边失败: %w", err)
		}

		// 更新快照边计数
		_, err = d.db.ExecContext(ctx, `
			UPDATE monitor_topology_snapshot 
			SET edge_count = edge_count + 1, update_time = ? 
			WHERE id = ?
		`, time.Now(), snapshotID)
		if err != nil {
			return fmt.Errorf("更新快照边计数失败: %w", err)
		}
	}

	return nil
}

// RemoveEdgeFromSnapshot 从快照中删除边
func (d *DAO) RemoveEdgeFromSnapshot(ctx context.Context, snapshotID string, edgeID string) error {
	// 检查边是否存在
	var count int
	err := d.db.QueryRowContext(ctx, `
		SELECT COUNT(*) FROM monitor_topology_edge 
		WHERE snapshot_id = ? AND id = ?
	`, snapshotID, edgeID).Scan(&count)
	if err != nil {
		return fmt.Errorf("检查边是否存在失败: %w", err)
	}

	if count == 0 {
		// 边不存在，不需要删除
		return nil
	}

	// 删除边
	_, err = d.db.ExecContext(ctx, `
		DELETE FROM monitor_topology_edge 
		WHERE snapshot_id = ? AND id = ?
	`, snapshotID, edgeID)
	if err != nil {
		return fmt.Errorf("删除边失败: %w", err)
	}

	// 更新快照边计数
	_, err = d.db.ExecContext(ctx, `
		UPDATE monitor_topology_snapshot 
		SET edge_count = edge_count - 1, update_time = ? 
		WHERE id = ?
	`, time.Now(), snapshotID)
	if err != nil {
		return fmt.Errorf("更新快照边计数失败: %w", err)
	}

	return nil
}
