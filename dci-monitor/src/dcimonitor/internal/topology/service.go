package topology

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"time"

	"common/logger"
	"dcimonitor/internal/kafka"

	"github.com/IBM/sarama"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
)

// Kafka主题常量
const (
	// 输入主题
	TopicRawTopologyData = "dci.monitor.v1.defaultchannel.topology.raw_data"
	// 输出主题
	TopicProcessedTopology = "dci.monitor.v1.defaultchannel.topology.processed"
)

// 映射状态常量
const (
	MappingStatusMapped  = "MAPPED"
	MappingStatusPartial = "PARTIAL"
	MappingStatusFailed  = "FAILED"
)

// Service 提供拓扑相关的业务逻辑
type Service struct {
	dao               *DAO
	identifierService *IdentifierService
	db                *sql.DB
	logger            *zap.Logger
	consumer          *kafka.Consumer
	producer          *kafka.Producer
	config            *TopologyProcessorConfig

	nodes            map[string]*TopologyNode
	edges            map[string]*TopologyEdge
	mutex            sync.RWMutex
	currentSnapshot  string
	lastSnapshotTime time.Time
	snapshotInterval time.Duration

	wg              sync.WaitGroup
	ctx             context.Context
	cancel          context.CancelFunc
	healthStatus    bool
	statusMutex     sync.RWMutex
	lastHealthCheck time.Time

	localPortRegexp *regexp.Regexp
}

// NewService 创建一个新的Service实例
func NewService(dao *DAO, db *sql.DB, logger *zap.Logger) (*Service, error) {
	if dao == nil {
		return nil, errors.New("DAO不能为空")
	}

	if db == nil {
		return nil, errors.New("数据库连接不能为空")
	}

	// 创建上下文，用于控制所有组件
	ctx, cancel := context.WithCancel(context.Background())

	// 创建标识符服务
	identifierService := NewIdentifierService(db, logger)

	// 编译正则表达式，用于从OID索引中提取端口索引
	localPortRegexp, err := regexp.Compile(`\.(\d+)$`)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("编译端口索引正则表达式失败: %w", err)
	}

	return &Service{
		dao:               dao,
		identifierService: identifierService,
		db:                db,
		logger:            logger,
		nodes:             make(map[string]*TopologyNode),
		edges:             make(map[string]*TopologyEdge),
		snapshotInterval:  1 * time.Hour, // 默认每小时创建一次快照
		ctx:               ctx,
		cancel:            cancel,
		healthStatus:      false,
		lastHealthCheck:   time.Now(),
		localPortRegexp:   localPortRegexp,
	}, nil
}

// Init 初始化拓扑服务
func (s *Service) Init(config *TopologyProcessorConfig) error {
	logger.Info("初始化拓扑服务")
	s.config = config

	// 设置拓扑快照间隔
	if config.SnapshotInterval != "" {
		interval, err := time.ParseDuration(config.SnapshotInterval)
		if err != nil {
			return fmt.Errorf("解析快照间隔失败: %w", err)
		}
		s.snapshotInterval = interval
	}

	// 加载最新的拓扑快照
	snapshotID, err := s.dao.GetCurrentSnapshotID(s.ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// 如果没有现有快照，创建一个新的
			logger.Info("没有找到现有拓扑快照，创建一个新的")
			snapshotID, err = s.dao.CreateSnapshot(s.ctx, "初始拓扑", "初始拓扑快照")
			if err != nil {
				return fmt.Errorf("创建初始快照失败: %w", err)
			}
		} else {
			return fmt.Errorf("获取当前快照失败: %w", err)
		}
	}

	// 加载现有快照的节点和边
	s.currentSnapshot = snapshotID
	if err := s.loadSnapshot(snapshotID); err != nil {
		return fmt.Errorf("加载快照 %s 失败: %w", snapshotID, err)
	}

	// 创建Kafka消费者（如果配置）
	if s.config.InputKafkaConfig != nil {
		consumerConfig := &kafka.ConsumerConfig{
			Brokers:       s.config.InputKafkaConfig["brokers"].([]string),
			Topics:        []string{TopicRawTopologyData},
			ConsumerGroup: s.config.InputKafkaConfig["consumer_group"].(string),
		}
		consumer, err := kafka.NewConsumer(consumerConfig)
		if err != nil {
			return fmt.Errorf("创建Kafka消费者失败: %w", err)
		}
		s.consumer = consumer
	}

	// 创建Kafka生产者（如果配置）
	if s.config.OutputKafkaConfig != nil {
		producerConfig := &kafka.ProducerConfig{
			Brokers: s.config.OutputKafkaConfig["brokers"].([]string),
		}
		producer, err := kafka.NewAsyncProducer(producerConfig)
		if err != nil {
			return fmt.Errorf("创建Kafka生产者失败: %w", err)
		}
		s.producer = producer
	}

	// 设置健康状态为true
	s.setHealthStatus(true)

	logger.Info("拓扑服务初始化完成",
		zap.String("snapshotID", snapshotID),
		zap.Int("nodeCount", len(s.nodes)),
		zap.Int("edgeCount", len(s.edges)))
	return nil
}

// Start 启动拓扑服务
func (s *Service) Start() error {
	logger.Info("启动拓扑服务")

	// 启动Kafka生产者（如果已配置）
	if s.producer != nil {
		if err := s.producer.Start(); err != nil {
			return fmt.Errorf("启动Kafka生产者失败: %w", err)
		}
		logger.Info("Kafka生产者已启动")
	}

	// 启动Kafka消费者（如果已配置）
	if s.consumer != nil {
		// 定义消息处理函数
		handler := func(msg *sarama.ConsumerMessage) error {
			return s.handleKafkaMessage(msg)
		}

		if err := s.consumer.Start(s.ctx, handler); err != nil {
			return fmt.Errorf("启动Kafka消费者失败: %w", err)
		}
		logger.Info("Kafka消费者已启动",
			zap.String("topic", TopicRawTopologyData),
			zap.String("group", s.config.InputKafkaConfig["consumer_group"].(string)))
	}

	// 启动健康检查
	if s.config.HealthCheckInterval != "" {
		interval, err := time.ParseDuration(s.config.HealthCheckInterval)
		if err == nil && interval > 0 {
			s.startHealthCheck(interval)
		}
	}

	logger.Info("拓扑服务启动成功")
	return nil
}

// Stop 停止拓扑服务
func (s *Service) Stop() error {
	logger.Info("停止拓扑服务")

	// 取消上下文，通知所有组件停止
	s.cancel()

	// 等待所有后台任务完成
	s.wg.Wait()

	// 停止Kafka消费者
	if s.consumer != nil {
		if err := s.consumer.Stop(); err != nil {
			logger.Error("停止Kafka消费者失败", zap.Error(err))
		}
		logger.Info("Kafka消费者已停止")
	}

	// 停止Kafka生产者
	if s.producer != nil {
		if err := s.producer.Stop(); err != nil {
			logger.Error("停止Kafka生产者失败", zap.Error(err))
		}
		logger.Info("Kafka生产者已停止")
	}

	logger.Info("拓扑服务已停止")
	return nil
}

// loadSnapshot 加载指定的拓扑快照到内存
func (s *Service) loadSnapshot(snapshotID string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 清空当前内存中的拓扑数据
	s.nodes = make(map[string]*TopologyNode)
	s.edges = make(map[string]*TopologyEdge)

	// 加载节点
	nodes, err := s.dao.GetSnapshotNodes(s.ctx, snapshotID)
	if err != nil {
		return fmt.Errorf("获取快照节点失败: %w", err)
	}

	for i := range nodes {
		s.nodes[nodes[i].ID] = &nodes[i]
	}

	// 加载边
	edges, err := s.dao.GetSnapshotEdges(s.ctx, snapshotID)
	if err != nil {
		return fmt.Errorf("获取快照边失败: %w", err)
	}

	for i := range edges {
		s.edges[edges[i].ID] = &edges[i]
	}

	logger.Info("快照已加载",
		zap.String("snapshotID", snapshotID),
		zap.Int("nodeCount", len(s.nodes)),
		zap.Int("edgeCount", len(s.edges)))

	return nil
}

// AddNode 添加或更新节点
func (s *Service) AddNode(ctx context.Context, node *TopologyNode) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 检查节点是否已存在
	existingNode, exists := s.nodes[node.ID]
	if exists {
		// 更新现有节点
		existingNode.Type = node.Type
		existingNode.Name = node.Name
		existingNode.Data = node.Data
		existingNode.Position = node.Position
	} else {
		// 添加新节点
		s.nodes[node.ID] = node
	}

	// 保存到数据库
	if err := s.dao.SaveNodeToSnapshot(ctx, s.currentSnapshot, node); err != nil {
		return fmt.Errorf("保存节点到快照失败: %w", err)
	}

	// 检查是否需要创建新的快照
	if time.Since(s.lastSnapshotTime) > s.snapshotInterval {
		if err := s.createNewSnapshot(ctx); err != nil {
			logger.Error("创建新快照失败", zap.Error(err))
		}
	}

	return nil
}

// AddEdge 添加或更新边
func (s *Service) AddEdge(ctx context.Context, edge *TopologyEdge) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 检查源节点和目标节点是否存在
	if _, exists := s.nodes[edge.Source]; !exists {
		return fmt.Errorf("源节点 %s 不存在", edge.Source)
	}
	if _, exists := s.nodes[edge.Target]; !exists {
		return fmt.Errorf("目标节点 %s 不存在", edge.Target)
	}

	// 如果边ID为空，生成一个新的ID
	if edge.ID == "" {
		edge.ID = fmt.Sprintf("%s-%s-%s", edge.Source, edge.Target, uuid.New().String())
	}

	// 检查边是否已存在
	existingEdge, exists := s.edges[edge.ID]
	if exists {
		// 更新现有边
		existingEdge.Source = edge.Source
		existingEdge.Target = edge.Target
		existingEdge.SourcePort = edge.SourcePort
		existingEdge.TargetPort = edge.TargetPort
		existingEdge.Type = edge.Type
		existingEdge.Data = edge.Data
	} else {
		// 添加新边
		s.edges[edge.ID] = edge
	}

	// 保存到数据库
	if err := s.dao.SaveEdgeToSnapshot(ctx, s.currentSnapshot, edge); err != nil {
		return fmt.Errorf("保存边到快照失败: %w", err)
	}

	return nil
}

// RemoveEdge 删除边
func (s *Service) RemoveEdge(ctx context.Context, edgeID string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 检查边是否存在
	if _, exists := s.edges[edgeID]; !exists {
		return nil // 边不存在，不需要删除
	}

	// 从内存中删除
	delete(s.edges, edgeID)

	// 从数据库中删除
	if err := s.dao.RemoveEdgeFromSnapshot(ctx, s.currentSnapshot, edgeID); err != nil {
		return fmt.Errorf("从快照中删除边失败: %w", err)
	}

	return nil
}

// GetTopology 获取当前拓扑
func (s *Service) GetTopology() *TopologyGraph {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	graph := &TopologyGraph{
		ID:        s.currentSnapshot,
		Name:      "当前拓扑",
		CreatedAt: s.lastSnapshotTime,
		Nodes:     make([]*TopologyNode, 0, len(s.nodes)),
		Edges:     make([]*TopologyEdge, 0, len(s.edges)),
	}

	// 添加所有节点
	for _, node := range s.nodes {
		graph.Nodes = append(graph.Nodes, node)
	}

	// 添加所有边
	for _, edge := range s.edges {
		graph.Edges = append(graph.Edges, edge)
	}

	return graph
}

// GetTopologyStats 获取拓扑统计信息
func (s *Service) GetTopologyStats() *TopologyStats {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	return &TopologyStats{
		NodeCount: len(s.nodes),
		EdgeCount: len(s.edges),
	}
}

// createNewSnapshot 创建新的拓扑快照
func (s *Service) createNewSnapshot(ctx context.Context) error {
	name := fmt.Sprintf("拓扑快照 %s", time.Now().Format("2006-01-02 15:04:05"))
	description := fmt.Sprintf("自动创建的拓扑快照，包含 %d 个节点和 %d 个边", len(s.nodes), len(s.edges))

	snapshotID, err := s.dao.CreateSnapshot(ctx, name, description)
	if err != nil {
		return fmt.Errorf("创建快照失败: %w", err)
	}

	// 更新当前快照ID和时间
	s.currentSnapshot = snapshotID
	s.lastSnapshotTime = time.Now()

	// 将所有节点和边保存到新快照
	for _, node := range s.nodes {
		if err := s.dao.SaveNodeToSnapshot(ctx, snapshotID, node); err != nil {
			logger.Error("保存节点到新快照失败",
				zap.Error(err),
				zap.String("nodeID", node.ID))
		}
	}

	for _, edge := range s.edges {
		if err := s.dao.SaveEdgeToSnapshot(ctx, snapshotID, edge); err != nil {
			logger.Error("保存边到新快照失败",
				zap.Error(err),
				zap.String("edgeID", edge.ID))
		}
	}

	logger.Info("创建了新的拓扑快照",
		zap.String("snapshotID", snapshotID),
		zap.Int("nodeCount", len(s.nodes)),
		zap.Int("edgeCount", len(s.edges)))

	return nil
}

// handleKafkaMessage 处理Kafka消息
func (s *Service) handleKafkaMessage(msg *sarama.ConsumerMessage) error {
	// 记录处理开始时间，用于计算处理延迟
	startTime := time.Now()

	logger.Debug("处理拓扑消息",
		zap.String("topic", msg.Topic),
		zap.Int32("partition", msg.Partition),
		zap.Int64("offset", msg.Offset))

	switch msg.Topic {
	case TopicRawTopologyData:
		// 解析消息
		var rawData LLDPRawData
		if err := jsoniter.Unmarshal(msg.Value, &rawData); err != nil {
			logger.Error("解析LLDP消息失败",
				zap.Error(err),
				zap.ByteString("value", msg.Value))
			return err
		}

		// 处理LLDP数据
		if err := s.processLLDPData(s.ctx, &rawData); err != nil {
			logger.Error("处理LLDP数据失败",
				zap.Error(err),
				zap.Any("rawData", rawData))
			return err
		}
	default:
		logger.Warn("收到未知主题的消息",
			zap.String("topic", msg.Topic),
			zap.ByteString("value", msg.Value))
	}

	// 记录处理延迟
	processingTime := time.Since(startTime)
	logger.Debug("拓扑消息处理完成",
		zap.Duration("processingTime", processingTime))

	return nil
}

// processLLDPData 处理LLDP数据
func (s *Service) processLLDPData(ctx context.Context, rawData *LLDPRawData) error {
	// 解析LLDP数据
	neighbor, err := s.parseLLDPData(rawData)
	if err != nil {
		return fmt.Errorf("解析LLDP数据失败: %w", err)
	}

	// 映射LLDP邻居关系到系统ID
	mappedResult, err := s.identifierService.MapLLDPNeighbor(neighbor.RemoteChassisID, neighbor.RemotePortID)
	if err != nil {
		return fmt.Errorf("映射LLDP邻居失败: %w", err)
	}

	// 将映射结果转换为内部MappedLLDPNeighbor
	internalMappedNeighbor := MappedLLDPNeighbor{
		LLDPNeighbor:         neighbor,
		MappedRemoteDeviceID: mappedResult.DeviceID,
		MappedRemotePortID:   mappedResult.InterfaceID,
		MappingStatus:        mappedResult.MappingStatus,
		LastSeen:             time.Now(),
	}

	// 根据映射状态处理
	switch internalMappedNeighbor.MappingStatus {
	case MappingStatusMapped:
		// 完全映射成功，更新拓扑
		if err := s.updateTopology(ctx, &internalMappedNeighbor); err != nil {
			return fmt.Errorf("更新拓扑失败: %w", err)
		}
	case MappingStatusPartial:
		// 部分映射成功，记录警告
		logger.Warn("LLDP邻居部分映射",
			zap.String("errorMessage", internalMappedNeighbor.ErrorMessage),
			zap.Any("mappedNeighbor", internalMappedNeighbor))
	case MappingStatusFailed:
		// 映射失败，记录错误
		logger.Error("LLDP邻居映射失败",
			zap.String("errorMessage", internalMappedNeighbor.ErrorMessage),
			zap.Any("neighbor", neighbor))
	}

	return nil
}

// parseLLDPData 解析LLDP原始数据
func (s *Service) parseLLDPData(rawData *LLDPRawData) (LLDPNeighbor, error) {
	neighbor := LLDPNeighbor{
		RawData: nil, // 暂不保存原始数据，避免占用过多空间
	}

	// 提取本地设备信息
	if agentHost, ok := rawData.Tags["agent_host"]; ok {
		neighbor.LocalDeviceID = agentHost
		neighbor.LocalDeviceType = "ip"
	} else {
		return neighbor, errors.New("LLDP数据中缺少agent_host")
	}

	// 提取本地端口索引
	if oidIndex, ok := rawData.Tags["index"]; ok {
		portIndex, err := s.extractLocalPortIndex(oidIndex)
		if err != nil {
			return neighbor, fmt.Errorf("提取端口索引失败: %w", err)
		}
		neighbor.LocalPortIndex = portIndex
	} else {
		return neighbor, errors.New("LLDP数据中缺少index")
	}

	// 提取远端设备机箱ID
	if remoteChassisID, ok := rawData.Fields["lldpRemChassisId"]; ok {
		if chassisID, ok := remoteChassisID.(string); ok {
			neighbor.RemoteChassisID = s.normalizeMacAddress(chassisID)
		} else {
			return neighbor, errors.New("远端机箱ID格式无效")
		}
	} else {
		return neighbor, errors.New("LLDP数据中缺少远端机箱ID")
	}

	// 提取远端设备端口ID
	if remotePortID, ok := rawData.Fields["lldpRemPortId"]; ok {
		if portID, ok := remotePortID.(string); ok {
			neighbor.RemotePortID = portID
		} else {
			return neighbor, errors.New("远端端口ID格式无效")
		}
	} else {
		return neighbor, errors.New("LLDP数据中缺少远端端口ID")
	}

	// 提取远端设备系统名称（可选）
	if remoteSysName, ok := rawData.Fields["lldpRemSysName"]; ok {
		if sysName, ok := remoteSysName.(string); ok {
			neighbor.RemoteSysName = sysName
		}
	}

	return neighbor, nil
}

// extractLocalPortIndex 从OID索引中提取端口索引
func (s *Service) extractLocalPortIndex(oidIndex string) (string, error) {
	// 使用正则表达式提取最后的数字部分作为端口索引
	matches := s.localPortRegexp.FindStringSubmatch(oidIndex)
	if len(matches) < 2 {
		return "", fmt.Errorf("无法从OID索引提取端口索引: %s", oidIndex)
	}
	return matches[1], nil
}

// normalizeMacAddress 标准化MAC地址格式
func (s *Service) normalizeMacAddress(mac string) string {
	// 移除所有非十六进制字符
	mac = strings.ToLower(mac)
	var result strings.Builder
	for _, c := range mac {
		if (c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') {
			result.WriteRune(c)
		}
	}
	return result.String()
}

// updateTopology 根据LLDP邻居信息更新拓扑
func (s *Service) updateTopology(ctx context.Context, mappedNeighbor *MappedLLDPNeighbor) error {
	// 1. 确保本地设备节点存在
	localNode := &TopologyNode{
		ID:   mappedNeighbor.MappedLocalDeviceID,
		Type: "device",
		Name: mappedNeighbor.LocalDeviceID, // 临时使用设备标识符作为名称
	}
	if err := s.AddNode(ctx, localNode); err != nil {
		return fmt.Errorf("添加本地节点失败: %w", err)
	}

	// 2. 确保远端设备节点存在
	remoteNode := &TopologyNode{
		ID:   mappedNeighbor.MappedRemoteDeviceID,
		Type: "device",
		Name: mappedNeighbor.RemoteSysName, // 使用系统名称作为名称
	}
	if err := s.AddNode(ctx, remoteNode); err != nil {
		return fmt.Errorf("添加远端节点失败: %w", err)
	}

	// 3. 创建或更新连接边
	edge := &TopologyEdge{
		ID:         fmt.Sprintf("%s-%s", mappedNeighbor.MappedLocalDeviceID, mappedNeighbor.MappedRemoteDeviceID),
		Source:     mappedNeighbor.MappedLocalDeviceID,
		Target:     mappedNeighbor.MappedRemoteDeviceID,
		SourcePort: mappedNeighbor.MappedLocalPortID,
		TargetPort: mappedNeighbor.MappedRemotePortID,
		Type:       "physical",
	}
	if err := s.AddEdge(ctx, edge); err != nil {
		return fmt.Errorf("添加边失败: %w", err)
	}

	// 4. 发布拓扑变更消息
	if s.producer != nil {
		op := &TopologyOperation{
			OperationType: "UPSERT_EDGE",
			Timestamp:     time.Now(),
		}

		// 序列化边数据
		edgeData, err := jsoniter.Marshal(edge)
		if err != nil {
			logger.Error("序列化边数据失败", zap.Error(err))
		} else {
			op.Data = edgeData

			// 序列化操作消息
			data, err := jsoniter.Marshal(op)
			if err != nil {
				logger.Error("序列化拓扑操作失败", zap.Error(err))
			} else {
				// 发送到Kafka
				err = s.producer.SendMessage(ctx, TopicProcessedTopology, nil, data, nil)
				if err != nil {
					logger.Error("发送拓扑变更消息失败", zap.Error(err))
				}
			}
		}
	}

	return nil
}

// startHealthCheck 启动健康检查
func (s *Service) startHealthCheck(interval time.Duration) {
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				s.runHealthCheck()
			case <-s.ctx.Done():
				return
			}
		}
	}()
}

// runHealthCheck 执行健康检查
func (s *Service) runHealthCheck() {
	// 检查数据库连接
	if err := s.db.PingContext(s.ctx); err != nil {
		logger.Error("数据库健康检查失败", zap.Error(err))
		s.setHealthStatus(false)
		return
	}

	// 检查Kafka消费者（如果已配置）
	if s.consumer != nil {
		// 检查消费者连接状态，由于kafka.Consumer没有IsHealthy方法，
		// 这里简单地检查consumer是否为nil
		if s.consumer == nil {
			logger.Error("Kafka消费者健康检查失败")
			s.setHealthStatus(false)
			return
		}
	}

	// 检查Kafka生产者（如果已配置）
	if s.producer != nil {
		// 检查生产者连接状态，由于kafka.Producer没有IsHealthy方法，
		// 这里简单地检查producer是否为nil
		if s.producer == nil {
			logger.Error("Kafka生产者健康检查失败")
			s.setHealthStatus(false)
			return
		}
	}

	// 所有检查通过
	s.setHealthStatus(true)
	s.lastHealthCheck = time.Now()
}

// setHealthStatus 设置健康状态
func (s *Service) setHealthStatus(status bool) {
	s.statusMutex.Lock()
	defer s.statusMutex.Unlock()
	s.healthStatus = status
}

// HealthCheck 获取健康状态
func (s *Service) HealthCheck() bool {
	s.statusMutex.RLock()
	defer s.statusMutex.RUnlock()
	return s.healthStatus
}
