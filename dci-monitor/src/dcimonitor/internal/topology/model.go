package topology

import (
	"database/sql"
	"encoding/json"
	"time"
)

// TopologyNode 表示拓扑图中的节点（设备）
type TopologyNode struct {
	ID       string          `json:"id"`                 // 节点唯一标识符
	Type     string          `json:"type"`               // 节点类型：switch, router, server, etc.
	Name     string          `json:"name"`               // 节点名称
	Data     json.RawMessage `json:"data,omitempty"`     // 节点附加数据，如位置信息
	Position json.RawMessage `json:"position,omitempty"` // 节点位置信息
}

// NodePosition 表示节点在拓扑图中的位置
type NodePosition struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
}

// TopologyEdge 表示拓扑图中的边（连接）
type TopologyEdge struct {
	ID         string          `json:"id"`                   // 边的唯一标识符
	Source     string          `json:"source"`               // 源节点ID
	Target     string          `json:"target"`               // 目标节点ID
	SourcePort string          `json:"sourcePort,omitempty"` // 源端口ID
	TargetPort string          `json:"targetPort,omitempty"` // 目标端口ID
	Type       string          `json:"type"`                 // 连接类型：physical, logical, etc.
	Data       json.RawMessage `json:"data,omitempty"`       // 边附加数据，如带宽、延迟等
}

// TopologyGraph 表示完整的拓扑图
type TopologyGraph struct {
	ID        string          `json:"id"`        // 拓扑图ID
	Name      string          `json:"name"`      // 拓扑图名称
	CreatedAt time.Time       `json:"createdAt"` // 创建时间
	Nodes     []*TopologyNode `json:"nodes"`     // 节点列表
	Edges     []*TopologyEdge `json:"edges"`     // 边列表
}

// TopologyChange 表示拓扑变更记录
type TopologyChange struct {
	Type      string      `json:"type"`      // 变更类型：ADD_NODE、REMOVE_NODE、ADD_EDGE、REMOVE_EDGE、UPDATE_NODE、UPDATE_EDGE
	Timestamp time.Time   `json:"timestamp"` // 变更时间
	NodeID    string      `json:"nodeID"`    // 受影响的节点ID（如适用）
	EdgeID    string      `json:"edgeID"`    // 受影响的边ID（如适用）
	OldValue  interface{} `json:"oldValue"`  // 变更前的值
	NewValue  interface{} `json:"newValue"`  // 变更后的值
}

// TopologyOperation 表示拓扑操作消息，用于发送到Kafka
type TopologyOperation struct {
	OperationType string          `json:"operationType"` // 操作类型: UPSERT_NODE, UPSERT_EDGE, REMOVE_NODE, REMOVE_EDGE
	Timestamp     time.Time       `json:"timestamp"`     // 操作时间戳
	Data          json.RawMessage `json:"data"`          // 操作数据
}

// TopologySnapshot 表示拓扑快照记录
type TopologySnapshot struct {
	ID          string    `json:"id"`          // 快照ID
	Name        string    `json:"name"`        // 快照名称
	Description string    `json:"description"` // 快照描述
	NodeCount   int       `json:"nodeCount"`   // 节点数量
	EdgeCount   int       `json:"edgeCount"`   // 边数量
	IsCurrent   bool      `json:"isCurrent"`   // 是否为当前活动快照
	CreatedBy   string    `json:"createdBy"`   // 创建者
	CreateTime  time.Time `json:"createTime"`  // 创建时间
	UpdateTime  time.Time `json:"updateTime"`  // 更新时间
}

// LLDPNeighbor 表示解析后的LLDP邻居信息
type LLDPNeighbor struct {
	// 本地设备信息
	LocalDeviceID   string `json:"localDeviceID"`   // 本地设备ID
	LocalDeviceType string `json:"localDeviceType"` // 本地设备类型
	LocalPortIndex  string `json:"localPortIndex"`  // 本地端口索引

	// 远端设备信息
	RemoteChassisID string `json:"remoteChassisID"` // 远端设备机箱ID
	RemotePortID    string `json:"remotePortID"`    // 远端设备端口ID
	RemoteSysName   string `json:"remoteSysName"`   // 远端设备系统名称

	// 原始数据
	RawData json.RawMessage `json:"rawData"` // 原始LLDP数据
}

// MappedLLDPNeighbor 表示映射后的LLDP邻居信息，包含系统标准ID
type MappedLLDPNeighbor struct {
	LLDPNeighbor                   // 嵌入原始LLDP邻居信息
	MappedLocalDeviceID  string    `json:"mappedLocalDeviceID"`  // 映射后的本地设备ID
	MappedLocalPortID    string    `json:"mappedLocalPortID"`    // 映射后的本地端口ID
	MappedRemoteDeviceID string    `json:"mappedRemoteDeviceID"` // 映射后的远端设备ID
	MappedRemotePortID   string    `json:"mappedRemotePortID"`   // 映射后的远端端口ID
	MappingStatus        string    `json:"mappingStatus"`        // 映射状态：MAPPED、PARTIAL、FAILED
	ErrorMessage         string    `json:"errorMessage"`         // 映射失败时的错误信息
	LastSeen             time.Time `json:"lastSeen"`             // 最后一次发现时间
}

// LLDPRawData 定义了从Telegraf/SNMP采集的原始LLDP数据
type LLDPRawData struct {
	Name      string                 `json:"name"`
	Tags      map[string]string      `json:"tags"`
	Fields    map[string]interface{} `json:"fields"`
	Timestamp time.Time              `json:"timestamp"`
}

// PortInfo 表示端口基本信息
type PortInfo struct {
	DeviceID    string    `json:"deviceID"`    // 所属设备ID
	PortID      string    `json:"portID"`      // 端口ID
	IfIndex     int       `json:"ifIndex"`     // 接口索引号
	PortName    string    `json:"portName"`    // 端口名称
	Description string    `json:"description"` // 端口描述
	AdminStatus int       `json:"adminStatus"` // 管理状态
	OperStatus  int       `json:"operStatus"`  // 运行状态
	MacAddress  string    `json:"macAddress"`  // MAC地址
	LastUpdated time.Time `json:"lastUpdated"` // 最后更新时间
}

// TopologyProcessorConfig 拓扑处理器配置
type TopologyProcessorConfig struct {
	SnapshotInterval    string                 `json:"snapshotInterval"`    // 快照间隔
	HealthCheckInterval string                 `json:"healthCheckInterval"` // 健康检查间隔
	InputKafkaConfig    map[string]interface{} `json:"inputKafkaConfig"`    // Kafka输入配置
	OutputKafkaConfig   map[string]interface{} `json:"outputKafkaConfig"`   // Kafka输出配置
}

// TopologyStats 表示拓扑统计信息
type TopologyStats struct {
	NodeCount     int `json:"nodeCount"`     // 节点数量
	EdgeCount     int `json:"edgeCount"`     // 边数量
	SwitchCount   int `json:"switchCount"`   // 交换机数量
	RouterCount   int `json:"routerCount"`   // 路由器数量
	ServerCount   int `json:"serverCount"`   // 服务器数量
	PhysicalLinks int `json:"physicalLinks"` // 物理链接数量
	LogicalLinks  int `json:"logicalLinks"`  // 逻辑链接数量
}

// DBTopologySnapshot 数据库拓扑快照
type DBTopologySnapshot struct {
	ID          string       `db:"id"`
	Name        string       `db:"name"`
	Description string       `db:"description"`
	CreatedAt   time.Time    `db:"created_at"`
	IsCurrent   bool         `db:"is_current"`
	NodesCount  int          `db:"nodes_count"`
	EdgesCount  int          `db:"edges_count"`
	UpdatedAt   time.Time    `db:"updated_at"`
	DeletedAt   sql.NullTime `db:"deleted_at"`
}

// DBTopologyNode 数据库拓扑节点
type DBTopologyNode struct {
	ID         string       `db:"id"`
	SnapshotID string       `db:"snapshot_id"`
	Type       string       `db:"type"`
	Name       string       `db:"name"`
	Data       []byte       `db:"data"`
	Position   []byte       `db:"position"`
	CreatedAt  time.Time    `db:"created_at"`
	UpdatedAt  time.Time    `db:"updated_at"`
	DeletedAt  sql.NullTime `db:"deleted_at"`
}

// DBTopologyEdge 数据库拓扑边
type DBTopologyEdge struct {
	ID         string       `db:"id"`
	SnapshotID string       `db:"snapshot_id"`
	Source     string       `db:"source"`
	Target     string       `db:"target"`
	SourcePort string       `db:"source_port"`
	TargetPort string       `db:"target_port"`
	Type       string       `db:"type"`
	Data       []byte       `db:"data"`
	CreatedAt  time.Time    `db:"created_at"`
	UpdatedAt  time.Time    `db:"updated_at"`
	DeletedAt  sql.NullTime `db:"deleted_at"`
}
