package topology

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"go.uber.org/zap"
)

// TopologyProcessor 拓扑处理器，用于处理拓扑数据
type TopologyProcessor struct {
	service *Service
	logger  *zap.Logger
	config  *TopologyProcessorConfig
	ctx     context.Context
	cancel  context.CancelFunc
}

// NewTopologyProcessor 创建一个新的拓扑处理器
func NewTopologyProcessor(db *sql.DB, config *TopologyProcessorConfig) (*TopologyProcessor, error) {
	if db == nil {
		return nil, fmt.Errorf("数据库连接不能为空")
	}

	if config == nil {
		return nil, fmt.Errorf("配置不能为空")
	}

	// 创建上下文，用于控制所有组件
	ctx, cancel := context.WithCancel(context.Background())

	// 创建DAO
	dao := NewDAO(db, zap.L())

	// 创建Service
	service, err := NewService(dao, db, zap.L())
	if err != nil {
		cancel()
		return nil, fmt.Errorf("创建拓扑服务失败: %w", err)
	}

	return &TopologyProcessor{
		service: service,
		logger:  zap.L(),
		config:  config,
		ctx:     ctx,
		cancel:  cancel,
	}, nil
}

// Init 初始化拓扑处理器
func (p *TopologyProcessor) Init() error {
	// 初始化拓扑服务
	err := p.service.Init(p.config)
	if err != nil {
		return fmt.Errorf("初始化拓扑服务失败: %w", err)
	}

	return nil
}

// Start 启动拓扑处理器
func (p *TopologyProcessor) Start() error {
	// 启动拓扑服务
	err := p.service.Start()
	if err != nil {
		return fmt.Errorf("启动拓扑服务失败: %w", err)
	}

	return nil
}

// Stop 停止拓扑处理器
func (p *TopologyProcessor) Stop() error {
	// 停止拓扑服务
	if p.service != nil {
		if err := p.service.Stop(); err != nil {
			p.logger.Error("停止拓扑服务失败", zap.Error(err))
		}
	}

	// 取消上下文
	p.cancel()

	return nil
}

// NewTopologyBuilder 创建一个新的拓扑构建器
func NewTopologyBuilder(db *sql.DB) *TopologyBuilder {
	return &TopologyBuilder{
		db:     db,
		logger: zap.L(),
	}
}

// TopologyBuilder 拓扑构建器，用于构建拓扑图
type TopologyBuilder struct {
	db     *sql.DB
	logger *zap.Logger
}

// Init 初始化拓扑构建器
func (b *TopologyBuilder) Init(ctx context.Context) error {
	// 检查数据库连接
	if err := b.db.PingContext(ctx); err != nil {
		return fmt.Errorf("数据库连接测试失败: %w", err)
	}
	return nil
}

// CreateNewSnapshot 创建新的拓扑快照
func (b *TopologyBuilder) CreateNewSnapshot(ctx context.Context, name, description string) error {
	// 生成快照ID
	snapshotID := fmt.Sprintf("snapshot_%s", time.Now().Format("20060102150405"))

	// 开始事务
	tx, err := b.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// 重置当前快照标志
	_, err = tx.ExecContext(ctx, "UPDATE monitor_topology_snapshot SET is_current = 0")
	if err != nil {
		return fmt.Errorf("重置当前快照标志失败: %w", err)
	}

	// 插入新快照
	_, err = tx.ExecContext(ctx,
		"INSERT INTO monitor_topology_snapshot (id, name, description, is_current, create_time) VALUES (?, ?, ?, 1, NOW())",
		snapshotID, name, description)
	if err != nil {
		return fmt.Errorf("插入新快照失败: %w", err)
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	b.logger.Info("成功创建拓扑快照",
		zap.String("id", snapshotID),
		zap.String("name", name))

	return nil
}

// CleanupInactiveIdentifiers 清理不活跃的标识符
func (s *IdentifierService) CleanupInactiveIdentifiers(ctx context.Context, days int) (int, error) {
	// 删除不活跃的标识符映射
	result, err := s.db.ExecContext(ctx, `
		DELETE FROM device_mapping 
		WHERE last_seen < DATE_SUB(NOW(), INTERVAL ? DAY)
	`, days)
	if err != nil {
		return 0, fmt.Errorf("删除不活跃设备映射失败: %w", err)
	}

	deviceCount, _ := result.RowsAffected()

	// 删除不活跃的接口映射
	result, err = s.db.ExecContext(ctx, `
		DELETE FROM interface_mapping 
		WHERE last_seen < DATE_SUB(NOW(), INTERVAL ? DAY)
	`, days)
	if err != nil {
		return int(deviceCount), fmt.Errorf("删除不活跃接口映射失败: %w", err)
	}

	interfaceCount, _ := result.RowsAffected()

	return int(deviceCount + interfaceCount), nil
}

// CleanupOldLLDPCache 清理旧的LLDP缓存
func (s *IdentifierService) CleanupOldLLDPCache(ctx context.Context, days int) (int, error) {
	// 删除旧的LLDP缓存
	result, err := s.db.ExecContext(ctx, `
		DELETE FROM lldp_neighbor_cache 
		WHERE last_seen < DATE_SUB(NOW(), INTERVAL ? DAY)
	`, days)
	if err != nil {
		return 0, fmt.Errorf("删除旧的LLDP缓存失败: %w", err)
	}

	count, _ := result.RowsAffected()
	return int(count), nil
}
