# 拓扑模块重构

本目录实现了拓扑模块的重构，按照DCI项目API代码规范指南进行了代码组织调整，将原有代码按照功能模块划分为：

- `model.go`：定义拓扑相关的数据模型
- `service.go`：提供业务逻辑层
- `dao.go`：负责数据访问
- `handler.go`：处理HTTP请求和响应

## 重构内容

1. 从原 `models` 包中提取并迁移了拓扑相关的数据结构到 `model.go`
   - 提取了TopologyNode、TopologyEdge、TopologyGraph等模型
   - 增加了TopologySnapshot、TopologyStats等新模型

2. 从原 `monitors/topology` 包中迁移了拓扑处理逻辑到 `service.go`
   - 整合了TopologyBuilder和LLDPProcessor的功能
   - 提供了拓扑管理的核心业务逻辑

3. 创建了拓扑数据访问层 `dao.go`
   - 提供了对拓扑快照、节点、边的数据库操作
   - 实现了数据库事务处理

4. 创建了拓扑API处理函数 `handler.go`
   - 实现了获取拓扑图、统计信息和健康状态的API
   - 提供了路由注册功能

## 接口说明

拓扑模块提供以下API接口：

1. `GET /api/v1/topology` - 获取当前拓扑图
2. `GET /api/v1/topology/stats` - 获取拓扑统计信息
3. `GET /api/v1/topology/health` - 获取拓扑服务健康状态

## 数据库表结构

拓扑模块使用以下数据库表：

1. `monitor_topology_snapshot` - 存储拓扑快照信息
2. `monitor_topology_node` - 存储拓扑节点信息
3. `monitor_topology_edge` - 存储拓扑边信息

## 消息队列

拓扑模块使用以下Kafka主题：

1. `dci.monitor.v1.defaultchannel.topology.raw_data` - 接收原始拓扑数据
2. `dci.monitor.v1.defaultchannel.topology.processed` - 发送处理后的拓扑变更

## 使用示例

```go
// 创建DAO
db := ... // 获取数据库连接
logger := ... // 获取日志记录器
dao := topology.NewDAO(db, logger)

// 创建Service
service, err := topology.NewService(dao, db, logger)
if err != nil {
    // 处理错误
}

// 初始化Service
config := &topology.TopologyProcessorConfig{
    SnapshotInterval: "1h",
    HealthCheckInterval: "30s",
}
if err := service.Init(config); err != nil {
    // 处理错误
}

// 启动Service
if err := service.Start(); err != nil {
    // 处理错误
}

// 创建Handler
handler := topology.NewHandler(service, logger)

// 注册路由
router := gin.Default()
handler.RegisterRoutes(router)
```

## 下一步工作

1. 完成与cmd/server.go的集成
2. 添加单元测试和集成测试
3. 实现更多的拓扑分析功能
4. 优化拓扑数据的存储和查询性能 