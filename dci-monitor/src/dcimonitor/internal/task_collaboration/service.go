package task_collaboration

import (
	"context"
	"dcimonitor/internal/alert"
	"dcimonitor/internal/utils/timeutil"
	"fmt"
	"time"

	"go.uber.org/zap"
)

// MetricsService 定义指标监测服务接口，避免循环依赖
type MetricsService interface {
	StartMetricsMonitoring(ctx context.Context, taskID, taskSessionID string, deviceIDs []string, metricTypes []string) error
	StopMetricsMonitoring(ctx context.Context, taskID string) error
}

// Service 提供任务协同监控的业务逻辑
type Service struct {
	dao            *DAO
	taskDAO        *alert.TaskDAO
	alertDAO       *alert.DAO
	metricsService MetricsService // 可选的指标监测服务
	logger         *zap.Logger
}

// NewService 创建一个新的Service实例
func NewService(dao *DAO, taskDAO *alert.TaskDAO, alertDAO *alert.DAO, logger *zap.Logger) *Service {
	return &Service{
		dao:            dao,
		taskDAO:        taskDAO,
		alertDAO:       alertDAO,
		metricsService: nil, // 默认为nil，表示没有集成指标监测
		logger:         logger,
	}
}

// SetMetricsService 设置指标监测服务（可选集成）
func (s *Service) SetMetricsService(metricsService MetricsService) {
	s.metricsService = metricsService
	s.logger.Info("指标监测服务已集成")
}

// ProcessTaskSignal 处理任务信号
func (s *Service) ProcessTaskSignal(signal *TaskSignal) (*TaskSignalResponse, error) {
	requestID := fmt.Sprintf("signal-%s-%d", signal.TaskID, time.Now().Unix())
	s.logger.Info("开始处理任务信号",
		zap.String("request_id", requestID),
		zap.String("task_id", signal.TaskID),
		zap.String("signal_type", signal.SignalType))

	switch signal.SignalType {
	case "start":
		return s.handleTaskStart(signal, requestID)
	case "end":
		return s.handleTaskEnd(signal, requestID)
	case "abort":
		return s.handleTaskAbort(signal, requestID)
	default:
		return &TaskSignalResponse{
			Success: false,
			Message: fmt.Sprintf("不支持的信号类型: %s", signal.SignalType),
			TaskID:  signal.TaskID,
		}, nil
	}
}

// handleTaskStart 处理任务开始信号
func (s *Service) handleTaskStart(signal *TaskSignal, requestID string) (*TaskSignalResponse, error) {
	// 创建任务监控会话
	session, err := s.taskDAO.StartTaskMonitoring(signal.TaskID, signal.TargetDevices)
	if err != nil {
		s.logger.Error("创建任务监控会话失败",
			zap.String("request_id", requestID),
			zap.String("task_id", signal.TaskID),
			zap.Error(err))
		return &TaskSignalResponse{
			Success: false,
			Message: "创建任务监控会话失败",
			TaskID:  signal.TaskID,
		}, err
	}

	// 启动数据收集
	err = s.startDataCollection(session.SessionID, signal.TargetDevices, requestID)
	if err != nil {
		s.logger.Error("启动数据收集失败",
			zap.String("request_id", requestID),
			zap.String("session_id", session.SessionID),
			zap.Error(err))
		// 数据收集失败不影响会话创建成功的响应
	}

	// 可选：启动指标监测（如果已集成）
	if s.metricsService != nil {
		ctx := context.Background()
		metricTypes := []string{"cpu", "memory", "traffic"} // 默认监测指标
		err = s.metricsService.StartMetricsMonitoring(ctx, signal.TaskID, session.SessionID, signal.TargetDevices, metricTypes)
		if err != nil {
			s.logger.Warn("启动指标监测失败（不影响主要流程）",
				zap.String("request_id", requestID),
				zap.String("task_id", signal.TaskID),
				zap.Error(err))
			// 指标监测失败不影响主要任务流程
		} else {
			s.logger.Info("指标监测已启动",
				zap.String("request_id", requestID),
				zap.String("task_id", signal.TaskID))
		}
	}

	s.logger.Info("任务监控会话创建成功",
		zap.String("request_id", requestID),
		zap.String("task_id", signal.TaskID),
		zap.String("session_id", session.SessionID),
		zap.Int("device_count", len(signal.TargetDevices)))

	return &TaskSignalResponse{
		Success:   true,
		SessionID: session.SessionID,
		Message:   "任务监控会话创建成功",
		TaskID:    signal.TaskID,
	}, nil
}

// handleTaskEnd 处理任务结束信号
func (s *Service) handleTaskEnd(signal *TaskSignal, requestID string) (*TaskSignalResponse, error) {
	// 停止任务监控 (自动结束)
	err := s.taskDAO.StopTaskMonitoring(signal.TaskID, "auto")
	if err != nil {
		s.logger.Error("停止任务监控失败",
			zap.String("request_id", requestID),
			zap.String("task_id", signal.TaskID),
			zap.Error(err))
		return &TaskSignalResponse{
			Success: false,
			Message: "停止任务监控失败",
			TaskID:  signal.TaskID,
		}, err
	}

	// 获取会话信息
	session, err := s.taskDAO.GetTaskSessionByTaskID(signal.TaskID)
	if err != nil {
		s.logger.Error("获取任务会话失败",
			zap.String("request_id", requestID),
			zap.String("task_id", signal.TaskID),
			zap.Error(err))
	}

	var sessionID string
	if session != nil {
		sessionID = session.SessionID
		// 停止数据收集
		err = s.stopDataCollection(sessionID, requestID)
		if err != nil {
			s.logger.Error("停止数据收集失败",
				zap.String("request_id", requestID),
				zap.String("session_id", sessionID),
				zap.Error(err))
		}

		// 可选：停止指标监测（如果已集成）
		if s.metricsService != nil {
			ctx := context.Background()
			err = s.metricsService.StopMetricsMonitoring(ctx, signal.TaskID)
			if err != nil {
				s.logger.Warn("停止指标监测失败（不影响主要流程）",
					zap.String("request_id", requestID),
					zap.String("task_id", signal.TaskID),
					zap.Error(err))
			} else {
				s.logger.Info("指标监测已停止",
					zap.String("request_id", requestID),
					zap.String("task_id", signal.TaskID))
			}
		}
	}

	s.logger.Info("任务监控会话结束",
		zap.String("request_id", requestID),
		zap.String("task_id", signal.TaskID),
		zap.String("session_id", sessionID))

	return &TaskSignalResponse{
		Success:   true,
		SessionID: sessionID,
		Message:   "任务监控会话结束",
		TaskID:    signal.TaskID,
	}, nil
}

// handleTaskAbort 处理任务中止信号
func (s *Service) handleTaskAbort(signal *TaskSignal, requestID string) (*TaskSignalResponse, error) {
	// 中止任务监控 - 与结束处理类似，但标记为中止状态 (自动中止)
	err := s.taskDAO.StopTaskMonitoring(signal.TaskID, "auto")
	if err != nil {
		s.logger.Error("中止任务监控失败",
			zap.String("request_id", requestID),
			zap.String("task_id", signal.TaskID),
			zap.Error(err))
		return &TaskSignalResponse{
			Success: false,
			Message: "中止任务监控失败",
			TaskID:  signal.TaskID,
		}, err
	}

	// 获取会话信息
	session, err := s.taskDAO.GetTaskSessionByTaskID(signal.TaskID)
	if err != nil {
		s.logger.Error("获取任务会话失败",
			zap.String("request_id", requestID),
			zap.String("task_id", signal.TaskID),
			zap.Error(err))
	}

	var sessionID string
	if session != nil {
		sessionID = session.SessionID
		// 停止数据收集
		err = s.stopDataCollection(sessionID, requestID)
		if err != nil {
			s.logger.Error("停止数据收集失败",
				zap.String("request_id", requestID),
				zap.String("session_id", sessionID),
				zap.Error(err))
		}
	}

	s.logger.Info("任务监控会话中止",
		zap.String("request_id", requestID),
		zap.String("task_id", signal.TaskID),
		zap.String("session_id", sessionID))

	return &TaskSignalResponse{
		Success:   true,
		SessionID: sessionID,
		Message:   "任务监控会话中止",
		TaskID:    signal.TaskID,
	}, nil
}

// StartTaskMonitoring 启动任务监控
func (s *Service) StartTaskMonitoring(taskID string, request *TaskMonitoringRequest) (*TaskMonitoringResponse, error) {
	s.logger.Info("启动任务监控",
		zap.String("task_id", taskID),
		zap.Int("device_count", len(request.DeviceIDs)))

	// 创建任务监控会话
	session, err := s.taskDAO.StartTaskMonitoring(taskID, request.DeviceIDs)
	if err != nil {
		s.logger.Error("创建任务监控会话失败",
			zap.String("task_id", taskID),
			zap.Error(err))
		return &TaskMonitoringResponse{
			Success: false,
			Message: "创建任务监控会话失败",
			TaskID:  taskID,
		}, err
	}

	// 启动数据收集
	requestID := fmt.Sprintf("manual-%s-%d", taskID, time.Now().Unix())
	err = s.startDataCollection(session.SessionID, request.DeviceIDs, requestID)
	if err != nil {
		s.logger.Error("启动数据收集失败",
			zap.String("task_id", taskID),
			zap.String("session_id", session.SessionID),
			zap.Error(err))
	}

	return &TaskMonitoringResponse{
		Success:     true,
		Message:     "任务监控启动成功",
		TaskID:      taskID,
		SessionID:   session.SessionID,
		DeviceCount: len(request.DeviceIDs),
		StartTime:   session.StartTime,
	}, nil
}

// StopTaskMonitoring 停止任务监控
func (s *Service) StopTaskMonitoring(taskID string) (*TaskMonitoringResponse, error) {
	s.logger.Info("停止任务监控", zap.String("task_id", taskID))

	// 获取会话信息
	session, err := s.taskDAO.GetTaskSessionByTaskID(taskID)
	if err != nil {
		s.logger.Error("获取任务会话失败",
			zap.String("task_id", taskID),
			zap.Error(err))
		return &TaskMonitoringResponse{
			Success: false,
			Message: "获取任务会话失败",
			TaskID:  taskID,
		}, err
	}

	if session == nil {
		return &TaskMonitoringResponse{
			Success: false,
			Message: "任务会话不存在",
			TaskID:  taskID,
		}, nil
	}

	// 停止任务监控 (手动停止)
	err = s.taskDAO.StopTaskMonitoring(taskID, "manual")
	if err != nil {
		s.logger.Error("停止任务监控失败",
			zap.String("task_id", taskID),
			zap.Error(err))
		return &TaskMonitoringResponse{
			Success: false,
			Message: "停止任务监控失败",
			TaskID:  taskID,
		}, err
	}

	// 停止数据收集
	requestID := fmt.Sprintf("stop-%s-%d", taskID, time.Now().Unix())
	err = s.stopDataCollection(session.SessionID, requestID)
	if err != nil {
		s.logger.Error("停止数据收集失败",
			zap.String("task_id", taskID),
			zap.String("session_id", session.SessionID),
			zap.Error(err))
	}

	// 计算持续时间
	var duration string
	var endTime time.Time
	if session.EndTime != nil {
		endTime = *session.EndTime
		duration = endTime.Sub(session.StartTime).String()
	} else {
		endTime = timeutil.NowInLocalTime()
		duration = endTime.Sub(session.StartTime).String()
	}

	return &TaskMonitoringResponse{
		Success:   true,
		Message:   "任务监控停止成功",
		TaskID:    taskID,
		SessionID: session.SessionID,
		StartTime: session.StartTime,
		EndTime:   endTime,
		Duration:  duration,
	}, nil
}

// GetTaskStatus 获取任务状态
func (s *Service) GetTaskStatus(taskID string) (*TaskStatusResponse, error) {
	s.logger.Debug("获取任务状态", zap.String("task_id", taskID))

	session, err := s.taskDAO.GetTaskSessionByTaskID(taskID)
	if err != nil {
		s.logger.Error("获取任务会话失败",
			zap.String("task_id", taskID),
			zap.Error(err))
		return nil, err
	}

	if session == nil {
		return &TaskStatusResponse{
			TaskID:   taskID,
			Status:   "UNKNOWN", // 使用字符串常量
			IsActive: false,
		}, nil
	}

	// 解析设备列表
	var deviceCount int
	if len(session.DeviceIDs) > 0 {
		deviceCount = len(session.DeviceIDs)
	}

	// 转换状态字符串
	var statusStr string
	switch session.Status {
	case "active":
		statusStr = "SUCCESS" // 监控会话活跃中
	case "completed":
		statusStr = "SUCCESS" // 监控会话已完成
	default:
		statusStr = "UNKNOWN" // 未知状态
	}

	return &TaskStatusResponse{
		TaskID:      taskID,
		Status:      statusStr,
		SessionID:   session.SessionID,
		StartTime:   session.StartTime,
		EndTime:     session.EndTime,
		DeviceCount: deviceCount,
		IsActive:    session.Status == "active",
	}, nil
}

// GetTaskAlerts 获取任务相关告警
func (s *Service) GetTaskAlerts(taskID string, limit, offset int) (*TaskAlertListResponse, error) {
	s.logger.Debug("获取任务相关告警",
		zap.String("task_id", taskID),
		zap.Int("limit", limit),
		zap.Int("offset", offset))

	// 获取告警列表
	alerts, total, err := s.dao.GetTaskRelatedAlerts(taskID, limit, offset)
	if err != nil {
		s.logger.Error("获取任务相关告警失败",
			zap.String("task_id", taskID),
			zap.Error(err))
		return nil, err
	}

	// 计算分页信息
	page := (offset / limit) + 1
	pagination := &PaginationInfo{
		Total:    total,
		Limit:    limit,
		Offset:   offset,
		Page:     page,
		PageSize: limit,
	}

	return &TaskAlertListResponse{
		TaskID:     taskID,
		Alerts:     alerts,
		Pagination: pagination,
	}, nil
}

// GetTaskAlertStatistics 获取任务告警统计
func (s *Service) GetTaskAlertStatistics(taskID string, startTime, endTime *time.Time) (*TaskAlertStatistics, error) {
	s.logger.Debug("获取任务告警统计",
		zap.String("task_id", taskID))

	// 如果没有指定时间范围，使用任务会话的时间范围
	var timeRange TimeRange
	if startTime != nil && endTime != nil {
		timeRange = TimeRange{
			StartTime: *startTime,
			EndTime:   *endTime,
		}
	} else {
		session, err := s.taskDAO.GetTaskSessionByTaskID(taskID)
		if err != nil {
			s.logger.Error("获取任务会话失败",
				zap.String("task_id", taskID),
				zap.Error(err))
			return nil, err
		}

		if session == nil {
			return &TaskAlertStatistics{
				TaskID: taskID,
			}, nil
		}

		timeRange.StartTime = session.StartTime
		if session.EndTime != nil {
			timeRange.EndTime = *session.EndTime
		} else {
			timeRange.EndTime = timeutil.NowInLocalTime()
		}
	}

	stats, err := s.dao.GetTaskAlertStatistics(taskID, timeRange)
	if err != nil {
		s.logger.Error("获取任务告警统计失败",
			zap.String("task_id", taskID),
			zap.Error(err))
		return nil, err
	}

	return stats, nil
}

// startDataCollection 启动数据收集
func (s *Service) startDataCollection(sessionID string, deviceIDs []string, requestID string) error {
	s.logger.Info("启动数据收集",
		zap.String("request_id", requestID),
		zap.String("session_id", sessionID),
		zap.Int("device_count", len(deviceIDs)))

	now := timeutil.NowInLocalTime()

	// 为每个设备和数据类型创建收集记录
	dataTypes := []string{"metrics", "logs", "alerts", "events"}

	for _, deviceID := range deviceIDs {
		for _, dataType := range dataTypes {
			collection := &TaskDataCollection{
				SessionID:       sessionID,
				DataType:        dataType,
				DeviceID:        deviceID,
				CollectionStart: now,
				Status:          "collecting",
				DataCount:       0,
			}

			err := s.dao.CreateTaskDataCollection(collection)
			if err != nil {
				s.logger.Error("创建数据收集记录失败",
					zap.String("request_id", requestID),
					zap.String("session_id", sessionID),
					zap.String("device_id", deviceID),
					zap.String("data_type", dataType),
					zap.Error(err))
				// 继续处理其他记录
			}
		}
	}

	return nil
}

// stopDataCollection 停止数据收集
func (s *Service) stopDataCollection(sessionID string, requestID string) error {
	s.logger.Info("停止数据收集",
		zap.String("request_id", requestID),
		zap.String("session_id", sessionID))

	// 获取所有进行中的收集记录
	collections, err := s.dao.GetTaskDataCollectionsBySessionID(sessionID)
	if err != nil {
		s.logger.Error("获取数据收集记录失败",
			zap.String("request_id", requestID),
			zap.String("session_id", sessionID),
			zap.Error(err))
		return err
	}

	now := timeutil.NowInLocalTime()

	// 更新所有进行中的收集记录为完成状态
	for _, collection := range collections {
		if collection.Status == "collecting" {
			collection.CollectionEnd = &now
			collection.Status = "completed"

			err := s.dao.UpdateTaskDataCollection(collection)
			if err != nil {
				s.logger.Error("更新数据收集记录失败",
					zap.String("request_id", requestID),
					zap.String("collection_id", collection.ID),
					zap.Error(err))
				// 继续处理其他记录
			}
		}
	}

	return nil
}

// GetCollectionSummary 获取数据收集汇总
func (s *Service) GetCollectionSummary(sessionID string) (*CollectionSummary, error) {
	// 获取任务ID
	session, err := s.taskDAO.GetTaskSessionByID(sessionID)
	if err != nil {
		s.logger.Error("获取任务会话失败",
			zap.String("session_id", sessionID),
			zap.Error(err))
		return nil, err
	}

	summary, err := s.dao.GetCollectionSummary(sessionID)
	if err != nil {
		s.logger.Error("获取数据收集汇总失败",
			zap.String("session_id", sessionID),
			zap.Error(err))
		return nil, err
	}

	if session != nil {
		summary.TaskID = session.TaskID
	}

	return summary, nil
}
