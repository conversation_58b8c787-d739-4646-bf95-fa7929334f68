package task_collaboration

import (
	"database/sql"
	"dcimonitor/internal/utils/timeutil"
	"encoding/json"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

// DAO 提供对任务协同监控数据的访问
type DAO struct {
	db     *sql.DB
	logger *zap.Logger
}

// NewDAO 创建一个新的DAO实例
func NewDAO(db *sql.DB, logger *zap.Logger) *DAO {
	return &DAO{
		db:     db,
		logger: logger,
	}
}

// CreateTaskDataCollection 创建任务数据收集记录
func (dao *DAO) CreateTaskDataCollection(collection *TaskDataCollection) error {
	if collection.ID == "" {
		collection.ID = uuid.New().String()
	}

	query := `
		INSERT INTO monitor_task_data_collections (
			id, session_id, data_type, device_id, collection_start, 
			collection_end, data_count, status, error_message, created_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	now := timeutil.NowInLocalTime()
	collection.CreatedAt = now

	_, err := dao.db.Exec(
		query,
		collection.ID, collection.SessionID, collection.DataType, collection.DeviceID,
		collection.CollectionStart, collection.CollectionEnd, collection.DataCount,
		collection.Status, collection.ErrorMessage, now,
	)

	if err != nil {
		dao.logger.Error("创建任务数据收集记录失败",
			zap.Error(err),
			zap.String("collection_id", collection.ID),
			zap.String("session_id", collection.SessionID))
		return err
	}

	return nil
}

// UpdateTaskDataCollection 更新任务数据收集记录
func (dao *DAO) UpdateTaskDataCollection(collection *TaskDataCollection) error {
	query := `
		UPDATE monitor_task_data_collections SET
			collection_end = ?, data_count = ?, status = ?, error_message = ?
		WHERE id = ?
	`

	_, err := dao.db.Exec(
		query,
		collection.CollectionEnd, collection.DataCount, collection.Status,
		collection.ErrorMessage, collection.ID,
	)

	if err != nil {
		dao.logger.Error("更新任务数据收集记录失败",
			zap.Error(err),
			zap.String("collection_id", collection.ID))
		return err
	}

	return nil
}

// GetTaskDataCollectionsBySessionID 根据会话ID获取数据收集记录
func (dao *DAO) GetTaskDataCollectionsBySessionID(sessionID string) ([]*TaskDataCollection, error) {
	query := `
		SELECT id, session_id, data_type, device_id, collection_start,
			   collection_end, data_count, status, error_message, created_at
		FROM monitor_task_data_collections
		WHERE session_id = ?
		ORDER BY created_at DESC
	`

	rows, err := dao.db.Query(query, sessionID)
	if err != nil {
		dao.logger.Error("查询任务数据收集记录失败",
			zap.Error(err),
			zap.String("session_id", sessionID))
		return nil, err
	}
	defer rows.Close()

	var collections []*TaskDataCollection
	for rows.Next() {
		collection := &TaskDataCollection{}
		err := rows.Scan(
			&collection.ID, &collection.SessionID, &collection.DataType,
			&collection.DeviceID, &collection.CollectionStart, &collection.CollectionEnd,
			&collection.DataCount, &collection.Status, &collection.ErrorMessage,
			&collection.CreatedAt,
		)
		if err != nil {
			dao.logger.Error("扫描任务数据收集记录失败", zap.Error(err))
			continue
		}
		collections = append(collections, collection)
	}

	return collections, nil
}

// CreateTaskAlertAssociation 创建任务告警关联记录
func (dao *DAO) CreateTaskAlertAssociation(association *TaskAlertAssociation) error {
	if association.ID == "" {
		association.ID = uuid.New().String()
	}

	query := `
		INSERT INTO monitor_task_alert_associations (
			id, task_id, alert_id, association_type, device_id, created_at
		) VALUES (?, ?, ?, ?, ?, ?)
	`

	now := timeutil.NowInLocalTime()
	association.CreatedAt = now

	_, err := dao.db.Exec(
		query,
		association.ID, association.TaskID, association.AlertID,
		association.AssociationType, association.DeviceID, now,
	)

	if err != nil {
		dao.logger.Error("创建任务告警关联记录失败",
			zap.Error(err),
			zap.String("association_id", association.ID),
			zap.String("task_id", association.TaskID),
			zap.String("alert_id", association.AlertID))
		return err
	}

	return nil
}

// BatchCreateTaskAlertAssociations 批量创建任务告警关联记录
func (dao *DAO) BatchCreateTaskAlertAssociations(associations []*TaskAlertAssociation) error {
	if len(associations) == 0 {
		return nil
	}

	tx, err := dao.db.Begin()
	if err != nil {
		dao.logger.Error("开始批量创建任务告警关联事务失败", zap.Error(err))
		return err
	}
	defer tx.Rollback()

	query := `
		INSERT INTO monitor_task_alert_associations (
			id, task_id, alert_id, association_type, device_id, created_at
		) VALUES (?, ?, ?, ?, ?, ?)
	`

	stmt, err := tx.Prepare(query)
	if err != nil {
		dao.logger.Error("准备批量插入语句失败", zap.Error(err))
		return err
	}
	defer stmt.Close()

	now := timeutil.NowInLocalTime()
	for _, association := range associations {
		if association.ID == "" {
			association.ID = uuid.New().String()
		}
		association.CreatedAt = now

		_, err = stmt.Exec(
			association.ID, association.TaskID, association.AlertID,
			association.AssociationType, association.DeviceID, now,
		)
		if err != nil {
			dao.logger.Error("批量插入任务告警关联记录失败",
				zap.Error(err),
				zap.String("task_id", association.TaskID),
				zap.String("alert_id", association.AlertID))
			return err
		}
	}

	if err = tx.Commit(); err != nil {
		dao.logger.Error("提交批量创建任务告警关联事务失败", zap.Error(err))
		return err
	}

	dao.logger.Info("批量创建任务告警关联记录成功",
		zap.Int("count", len(associations)))

	return nil
}

// GetTaskAlertAssociations 获取任务的告警关联记录
func (dao *DAO) GetTaskAlertAssociations(taskID string) ([]*TaskAlertAssociation, error) {
	query := `
		SELECT id, task_id, alert_id, association_type, device_id, created_at
		FROM monitor_task_alert_associations
		WHERE task_id = ?
		ORDER BY created_at DESC
	`

	rows, err := dao.db.Query(query, taskID)
	if err != nil {
		dao.logger.Error("查询任务告警关联记录失败",
			zap.Error(err),
			zap.String("task_id", taskID))
		return nil, err
	}
	defer rows.Close()

	var associations []*TaskAlertAssociation
	for rows.Next() {
		association := &TaskAlertAssociation{}
		err := rows.Scan(
			&association.ID, &association.TaskID, &association.AlertID,
			&association.AssociationType, &association.DeviceID, &association.CreatedAt,
		)
		if err != nil {
			dao.logger.Error("扫描任务告警关联记录失败", zap.Error(err))
			continue
		}
		associations = append(associations, association)
	}

	return associations, nil
}

// GetTaskAlertStatistics 获取任务告警统计信息
func (dao *DAO) GetTaskAlertStatistics(taskID string, timeRange TimeRange) (*TaskAlertStatistics, error) {
	query := `
		SELECT 
			COUNT(*) as total_count,
			a.level,
			a.status,
			JSON_EXTRACT(a.annotations, '$.monitoring_phase') as phase,
			taa.association_type,
			a.device_id
		FROM monitor_alert a
		INNER JOIN monitor_task_alert_associations taa ON a.id = taa.alert_id
		WHERE taa.task_id = ? 
		AND a.starts_at BETWEEN ? AND ?
		GROUP BY a.level, a.status, phase, taa.association_type, a.device_id
	`

	rows, err := dao.db.Query(query, taskID, timeRange.StartTime, timeRange.EndTime)
	if err != nil {
		dao.logger.Error("查询任务告警统计失败",
			zap.Error(err),
			zap.String("task_id", taskID))
		return nil, err
	}
	defer rows.Close()

	stats := &TaskAlertStatistics{
		TaskID:      taskID,
		TimeRange:   timeRange,
		LevelCount:  make(map[string]int),
		StatusCount: make(map[string]int),
		PhaseCount:  make(map[string]int),
		DeviceCount: make(map[string]int),
	}

	for rows.Next() {
		var count int
		var level, status, phase, associationType, deviceID sql.NullString

		err := rows.Scan(&count, &level, &status, &phase, &associationType, &deviceID)
		if err != nil {
			dao.logger.Error("扫描任务告警统计失败", zap.Error(err))
			continue
		}

		stats.TotalCount += count

		if level.Valid {
			stats.LevelCount[level.String] += count
		}
		if status.Valid {
			stats.StatusCount[status.String] += count
		}
		if phase.Valid {
			stats.PhaseCount[phase.String] += count
		}
		if associationType.Valid {
			stats.PhaseCount[associationType.String] += count
		}
		if deviceID.Valid {
			stats.DeviceCount[deviceID.String] += count
		}
	}

	return stats, nil
}

// GetTaskRelatedAlerts 获取任务相关的告警列表
func (dao *DAO) GetTaskRelatedAlerts(taskID string, limit, offset int) ([]*TaskRelatedAlert, int, error) {
	// 查询总数
	countQuery := `
		SELECT COUNT(*)
		FROM monitor_alert a
		INNER JOIN monitor_task_alert_associations taa ON a.id = taa.alert_id
		WHERE taa.task_id = ?
	`

	var total int
	err := dao.db.QueryRow(countQuery, taskID).Scan(&total)
	if err != nil {
		dao.logger.Error("查询任务相关告警总数失败",
			zap.Error(err),
			zap.String("task_id", taskID))
		return nil, 0, err
	}

	// 查询告警列表
	query := `
		SELECT a.id, a.name, a.level, a.status, a.device_id, a.starts_at, a.ends_at,
			   taa.association_type, a.annotations
		FROM monitor_alert a
		INNER JOIN monitor_task_alert_associations taa ON a.id = taa.alert_id
		WHERE taa.task_id = ?
		ORDER BY a.starts_at DESC
		LIMIT ? OFFSET ?
	`

	rows, err := dao.db.Query(query, taskID, limit, offset)
	if err != nil {
		dao.logger.Error("查询任务相关告警列表失败",
			zap.Error(err),
			zap.String("task_id", taskID))
		return nil, 0, err
	}
	defer rows.Close()

	var alerts []*TaskRelatedAlert
	for rows.Next() {
		alert := &TaskRelatedAlert{}
		var annotationsJSON sql.NullString
		var associationType string

		err := rows.Scan(
			&alert.ID, &alert.Name, &alert.Level, &alert.Status,
			&alert.DeviceID, &alert.StartsAt, &alert.EndsAt,
			&associationType, &annotationsJSON,
		)
		if err != nil {
			dao.logger.Error("扫描任务相关告警失败", zap.Error(err))
			continue
		}

		// 解析annotations获取task_relation和monitoring_phase
		if annotationsJSON.Valid {
			var annotations map[string]string
			if err := json.Unmarshal([]byte(annotationsJSON.String), &annotations); err == nil {
				if taskRelation, ok := annotations["task_relation"]; ok {
					alert.TaskRelation = taskRelation
				}
				if monitoringPhase, ok := annotations["monitoring_phase"]; ok {
					alert.MonitoringPhase = monitoringPhase
				}
			}
		}

		// 设置默认值
		if alert.TaskRelation == "" {
			if associationType == "active_execution" {
				alert.TaskRelation = "任务执行期间"
			} else if associationType == "post_completion" {
				alert.TaskRelation = "任务后续影响"
			}
		}

		if alert.MonitoringPhase == "" {
			alert.MonitoringPhase = associationType
		}

		alerts = append(alerts, alert)
	}

	return alerts, total, nil
}

// GetCollectionSummary 获取数据收集汇总信息
func (dao *DAO) GetCollectionSummary(sessionID string) (*CollectionSummary, error) {
	query := `
		SELECT 
			session_id,
			SUM(CASE WHEN data_type = 'metrics' THEN data_count ELSE 0 END) as total_metrics,
			SUM(CASE WHEN data_type = 'logs' THEN data_count ELSE 0 END) as total_logs,
			SUM(CASE WHEN data_type = 'alerts' THEN data_count ELSE 0 END) as total_alerts,
			SUM(CASE WHEN data_type = 'events' THEN data_count ELSE 0 END) as total_events
		FROM monitor_task_data_collections
		WHERE session_id = ?
		GROUP BY session_id
	`

	summary := &CollectionSummary{SessionID: sessionID}
	err := dao.db.QueryRow(query, sessionID).Scan(
		&summary.SessionID,
		&summary.TotalMetrics,
		&summary.TotalLogs,
		&summary.TotalAlerts,
		&summary.TotalEvents,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			// 没有找到记录，返回空的汇总
			return summary, nil
		}
		dao.logger.Error("查询数据收集汇总失败",
			zap.Error(err),
			zap.String("session_id", sessionID))
		return nil, err
	}

	return summary, nil
}
