package task_collaboration

import (
	"database/sql"
	"dcimonitor/internal/alert"
	"fmt"

	"go.uber.org/zap"
)

// Module 任务协同监控模块
type Module struct {
	DAO           *DAO
	Service       *Service
	DataCollector *DataCollector
	Handler       *Handler
	Config        *ModuleConfig
	logger        *zap.Logger
}

// ModuleConfig 模块配置
type ModuleConfig struct {
	DataCollector *DataCollectorConfig `yaml:"data_collector"`
	Enabled       bool                 `yaml:"enabled"`
}

// NewModule 创建新的模块实例
func NewModule(
	db *sql.DB,
	taskDAO *alert.TaskDAO,
	alertDAO *alert.DAO,
	config *ModuleConfig,
	logger *zap.Logger,
) (*Module, error) {
	if config == nil {
		return nil, fmt.Errorf("模块配置不能为空")
	}

	if !config.Enabled {
		logger.Info("任务协同监控模块已禁用")
		return &Module{
			Config: config,
			logger: logger,
		}, nil
	}

	logger.Info("初始化任务协同监控模块")

	// 创建DAO
	dao := NewDAO(db, logger)

	// 创建Service
	service := NewService(dao, taskDAO, alertDAO, logger)

	// 创建数据收集器
	var dataCollector *DataCollector
	if config.DataCollector != nil {
		var err error
		dataCollector, err = NewDataCollector(dao, config.DataCollector, logger)
		if err != nil {
			logger.Error("创建数据收集器失败", zap.Error(err))
			return nil, fmt.Errorf("创建数据收集器失败: %w", err)
		}
	}

	// 创建Handler（不依赖SignalProcessor）
	handler := NewHandler(service, nil, dataCollector, logger)

	module := &Module{
		DAO:           dao,
		Service:       service,
		DataCollector: dataCollector,
		Handler:       handler,
		Config:        config,
		logger:        logger,
	}

	logger.Info("任务协同监控模块初始化成功")
	return module, nil
}

// Start 启动模块
func (m *Module) Start() error {
	if !m.Config.Enabled {
		m.logger.Info("任务协同监控模块未启用，跳过启动")
		return nil
	}

	m.logger.Info("启动任务协同监控模块")

	// 启动数据收集器
	if m.DataCollector != nil {
		if err := m.DataCollector.Start(); err != nil {
			m.logger.Error("启动数据收集器失败", zap.Error(err))
			// 不返回错误，允许模块继续运行，REST API仍然可用
			m.logger.Warn("数据收集器启动失败，数据收集功能不可用，但REST API接口仍然可用")
		} else {
			m.logger.Info("数据收集器启动成功")
		}
	}

	m.logger.Info("任务协同监控模块启动完成")
	return nil
}

// Stop 停止模块
func (m *Module) Stop() error {
	if !m.Config.Enabled {
		m.logger.Info("任务协同监控模块未启用，跳过停止")
		return nil
	}

	m.logger.Info("停止任务协同监控模块")

	// 停止数据收集器
	if m.DataCollector != nil {
		if err := m.DataCollector.Stop(); err != nil {
			m.logger.Error("停止数据收集器失败", zap.Error(err))
		} else {
			m.logger.Info("数据收集器停止成功")
		}
	}

	m.logger.Info("任务协同监控模块停止完成")
	return nil
}

// IsEnabled 检查模块是否启用
func (m *Module) IsEnabled() bool {
	return m.Config.Enabled
}

// IsRunning 检查模块是否运行中
func (m *Module) IsRunning() bool {
	if !m.Config.Enabled {
		return false
	}

	dataCollectorRunning := m.DataCollector != nil && m.DataCollector.GetStatus()["is_running"].(bool)

	return dataCollectorRunning
}

// GetStatus 获取模块状态
func (m *Module) GetStatus() map[string]interface{} {
	status := map[string]interface{}{
		"enabled":   m.Config.Enabled,
		"running":   m.IsRunning(),
		"component": "task_collaboration",
		"version":   "1.0.0",
	}

	if m.Config.Enabled {
		if m.DataCollector != nil {
			status["data_collector"] = m.DataCollector.GetStatus()
		}
	}

	return status
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig() *ModuleConfig {
	return &ModuleConfig{
		Enabled: true,
		DataCollector: &DataCollectorConfig{
			CollectInterval:          "30s",
			MetricsEnabled:           true,
			LogsEnabled:              true,
			AlertsEnabled:            true,
			EventsEnabled:            true,
			MaxConcurrentCollections: 10,
			PrometheusEndpoint:       "http://localhost:9090",
			ElasticsearchEndpoint:    "http://localhost:9200",
		},
	}
}

// ValidateConfig 验证配置
func ValidateConfig(config *ModuleConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}

	if !config.Enabled {
		return nil // 如果模块未启用，不需要验证其他配置
	}

	// 验证数据收集器配置
	if config.DataCollector != nil {
		if config.DataCollector.CollectInterval == "" {
			return fmt.Errorf("数据收集器收集间隔不能为空")
		}
		if config.DataCollector.MaxConcurrentCollections <= 0 {
			return fmt.Errorf("数据收集器最大并发数必须大于0")
		}
	}

	return nil
}

// ExtendTaskAlertAssociationService 扩展告警关联服务
// 这个方法用于将任务协同监控功能集成到alert模块中
func (m *Module) ExtendTaskAlertAssociationService() TaskAlertAssociationExtension {
	if !m.Config.Enabled || m.Service == nil {
		// 如果模块未启用，返回空实现
		return &EmptyTaskAlertAssociationExtension{}
	}

	return &TaskAlertAssociationExtensionImpl{
		service: m.Service,
		logger:  m.logger,
	}
}

// TaskAlertAssociationExtension 任务告警关联扩展接口
type TaskAlertAssociationExtension interface {
	GetTaskByDeviceWithExtendedMonitoring(deviceID string) (*TaskAssociationResult, error)
	CreateTaskAlertAssociation(taskID, alertID, deviceID, associationType string) error
	BatchCreateTaskAlertAssociations(associations []*TaskAlertAssociation) error
}

// TaskAlertAssociationExtensionImpl 任务告警关联扩展实现
type TaskAlertAssociationExtensionImpl struct {
	service *Service
	logger  *zap.Logger
}

// GetTaskByDeviceWithExtendedMonitoring 查询设备相关的任务（包含延展监测）
func (ext *TaskAlertAssociationExtensionImpl) GetTaskByDeviceWithExtendedMonitoring(deviceID string) (*TaskAssociationResult, error) {
	// 这里需要调用Service层的方法
	// 由于Service层还没有实现这个方法，这里先返回空结果
	return &TaskAssociationResult{}, nil
}

// CreateTaskAlertAssociation 创建任务告警关联
func (ext *TaskAlertAssociationExtensionImpl) CreateTaskAlertAssociation(taskID, alertID, deviceID, associationType string) error {
	association := &TaskAlertAssociation{
		TaskID:          taskID,
		AlertID:         alertID,
		DeviceID:        deviceID,
		AssociationType: associationType,
	}

	return ext.service.dao.CreateTaskAlertAssociation(association)
}

// BatchCreateTaskAlertAssociations 批量创建任务告警关联
func (ext *TaskAlertAssociationExtensionImpl) BatchCreateTaskAlertAssociations(associations []*TaskAlertAssociation) error {
	return ext.service.dao.BatchCreateTaskAlertAssociations(associations)
}

// EmptyTaskAlertAssociationExtension 空的任务告警关联扩展实现
type EmptyTaskAlertAssociationExtension struct{}

func (ext *EmptyTaskAlertAssociationExtension) GetTaskByDeviceWithExtendedMonitoring(deviceID string) (*TaskAssociationResult, error) {
	return &TaskAssociationResult{}, nil
}

func (ext *EmptyTaskAlertAssociationExtension) CreateTaskAlertAssociation(taskID, alertID, deviceID, associationType string) error {
	return nil
}

func (ext *EmptyTaskAlertAssociationExtension) BatchCreateTaskAlertAssociations(associations []*TaskAlertAssociation) error {
	return nil
}

// TaskAssociationResult 任务关联查询结果
type TaskAssociationResult struct {
	TaskID      string `json:"taskId"`
	AlertType   string `json:"alertType"` // "active" 或 "post_task"
	SessionID   string `json:"sessionId"`
	DeviceCount int    `json:"deviceCount"`
}
