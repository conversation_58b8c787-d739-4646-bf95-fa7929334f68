package task_collaboration

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/IBM/sarama"
	"go.uber.org/zap"
)

// SignalProcessor 任务信号处理器
type SignalProcessor struct {
	service         *Service
	consumerGroup   sarama.ConsumerGroup
	logger          *zap.Logger
	ctx             context.Context
	cancel          context.CancelFunc
	wg              sync.WaitGroup
	topic           string
	consumerGroupID string
	isRunning       bool
	mu              sync.RWMutex
}

// SignalProcessorConfig 信号处理器配置
type SignalProcessorConfig struct {
	Brokers           []string `yaml:"brokers"`
	Topic             string   `yaml:"topic"`
	ConsumerGroupID   string   `yaml:"consumer_group_id"`
	SessionTimeout    string   `yaml:"session_timeout"`
	HeartbeatInterval string   `yaml:"heartbeat_interval"`
}

// NewSignalProcessor 创建新的信号处理器实例
func NewSignalProcessor(service *Service, config *SignalProcessorConfig, logger *zap.Logger) (*SignalProcessor, error) {
	// 创建Sarama配置
	saramaConfig := sarama.NewConfig()
	saramaConfig.Version = sarama.V2_8_0_0
	saramaConfig.Consumer.Group.Session.Timeout = 10 * time.Second
	saramaConfig.Consumer.Group.Heartbeat.Interval = 3 * time.Second
	saramaConfig.Consumer.Offsets.Initial = sarama.OffsetNewest
	saramaConfig.Consumer.Return.Errors = true

	// 解析配置中的超时时间
	if config.SessionTimeout != "" {
		if duration, err := time.ParseDuration(config.SessionTimeout); err == nil {
			saramaConfig.Consumer.Group.Session.Timeout = duration
		}
	}
	if config.HeartbeatInterval != "" {
		if duration, err := time.ParseDuration(config.HeartbeatInterval); err == nil {
			saramaConfig.Consumer.Group.Heartbeat.Interval = duration
		}
	}

	// 创建消费者组
	consumerGroup, err := sarama.NewConsumerGroup(config.Brokers, config.ConsumerGroupID, saramaConfig)
	if err != nil {
		logger.Error("创建Kafka消费者组失败",
			zap.Error(err),
			zap.Strings("brokers", config.Brokers),
			zap.String("consumer_group", config.ConsumerGroupID))
		return nil, fmt.Errorf("创建Kafka消费者组失败: %w", err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	processor := &SignalProcessor{
		service:         service,
		consumerGroup:   consumerGroup,
		logger:          logger,
		ctx:             ctx,
		cancel:          cancel,
		topic:           config.Topic,
		consumerGroupID: config.ConsumerGroupID,
		isRunning:       false,
	}

	logger.Info("任务信号处理器创建成功",
		zap.String("topic", config.Topic),
		zap.String("consumer_group", config.ConsumerGroupID),
		zap.Strings("brokers", config.Brokers))

	return processor, nil
}

// Start 启动信号处理器
func (sp *SignalProcessor) Start() error {
	sp.mu.Lock()
	defer sp.mu.Unlock()

	if sp.isRunning {
		return fmt.Errorf("信号处理器已在运行中")
	}

	sp.logger.Info("启动任务信号处理器",
		zap.String("topic", sp.topic),
		zap.String("consumer_group", sp.consumerGroupID))

	sp.isRunning = true

	// 启动消费者协程
	sp.wg.Add(1)
	go sp.consumeLoop()

	// 启动错误处理协程
	sp.wg.Add(1)
	go sp.errorLoop()

	return nil
}

// Stop 停止信号处理器
func (sp *SignalProcessor) Stop() error {
	sp.mu.Lock()
	defer sp.mu.Unlock()

	if !sp.isRunning {
		return nil
	}

	sp.logger.Info("停止任务信号处理器")

	// 取消上下文
	sp.cancel()

	// 关闭消费者组
	if err := sp.consumerGroup.Close(); err != nil {
		sp.logger.Error("关闭Kafka消费者组失败", zap.Error(err))
	}

	// 等待所有协程结束
	sp.wg.Wait()

	sp.isRunning = false

	sp.logger.Info("任务信号处理器已停止")
	return nil
}

// IsRunning 检查处理器是否在运行
func (sp *SignalProcessor) IsRunning() bool {
	sp.mu.RLock()
	defer sp.mu.RUnlock()
	return sp.isRunning
}

// consumeLoop 消费循环
func (sp *SignalProcessor) consumeLoop() {
	defer sp.wg.Done()

	topics := []string{sp.topic}
	handler := &SignalConsumerHandler{
		processor: sp,
		logger:    sp.logger,
	}

	for {
		select {
		case <-sp.ctx.Done():
			sp.logger.Info("消费循环收到停止信号")
			return
		default:
			err := sp.consumerGroup.Consume(sp.ctx, topics, handler)
			if err != nil {
				sp.logger.Error("Kafka消费过程中发生错误", zap.Error(err))
				// 短暂等待后重试
				time.Sleep(time.Second)
			}
		}
	}
}

// errorLoop 错误处理循环
func (sp *SignalProcessor) errorLoop() {
	defer sp.wg.Done()

	for {
		select {
		case <-sp.ctx.Done():
			sp.logger.Info("错误处理循环收到停止信号")
			return
		case err := <-sp.consumerGroup.Errors():
			if err != nil {
				sp.logger.Error("Kafka消费者组错误", zap.Error(err))
			}
		}
	}
}

// SignalConsumerHandler Kafka消费者处理器
type SignalConsumerHandler struct {
	processor *SignalProcessor
	logger    *zap.Logger
}

// Setup 消费者组设置
func (h *SignalConsumerHandler) Setup(sarama.ConsumerGroupSession) error {
	h.logger.Info("Kafka消费者组会话开始")
	return nil
}

// Cleanup 消费者组清理
func (h *SignalConsumerHandler) Cleanup(sarama.ConsumerGroupSession) error {
	h.logger.Info("Kafka消费者组会话结束")
	return nil
}

// ConsumeClaim 消费分区消息
func (h *SignalConsumerHandler) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for {
		select {
		case message := <-claim.Messages():
			if message == nil {
				return nil
			}

			// 处理消息
			err := h.processMessage(message)
			if err != nil {
				h.logger.Error("处理Kafka消息失败",
					zap.Error(err),
					zap.String("topic", message.Topic),
					zap.Int32("partition", message.Partition),
					zap.Int64("offset", message.Offset))
			}

			// 标记消息已处理
			session.MarkMessage(message, "")

		case <-session.Context().Done():
			return nil
		}
	}
}

// processMessage 处理单个消息
func (h *SignalConsumerHandler) processMessage(message *sarama.ConsumerMessage) error {
	requestID := fmt.Sprintf("kafka-%s-%d-%d", message.Topic, message.Partition, message.Offset)

	h.logger.Info("收到任务信号消息",
		zap.String("request_id", requestID),
		zap.String("topic", message.Topic),
		zap.Int32("partition", message.Partition),
		zap.Int64("offset", message.Offset),
		zap.Time("timestamp", message.Timestamp))

	// 解析消息体为TaskSignal
	var signal TaskSignal
	if err := json.Unmarshal(message.Value, &signal); err != nil {
		h.logger.Error("解析任务信号失败",
			zap.String("request_id", requestID),
			zap.Error(err),
			zap.ByteString("message_value", message.Value))
		return fmt.Errorf("解析任务信号失败: %w", err)
	}

	// 验证信号完整性
	if err := h.validateSignal(&signal); err != nil {
		h.logger.Error("任务信号验证失败",
			zap.String("request_id", requestID),
			zap.Error(err),
			zap.String("signal_type", signal.SignalType),
			zap.String("task_id", signal.TaskID))
		return fmt.Errorf("任务信号验证失败: %w", err)
	}

	// 设置消息时间戳
	if signal.Timestamp.IsZero() {
		signal.Timestamp = message.Timestamp
	}

	// 处理消息头中的追踪信息
	h.processMessageHeaders(message.Headers, requestID)

	// 调用Service层处理信号
	response, err := h.processor.service.ProcessTaskSignal(&signal)
	if err != nil {
		h.logger.Error("处理任务信号失败",
			zap.String("request_id", requestID),
			zap.String("task_id", signal.TaskID),
			zap.String("signal_type", signal.SignalType),
			zap.Error(err))
		return fmt.Errorf("处理任务信号失败: %w", err)
	}

	h.logger.Info("任务信号处理成功",
		zap.String("request_id", requestID),
		zap.String("task_id", signal.TaskID),
		zap.String("signal_type", signal.SignalType),
		zap.String("session_id", response.SessionID),
		zap.Bool("success", response.Success),
		zap.String("message", response.Message))

	return nil
}

// validateSignal 验证任务信号的完整性
func (h *SignalConsumerHandler) validateSignal(signal *TaskSignal) error {
	if signal.TaskID == "" {
		return fmt.Errorf("任务ID不能为空")
	}

	if signal.SignalType == "" {
		return fmt.Errorf("信号类型不能为空")
	}

	// 验证信号类型
	switch signal.SignalType {
	case "start", "end", "abort":
		// 有效的信号类型
	default:
		return fmt.Errorf("无效的信号类型: %s", signal.SignalType)
	}

	// 对于开始信号，验证必需字段
	if signal.SignalType == "start" {
		if signal.TaskType == "" {
			return fmt.Errorf("任务类型不能为空")
		}
		if len(signal.TargetDevices) == 0 {
			return fmt.Errorf("目标设备列表不能为空")
		}
	}

	return nil
}

// processMessageHeaders 处理消息头信息
func (h *SignalConsumerHandler) processMessageHeaders(headers []*sarama.RecordHeader, requestID string) {
	headerMap := make(map[string]string)
	for _, header := range headers {
		if header != nil {
			headerMap[string(header.Key)] = string(header.Value)
		}
	}

	if len(headerMap) > 0 {
		h.logger.Debug("消息头信息",
			zap.String("request_id", requestID),
			zap.Any("headers", headerMap))
	}

	// 处理特殊的追踪头
	if traceID, exists := headerMap["trace_id"]; exists {
		h.logger.Info("收到追踪ID",
			zap.String("request_id", requestID),
			zap.String("trace_id", traceID))
	}

	if spanID, exists := headerMap["span_id"]; exists {
		h.logger.Info("收到跨度ID",
			zap.String("request_id", requestID),
			zap.String("span_id", spanID))
	}
}

// GetStatus 获取处理器状态信息
func (sp *SignalProcessor) GetStatus() map[string]interface{} {
	sp.mu.RLock()
	defer sp.mu.RUnlock()

	status := map[string]interface{}{
		"is_running":        sp.isRunning,
		"topic":             sp.topic,
		"consumer_group_id": sp.consumerGroupID,
	}

	if sp.isRunning {
		status["start_time"] = time.Now() // 这里应该记录实际的启动时间
	}

	return status
}

// ProcessDirectSignal 直接处理任务信号（用于REST API）
func (sp *SignalProcessor) ProcessDirectSignal(signal *TaskSignal) (*TaskSignalResponse, error) {
	requestID := fmt.Sprintf("direct-%s-%d", signal.TaskID, time.Now().Unix())

	sp.logger.Info("直接处理任务信号",
		zap.String("request_id", requestID),
		zap.String("task_id", signal.TaskID),
		zap.String("signal_type", signal.SignalType))

	// 验证信号
	handler := &SignalConsumerHandler{
		processor: sp,
		logger:    sp.logger,
	}

	if err := handler.validateSignal(signal); err != nil {
		sp.logger.Error("任务信号验证失败",
			zap.String("request_id", requestID),
			zap.Error(err))
		return &TaskSignalResponse{
			Success: false,
			Message: fmt.Sprintf("信号验证失败: %s", err.Error()),
			TaskID:  signal.TaskID,
		}, nil
	}

	// 设置时间戳
	if signal.Timestamp.IsZero() {
		signal.Timestamp = time.Now()
	}

	// 调用Service层处理
	response, err := sp.service.ProcessTaskSignal(signal)
	if err != nil {
		sp.logger.Error("处理任务信号失败",
			zap.String("request_id", requestID),
			zap.String("task_id", signal.TaskID),
			zap.Error(err))
		return &TaskSignalResponse{
			Success: false,
			Message: fmt.Sprintf("处理失败: %s", err.Error()),
			TaskID:  signal.TaskID,
		}, nil
	}

	sp.logger.Info("直接处理任务信号成功",
		zap.String("request_id", requestID),
		zap.String("task_id", signal.TaskID),
		zap.Bool("success", response.Success))

	return response, nil
}
