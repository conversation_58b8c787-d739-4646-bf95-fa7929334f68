package task_collaboration

import (
	"net/http"
	"strconv"
	"time"

	"dcimonitor/internal/middleware"
	"dcimonitor/internal/models"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Handler HTTP请求处理器
type Handler struct {
	service         *Service
	signalProcessor *SignalProcessor
	dataCollector   *DataCollector
	logger          *zap.Logger
}

// NewHandler 创建新的Handler实例
func NewHandler(service *Service, signalProcessor *SignalProcessor, dataCollector *DataCollector, logger *zap.Logger) *Handler {
	return &Handler{
		service:         service,
		signalProcessor: signalProcessor,
		dataCollector:   dataCollector,
		logger:          logger,
	}
}

// SetMetricsService 设置指标监测服务（用于模块集成）
func (h *Handler) SetMetricsService(metricsService MetricsService) {
	h.service.SetMetricsService(metricsService)
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(r *gin.RouterGroup) {
	// 任务信号处理
	r.POST("/tasks/signals", h.ReceiveTaskSignal)

	// 任务监控管理
	tasks := r.Group("/tasks")
	{
		tasks.POST("/:taskId/monitoring/start", h.StartTaskMonitoring)
		tasks.POST("/:taskId/monitoring/manual/stop", h.StopTaskMonitoring)
		tasks.GET("/:taskId/monitoring/status", h.GetTaskStatus)
	}

	// 任务告警查询
	tasks.GET("/:taskId/alerts", h.GetTaskAlerts)
	tasks.GET("/:taskId/alerts/statistics", h.GetTaskAlertStatistics)

	// 系统状态查询
	system := r.Group("/system")
	{
		system.GET("/task-collaboration/status", h.GetSystemStatus)
		system.GET("/task-collaboration/collections/:sessionId", h.GetCollectionStatus)
	}
}

// ReceiveTaskSignal 接收任务信号
// @Summary 接收任务信号
// @Description 接收来自网络自动化控制系统的任务信号（开始、结束、中止），用于启动、结束或中止任务监控会话，注意与 /api/v1/tasks/{taskId}/monitoring/start或stop 的手动操作的区别

// @Tags 任务协同监控
// @Accept json
// @Produce json
// @Param signal body TaskSignal true "任务信号" example({"signal_type":"start","task_id":"550e8400-e29b-41d4-a716-446655440000","timestamp":"2025-01-11T10:15:30Z","task_type":"CONFIG_DEPLOY","target_devices":["211","210"],"expected_duration":30,"port_name":"GigabitEthernet0/0/1","port_id":"12345"})
// @Success 200 {object} models.Response{data=TaskSignalResponse} "信号处理成功" example({"code":200,"data":{"success":true,"sessionId":"session-12345","message":"任务监控会话已创建","taskId":"550e8400-e29b-41d4-a716-446655440000"}})
// @Failure 400 {object} models.Response{data=models.ErrorResponse} "请求参数错误" example({"code":400,"data":{"error":"请求参数格式错误","request_id":"789fe92e-6e44-4df4-8734-0bbcc59a2689","details":"invalid signal_type: must be one of [start end abort]"}})
// @Failure 500 {object} models.Response{data=models.ErrorResponse} "服务器内部错误" example({"code":500,"data":{"error":"处理任务信号失败","request_id":"789fe92e-6e44-4df4-8734-0bbcc59a2689","details":"database connection error"}})
// @Router /api/v1/tasks/signals [post]
func (h *Handler) ReceiveTaskSignal(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	h.logger.Info("接收任务信号请求", zap.String("request_id", requestID))

	var signal TaskSignal
	if err := c.ShouldBindJSON(&signal); err != nil {
		h.logger.Error("任务信号参数绑定失败",
			zap.String("request_id", requestID),
			zap.Error(err))
		c.JSON(http.StatusBadRequest, models.Response{
			Code: http.StatusBadRequest,
			Data: models.ErrorResponse{
				Error:     "请求参数格式错误",
				RequestID: requestID,
				Details:   err.Error(),
			},
		})
		return
	}

	// 处理信号
	response, err := h.service.ProcessTaskSignal(&signal)
	if err != nil {
		h.logger.Error("处理任务信号失败",
			zap.String("request_id", requestID),
			zap.String("task_id", signal.TaskID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.Response{
			Code: http.StatusInternalServerError,
			Data: models.ErrorResponse{
				Error:     "处理任务信号失败",
				RequestID: requestID,
				Details:   err.Error(),
			},
		})
		return
	}

	h.logger.Info("任务信号处理完成",
		zap.String("request_id", requestID),
		zap.String("task_id", signal.TaskID),
		zap.Bool("success", response.Success))

	c.JSON(http.StatusOK, models.Response{
		Code: http.StatusOK,
		Data: response,
	})
}

// StartTaskMonitoring 启动任务监控
// @Summary 启动任务监控
// @Description 手动启动指定任务的监控会话，开始收集相关设备的性能指标和日志数据
// @Tags 任务协同监控
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID" example("550e8400-e29b-41d4-a716-446655440000")
// @Param request body TaskMonitoringRequest true "监控请求" example({"deviceIds":["CE1","CE2","************"]})
// @Success 200 {object} models.Response{data=TaskMonitoringResponse} "监控启动成功" example({"code":200,"data":{"success":true,"message":"任务监控启动成功","taskId":"550e8400-e29b-41d4-a716-446655440000","sessionId":"session-12345","deviceCount":3,"startTime":"2025-01-11T10:15:30Z"}})
// @Failure 400 {object} models.Response{data=models.ErrorResponse} "请求参数错误" example({"code":400,"data":{"error":"请求参数格式错误","request_id":"789fe92e-6e44-4df4-8734-0bbcc59a2689","details":"deviceIds cannot be empty"}})
// @Failure 500 {object} models.Response{data=models.ErrorResponse} "服务器内部错误" example({"code":500,"data":{"error":"启动任务监控失败","request_id":"789fe92e-6e44-4df4-8734-0bbcc59a2689","details":"failed to create monitoring session"}})
// @Router /api/v1/tasks/{taskId}/monitoring/start [post]
func (h *Handler) StartTaskMonitoring(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	taskID := c.Param("taskId")

	h.logger.Info("启动任务监控请求",
		zap.String("request_id", requestID),
		zap.String("task_id", taskID))

	var request TaskMonitoringRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error("监控请求参数绑定失败",
			zap.String("request_id", requestID),
			zap.String("task_id", taskID),
			zap.Error(err))
		c.JSON(http.StatusBadRequest, models.Response{
			Code: http.StatusBadRequest,
			Data: models.ErrorResponse{
				Error:     "请求参数格式错误",
				RequestID: requestID,
				Details:   err.Error(),
			},
		})
		return
	}

	// 启动监控
	response, err := h.service.StartTaskMonitoring(taskID, &request)
	if err != nil {
		h.logger.Error("启动任务监控失败",
			zap.String("request_id", requestID),
			zap.String("task_id", taskID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.Response{
			Code: http.StatusInternalServerError,
			Data: models.ErrorResponse{
				Error:     "启动任务监控失败",
				RequestID: requestID,
				Details:   err.Error(),
			},
		})
		return
	}

	h.logger.Info("任务监控启动成功",
		zap.String("request_id", requestID),
		zap.String("task_id", taskID),
		zap.String("session_id", response.SessionID))

	c.JSON(http.StatusOK, models.Response{
		Code: http.StatusOK,
		Data: response,
	})
}

// StopTaskMonitoring 停止任务监控
// @Summary 停止任务监控
// @Description 手动停止指定任务的监控会话
// @Tags 任务协同监控
// @Produce json
// @Param taskId path string true "任务ID"
// @Success 200 {object} models.Response{data=TaskMonitoringResponse} "监控停止成功"
// @Failure 404 {object} models.Response{data=models.ErrorResponse} "任务不存在"
// @Failure 500 {object} models.Response{data=models.ErrorResponse} "服务器内部错误"
// @Router /api/v1/tasks/{taskId}/monitoring/manual/stop [post]
func (h *Handler) StopTaskMonitoring(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	taskID := c.Param("taskId")

	h.logger.Info("停止任务监控请求",
		zap.String("request_id", requestID),
		zap.String("task_id", taskID))

	// 停止监控
	response, err := h.service.StopTaskMonitoring(taskID)
	if err != nil {
		h.logger.Error("停止任务监控失败",
			zap.String("request_id", requestID),
			zap.String("task_id", taskID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.Response{
			Code: http.StatusInternalServerError,
			Data: models.ErrorResponse{
				Error:     "停止任务监控失败",
				RequestID: requestID,
				Details:   err.Error(),
			},
		})
		return
	}

	if !response.Success {
		c.JSON(http.StatusNotFound, models.Response{
			Code: http.StatusNotFound,
			Data: models.ErrorResponse{
				Error:     response.Message,
				RequestID: requestID,
			},
		})
		return
	}

	h.logger.Info("任务监控停止成功",
		zap.String("request_id", requestID),
		zap.String("task_id", taskID),
		zap.String("session_id", response.SessionID))

	c.JSON(http.StatusOK, models.Response{
		Code: http.StatusOK,
		Data: response,
	})
}

// GetTaskStatus 获取任务状态
// @Summary 获取任务状态
// @Description 查询指定任务的监控状态和基本信息
// @Tags 任务协同监控
// @Produce json
// @Param taskId path string true "任务ID"
// @Success 200 {object} models.Response{data=TaskStatusResponse} "查询成功"
// @Failure 500 {object} models.Response{data=models.ErrorResponse} "服务器内部错误"
// @Router /api/v1/tasks/{taskId}/monitoring/status [get]
func (h *Handler) GetTaskStatus(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	taskID := c.Param("taskId")

	h.logger.Debug("获取任务状态请求",
		zap.String("request_id", requestID),
		zap.String("task_id", taskID))

	// 获取任务状态
	response, err := h.service.GetTaskStatus(taskID)
	if err != nil {
		h.logger.Error("获取任务状态失败",
			zap.String("request_id", requestID),
			zap.String("task_id", taskID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.Response{
			Code: http.StatusInternalServerError,
			Data: models.ErrorResponse{
				Error:     "获取任务状态失败",
				RequestID: requestID,
				Details:   err.Error(),
			},
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code: http.StatusOK,
		Data: response,
	})
}

// GetTaskAlerts 获取任务相关告警
// @Summary 获取任务相关告警
// @Description 查询指定任务执行期间相关的告警列表，包括任务执行期间和延展监测期的告警
// @Tags 任务协同监控
// @Produce json
// @Param taskId path string true "任务ID" example("550e8400-e29b-41d4-a716-446655440000")
// @Param start_time query string false "开始时间（RFC3339格式）" example("2025-01-11T10:15:30Z")
// @Param end_time query string false "结束时间（RFC3339格式）" example("2025-01-11T11:45:30Z")
// @Param limit query int false "每页数量" default(20) example(20)
// @Param offset query int false "偏移量" default(0) example(0)
// @Success 200 {object} models.Response{data=TaskAlertListResponse} "查询成功" example({"code":200,"data":{"taskId":"550e8400-e29b-41d4-a716-446655440000","alerts":[{"id":"alert-001","name":"Interface Down","level":"critical","status":"firing","deviceId":"CE1","startsAt":"2025-01-11T10:20:00Z","endsAt":null,"taskRelation":"任务执行期间","monitoringPhase":"active_execution"}],"statistics":{"totalCount":5,"levelCount":{"critical":1,"warning":3,"info":1},"phaseCount":{"active_execution":4,"post_completion":1}},"pagination":{"total":5,"limit":20,"offset":0}}})
// @Failure 400 {object} models.Response{data=models.ErrorResponse} "请求参数错误" example({"code":400,"data":{"error":"请求参数格式错误","request_id":"789fe92e-6e44-4df4-8734-0bbcc59a2689","details":"invalid time format"}})
// @Failure 500 {object} models.Response{data=models.ErrorResponse} "服务器内部错误" example({"code":500,"data":{"error":"获取任务告警失败","request_id":"789fe92e-6e44-4df4-8734-0bbcc59a2689","details":"database query error"}})
// @Router /api/v1/tasks/{taskId}/alerts [get]
func (h *Handler) GetTaskAlerts(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	taskID := c.Param("taskId")

	// 解析分页参数
	limit := 20
	offset := 0
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}
	if offsetStr := c.Query("offset"); offsetStr != "" {
		if o, err := strconv.Atoi(offsetStr); err == nil && o >= 0 {
			offset = o
		}
	}

	h.logger.Debug("获取任务告警请求",
		zap.String("request_id", requestID),
		zap.String("task_id", taskID),
		zap.Int("limit", limit),
		zap.Int("offset", offset))

	// 获取告警列表
	response, err := h.service.GetTaskAlerts(taskID, limit, offset)
	if err != nil {
		h.logger.Error("获取任务告警失败",
			zap.String("request_id", requestID),
			zap.String("task_id", taskID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.Response{
			Code: http.StatusInternalServerError,
			Data: models.ErrorResponse{
				Error:     "获取任务告警失败",
				RequestID: requestID,
				Details:   err.Error(),
			},
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code: http.StatusOK,
		Data: response,
	})
}

// GetTaskAlertStatistics 获取任务告警统计
// @Summary 获取任务告警统计
// @Description 查询指定任务执行期间的告警统计信息，包括按级别、状态、阶段和设备的统计分布
// @Tags 任务协同监控
// @Produce json
// @Param taskId path string true "任务ID" example("550e8400-e29b-41d4-a716-446655440000")
// @Param start_time query string false "开始时间（RFC3339格式）" example("2025-01-11T10:15:30Z")
// @Param end_time query string false "结束时间（RFC3339格式）" example("2025-01-11T11:45:30Z")
// @Success 200 {object} models.Response{data=TaskAlertStatistics} "查询成功" example({"code":200,"data":{"taskId":"550e8400-e29b-41d4-a716-446655440000","timeRange":{"startTime":"2025-01-11T10:15:30Z","endTime":"2025-01-11T11:45:30Z"},"totalCount":5,"levelCount":{"critical":1,"warning":3,"info":1},"statusCount":{"firing":2,"resolved":3},"phaseCount":{"active_execution":4,"post_completion":1},"deviceCount":{"CE1":3,"CE2":2},"timeDistribution":[{"timeSlot":"2025-01-11T10:15:00Z","count":2},{"timeSlot":"2025-01-11T10:30:00Z","count":3}]}})
// @Failure 400 {object} models.Response{data=models.ErrorResponse} "请求参数错误" example({"code":400,"data":{"error":"请求参数格式错误","request_id":"789fe92e-6e44-4df4-8734-0bbcc59a2689","details":"invalid time format"}})
// @Failure 500 {object} models.Response{data=models.ErrorResponse} "服务器内部错误" example({"code":500,"data":{"error":"获取任务告警统计失败","request_id":"789fe92e-6e44-4df4-8734-0bbcc59a2689","details":"database query error"}})
// @Router /api/v1/tasks/{taskId}/alerts/statistics [get]
func (h *Handler) GetTaskAlertStatistics(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	taskID := c.Param("taskId")

	// 解析时间参数
	var startTime, endTime *time.Time
	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if t, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			startTime = &t
		} else {
			h.logger.Warn("开始时间格式错误",
				zap.String("request_id", requestID),
				zap.String("start_time", startTimeStr),
				zap.Error(err))
		}
	}
	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if t, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			endTime = &t
		} else {
			h.logger.Warn("结束时间格式错误",
				zap.String("request_id", requestID),
				zap.String("end_time", endTimeStr),
				zap.Error(err))
		}
	}

	h.logger.Debug("获取任务告警统计请求",
		zap.String("request_id", requestID),
		zap.String("task_id", taskID))

	// 获取告警统计
	response, err := h.service.GetTaskAlertStatistics(taskID, startTime, endTime)
	if err != nil {
		h.logger.Error("获取任务告警统计失败",
			zap.String("request_id", requestID),
			zap.String("task_id", taskID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.Response{
			Code: http.StatusInternalServerError,
			Data: models.ErrorResponse{
				Error:     "获取任务告警统计失败",
				RequestID: requestID,
				Details:   err.Error(),
			},
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code: http.StatusOK,
		Data: response,
	})
}

// GetSystemStatus 获取系统状态
// @Summary 获取系统状态
// @Description 查询任务协同监控系统的运行状态
// @Tags 系统管理
// @Produce json
// @Success 200 {object} models.Response "查询成功"
// @Router /api/v1/system/task-collaboration/status [get]
func (h *Handler) GetSystemStatus(c *gin.Context) {
	requestID := middleware.GetRequestID(c)

	h.logger.Debug("获取系统状态请求", zap.String("request_id", requestID))

	// 获取各组件状态
	status := map[string]interface{}{
		"signal_processor": h.signalProcessor.GetStatus(),
		"data_collector":   h.dataCollector.GetStatus(),
		"timestamp":        time.Now().Format(time.RFC3339),
		"version":          "1.0.0",
	}

	c.JSON(http.StatusOK, models.Response{
		Code: http.StatusOK,
		Data: status,
	})
}

// GetCollectionStatus 获取数据收集状态
// @Summary 获取数据收集状态
// @Description 查询指定会话的数据收集状态详情
// @Tags 系统管理
// @Produce json
// @Param sessionId path string true "会话ID"
// @Success 200 {object} models.Response{data=map[string]interface{}} "查询成功"
// @Failure 404 {object} models.Response{data=models.ErrorResponse} "会话不存在"
// @Failure 500 {object} models.Response{data=models.ErrorResponse} "服务器内部错误"
// @Router /api/v1/system/task-collaboration/collections/{sessionId} [get]
func (h *Handler) GetCollectionStatus(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	sessionID := c.Param("sessionId")

	h.logger.Debug("获取数据收集状态请求",
		zap.String("request_id", requestID),
		zap.String("session_id", sessionID))

	// 获取收集状态
	status, err := h.dataCollector.GetCollectionStatus(sessionID)
	if err != nil {
		h.logger.Error("获取数据收集状态失败",
			zap.String("request_id", requestID),
			zap.String("session_id", sessionID),
			zap.Error(err))
		c.JSON(http.StatusNotFound, models.Response{
			Code: http.StatusNotFound,
			Data: models.ErrorResponse{
				Error:     "会话不存在",
				RequestID: requestID,
				Details:   err.Error(),
			},
		})
		return
	}

	// 获取数据库中的汇总信息
	summary, err := h.service.GetCollectionSummary(sessionID)
	if err != nil {
		h.logger.Error("获取数据收集汇总失败",
			zap.String("request_id", requestID),
			zap.String("session_id", sessionID),
			zap.Error(err))
	}

	response := map[string]interface{}{
		"context": status,
		"summary": summary,
	}

	c.JSON(http.StatusOK, models.Response{
		Code: http.StatusOK,
		Data: response,
	})
}
