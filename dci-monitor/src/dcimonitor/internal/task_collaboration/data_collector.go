package task_collaboration

import (
	"context"
	"dcimonitor/internal/utils/timeutil"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// DataCollector 数据收集器
type DataCollector struct {
	dao             *DAO
	logger          *zap.Logger
	collections     map[string]*CollectionContext // sessionID -> CollectionContext
	mu              sync.RWMutex
	collectInterval time.Duration
	isRunning       bool
	ctx             context.Context
	cancel          context.CancelFunc
	wg              sync.WaitGroup
}

// CollectionContext 收集上下文
type CollectionContext struct {
	SessionID   string                         `json:"sessionId"`
	TaskID      string                         `json:"taskId"`
	DeviceIDs   []string                       `json:"deviceIds"`
	StartTime   time.Time                      `json:"startTime"`
	Status      string                         `json:"status"` // active, stopped, failed
	Collections map[string]*TaskDataCollection `json:"collections"`
	mu          sync.RWMutex
}

// DataCollectorConfig 数据收集器配置
type DataCollectorConfig struct {
	CollectInterval          string `yaml:"collect_interval"`
	MetricsEnabled           bool   `yaml:"metrics_enabled"`
	LogsEnabled              bool   `yaml:"logs_enabled"`
	AlertsEnabled            bool   `yaml:"alerts_enabled"`
	EventsEnabled            bool   `yaml:"events_enabled"`
	MaxConcurrentCollections int    `yaml:"max_concurrent_collections"`
	PrometheusEndpoint       string `yaml:"prometheus_endpoint"`
	ElasticsearchEndpoint    string `yaml:"elasticsearch_endpoint"`
}

// CollectionResult 收集结果
type CollectionResult struct {
	SessionID    string      `json:"sessionId"`
	DeviceID     string      `json:"deviceId"`
	DataType     string      `json:"dataType"`
	DataCount    int         `json:"dataCount"`
	Success      bool        `json:"success"`
	ErrorMessage string      `json:"errorMessage,omitempty"`
	StartTime    time.Time   `json:"startTime"`
	EndTime      time.Time   `json:"endTime"`
	Data         interface{} `json:"data,omitempty"`
}

// NewDataCollector 创建新的数据收集器实例
func NewDataCollector(dao *DAO, config *DataCollectorConfig, logger *zap.Logger) (*DataCollector, error) {
	// 解析收集间隔
	collectInterval := 30 * time.Second // 默认30秒
	if config.CollectInterval != "" {
		if duration, err := time.ParseDuration(config.CollectInterval); err == nil {
			collectInterval = duration
		}
	}

	ctx, cancel := context.WithCancel(context.Background())

	collector := &DataCollector{
		dao:             dao,
		logger:          logger,
		collections:     make(map[string]*CollectionContext),
		collectInterval: collectInterval,
		isRunning:       false,
		ctx:             ctx,
		cancel:          cancel,
	}

	logger.Info("数据收集器创建成功",
		zap.Duration("collect_interval", collectInterval),
		zap.Bool("metrics_enabled", config.MetricsEnabled),
		zap.Bool("logs_enabled", config.LogsEnabled),
		zap.Bool("alerts_enabled", config.AlertsEnabled),
		zap.Bool("events_enabled", config.EventsEnabled))

	return collector, nil
}

// Start 启动数据收集器
func (dc *DataCollector) Start() error {
	dc.mu.Lock()
	defer dc.mu.Unlock()

	if dc.isRunning {
		return fmt.Errorf("数据收集器已在运行中")
	}

	dc.logger.Info("启动数据收集器",
		zap.Duration("collect_interval", dc.collectInterval))

	dc.isRunning = true

	// 启动收集循环
	dc.wg.Add(1)
	go dc.collectLoop()

	return nil
}

// Stop 停止数据收集器
func (dc *DataCollector) Stop() error {
	dc.mu.Lock()
	defer dc.mu.Unlock()

	if !dc.isRunning {
		return nil
	}

	dc.logger.Info("停止数据收集器")

	// 取消上下文
	dc.cancel()

	// 等待所有协程结束
	dc.wg.Wait()

	dc.isRunning = false

	dc.logger.Info("数据收集器已停止")
	return nil
}

// StartCollection 开始收集指定会话的数据
func (dc *DataCollector) StartCollection(sessionID, taskID string, deviceIDs []string) error {
	dc.mu.Lock()
	defer dc.mu.Unlock()

	// 检查是否已存在该会话的收集任务
	if _, exists := dc.collections[sessionID]; exists {
		return fmt.Errorf("会话 %s 的数据收集任务已存在", sessionID)
	}

	// 创建收集上下文
	context := &CollectionContext{
		SessionID:   sessionID,
		TaskID:      taskID,
		DeviceIDs:   deviceIDs,
		StartTime:   timeutil.NowInLocalTime(),
		Status:      "active",
		Collections: make(map[string]*TaskDataCollection),
	}

	dc.collections[sessionID] = context

	dc.logger.Info("开始数据收集",
		zap.String("session_id", sessionID),
		zap.String("task_id", taskID),
		zap.Int("device_count", len(deviceIDs)))

	// 为每个设备和数据类型创建收集记录
	dataTypes := []string{"metrics", "logs", "alerts", "events"}
	for _, deviceID := range deviceIDs {
		for _, dataType := range dataTypes {
			collection := &TaskDataCollection{
				SessionID:       sessionID,
				DataType:        dataType,
				DeviceID:        deviceID,
				CollectionStart: timeutil.NowInLocalTime(),
				Status:          "collecting",
				DataCount:       0,
			}

			err := dc.dao.CreateTaskDataCollection(collection)
			if err != nil {
				dc.logger.Error("创建数据收集记录失败",
					zap.String("session_id", sessionID),
					zap.String("device_id", deviceID),
					zap.String("data_type", dataType),
					zap.Error(err))
				continue
			}

			// 保存到上下文
			context.mu.Lock()
			context.Collections[collection.ID] = collection
			context.mu.Unlock()
		}
	}

	return nil
}

// StopCollection 停止指定会话的数据收集
func (dc *DataCollector) StopCollection(sessionID string) error {
	dc.mu.Lock()
	defer dc.mu.Unlock()

	context, exists := dc.collections[sessionID]
	if !exists {
		return fmt.Errorf("会话 %s 的数据收集任务不存在", sessionID)
	}

	dc.logger.Info("停止数据收集",
		zap.String("session_id", sessionID),
		zap.String("task_id", context.TaskID))

	// 更新状态
	context.mu.Lock()
	context.Status = "stopped"
	context.mu.Unlock()

	// 更新数据库中的收集记录
	now := timeutil.NowInLocalTime()
	for _, collection := range context.Collections {
		if collection.Status == "collecting" {
			collection.CollectionEnd = &now
			collection.Status = "completed"

			err := dc.dao.UpdateTaskDataCollection(collection)
			if err != nil {
				dc.logger.Error("更新数据收集记录失败",
					zap.String("session_id", sessionID),
					zap.String("collection_id", collection.ID),
					zap.Error(err))
			}
		}
	}

	// 从内存中移除
	delete(dc.collections, sessionID)

	return nil
}

// collectLoop 数据收集循环
func (dc *DataCollector) collectLoop() {
	defer dc.wg.Done()

	ticker := time.NewTicker(dc.collectInterval)
	defer ticker.Stop()

	for {
		select {
		case <-dc.ctx.Done():
			dc.logger.Info("数据收集循环收到停止信号")
			return
		case <-ticker.C:
			dc.performCollection()
		}
	}
}

// performCollection 执行一次数据收集
func (dc *DataCollector) performCollection() {
	dc.mu.RLock()
	activeSessions := make(map[string]*CollectionContext)
	for sessionID, context := range dc.collections {
		if context.Status == "active" {
			activeSessions[sessionID] = context
		}
	}
	dc.mu.RUnlock()

	if len(activeSessions) == 0 {
		return
	}

	dc.logger.Debug("执行数据收集",
		zap.Int("active_sessions", len(activeSessions)))

	// 并发收集各个会话的数据
	var wg sync.WaitGroup
	for sessionID, context := range activeSessions {
		wg.Add(1)
		go func(sID string, ctx *CollectionContext) {
			defer wg.Done()
			dc.collectSessionData(sID, ctx)
		}(sessionID, context)
	}

	wg.Wait()
}

// collectSessionData 收集指定会话的数据
func (dc *DataCollector) collectSessionData(sessionID string, collectionCtx *CollectionContext) {
	collectionCtx.mu.RLock()
	deviceIDs := collectionCtx.DeviceIDs
	collectionCtx.mu.RUnlock()

	requestID := fmt.Sprintf("collect-%s-%d", sessionID, time.Now().Unix())

	dc.logger.Debug("收集会话数据",
		zap.String("request_id", requestID),
		zap.String("session_id", sessionID),
		zap.String("task_id", collectionCtx.TaskID),
		zap.Int("device_count", len(deviceIDs)))

	// 为每个设备收集各类型数据
	for _, deviceID := range deviceIDs {
		// 收集性能指标
		go dc.collectMetrics(requestID, sessionID, deviceID)

		// 收集日志数据
		go dc.collectLogs(requestID, sessionID, deviceID)

		// 收集告警数据
		go dc.collectAlerts(requestID, sessionID, deviceID)

		// 收集事件数据
		go dc.collectEvents(requestID, sessionID, deviceID)
	}
}

// collectMetrics 收集性能指标数据
func (dc *DataCollector) collectMetrics(requestID, sessionID, deviceID string) {
	start := time.Now()

	dc.logger.Debug("收集性能指标",
		zap.String("request_id", requestID),
		zap.String("session_id", sessionID),
		zap.String("device_id", deviceID))

	// TODO: 实际的Prometheus查询逻辑
	// 这里应该调用Prometheus API获取设备的性能指标
	// 例如：CPU使用率、内存使用率、网络流量等

	// 模拟数据收集
	dataCount := dc.simulateMetricsCollection(deviceID)

	result := &CollectionResult{
		SessionID: sessionID,
		DeviceID:  deviceID,
		DataType:  "metrics",
		DataCount: dataCount,
		Success:   true,
		StartTime: start,
		EndTime:   time.Now(),
	}

	dc.updateCollectionResult(requestID, result)
}

// collectLogs 收集日志数据
func (dc *DataCollector) collectLogs(requestID, sessionID, deviceID string) {
	start := time.Now()

	dc.logger.Debug("收集日志数据",
		zap.String("request_id", requestID),
		zap.String("session_id", sessionID),
		zap.String("device_id", deviceID))

	// TODO: 实际的Elasticsearch查询逻辑
	// 这里应该调用Elasticsearch API获取设备的日志数据

	// 模拟数据收集
	dataCount := dc.simulateLogsCollection(deviceID)

	result := &CollectionResult{
		SessionID: sessionID,
		DeviceID:  deviceID,
		DataType:  "logs",
		DataCount: dataCount,
		Success:   true,
		StartTime: start,
		EndTime:   time.Now(),
	}

	dc.updateCollectionResult(requestID, result)
}

// collectAlerts 收集告警数据
func (dc *DataCollector) collectAlerts(requestID, sessionID, deviceID string) {
	start := time.Now()

	dc.logger.Debug("收集告警数据",
		zap.String("request_id", requestID),
		zap.String("session_id", sessionID),
		zap.String("device_id", deviceID))

	// TODO: 实际的告警查询逻辑
	// 这里应该查询monitor_alert表获取设备相关的告警

	// 模拟数据收集
	dataCount := dc.simulateAlertsCollection(deviceID)

	result := &CollectionResult{
		SessionID: sessionID,
		DeviceID:  deviceID,
		DataType:  "alerts",
		DataCount: dataCount,
		Success:   true,
		StartTime: start,
		EndTime:   time.Now(),
	}

	dc.updateCollectionResult(requestID, result)
}

// collectEvents 收集事件数据
func (dc *DataCollector) collectEvents(requestID, sessionID, deviceID string) {
	start := time.Now()

	dc.logger.Debug("收集事件数据",
		zap.String("request_id", requestID),
		zap.String("session_id", sessionID),
		zap.String("device_id", deviceID))

	// TODO: 实际的事件查询逻辑
	// 这里应该查询相关的事件表获取设备事件数据

	// 模拟数据收集
	dataCount := dc.simulateEventsCollection(deviceID)

	result := &CollectionResult{
		SessionID: sessionID,
		DeviceID:  deviceID,
		DataType:  "events",
		DataCount: dataCount,
		Success:   true,
		StartTime: start,
		EndTime:   time.Now(),
	}

	dc.updateCollectionResult(requestID, result)
}

// updateCollectionResult 更新收集结果
func (dc *DataCollector) updateCollectionResult(requestID string, result *CollectionResult) {
	// 查找对应的数据收集记录并更新
	collections, err := dc.dao.GetTaskDataCollectionsBySessionID(result.SessionID)
	if err != nil {
		dc.logger.Error("获取数据收集记录失败",
			zap.String("request_id", requestID),
			zap.String("session_id", result.SessionID),
			zap.Error(err))
		return
	}

	// 找到匹配的收集记录
	for _, collection := range collections {
		if collection.DeviceID == result.DeviceID &&
			collection.DataType == result.DataType &&
			collection.Status == "collecting" {

			// 更新数据计数
			collection.DataCount += result.DataCount

			err := dc.dao.UpdateTaskDataCollection(collection)
			if err != nil {
				dc.logger.Error("更新数据收集记录失败",
					zap.String("request_id", requestID),
					zap.String("collection_id", collection.ID),
					zap.Error(err))
			} else {
				dc.logger.Debug("更新数据收集记录成功",
					zap.String("request_id", requestID),
					zap.String("session_id", result.SessionID),
					zap.String("device_id", result.DeviceID),
					zap.String("data_type", result.DataType),
					zap.Int("data_count", result.DataCount))
			}
			break
		}
	}
}

// 模拟数据收集方法（实际实现时需要替换）

func (dc *DataCollector) simulateMetricsCollection(deviceID string) int {
	// 模拟收集到的性能指标数量
	return 10 + len(deviceID)%20 // 简单的模拟逻辑
}

func (dc *DataCollector) simulateLogsCollection(deviceID string) int {
	// 模拟收集到的日志数量
	return 5 + len(deviceID)%15
}

func (dc *DataCollector) simulateAlertsCollection(deviceID string) int {
	// 模拟收集到的告警数量
	return len(deviceID) % 5
}

func (dc *DataCollector) simulateEventsCollection(deviceID string) int {
	// 模拟收集到的事件数量
	return 2 + len(deviceID)%8
}

// GetStatus 获取数据收集器状态
func (dc *DataCollector) GetStatus() map[string]interface{} {
	dc.mu.RLock()
	defer dc.mu.RUnlock()

	activeSessions := 0
	for _, context := range dc.collections {
		if context.Status == "active" {
			activeSessions++
		}
	}

	return map[string]interface{}{
		"is_running":       dc.isRunning,
		"collect_interval": dc.collectInterval.String(),
		"total_sessions":   len(dc.collections),
		"active_sessions":  activeSessions,
	}
}

// GetCollectionStatus 获取指定会话的收集状态
func (dc *DataCollector) GetCollectionStatus(sessionID string) (*CollectionContext, error) {
	dc.mu.RLock()
	defer dc.mu.RUnlock()

	context, exists := dc.collections[sessionID]
	if !exists {
		return nil, fmt.Errorf("会话 %s 的数据收集任务不存在", sessionID)
	}

	return context, nil
}
