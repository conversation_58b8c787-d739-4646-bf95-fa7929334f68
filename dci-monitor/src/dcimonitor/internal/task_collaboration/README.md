# 任务协同监控模块 (Task Collaboration)

## 概述

任务协同监控模块是DCI监控系统的核心功能模块，实现网络自动化任务与监控系统的协同工作。该模块负责接收来自网络自动化控制系统的任务信号，创建任务监控会话，收集任务执行期间的相关数据，并提供任务相关的告警查询和统计功能。

## 功能特性

### 核心功能
- **任务信号处理**：接收并处理来自网络自动化控制系统的任务开始、结束、中止信号
- **任务监控会话管理**：创建、管理和停止任务监控会话
- **数据收集协调**：启动和停止任务相关的数据收集（性能指标、日志、告警、事件）
- **告警关联管理**：自动关联任务期间产生的告警，支持延展监测
- **统计查询服务**：提供任务相关告警的统计和查询功能

### 特性亮点
- **延展监测**：任务结束后1小时内的告警仍会关联到任务
- **多维度数据收集**：支持metrics、logs、alerts、events四种数据类型
- **灵活的时间范围**：支持任务期间和自定义时间范围的查询
- **完整的生命周期管理**：从任务开始到结束的全程监控

## 架构设计

### 模块结构
```
internal/task_collaboration/
├── model.go              # 数据模型定义
├── dao.go                # 数据访问层
├── service.go            # 业务逻辑层
├── handler.go            # HTTP请求处理层（待实现）
├── signal_processor.go   # 任务信号处理器（待实现）
├── data_collector.go     # 数据收集器（待实现）
└── README.md             # 模块说明文档
```

### 数据库表结构
```
sql/task_collaboration/
├── task_collaboration.sql # 模块相关表结构
└── README.md              # 数据库说明文档
```

### 依赖关系
- **内部依赖**：
  - `internal/alert` - 告警模块（TaskDAO、DAO）
  - `internal/utils/timeutil` - 时间处理工具
  - `internal/models` - 通用数据模型
  - `internal/middleware` - 请求处理中间件
- **外部依赖**：
  - `database/sql` - 数据库操作
  - `go.uber.org/zap` - 日志记录
  - `github.com/google/uuid` - UUID生成

## 数据模型

### 核心数据结构

#### 任务信号相关
- `TaskSignal`: 任务信号数据结构（start/end/abort）
- `TaskSignalResponse`: 任务信号处理响应
- `TaskMonitoringRequest`: 任务监控请求
- `TaskMonitoringResponse`: 任务监控响应

#### 告警统计相关
- `TaskAlertStatistics`: 任务告警统计信息
- `TaskAlertListResponse`: 任务告警列表响应
- `TaskRelatedAlert`: 任务相关告警信息

#### 数据收集相关
- `TaskDataCollection`: 任务数据收集记录
- `TaskAlertAssociation`: 任务告警关联记录
- `CollectionSummary`: 数据收集汇总

## API接口设计

### 任务信号处理
- `POST /api/v1/tasks/signals` - 接收任务信号

### 任务监控管理
- `POST /api/v1/tasks/{taskId}/monitoring/start` - 启动任务监控
- `POST /api/v1/tasks/{taskId}/monitoring/stop` - 停止任务监控
- `GET /api/v1/tasks/{taskId}/monitoring/status` - 查询任务状态

### 任务告警查询
- `GET /api/v1/tasks/{taskId}/alerts` - 获取任务相关告警列表
- `GET /api/v1/tasks/{taskId}/alerts/statistics` - 获取任务告警统计

## 使用示例

### 服务初始化
```go
// 创建DAO实例
dao := task_collaboration.NewDAO(db, logger)

// 创建Service实例
service := task_collaboration.NewService(dao, taskDAO, alertDAO, logger)
```

### 处理任务信号
```go
signal := &task_collaboration.TaskSignal{
    SignalType:    "start",
    TaskID:        "task-001",
    TaskType:      "configuration",
    TargetDevices: []string{"device1", "device2"},
    Timestamp:     time.Now(),
}

response, err := service.ProcessTaskSignal(signal)
if err != nil {
    // 处理错误
}
```

### 查询任务告警统计
```go
stats, err := service.GetTaskAlertStatistics("task-001", nil, nil)
if err != nil {
    // 处理错误
}

fmt.Printf("任务 %s 总计产生 %d 个告警\n", stats.TaskID, stats.TotalCount)
```

## 配置说明

### 必需配置项
```yaml
task_collaboration:
  # 延展监测持续时间
  extended_monitoring_duration: "1h"
  # 数据收集间隔
  data_collection_interval: "30s"
  # 最大并发任务数
  max_concurrent_tasks: 50
```

### 环境变量支持
- `DCI_TASK_EXTENDED_MONITORING_DURATION` - 延展监测持续时间
- `DCI_TASK_DATA_COLLECTION_INTERVAL` - 数据收集间隔
- `DCI_KAFKA_TASK_SIGNALS_TOPIC` - Kafka任务信号主题

## 数据库设计

### 主要表结构

#### monitor_task_alert_associations
任务告警关联表，记录任务与告警的关联关系：
- 支持`active_execution`（任务执行期间）和`post_completion`（任务完成后延展监测）两种关联类型
- 包含完整的索引设计，支持高效查询

#### monitor_task_data_collections
任务数据收集记录表，记录数据收集状态：
- 支持四种数据类型：metrics、logs、alerts、events
- 跟踪收集状态和数据统计
- 支持按会话和设备进行查询

### 视图设计
- `view_task_alert_summary` - 任务告警汇总视图
- `view_task_collection_summary` - 任务数据收集汇总视图

## 业务流程

### 任务监控流程
1. **接收任务信号** → 验证信号格式和参数
2. **创建监控会话** → 调用alert模块的TaskDAO创建会话
3. **启动数据收集** → 为每个设备和数据类型创建收集记录
4. **监控过程中** → 自动关联产生的告警
5. **结束监控** → 停止数据收集，进入延展监测期
6. **延展监测** → 继续关联1小时内的告警

### 告警关联流程
1. **告警产生** → alert模块检测到新告警
2. **设备ID提取** → 从告警标签中提取设备ID
3. **任务查询** → 查询该设备是否有关联的活跃任务或延展监测
4. **创建关联** → 如有匹配任务，创建关联记录
5. **标记增强** → 在告警annotations中添加任务关联信息

## 性能考虑

### 索引策略
- 为高频查询字段建立索引（task_id、device_id、session_id）
- 使用复合索引优化多字段查询性能
- 时间字段索引支持时间范围查询

### 数据清理
- 定期清理过期的数据收集记录
- 压缩历史告警关联数据
- 监控表大小和查询性能

## 日志记录

### 关键操作日志
- 任务信号处理的完整流程
- 数据收集的启动和停止
- 告警关联的创建过程
- 错误和异常情况的详细记录

### 日志级别
- **INFO**: 正常业务流程记录
- **WARN**: 非关键错误和警告
- **ERROR**: 关键错误和异常
- **DEBUG**: 详细的调试信息

## 开发状态

### 已完成
- ✅ 数据模型定义 (model.go)
- ✅ 数据访问层 (dao.go)
- ✅ 业务逻辑层 (service.go)
- ✅ 数据库表结构 (task_collaboration.sql)

### 开发中
- 🔄 HTTP请求处理层 (handler.go)
- 🔄 任务信号处理器 (signal_processor.go)
- 🔄 数据收集器 (data_collector.go)

### 待实现
- ⏳ Kafka消息处理集成
- ⏳ Prometheus数据收集接口
- ⏳ 单元测试覆盖
- ⏳ API文档和示例

## 注意事项

1. **时区一致性**：所有时间处理必须使用Asia/Shanghai时区
2. **错误处理**：严禁忽略error返回值
3. **日志记录**：关键操作必须记录request_id
4. **性能监控**：定期监控数据库查询性能
5. **数据安全**：严格验证所有输入参数 