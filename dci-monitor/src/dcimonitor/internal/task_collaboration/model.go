package task_collaboration

import (
	"time"
)

// 监控会话状态枚举（用于TaskSession中的status字段）
const (
	SessionStatusActive    = 1 // 监控会话活跃中
	SessionStatusCompleted = 2 // 监控会话已完成
	SessionStatusNotFound  = 0 // 会话未找到
)

// GetTaskStatusDesc 获取任务执行结果状态描述
func GetTaskStatusDesc(status string) string {
	switch status {
	case "SUCCESS":
		return "任务执行成功"
	case "FAILURE":
		return "任务执行失败"
	case "REVOKED":
		return "任务被中止"
	default:
		return "未知状态"
	}
}

// GetSessionStatusDesc 获取监控会话状态描述
func GetSessionStatusDesc(status int) string {
	switch status {
	case SessionStatusActive:
		return "监控会话活跃中"
	case SessionStatusCompleted:
		return "监控会话已完成"
	case SessionStatusNotFound:
		return "会话未找到"
	default:
		return "未知状态"
	}
}

// TaskSignal 任务信号数据结构
type TaskSignal struct {
	SignalType       string    `json:"signal_type" binding:"required,oneof=start end abort"`
	TaskID           string    `json:"task_id" binding:"required"` // 移除UUID验证，允许自定义任务ID格式
	Timestamp        time.Time `json:"timestamp"`
	TaskType         string    `json:"task_type" binding:"required,oneof=CONFIG_DEPLOY CONFIG_RECOVERY CONFIG_DOWNLOAD OTHER"`
	TargetDevices    []string  `json:"target_devices" binding:"required,min=1"`
	ExpectedDuration int       `json:"expected_duration" binding:"min=0"`
	Status           string    `json:"status,omitempty" binding:"omitempty,oneof=SUCCESS FAILURE REVOKED"` // 基于自动化系统Django choices规范
	PortName         string    `json:"port_name,omitempty"`                                                // 端口名称，选填
	PortID           string    `json:"port_id,omitempty"`                                                  // 逻辑端口ID，选填
}

// TaskSignalResponse 任务信号响应
type TaskSignalResponse struct {
	Success   bool   `json:"success"`
	SessionID string `json:"sessionId,omitempty"`
	Message   string `json:"message"`
	TaskID    string `json:"taskId"`
}

// TaskMonitoringRequest 任务监控请求
type TaskMonitoringRequest struct {
	DeviceIDs []string `json:"deviceIds" binding:"required,min=1"`
}

// TaskMonitoringResponse 任务监控响应
type TaskMonitoringResponse struct {
	Success     bool      `json:"success"`
	Message     string    `json:"message"`
	TaskID      string    `json:"taskId"`
	SessionID   string    `json:"sessionId,omitempty"`
	DeviceCount int       `json:"deviceCount,omitempty"`
	StartTime   time.Time `json:"startTime,omitempty"`
	EndTime     time.Time `json:"endTime,omitempty"`
	Duration    string    `json:"duration,omitempty"`
}

// TaskStatusResponse 任务状态响应
type TaskStatusResponse struct {
	TaskID      string     `json:"taskId"`
	Status      string     `json:"status"` // 使用自动化系统标准枚举值: SUCCESS, FAILURE, REVOKED
	SessionID   string     `json:"sessionId"`
	StartTime   time.Time  `json:"startTime"`
	EndTime     *time.Time `json:"endTime,omitempty"`
	DeviceCount int        `json:"deviceCount"`
	IsActive    bool       `json:"isActive"`
}

// TaskAlertStatistics 任务告警统计
type TaskAlertStatistics struct {
	TaskID           string          `json:"taskId"`
	TimeRange        TimeRange       `json:"timeRange"`
	TotalCount       int             `json:"totalCount"`
	LevelCount       map[string]int  `json:"levelCount"`
	StatusCount      map[string]int  `json:"statusCount"`
	PhaseCount       map[string]int  `json:"phaseCount"`
	DeviceCount      map[string]int  `json:"deviceCount"`
	TimeDistribution []TimeSlotCount `json:"timeDistribution"`
}

// TaskAlertListResponse 任务告警列表响应
type TaskAlertListResponse struct {
	TaskID     string               `json:"taskId"`
	Alerts     []*TaskRelatedAlert  `json:"alerts"`
	Statistics *TaskAlertStatistics `json:"statistics"`
	Pagination *PaginationInfo      `json:"pagination"`
}

// TaskRelatedAlert 任务相关告警
type TaskRelatedAlert struct {
	ID              string     `json:"id"`
	Name            string     `json:"name"`
	Level           string     `json:"level"`
	Status          string     `json:"status"`
	DeviceID        string     `json:"deviceId"`
	StartsAt        time.Time  `json:"startsAt"`
	EndsAt          *time.Time `json:"endsAt,omitempty"`
	TaskRelation    string     `json:"taskRelation"`
	MonitoringPhase string     `json:"monitoringPhase"`
}

// TimeRange 时间范围
type TimeRange struct {
	StartTime time.Time `json:"startTime"`
	EndTime   time.Time `json:"endTime"`
}

// TimeSlotCount 时间段计数
type TimeSlotCount struct {
	TimeSlot string `json:"timeSlot"`
	Count    int    `json:"count"`
}

// PaginationInfo 分页信息
type PaginationInfo struct {
	Total    int `json:"total"`
	Limit    int `json:"limit"`
	Offset   int `json:"offset"`
	Page     int `json:"page"`
	PageSize int `json:"pageSize"`
}

// TaskDataCollection 任务数据收集记录
type TaskDataCollection struct {
	ID              string     `json:"id" db:"id"`
	SessionID       string     `json:"sessionId" db:"session_id"`
	DataType        string     `json:"dataType" db:"data_type"`
	DeviceID        string     `json:"deviceId" db:"device_id"`
	CollectionStart time.Time  `json:"collectionStart" db:"collection_start"`
	CollectionEnd   *time.Time `json:"collectionEnd,omitempty" db:"collection_end"`
	DataCount       int        `json:"dataCount" db:"data_count"`
	Status          string     `json:"status" db:"status"`
	ErrorMessage    string     `json:"errorMessage,omitempty" db:"error_message"`
	CreatedAt       time.Time  `json:"createdAt" db:"created_at"`
}

// TableName 返回TaskDataCollection对应的数据库表名
func (TaskDataCollection) TableName() string {
	return "monitor_task_data_collections"
}

// TaskAlertAssociation 任务告警关联记录
type TaskAlertAssociation struct {
	ID              string    `json:"id" db:"id"`
	TaskID          string    `json:"taskId" db:"task_id"`
	AlertID         string    `json:"alertId" db:"alert_id"`
	AssociationType string    `json:"associationType" db:"association_type"`
	DeviceID        string    `json:"deviceId" db:"device_id"`
	CreatedAt       time.Time `json:"createdAt" db:"created_at"`
}

// TableName 返回TaskAlertAssociation对应的数据库表名
func (TaskAlertAssociation) TableName() string {
	return "monitor_task_alert_associations"
}

// MetricData 性能指标数据
type MetricData struct {
	DeviceID   string            `json:"deviceId"`
	MetricName string            `json:"metricName"`
	Value      float64           `json:"value"`
	Timestamp  time.Time         `json:"timestamp"`
	Labels     map[string]string `json:"labels,omitempty"`
}

// LogData 日志数据
type LogData struct {
	DeviceID  string                 `json:"deviceId"`
	Level     string                 `json:"level"`
	Message   string                 `json:"message"`
	Timestamp time.Time              `json:"timestamp"`
	Fields    map[string]interface{} `json:"fields,omitempty"`
}

// CollectionSummary 数据收集汇总
type CollectionSummary struct {
	SessionID    string `json:"sessionId"`
	TaskID       string `json:"taskId"`
	TotalMetrics int    `json:"totalMetrics"`
	TotalLogs    int    `json:"totalLogs"`
	TotalAlerts  int    `json:"totalAlerts"`
	TotalEvents  int    `json:"totalEvents"`
}
