package metrics_monitoring

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Handler 指标监测API处理器
type Handler struct {
	service *Service
	logger  *zap.Logger
}

// NewHandler 创建API处理器实例
func NewHandler(service *Service, logger *zap.Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	// 指标数据记录管理接口
	router.GET("/tasks/:taskId/metrics-monitoring/status", h.GetMetricsStatus)
	router.POST("/tasks/:taskId/metrics-monitoring/manual-start", h.ManualStartMetrics)
	router.POST("/tasks/:taskId/metrics-monitoring/manual-stop", h.ManualStopMetrics)

	// 指标基线查询接口
	router.GET("/tasks/:taskId/metrics-baseline", h.GetMetricsBaseline)
	router.GET("/tasks/:taskId/metrics-comparison", h.GetMetricsComparison)

	// 指标数据查询接口
	router.GET("/tasks/:taskId/metrics-data", h.GetMetricsData)
	router.GET("/tasks/:taskId/metrics-trends", h.GetMetricsTrends)
	router.GET("/tasks/:taskId/metrics-statistics", h.GetMetricsStatistics)

	// 数据管理接口
	router.DELETE("/tasks/:taskId/metrics-monitoring/cascade", h.DeleteMetricsDataCascade)
	router.GET("/tasks/:taskId/metrics-monitoring/dependencies", h.GetMetricsDependencies)
	router.DELETE("/tasks/:taskId/metrics-data/cleanup", h.CleanupMetricsData)
}

// GetMetricsStatus 查询指标数据记录状态
// @Summary 查询指标数据记录状态
// @Description 查询任务指标数据记录会话的当前状态，指标数据记录会话由任务信号自动创建
// @Tags 指标数据记录管理
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID" example("task_20250118_001")
// @Success 200 {object} Response{data=MetricsStatusResponse} "成功"
// @Failure 400 {object} Response{data=ErrorData} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorData} "任务指标数据记录会话不存在"
// @Failure 500 {object} Response{data=ErrorData} "服务器内部错误"
// @Router /api/v1/tasks/{taskId}/metrics-monitoring/status [get]
func (h *Handler) GetMetricsStatus(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		h.respondError(c, http.StatusBadRequest, "任务ID不能为空")
		return
	}

	status, err := h.service.GetMetricsStatus(c.Request.Context(), taskID)
	if err != nil {
		if err.Error() == "未找到任务的指标监测会话" {
			h.respondError(c, http.StatusNotFound, "任务指标数据记录会话不存在")
			return
		}
		h.logger.Error("查询指标监测状态失败",
			zap.String("task_id", taskID), zap.Error(err))
		h.respondError(c, http.StatusInternalServerError, "查询指标监测状态失败")
		return
	}

	h.respondSuccess(c, status)
}

// ManualStartMetrics 手动启动指标数据记录（调试用）
// @Summary 手动启动指标数据记录（调试用）
// @Description 手动启动任务的指标数据记录。正常流程：指标数据记录由任务开始信号自动触发，无需手动调用。调试用途：用于开发调试、测试验证或自动启动失败时的补充操作
// @Tags 指标数据记录管理
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID" example("task_20250118_001")
// @Param request body MetricsStartRequest true "启动参数"
// @Success 201 {object} Response{data=MetricsStartResponse} "创建成功"
// @Failure 400 {object} Response{data=ErrorData} "请求参数错误"
// @Failure 409 {object} Response{data=ErrorData} "指标数据记录会话已存在"
// @Failure 422 {object} Response{data=ErrorData} "参数验证失败"
// @Failure 500 {object} Response{data=ErrorData} "服务器内部错误"
// @Router /api/v1/tasks/{taskId}/metrics-monitoring/manual-start [post]
func (h *Handler) ManualStartMetrics(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		h.respondError(c, http.StatusBadRequest, "任务ID不能为空")
		return
	}

	var req MetricsStartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.respondError(c, http.StatusBadRequest, "请求参数格式错误: "+err.Error())
		return
	}

	// 验证请求参数
	if len(req.DeviceIds) == 0 {
		h.respondError(c, http.StatusUnprocessableEntity, "设备ID列表不能为空")
		return
	}
	if len(req.MetricsTypes) == 0 {
		h.respondError(c, http.StatusUnprocessableEntity, "指标类型列表不能为空")
		return
	}

	// 假设taskSessionID与taskID相同，实际应该从任务服务查询
	taskSessionID := taskID + "_session"

	status, err := h.service.StartMetricsMonitoring(c.Request.Context(),
		taskID, taskSessionID, req.DeviceIds, req.MetricsTypes)
	if err != nil {
		if err.Error() == "指标数据记录会话已存在" {
			h.respondError(c, http.StatusConflict, "指标数据记录会话已存在")
			return
		}
		h.logger.Error("启动指标监测失败",
			zap.String("task_id", taskID), zap.Error(err))
		h.respondError(c, http.StatusInternalServerError, "启动指标监测失败")
		return
	}

	response := &MetricsStartResponse{
		SessionID: status.SessionID,
		TaskID:    taskID,
		Status:    status.Status,
		Message:   "指标数据记录会话已成功启动",
	}

	c.JSON(http.StatusCreated, gin.H{"code": 201, "data": response})
}

// ManualStopMetrics 手动停止指标数据记录
// @Summary 手动停止指标数据记录
// @Description 手动停止任务的指标数据记录。正常流程：指标数据记录由任务结束信号自动停止并切换到延展数据记录模式。特殊情况：用于紧急停止、异常处理或测试场景
// @Tags 指标数据记录管理
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID" example("task_20250118_001")
// @Success 200 {object} Response{data=MetricsStopResponse} "成功"
// @Failure 400 {object} Response{data=ErrorData} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorData} "未找到指标数据记录会话"
// @Failure 500 {object} Response{data=ErrorData} "服务器内部错误"
// @Router /api/v1/tasks/{taskId}/metrics-monitoring/manual-stop [post]
func (h *Handler) ManualStopMetrics(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		h.respondError(c, http.StatusBadRequest, "任务ID不能为空")
		return
	}

	response, err := h.service.StopMetricsMonitoring(c.Request.Context(), taskID)
	if err != nil {
		if err.Error() == "未找到任务的指标监测会话" {
			h.respondError(c, http.StatusNotFound, "未找到指标数据记录会话")
			return
		}
		h.logger.Error("停止指标监测失败",
			zap.String("task_id", taskID), zap.Error(err))
		h.respondError(c, http.StatusInternalServerError, "停止指标监测失败")
		return
	}

	h.respondSuccess(c, response)
}

// GetMetricsBaseline 查询指标基线统计数据
// @Summary 查询指标基线统计数据
// @Description 查询任务的指标基线统计数据，返回任务执行前30分钟的指标统计特征数据
// @Tags 指标基线查询
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID" example("task_20250118_001")
// @Param device_id query string false "设备ID筛选" example("device_001")
// @Param metric_name query string false "指标名称筛选" example("dci_snmp_status_cpu_usage")
// @Param page query int false "页码" default(1) minimum(1)
// @Param page_size query int false "每页数量" default(20) minimum(1) maximum(100)
// @Success 200 {object} Response{data=MetricsBaselineListResponse} "成功"
// @Failure 400 {object} Response{data=ErrorData} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorData} "未找到指标监测会话"
// @Failure 500 {object} Response{data=ErrorData} "服务器内部错误"
// @Router /api/v1/tasks/{taskId}/metrics-baseline [get]
func (h *Handler) GetMetricsBaseline(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		h.respondError(c, http.StatusBadRequest, "任务ID不能为空")
		return
	}

	deviceID := c.Query("device_id")
	metricName := c.Query("metric_name")
	page, pageSize := h.getPaginationParams(c)

	response, err := h.service.GetMetricsBaseline(c.Request.Context(),
		taskID, deviceID, metricName, page, pageSize)
	if err != nil {
		if err.Error() == "未找到任务的指标监测会话" {
			h.respondError(c, http.StatusNotFound, "未找到指标监测会话")
			return
		}
		h.logger.Error("查询指标基线失败",
			zap.String("task_id", taskID), zap.Error(err))
		h.respondError(c, http.StatusInternalServerError, "查询指标基线失败")
		return
	}

	h.respondSuccess(c, response)
}

// GetMetricsComparison 查询指标对比数据
// @Summary 查询指标对比数据
// @Description 查询任务前后的指标对比数据，返回基线值与实时值的对比数据，供前端展示和人工分析
// @Tags 指标基线查询
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID" example("task_20250118_001")
// @Param device_id query string false "设备ID筛选" example("device_001")
// @Param metric_name query string false "指标名称筛选" example("dci_snmp_status_cpu_usage")
// @Success 200 {object} Response{data=MetricsComparisonResponse} "成功"
// @Failure 400 {object} Response{data=ErrorData} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorData} "未找到指标监测会话"
// @Failure 500 {object} Response{data=ErrorData} "服务器内部错误"
// @Router /api/v1/tasks/{taskId}/metrics-comparison [get]
func (h *Handler) GetMetricsComparison(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		h.respondError(c, http.StatusBadRequest, "任务ID不能为空")
		return
	}

	deviceID := c.Query("device_id")
	metricName := c.Query("metric_name")

	response, err := h.service.GetMetricsComparison(c.Request.Context(),
		taskID, deviceID, metricName)
	if err != nil {
		if err.Error() == "未找到任务的指标监测会话" {
			h.respondError(c, http.StatusNotFound, "未找到指标监测会话")
			return
		}
		h.logger.Error("查询指标对比失败",
			zap.String("task_id", taskID), zap.Error(err))
		h.respondError(c, http.StatusInternalServerError, "查询指标对比失败")
		return
	}

	h.respondSuccess(c, response)
}

// GetMetricsData 查询指标数据记录
// @Summary 查询指标数据记录
// @Description 查询任务期间的指标数据记录（时序数据），支持按设备、指标类型、监测阶段、时间范围筛选
// @Tags 指标数据查询
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID" example("task_20250118_001")
// @Param device_id query string false "设备ID筛选" example("device_001")
// @Param metric_name query string false "指标名称筛选" example("dci_snmp_status_cpu_usage")
// @Param monitoring_phase query string false "监测阶段筛选" Enums(baseline, task_execution, extended) example("task_execution")
// @Param start_time query string false "开始时间" format(date-time) example("2025-01-18T09:30:00+08:00")
// @Param end_time query string false "结束时间" format(date-time) example("2025-01-18T10:30:00+08:00")
// @Param page query int false "页码" default(1) minimum(1)
// @Param page_size query int false "每页数量" default(20) minimum(1) maximum(100)
// @Success 200 {object} Response{data=MetricsDataListResponse} "成功"
// @Failure 400 {object} Response{data=ErrorData} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorData} "未找到指标监测会话"
// @Failure 500 {object} Response{data=ErrorData} "服务器内部错误"
// @Router /api/v1/tasks/{taskId}/metrics-data [get]
func (h *Handler) GetMetricsData(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		h.respondError(c, http.StatusBadRequest, "任务ID不能为空")
		return
	}

	deviceID := c.Query("device_id")
	metricName := c.Query("metric_name")
	monitoringPhase := c.Query("monitoring_phase")
	page, pageSize := h.getPaginationParams(c)

	var startTime, endTime *time.Time
	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if t, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			startTime = &t
		} else {
			h.respondError(c, http.StatusBadRequest, "开始时间格式错误")
			return
		}
	}
	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if t, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			endTime = &t
		} else {
			h.respondError(c, http.StatusBadRequest, "结束时间格式错误")
			return
		}
	}

	response, err := h.service.GetMetricsData(c.Request.Context(),
		taskID, deviceID, metricName, monitoringPhase, startTime, endTime, page, pageSize)
	if err != nil {
		if err.Error() == "未找到任务的指标监测会话" {
			h.respondError(c, http.StatusNotFound, "未找到指标监测会话")
			return
		}
		h.logger.Error("查询指标数据记录失败",
			zap.String("task_id", taskID), zap.Error(err))
		h.respondError(c, http.StatusInternalServerError, "查询指标数据记录失败")
		return
	}

	h.respondSuccess(c, response)
}

// GetMetricsTrends 查询指标趋势数据
// @Summary 查询指标趋势数据
// @Description 查询任务期间的指标变化趋势数据，基于指标数据记录表生成时间序列图表数据，支持前端可视化展示
// @Tags 指标数据查询
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID" example("task_20250118_001")
// @Param device_id query string false "设备ID筛选" example("device_001")
// @Param metric_name query string false "指标名称筛选" example("dci_snmp_status_cpu_usage")
// @Param interval query string false "时间间隔" default("1m") example("5m")
// @Success 200 {object} Response{data=MetricsTrendsResponse} "成功"
// @Failure 400 {object} Response{data=ErrorData} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorData} "未找到指标监测会话"
// @Failure 500 {object} Response{data=ErrorData} "服务器内部错误"
// @Router /api/v1/tasks/{taskId}/metrics-trends [get]
func (h *Handler) GetMetricsTrends(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		h.respondError(c, http.StatusBadRequest, "任务ID不能为空")
		return
	}

	deviceID := c.Query("device_id")
	metricName := c.Query("metric_name")
	intervalStr := c.DefaultQuery("interval", "1m")

	interval, err := time.ParseDuration(intervalStr)
	if err != nil {
		h.respondError(c, http.StatusBadRequest, "时间间隔格式错误")
		return
	}

	// 查询监测会话
	session, err := h.service.GetMetricsStatus(c.Request.Context(), taskID)
	if err != nil {
		if err.Error() == "未找到任务的指标监测会话" {
			h.respondError(c, http.StatusNotFound, "未找到指标监测会话")
			return
		}
		h.logger.Error("查询指标监测会话失败",
			zap.String("task_id", taskID), zap.Error(err))
		h.respondError(c, http.StatusInternalServerError, "查询指标监测会话失败")
		return
	}

	// 生成趋势数据
	dataPoints, err := h.service.metricsRecorder.GenerateTrendsData(session.SessionID, deviceID, metricName, interval)
	if err != nil {
		h.logger.Error("生成趋势数据失败",
			zap.String("task_id", taskID), zap.Error(err))
		h.respondError(c, http.StatusInternalServerError, "生成趋势数据失败")
		return
	}

	response := &MetricsTrendsResponse{
		DeviceID:   deviceID,
		MetricName: metricName,
		TimeRange: TimeRange{
			StartTime: session.BaselineStart,
			EndTime:   session.MonitoringStart,
		},
		DataPoints: dataPoints,
	}

	// 添加基线信息
	if baseline, err := h.service.baselineCalculator.GetBaseline(session.SessionID, deviceID, metricName); err == nil && baseline != nil {
		if baseline.AvgValue != nil {
			response.Baseline = &MetricsBaselineSummary{
				AvgValue: *baseline.AvgValue,
				TimeRange: TimeRange{
					StartTime: baseline.TimeRangeStart,
					EndTime:   baseline.TimeRangeEnd,
				},
			}
		}
	}

	h.respondSuccess(c, response)
}

// GetMetricsStatistics 查询指标统计汇总
// @Summary 查询指标统计汇总
// @Description 查询任务的指标统计汇总数据，基于基线表和数据记录表生成统计汇总，包括基线值、当前值、变化幅度等信息
// @Tags 指标数据查询
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID" example("task_20250118_001")
// @Success 200 {object} Response{data=MetricsStatisticsResponse} "成功"
// @Failure 400 {object} Response{data=ErrorData} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorData} "未找到指标监测会话"
// @Failure 500 {object} Response{data=ErrorData} "服务器内部错误"
// @Router /api/v1/tasks/{taskId}/metrics-statistics [get]
func (h *Handler) GetMetricsStatistics(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		h.respondError(c, http.StatusBadRequest, "任务ID不能为空")
		return
	}

	// 查询监测会话
	session, err := h.service.GetMetricsStatus(c.Request.Context(), taskID)
	if err != nil {
		if err.Error() == "未找到任务的指标监测会话" {
			h.respondError(c, http.StatusNotFound, "未找到指标监测会话")
			return
		}
		h.logger.Error("查询指标监测会话失败",
			zap.String("task_id", taskID), zap.Error(err))
		h.respondError(c, http.StatusInternalServerError, "查询指标监测会话失败")
		return
	}

	// 获取基线数据
	baselines, err := h.service.baselineCalculator.GetAllBaselines(session.SessionID)
	if err != nil {
		h.logger.Error("查询基线数据失败",
			zap.String("task_id", taskID), zap.Error(err))
		h.respondError(c, http.StatusInternalServerError, "查询基线数据失败")
		return
	}

	// 构建统计响应
	response := &MetricsStatisticsResponse{
		TaskID:    taskID,
		SessionID: session.SessionID,
		Summary: &MetricsOverallSummary{
			TotalDevices: len(session.DeviceIds),
			TotalMetrics: len(session.MetricsTypes),
		},
	}

	// 按设备分组统计
	deviceStats := make(map[string]*MetricsDeviceStatistics)
	for _, baseline := range baselines {
		if _, exists := deviceStats[baseline.DeviceID]; !exists {
			deviceStats[baseline.DeviceID] = &MetricsDeviceStatistics{
				DeviceID:   baseline.DeviceID,
				DeviceName: baseline.DeviceID, // 可以从设备管理服务获取实际名称
				Metrics:    []*MetricsDeviceMetric{},
			}
		}

		// 获取各阶段统计
		stats, err := h.service.metricsRecorder.GetMetricsDataStatistics(session.SessionID, baseline.DeviceID, baseline.MetricName)
		if err != nil {
			h.logger.Warn("获取指标统计失败",
				zap.String("device_id", baseline.DeviceID),
				zap.String("metric_name", baseline.MetricName),
				zap.Error(err))
			continue
		}

		deviceMetric := &MetricsDeviceMetric{
			MetricName: baseline.MetricName,
		}

		// 基线统计
		if baseline.AvgValue != nil {
			deviceMetric.Baseline = &MetricsStatisticsSummary{
				AvgValue: *baseline.AvgValue,
				MaxValue: *baseline.MaxValue,
				MinValue: *baseline.MinValue,
			}
		}

		// 任务期统计
		if stats.TaskPeriod != nil {
			deviceMetric.TaskPeriod = &MetricsStatisticsSummary{
				AvgValue: stats.TaskPeriod.AvgValue,
				MaxValue: stats.TaskPeriod.MaxValue,
				MinValue: stats.TaskPeriod.MinValue,
			}
		}

		// 延展期统计
		if stats.ExtendedPeriod != nil {
			deviceMetric.ExtendedPeriod = &MetricsStatisticsSummary{
				AvgValue: stats.ExtendedPeriod.AvgValue,
				MaxValue: stats.ExtendedPeriod.MaxValue,
				MinValue: stats.ExtendedPeriod.MinValue,
			}
		}

		// 变化汇总
		if stats.ChangeSummary != nil {
			deviceMetric.ChangeSummary = &MetricsDeviceChangeSummary{
				MaxIncrease: stats.ChangeSummary.MaxIncrease,
				AvgChange:   stats.ChangeSummary.AvgChange,
				Volatility:  stats.ChangeSummary.Volatility,
			}
		}

		deviceStats[baseline.DeviceID].Metrics = append(deviceStats[baseline.DeviceID].Metrics, deviceMetric)
	}

	// 转换为数组
	for _, stat := range deviceStats {
		response.DeviceStatistics = append(response.DeviceStatistics, stat)
		response.Summary.TotalMetrics += len(stat.Metrics)
	}

	// 计算监控时长
	if session.MonitoringEnd != nil {
		duration := session.MonitoringEnd.Sub(session.MonitoringStart)
		response.Summary.MonitoringDuration = duration.String()
	}

	h.respondSuccess(c, response)
}

// DeleteMetricsDataCascade 级联删除指标数据
// @Summary 级联删除指标数据
// @Description 按正确顺序级联删除任务的所有指标相关数据。由于采用了限制删除策略，提供应用层的级联删除功能，按照正确顺序删除数据记录→基线数据→数据记录会话
// @Tags 数据管理
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID" example("task_20250118_001")
// @Success 200 {object} Response{data=MetricsDeleteResponse} "成功"
// @Failure 400 {object} Response{data=ErrorData} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorData} "未找到指标监测会话"
// @Failure 500 {object} Response{data=ErrorData} "服务器内部错误"
// @Router /api/v1/tasks/{taskId}/metrics-monitoring/cascade [delete]
func (h *Handler) DeleteMetricsDataCascade(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		h.respondError(c, http.StatusBadRequest, "任务ID不能为空")
		return
	}

	response, err := h.service.DeleteMetricsDataCascade(c.Request.Context(), taskID)
	if err != nil {
		if err.Error() == "未找到任务的指标监测会话" {
			h.respondError(c, http.StatusNotFound, "未找到指标监测会话")
			return
		}
		h.logger.Error("级联删除指标数据失败",
			zap.String("task_id", taskID), zap.Error(err))
		h.respondError(c, http.StatusInternalServerError, "级联删除指标数据失败")
		return
	}

	h.respondSuccess(c, response)
}

// GetMetricsDependencies 查询数据关联状态
// @Summary 查询数据关联状态
// @Description 查询指标数据记录的关联依赖状态，返回各表间的数据关联情况，用于删除前的依赖检查
// @Tags 数据管理
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID" example("task_20250118_001")
// @Success 200 {object} Response{data=MetricsDependenciesResponse} "成功"
// @Failure 400 {object} Response{data=ErrorData} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorData} "未找到指标监测会话"
// @Failure 500 {object} Response{data=ErrorData} "服务器内部错误"
// @Router /api/v1/tasks/{taskId}/metrics-monitoring/dependencies [get]
func (h *Handler) GetMetricsDependencies(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		h.respondError(c, http.StatusBadRequest, "任务ID不能为空")
		return
	}

	// 查询监测会话
	session, err := h.service.GetMetricsStatus(c.Request.Context(), taskID)
	if err != nil {
		if err.Error() == "未找到任务的指标监测会话" {
			response := &MetricsDependenciesResponse{
				TaskID:            taskID,
				HasMetricsSession: false,
				CanDelete:         true,
			}
			h.respondSuccess(c, response)
			return
		}
		h.logger.Error("查询指标监测会话失败",
			zap.String("task_id", taskID), zap.Error(err))
		h.respondError(c, http.StatusInternalServerError, "查询指标监测会话失败")
		return
	}

	// 查询基线数据数量
	baselines, err := h.service.baselineCalculator.GetAllBaselines(session.SessionID)
	if err != nil {
		h.logger.Error("查询基线数据失败", zap.Error(err))
		h.respondError(c, http.StatusInternalServerError, "查询基线数据失败")
		return
	}

	// 查询数据记录数量
	dataRecords, total, err := h.service.dao.GetMetricsChangesBySessionID(session.SessionID, "", "", "", nil, nil, 0, 1)
	if err != nil {
		h.logger.Error("查询数据记录失败", zap.Error(err))
		h.respondError(c, http.StatusInternalServerError, "查询数据记录失败")
		return
	}

	var timeRange *TimeRange
	if len(dataRecords) > 0 {
		timeRange = &TimeRange{
			StartTime: session.BaselineStart,
			EndTime:   session.MonitoringStart,
		}
		if session.ExtendedEnd != nil {
			timeRange.EndTime = *session.ExtendedEnd
		}
	}

	response := &MetricsDependenciesResponse{
		TaskID:            taskID,
		HasMetricsSession: true,
		Dependencies: &MetricsDependenciesDetail{
			MetricsSession: &MetricsSessionInfo{
				SessionID: session.SessionID,
				Status:    session.Status,
			},
			BaselineRecords: &MetricsRecordInfo{
				Count:     len(baselines),
				DeviceIds: session.DeviceIds,
			},
			DataRecords: &MetricsRecordInfo{
				Count:     total,
				DeviceIds: session.DeviceIds,
				TimeRange: timeRange,
			},
		},
		CanDelete: true,
	}

	h.respondSuccess(c, response)
}

// CleanupMetricsData 数据清理接口
// @Summary 数据清理接口
// @Description 清理指定时间范围外的历史指标数据记录，支持按时间范围清理过期数据，需要按照正确的删除顺序处理
// @Tags 数据管理
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID" example("task_20250118_001")
// @Param before query string true "删除此时间之前的数据" format(date-time) example("2025-01-17T00:00:00+08:00")
// @Success 200 {object} Response{data=MetricsCleanupResponse} "成功"
// @Failure 400 {object} Response{data=ErrorData} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorData} "未找到指标监测会话"
// @Failure 500 {object} Response{data=ErrorData} "服务器内部错误"
// @Router /api/v1/tasks/{taskId}/metrics-data/cleanup [delete]
func (h *Handler) CleanupMetricsData(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		h.respondError(c, http.StatusBadRequest, "任务ID不能为空")
		return
	}

	beforeStr := c.Query("before")
	if beforeStr == "" {
		h.respondError(c, http.StatusBadRequest, "清理时间参数不能为空")
		return
	}

	before, err := time.Parse(time.RFC3339, beforeStr)
	if err != nil {
		h.respondError(c, http.StatusBadRequest, "清理时间格式错误")
		return
	}

	deletedCount, err := h.service.dao.DeleteMetricsDataByTimeRange(taskID, &before)
	if err != nil {
		h.logger.Error("清理历史数据失败",
			zap.String("task_id", taskID), zap.Error(err))
		h.respondError(c, http.StatusInternalServerError, "清理历史数据失败")
		return
	}

	response := &MetricsCleanupResponse{
		TaskID:         taskID,
		CleanupBefore:  beforeStr,
		DeletedRecords: deletedCount,
		Message:        "历史数据清理完成",
	}

	h.respondSuccess(c, response)
}

// ==========================================================================
// 辅助方法
// ==========================================================================

// getPaginationParams 获取分页参数
func (h *Handler) getPaginationParams(c *gin.Context) (page, pageSize int) {
	page = 1
	pageSize = 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	return page, pageSize
}

// respondSuccess 成功响应
func (h *Handler) respondSuccess(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, gin.H{"code": 200, "data": data})
}

// respondError 错误响应
func (h *Handler) respondError(c *gin.Context, statusCode int, message string) {
	requestID := c.GetString("request_id")
	if requestID == "" {
		requestID = c.GetHeader("X-Request-ID")
	}

	c.JSON(statusCode, gin.H{
		"code": statusCode,
		"data": gin.H{
			"error":      message,
			"request_id": requestID,
		},
	})
}

// ==========================================================================
// 通用响应结构
// ==========================================================================

// Response 统一响应格式
type Response struct {
	Code int         `json:"code"`
	Data interface{} `json:"data"`
}

// ErrorData 错误响应数据
type ErrorData struct {
	Error     string `json:"error"`
	RequestID string `json:"request_id"`
}
