package metrics_monitoring

import (
	"context"
	"dcimonitor/internal/traffic"
	"dcimonitor/internal/utils/timeutil"
	"encoding/json"
	"fmt"
	"time"

	"database/sql"

	"go.uber.org/zap"
)

// Service 指标监测服务
type Service struct {
	dao                *DAO
	metricsCollector   *MetricsCollector
	baselineCalculator *BaselineCalculator
	metricsRecorder    *MetricsRecorder
	config             *MetricsMonitoringConfig
	logger             *zap.Logger
}

// NewService 创建指标监测服务实例
func NewService(db *sql.DB, trafficDAO traffic.TrafficDAO, config *MetricsMonitoringConfig, logger *zap.Logger) *Service {
	// 创建DAO
	dao := NewDAO(db, logger) // 传入实际的数据库连接

	// 创建核心组件
	metricsCollector := NewMetricsCollector(trafficDAO, config, logger)
	baselineCalculator := NewBaselineCalculator(dao, metricsCollector, config, logger)
	metricsRecorder := NewMetricsRecorder(dao, metricsCollector, baselineCalculator, config, logger)

	return &Service{
		dao:                dao,
		metricsCollector:   metricsCollector,
		baselineCalculator: baselineCalculator,
		metricsRecorder:    metricsRecorder,
		config:             config,
		logger:             logger,
	}
}

// StartMetricsMonitoring 启动指标监测
func (s *Service) StartMetricsMonitoring(ctx context.Context, taskID, taskSessionID string,
	deviceIDs []string, metricTypes []string) (*MetricsStatusResponse, error) {

	s.logger.Info("启动指标监测",
		zap.String("task_id", taskID),
		zap.String("task_session_id", taskSessionID),
		zap.Strings("device_ids", deviceIDs),
		zap.Strings("metric_types", metricTypes))

	// 验证指标类型
	err := s.metricsCollector.ValidateMetricTypes(metricTypes)
	if err != nil {
		return nil, fmt.Errorf("指标类型验证失败: %w", err)
	}

	// 解析配置时间
	baselineDuration, err := time.ParseDuration(s.config.BaselineDuration)
	if err != nil {
		s.logger.Error("解析基线时长失败", zap.Error(err))
		baselineDuration = 30 * time.Minute // 默认30分钟
	}

	now := timeutil.NowInLocalTime()
	baselineStart := now.Add(-baselineDuration)
	baselineEnd := now

	// 创建指标监测会话
	session := &MetricsSession{
		TaskID:          taskID,
		TaskSessionID:   taskSessionID,
		DeviceIDs:       s.serializeStringArray(deviceIDs),
		MetricsTypes:    s.serializeStringArray(metricTypes),
		BaselineStart:   baselineStart,
		BaselineEnd:     baselineEnd,
		MonitoringStart: now,
		Status:          MetricsStatusMonitoringActive,
	}

	err = s.dao.CreateMetricsSession(session)
	if err != nil {
		return nil, fmt.Errorf("创建指标监测会话失败: %w", err)
	}

	// 异步启动基线计算
	go func() {
		bgCtx := context.Background()
		s.logger.Info("开始异步基线计算", zap.String("session_id", session.SessionID))

		err := s.baselineCalculator.CalculateBaseline(bgCtx, session.SessionID,
			deviceIDs, metricTypes, baselineStart, baselineEnd)
		if err != nil {
			s.logger.Error("基线计算失败",
				zap.String("session_id", session.SessionID),
				zap.Error(err))
			return
		}

		// 更新状态为基线分析中
		err = s.dao.UpdateMetricsSessionStatus(session.SessionID, MetricsStatusBaselineAnalyzing)
		if err != nil {
			s.logger.Error("更新基线分析状态失败", zap.Error(err))
		}

		// 检查基线是否完成
		baselineReady, err := s.baselineCalculator.IsBaselineReady(session.SessionID, deviceIDs, metricTypes)
		if err != nil {
			s.logger.Error("检查基线就绪状态失败", zap.Error(err))
		} else if baselineReady {
			err = s.dao.UpdateMetricsSessionStatus(session.SessionID, MetricsStatusFullyActive)
			if err != nil {
				s.logger.Error("更新完全激活状态失败", zap.Error(err))
			}
			s.logger.Info("基线计算完成，指标监测完全激活", zap.String("session_id", session.SessionID))
		}
	}()

	// 立即启动实时数据记录
	go func() {
		bgCtx := context.Background()
		queryInterval, err := time.ParseDuration(s.config.QueryInterval)
		if err != nil {
			queryInterval = time.Minute
		}

		stopCh := make(chan struct{})
		s.logger.Info("开始实时数据记录",
			zap.String("session_id", session.SessionID),
			zap.Duration("interval", queryInterval))

		s.metricsRecorder.RecordPeriodicMetrics(bgCtx, session.SessionID,
			deviceIDs, metricTypes, MonitoringPhaseTaskExecution, queryInterval, stopCh)
	}()

	// 返回状态响应
	response := &MetricsStatusResponse{
		SessionID:       session.SessionID,
		TaskID:          taskID,
		TaskSessionID:   taskSessionID,
		DeviceIds:       deviceIDs,
		MetricsTypes:    metricTypes,
		BaselineStart:   baselineStart,
		BaselineEnd:     baselineEnd,
		MonitoringStart: now,
		Status:          MetricsStatusMonitoringActive,
		CreatedAt:       session.CreatedAt,
	}

	return response, nil
}

// StopMetricsMonitoring 停止指标监测
func (s *Service) StopMetricsMonitoring(ctx context.Context, taskID string) (*MetricsStopResponse, error) {
	s.logger.Info("停止指标监测", zap.String("task_id", taskID))

	// 查询监测会话
	session, err := s.dao.GetMetricsSessionByTaskID(taskID)
	if err != nil {
		return nil, fmt.Errorf("查询指标监测会话失败: %w", err)
	}
	if session == nil {
		return nil, fmt.Errorf("未找到任务的指标监测会话")
	}

	// 更新会话结束时间和状态
	now := timeutil.NowInLocalTime()
	err = s.dao.UpdateMetricsSessionTimes(session.SessionID, &now, nil)
	if err != nil {
		s.logger.Error("更新监测结束时间失败", zap.Error(err))
	}

	// 启动延展监测
	err = s.startExtendedMonitoring(ctx, session)
	if err != nil {
		s.logger.Error("启动延展监测失败", zap.Error(err))
	}

	response := &MetricsStopResponse{
		SessionID: session.SessionID,
		TaskID:    taskID,
		Status:    MetricsStatusExtendedMonitoring,
		Message:   "任务监测已停止，延展监测已启动",
	}

	return response, nil
}

// startExtendedMonitoring 启动延展监测
func (s *Service) startExtendedMonitoring(_ context.Context, session *MetricsSession) error {
	// 解析延展时长
	extendedDuration, err := time.ParseDuration(s.config.ExtendedDuration)
	if err != nil {
		extendedDuration = time.Hour // 默认1小时
	}

	// 更新状态为延展监测
	err = s.dao.UpdateMetricsSessionStatus(session.SessionID, MetricsStatusExtendedMonitoring)
	if err != nil {
		return err
	}

	// 反序列化设备ID和指标类型
	deviceIDs, err := s.deserializeStringArray(session.DeviceIDs)
	if err != nil {
		return fmt.Errorf("反序列化设备ID失败: %w", err)
	}

	metricTypes, err := s.deserializeStringArray(session.MetricsTypes)
	if err != nil {
		return fmt.Errorf("反序列化指标类型失败: %w", err)
	}

	// 启动延展期数据记录
	go func() {
		bgCtx, cancel := context.WithTimeout(context.Background(), extendedDuration)
		defer cancel()

		queryInterval, err := time.ParseDuration(s.config.QueryInterval)
		if err != nil {
			queryInterval = time.Minute
		}

		stopCh := make(chan struct{})

		s.logger.Info("开始延展期数据记录",
			zap.String("session_id", session.SessionID),
			zap.Duration("duration", extendedDuration))

		// 启动延展期记录
		go s.metricsRecorder.RecordPeriodicMetrics(bgCtx, session.SessionID,
			deviceIDs, metricTypes, MonitoringPhaseExtended, queryInterval, stopCh)

		// 等待延展期结束
		<-bgCtx.Done()
		close(stopCh)

		// 更新延展结束时间和完成状态
		extendedEnd := timeutil.NowInLocalTime()
		err = s.dao.UpdateMetricsSessionTimes(session.SessionID, session.MonitoringEnd, &extendedEnd)
		if err != nil {
			s.logger.Error("更新延展结束时间失败", zap.Error(err))
		}

		err = s.dao.UpdateMetricsSessionStatus(session.SessionID, MetricsStatusCompleted)
		if err != nil {
			s.logger.Error("更新完成状态失败", zap.Error(err))
		}

		s.logger.Info("延展监测完成", zap.String("session_id", session.SessionID))
	}()

	return nil
}

// GetMetricsStatus 获取指标监测状态
func (s *Service) GetMetricsStatus(ctx context.Context, taskID string) (*MetricsStatusResponse, error) {
	session, err := s.dao.GetMetricsSessionByTaskID(taskID)
	if err != nil {
		return nil, fmt.Errorf("查询指标监测会话失败: %w", err)
	}
	if session == nil {
		return nil, fmt.Errorf("未找到任务的指标监测会话")
	}

	// 反序列化设备ID和指标类型
	deviceIDs, err := s.deserializeStringArray(session.DeviceIDs)
	if err != nil {
		s.logger.Error("反序列化设备ID失败", zap.Error(err))
		deviceIDs = []string{}
	}

	metricTypes, err := s.deserializeStringArray(session.MetricsTypes)
	if err != nil {
		s.logger.Error("反序列化指标类型失败", zap.Error(err))
		metricTypes = []string{}
	}

	response := &MetricsStatusResponse{
		SessionID:       session.SessionID,
		TaskID:          session.TaskID,
		TaskSessionID:   session.TaskSessionID,
		DeviceIds:       deviceIDs,
		MetricsTypes:    metricTypes,
		BaselineStart:   session.BaselineStart,
		BaselineEnd:     session.BaselineEnd,
		MonitoringStart: session.MonitoringStart,
		MonitoringEnd:   session.MonitoringEnd,
		ExtendedEnd:     session.ExtendedEnd,
		Status:          session.Status,
		CreatedAt:       session.CreatedAt,
	}

	return response, nil
}

// GetMetricsBaseline 获取指标基线数据
func (s *Service) GetMetricsBaseline(ctx context.Context, taskID, deviceID, metricName string,
	page, pageSize int) (*MetricsBaselineListResponse, error) {

	// 查询监测会话
	session, err := s.dao.GetMetricsSessionByTaskID(taskID)
	if err != nil {
		return nil, fmt.Errorf("查询指标监测会话失败: %w", err)
	}
	if session == nil {
		return nil, fmt.Errorf("未找到任务的指标监测会话")
	}

	// 计算分页参数
	offset := (page - 1) * pageSize

	// 查询基线数据
	baselines, total, err := s.dao.GetMetricsBaselinesBySessionID(session.SessionID, deviceID, metricName, offset, pageSize)
	if err != nil {
		return nil, fmt.Errorf("查询基线数据失败: %w", err)
	}

	// 计算页数
	pageCount := (total + pageSize - 1) / pageSize

	response := &MetricsBaselineListResponse{
		Items: baselines,
		Pagination: &PaginationInfo{
			Total:     total,
			Page:      page,
			PageSize:  pageSize,
			PageCount: pageCount,
		},
	}

	return response, nil
}

// GetMetricsComparison 获取指标对比数据
func (s *Service) GetMetricsComparison(ctx context.Context, taskID, deviceID, metricName string) (*MetricsComparisonResponse, error) {
	// 查询监测会话
	session, err := s.dao.GetMetricsSessionByTaskID(taskID)
	if err != nil {
		return nil, fmt.Errorf("查询指标监测会话失败: %w", err)
	}
	if session == nil {
		return nil, fmt.Errorf("未找到任务的指标监测会话")
	}

	// 查询基线数据
	baselines, _, err := s.dao.GetMetricsBaselinesBySessionID(session.SessionID, deviceID, metricName, 0, 100)
	if err != nil {
		return nil, fmt.Errorf("查询基线数据失败: %w", err)
	}

	var items []*MetricsComparisonData
	for _, baseline := range baselines {
		// 获取各阶段的统计数据
		stats, err := s.metricsRecorder.GetMetricsDataStatistics(session.SessionID, baseline.DeviceID, baseline.MetricName)
		if err != nil {
			s.logger.Error("获取指标统计失败",
				zap.String("device_id", baseline.DeviceID),
				zap.String("metric_name", baseline.MetricName),
				zap.Error(err))
			continue
		}

		// 构建对比数据
		item := &MetricsComparisonData{
			DeviceID:   baseline.DeviceID,
			MetricName: baseline.MetricName,
		}

		// 基线数据
		if baseline.AvgValue != nil {
			item.Baseline = &MetricsStatisticsSummary{
				AvgValue: *baseline.AvgValue,
				MaxValue: *baseline.MaxValue,
				MinValue: *baseline.MinValue,
			}
		}

		// 任务期数据
		if stats.TaskPeriod != nil {
			item.TaskPeriod = &MetricsStatisticsSummary{
				AvgValue: stats.TaskPeriod.AvgValue,
				MaxValue: stats.TaskPeriod.MaxValue,
				MinValue: stats.TaskPeriod.MinValue,
			}
		}

		// 延展期数据
		if stats.ExtendedPeriod != nil {
			item.ExtendedPeriod = &MetricsStatisticsSummary{
				AvgValue: stats.ExtendedPeriod.AvgValue,
				MaxValue: stats.ExtendedPeriod.MaxValue,
				MinValue: stats.ExtendedPeriod.MinValue,
			}
		}

		// 计算变化
		item.Changes = s.calculateComparisonChanges(item.Baseline, item.TaskPeriod, item.ExtendedPeriod)

		items = append(items, item)
	}

	response := &MetricsComparisonResponse{
		Items: items,
	}

	return response, nil
}

// calculateComparisonChanges 计算对比变化
func (s *Service) calculateComparisonChanges(baseline, taskPeriod, extendedPeriod *MetricsStatisticsSummary) *MetricsComparisonChanges {
	changes := &MetricsComparisonChanges{}

	if baseline != nil && taskPeriod != nil {
		if baseline.AvgValue != 0 {
			change := ((taskPeriod.AvgValue - baseline.AvgValue) / baseline.AvgValue) * 100
			changes.BaselineToTask = fmt.Sprintf("%+.1f%%", change)
		}
	}

	if taskPeriod != nil && extendedPeriod != nil {
		if taskPeriod.AvgValue != 0 {
			change := ((extendedPeriod.AvgValue - taskPeriod.AvgValue) / taskPeriod.AvgValue) * 100
			changes.TaskToExtended = fmt.Sprintf("%+.1f%%", change)
		}
	}

	if baseline != nil && extendedPeriod != nil {
		if baseline.AvgValue != 0 {
			change := ((extendedPeriod.AvgValue - baseline.AvgValue) / baseline.AvgValue) * 100
			changes.OverallChange = fmt.Sprintf("%+.1f%%", change)
		}
	}

	return changes
}

// GetMetricsData 获取指标数据记录
func (s *Service) GetMetricsData(ctx context.Context, taskID, deviceID, metricName, monitoringPhase string,
	startTime, endTime *time.Time, page, pageSize int) (*MetricsDataListResponse, error) {

	// 查询监测会话
	session, err := s.dao.GetMetricsSessionByTaskID(taskID)
	if err != nil {
		return nil, fmt.Errorf("查询指标监测会话失败: %w", err)
	}
	if session == nil {
		return nil, fmt.Errorf("未找到任务的指标监测会话")
	}

	// 计算分页参数
	offset := (page - 1) * pageSize

	// 查询数据记录
	changes, total, err := s.dao.GetMetricsChangesBySessionID(session.SessionID, deviceID, metricName,
		monitoringPhase, startTime, endTime, offset, pageSize)
	if err != nil {
		return nil, fmt.Errorf("查询指标数据记录失败: %w", err)
	}

	// 计算页数
	pageCount := (total + pageSize - 1) / pageSize

	response := &MetricsDataListResponse{
		Items: changes,
		Pagination: &PaginationInfo{
			Total:     total,
			Page:      page,
			PageSize:  pageSize,
			PageCount: pageCount,
		},
	}

	return response, nil
}

// DeleteMetricsDataCascade 级联删除指标数据
func (s *Service) DeleteMetricsDataCascade(ctx context.Context, taskID string) (*MetricsDeleteResponse, error) {
	// 查询监测会话
	session, err := s.dao.GetMetricsSessionByTaskID(taskID)
	if err != nil {
		return nil, fmt.Errorf("查询指标监测会话失败: %w", err)
	}
	if session == nil {
		return nil, fmt.Errorf("未找到任务的指标监测会话")
	}

	// 级联删除数据
	changeCount, baselineCount, sessionCount, err := s.dao.DeleteMetricsDataCascade(session.SessionID)
	if err != nil {
		return nil, fmt.Errorf("级联删除指标数据失败: %w", err)
	}

	response := &MetricsDeleteResponse{
		TaskID: taskID,
		DeletedItems: &MetricsDeletedItems{
			MetricsDataRecords: changeCount,
			BaselineRecords:    baselineCount,
			MetricsSession:     sessionCount,
		},
		Message: "指标数据已成功级联删除",
	}

	return response, nil
}

// serializeStringArray 序列化字符串数组
func (s *Service) serializeStringArray(arr []string) string {
	if len(arr) == 0 {
		return "[]"
	}

	bytes, err := json.Marshal(arr)
	if err != nil {
		s.logger.Error("序列化字符串数组失败", zap.Error(err))
		return "[]"
	}

	return string(bytes)
}

// deserializeStringArray 反序列化字符串数组
func (s *Service) deserializeStringArray(data string) ([]string, error) {
	if data == "" || data == "[]" {
		return []string{}, nil
	}

	var result []string
	err := json.Unmarshal([]byte(data), &result)
	if err != nil {
		return nil, fmt.Errorf("反序列化字符串数组失败: %w", err)
	}

	return result, nil
}
