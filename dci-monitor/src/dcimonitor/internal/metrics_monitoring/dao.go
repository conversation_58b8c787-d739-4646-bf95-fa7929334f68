package metrics_monitoring

import (
	"database/sql"
	"dcimonitor/internal/utils/timeutil"
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

// DAO 提供对指标监测数据的访问
type DAO struct {
	db     *sql.DB
	logger *zap.Logger
}

// NewDAO 创建一个新的DAO实例
func NewDAO(db *sql.DB, logger *zap.Logger) *DAO {
	return &DAO{
		db:     db,
		logger: logger,
	}
}

// ==========================================================================
// 指标监测会话相关操作
// ==========================================================================

// CreateMetricsSession 创建指标监测会话
func (dao *DAO) CreateMetricsSession(session *MetricsSession) error {
	if session.SessionID == "" {
		session.SessionID = "metrics_session_" + uuid.New().String()
	}

	// 序列化设备ID列表和指标类型列表
	deviceIDsJSON, err := json.Marshal(session.DeviceIDs)
	if err != nil {
		dao.logger.Error("序列化设备ID列表失败", zap.Error(err))
		return err
	}

	metricsTypesJSON, err := json.Marshal(session.MetricsTypes)
	if err != nil {
		dao.logger.Error("序列化指标类型列表失败", zap.Error(err))
		return err
	}

	query := `
		INSERT INTO monitor_task_metrics_sessions (
			session_id, task_id, task_session_id, device_ids, metrics_types,
			baseline_start, baseline_end, monitoring_start, monitoring_end,
			extended_end, status, created_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	now := timeutil.NowInLocalTime()
	session.CreatedAt = now

	_, err = dao.db.Exec(
		query,
		session.SessionID, session.TaskID, session.TaskSessionID,
		string(deviceIDsJSON), string(metricsTypesJSON),
		session.BaselineStart, session.BaselineEnd, session.MonitoringStart,
		session.MonitoringEnd, session.ExtendedEnd, session.Status, now,
	)

	if err != nil {
		dao.logger.Error("创建指标监测会话失败",
			zap.Error(err),
			zap.String("session_id", session.SessionID),
			zap.String("task_id", session.TaskID))
		return err
	}

	dao.logger.Info("成功创建指标监测会话",
		zap.String("session_id", session.SessionID),
		zap.String("task_id", session.TaskID),
		zap.String("status", session.Status))

	return nil
}

// GetMetricsSessionByTaskID 根据任务ID查询指标监测会话
func (dao *DAO) GetMetricsSessionByTaskID(taskID string) (*MetricsSession, error) {
	query := `
		SELECT session_id, task_id, task_session_id, device_ids, metrics_types,
			   baseline_start, baseline_end, monitoring_start, monitoring_end,
			   extended_end, status, created_at
		FROM monitor_task_metrics_sessions
		WHERE task_id = ?
		ORDER BY created_at DESC
		LIMIT 1
	`

	session := &MetricsSession{}
	var deviceIDsJSON, metricsTypesJSON string

	err := dao.db.QueryRow(query, taskID).Scan(
		&session.SessionID, &session.TaskID, &session.TaskSessionID,
		&deviceIDsJSON, &metricsTypesJSON,
		&session.BaselineStart, &session.BaselineEnd, &session.MonitoringStart,
		&session.MonitoringEnd, &session.ExtendedEnd, &session.Status, &session.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			dao.logger.Warn("未找到指标监测会话", zap.String("task_id", taskID))
			return nil, nil
		}
		dao.logger.Error("查询指标监测会话失败",
			zap.Error(err),
			zap.String("task_id", taskID))
		return nil, err
	}

	// 反序列化JSON字段
	session.DeviceIDs = deviceIDsJSON
	session.MetricsTypes = metricsTypesJSON

	return session, nil
}

// GetMetricsSessionBySessionID 根据会话ID查询指标监测会话
func (dao *DAO) GetMetricsSessionBySessionID(sessionID string) (*MetricsSession, error) {
	query := `
		SELECT session_id, task_id, task_session_id, device_ids, metrics_types,
			   baseline_start, baseline_end, monitoring_start, monitoring_end,
			   extended_end, status, created_at
		FROM monitor_task_metrics_sessions
		WHERE session_id = ?
	`

	session := &MetricsSession{}
	var deviceIDsJSON, metricsTypesJSON string

	err := dao.db.QueryRow(query, sessionID).Scan(
		&session.SessionID, &session.TaskID, &session.TaskSessionID,
		&deviceIDsJSON, &metricsTypesJSON,
		&session.BaselineStart, &session.BaselineEnd, &session.MonitoringStart,
		&session.MonitoringEnd, &session.ExtendedEnd, &session.Status, &session.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			dao.logger.Warn("未找到指标监测会话", zap.String("session_id", sessionID))
			return nil, nil
		}
		dao.logger.Error("查询指标监测会话失败",
			zap.Error(err),
			zap.String("session_id", sessionID))
		return nil, err
	}

	// 反序列化JSON字段
	session.DeviceIDs = deviceIDsJSON
	session.MetricsTypes = metricsTypesJSON

	return session, nil
}

// UpdateMetricsSessionStatus 更新指标监测会话状态
func (dao *DAO) UpdateMetricsSessionStatus(sessionID, status string) error {
	query := `
		UPDATE monitor_task_metrics_sessions
		SET status = ?
		WHERE session_id = ?
	`

	result, err := dao.db.Exec(query, status, sessionID)
	if err != nil {
		dao.logger.Error("更新指标监测会话状态失败",
			zap.Error(err),
			zap.String("session_id", sessionID),
			zap.String("status", status))
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		dao.logger.Error("获取更新影响行数失败", zap.Error(err))
		return err
	}

	if rowsAffected == 0 {
		dao.logger.Warn("未找到要更新的指标监测会话", zap.String("session_id", sessionID))
		return sql.ErrNoRows
	}

	dao.logger.Info("成功更新指标监测会话状态",
		zap.String("session_id", sessionID),
		zap.String("status", status))

	return nil
}

// UpdateMetricsSessionTimes 更新指标监测会话时间
func (dao *DAO) UpdateMetricsSessionTimes(sessionID string, monitoringEnd, extendedEnd *time.Time) error {
	query := `
		UPDATE monitor_task_metrics_sessions
		SET monitoring_end = ?, extended_end = ?
		WHERE session_id = ?
	`

	_, err := dao.db.Exec(query, monitoringEnd, extendedEnd, sessionID)
	if err != nil {
		dao.logger.Error("更新指标监测会话时间失败",
			zap.Error(err),
			zap.String("session_id", sessionID))
		return err
	}

	dao.logger.Info("成功更新指标监测会话时间",
		zap.String("session_id", sessionID))

	return nil
}

// ==========================================================================
// 指标基线相关操作
// ==========================================================================

// CreateMetricsBaseline 创建指标基线记录
func (dao *DAO) CreateMetricsBaseline(baseline *MetricsBaseline) error {
	if baseline.ID == "" {
		baseline.ID = uuid.New().String()
	}

	query := `
		INSERT INTO monitor_task_metrics_baselines (
			id, metrics_session_id, device_id, metric_name,
			avg_value, max_value, min_value, std_dev, sample_count,
			time_range_start, time_range_end, created_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	now := timeutil.NowInLocalTime()
	baseline.CreatedAt = now

	_, err := dao.db.Exec(
		query,
		baseline.ID, baseline.MetricsSessionID, baseline.DeviceID, baseline.MetricName,
		baseline.AvgValue, baseline.MaxValue, baseline.MinValue, baseline.StdDev,
		baseline.SampleCount, baseline.TimeRangeStart, baseline.TimeRangeEnd, now,
	)

	if err != nil {
		dao.logger.Error("创建指标基线记录失败",
			zap.Error(err),
			zap.String("id", baseline.ID),
			zap.String("session_id", baseline.MetricsSessionID),
			zap.String("device_id", baseline.DeviceID),
			zap.String("metric_name", baseline.MetricName))
		return err
	}

	dao.logger.Info("成功创建指标基线记录",
		zap.String("id", baseline.ID),
		zap.String("session_id", baseline.MetricsSessionID),
		zap.String("device_id", baseline.DeviceID),
		zap.String("metric_name", baseline.MetricName))

	return nil
}

// GetMetricsBaselinesBySessionID 根据会话ID查询指标基线记录
func (dao *DAO) GetMetricsBaselinesBySessionID(sessionID, deviceID, metricName string, offset, limit int) ([]*MetricsBaseline, int, error) {
	// 构建查询条件
	whereClause := "WHERE metrics_session_id = ?"
	args := []interface{}{sessionID}

	if deviceID != "" {
		whereClause += " AND device_id = ?"
		args = append(args, deviceID)
	}

	if metricName != "" {
		whereClause += " AND metric_name = ?"
		args = append(args, metricName)
	}

	// 查询总数
	countQuery := "SELECT COUNT(*) FROM monitor_task_metrics_baselines " + whereClause
	var total int
	err := dao.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		dao.logger.Error("查询指标基线记录总数失败", zap.Error(err))
		return nil, 0, err
	}

	// 查询数据
	query := `
		SELECT id, metrics_session_id, device_id, metric_name,
			   avg_value, max_value, min_value, std_dev, sample_count,
			   time_range_start, time_range_end, created_at
		FROM monitor_task_metrics_baselines ` + whereClause + `
		ORDER BY created_at DESC
		LIMIT ? OFFSET ?
	`

	args = append(args, limit, offset)
	rows, err := dao.db.Query(query, args...)
	if err != nil {
		dao.logger.Error("查询指标基线记录失败", zap.Error(err))
		return nil, 0, err
	}
	defer rows.Close()

	var baselines []*MetricsBaseline
	for rows.Next() {
		baseline := &MetricsBaseline{}
		err := rows.Scan(
			&baseline.ID, &baseline.MetricsSessionID, &baseline.DeviceID, &baseline.MetricName,
			&baseline.AvgValue, &baseline.MaxValue, &baseline.MinValue, &baseline.StdDev,
			&baseline.SampleCount, &baseline.TimeRangeStart, &baseline.TimeRangeEnd, &baseline.CreatedAt,
		)
		if err != nil {
			dao.logger.Error("扫描指标基线记录失败", zap.Error(err))
			return nil, 0, err
		}
		baselines = append(baselines, baseline)
	}

	if err = rows.Err(); err != nil {
		dao.logger.Error("遍历指标基线记录失败", zap.Error(err))
		return nil, 0, err
	}

	return baselines, total, nil
}

// ==========================================================================
// 指标数据记录相关操作
// ==========================================================================

// CreateMetricsChange 创建指标数据记录
func (dao *DAO) CreateMetricsChange(change *MetricsChange) error {
	if change.ID == "" {
		change.ID = uuid.New().String()
	}

	query := `
		INSERT INTO monitor_task_metrics_changes (
			id, metrics_session_id, device_id, metric_name, change_type,
			current_value, baseline_value, change_magnitude, detection_time,
			monitoring_phase, severity, description, created_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	now := timeutil.NowInLocalTime()
	change.CreatedAt = now

	_, err := dao.db.Exec(
		query,
		change.ID, change.MetricsSessionID, change.DeviceID, change.MetricName,
		change.ChangeType, change.CurrentValue, change.BaselineValue, change.ChangeMagnitude,
		change.DetectionTime, change.MonitoringPhase, change.Severity, change.Description, now,
	)

	if err != nil {
		dao.logger.Error("创建指标数据记录失败",
			zap.Error(err),
			zap.String("id", change.ID),
			zap.String("session_id", change.MetricsSessionID),
			zap.String("device_id", change.DeviceID),
			zap.String("metric_name", change.MetricName))
		return err
	}

	dao.logger.Debug("成功创建指标数据记录",
		zap.String("id", change.ID),
		zap.String("session_id", change.MetricsSessionID),
		zap.String("device_id", change.DeviceID),
		zap.String("metric_name", change.MetricName))

	return nil
}

// GetMetricsChangesBySessionID 根据会话ID查询指标数据记录
func (dao *DAO) GetMetricsChangesBySessionID(sessionID, deviceID, metricName, monitoringPhase string,
	startTime, endTime *time.Time, offset, limit int) ([]*MetricsChange, int, error) {

	// 构建查询条件
	whereClause := "WHERE metrics_session_id = ?"
	args := []interface{}{sessionID}

	if deviceID != "" {
		whereClause += " AND device_id = ?"
		args = append(args, deviceID)
	}

	if metricName != "" {
		whereClause += " AND metric_name = ?"
		args = append(args, metricName)
	}

	if monitoringPhase != "" {
		whereClause += " AND monitoring_phase = ?"
		args = append(args, monitoringPhase)
	}

	if startTime != nil {
		whereClause += " AND detection_time >= ?"
		args = append(args, startTime)
	}

	if endTime != nil {
		whereClause += " AND detection_time <= ?"
		args = append(args, endTime)
	}

	// 查询总数
	countQuery := "SELECT COUNT(*) FROM monitor_task_metrics_changes " + whereClause
	var total int
	err := dao.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		dao.logger.Error("查询指标数据记录总数失败", zap.Error(err))
		return nil, 0, err
	}

	// 查询数据
	query := `
		SELECT id, metrics_session_id, device_id, metric_name, change_type,
			   current_value, baseline_value, change_magnitude, detection_time,
			   monitoring_phase, severity, description, created_at
		FROM monitor_task_metrics_changes ` + whereClause + `
		ORDER BY detection_time DESC
		LIMIT ? OFFSET ?
	`

	args = append(args, limit, offset)
	rows, err := dao.db.Query(query, args...)
	if err != nil {
		dao.logger.Error("查询指标数据记录失败", zap.Error(err))
		return nil, 0, err
	}
	defer rows.Close()

	var changes []*MetricsChange
	for rows.Next() {
		change := &MetricsChange{}
		err := rows.Scan(
			&change.ID, &change.MetricsSessionID, &change.DeviceID, &change.MetricName,
			&change.ChangeType, &change.CurrentValue, &change.BaselineValue, &change.ChangeMagnitude,
			&change.DetectionTime, &change.MonitoringPhase, &change.Severity, &change.Description, &change.CreatedAt,
		)
		if err != nil {
			dao.logger.Error("扫描指标数据记录失败", zap.Error(err))
			return nil, 0, err
		}
		changes = append(changes, change)
	}

	if err = rows.Err(); err != nil {
		dao.logger.Error("遍历指标数据记录失败", zap.Error(err))
		return nil, 0, err
	}

	return changes, total, nil
}

// ==========================================================================
// 数据管理相关操作
// ==========================================================================

// DeleteMetricsDataCascade 级联删除指标相关数据
func (dao *DAO) DeleteMetricsDataCascade(sessionID string) (int, int, int, error) {
	// 开启事务
	tx, err := dao.db.Begin()
	if err != nil {
		dao.logger.Error("开启事务失败", zap.Error(err))
		return 0, 0, 0, err
	}
	defer tx.Rollback()

	// 删除指标数据记录
	changeResult, err := tx.Exec("DELETE FROM monitor_task_metrics_changes WHERE metrics_session_id = ?", sessionID)
	if err != nil {
		dao.logger.Error("删除指标数据记录失败", zap.Error(err), zap.String("session_id", sessionID))
		return 0, 0, 0, err
	}
	changeCount, _ := changeResult.RowsAffected()

	// 删除基线记录
	baselineResult, err := tx.Exec("DELETE FROM monitor_task_metrics_baselines WHERE metrics_session_id = ?", sessionID)
	if err != nil {
		dao.logger.Error("删除基线记录失败", zap.Error(err), zap.String("session_id", sessionID))
		return 0, 0, 0, err
	}
	baselineCount, _ := baselineResult.RowsAffected()

	// 删除监测会话
	sessionResult, err := tx.Exec("DELETE FROM monitor_task_metrics_sessions WHERE session_id = ?", sessionID)
	if err != nil {
		dao.logger.Error("删除监测会话失败", zap.Error(err), zap.String("session_id", sessionID))
		return 0, 0, 0, err
	}
	sessionCount, _ := sessionResult.RowsAffected()

	// 提交事务
	if err = tx.Commit(); err != nil {
		dao.logger.Error("提交删除事务失败", zap.Error(err))
		return 0, 0, 0, err
	}

	dao.logger.Info("成功级联删除指标数据",
		zap.String("session_id", sessionID),
		zap.Int64("change_count", changeCount),
		zap.Int64("baseline_count", baselineCount),
		zap.Int64("session_count", sessionCount))

	return int(changeCount), int(baselineCount), int(sessionCount), nil
}

// DeleteMetricsDataByTimeRange 按时间范围删除指标数据记录
func (dao *DAO) DeleteMetricsDataByTimeRange(taskID string, before *time.Time) (int, error) {
	// 首先获取该任务的metrics_session_id
	sessionQuery := `
		SELECT session_id FROM monitor_task_metrics_sessions 
		WHERE task_id = ?
	`

	var sessionID string
	err := dao.db.QueryRow(sessionQuery, taskID).Scan(&sessionID)
	if err != nil {
		if err == sql.ErrNoRows {
			dao.logger.Warn("未找到任务对应的指标监测会话", zap.String("task_id", taskID))
			return 0, nil
		}
		dao.logger.Error("查询指标监测会话失败", zap.Error(err))
		return 0, err
	}

	// 删除指定时间之前的数据记录
	deleteQuery := `
		DELETE FROM monitor_task_metrics_changes 
		WHERE metrics_session_id = ? AND created_at < ?
	`

	result, err := dao.db.Exec(deleteQuery, sessionID, before)
	if err != nil {
		dao.logger.Error("删除历史指标数据记录失败",
			zap.Error(err),
			zap.String("task_id", taskID),
			zap.String("session_id", sessionID))
		return 0, err
	}

	deletedCount, _ := result.RowsAffected()

	dao.logger.Info("成功删除历史指标数据记录",
		zap.String("task_id", taskID),
		zap.String("session_id", sessionID),
		zap.Int64("deleted_count", deletedCount))

	return int(deletedCount), nil
}
