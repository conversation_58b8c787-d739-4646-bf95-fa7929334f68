package metrics_monitoring

import (
	"context"
	"fmt"
	"math"
	"time"

	"go.uber.org/zap"
)

// BaselineCalculator 基线统计计算器
type BaselineCalculator struct {
	dao              *DAO
	metricsCollector *MetricsCollector
	config           *MetricsMonitoringConfig
	logger           *zap.Logger
}

// NewBaselineCalculator 创建基线统计计算器实例
func NewBaselineCalculator(dao *DAO, metricsCollector *MetricsCollector,
	config *MetricsMonitoringConfig, logger *zap.Logger) *BaselineCalculator {
	return &BaselineCalculator{
		dao:              dao,
		metricsCollector: metricsCollector,
		config:           config,
		logger:           logger,
	}
}

// CalculateBaseline 计算指标基线统计
func (bc *BaselineCalculator) CalculateBaseline(ctx context.Context, sessionID string,
	deviceIDs []string, metricTypes []string, baselineStart, baselineEnd time.Time) error {

	bc.logger.Info("开始计算指标基线统计",
		zap.String("session_id", sessionID),
		zap.Int("device_count", len(deviceIDs)),
		zap.Int("metric_type_count", len(metricTypes)),
		zap.Time("baseline_start", baselineStart),
		zap.Time("baseline_end", baselineEnd))

	// 解析查询间隔
	queryInterval, err := time.ParseDuration(bc.config.QueryInterval)
	if err != nil {
		bc.logger.Error("解析查询间隔失败", zap.Error(err))
		queryInterval = time.Minute // 默认1分钟
	}

	// 为每个设备和指标类型计算基线
	var totalBaselines int
	for _, deviceID := range deviceIDs {
		for _, metricType := range metricTypes {
			baseline, err := bc.calculateDeviceMetricBaseline(ctx, sessionID, deviceID, metricType,
				baselineStart, baselineEnd, queryInterval)
			if err != nil {
				bc.logger.Error("计算设备指标基线失败",
					zap.String("device_id", deviceID),
					zap.String("metric_type", metricType),
					zap.Error(err))
				continue
			}

			if baseline != nil {
				err = bc.dao.CreateMetricsBaseline(baseline)
				if err != nil {
					bc.logger.Error("保存指标基线失败",
						zap.String("device_id", deviceID),
						zap.String("metric_type", metricType),
						zap.Error(err))
					continue
				}
				totalBaselines++
			}
		}
	}

	bc.logger.Info("指标基线统计计算完成",
		zap.String("session_id", sessionID),
		zap.Int("total_baselines", totalBaselines))

	return nil
}

// calculateDeviceMetricBaseline 计算单个设备指标的基线统计
func (bc *BaselineCalculator) calculateDeviceMetricBaseline(ctx context.Context, sessionID, deviceID, metricType string,
	baselineStart, baselineEnd time.Time, queryInterval time.Duration) (*MetricsBaseline, error) {

	// 查询基线期的历史数据
	metrics, err := bc.metricsCollector.QueryMetricsDataRange(ctx, []string{deviceID}, []string{metricType},
		baselineStart, baselineEnd, queryInterval)
	if err != nil {
		return nil, fmt.Errorf("查询基线数据失败: %w", err)
	}

	if len(metrics) == 0 {
		bc.logger.Warn("基线期无数据",
			zap.String("device_id", deviceID),
			zap.String("metric_type", metricType))
		return nil, nil
	}

	// 按指标名称分组计算基线（一个metric_type可能对应多个具体指标）
	groupedMetrics := bc.groupMetricsByName(metrics)

	// 如果有多个指标，选择数据最多的一个
	var targetMetricName string
	var targetMetrics []*MetricData
	maxCount := 0

	for metricName, metricList := range groupedMetrics {
		if len(metricList) > maxCount {
			maxCount = len(metricList)
			targetMetricName = metricName
			targetMetrics = metricList
		}
	}

	if len(targetMetrics) == 0 {
		return nil, nil
	}

	// 计算统计特征
	stats := bc.calculateStatistics(targetMetrics)

	baseline := &MetricsBaseline{
		MetricsSessionID: sessionID,
		DeviceID:         deviceID,
		MetricName:       targetMetricName,
		AvgValue:         &stats.AvgValue,
		MaxValue:         &stats.MaxValue,
		MinValue:         &stats.MinValue,
		StdDev:           &stats.StdDev,
		SampleCount:      &stats.SampleCount,
		TimeRangeStart:   baselineStart,
		TimeRangeEnd:     baselineEnd,
	}

	bc.logger.Debug("计算设备指标基线完成",
		zap.String("device_id", deviceID),
		zap.String("metric_name", targetMetricName),
		zap.Float64("avg_value", stats.AvgValue),
		zap.Int("sample_count", stats.SampleCount))

	return baseline, nil
}

// groupMetricsByName 按指标名称分组
func (bc *BaselineCalculator) groupMetricsByName(metrics []*MetricData) map[string][]*MetricData {
	grouped := make(map[string][]*MetricData)
	for _, metric := range metrics {
		grouped[metric.MetricName] = append(grouped[metric.MetricName], metric)
	}
	return grouped
}

// StatisticsResult 统计结果
type StatisticsResult struct {
	AvgValue    float64
	MaxValue    float64
	MinValue    float64
	StdDev      float64
	SampleCount int
}

// calculateStatistics 计算统计特征
func (bc *BaselineCalculator) calculateStatistics(metrics []*MetricData) *StatisticsResult {
	if len(metrics) == 0 {
		return &StatisticsResult{}
	}

	var sum, min, max float64
	min = metrics[0].Value
	max = metrics[0].Value

	// 计算总和、最小值、最大值
	for _, metric := range metrics {
		sum += metric.Value
		if metric.Value < min {
			min = metric.Value
		}
		if metric.Value > max {
			max = metric.Value
		}
	}

	avg := sum / float64(len(metrics))

	// 计算标准差
	var sumSquaredDiff float64
	for _, metric := range metrics {
		diff := metric.Value - avg
		sumSquaredDiff += diff * diff
	}

	var stdDev float64
	if len(metrics) > 1 {
		variance := sumSquaredDiff / float64(len(metrics)-1)
		stdDev = math.Sqrt(variance)
	}

	return &StatisticsResult{
		AvgValue:    avg,
		MaxValue:    max,
		MinValue:    min,
		StdDev:      stdDev,
		SampleCount: len(metrics),
	}
}

// GetBaseline 获取指标基线数据
func (bc *BaselineCalculator) GetBaseline(sessionID, deviceID, metricName string) (*MetricsBaseline, error) {
	baselines, _, err := bc.dao.GetMetricsBaselinesBySessionID(sessionID, deviceID, metricName, 0, 1)
	if err != nil {
		return nil, err
	}

	if len(baselines) == 0 {
		return nil, nil
	}

	return baselines[0], nil
}

// GetAllBaselines 获取会话的所有基线数据
func (bc *BaselineCalculator) GetAllBaselines(sessionID string) ([]*MetricsBaseline, error) {
	baselines, _, err := bc.dao.GetMetricsBaselinesBySessionID(sessionID, "", "", 0, 1000)
	if err != nil {
		return nil, err
	}

	return baselines, nil
}

// CompareWithBaseline 与基线进行对比
func (bc *BaselineCalculator) CompareWithBaseline(baseline *MetricsBaseline, currentValue float64) *BaselineComparison {
	if baseline == nil || baseline.AvgValue == nil {
		return &BaselineComparison{
			HasBaseline: false,
		}
	}

	avgValue := *baseline.AvgValue
	changeMagnitude := currentValue - avgValue
	var changePercentage float64
	if avgValue != 0 {
		changePercentage = (changeMagnitude / avgValue) * 100
	}

	// 判断是否超出正常范围（基于标准差）
	var isOutlier bool
	if baseline.StdDev != nil && *baseline.StdDev > 0 {
		zScore := math.Abs(changeMagnitude) / *baseline.StdDev
		isOutlier = zScore > 2.0 // 2个标准差之外认为是异常
	}

	return &BaselineComparison{
		HasBaseline:      true,
		BaselineValue:    avgValue,
		CurrentValue:     currentValue,
		ChangeMagnitude:  changeMagnitude,
		ChangePercentage: changePercentage,
		IsOutlier:        isOutlier,
		BaselineStdDev:   baseline.StdDev,
	}
}

// BaselineComparison 基线对比结果
type BaselineComparison struct {
	HasBaseline      bool     `json:"hasBaseline"`
	BaselineValue    float64  `json:"baselineValue"`
	CurrentValue     float64  `json:"currentValue"`
	ChangeMagnitude  float64  `json:"changeMagnitude"`
	ChangePercentage float64  `json:"changePercentage"`
	IsOutlier        bool     `json:"isOutlier"`
	BaselineStdDev   *float64 `json:"baselineStdDev,omitempty"`
}

// CalculateChangeDescription 计算变化描述
func (bc *BaselineCalculator) CalculateChangeDescription(comparison *BaselineComparison) string {
	if !comparison.HasBaseline {
		return "无基线数据"
	}

	magnitude := math.Abs(comparison.ChangePercentage)
	direction := "增加"
	if comparison.ChangeMagnitude < 0 {
		direction = "减少"
	}

	if magnitude < 5 {
		return fmt.Sprintf("相对基线%s%.2f%% (轻微变化)", direction, magnitude)
	} else if magnitude < 20 {
		return fmt.Sprintf("相对基线%s%.2f%% (中等变化)", direction, magnitude)
	} else {
		return fmt.Sprintf("相对基线%s%.2f%% (显著变化)", direction, magnitude)
	}
}

// IsBaselineReady 检查基线是否就绪
func (bc *BaselineCalculator) IsBaselineReady(sessionID string, deviceIDs []string, metricTypes []string) (bool, error) {
	expectedCount := len(deviceIDs) * len(metricTypes)
	baselines, err := bc.GetAllBaselines(sessionID)
	if err != nil {
		return false, err
	}

	actualCount := len(baselines)
	isReady := actualCount >= expectedCount

	bc.logger.Debug("检查基线就绪状态",
		zap.String("session_id", sessionID),
		zap.Int("expected_count", expectedCount),
		zap.Int("actual_count", actualCount),
		zap.Bool("is_ready", isReady))

	return isReady, nil
}
