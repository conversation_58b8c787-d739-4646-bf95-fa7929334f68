package metrics_monitoring

import (
	"time"
)

// ==========================================================================
// 指标监测枚举常量
// ==========================================================================

// 指标监测会话状态枚举
const (
	MetricsStatusMonitoringActive   = "monitoring_active"   // 实时数据记录已启动
	MetricsStatusBaselineAnalyzing  = "baseline_analyzing"  // 基线统计中
	MetricsStatusFullyActive        = "fully_active"        // 基线统计+数据记录全部就绪
	MetricsStatusExtendedMonitoring = "extended_monitoring" // 延展数据记录中
	MetricsStatusCompleted          = "completed"           // 已完成
)

// 监测阶段枚举
const (
	MonitoringPhaseBaseline      = "baseline"       // 基线期
	MonitoringPhaseTaskExecution = "task_execution" // 任务期
	MonitoringPhaseExtended      = "extended"       // 延展期
)

// 记录类型枚举
const (
	ChangeTypeDataRecord        = "data_record"        // 数据记录（当前阶段使用）
	ChangeTypeThresholdExceeded = "threshold_exceeded" // 阈值超限（预留）
	ChangeTypeTrendChange       = "trend_change"       // 趋势变化（预留）
	ChangeTypeSuddenChange      = "sudden_change"      // 突变（预留）
	ChangeTypePatternAnomaly    = "pattern_anomaly"    // 模式异常（预留）
)

// 严重程度枚举
const (
	SeverityInfo     = "info"     // 信息（当前阶段使用）
	SeverityLow      = "low"      // 低（预留）
	SeverityMedium   = "medium"   // 中（预留）
	SeverityHigh     = "high"     // 高（预留）
	SeverityCritical = "critical" // 严重（预留）
)

// ==========================================================================
// 核心数据模型
// ==========================================================================

// MetricsSession 指标监测会话表
type MetricsSession struct {
	SessionID       string     `json:"sessionId" db:"session_id"`
	TaskID          string     `json:"taskId" db:"task_id"`
	TaskSessionID   string     `json:"taskSessionId" db:"task_session_id"`
	DeviceIDs       string     `json:"deviceIds" db:"device_ids"`       // JSON数组存储为字符串
	MetricsTypes    string     `json:"metricsTypes" db:"metrics_types"` // JSON数组存储为字符串
	BaselineStart   time.Time  `json:"baselineStart" db:"baseline_start"`
	BaselineEnd     time.Time  `json:"baselineEnd" db:"baseline_end"`
	MonitoringStart time.Time  `json:"monitoringStart" db:"monitoring_start"`
	MonitoringEnd   *time.Time `json:"monitoringEnd,omitempty" db:"monitoring_end"`
	ExtendedEnd     *time.Time `json:"extendedEnd,omitempty" db:"extended_end"`
	Status          string     `json:"status" db:"status"`
	CreatedAt       time.Time  `json:"createdAt" db:"created_at"`
}

// TableName 返回MetricsSession对应的数据库表名
func (MetricsSession) TableName() string {
	return "monitor_task_metrics_sessions"
}

// MetricsBaseline 指标基线统计表
type MetricsBaseline struct {
	ID               string    `json:"id" db:"id"`
	MetricsSessionID string    `json:"metricsSessionId" db:"metrics_session_id"`
	DeviceID         string    `json:"deviceId" db:"device_id"`
	MetricName       string    `json:"metricName" db:"metric_name"`
	AvgValue         *float64  `json:"avgValue,omitempty" db:"avg_value"`
	MaxValue         *float64  `json:"maxValue,omitempty" db:"max_value"`
	MinValue         *float64  `json:"minValue,omitempty" db:"min_value"`
	StdDev           *float64  `json:"stdDev,omitempty" db:"std_dev"`
	SampleCount      *int      `json:"sampleCount,omitempty" db:"sample_count"`
	TimeRangeStart   time.Time `json:"timeRangeStart" db:"time_range_start"`
	TimeRangeEnd     time.Time `json:"timeRangeEnd" db:"time_range_end"`
	CreatedAt        time.Time `json:"createdAt" db:"created_at"`
}

// TableName 返回MetricsBaseline对应的数据库表名
func (MetricsBaseline) TableName() string {
	return "monitor_task_metrics_baselines"
}

// MetricsChange 指标数据记录表
type MetricsChange struct {
	ID               string    `json:"id" db:"id"`
	MetricsSessionID string    `json:"metricsSessionId" db:"metrics_session_id"`
	DeviceID         string    `json:"deviceId" db:"device_id"`
	MetricName       string    `json:"metricName" db:"metric_name"`
	ChangeType       string    `json:"changeType" db:"change_type"`
	CurrentValue     float64   `json:"currentValue" db:"current_value"`
	BaselineValue    *float64  `json:"baselineValue,omitempty" db:"baseline_value"`
	ChangeMagnitude  *float64  `json:"changeMagnitude,omitempty" db:"change_magnitude"`
	DetectionTime    time.Time `json:"detectionTime" db:"detection_time"`
	MonitoringPhase  string    `json:"monitoringPhase" db:"monitoring_phase"`
	Severity         string    `json:"severity" db:"severity"`
	Description      string    `json:"description,omitempty" db:"description"`
	CreatedAt        time.Time `json:"createdAt" db:"created_at"`
}

// TableName 返回MetricsChange对应的数据库表名
func (MetricsChange) TableName() string {
	return "monitor_task_metrics_changes"
}

// MetricsMonitoringConfig 指标监测配置
type MetricsMonitoringConfig struct {
	BaselineDuration string   `yaml:"baseline_duration"` // 基线期时长
	ExtendedDuration string   `yaml:"extended_duration"` // 延展监测时长
	QueryInterval    string   `yaml:"query_interval"`    // 查询间隔
	BatchSize        int      `yaml:"batch_size"`        // 批量处理大小
	Timeout          string   `yaml:"timeout"`           // 查询超时时间
	SupportedMetrics []string `yaml:"supported_metrics"` // 支持的指标类型
}

// ==========================================================================
// API 请求响应模型
// ==========================================================================

// MetricsStatusResponse 指标监测状态响应
type MetricsStatusResponse struct {
	SessionID       string     `json:"sessionId"`
	TaskID          string     `json:"taskId"`
	TaskSessionID   string     `json:"taskSessionId"`
	DeviceIds       []string   `json:"deviceIds"`
	MetricsTypes    []string   `json:"metricsTypes"`
	BaselineStart   time.Time  `json:"baselineStart"`
	BaselineEnd     time.Time  `json:"baselineEnd"`
	MonitoringStart time.Time  `json:"monitoringStart"`
	MonitoringEnd   *time.Time `json:"monitoringEnd,omitempty"`
	ExtendedEnd     *time.Time `json:"extendedEnd,omitempty"`
	Status          string     `json:"status"`
	CreatedAt       time.Time  `json:"createdAt"`
}

// MetricsStartRequest 手动启动指标监测请求
type MetricsStartRequest struct {
	DeviceIds        []string `json:"deviceIds" binding:"required,min=1"`
	MetricsTypes     []string `json:"metricsTypes" binding:"required,min=1"`
	BaselineDuration string   `json:"baselineDuration,omitempty"`
	ExtendedDuration string   `json:"extendedDuration,omitempty"`
}

// MetricsStartResponse 启动指标监测响应
type MetricsStartResponse struct {
	SessionID string `json:"sessionId"`
	TaskID    string `json:"taskId"`
	Status    string `json:"status"`
	Message   string `json:"message"`
}

// MetricsStopResponse 停止指标监测响应
type MetricsStopResponse struct {
	SessionID string `json:"sessionId"`
	TaskID    string `json:"taskId"`
	Status    string `json:"status"`
	Message   string `json:"message"`
}

// TimeRange 时间范围
type TimeRange struct {
	StartTime time.Time `json:"startTime"`
	EndTime   time.Time `json:"endTime"`
}

// PaginationInfo 分页信息
type PaginationInfo struct {
	Total     int `json:"total"`
	Page      int `json:"page"`
	PageSize  int `json:"pageSize"`
	PageCount int `json:"pageCount"`
}

// MetricsBaselineListResponse 基线查询响应
type MetricsBaselineListResponse struct {
	Items      []*MetricsBaseline `json:"items"`
	Pagination *PaginationInfo    `json:"pagination"`
}

// MetricsComparisonData 指标对比数据
type MetricsComparisonData struct {
	DeviceID       string                    `json:"deviceId"`
	MetricName     string                    `json:"metricName"`
	Baseline       *MetricsStatisticsSummary `json:"baseline"`
	TaskPeriod     *MetricsStatisticsSummary `json:"taskPeriod"`
	ExtendedPeriod *MetricsStatisticsSummary `json:"extendedPeriod"`
	Changes        *MetricsComparisonChanges `json:"changes"`
}

// MetricsStatisticsSummary 统计汇总
type MetricsStatisticsSummary struct {
	AvgValue float64 `json:"avgValue"`
	MaxValue float64 `json:"maxValue"`
	MinValue float64 `json:"minValue"`
}

// MetricsComparisonChanges 对比变化
type MetricsComparisonChanges struct {
	BaselineToTask string `json:"baselineToTask"`
	TaskToExtended string `json:"taskToExtended"`
	OverallChange  string `json:"overallChange"`
}

// MetricsComparisonResponse 指标对比响应
type MetricsComparisonResponse struct {
	Items []*MetricsComparisonData `json:"items"`
}

// MetricsDataListResponse 指标数据记录列表响应
type MetricsDataListResponse struct {
	Items      []*MetricsChange `json:"items"`
	Pagination *PaginationInfo  `json:"pagination"`
}

// MetricsTrendsDataPoint 趋势数据点
type MetricsTrendsDataPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
	Phase     string    `json:"phase"`
}

// MetricsTrendsResponse 指标趋势查询响应
type MetricsTrendsResponse struct {
	DeviceID   string                    `json:"deviceId"`
	MetricName string                    `json:"metricName"`
	TimeRange  TimeRange                 `json:"timeRange"`
	Baseline   *MetricsBaselineSummary   `json:"baseline"`
	DataPoints []*MetricsTrendsDataPoint `json:"dataPoints"`
}

// MetricsBaselineSummary 基线汇总（用于趋势响应）
type MetricsBaselineSummary struct {
	AvgValue  float64   `json:"avgValue"`
	TimeRange TimeRange `json:"timeRange"`
}

// MetricsStatisticsResponse 指标统计汇总响应
type MetricsStatisticsResponse struct {
	TaskID           string                     `json:"taskId"`
	SessionID        string                     `json:"sessionId"`
	Summary          *MetricsOverallSummary     `json:"summary"`
	DeviceStatistics []*MetricsDeviceStatistics `json:"deviceStatistics"`
}

// MetricsOverallSummary 总体汇总
type MetricsOverallSummary struct {
	TotalDevices       int    `json:"totalDevices"`
	TotalMetrics       int    `json:"totalMetrics"`
	MonitoringDuration string `json:"monitoringDuration"`
	DataPoints         int    `json:"dataPoints"`
}

// MetricsDeviceStatistics 设备指标统计
type MetricsDeviceStatistics struct {
	DeviceID   string                 `json:"deviceId"`
	DeviceName string                 `json:"deviceName"`
	Metrics    []*MetricsDeviceMetric `json:"metrics"`
}

// MetricsDeviceMetric 设备指标详情
type MetricsDeviceMetric struct {
	MetricName     string                      `json:"metricName"`
	Baseline       *MetricsStatisticsSummary   `json:"baseline"`
	TaskPeriod     *MetricsStatisticsSummary   `json:"taskPeriod"`
	ExtendedPeriod *MetricsStatisticsSummary   `json:"extendedPeriod"`
	ChangeSummary  *MetricsDeviceChangeSummary `json:"changeSummary"`
}

// MetricsDeviceChangeSummary 设备指标变化汇总
type MetricsDeviceChangeSummary struct {
	MaxIncrease float64 `json:"maxIncrease"`
	AvgChange   float64 `json:"avgChange"`
	Volatility  string  `json:"volatility"`
}

// MetricsDependenciesResponse 数据关联状态响应
type MetricsDependenciesResponse struct {
	TaskID            string                     `json:"taskId"`
	HasMetricsSession bool                       `json:"hasMetricsSession"`
	Dependencies      *MetricsDependenciesDetail `json:"dependencies"`
	CanDelete         bool                       `json:"canDelete"`
}

// MetricsDependenciesDetail 依赖详情
type MetricsDependenciesDetail struct {
	MetricsSession  *MetricsSessionInfo `json:"metricsSession"`
	BaselineRecords *MetricsRecordInfo  `json:"baselineRecords"`
	DataRecords     *MetricsRecordInfo  `json:"dataRecords"`
}

// MetricsSessionInfo 指标会话信息
type MetricsSessionInfo struct {
	SessionID string `json:"sessionId"`
	Status    string `json:"status"`
}

// MetricsRecordInfo 指标记录信息
type MetricsRecordInfo struct {
	Count     int        `json:"count"`
	DeviceIds []string   `json:"deviceIds,omitempty"`
	TimeRange *TimeRange `json:"timeRange,omitempty"`
}

// MetricsDeleteResponse 指标数据删除响应
type MetricsDeleteResponse struct {
	TaskID       string               `json:"taskId"`
	DeletedItems *MetricsDeletedItems `json:"deletedItems"`
	Message      string               `json:"message"`
}

// MetricsDeletedItems 已删除项目统计
type MetricsDeletedItems struct {
	MetricsDataRecords int `json:"metricsDataRecords"`
	BaselineRecords    int `json:"baselineRecords"`
	MetricsSession     int `json:"metricsSession"`
}

// MetricsCleanupResponse 指标数据清理响应
type MetricsCleanupResponse struct {
	TaskID         string `json:"taskId"`
	CleanupBefore  string `json:"cleanupBefore"`
	DeletedRecords int    `json:"deletedRecords"`
	Message        string `json:"message"`
}

// MetricData 性能指标数据
type MetricData struct {
	DeviceID   string            `json:"deviceId"`
	MetricName string            `json:"metricName"`
	Value      float64           `json:"value"`
	Timestamp  time.Time         `json:"timestamp"`
	Labels     map[string]string `json:"labels,omitempty"`
}
