package metrics_monitoring

import (
	"context"
	"dcimonitor/internal/models"
	"dcimonitor/internal/traffic"
	"fmt"
	"strings"
	"time"

	"math"

	"go.uber.org/zap"
)

// MetricsCollector 指标数据收集器
type MetricsCollector struct {
	trafficDAO traffic.TrafficDAO
	config     *MetricsMonitoringConfig
	logger     *zap.Logger
}

// NewMetricsCollector 创建指标数据收集器实例
func NewMetricsCollector(trafficDAO traffic.TrafficDAO, config *MetricsMonitoringConfig, logger *zap.Logger) *MetricsCollector {
	return &MetricsCollector{
		trafficDAO: trafficDAO,
		config:     config,
		logger:     logger,
	}
}

// QueryMetricsData 查询指标数据
func (mc *MetricsCollector) QueryMetricsData(ctx context.Context, deviceIDs []string, metricTypes []string, queryTime time.Time) ([]*MetricData, error) {
	var allMetrics []*MetricData

	for _, deviceID := range deviceIDs {
		for _, metricType := range metricTypes {
			metrics, err := mc.queryDeviceMetrics(ctx, deviceID, metricType, queryTime)
			if err != nil {
				mc.logger.Error("查询设备指标失败",
					zap.String("device_id", deviceID),
					zap.String("metric_type", metricType),
					zap.Error(err))
				continue
			}
			allMetrics = append(allMetrics, metrics...)
		}
	}

	mc.logger.Debug("查询指标数据完成",
		zap.Int("device_count", len(deviceIDs)),
		zap.Int("metric_type_count", len(metricTypes)),
		zap.Int("total_metrics", len(allMetrics)))

	return allMetrics, nil
}

// QueryMetricsDataRange 查询时间范围内的指标数据
func (mc *MetricsCollector) QueryMetricsDataRange(ctx context.Context, deviceIDs []string, metricTypes []string,
	startTime, endTime time.Time, step time.Duration) ([]*MetricData, error) {

	var allMetrics []*MetricData

	for _, deviceID := range deviceIDs {
		for _, metricType := range metricTypes {
			metrics, err := mc.queryDeviceMetricsRange(ctx, deviceID, metricType, startTime, endTime, step)
			if err != nil {
				mc.logger.Error("查询设备指标范围数据失败",
					zap.String("device_id", deviceID),
					zap.String("metric_type", metricType),
					zap.Time("start_time", startTime),
					zap.Time("end_time", endTime),
					zap.Error(err))
				continue
			}
			allMetrics = append(allMetrics, metrics...)
		}
	}

	mc.logger.Debug("查询指标范围数据完成",
		zap.Int("device_count", len(deviceIDs)),
		zap.Int("metric_type_count", len(metricTypes)),
		zap.Time("start_time", startTime),
		zap.Time("end_time", endTime),
		zap.Int("total_metrics", len(allMetrics)))

	return allMetrics, nil
}

// queryDeviceMetrics 查询单个设备的指标数据
func (mc *MetricsCollector) queryDeviceMetrics(ctx context.Context, deviceID, metricType string, queryTime time.Time) ([]*MetricData, error) {
	query := mc.buildPromQLQuery(deviceID, metricType)
	if query == "" {
		mc.logger.Warn("不支持的指标类型", zap.String("metric_type", metricType))
		return nil, fmt.Errorf("不支持的指标类型: %s", metricType)
	}

	// 查询即时数据
	result, err := mc.trafficDAO.QueryRate(ctx, query, queryTime)
	if err != nil {
		return nil, fmt.Errorf("查询Prometheus失败: %w", err)
	}

	// 转换结果
	metrics := mc.convertFloatResult(result, deviceID, metricType, queryTime)
	return metrics, nil
}

// queryDeviceMetricsRange 查询设备指标的时间范围数据
func (mc *MetricsCollector) queryDeviceMetricsRange(ctx context.Context, deviceID, metricType string,
	startTime, endTime time.Time, step time.Duration) ([]*MetricData, error) {

	query := mc.buildPromQLQuery(deviceID, metricType)
	if query == "" {
		mc.logger.Warn("不支持的指标类型", zap.String("metric_type", metricType))
		return nil, fmt.Errorf("不支持的指标类型: %s", metricType)
	}

	// 查询范围数据
	result, err := mc.trafficDAO.QueryVectorOverTime(ctx, query, startTime, endTime, step)
	if err != nil {
		return nil, fmt.Errorf("查询Prometheus范围数据失败: %w", err)
	}

	// 转换结果
	metrics := mc.convertTimeValuePairResult(result, deviceID, metricType)
	return metrics, nil
}

// buildPromQLQuery 构建PromQL查询语句
func (mc *MetricsCollector) buildPromQLQuery(deviceID, metricType string) string {
	switch metricType {
	case "cpu":
		return fmt.Sprintf(`dci_snmp_status_cpu_usage{device_id="%s"}`, deviceID)
	case "memory":
		return fmt.Sprintf(`dci_snmp_status_memory_usage{device_id="%s"}`, deviceID)
	case "traffic_in":
		return fmt.Sprintf(`dci_snmp_flow_ifHCInOctets{device_id="%s"}`, deviceID)
	case "traffic_out":
		return fmt.Sprintf(`dci_snmp_flow_ifHCOutOctets{device_id="%s"}`, deviceID)
	case "traffic":
		// 综合查询入向和出向流量
		return fmt.Sprintf(`{__name__=~"dci_snmp_flow_ifHCInOctets|dci_snmp_flow_ifHCOutOctets",device_id="%s"}`, deviceID)
	case "interface_status":
		return fmt.Sprintf(`dci_snmp_status_interface{device_id="%s"}`, deviceID)
	case "interface_admin":
		return fmt.Sprintf(`dci_snmp_status_interface_admin{device_id="%s"}`, deviceID)
	default:
		mc.logger.Warn("未知的指标类型", zap.String("metric_type", metricType))
		return ""
	}
}

// convertFloatResult 转换浮点数结果
func (mc *MetricsCollector) convertFloatResult(result float64, deviceID, metricType string, queryTime time.Time) []*MetricData {
	metricName := mc.getFullMetricNameFromType(metricType)

	metric := &MetricData{
		DeviceID:   deviceID,
		MetricName: metricName,
		Value:      result,
		Timestamp:  queryTime,
		Labels: map[string]string{
			"device_id": deviceID,
		},
	}

	return []*MetricData{metric}
}

// convertTimeValuePairResult 转换时间值对结果
func (mc *MetricsCollector) convertTimeValuePairResult(result []models.TimeValuePair, deviceID, metricType string) []*MetricData {
	if len(result) == 0 {
		return nil
	}

	var metrics []*MetricData
	metricName := mc.getFullMetricNameFromType(metricType)

	for _, pair := range result {
		metric := &MetricData{
			DeviceID:   deviceID,
			MetricName: metricName,
			Value:      pair.Value,
			Timestamp:  pair.Timestamp,
			Labels: map[string]string{
				"device_id": deviceID,
			},
		}
		metrics = append(metrics, metric)
	}

	return metrics
}

// getFullMetricNameFromType 根据指标类型获取完整的指标名称
func (mc *MetricsCollector) getFullMetricNameFromType(metricType string) string {
	switch metricType {
	case "cpu":
		return "dci_snmp_status_cpu_usage"
	case "memory":
		return "dci_snmp_status_memory_usage"
	case "traffic_in":
		return "dci_snmp_flow_ifHCInOctets"
	case "traffic_out":
		return "dci_snmp_flow_ifHCOutOctets"
	case "interface_status":
		return "dci_snmp_status_interface"
	case "interface_admin":
		return "dci_snmp_status_interface_admin"
	default:
		return metricType
	}
}

// CalculateBasicStatistics 计算基本统计特征
func (mc *MetricsCollector) CalculateBasicStatistics(metrics []*MetricData) *MetricsStatisticsSummary {
	if len(metrics) == 0 {
		return &MetricsStatisticsSummary{}
	}

	var sum, min, max float64
	min = metrics[0].Value
	max = metrics[0].Value

	for _, metric := range metrics {
		sum += metric.Value
		if metric.Value < min {
			min = metric.Value
		}
		if metric.Value > max {
			max = metric.Value
		}
	}

	avg := sum / float64(len(metrics))

	return &MetricsStatisticsSummary{
		AvgValue: avg,
		MaxValue: max,
		MinValue: min,
	}
}

// CalculateStandardDeviation 计算标准差
func (mc *MetricsCollector) CalculateStandardDeviation(metrics []*MetricData, avg float64) float64 {
	if len(metrics) <= 1 {
		return 0.0
	}

	var sumSquaredDiff float64
	for _, metric := range metrics {
		diff := metric.Value - avg
		sumSquaredDiff += diff * diff
	}

	variance := sumSquaredDiff / float64(len(metrics)-1)
	return math.Sqrt(variance)
}

// ValidateMetricTypes 验证指标类型是否支持
func (mc *MetricsCollector) ValidateMetricTypes(metricTypes []string) error {
	supportedTypes := map[string]bool{
		"cpu":              true,
		"memory":           true,
		"traffic":          true,
		"traffic_in":       true,
		"traffic_out":      true,
		"interface_status": true,
		"interface_admin":  true,
	}

	var unsupportedTypes []string
	for _, metricType := range metricTypes {
		if !supportedTypes[metricType] {
			unsupportedTypes = append(unsupportedTypes, metricType)
		}
	}

	if len(unsupportedTypes) > 0 {
		return fmt.Errorf("不支持的指标类型: %s", strings.Join(unsupportedTypes, ", "))
	}

	return nil
}
