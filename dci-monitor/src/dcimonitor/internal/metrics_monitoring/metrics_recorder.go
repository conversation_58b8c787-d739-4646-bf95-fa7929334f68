package metrics_monitoring

import (
	"context"
	"dcimonitor/internal/utils/timeutil"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"
)

// MetricsRecorder 指标数据记录器
type MetricsRecorder struct {
	dao                *DAO
	metricsCollector   *MetricsCollector
	baselineCalculator *BaselineCalculator
	config             *MetricsMonitoringConfig
	logger             *zap.Logger
}

// NewMetricsRecorder 创建指标数据记录器实例
func NewMetricsRecorder(dao *DAO, metricsCollector *MetricsCollector,
	baselineCalculator *BaselineCalculator, config *MetricsMonitoringConfig, logger *zap.Logger) *MetricsRecorder {
	return &MetricsRecorder{
		dao:                dao,
		metricsCollector:   metricsCollector,
		baselineCalculator: baselineCalculator,
		config:             config,
		logger:             logger,
	}
}

// RecordMetricsData 记录指标数据
func (mr *MetricsRecorder) RecordMetricsData(ctx context.Context, sessionID string,
	deviceIDs []string, metricTypes []string, monitoringPhase string) error {

	mr.logger.Debug("开始记录指标数据",
		zap.String("session_id", sessionID),
		zap.String("monitoring_phase", monitoringPhase),
		zap.Int("device_count", len(deviceIDs)),
		zap.Int("metric_type_count", len(metricTypes)))

	// 获取当前时间
	now := timeutil.NowInLocalTime()

	// 查询当前指标数据
	currentMetrics, err := mr.metricsCollector.QueryMetricsData(ctx, deviceIDs, metricTypes, now)
	if err != nil {
		mr.logger.Error("查询当前指标数据失败", zap.Error(err))
		return fmt.Errorf("查询当前指标数据失败: %w", err)
	}

	if len(currentMetrics) == 0 {
		mr.logger.Warn("无当前指标数据", zap.String("session_id", sessionID))
		return nil
	}

	// 检查基线是否就绪
	baselineReady, err := mr.baselineCalculator.IsBaselineReady(sessionID, deviceIDs, metricTypes)
	if err != nil {
		mr.logger.Error("检查基线就绪状态失败", zap.Error(err))
		baselineReady = false
	}

	// 记录指标数据
	var recordedCount int
	for _, metric := range currentMetrics {
		record, err := mr.createMetricsRecord(sessionID, metric, monitoringPhase, baselineReady)
		if err != nil {
			mr.logger.Error("创建指标记录失败",
				zap.String("device_id", metric.DeviceID),
				zap.String("metric_name", metric.MetricName),
				zap.Error(err))
			continue
		}

		err = mr.dao.CreateMetricsChange(record)
		if err != nil {
			mr.logger.Error("保存指标记录失败",
				zap.String("device_id", metric.DeviceID),
				zap.String("metric_name", metric.MetricName),
				zap.Error(err))
			continue
		}
		recordedCount++
	}

	mr.logger.Debug("指标数据记录完成",
		zap.String("session_id", sessionID),
		zap.String("monitoring_phase", monitoringPhase),
		zap.Int("total_metrics", len(currentMetrics)),
		zap.Int("recorded_count", recordedCount),
		zap.Bool("baseline_ready", baselineReady))

	return nil
}

// createMetricsRecord 创建指标记录
func (mr *MetricsRecorder) createMetricsRecord(sessionID string, metric *MetricData,
	monitoringPhase string, baselineReady bool) (*MetricsChange, error) {

	record := &MetricsChange{
		MetricsSessionID: sessionID,
		DeviceID:         metric.DeviceID,
		MetricName:       metric.MetricName,
		ChangeType:       ChangeTypeDataRecord,
		CurrentValue:     metric.Value,
		DetectionTime:    metric.Timestamp,
		MonitoringPhase:  monitoringPhase,
		Severity:         SeverityInfo,
	}

	// 如果基线就绪，进行基线对比
	if baselineReady {
		baseline, err := mr.baselineCalculator.GetBaseline(sessionID, metric.DeviceID, metric.MetricName)
		if err != nil {
			mr.logger.Warn("获取基线数据失败",
				zap.String("device_id", metric.DeviceID),
				zap.String("metric_name", metric.MetricName),
				zap.Error(err))
		} else if baseline != nil && baseline.AvgValue != nil {
			record.BaselineValue = baseline.AvgValue
			changeMagnitude := metric.Value - *baseline.AvgValue
			record.ChangeMagnitude = &changeMagnitude

			// 生成变化描述
			comparison := mr.baselineCalculator.CompareWithBaseline(baseline, metric.Value)
			description := mr.baselineCalculator.CalculateChangeDescription(comparison)
			record.Description = description
		}
	}

	// 如果没有基线对比，使用简单描述
	if record.Description == "" {
		record.Description = fmt.Sprintf("%s期间%s当前值记录",
			mr.getPhaseDescription(monitoringPhase), metric.MetricName)
	}

	return record, nil
}

// getPhaseDescription 获取阶段描述
func (mr *MetricsRecorder) getPhaseDescription(phase string) string {
	switch phase {
	case MonitoringPhaseBaseline:
		return "基线期"
	case MonitoringPhaseTaskExecution:
		return "任务执行"
	case MonitoringPhaseExtended:
		return "延展监测"
	default:
		return "未知阶段"
	}
}

// RecordPeriodicMetrics 定期记录指标数据
func (mr *MetricsRecorder) RecordPeriodicMetrics(ctx context.Context, sessionID string,
	deviceIDs []string, metricTypes []string, monitoringPhase string,
	interval time.Duration, stopCh <-chan struct{}) {

	mr.logger.Info("开始定期记录指标数据",
		zap.String("session_id", sessionID),
		zap.String("monitoring_phase", monitoringPhase),
		zap.Duration("interval", interval))

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			mr.logger.Info("定期记录因上下文取消而停止", zap.String("session_id", sessionID))
			return
		case <-stopCh:
			mr.logger.Info("定期记录因停止信号而停止", zap.String("session_id", sessionID))
			return
		case <-ticker.C:
			err := mr.RecordMetricsData(ctx, sessionID, deviceIDs, metricTypes, monitoringPhase)
			if err != nil {
				mr.logger.Error("定期记录指标数据失败",
					zap.String("session_id", sessionID),
					zap.Error(err))
			}
		}
	}
}

// GetMetricsDataStatistics 获取指标数据统计
func (mr *MetricsRecorder) GetMetricsDataStatistics(sessionID, deviceID, metricName string) (*MetricsDataStatistics, error) {
	// 查询所有阶段的数据
	allChanges, _, err := mr.dao.GetMetricsChangesBySessionID(sessionID, deviceID, metricName, "", nil, nil, 0, 10000)
	if err != nil {
		return nil, err
	}

	if len(allChanges) == 0 {
		return &MetricsDataStatistics{
			DeviceID:   deviceID,
			MetricName: metricName,
		}, nil
	}

	// 按阶段分组统计
	stats := &MetricsDataStatistics{
		DeviceID:   deviceID,
		MetricName: metricName,
	}

	phaseGroups := mr.groupChangesByPhase(allChanges)

	if baseline, exists := phaseGroups[MonitoringPhaseBaseline]; exists && len(baseline) > 0 {
		stats.BaselinePeriod = mr.calculatePhaseStatistics(baseline)
	}

	if task, exists := phaseGroups[MonitoringPhaseTaskExecution]; exists && len(task) > 0 {
		stats.TaskPeriod = mr.calculatePhaseStatistics(task)
	}

	if extended, exists := phaseGroups[MonitoringPhaseExtended]; exists && len(extended) > 0 {
		stats.ExtendedPeriod = mr.calculatePhaseStatistics(extended)
	}

	// 计算整体统计
	stats.Overall = mr.calculatePhaseStatistics(allChanges)

	// 计算变化汇总
	stats.ChangeSummary = mr.calculateChangeSummary(allChanges)

	return stats, nil
}

// groupChangesByPhase 按阶段分组
func (mr *MetricsRecorder) groupChangesByPhase(changes []*MetricsChange) map[string][]*MetricsChange {
	groups := make(map[string][]*MetricsChange)
	for _, change := range changes {
		groups[change.MonitoringPhase] = append(groups[change.MonitoringPhase], change)
	}
	return groups
}

// calculatePhaseStatistics 计算阶段统计
func (mr *MetricsRecorder) calculatePhaseStatistics(changes []*MetricsChange) *MetricsPhaseStatistics {
	if len(changes) == 0 {
		return &MetricsPhaseStatistics{}
	}

	var sum, min, max float64
	min = changes[0].CurrentValue
	max = changes[0].CurrentValue

	for _, change := range changes {
		sum += change.CurrentValue
		if change.CurrentValue < min {
			min = change.CurrentValue
		}
		if change.CurrentValue > max {
			max = change.CurrentValue
		}
	}

	avg := sum / float64(len(changes))

	return &MetricsPhaseStatistics{
		AvgValue:   avg,
		MaxValue:   max,
		MinValue:   min,
		DataPoints: len(changes),
		StartTime:  changes[0].DetectionTime,
		EndTime:    changes[len(changes)-1].DetectionTime,
	}
}

// calculateChangeSummary 计算变化汇总
func (mr *MetricsRecorder) calculateChangeSummary(changes []*MetricsChange) *MetricsChangeSummary {
	if len(changes) == 0 {
		return &MetricsChangeSummary{}
	}

	var maxIncrease, maxDecrease, totalChange float64
	var hasBaselineCount int

	for _, change := range changes {
		if change.ChangeMagnitude != nil {
			magnitude := *change.ChangeMagnitude
			totalChange += magnitude

			if magnitude > maxIncrease {
				maxIncrease = magnitude
			}
			if magnitude < maxDecrease {
				maxDecrease = magnitude
			}
			hasBaselineCount++
		}
	}

	var avgChange float64
	if hasBaselineCount > 0 {
		avgChange = totalChange / float64(hasBaselineCount)
	}

	// 计算波动性
	volatility := "低"
	if hasBaselineCount > 0 {
		changeRange := maxIncrease - maxDecrease
		firstValue := changes[0].CurrentValue
		if firstValue > 0 {
			changeRatio := changeRange / firstValue
			if changeRatio > 0.5 {
				volatility = "高"
			} else if changeRatio > 0.2 {
				volatility = "中"
			}
		}
	}

	return &MetricsChangeSummary{
		MaxIncrease: maxIncrease,
		MaxDecrease: maxDecrease,
		AvgChange:   avgChange,
		Volatility:  volatility,
	}
}

// MetricsDataStatistics 指标数据统计
type MetricsDataStatistics struct {
	DeviceID       string                  `json:"deviceId"`
	MetricName     string                  `json:"metricName"`
	BaselinePeriod *MetricsPhaseStatistics `json:"baselinePeriod,omitempty"`
	TaskPeriod     *MetricsPhaseStatistics `json:"taskPeriod,omitempty"`
	ExtendedPeriod *MetricsPhaseStatistics `json:"extendedPeriod,omitempty"`
	Overall        *MetricsPhaseStatistics `json:"overall"`
	ChangeSummary  *MetricsChangeSummary   `json:"changeSummary"`
}

// MetricsPhaseStatistics 阶段统计
type MetricsPhaseStatistics struct {
	AvgValue   float64   `json:"avgValue"`
	MaxValue   float64   `json:"maxValue"`
	MinValue   float64   `json:"minValue"`
	DataPoints int       `json:"dataPoints"`
	StartTime  time.Time `json:"startTime"`
	EndTime    time.Time `json:"endTime"`
}

// MetricsChangeSummary 变化汇总
type MetricsChangeSummary struct {
	MaxIncrease float64 `json:"maxIncrease"`
	MaxDecrease float64 `json:"maxDecrease"`
	AvgChange   float64 `json:"avgChange"`
	Volatility  string  `json:"volatility"`
}

// GenerateTrendsData 生成趋势数据
func (mr *MetricsRecorder) GenerateTrendsData(sessionID, deviceID, metricName string,
	interval time.Duration) ([]*MetricsTrendsDataPoint, error) {

	// 查询所有数据记录
	changes, _, err := mr.dao.GetMetricsChangesBySessionID(sessionID, deviceID, metricName, "", nil, nil, 0, 10000)
	if err != nil {
		return nil, err
	}

	if len(changes) == 0 {
		return nil, nil
	}

	// 转换为趋势数据点
	var dataPoints []*MetricsTrendsDataPoint
	for _, change := range changes {
		point := &MetricsTrendsDataPoint{
			Timestamp: change.DetectionTime,
			Value:     change.CurrentValue,
			Phase:     change.MonitoringPhase,
		}
		dataPoints = append(dataPoints, point)
	}

	return dataPoints, nil
}

// BatchRecordMetrics 批量记录指标数据
func (mr *MetricsRecorder) BatchRecordMetrics(ctx context.Context, sessionID string,
	metrics []*MetricData, monitoringPhase string, baselineReady bool) error {

	if len(metrics) == 0 {
		return nil
	}

	var records []*MetricsChange
	for _, metric := range metrics {
		record, err := mr.createMetricsRecord(sessionID, metric, monitoringPhase, baselineReady)
		if err != nil {
			mr.logger.Warn("创建指标记录失败",
				zap.String("device_id", metric.DeviceID),
				zap.String("metric_name", metric.MetricName),
				zap.Error(err))
			continue
		}
		records = append(records, record)
	}

	// 批量保存 (这里简化为循环调用，实际可以实现真正的批量插入)
	var successCount int
	for _, record := range records {
		err := mr.dao.CreateMetricsChange(record)
		if err != nil {
			mr.logger.Error("批量保存指标记录失败",
				zap.String("device_id", record.DeviceID),
				zap.String("metric_name", record.MetricName),
				zap.Error(err))
			continue
		}
		successCount++
	}

	mr.logger.Info("批量记录指标数据完成",
		zap.String("session_id", sessionID),
		zap.String("monitoring_phase", monitoringPhase),
		zap.Int("total_metrics", len(metrics)),
		zap.Int("success_count", successCount))

	return nil
}

// ValidateMetricsData 验证指标数据
func (mr *MetricsRecorder) ValidateMetricsData(metric *MetricData) error {
	if metric.DeviceID == "" {
		return fmt.Errorf("设备ID不能为空")
	}
	if metric.MetricName == "" {
		return fmt.Errorf("指标名称不能为空")
	}
	if metric.Timestamp.IsZero() {
		return fmt.Errorf("时间戳不能为空")
	}

	// 检查指标名称是否合法
	validMetricPrefixes := []string{
		"dci_snmp_status_",
		"dci_snmp_flow_",
	}

	valid := false
	for _, prefix := range validMetricPrefixes {
		if strings.HasPrefix(metric.MetricName, prefix) {
			valid = true
			break
		}
	}

	if !valid {
		return fmt.Errorf("指标名称格式不正确: %s", metric.MetricName)
	}

	return nil
}
