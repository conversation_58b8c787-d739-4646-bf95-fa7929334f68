#!/bin/bash

# 测试Kafka消息发送脚本
# 用于测试流量数据处理服务

# Kafka配置
KAFKA_BROKERS="dcikafka.intra.citic-x.com:30010,dcikafka.intra.citic-x.com:30011,dcikafka.intra.citic-x.com:30012"
KAFKA_TOPIC="dci.monitor.v1.defaultchannel.flows.snmp"

# 临时文件
TEMP_FILE="/tmp/test_kafka_message.json"

# 测试消息 - 修正格式以符合FlowData结构体
cat > "$TEMP_FILE" << 'EOF'
{
  "name": "interface",
  "tags": {
    "device_ip": "************",
    "host": "test-host",
    "ifDescr": "10GE1/0/1",
    "ifIndex": "11",
    "sysName": "SW1"
  },
  "fields": {
    "ifAdminStatus": 1,
    "ifDescr": "10GE1/0/1",
    "ifInDiscards": 0,
    "ifInErrors": 0,
    "ifHCInOctets": 116314713,
    "ifOutDiscards": 0,
    "ifOutErrors": 0,
    "ifHCOutOctets": 21501364,
    "if_index": "11"
  },
  "timestamp": 1749015021
}
EOF

echo "准备发送测试消息到Kafka..."
echo "主题: $KAFKA_TOPIC"
echo "消息内容已保存到: $TEMP_FILE"

# 使用kafkacat发送消息
if command -v kafkacat > /dev/null 2>&1; then
    echo "使用kafkacat发送消息..."
    kafkacat -P -b $KAFKA_BROKERS -t $KAFKA_TOPIC -l "$TEMP_FILE"
    echo "消息已发送"
elif command -v kcat > /dev/null 2>&1; then
    echo "使用kcat发送消息..."
    kcat -P -b $KAFKA_BROKERS -t $KAFKA_TOPIC -l "$TEMP_FILE"
    echo "消息已发送"
else
    echo "未找到kafkacat或kcat工具，请安装后再试"
    echo "可以使用以下命令安装:"
    echo "  brew install kcat    # macOS"
    echo "  apt install kafkacat # Ubuntu/Debian"
    echo "  yum install kafkacat # CentOS/RHEL"
    exit 1
fi

echo "测试完成" 