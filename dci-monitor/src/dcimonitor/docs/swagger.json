{"schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "DCI监控系统提供的告警、设备状态、流量监测等API接口", "title": "DCI监控系统API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "DCI监控团队", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/", "paths": {"/api/v1/alert-rules": {"get": {"description": "获取系统中配置的告警规则列表", "produces": ["application/json"], "tags": ["告警规则"], "summary": "获取告警规则列表", "parameters": [{"type": "string", "description": "规则来源", "name": "source", "in": "query"}, {"type": "boolean", "description": "是否启用", "name": "enabled", "in": "query"}], "responses": {"200": {"description": "规则列表", "schema": {"$ref": "#/definitions/alert.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/alert.Response"}}}}, "post": {"description": "创建新的告警规则", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["告警规则"], "summary": "创建告警规则", "parameters": [{"description": "规则定义", "name": "rule", "in": "body", "required": true, "schema": {"$ref": "#/definitions/alert.AlertRule"}}], "responses": {"200": {"description": "创建成功", "schema": {"allOf": [{"$ref": "#/definitions/alert.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/alert.AlertRule"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/alert.Response"}}, "409": {"description": "同名规则已存在", "schema": {"$ref": "#/definitions/alert.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/alert.Response"}}}}}, "/api/v1/alert-rules/{id}": {"get": {"description": "根据规则ID获取告警规则详细信息", "produces": ["application/json"], "tags": ["告警规则"], "summary": "获取告警规则详情", "parameters": [{"type": "string", "description": "规则ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "规则详情", "schema": {"allOf": [{"$ref": "#/definitions/alert.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/alert.AlertRule"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/alert.Response"}}, "404": {"description": "规则不存在", "schema": {"$ref": "#/definitions/alert.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/alert.Response"}}}}, "put": {"description": "更新现有告警规则", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["告警规则"], "summary": "更新告警规则", "parameters": [{"type": "string", "description": "规则ID", "name": "id", "in": "path", "required": true}, {"description": "规则定义", "name": "rule", "in": "body", "required": true, "schema": {"$ref": "#/definitions/alert.AlertRule"}}], "responses": {"200": {"description": "更新成功", "schema": {"allOf": [{"$ref": "#/definitions/alert.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/alert.AlertRule"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/alert.Response"}}, "404": {"description": "规则不存在", "schema": {"$ref": "#/definitions/alert.Response"}}, "409": {"description": "同名规则已存在", "schema": {"$ref": "#/definitions/alert.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/alert.Response"}}}}, "delete": {"description": "删除现有告警规则", "produces": ["application/json"], "tags": ["告警规则"], "summary": "删除告警规则", "parameters": [{"type": "string", "description": "规则ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"$ref": "#/definitions/alert.Response"}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/alert.Response"}}, "404": {"description": "规则不存在", "schema": {"$ref": "#/definitions/alert.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/alert.Response"}}}}}, "/api/v1/alert-rules/{id}/toggle": {"put": {"description": "切换告警规则的启用状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["告警规则"], "summary": "启用/禁用告警规则", "parameters": [{"type": "string", "description": "规则ID", "name": "id", "in": "path", "required": true}, {"description": "状态设置", "name": "toggle", "in": "body", "required": true, "schema": {"$ref": "#/definitions/alert.AlertRuleToggleRequest"}}], "responses": {"200": {"description": "操作成功", "schema": {"$ref": "#/definitions/alert.Response"}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/alert.Response"}}, "404": {"description": "规则不存在", "schema": {"$ref": "#/definitions/alert.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/alert.Response"}}}}}, "/api/v1/alerts": {"get": {"description": "获取符合筛选条件的告警列表，支持分页", "produces": ["application/json"], "tags": ["告警管理"], "summary": "获取告警列表", "parameters": [{"type": "string", "description": "告警来源", "name": "source", "in": "query"}, {"type": "string", "description": "告警级别(critical/warning/info)", "name": "level", "in": "query"}, {"type": "string", "description": "告警状态(firing/acknowledged/resolved)", "name": "status", "in": "query"}, {"type": "string", "description": "设备ID", "name": "device_id", "in": "query"}, {"type": "integer", "description": "页码(默认1)", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量(默认20，最大100)", "name": "page_size", "in": "query"}], "responses": {"200": {"description": "成功返回", "schema": {"allOf": [{"$ref": "#/definitions/alert.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/alert.AlertListResponse"}}}]}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/alert.Response"}}}}}, "/api/v1/alerts/statistics": {"get": {"description": "获取告警的各种统计数据，包括按状态、级别和时间的统计", "produces": ["application/json"], "tags": ["告警管理"], "summary": "获取告警统计", "responses": {"200": {"description": "统计数据", "schema": {"$ref": "#/definitions/alert.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/alert.Response"}}}}}, "/api/v1/alerts/webhook/prometheus": {"post": {"description": "接收并处理Prometheus AlertManager发送的告警通知", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["告警管理"], "summary": "处理Prometheus告警Webhook", "parameters": [{"description": "Prometheus告警数据", "name": "webhook", "in": "body", "required": true, "schema": {"$ref": "#/definitions/alert.PrometheusWebhookRequest"}}], "responses": {"200": {"description": "成功处理", "schema": {"$ref": "#/definitions/alert.Response"}}, "400": {"description": "请求格式错误", "schema": {"$ref": "#/definitions/alert.Response"}}}}}, "/api/v1/alerts/{id}": {"get": {"description": "根据告警ID获取详细信息", "produces": ["application/json"], "tags": ["告警管理"], "summary": "获取告警详情", "parameters": [{"type": "string", "description": "告警ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "成功返回", "schema": {"allOf": [{"$ref": "#/definitions/alert.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/alert.Alert"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/alert.Response"}}, "404": {"description": "告警不存在", "schema": {"$ref": "#/definitions/alert.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/alert.Response"}}}}}, "/api/v1/alerts/{id}/acknowledge": {"put": {"description": "标记告警为已确认状态，表明有人正在处理该告警", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["告警管理"], "summary": "确认告警", "parameters": [{"type": "string", "example": "\"a1b2c3d4-e5f6-7890-abcd-ef1234567890\"", "description": "告警ID", "name": "id", "in": "path", "required": true}, {"description": "告警确认请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/alert.AlertAcknowledgeRequest"}}], "responses": {"200": {"description": "告警确认成功\" example({\"code\":200,\"message\":\"告警已确认\"})", "schema": {"$ref": "#/definitions/alert.AlertAcknowledgeResponse"}}, "400": {"description": "请求参数错误\" example({\"code\":400,\"data\":{\"message\":\"请求体不能为空\"}})", "schema": {"$ref": "#/definitions/models.Response"}}, "404": {"description": "告警不存在\" example({\"code\":404,\"data\":{\"message\":\"告警不存在\"}})", "schema": {"$ref": "#/definitions/models.Response"}}, "500": {"description": "服务器内部错误\" example({\"code\":500,\"data\":{\"message\":\"确认告警失败: 数据库错误\"}})", "schema": {"$ref": "#/definitions/models.Response"}}}}}, "/api/v1/switches/{deviceID}": {"get": {"description": "根据提供的设备 ID，获取该交换机的当前详细状态信息，包括 CPU 利用率、内存利用率以及所有端口的基本状态列表。", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["05-设备监控"], "summary": "获取交换机完整状态", "parameters": [{"type": "string", "example": "CE1", "description": "要查询状态的设备 ID", "name": "deviceID", "in": "path", "required": true}], "responses": {"200": {"description": "成功获取交换机状态", "schema": {"$ref": "#/definitions/device.SwitchStatus"}}, "404": {"description": "交换机未找到", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/switches/{deviceID}/ports/{ifName}": {"get": {"description": "根据路径参数 `deviceID`, `ifName` 和查询参数 `data` 获取特定端口的信息。\n注意: `ifName` 参数使用Base64编码，是为了安全地在URL路径中传递包含特殊字符（如'/'）的接口名称。\n例如，接口 \"10GE1/0/1\" 应当编码为 \"MTBHRS8wLzE=\"。", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["05-设备监控"], "summary": "获取端口数据 (状态/上行/下行)", "parameters": [{"type": "string", "example": "210", "description": "端口所属的设备 ID", "name": "deviceID", "in": "path", "required": true}, {"type": "string", "example": "{{'10GE1/0/1'|base64}}", "description": "要查询的端口名称，使用Base64编码。例如，将 '10GE1/0/1' 编码为 'MTBHRS8wLzE='", "name": "ifName", "in": "path", "required": true}, {"enum": ["status", "upstream", "downstream"], "type": "string", "default": "status", "description": "请求的数据类型。如果省略或提供无效值, 默认为 status。", "name": "data", "in": "query"}], "responses": {"200": {"description": "当 data=downstream 时的响应，包含下行流量数据：totalOutBytes, totalOutPkts, outUtil等", "schema": {"$ref": "#/definitions/device.PortDownstreamTraffic"}}, "400": {"description": "无效的请求参数", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "指定的设备或端口未找到", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/switches/{deviceID}/ports/{ifName}/admin": {"get": {"description": "根据路径参数 `deviceID` 和 `ifName` 获取特定端口的管理状态信息。\n注意: ifName 使用 Base64 编码，不再支持特殊字符的 URL 编码方式。\n例如：\"100GE1/0/6\" 编码为 \"MTAwR0UxLzAvNg==\"", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["05-设备监控"], "summary": "获取端口管理状态", "parameters": [{"type": "string", "example": "210", "description": "端口所属的设备 ID", "name": "deviceID", "in": "path", "required": true}, {"type": "string", "example": "MTAwR0UxLzAvNg==", "description": "要查询的端口名称，使用Base64编码", "name": "ifName", "in": "path", "required": true}], "responses": {"200": {"description": "成功获取端口管理状态", "schema": {"$ref": "#/definitions/device.PortAdminStatus"}}, "400": {"description": "无效的请求参数", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "指定的设备或端口未找到", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/switches/{deviceID}/ports/{ifName}/flow": {"get": {"description": "查询单个端口在指定时间范围内的平均速率和总流量。\n时间范围通过 `start_time` 和 `end_time` 指定，格式为 RFC3339 UTC。如果未提供，则默认为最近15分钟。\n注意: ifName 使用 Base64 编码，不再支持特殊字符的 URL 编码方式。", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["07-性能管理-单端口流量查询"], "summary": "查询单端口实时流量", "parameters": [{"type": "string", "example": "210", "description": "设备ID", "name": "deviceID", "in": "path", "required": true}, {"type": "string", "example": "MTAwR0UxLzAvNg==", "description": "端口名称，使用Base64编码", "name": "ifName", "in": "path", "required": true}, {"type": "string", "example": "6005002", "description": "VNI (Virtual Network Identifier) 标识符，用于进一步筛选流量", "name": "vni", "in": "query"}, {"type": "string", "example": "2025-06-04T10:00:00Z", "description": "查询起始时间 (RFC3339 UTC 格式)。默认为15分钟前", "name": "start_time", "in": "query"}, {"type": "string", "example": "2025-06-06T10:15:00Z", "description": "查询结束时间 (RFC3339 UTC 格式)。默认为当前时间", "name": "end_time", "in": "query"}], "responses": {"200": {"description": "成功返回端口流量数据", "schema": {"$ref": "#/definitions/traffic.PortFlowResponse"}}, "400": {"description": "请求参数无效", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/switches/{deviceID}/ports/{ifName}/flow/history": {"get": {"description": "查询单个端口在指定时间范围内的历史流量速率时间序列。\n时间范围通过 `start_time` 和 `end_time` 指定，格式为 RFC3339 UTC。\n`step` 参数定义了数据点之间的时间间隔。\n注意: ifName 使用 Base64 编码，不再支持特殊字符的 URL 编码方式。", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["07-性能管理-单端口流量查询"], "summary": "查询单端口历史流量", "parameters": [{"type": "string", "example": "210", "description": "设备ID", "name": "deviceID", "in": "path", "required": true}, {"type": "string", "example": "{{'10GE1/0/1'|base64}}", "description": "端口名称，使用Base64编码", "name": "ifName", "in": "path", "required": true}, {"type": "string", "example": "6005002", "description": "VNI (Virtual Network Identifier) 标识符，用于进一步筛选流量", "name": "vni", "in": "query"}, {"type": "string", "example": "2025-06-18T15:00:00Z", "description": "查询起始时间 (RFC3339 UTC 格式)", "name": "start_time", "in": "query", "required": true}, {"type": "string", "example": "2025-06-18T16:15:00Z", "description": "查询结束时间 (RFC3339 UTC 格式)", "name": "end_time", "in": "query", "required": true}, {"type": "string", "example": "5m", "description": "查询步长, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。默认为'1m'", "name": "step", "in": "query"}], "responses": {"200": {"description": "成功返回端口历史流量数据", "schema": {"$ref": "#/definitions/traffic.PortHistoryResponse"}}, "400": {"description": "请求参数无效", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/system/task-collaboration/collections/{sessionId}": {"get": {"description": "查询指定会话的数据收集状态详情", "produces": ["application/json"], "tags": ["系统管理"], "summary": "获取数据收集状态", "parameters": [{"type": "string", "description": "会话ID", "name": "sessionId", "in": "path", "required": true}], "responses": {"200": {"description": "查询成功", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": true}}}]}}, "404": {"description": "会话不存在", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ErrorResponse"}}}]}}, "500": {"description": "服务器内部错误", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ErrorResponse"}}}]}}}}}, "/api/v1/system/task-collaboration/status": {"get": {"description": "查询任务协同监控系统的运行状态", "produces": ["application/json"], "tags": ["系统管理"], "summary": "获取系统状态", "responses": {"200": {"description": "查询成功", "schema": {"$ref": "#/definitions/models.Response"}}}}}, "/api/v1/tasks/signals": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["任务协同监控"], "parameters": [{"description": "任务信号", "name": "signal", "in": "body", "required": true, "schema": {"$ref": "#/definitions/task_collaboration.TaskSignal"}}], "responses": {"200": {"description": "信号处理成功\" example({\"code\":200,\"data\":{\"success\":true,\"sessionId\":\"session-12345\",\"message\":\"任务监控会话已创建\",\"taskId\":\"550e8400-e29b-41d4-a716-446655440000\"}})", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/task_collaboration.TaskSignalResponse"}}}]}}, "400": {"description": "请求参数错误\" example({\"code\":400,\"data\":{\"error\":\"请求参数格式错误\",\"request_id\":\"789fe92e-6e44-4df4-8734-0bbcc59a2689\",\"details\":\"invalid signal_type: must be one of [start end abort]\"}})", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ErrorResponse"}}}]}}, "500": {"description": "服务器内部错误\" example({\"code\":500,\"data\":{\"error\":\"处理任务信号失败\",\"request_id\":\"789fe92e-6e44-4df4-8734-0bbcc59a2689\",\"details\":\"database connection error\"}})", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ErrorResponse"}}}]}}}}}, "/api/v1/tasks/{taskId}/alerts": {"get": {"description": "查询指定任务执行期间相关的告警列表，包括任务执行期间和延展监测期的告警", "produces": ["application/json"], "tags": ["任务协同监控"], "summary": "获取任务相关告警", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-446655440000\"", "description": "任务ID", "name": "taskId", "in": "path", "required": true}, {"type": "string", "example": "\"2025-01-11T10:15:30Z\"", "description": "开始时间（RFC3339格式）", "name": "start_time", "in": "query"}, {"type": "string", "example": "\"2025-01-11T11:45:30Z\"", "description": "结束时间（RFC3339格式）", "name": "end_time", "in": "query"}, {"type": "integer", "default": 20, "example": 20, "description": "每页数量", "name": "limit", "in": "query"}, {"type": "integer", "default": 0, "example": 0, "description": "偏移量", "name": "offset", "in": "query"}], "responses": {"200": {"description": "查询成功\" example({\"code\":200,\"data\":{\"taskId\":\"550e8400-e29b-41d4-a716-446655440000\",\"alerts\":[{\"id\":\"alert-001\",\"name\":\"Interface Down\",\"level\":\"critical\",\"status\":\"firing\",\"deviceId\":\"CE1\",\"startsAt\":\"2025-01-11T10:20:00Z\",\"endsAt\":null,\"taskRelation\":\"任务执行期间\",\"monitoringPhase\":\"active_execution\"}],\"statistics\":{\"totalCount\":5,\"levelCount\":{\"critical\":1,\"warning\":3,\"info\":1},\"phaseCount\":{\"active_execution\":4,\"post_completion\":1}},\"pagination\":{\"total\":5,\"limit\":20,\"offset\":0}}})", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/task_collaboration.TaskAlertListResponse"}}}]}}, "400": {"description": "请求参数错误\" example({\"code\":400,\"data\":{\"error\":\"请求参数格式错误\",\"request_id\":\"789fe92e-6e44-4df4-8734-0bbcc59a2689\",\"details\":\"invalid time format\"}})", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ErrorResponse"}}}]}}, "500": {"description": "服务器内部错误\" example({\"code\":500,\"data\":{\"error\":\"获取任务告警失败\",\"request_id\":\"789fe92e-6e44-4df4-8734-0bbcc59a2689\",\"details\":\"database query error\"}})", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ErrorResponse"}}}]}}}}}, "/api/v1/tasks/{taskId}/alerts/statistics": {"get": {"description": "查询指定任务执行期间的告警统计信息，包括按级别、状态、阶段和设备的统计分布", "produces": ["application/json"], "tags": ["任务协同监控"], "summary": "获取任务告警统计", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-446655440000\"", "description": "任务ID", "name": "taskId", "in": "path", "required": true}, {"type": "string", "example": "\"2025-01-11T10:15:30Z\"", "description": "开始时间（RFC3339格式）", "name": "start_time", "in": "query"}, {"type": "string", "example": "\"2025-01-11T11:45:30Z\"", "description": "结束时间（RFC3339格式）", "name": "end_time", "in": "query"}], "responses": {"200": {"description": "查询成功\" example({\"code\":200,\"data\":{\"taskId\":\"550e8400-e29b-41d4-a716-446655440000\",\"timeRange\":{\"startTime\":\"2025-01-11T10:15:30Z\",\"endTime\":\"2025-01-11T11:45:30Z\"},\"totalCount\":5,\"levelCount\":{\"critical\":1,\"warning\":3,\"info\":1},\"statusCount\":{\"firing\":2,\"resolved\":3},\"phaseCount\":{\"active_execution\":4,\"post_completion\":1},\"deviceCount\":{\"CE1\":3,\"CE2\":2},\"timeDistribution\":[{\"timeSlot\":\"2025-01-11T10:15:00Z\",\"count\":2},{\"timeSlot\":\"2025-01-11T10:30:00Z\",\"count\":3}]}})", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/task_collaboration.TaskAlertStatistics"}}}]}}, "400": {"description": "请求参数错误\" example({\"code\":400,\"data\":{\"error\":\"请求参数格式错误\",\"request_id\":\"789fe92e-6e44-4df4-8734-0bbcc59a2689\",\"details\":\"invalid time format\"}})", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ErrorResponse"}}}]}}, "500": {"description": "服务器内部错误\" example({\"code\":500,\"data\":{\"error\":\"获取任务告警统计失败\",\"request_id\":\"789fe92e-6e44-4df4-8734-0bbcc59a2689\",\"details\":\"database query error\"}})", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ErrorResponse"}}}]}}}}}, "/api/v1/tasks/{taskId}/metrics-baseline": {"get": {"description": "查询任务的指标基线统计数据，返回任务执行前30分钟的指标统计特征数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["指标基线查询"], "summary": "查询指标基线统计数据", "parameters": [{"type": "string", "example": "\"task_20250118_001\"", "description": "任务ID", "name": "taskId", "in": "path", "required": true}, {"type": "string", "example": "\"device_001\"", "description": "设备ID筛选", "name": "device_id", "in": "query"}, {"type": "string", "example": "\"dci_snmp_status_cpu_usage\"", "description": "指标名称筛选", "name": "metric_name", "in": "query"}, {"minimum": 1, "type": "integer", "default": 1, "description": "页码", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "default": 20, "description": "每页数量", "name": "page_size", "in": "query"}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.MetricsBaselineListResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "404": {"description": "未找到指标监测会话", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "500": {"description": "服务器内部错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}}}}, "/api/v1/tasks/{taskId}/metrics-comparison": {"get": {"description": "查询任务前后的指标对比数据，返回基线值与实时值的对比数据，供前端展示和人工分析", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["指标基线查询"], "summary": "查询指标对比数据", "parameters": [{"type": "string", "example": "\"task_20250118_001\"", "description": "任务ID", "name": "taskId", "in": "path", "required": true}, {"type": "string", "example": "\"device_001\"", "description": "设备ID筛选", "name": "device_id", "in": "query"}, {"type": "string", "example": "\"dci_snmp_status_cpu_usage\"", "description": "指标名称筛选", "name": "metric_name", "in": "query"}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.MetricsComparisonResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "404": {"description": "未找到指标监测会话", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "500": {"description": "服务器内部错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}}}}, "/api/v1/tasks/{taskId}/metrics-data": {"get": {"description": "查询任务期间的指标数据记录（时序数据），支持按设备、指标类型、监测阶段、时间范围筛选", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["指标数据查询"], "summary": "查询指标数据记录", "parameters": [{"type": "string", "example": "\"task_20250118_001\"", "description": "任务ID", "name": "taskId", "in": "path", "required": true}, {"type": "string", "example": "\"device_001\"", "description": "设备ID筛选", "name": "device_id", "in": "query"}, {"type": "string", "example": "\"dci_snmp_status_cpu_usage\"", "description": "指标名称筛选", "name": "metric_name", "in": "query"}, {"enum": ["baseline", "task_execution", "extended"], "type": "string", "example": "\"task_execution\"", "description": "监测阶段筛选", "name": "monitoring_phase", "in": "query"}, {"type": "string", "format": "date-time", "example": "\"2025-01-18T09:30:00+08:00\"", "description": "开始时间", "name": "start_time", "in": "query"}, {"type": "string", "format": "date-time", "example": "\"2025-01-18T10:30:00+08:00\"", "description": "结束时间", "name": "end_time", "in": "query"}, {"minimum": 1, "type": "integer", "default": 1, "description": "页码", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "default": 20, "description": "每页数量", "name": "page_size", "in": "query"}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.MetricsDataListResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "404": {"description": "未找到指标监测会话", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "500": {"description": "服务器内部错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}}}}, "/api/v1/tasks/{taskId}/metrics-data/cleanup": {"delete": {"description": "清理指定时间范围外的历史指标数据记录，支持按时间范围清理过期数据，需要按照正确的删除顺序处理", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["数据管理"], "summary": "数据清理接口", "parameters": [{"type": "string", "example": "\"task_20250118_001\"", "description": "任务ID", "name": "taskId", "in": "path", "required": true}, {"type": "string", "format": "date-time", "example": "\"2025-01-17T00:00:00+08:00\"", "description": "删除此时间之前的数据", "name": "before", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.MetricsCleanupResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "404": {"description": "未找到指标监测会话", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "500": {"description": "服务器内部错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}}}}, "/api/v1/tasks/{taskId}/metrics-monitoring/cascade": {"delete": {"description": "按正确顺序级联删除任务的所有指标相关数据。由于采用了限制删除策略，提供应用层的级联删除功能，按照正确顺序删除数据记录→基线数据→数据记录会话", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["数据管理"], "summary": "级联删除指标数据", "parameters": [{"type": "string", "example": "\"task_20250118_001\"", "description": "任务ID", "name": "taskId", "in": "path", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.MetricsDeleteResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "404": {"description": "未找到指标监测会话", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "500": {"description": "服务器内部错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}}}}, "/api/v1/tasks/{taskId}/metrics-monitoring/dependencies": {"get": {"description": "查询指标数据记录的关联依赖状态，返回各表间的数据关联情况，用于删除前的依赖检查", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["数据管理"], "summary": "查询数据关联状态", "parameters": [{"type": "string", "example": "\"task_20250118_001\"", "description": "任务ID", "name": "taskId", "in": "path", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.MetricsDependenciesResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "404": {"description": "未找到指标监测会话", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "500": {"description": "服务器内部错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}}}}, "/api/v1/tasks/{taskId}/metrics-monitoring/manual-start": {"post": {"description": "手动启动任务的指标数据记录。正常流程：指标数据记录由任务开始信号自动触发，无需手动调用。调试用途：用于开发调试、测试验证或自动启动失败时的补充操作", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["指标数据记录管理"], "summary": "手动启动指标数据记录（调试用）", "parameters": [{"type": "string", "example": "\"task_20250118_001\"", "description": "任务ID", "name": "taskId", "in": "path", "required": true}, {"description": "启动参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/metrics_monitoring.MetricsStartRequest"}}], "responses": {"201": {"description": "创建成功", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.MetricsStartResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "409": {"description": "指标数据记录会话已存在", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "422": {"description": "参数验证失败", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "500": {"description": "服务器内部错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}}}}, "/api/v1/tasks/{taskId}/metrics-monitoring/manual-stop": {"post": {"description": "手动停止任务的指标数据记录。正常流程：指标数据记录由任务结束信号自动停止并切换到延展数据记录模式。特殊情况：用于紧急停止、异常处理或测试场景", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["指标数据记录管理"], "summary": "手动停止指标数据记录", "parameters": [{"type": "string", "example": "\"task_20250118_001\"", "description": "任务ID", "name": "taskId", "in": "path", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.MetricsStopResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "404": {"description": "未找到指标数据记录会话", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "500": {"description": "服务器内部错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}}}}, "/api/v1/tasks/{taskId}/metrics-monitoring/status": {"get": {"description": "查询任务指标数据记录会话的当前状态，指标数据记录会话由任务信号自动创建", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["指标数据记录管理"], "summary": "查询指标数据记录状态", "parameters": [{"type": "string", "example": "\"task_20250118_001\"", "description": "任务ID", "name": "taskId", "in": "path", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.MetricsStatusResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "404": {"description": "任务指标数据记录会话不存在", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "500": {"description": "服务器内部错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}}}}, "/api/v1/tasks/{taskId}/metrics-statistics": {"get": {"description": "查询任务的指标统计汇总数据，基于基线表和数据记录表生成统计汇总，包括基线值、当前值、变化幅度等信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["指标数据查询"], "summary": "查询指标统计汇总", "parameters": [{"type": "string", "example": "\"task_20250118_001\"", "description": "任务ID", "name": "taskId", "in": "path", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.MetricsStatisticsResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "404": {"description": "未找到指标监测会话", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "500": {"description": "服务器内部错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}}}}, "/api/v1/tasks/{taskId}/metrics-trends": {"get": {"description": "查询任务期间的指标变化趋势数据，基于指标数据记录表生成时间序列图表数据，支持前端可视化展示", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["指标数据查询"], "summary": "查询指标趋势数据", "parameters": [{"type": "string", "example": "\"task_20250118_001\"", "description": "任务ID", "name": "taskId", "in": "path", "required": true}, {"type": "string", "example": "\"device_001\"", "description": "设备ID筛选", "name": "device_id", "in": "query"}, {"type": "string", "example": "\"dci_snmp_status_cpu_usage\"", "description": "指标名称筛选", "name": "metric_name", "in": "query"}, {"type": "string", "default": "\"1m\"", "example": "\"5m\"", "description": "时间间隔", "name": "interval", "in": "query"}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.MetricsTrendsResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "404": {"description": "未找到指标监测会话", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}, "500": {"description": "服务器内部错误", "schema": {"allOf": [{"$ref": "#/definitions/metrics_monitoring.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/metrics_monitoring.ErrorData"}}}]}}}}}, "/api/v1/tasks/{taskId}/monitoring/manual/stop": {"post": {"description": "手动停止指定任务的监控会话", "produces": ["application/json"], "tags": ["任务协同监控"], "summary": "停止任务监控", "parameters": [{"type": "string", "description": "任务ID", "name": "taskId", "in": "path", "required": true}], "responses": {"200": {"description": "监控停止成功", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/task_collaboration.TaskMonitoringResponse"}}}]}}, "404": {"description": "任务不存在", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ErrorResponse"}}}]}}, "500": {"description": "服务器内部错误", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ErrorResponse"}}}]}}}}}, "/api/v1/tasks/{taskId}/monitoring/start": {"post": {"description": "手动启动指定任务的监控会话，开始收集相关设备的性能指标和日志数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["任务协同监控"], "summary": "启动任务监控", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-446655440000\"", "description": "任务ID", "name": "taskId", "in": "path", "required": true}, {"description": "监控请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/task_collaboration.TaskMonitoringRequest"}}], "responses": {"200": {"description": "监控启动成功\" example({\"code\":200,\"data\":{\"success\":true,\"message\":\"任务监控启动成功\",\"taskId\":\"550e8400-e29b-41d4-a716-446655440000\",\"sessionId\":\"session-12345\",\"deviceCount\":3,\"startTime\":\"2025-01-11T10:15:30Z\"}})", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/task_collaboration.TaskMonitoringResponse"}}}]}}, "400": {"description": "请求参数错误\" example({\"code\":400,\"data\":{\"error\":\"请求参数格式错误\",\"request_id\":\"789fe92e-6e44-4df4-8734-0bbcc59a2689\",\"details\":\"deviceIds cannot be empty\"}})", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ErrorResponse"}}}]}}, "500": {"description": "服务器内部错误\" example({\"code\":500,\"data\":{\"error\":\"启动任务监控失败\",\"request_id\":\"789fe92e-6e44-4df4-8734-0bbcc59a2689\",\"details\":\"failed to create monitoring session\"}})", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ErrorResponse"}}}]}}}}}, "/api/v1/tasks/{taskId}/monitoring/status": {"get": {"description": "查询指定任务的监控状态和基本信息", "produces": ["application/json"], "tags": ["任务协同监控"], "summary": "获取任务状态", "parameters": [{"type": "string", "description": "任务ID", "name": "taskId", "in": "path", "required": true}], "responses": {"200": {"description": "查询成功", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/task_collaboration.TaskStatusResponse"}}}]}}, "500": {"description": "服务器内部错误", "schema": {"allOf": [{"$ref": "#/definitions/models.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ErrorResponse"}}}]}}}}}, "/api/v1/topology": {"get": {"description": "获取当前网络拓扑图，包括所有设备节点和连接", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["拓扑管理"], "summary": "获取当前拓扑图", "responses": {"200": {"description": "成功获取拓扑图", "schema": {"$ref": "#/definitions/topology.SwaggerTopologyGraph"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/topology/health": {"get": {"description": "获取拓扑服务的健康状态，包括数据库连接、Kafka连接等", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["拓扑管理"], "summary": "获取拓扑服务健康状态", "responses": {"200": {"description": "成功获取健康状态", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/topology/stats": {"get": {"description": "获取当前拓扑图的统计信息，如节点数量和连接数量", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["拓扑管理"], "summary": "获取拓扑统计信息", "responses": {"200": {"description": "成功获取拓扑统计信息", "schema": {"$ref": "#/definitions/topology.TopologyStats"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/traffic/chart/average": {"get": {"description": "查询A-Z链路在指定时间范围内的平均流量速率时间序列。支持通过VNI进行筛选。\n**注意: `a_port_id` 和 `z_port_id` 中的特殊字符 (如 '/') 需要进行 URL 编码。**", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["06-性能管理-A-Z链路流量"], "summary": "获取A-Z链路平均流量图", "parameters": [{"type": "string", "example": "CE1", "description": "A端设备ID", "name": "a_switch_id", "in": "query", "required": true}, {"type": "string", "example": "GE1%2F0%2F1", "description": "A端端口ID (URL Encoded)", "name": "a_port_id", "in": "query", "required": true}, {"type": "string", "example": "CE2", "description": "Z端设备ID", "name": "z_switch_id", "in": "query", "required": true}, {"type": "string", "example": "GE1%2F0%2F1", "description": "Z端端口ID (URL Encoded)", "name": "z_port_id", "in": "query", "required": true}, {"type": "string", "example": "1m", "description": "查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。", "name": "granularity", "in": "query", "required": true}, {"type": "string", "example": "6005002", "description": "VNI (Virtual Network Identifier) 标识符", "name": "vni", "in": "query"}], "responses": {"200": {"description": "成功返回平均流量数据", "schema": {"$ref": "#/definitions/traffic.TrafficChartData"}}, "400": {"description": "请求参数无效", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/traffic/chart/maximum": {"get": {"description": "查询A-Z链路在指定时间范围内的峰值流量速率时间序列。支持通过VNI进行筛选。\n**注意: `a_port_id` 和 `z_port_id` 中的特殊字符 (如 '/') 需要进行 URL 编码。**", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["06-性能管理-A-Z链路流量"], "summary": "获取A-Z链路最大(峰值)流量图", "parameters": [{"type": "string", "example": "CE1", "description": "A端设备ID", "name": "a_switch_id", "in": "query", "required": true}, {"type": "string", "example": "GE1%2F0%2F1", "description": "A端端口ID (URL Encoded)", "name": "a_port_id", "in": "query", "required": true}, {"type": "string", "example": "CE2", "description": "Z端设备ID", "name": "z_switch_id", "in": "query", "required": true}, {"type": "string", "example": "GE1%2F0%2F1", "description": "Z端端口ID (URL Encoded)", "name": "z_port_id", "in": "query", "required": true}, {"type": "string", "example": "1m", "description": "查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。", "name": "granularity", "in": "query", "required": true}, {"type": "string", "example": "6005002", "description": "VNI (Virtual Network Identifier) 标识符", "name": "vni", "in": "query"}], "responses": {"200": {"description": "成功返回峰值流量数据", "schema": {"$ref": "#/definitions/traffic.TrafficChartData"}}, "400": {"description": "请求参数无效", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/traffic/chart/minimum": {"get": {"description": "查询A-Z链路在指定时间范围内的谷值流量速率时间序列。支持通过VNI进行筛选。\n**注意: `a_port_id` 和 `z_port_id` 中的特殊字符 (如 '/') 需要进行 URL 编码。**", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["06-性能管理-A-Z链路流量"], "summary": "获取A-Z链路最小(谷值)流量图", "parameters": [{"type": "string", "example": "CE1", "description": "A端设备ID", "name": "a_switch_id", "in": "query", "required": true}, {"type": "string", "example": "GE1%2F0%2F1", "description": "A端端口ID (URL Encoded)", "name": "a_port_id", "in": "query", "required": true}, {"type": "string", "example": "CE2", "description": "Z端设备ID", "name": "z_switch_id", "in": "query", "required": true}, {"type": "string", "example": "GE1%2F0%2F1", "description": "Z端端口ID (URL Encoded)", "name": "z_port_id", "in": "query", "required": true}, {"type": "string", "example": "1m", "description": "查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。", "name": "granularity", "in": "query", "required": true}, {"type": "string", "example": "6005002", "description": "VNI (Virtual Network Identifier) 标识符", "name": "vni", "in": "query"}], "responses": {"200": {"description": "成功返回谷值流量数据", "schema": {"$ref": "#/definitions/traffic.TrafficChartData"}}, "400": {"description": "请求参数无效", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/traffic/summary": {"get": {"description": "获取A-Z链路在指定时间范围内的流量摘要信息，如总流量、平均/峰值/谷值速率等。 **注意：此接口当前尚未实现。**\n**注意: `a_port_id` 和 `z_port_id` 中的特殊字符 (如 '/') 需要进行 URL 编码。**", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["06-性能管理-A-Z链路流量"], "summary": "获取A-Z链路流量摘要", "parameters": [{"type": "string", "example": "CE1", "description": "A端设备ID", "name": "a_switch_id", "in": "query", "required": true}, {"type": "string", "example": "GE1%2F0%2F1", "description": "A端端口ID (URL Encoded)", "name": "a_port_id", "in": "query", "required": true}, {"type": "string", "example": "CE2", "description": "Z端设备ID", "name": "z_switch_id", "in": "query", "required": true}, {"type": "string", "example": "GE1%2F0%2F1", "description": "Z端端口ID (URL Encoded)", "name": "z_port_id", "in": "query", "required": true}, {"type": "string", "example": "1m", "description": "查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。", "name": "granularity", "in": "query", "required": true}, {"type": "string", "example": "6005002", "description": "VNI (Virtual Network Identifier) 标识符", "name": "vni", "in": "query"}], "responses": {"501": {"description": "接口未实现", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}}, "definitions": {"alert.Alert": {"type": "object", "properties": {"acknowledged_at": {"type": "string"}, "acknowledged_by": {"type": "string"}, "annotations": {"$ref": "#/definitions/alert.JSONMap"}, "description": {"type": "string"}, "device_id": {"description": "任务协同字段(网络自动化任务关联)", "type": "string"}, "ends_at": {"type": "string"}, "generator_url": {"type": "string"}, "id": {"type": "string"}, "labels": {"$ref": "#/definitions/alert.JSONMap"}, "level": {"type": "string"}, "name": {"type": "string"}, "note": {"type": "string"}, "notification_due_at": {"type": "string"}, "resolved_at": {"type": "string"}, "rule_id": {"type": "string"}, "source": {"type": "string"}, "starts_at": {"type": "string"}, "status": {"type": "string"}, "task_association": {"$ref": "#/definitions/alert.TaskAssociation"}, "updated_at": {"type": "string"}}}, "alert.AlertAcknowledgeRequest": {"type": "object", "required": ["acknowledged_by"], "properties": {"acknowledged_by": {"description": "告警确认人，必填字段，表示谁在处理此告警", "type": "string", "example": "张三"}, "note": {"description": "处理备注，选填，可以添加处理进展或计划", "type": "string", "example": "正在联系网络组排查原因"}}}, "alert.AlertAcknowledgeResponse": {"type": "object", "properties": {"code": {"description": "状态码", "type": "integer", "example": 200}, "message": {"description": "响应消息", "type": "string", "example": "告警已确认"}}}, "alert.AlertListResponse": {"type": "object", "properties": {"alerts": {"description": "告警列表", "type": "array", "items": {"$ref": "#/definitions/alert.Alert"}}, "page": {"description": "当前页码", "type": "integer"}, "page_count": {"description": "总页数", "type": "integer"}, "page_size": {"description": "每页数量", "type": "integer"}, "total": {"description": "总记录数", "type": "integer"}}}, "alert.AlertRule": {"type": "object", "properties": {"annotations": {"$ref": "#/definitions/alert.JSONMap"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "duration": {"type": "integer"}, "enabled": {"type": "boolean"}, "expression": {"type": "string"}, "id": {"type": "string"}, "labels": {"$ref": "#/definitions/alert.JSONMap"}, "level": {"type": "string"}, "name": {"type": "string"}, "source": {"type": "string"}, "updated_at": {"type": "string"}}}, "alert.AlertRuleToggleRequest": {"type": "object", "required": ["enabled"], "properties": {"enabled": {"description": "是否启用", "type": "boolean"}}}, "alert.JSONMap": {"type": "object", "additionalProperties": {"type": "string"}}, "alert.PrometheusWebhookAlert": {"type": "object", "properties": {"annotations": {"type": "object", "additionalProperties": {"type": "string"}}, "endsAt": {"type": "string"}, "generatorURL": {"type": "string"}, "labels": {"type": "object", "additionalProperties": {"type": "string"}}, "startsAt": {"type": "string"}, "status": {"type": "string"}}}, "alert.PrometheusWebhookRequest": {"type": "object", "properties": {"alerts": {"type": "array", "items": {"$ref": "#/definitions/alert.PrometheusWebhookAlert"}}, "commonAnnotations": {"type": "object", "additionalProperties": {"type": "string"}}, "commonLabels": {"type": "object", "additionalProperties": {"type": "string"}}, "externalURL": {"type": "string"}, "groupKey": {"type": "string"}, "groupLabels": {"type": "object", "additionalProperties": {"type": "string"}}, "receiver": {"type": "string"}, "status": {"type": "string"}, "version": {"type": "string"}}}, "alert.Response": {"type": "object", "properties": {"code": {"description": "状态码", "type": "integer"}, "data": {"description": "响应数据"}}}, "alert.TaskAssociation": {"type": "object", "properties": {"association_time": {"type": "string"}, "association_type": {"description": "\"active\" 或 \"post_task\"", "type": "string"}, "task_id": {"type": "string"}}}, "device.Port": {"type": "object", "properties": {"description": {"type": "string"}, "inErrors": {"type": "integer"}, "inRate": {"description": "上行速率", "type": "integer"}, "name": {"description": "例如 \"GigabitEthernet1/0/0\"", "type": "string"}, "outErrors": {"type": "integer"}, "outRate": {"description": "下行速率", "type": "integer"}, "physicalState": {"description": "物理状态: \"up\", \"down\", \"admin_down\"", "type": "string"}, "portId": {"description": "Changed field name to PortID", "type": "string"}, "protocolState": {"description": "协议状态: \"up\", \"down\"", "type": "string"}, "rateUnit": {"description": "速率单位 (e.g., \"bps\")", "type": "string"}, "timestamp": {"description": "流量数据获取时间", "type": "string"}, "totalInBytes": {"description": "上行字节总量", "type": "integer"}, "totalInPkts": {"description": "上行包总量", "type": "integer"}, "totalOutBytes": {"description": "下行字节总量", "type": "integer"}, "totalOutPkts": {"description": "下行包总量", "type": "integer"}}}, "device.PortAdminStatus": {"type": "object", "properties": {"deviceId": {"type": "string"}, "portId": {"type": "string"}, "portName": {"type": "string"}, "status": {"description": "1:up, 2:down", "type": "integer"}, "updateAt": {"type": "string"}}}, "device.PortDownstreamTraffic": {"type": "object", "properties": {"deviceId": {"type": "string"}, "outRate": {"type": "integer"}, "portId": {"type": "string"}, "portName": {"type": "string"}, "rateUnit": {"type": "string"}, "timestamp": {"type": "string"}, "totalOutBytes": {"type": "integer"}, "totalOutPkts": {"type": "integer"}}}, "device.PortStatusResponse": {"type": "object", "properties": {"description": {"type": "string"}, "deviceId": {"type": "string"}, "inErrors": {"type": "integer"}, "inRate": {"description": "上行速率", "type": "integer"}, "name": {"description": "例如 \"GigabitEthernet1/0/0\"", "type": "string"}, "outErrors": {"type": "integer"}, "outRate": {"description": "下行速率", "type": "integer"}, "physicalState": {"description": "物理状态: \"up\", \"down\", \"admin_down\"", "type": "string"}, "portId": {"description": "Changed field name to PortID", "type": "string"}, "protocolState": {"description": "协议状态: \"up\", \"down\"", "type": "string"}, "rateUnit": {"description": "速率单位 (e.g., \"bps\")", "type": "string"}, "timestamp": {"description": "流量数据获取时间", "type": "string"}, "totalInBytes": {"description": "上行字节总量", "type": "integer"}, "totalInPkts": {"description": "上行包总量", "type": "integer"}, "totalOutBytes": {"description": "下行字节总量", "type": "integer"}, "totalOutPkts": {"description": "下行包总量", "type": "integer"}}}, "device.PortUpstreamTraffic": {"type": "object", "properties": {"deviceId": {"type": "string"}, "inRate": {"type": "integer"}, "portId": {"type": "string"}, "portName": {"type": "string"}, "rateUnit": {"type": "string"}, "timestamp": {"type": "string"}, "totalInBytes": {"type": "integer"}, "totalInPkts": {"type": "integer"}}}, "device.SwitchStatus": {"type": "object", "properties": {"community": {"type": "string"}, "id": {"type": "string"}, "ip": {"type": "string"}, "lastUpdated": {"type": "string"}, "name": {"type": "string"}, "ports": {"type": "array", "items": {"$ref": "#/definitions/device.Port"}}, "status": {"description": "整体状态: \"online\", \"offline\", \"unknown\"", "type": "string"}}}, "metrics_monitoring.ErrorData": {"type": "object", "properties": {"error": {"type": "string"}, "request_id": {"type": "string"}}}, "metrics_monitoring.MetricsBaseline": {"type": "object", "properties": {"avgValue": {"type": "number"}, "createdAt": {"type": "string"}, "deviceId": {"type": "string"}, "id": {"type": "string"}, "maxValue": {"type": "number"}, "metricName": {"type": "string"}, "metricsSessionId": {"type": "string"}, "minValue": {"type": "number"}, "sampleCount": {"type": "integer"}, "stdDev": {"type": "number"}, "timeRangeEnd": {"type": "string"}, "timeRangeStart": {"type": "string"}}}, "metrics_monitoring.MetricsBaselineListResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/metrics_monitoring.MetricsBaseline"}}, "pagination": {"$ref": "#/definitions/metrics_monitoring.PaginationInfo"}}}, "metrics_monitoring.MetricsBaselineSummary": {"type": "object", "properties": {"avgValue": {"type": "number"}, "timeRange": {"$ref": "#/definitions/metrics_monitoring.TimeRange"}}}, "metrics_monitoring.MetricsChange": {"type": "object", "properties": {"baselineValue": {"type": "number"}, "changeMagnitude": {"type": "number"}, "changeType": {"type": "string"}, "createdAt": {"type": "string"}, "currentValue": {"type": "number"}, "description": {"type": "string"}, "detectionTime": {"type": "string"}, "deviceId": {"type": "string"}, "id": {"type": "string"}, "metricName": {"type": "string"}, "metricsSessionId": {"type": "string"}, "monitoringPhase": {"type": "string"}, "severity": {"type": "string"}}}, "metrics_monitoring.MetricsCleanupResponse": {"type": "object", "properties": {"cleanupBefore": {"type": "string"}, "deletedRecords": {"type": "integer"}, "message": {"type": "string"}, "taskId": {"type": "string"}}}, "metrics_monitoring.MetricsComparisonChanges": {"type": "object", "properties": {"baselineToTask": {"type": "string"}, "overallChange": {"type": "string"}, "taskToExtended": {"type": "string"}}}, "metrics_monitoring.MetricsComparisonData": {"type": "object", "properties": {"baseline": {"$ref": "#/definitions/metrics_monitoring.MetricsStatisticsSummary"}, "changes": {"$ref": "#/definitions/metrics_monitoring.MetricsComparisonChanges"}, "deviceId": {"type": "string"}, "extendedPeriod": {"$ref": "#/definitions/metrics_monitoring.MetricsStatisticsSummary"}, "metricName": {"type": "string"}, "taskPeriod": {"$ref": "#/definitions/metrics_monitoring.MetricsStatisticsSummary"}}}, "metrics_monitoring.MetricsComparisonResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/metrics_monitoring.MetricsComparisonData"}}}}, "metrics_monitoring.MetricsDataListResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/metrics_monitoring.MetricsChange"}}, "pagination": {"$ref": "#/definitions/metrics_monitoring.PaginationInfo"}}}, "metrics_monitoring.MetricsDeleteResponse": {"type": "object", "properties": {"deletedItems": {"$ref": "#/definitions/metrics_monitoring.MetricsDeletedItems"}, "message": {"type": "string"}, "taskId": {"type": "string"}}}, "metrics_monitoring.MetricsDeletedItems": {"type": "object", "properties": {"baselineRecords": {"type": "integer"}, "metricsDataRecords": {"type": "integer"}, "metricsSession": {"type": "integer"}}}, "metrics_monitoring.MetricsDependenciesDetail": {"type": "object", "properties": {"baselineRecords": {"$ref": "#/definitions/metrics_monitoring.MetricsRecordInfo"}, "dataRecords": {"$ref": "#/definitions/metrics_monitoring.MetricsRecordInfo"}, "metricsSession": {"$ref": "#/definitions/metrics_monitoring.MetricsSessionInfo"}}}, "metrics_monitoring.MetricsDependenciesResponse": {"type": "object", "properties": {"canDelete": {"type": "boolean"}, "dependencies": {"$ref": "#/definitions/metrics_monitoring.MetricsDependenciesDetail"}, "hasMetricsSession": {"type": "boolean"}, "taskId": {"type": "string"}}}, "metrics_monitoring.MetricsDeviceChangeSummary": {"type": "object", "properties": {"avgChange": {"type": "number"}, "maxIncrease": {"type": "number"}, "volatility": {"type": "string"}}}, "metrics_monitoring.MetricsDeviceMetric": {"type": "object", "properties": {"baseline": {"$ref": "#/definitions/metrics_monitoring.MetricsStatisticsSummary"}, "changeSummary": {"$ref": "#/definitions/metrics_monitoring.MetricsDeviceChangeSummary"}, "extendedPeriod": {"$ref": "#/definitions/metrics_monitoring.MetricsStatisticsSummary"}, "metricName": {"type": "string"}, "taskPeriod": {"$ref": "#/definitions/metrics_monitoring.MetricsStatisticsSummary"}}}, "metrics_monitoring.MetricsDeviceStatistics": {"type": "object", "properties": {"deviceId": {"type": "string"}, "deviceName": {"type": "string"}, "metrics": {"type": "array", "items": {"$ref": "#/definitions/metrics_monitoring.MetricsDeviceMetric"}}}}, "metrics_monitoring.MetricsOverallSummary": {"type": "object", "properties": {"dataPoints": {"type": "integer"}, "monitoringDuration": {"type": "string"}, "totalDevices": {"type": "integer"}, "totalMetrics": {"type": "integer"}}}, "metrics_monitoring.MetricsRecordInfo": {"type": "object", "properties": {"count": {"type": "integer"}, "deviceIds": {"type": "array", "items": {"type": "string"}}, "timeRange": {"$ref": "#/definitions/metrics_monitoring.TimeRange"}}}, "metrics_monitoring.MetricsSessionInfo": {"type": "object", "properties": {"sessionId": {"type": "string"}, "status": {"type": "string"}}}, "metrics_monitoring.MetricsStartRequest": {"type": "object", "required": ["deviceIds", "metricsTypes"], "properties": {"baselineDuration": {"type": "string"}, "deviceIds": {"type": "array", "minItems": 1, "items": {"type": "string"}}, "extendedDuration": {"type": "string"}, "metricsTypes": {"type": "array", "minItems": 1, "items": {"type": "string"}}}}, "metrics_monitoring.MetricsStartResponse": {"type": "object", "properties": {"message": {"type": "string"}, "sessionId": {"type": "string"}, "status": {"type": "string"}, "taskId": {"type": "string"}}}, "metrics_monitoring.MetricsStatisticsResponse": {"type": "object", "properties": {"deviceStatistics": {"type": "array", "items": {"$ref": "#/definitions/metrics_monitoring.MetricsDeviceStatistics"}}, "sessionId": {"type": "string"}, "summary": {"$ref": "#/definitions/metrics_monitoring.MetricsOverallSummary"}, "taskId": {"type": "string"}}}, "metrics_monitoring.MetricsStatisticsSummary": {"type": "object", "properties": {"avgValue": {"type": "number"}, "maxValue": {"type": "number"}, "minValue": {"type": "number"}}}, "metrics_monitoring.MetricsStatusResponse": {"type": "object", "properties": {"baselineEnd": {"type": "string"}, "baselineStart": {"type": "string"}, "createdAt": {"type": "string"}, "deviceIds": {"type": "array", "items": {"type": "string"}}, "extendedEnd": {"type": "string"}, "metricsTypes": {"type": "array", "items": {"type": "string"}}, "monitoringEnd": {"type": "string"}, "monitoringStart": {"type": "string"}, "sessionId": {"type": "string"}, "status": {"type": "string"}, "taskId": {"type": "string"}, "taskSessionId": {"type": "string"}}}, "metrics_monitoring.MetricsStopResponse": {"type": "object", "properties": {"message": {"type": "string"}, "sessionId": {"type": "string"}, "status": {"type": "string"}, "taskId": {"type": "string"}}}, "metrics_monitoring.MetricsTrendsDataPoint": {"type": "object", "properties": {"phase": {"type": "string"}, "timestamp": {"type": "string"}, "value": {"type": "number"}}}, "metrics_monitoring.MetricsTrendsResponse": {"type": "object", "properties": {"baseline": {"$ref": "#/definitions/metrics_monitoring.MetricsBaselineSummary"}, "dataPoints": {"type": "array", "items": {"$ref": "#/definitions/metrics_monitoring.MetricsTrendsDataPoint"}}, "deviceId": {"type": "string"}, "metricName": {"type": "string"}, "timeRange": {"$ref": "#/definitions/metrics_monitoring.TimeRange"}}}, "metrics_monitoring.PaginationInfo": {"type": "object", "properties": {"page": {"type": "integer"}, "pageCount": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "metrics_monitoring.Response": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}}}, "metrics_monitoring.TimeRange": {"type": "object", "properties": {"endTime": {"type": "string"}, "startTime": {"type": "string"}}}, "models.ErrorResponse": {"type": "object", "properties": {"details": {"description": "错误详情", "type": "string"}, "error": {"description": "错误信息", "type": "string"}, "request_id": {"description": "请求ID", "type": "string"}}}, "models.Response": {"type": "object", "properties": {"code": {"description": "状态码", "type": "integer"}, "data": {"description": "响应数据"}}}, "task_collaboration.PaginationInfo": {"type": "object", "properties": {"limit": {"type": "integer"}, "offset": {"type": "integer"}, "page": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "task_collaboration.TaskAlertListResponse": {"type": "object", "properties": {"alerts": {"type": "array", "items": {"$ref": "#/definitions/task_collaboration.TaskRelatedAlert"}}, "pagination": {"$ref": "#/definitions/task_collaboration.PaginationInfo"}, "statistics": {"$ref": "#/definitions/task_collaboration.TaskAlertStatistics"}, "taskId": {"type": "string"}}}, "task_collaboration.TaskAlertStatistics": {"type": "object", "properties": {"deviceCount": {"type": "object", "additionalProperties": {"type": "integer"}}, "levelCount": {"type": "object", "additionalProperties": {"type": "integer"}}, "phaseCount": {"type": "object", "additionalProperties": {"type": "integer"}}, "statusCount": {"type": "object", "additionalProperties": {"type": "integer"}}, "taskId": {"type": "string"}, "timeDistribution": {"type": "array", "items": {"$ref": "#/definitions/task_collaboration.TimeSlotCount"}}, "timeRange": {"$ref": "#/definitions/task_collaboration.TimeRange"}, "totalCount": {"type": "integer"}}}, "task_collaboration.TaskMonitoringRequest": {"type": "object", "required": ["deviceIds"], "properties": {"deviceIds": {"type": "array", "minItems": 1, "items": {"type": "string"}}}}, "task_collaboration.TaskMonitoringResponse": {"type": "object", "properties": {"deviceCount": {"type": "integer"}, "duration": {"type": "string"}, "endTime": {"type": "string"}, "message": {"type": "string"}, "sessionId": {"type": "string"}, "startTime": {"type": "string"}, "success": {"type": "boolean"}, "taskId": {"type": "string"}}}, "task_collaboration.TaskRelatedAlert": {"type": "object", "properties": {"deviceId": {"type": "string"}, "endsAt": {"type": "string"}, "id": {"type": "string"}, "level": {"type": "string"}, "monitoringPhase": {"type": "string"}, "name": {"type": "string"}, "startsAt": {"type": "string"}, "status": {"type": "string"}, "taskRelation": {"type": "string"}}}, "task_collaboration.TaskSignal": {"type": "object", "required": ["signal_type", "target_devices", "task_id", "task_type"], "properties": {"expected_duration": {"type": "integer", "minimum": 0}, "port_id": {"description": "逻辑端口ID，选填", "type": "string"}, "port_name": {"description": "端口名称，选填", "type": "string"}, "signal_type": {"type": "string", "enum": ["start", "end", "abort"]}, "status": {"description": "基于自动化系统Django choices规范", "type": "string", "enum": ["SUCCESS", "FAILURE", "REVOKED"]}, "target_devices": {"type": "array", "minItems": 1, "items": {"type": "string"}}, "task_id": {"description": "移除UUID验证，允许自定义任务ID格式", "type": "string"}, "task_type": {"type": "string", "enum": ["CONFIG_DEPLOY", "CONFIG_RECOVERY", "CONFIG_DOWNLOAD", "OTHER"]}, "timestamp": {"type": "string"}}}, "task_collaboration.TaskSignalResponse": {"type": "object", "properties": {"message": {"type": "string"}, "sessionId": {"type": "string"}, "success": {"type": "boolean"}, "taskId": {"type": "string"}}}, "task_collaboration.TaskStatusResponse": {"type": "object", "properties": {"deviceCount": {"type": "integer"}, "endTime": {"type": "string"}, "isActive": {"type": "boolean"}, "sessionId": {"type": "string"}, "startTime": {"type": "string"}, "status": {"description": "使用自动化系统标准枚举值: SUCCESS, FAILURE, REVOKED", "type": "string"}, "taskId": {"type": "string"}}}, "task_collaboration.TimeRange": {"type": "object", "properties": {"endTime": {"type": "string"}, "startTime": {"type": "string"}}}, "task_collaboration.TimeSlotCount": {"type": "object", "properties": {"count": {"type": "integer"}, "timeSlot": {"type": "string"}}}, "topology.SwaggerTopologyEdge": {"description": "拓扑图中的边（连接）", "type": "object", "properties": {"data": {"description": "连接附加数据，JSON格式"}, "id": {"description": "边ID", "type": "string"}, "source": {"description": "源节点ID", "type": "string"}, "sourcePort": {"description": "源端口ID", "type": "string"}, "target": {"description": "目标节点ID", "type": "string"}, "targetPort": {"description": "目标端口ID", "type": "string"}, "type": {"description": "连接类型：physical、logical等", "type": "string"}}}, "topology.SwaggerTopologyGraph": {"description": "完整的拓扑图", "type": "object", "properties": {"createdAt": {"description": "创建时间", "type": "string"}, "edges": {"description": "边列表", "type": "array", "items": {"$ref": "#/definitions/topology.SwaggerTopologyEdge"}}, "id": {"description": "拓扑图ID", "type": "string"}, "name": {"description": "拓扑图名称", "type": "string"}, "nodes": {"description": "节点列表", "type": "array", "items": {"$ref": "#/definitions/topology.SwaggerTopologyNode"}}}}, "topology.SwaggerTopologyNode": {"description": "拓扑图中的节点（设备）", "type": "object", "properties": {"data": {"description": "节点附加数据，JSON格式"}, "id": {"description": "节点ID，与设备ID相同", "type": "string"}, "name": {"description": "节点名称", "type": "string"}, "position": {"description": "节点位置（可选）"}, "type": {"description": "节点类型：switch、router、server等", "type": "string"}}}, "topology.TopologyStats": {"type": "object", "properties": {"edgeCount": {"description": "边数量", "type": "integer"}, "logicalLinks": {"description": "逻辑链接数量", "type": "integer"}, "nodeCount": {"description": "节点数量", "type": "integer"}, "physicalLinks": {"description": "物理链接数量", "type": "integer"}, "routerCount": {"description": "路由器数量", "type": "integer"}, "serverCount": {"description": "服务器数量", "type": "integer"}, "switchCount": {"description": "交换机数量", "type": "integer"}}}, "traffic.PortFlowData": {"type": "object", "properties": {"in_rate": {"type": "number"}, "in_total": {"type": "number"}, "out_rate": {"type": "number"}, "out_total": {"type": "number"}, "unit_rate": {"description": "速率单位, e.g., \"Mbps\"", "type": "string"}, "unit_total": {"description": "总量单位, e.g., \"MB\", \"GB\"", "type": "string"}}}, "traffic.PortFlowQueryDetails": {"type": "object", "properties": {"device_id": {"type": "string"}, "end_time": {"type": "string"}, "port_id": {"type": "string"}, "start_time": {"type": "string"}, "vni": {"type": "string"}}}, "traffic.PortFlowResponse": {"type": "object", "properties": {"flow_data": {"$ref": "#/definitions/traffic.PortFlowData"}, "query_details": {"$ref": "#/definitions/traffic.PortFlowQueryDetails"}, "request_id": {"type": "string"}}}, "traffic.PortHistoryData": {"type": "object", "properties": {"series": {"description": "数据系列数组", "type": "array", "items": {"$ref": "#/definitions/traffic.TrafficSeries"}}, "timestamps": {"description": "RFC3339 UTC 格式的时间戳数组", "type": "array", "items": {"type": "string"}}, "unit": {"description": "数据单位 (e.g., \"Mbps\")", "type": "string"}}}, "traffic.PortHistoryQueryDetails": {"type": "object", "properties": {"device_id": {"type": "string"}, "end_time": {"type": "string"}, "port_id": {"type": "string"}, "start_time": {"type": "string"}, "step": {"type": "string"}, "vni": {"type": "string"}}}, "traffic.PortHistoryResponse": {"type": "object", "properties": {"history_data": {"$ref": "#/definitions/traffic.PortHistoryData"}, "query_details": {"$ref": "#/definitions/traffic.PortHistoryQueryDetails"}, "request_id": {"type": "string"}}}, "traffic.TrafficChartData": {"type": "object", "properties": {"series": {"description": "数据系列数组", "type": "array", "items": {"$ref": "#/definitions/traffic.TrafficSeries"}}, "timestamps": {"description": "RFC3339 UTC 格式的时间戳数组", "type": "array", "items": {"type": "string"}}, "unit": {"description": "数据单位 (e.g., \"Mbps\", \"Kbps\")", "type": "string"}}}, "traffic.TrafficSeries": {"type": "object", "properties": {"data": {"description": "与 Timestamps 对应的数值数组", "type": "array", "items": {"type": "number"}}, "name": {"description": "系列名称 (e.g., \"A端 (CE1-GE1/0/1) 平均入流量\")", "type": "string"}}}}}