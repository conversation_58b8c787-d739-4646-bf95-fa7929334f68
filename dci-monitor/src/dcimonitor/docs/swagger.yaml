basePath: /
definitions:
  alert.Alert:
    properties:
      acknowledged_at:
        type: string
      acknowledged_by:
        type: string
      annotations:
        $ref: '#/definitions/alert.JSONMap'
      description:
        type: string
      device_id:
        description: 任务协同字段(网络自动化任务关联)
        type: string
      ends_at:
        type: string
      generator_url:
        type: string
      id:
        type: string
      labels:
        $ref: '#/definitions/alert.JSONMap'
      level:
        type: string
      name:
        type: string
      note:
        type: string
      notification_due_at:
        type: string
      resolved_at:
        type: string
      rule_id:
        type: string
      source:
        type: string
      starts_at:
        type: string
      status:
        type: string
      task_association:
        $ref: '#/definitions/alert.TaskAssociation'
      updated_at:
        type: string
    type: object
  alert.AlertAcknowledgeRequest:
    properties:
      acknowledged_by:
        description: 告警确认人，必填字段，表示谁在处理此告警
        example: 张三
        type: string
      note:
        description: 处理备注，选填，可以添加处理进展或计划
        example: 正在联系网络组排查原因
        type: string
    required:
    - acknowledged_by
    type: object
  alert.AlertAcknowledgeResponse:
    properties:
      code:
        description: 状态码
        example: 200
        type: integer
      message:
        description: 响应消息
        example: 告警已确认
        type: string
    type: object
  alert.AlertListResponse:
    properties:
      alerts:
        description: 告警列表
        items:
          $ref: '#/definitions/alert.Alert'
        type: array
      page:
        description: 当前页码
        type: integer
      page_count:
        description: 总页数
        type: integer
      page_size:
        description: 每页数量
        type: integer
      total:
        description: 总记录数
        type: integer
    type: object
  alert.AlertRule:
    properties:
      annotations:
        $ref: '#/definitions/alert.JSONMap'
      created_at:
        type: string
      description:
        type: string
      duration:
        type: integer
      enabled:
        type: boolean
      expression:
        type: string
      id:
        type: string
      labels:
        $ref: '#/definitions/alert.JSONMap'
      level:
        type: string
      name:
        type: string
      source:
        type: string
      updated_at:
        type: string
    type: object
  alert.AlertRuleToggleRequest:
    properties:
      enabled:
        description: 是否启用
        type: boolean
    required:
    - enabled
    type: object
  alert.JSONMap:
    additionalProperties:
      type: string
    type: object
  alert.PrometheusWebhookAlert:
    properties:
      annotations:
        additionalProperties:
          type: string
        type: object
      endsAt:
        type: string
      generatorURL:
        type: string
      labels:
        additionalProperties:
          type: string
        type: object
      startsAt:
        type: string
      status:
        type: string
    type: object
  alert.PrometheusWebhookRequest:
    properties:
      alerts:
        items:
          $ref: '#/definitions/alert.PrometheusWebhookAlert'
        type: array
      commonAnnotations:
        additionalProperties:
          type: string
        type: object
      commonLabels:
        additionalProperties:
          type: string
        type: object
      externalURL:
        type: string
      groupKey:
        type: string
      groupLabels:
        additionalProperties:
          type: string
        type: object
      receiver:
        type: string
      status:
        type: string
      version:
        type: string
    type: object
  alert.Response:
    properties:
      code:
        description: 状态码
        type: integer
      data:
        description: 响应数据
    type: object
  alert.TaskAssociation:
    properties:
      association_time:
        type: string
      association_type:
        description: '"active" 或 "post_task"'
        type: string
      task_id:
        type: string
    type: object
  device.Port:
    properties:
      description:
        type: string
      inErrors:
        type: integer
      inRate:
        description: 上行速率
        type: integer
      name:
        description: 例如 "GigabitEthernet1/0/0"
        type: string
      outErrors:
        type: integer
      outRate:
        description: 下行速率
        type: integer
      physicalState:
        description: '物理状态: "up", "down", "admin_down"'
        type: string
      portId:
        description: Changed field name to PortID
        type: string
      protocolState:
        description: '协议状态: "up", "down"'
        type: string
      rateUnit:
        description: 速率单位 (e.g., "bps")
        type: string
      timestamp:
        description: 流量数据获取时间
        type: string
      totalInBytes:
        description: 上行字节总量
        type: integer
      totalInPkts:
        description: 上行包总量
        type: integer
      totalOutBytes:
        description: 下行字节总量
        type: integer
      totalOutPkts:
        description: 下行包总量
        type: integer
    type: object
  device.PortAdminStatus:
    properties:
      deviceId:
        type: string
      portId:
        type: string
      portName:
        type: string
      status:
        description: 1:up, 2:down
        type: integer
      updateAt:
        type: string
    type: object
  device.PortDownstreamTraffic:
    properties:
      deviceId:
        type: string
      outRate:
        type: integer
      portId:
        type: string
      portName:
        type: string
      rateUnit:
        type: string
      timestamp:
        type: string
      totalOutBytes:
        type: integer
      totalOutPkts:
        type: integer
    type: object
  device.PortStatusResponse:
    properties:
      description:
        type: string
      deviceId:
        type: string
      inErrors:
        type: integer
      inRate:
        description: 上行速率
        type: integer
      name:
        description: 例如 "GigabitEthernet1/0/0"
        type: string
      outErrors:
        type: integer
      outRate:
        description: 下行速率
        type: integer
      physicalState:
        description: '物理状态: "up", "down", "admin_down"'
        type: string
      portId:
        description: Changed field name to PortID
        type: string
      protocolState:
        description: '协议状态: "up", "down"'
        type: string
      rateUnit:
        description: 速率单位 (e.g., "bps")
        type: string
      timestamp:
        description: 流量数据获取时间
        type: string
      totalInBytes:
        description: 上行字节总量
        type: integer
      totalInPkts:
        description: 上行包总量
        type: integer
      totalOutBytes:
        description: 下行字节总量
        type: integer
      totalOutPkts:
        description: 下行包总量
        type: integer
    type: object
  device.PortUpstreamTraffic:
    properties:
      deviceId:
        type: string
      inRate:
        type: integer
      portId:
        type: string
      portName:
        type: string
      rateUnit:
        type: string
      timestamp:
        type: string
      totalInBytes:
        type: integer
      totalInPkts:
        type: integer
    type: object
  device.SwitchStatus:
    properties:
      community:
        type: string
      id:
        type: string
      ip:
        type: string
      lastUpdated:
        type: string
      name:
        type: string
      ports:
        items:
          $ref: '#/definitions/device.Port'
        type: array
      status:
        description: '整体状态: "online", "offline", "unknown"'
        type: string
    type: object
  metrics_monitoring.ErrorData:
    properties:
      error:
        type: string
      request_id:
        type: string
    type: object
  metrics_monitoring.MetricsBaseline:
    properties:
      avgValue:
        type: number
      createdAt:
        type: string
      deviceId:
        type: string
      id:
        type: string
      maxValue:
        type: number
      metricName:
        type: string
      metricsSessionId:
        type: string
      minValue:
        type: number
      sampleCount:
        type: integer
      stdDev:
        type: number
      timeRangeEnd:
        type: string
      timeRangeStart:
        type: string
    type: object
  metrics_monitoring.MetricsBaselineListResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/metrics_monitoring.MetricsBaseline'
        type: array
      pagination:
        $ref: '#/definitions/metrics_monitoring.PaginationInfo'
    type: object
  metrics_monitoring.MetricsBaselineSummary:
    properties:
      avgValue:
        type: number
      timeRange:
        $ref: '#/definitions/metrics_monitoring.TimeRange'
    type: object
  metrics_monitoring.MetricsChange:
    properties:
      baselineValue:
        type: number
      changeMagnitude:
        type: number
      changeType:
        type: string
      createdAt:
        type: string
      currentValue:
        type: number
      description:
        type: string
      detectionTime:
        type: string
      deviceId:
        type: string
      id:
        type: string
      metricName:
        type: string
      metricsSessionId:
        type: string
      monitoringPhase:
        type: string
      severity:
        type: string
    type: object
  metrics_monitoring.MetricsCleanupResponse:
    properties:
      cleanupBefore:
        type: string
      deletedRecords:
        type: integer
      message:
        type: string
      taskId:
        type: string
    type: object
  metrics_monitoring.MetricsComparisonChanges:
    properties:
      baselineToTask:
        type: string
      overallChange:
        type: string
      taskToExtended:
        type: string
    type: object
  metrics_monitoring.MetricsComparisonData:
    properties:
      baseline:
        $ref: '#/definitions/metrics_monitoring.MetricsStatisticsSummary'
      changes:
        $ref: '#/definitions/metrics_monitoring.MetricsComparisonChanges'
      deviceId:
        type: string
      extendedPeriod:
        $ref: '#/definitions/metrics_monitoring.MetricsStatisticsSummary'
      metricName:
        type: string
      taskPeriod:
        $ref: '#/definitions/metrics_monitoring.MetricsStatisticsSummary'
    type: object
  metrics_monitoring.MetricsComparisonResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/metrics_monitoring.MetricsComparisonData'
        type: array
    type: object
  metrics_monitoring.MetricsDataListResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/metrics_monitoring.MetricsChange'
        type: array
      pagination:
        $ref: '#/definitions/metrics_monitoring.PaginationInfo'
    type: object
  metrics_monitoring.MetricsDeleteResponse:
    properties:
      deletedItems:
        $ref: '#/definitions/metrics_monitoring.MetricsDeletedItems'
      message:
        type: string
      taskId:
        type: string
    type: object
  metrics_monitoring.MetricsDeletedItems:
    properties:
      baselineRecords:
        type: integer
      metricsDataRecords:
        type: integer
      metricsSession:
        type: integer
    type: object
  metrics_monitoring.MetricsDependenciesDetail:
    properties:
      baselineRecords:
        $ref: '#/definitions/metrics_monitoring.MetricsRecordInfo'
      dataRecords:
        $ref: '#/definitions/metrics_monitoring.MetricsRecordInfo'
      metricsSession:
        $ref: '#/definitions/metrics_monitoring.MetricsSessionInfo'
    type: object
  metrics_monitoring.MetricsDependenciesResponse:
    properties:
      canDelete:
        type: boolean
      dependencies:
        $ref: '#/definitions/metrics_monitoring.MetricsDependenciesDetail'
      hasMetricsSession:
        type: boolean
      taskId:
        type: string
    type: object
  metrics_monitoring.MetricsDeviceChangeSummary:
    properties:
      avgChange:
        type: number
      maxIncrease:
        type: number
      volatility:
        type: string
    type: object
  metrics_monitoring.MetricsDeviceMetric:
    properties:
      baseline:
        $ref: '#/definitions/metrics_monitoring.MetricsStatisticsSummary'
      changeSummary:
        $ref: '#/definitions/metrics_monitoring.MetricsDeviceChangeSummary'
      extendedPeriod:
        $ref: '#/definitions/metrics_monitoring.MetricsStatisticsSummary'
      metricName:
        type: string
      taskPeriod:
        $ref: '#/definitions/metrics_monitoring.MetricsStatisticsSummary'
    type: object
  metrics_monitoring.MetricsDeviceStatistics:
    properties:
      deviceId:
        type: string
      deviceName:
        type: string
      metrics:
        items:
          $ref: '#/definitions/metrics_monitoring.MetricsDeviceMetric'
        type: array
    type: object
  metrics_monitoring.MetricsOverallSummary:
    properties:
      dataPoints:
        type: integer
      monitoringDuration:
        type: string
      totalDevices:
        type: integer
      totalMetrics:
        type: integer
    type: object
  metrics_monitoring.MetricsRecordInfo:
    properties:
      count:
        type: integer
      deviceIds:
        items:
          type: string
        type: array
      timeRange:
        $ref: '#/definitions/metrics_monitoring.TimeRange'
    type: object
  metrics_monitoring.MetricsSessionInfo:
    properties:
      sessionId:
        type: string
      status:
        type: string
    type: object
  metrics_monitoring.MetricsStartRequest:
    properties:
      baselineDuration:
        type: string
      deviceIds:
        items:
          type: string
        minItems: 1
        type: array
      extendedDuration:
        type: string
      metricsTypes:
        items:
          type: string
        minItems: 1
        type: array
    required:
    - deviceIds
    - metricsTypes
    type: object
  metrics_monitoring.MetricsStartResponse:
    properties:
      message:
        type: string
      sessionId:
        type: string
      status:
        type: string
      taskId:
        type: string
    type: object
  metrics_monitoring.MetricsStatisticsResponse:
    properties:
      deviceStatistics:
        items:
          $ref: '#/definitions/metrics_monitoring.MetricsDeviceStatistics'
        type: array
      sessionId:
        type: string
      summary:
        $ref: '#/definitions/metrics_monitoring.MetricsOverallSummary'
      taskId:
        type: string
    type: object
  metrics_monitoring.MetricsStatisticsSummary:
    properties:
      avgValue:
        type: number
      maxValue:
        type: number
      minValue:
        type: number
    type: object
  metrics_monitoring.MetricsStatusResponse:
    properties:
      baselineEnd:
        type: string
      baselineStart:
        type: string
      createdAt:
        type: string
      deviceIds:
        items:
          type: string
        type: array
      extendedEnd:
        type: string
      metricsTypes:
        items:
          type: string
        type: array
      monitoringEnd:
        type: string
      monitoringStart:
        type: string
      sessionId:
        type: string
      status:
        type: string
      taskId:
        type: string
      taskSessionId:
        type: string
    type: object
  metrics_monitoring.MetricsStopResponse:
    properties:
      message:
        type: string
      sessionId:
        type: string
      status:
        type: string
      taskId:
        type: string
    type: object
  metrics_monitoring.MetricsTrendsDataPoint:
    properties:
      phase:
        type: string
      timestamp:
        type: string
      value:
        type: number
    type: object
  metrics_monitoring.MetricsTrendsResponse:
    properties:
      baseline:
        $ref: '#/definitions/metrics_monitoring.MetricsBaselineSummary'
      dataPoints:
        items:
          $ref: '#/definitions/metrics_monitoring.MetricsTrendsDataPoint'
        type: array
      deviceId:
        type: string
      metricName:
        type: string
      timeRange:
        $ref: '#/definitions/metrics_monitoring.TimeRange'
    type: object
  metrics_monitoring.PaginationInfo:
    properties:
      page:
        type: integer
      pageCount:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  metrics_monitoring.Response:
    properties:
      code:
        type: integer
      data: {}
    type: object
  metrics_monitoring.TimeRange:
    properties:
      endTime:
        type: string
      startTime:
        type: string
    type: object
  models.ErrorResponse:
    properties:
      details:
        description: 错误详情
        type: string
      error:
        description: 错误信息
        type: string
      request_id:
        description: 请求ID
        type: string
    type: object
  models.Response:
    properties:
      code:
        description: 状态码
        type: integer
      data:
        description: 响应数据
    type: object
  task_collaboration.PaginationInfo:
    properties:
      limit:
        type: integer
      offset:
        type: integer
      page:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  task_collaboration.TaskAlertListResponse:
    properties:
      alerts:
        items:
          $ref: '#/definitions/task_collaboration.TaskRelatedAlert'
        type: array
      pagination:
        $ref: '#/definitions/task_collaboration.PaginationInfo'
      statistics:
        $ref: '#/definitions/task_collaboration.TaskAlertStatistics'
      taskId:
        type: string
    type: object
  task_collaboration.TaskAlertStatistics:
    properties:
      deviceCount:
        additionalProperties:
          type: integer
        type: object
      levelCount:
        additionalProperties:
          type: integer
        type: object
      phaseCount:
        additionalProperties:
          type: integer
        type: object
      statusCount:
        additionalProperties:
          type: integer
        type: object
      taskId:
        type: string
      timeDistribution:
        items:
          $ref: '#/definitions/task_collaboration.TimeSlotCount'
        type: array
      timeRange:
        $ref: '#/definitions/task_collaboration.TimeRange'
      totalCount:
        type: integer
    type: object
  task_collaboration.TaskMonitoringRequest:
    properties:
      deviceIds:
        items:
          type: string
        minItems: 1
        type: array
    required:
    - deviceIds
    type: object
  task_collaboration.TaskMonitoringResponse:
    properties:
      deviceCount:
        type: integer
      duration:
        type: string
      endTime:
        type: string
      message:
        type: string
      sessionId:
        type: string
      startTime:
        type: string
      success:
        type: boolean
      taskId:
        type: string
    type: object
  task_collaboration.TaskRelatedAlert:
    properties:
      deviceId:
        type: string
      endsAt:
        type: string
      id:
        type: string
      level:
        type: string
      monitoringPhase:
        type: string
      name:
        type: string
      startsAt:
        type: string
      status:
        type: string
      taskRelation:
        type: string
    type: object
  task_collaboration.TaskSignal:
    properties:
      expected_duration:
        minimum: 0
        type: integer
      port_id:
        description: 逻辑端口ID，选填
        type: string
      port_name:
        description: 端口名称，选填
        type: string
      signal_type:
        enum:
        - start
        - end
        - abort
        type: string
      status:
        description: 基于自动化系统Django choices规范
        enum:
        - SUCCESS
        - FAILURE
        - REVOKED
        type: string
      target_devices:
        items:
          type: string
        minItems: 1
        type: array
      task_id:
        description: 移除UUID验证，允许自定义任务ID格式
        type: string
      task_type:
        enum:
        - CONFIG_DEPLOY
        - CONFIG_RECOVERY
        - CONFIG_DOWNLOAD
        - OTHER
        type: string
      timestamp:
        type: string
    required:
    - signal_type
    - target_devices
    - task_id
    - task_type
    type: object
  task_collaboration.TaskSignalResponse:
    properties:
      message:
        type: string
      sessionId:
        type: string
      success:
        type: boolean
      taskId:
        type: string
    type: object
  task_collaboration.TaskStatusResponse:
    properties:
      deviceCount:
        type: integer
      endTime:
        type: string
      isActive:
        type: boolean
      sessionId:
        type: string
      startTime:
        type: string
      status:
        description: '使用自动化系统标准枚举值: SUCCESS, FAILURE, REVOKED'
        type: string
      taskId:
        type: string
    type: object
  task_collaboration.TimeRange:
    properties:
      endTime:
        type: string
      startTime:
        type: string
    type: object
  task_collaboration.TimeSlotCount:
    properties:
      count:
        type: integer
      timeSlot:
        type: string
    type: object
  topology.SwaggerTopologyEdge:
    description: 拓扑图中的边（连接）
    properties:
      data:
        description: 连接附加数据，JSON格式
      id:
        description: 边ID
        type: string
      source:
        description: 源节点ID
        type: string
      sourcePort:
        description: 源端口ID
        type: string
      target:
        description: 目标节点ID
        type: string
      targetPort:
        description: 目标端口ID
        type: string
      type:
        description: 连接类型：physical、logical等
        type: string
    type: object
  topology.SwaggerTopologyGraph:
    description: 完整的拓扑图
    properties:
      createdAt:
        description: 创建时间
        type: string
      edges:
        description: 边列表
        items:
          $ref: '#/definitions/topology.SwaggerTopologyEdge'
        type: array
      id:
        description: 拓扑图ID
        type: string
      name:
        description: 拓扑图名称
        type: string
      nodes:
        description: 节点列表
        items:
          $ref: '#/definitions/topology.SwaggerTopologyNode'
        type: array
    type: object
  topology.SwaggerTopologyNode:
    description: 拓扑图中的节点（设备）
    properties:
      data:
        description: 节点附加数据，JSON格式
      id:
        description: 节点ID，与设备ID相同
        type: string
      name:
        description: 节点名称
        type: string
      position:
        description: 节点位置（可选）
      type:
        description: 节点类型：switch、router、server等
        type: string
    type: object
  topology.TopologyStats:
    properties:
      edgeCount:
        description: 边数量
        type: integer
      logicalLinks:
        description: 逻辑链接数量
        type: integer
      nodeCount:
        description: 节点数量
        type: integer
      physicalLinks:
        description: 物理链接数量
        type: integer
      routerCount:
        description: 路由器数量
        type: integer
      serverCount:
        description: 服务器数量
        type: integer
      switchCount:
        description: 交换机数量
        type: integer
    type: object
  traffic.PortFlowData:
    properties:
      in_rate:
        type: number
      in_total:
        type: number
      out_rate:
        type: number
      out_total:
        type: number
      unit_rate:
        description: 速率单位, e.g., "Mbps"
        type: string
      unit_total:
        description: 总量单位, e.g., "MB", "GB"
        type: string
    type: object
  traffic.PortFlowQueryDetails:
    properties:
      device_id:
        type: string
      end_time:
        type: string
      port_id:
        type: string
      start_time:
        type: string
      vni:
        type: string
    type: object
  traffic.PortFlowResponse:
    properties:
      flow_data:
        $ref: '#/definitions/traffic.PortFlowData'
      query_details:
        $ref: '#/definitions/traffic.PortFlowQueryDetails'
      request_id:
        type: string
    type: object
  traffic.PortHistoryData:
    properties:
      series:
        description: 数据系列数组
        items:
          $ref: '#/definitions/traffic.TrafficSeries'
        type: array
      timestamps:
        description: RFC3339 UTC 格式的时间戳数组
        items:
          type: string
        type: array
      unit:
        description: 数据单位 (e.g., "Mbps")
        type: string
    type: object
  traffic.PortHistoryQueryDetails:
    properties:
      device_id:
        type: string
      end_time:
        type: string
      port_id:
        type: string
      start_time:
        type: string
      step:
        type: string
      vni:
        type: string
    type: object
  traffic.PortHistoryResponse:
    properties:
      history_data:
        $ref: '#/definitions/traffic.PortHistoryData'
      query_details:
        $ref: '#/definitions/traffic.PortHistoryQueryDetails'
      request_id:
        type: string
    type: object
  traffic.TrafficChartData:
    properties:
      series:
        description: 数据系列数组
        items:
          $ref: '#/definitions/traffic.TrafficSeries'
        type: array
      timestamps:
        description: RFC3339 UTC 格式的时间戳数组
        items:
          type: string
        type: array
      unit:
        description: 数据单位 (e.g., "Mbps", "Kbps")
        type: string
    type: object
  traffic.TrafficSeries:
    properties:
      data:
        description: 与 Timestamps 对应的数值数组
        items:
          type: number
        type: array
      name:
        description: 系列名称 (e.g., "A端 (CE1-GE1/0/1) 平均入流量")
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: DCI监控团队
  description: DCI监控系统提供的告警、设备状态、流量监测等API接口
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: DCI监控系统API
  version: "1.0"
paths:
  /api/v1/alert-rules:
    get:
      description: 获取系统中配置的告警规则列表
      parameters:
      - description: 规则来源
        in: query
        name: source
        type: string
      - description: 是否启用
        in: query
        name: enabled
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: 规则列表
          schema:
            $ref: '#/definitions/alert.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 获取告警规则列表
      tags:
      - 告警规则
    post:
      consumes:
      - application/json
      description: 创建新的告警规则
      parameters:
      - description: 规则定义
        in: body
        name: rule
        required: true
        schema:
          $ref: '#/definitions/alert.AlertRule'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/alert.Response'
            - properties:
                data:
                  $ref: '#/definitions/alert.AlertRule'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/alert.Response'
        "409":
          description: 同名规则已存在
          schema:
            $ref: '#/definitions/alert.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 创建告警规则
      tags:
      - 告警规则
  /api/v1/alert-rules/{id}:
    delete:
      description: 删除现有告警规则
      parameters:
      - description: 规则ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/alert.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/alert.Response'
        "404":
          description: 规则不存在
          schema:
            $ref: '#/definitions/alert.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 删除告警规则
      tags:
      - 告警规则
    get:
      description: 根据规则ID获取告警规则详细信息
      parameters:
      - description: 规则ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 规则详情
          schema:
            allOf:
            - $ref: '#/definitions/alert.Response'
            - properties:
                data:
                  $ref: '#/definitions/alert.AlertRule'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/alert.Response'
        "404":
          description: 规则不存在
          schema:
            $ref: '#/definitions/alert.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 获取告警规则详情
      tags:
      - 告警规则
    put:
      consumes:
      - application/json
      description: 更新现有告警规则
      parameters:
      - description: 规则ID
        in: path
        name: id
        required: true
        type: string
      - description: 规则定义
        in: body
        name: rule
        required: true
        schema:
          $ref: '#/definitions/alert.AlertRule'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/alert.Response'
            - properties:
                data:
                  $ref: '#/definitions/alert.AlertRule'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/alert.Response'
        "404":
          description: 规则不存在
          schema:
            $ref: '#/definitions/alert.Response'
        "409":
          description: 同名规则已存在
          schema:
            $ref: '#/definitions/alert.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 更新告警规则
      tags:
      - 告警规则
  /api/v1/alert-rules/{id}/toggle:
    put:
      consumes:
      - application/json
      description: 切换告警规则的启用状态
      parameters:
      - description: 规则ID
        in: path
        name: id
        required: true
        type: string
      - description: 状态设置
        in: body
        name: toggle
        required: true
        schema:
          $ref: '#/definitions/alert.AlertRuleToggleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 操作成功
          schema:
            $ref: '#/definitions/alert.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/alert.Response'
        "404":
          description: 规则不存在
          schema:
            $ref: '#/definitions/alert.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 启用/禁用告警规则
      tags:
      - 告警规则
  /api/v1/alerts:
    get:
      description: 获取符合筛选条件的告警列表，支持分页
      parameters:
      - description: 告警来源
        in: query
        name: source
        type: string
      - description: 告警级别(critical/warning/info)
        in: query
        name: level
        type: string
      - description: 告警状态(firing/acknowledged/resolved)
        in: query
        name: status
        type: string
      - description: 设备ID
        in: query
        name: device_id
        type: string
      - description: 页码(默认1)
        in: query
        name: page
        type: integer
      - description: 每页数量(默认20，最大100)
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回
          schema:
            allOf:
            - $ref: '#/definitions/alert.Response'
            - properties:
                data:
                  $ref: '#/definitions/alert.AlertListResponse'
              type: object
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 获取告警列表
      tags:
      - 告警管理
  /api/v1/alerts/{id}:
    get:
      description: 根据告警ID获取详细信息
      parameters:
      - description: 告警ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回
          schema:
            allOf:
            - $ref: '#/definitions/alert.Response'
            - properties:
                data:
                  $ref: '#/definitions/alert.Alert'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/alert.Response'
        "404":
          description: 告警不存在
          schema:
            $ref: '#/definitions/alert.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 获取告警详情
      tags:
      - 告警管理
  /api/v1/alerts/{id}/acknowledge:
    put:
      consumes:
      - application/json
      description: 标记告警为已确认状态，表明有人正在处理该告警
      parameters:
      - description: 告警ID
        example: '"a1b2c3d4-e5f6-7890-abcd-ef1234567890"'
        in: path
        name: id
        required: true
        type: string
      - description: 告警确认请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/alert.AlertAcknowledgeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 告警确认成功" example({"code":200,"message":"告警已确认"})
          schema:
            $ref: '#/definitions/alert.AlertAcknowledgeResponse'
        "400":
          description: 请求参数错误" example({"code":400,"data":{"message":"请求体不能为空"}})
          schema:
            $ref: '#/definitions/models.Response'
        "404":
          description: 告警不存在" example({"code":404,"data":{"message":"告警不存在"}})
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: '服务器内部错误" example({"code":500,"data":{"message":"确认告警失败: 数据库错误"}})'
          schema:
            $ref: '#/definitions/models.Response'
      summary: 确认告警
      tags:
      - 告警管理
  /api/v1/alerts/statistics:
    get:
      description: 获取告警的各种统计数据，包括按状态、级别和时间的统计
      produces:
      - application/json
      responses:
        "200":
          description: 统计数据
          schema:
            $ref: '#/definitions/alert.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 获取告警统计
      tags:
      - 告警管理
  /api/v1/alerts/webhook/prometheus:
    post:
      consumes:
      - application/json
      description: 接收并处理Prometheus AlertManager发送的告警通知
      parameters:
      - description: Prometheus告警数据
        in: body
        name: webhook
        required: true
        schema:
          $ref: '#/definitions/alert.PrometheusWebhookRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功处理
          schema:
            $ref: '#/definitions/alert.Response'
        "400":
          description: 请求格式错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 处理Prometheus告警Webhook
      tags:
      - 告警管理
  /api/v1/switches/{deviceID}:
    get:
      consumes:
      - application/json
      description: 根据提供的设备 ID，获取该交换机的当前详细状态信息，包括 CPU 利用率、内存利用率以及所有端口的基本状态列表。
      parameters:
      - description: 要查询状态的设备 ID
        example: CE1
        in: path
        name: deviceID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功获取交换机状态
          schema:
            $ref: '#/definitions/device.SwitchStatus'
        "404":
          description: 交换机未找到
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取交换机完整状态
      tags:
      - 05-设备监控
  /api/v1/switches/{deviceID}/ports/{ifName}:
    get:
      consumes:
      - application/json
      description: |-
        根据路径参数 `deviceID`, `ifName` 和查询参数 `data` 获取特定端口的信息。
        注意: `ifName` 参数使用Base64编码，是为了安全地在URL路径中传递包含特殊字符（如'/'）的接口名称。
        例如，接口 "10GE1/0/1" 应当编码为 "MTBHRS8wLzE="。
      parameters:
      - description: 端口所属的设备 ID
        example: "210"
        in: path
        name: deviceID
        required: true
        type: string
      - description: 要查询的端口名称，使用Base64编码。例如，将 '10GE1/0/1' 编码为 'MTBHRS8wLzE='
        example: '{{''10GE1/0/1''|base64}}'
        in: path
        name: ifName
        required: true
        type: string
      - default: status
        description: 请求的数据类型。如果省略或提供无效值, 默认为 status。
        enum:
        - status
        - upstream
        - downstream
        in: query
        name: data
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 当 data=downstream 时的响应，包含下行流量数据：totalOutBytes, totalOutPkts,
            outUtil等
          schema:
            $ref: '#/definitions/device.PortDownstreamTraffic'
        "400":
          description: 无效的请求参数
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: 指定的设备或端口未找到
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取端口数据 (状态/上行/下行)
      tags:
      - 05-设备监控
  /api/v1/switches/{deviceID}/ports/{ifName}/admin:
    get:
      consumes:
      - application/json
      description: |-
        根据路径参数 `deviceID` 和 `ifName` 获取特定端口的管理状态信息。
        注意: ifName 使用 Base64 编码，不再支持特殊字符的 URL 编码方式。
        例如："100GE1/0/6" 编码为 "MTAwR0UxLzAvNg=="
      parameters:
      - description: 端口所属的设备 ID
        example: "210"
        in: path
        name: deviceID
        required: true
        type: string
      - description: 要查询的端口名称，使用Base64编码
        example: MTAwR0UxLzAvNg==
        in: path
        name: ifName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功获取端口管理状态
          schema:
            $ref: '#/definitions/device.PortAdminStatus'
        "400":
          description: 无效的请求参数
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: 指定的设备或端口未找到
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取端口管理状态
      tags:
      - 05-设备监控
  /api/v1/switches/{deviceID}/ports/{ifName}/flow:
    get:
      consumes:
      - application/json
      description: |-
        查询单个端口在指定时间范围内的平均速率和总流量。
        时间范围通过 `start_time` 和 `end_time` 指定，格式为 RFC3339 UTC。如果未提供，则默认为最近15分钟。
        注意: ifName 使用 Base64 编码，不再支持特殊字符的 URL 编码方式。
      parameters:
      - description: 设备ID
        example: "210"
        in: path
        name: deviceID
        required: true
        type: string
      - description: 端口名称，使用Base64编码
        example: MTAwR0UxLzAvNg==
        in: path
        name: ifName
        required: true
        type: string
      - description: VNI (Virtual Network Identifier) 标识符，用于进一步筛选流量
        example: "6005002"
        in: query
        name: vni
        type: string
      - description: 查询起始时间 (RFC3339 UTC 格式)。默认为15分钟前
        example: "2025-06-04T10:00:00Z"
        in: query
        name: start_time
        type: string
      - description: 查询结束时间 (RFC3339 UTC 格式)。默认为当前时间
        example: "2025-06-06T10:15:00Z"
        in: query
        name: end_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回端口流量数据
          schema:
            $ref: '#/definitions/traffic.PortFlowResponse'
        "400":
          description: 请求参数无效
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 查询单端口实时流量
      tags:
      - 07-性能管理-单端口流量查询
  /api/v1/switches/{deviceID}/ports/{ifName}/flow/history:
    get:
      consumes:
      - application/json
      description: |-
        查询单个端口在指定时间范围内的历史流量速率时间序列。
        时间范围通过 `start_time` 和 `end_time` 指定，格式为 RFC3339 UTC。
        `step` 参数定义了数据点之间的时间间隔。
        注意: ifName 使用 Base64 编码，不再支持特殊字符的 URL 编码方式。
      parameters:
      - description: 设备ID
        example: "210"
        in: path
        name: deviceID
        required: true
        type: string
      - description: 端口名称，使用Base64编码
        example: '{{''10GE1/0/1''|base64}}'
        in: path
        name: ifName
        required: true
        type: string
      - description: VNI (Virtual Network Identifier) 标识符，用于进一步筛选流量
        example: "6005002"
        in: query
        name: vni
        type: string
      - description: 查询起始时间 (RFC3339 UTC 格式)
        example: "2025-06-18T15:00:00Z"
        in: query
        name: start_time
        required: true
        type: string
      - description: 查询结束时间 (RFC3339 UTC 格式)
        example: "2025-06-18T16:15:00Z"
        in: query
        name: end_time
        required: true
        type: string
      - description: 查询步长, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。默认为'1m'
        example: 5m
        in: query
        name: step
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回端口历史流量数据
          schema:
            $ref: '#/definitions/traffic.PortHistoryResponse'
        "400":
          description: 请求参数无效
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 查询单端口历史流量
      tags:
      - 07-性能管理-单端口流量查询
  /api/v1/system/task-collaboration/collections/{sessionId}:
    get:
      description: 查询指定会话的数据收集状态详情
      parameters:
      - description: 会话ID
        in: path
        name: sessionId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 查询成功
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
              type: object
        "404":
          description: 会话不存在
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.ErrorResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.ErrorResponse'
              type: object
      summary: 获取数据收集状态
      tags:
      - 系统管理
  /api/v1/system/task-collaboration/status:
    get:
      description: 查询任务协同监控系统的运行状态
      produces:
      - application/json
      responses:
        "200":
          description: 查询成功
          schema:
            $ref: '#/definitions/models.Response'
      summary: 获取系统状态
      tags:
      - 系统管理
  /api/v1/tasks/{taskId}/alerts:
    get:
      description: 查询指定任务执行期间相关的告警列表，包括任务执行期间和延展监测期的告警
      parameters:
      - description: 任务ID
        example: '"550e8400-e29b-41d4-a716-446655440000"'
        in: path
        name: taskId
        required: true
        type: string
      - description: 开始时间（RFC3339格式）
        example: '"2025-01-11T10:15:30Z"'
        in: query
        name: start_time
        type: string
      - description: 结束时间（RFC3339格式）
        example: '"2025-01-11T11:45:30Z"'
        in: query
        name: end_time
        type: string
      - default: 20
        description: 每页数量
        example: 20
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        example: 0
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 查询成功" example({"code":200,"data":{"taskId":"550e8400-e29b-41d4-a716-446655440000","alerts":[{"id":"alert-001","name":"Interface
            Down","level":"critical","status":"firing","deviceId":"CE1","startsAt":"2025-01-11T10:20:00Z","endsAt":null,"taskRelation":"任务执行期间","monitoringPhase":"active_execution"}],"statistics":{"totalCount":5,"levelCount":{"critical":1,"warning":3,"info":1},"phaseCount":{"active_execution":4,"post_completion":1}},"pagination":{"total":5,"limit":20,"offset":0}}})
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/task_collaboration.TaskAlertListResponse'
              type: object
        "400":
          description: 请求参数错误" example({"code":400,"data":{"error":"请求参数格式错误","request_id":"789fe92e-6e44-4df4-8734-0bbcc59a2689","details":"invalid
            time format"}})
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.ErrorResponse'
              type: object
        "500":
          description: 服务器内部错误" example({"code":500,"data":{"error":"获取任务告警失败","request_id":"789fe92e-6e44-4df4-8734-0bbcc59a2689","details":"database
            query error"}})
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.ErrorResponse'
              type: object
      summary: 获取任务相关告警
      tags:
      - 任务协同监控
  /api/v1/tasks/{taskId}/alerts/statistics:
    get:
      description: 查询指定任务执行期间的告警统计信息，包括按级别、状态、阶段和设备的统计分布
      parameters:
      - description: 任务ID
        example: '"550e8400-e29b-41d4-a716-446655440000"'
        in: path
        name: taskId
        required: true
        type: string
      - description: 开始时间（RFC3339格式）
        example: '"2025-01-11T10:15:30Z"'
        in: query
        name: start_time
        type: string
      - description: 结束时间（RFC3339格式）
        example: '"2025-01-11T11:45:30Z"'
        in: query
        name: end_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 查询成功" example({"code":200,"data":{"taskId":"550e8400-e29b-41d4-a716-446655440000","timeRange":{"startTime":"2025-01-11T10:15:30Z","endTime":"2025-01-11T11:45:30Z"},"totalCount":5,"levelCount":{"critical":1,"warning":3,"info":1},"statusCount":{"firing":2,"resolved":3},"phaseCount":{"active_execution":4,"post_completion":1},"deviceCount":{"CE1":3,"CE2":2},"timeDistribution":[{"timeSlot":"2025-01-11T10:15:00Z","count":2},{"timeSlot":"2025-01-11T10:30:00Z","count":3}]}})
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/task_collaboration.TaskAlertStatistics'
              type: object
        "400":
          description: 请求参数错误" example({"code":400,"data":{"error":"请求参数格式错误","request_id":"789fe92e-6e44-4df4-8734-0bbcc59a2689","details":"invalid
            time format"}})
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.ErrorResponse'
              type: object
        "500":
          description: 服务器内部错误" example({"code":500,"data":{"error":"获取任务告警统计失败","request_id":"789fe92e-6e44-4df4-8734-0bbcc59a2689","details":"database
            query error"}})
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.ErrorResponse'
              type: object
      summary: 获取任务告警统计
      tags:
      - 任务协同监控
  /api/v1/tasks/{taskId}/metrics-baseline:
    get:
      consumes:
      - application/json
      description: 查询任务的指标基线统计数据，返回任务执行前30分钟的指标统计特征数据
      parameters:
      - description: 任务ID
        example: '"task_20250118_001"'
        in: path
        name: taskId
        required: true
        type: string
      - description: 设备ID筛选
        example: '"device_001"'
        in: query
        name: device_id
        type: string
      - description: 指标名称筛选
        example: '"dci_snmp_status_cpu_usage"'
        in: query
        name: metric_name
        type: string
      - default: 1
        description: 页码
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 20
        description: 每页数量
        in: query
        maximum: 100
        minimum: 1
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.MetricsBaselineListResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "404":
          description: 未找到指标监测会话
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
      summary: 查询指标基线统计数据
      tags:
      - 指标基线查询
  /api/v1/tasks/{taskId}/metrics-comparison:
    get:
      consumes:
      - application/json
      description: 查询任务前后的指标对比数据，返回基线值与实时值的对比数据，供前端展示和人工分析
      parameters:
      - description: 任务ID
        example: '"task_20250118_001"'
        in: path
        name: taskId
        required: true
        type: string
      - description: 设备ID筛选
        example: '"device_001"'
        in: query
        name: device_id
        type: string
      - description: 指标名称筛选
        example: '"dci_snmp_status_cpu_usage"'
        in: query
        name: metric_name
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.MetricsComparisonResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "404":
          description: 未找到指标监测会话
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
      summary: 查询指标对比数据
      tags:
      - 指标基线查询
  /api/v1/tasks/{taskId}/metrics-data:
    get:
      consumes:
      - application/json
      description: 查询任务期间的指标数据记录（时序数据），支持按设备、指标类型、监测阶段、时间范围筛选
      parameters:
      - description: 任务ID
        example: '"task_20250118_001"'
        in: path
        name: taskId
        required: true
        type: string
      - description: 设备ID筛选
        example: '"device_001"'
        in: query
        name: device_id
        type: string
      - description: 指标名称筛选
        example: '"dci_snmp_status_cpu_usage"'
        in: query
        name: metric_name
        type: string
      - description: 监测阶段筛选
        enum:
        - baseline
        - task_execution
        - extended
        example: '"task_execution"'
        in: query
        name: monitoring_phase
        type: string
      - description: 开始时间
        example: '"2025-01-18T09:30:00+08:00"'
        format: date-time
        in: query
        name: start_time
        type: string
      - description: 结束时间
        example: '"2025-01-18T10:30:00+08:00"'
        format: date-time
        in: query
        name: end_time
        type: string
      - default: 1
        description: 页码
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 20
        description: 每页数量
        in: query
        maximum: 100
        minimum: 1
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.MetricsDataListResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "404":
          description: 未找到指标监测会话
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
      summary: 查询指标数据记录
      tags:
      - 指标数据查询
  /api/v1/tasks/{taskId}/metrics-data/cleanup:
    delete:
      consumes:
      - application/json
      description: 清理指定时间范围外的历史指标数据记录，支持按时间范围清理过期数据，需要按照正确的删除顺序处理
      parameters:
      - description: 任务ID
        example: '"task_20250118_001"'
        in: path
        name: taskId
        required: true
        type: string
      - description: 删除此时间之前的数据
        example: '"2025-01-17T00:00:00+08:00"'
        format: date-time
        in: query
        name: before
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.MetricsCleanupResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "404":
          description: 未找到指标监测会话
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
      summary: 数据清理接口
      tags:
      - 数据管理
  /api/v1/tasks/{taskId}/metrics-monitoring/cascade:
    delete:
      consumes:
      - application/json
      description: 按正确顺序级联删除任务的所有指标相关数据。由于采用了限制删除策略，提供应用层的级联删除功能，按照正确顺序删除数据记录→基线数据→数据记录会话
      parameters:
      - description: 任务ID
        example: '"task_20250118_001"'
        in: path
        name: taskId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.MetricsDeleteResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "404":
          description: 未找到指标监测会话
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
      summary: 级联删除指标数据
      tags:
      - 数据管理
  /api/v1/tasks/{taskId}/metrics-monitoring/dependencies:
    get:
      consumes:
      - application/json
      description: 查询指标数据记录的关联依赖状态，返回各表间的数据关联情况，用于删除前的依赖检查
      parameters:
      - description: 任务ID
        example: '"task_20250118_001"'
        in: path
        name: taskId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.MetricsDependenciesResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "404":
          description: 未找到指标监测会话
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
      summary: 查询数据关联状态
      tags:
      - 数据管理
  /api/v1/tasks/{taskId}/metrics-monitoring/manual-start:
    post:
      consumes:
      - application/json
      description: 手动启动任务的指标数据记录。正常流程：指标数据记录由任务开始信号自动触发，无需手动调用。调试用途：用于开发调试、测试验证或自动启动失败时的补充操作
      parameters:
      - description: 任务ID
        example: '"task_20250118_001"'
        in: path
        name: taskId
        required: true
        type: string
      - description: 启动参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/metrics_monitoring.MetricsStartRequest'
      produces:
      - application/json
      responses:
        "201":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.MetricsStartResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "409":
          description: 指标数据记录会话已存在
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "422":
          description: 参数验证失败
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
      summary: 手动启动指标数据记录（调试用）
      tags:
      - 指标数据记录管理
  /api/v1/tasks/{taskId}/metrics-monitoring/manual-stop:
    post:
      consumes:
      - application/json
      description: 手动停止任务的指标数据记录。正常流程：指标数据记录由任务结束信号自动停止并切换到延展数据记录模式。特殊情况：用于紧急停止、异常处理或测试场景
      parameters:
      - description: 任务ID
        example: '"task_20250118_001"'
        in: path
        name: taskId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.MetricsStopResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "404":
          description: 未找到指标数据记录会话
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
      summary: 手动停止指标数据记录
      tags:
      - 指标数据记录管理
  /api/v1/tasks/{taskId}/metrics-monitoring/status:
    get:
      consumes:
      - application/json
      description: 查询任务指标数据记录会话的当前状态，指标数据记录会话由任务信号自动创建
      parameters:
      - description: 任务ID
        example: '"task_20250118_001"'
        in: path
        name: taskId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.MetricsStatusResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "404":
          description: 任务指标数据记录会话不存在
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
      summary: 查询指标数据记录状态
      tags:
      - 指标数据记录管理
  /api/v1/tasks/{taskId}/metrics-statistics:
    get:
      consumes:
      - application/json
      description: 查询任务的指标统计汇总数据，基于基线表和数据记录表生成统计汇总，包括基线值、当前值、变化幅度等信息
      parameters:
      - description: 任务ID
        example: '"task_20250118_001"'
        in: path
        name: taskId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.MetricsStatisticsResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "404":
          description: 未找到指标监测会话
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
      summary: 查询指标统计汇总
      tags:
      - 指标数据查询
  /api/v1/tasks/{taskId}/metrics-trends:
    get:
      consumes:
      - application/json
      description: 查询任务期间的指标变化趋势数据，基于指标数据记录表生成时间序列图表数据，支持前端可视化展示
      parameters:
      - description: 任务ID
        example: '"task_20250118_001"'
        in: path
        name: taskId
        required: true
        type: string
      - description: 设备ID筛选
        example: '"device_001"'
        in: query
        name: device_id
        type: string
      - description: 指标名称筛选
        example: '"dci_snmp_status_cpu_usage"'
        in: query
        name: metric_name
        type: string
      - default: '"1m"'
        description: 时间间隔
        example: '"5m"'
        in: query
        name: interval
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.MetricsTrendsResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "404":
          description: 未找到指标监测会话
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/metrics_monitoring.Response'
            - properties:
                data:
                  $ref: '#/definitions/metrics_monitoring.ErrorData'
              type: object
      summary: 查询指标趋势数据
      tags:
      - 指标数据查询
  /api/v1/tasks/{taskId}/monitoring/manual/stop:
    post:
      description: 手动停止指定任务的监控会话
      parameters:
      - description: 任务ID
        in: path
        name: taskId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 监控停止成功
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/task_collaboration.TaskMonitoringResponse'
              type: object
        "404":
          description: 任务不存在
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.ErrorResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.ErrorResponse'
              type: object
      summary: 停止任务监控
      tags:
      - 任务协同监控
  /api/v1/tasks/{taskId}/monitoring/start:
    post:
      consumes:
      - application/json
      description: 手动启动指定任务的监控会话，开始收集相关设备的性能指标和日志数据
      parameters:
      - description: 任务ID
        example: '"550e8400-e29b-41d4-a716-446655440000"'
        in: path
        name: taskId
        required: true
        type: string
      - description: 监控请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/task_collaboration.TaskMonitoringRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 监控启动成功" example({"code":200,"data":{"success":true,"message":"任务监控启动成功","taskId":"550e8400-e29b-41d4-a716-446655440000","sessionId":"session-12345","deviceCount":3,"startTime":"2025-01-11T10:15:30Z"}})
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/task_collaboration.TaskMonitoringResponse'
              type: object
        "400":
          description: 请求参数错误" example({"code":400,"data":{"error":"请求参数格式错误","request_id":"789fe92e-6e44-4df4-8734-0bbcc59a2689","details":"deviceIds
            cannot be empty"}})
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.ErrorResponse'
              type: object
        "500":
          description: 服务器内部错误" example({"code":500,"data":{"error":"启动任务监控失败","request_id":"789fe92e-6e44-4df4-8734-0bbcc59a2689","details":"failed
            to create monitoring session"}})
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.ErrorResponse'
              type: object
      summary: 启动任务监控
      tags:
      - 任务协同监控
  /api/v1/tasks/{taskId}/monitoring/status:
    get:
      description: 查询指定任务的监控状态和基本信息
      parameters:
      - description: 任务ID
        in: path
        name: taskId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 查询成功
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/task_collaboration.TaskStatusResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.ErrorResponse'
              type: object
      summary: 获取任务状态
      tags:
      - 任务协同监控
  /api/v1/tasks/signals:
    post:
      consumes:
      - application/json
      parameters:
      - description: 任务信号
        in: body
        name: signal
        required: true
        schema:
          $ref: '#/definitions/task_collaboration.TaskSignal'
      produces:
      - application/json
      responses:
        "200":
          description: 信号处理成功" example({"code":200,"data":{"success":true,"sessionId":"session-12345","message":"任务监控会话已创建","taskId":"550e8400-e29b-41d4-a716-446655440000"}})
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/task_collaboration.TaskSignalResponse'
              type: object
        "400":
          description: '请求参数错误" example({"code":400,"data":{"error":"请求参数格式错误","request_id":"789fe92e-6e44-4df4-8734-0bbcc59a2689","details":"invalid
            signal_type: must be one of [start end abort]"}})'
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.ErrorResponse'
              type: object
        "500":
          description: 服务器内部错误" example({"code":500,"data":{"error":"处理任务信号失败","request_id":"789fe92e-6e44-4df4-8734-0bbcc59a2689","details":"database
            connection error"}})
          schema:
            allOf:
            - $ref: '#/definitions/models.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.ErrorResponse'
              type: object
      tags:
      - 任务协同监控
  /api/v1/topology:
    get:
      consumes:
      - application/json
      description: 获取当前网络拓扑图，包括所有设备节点和连接
      produces:
      - application/json
      responses:
        "200":
          description: 成功获取拓扑图
          schema:
            $ref: '#/definitions/topology.SwaggerTopologyGraph'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取当前拓扑图
      tags:
      - 拓扑管理
  /api/v1/topology/health:
    get:
      consumes:
      - application/json
      description: 获取拓扑服务的健康状态，包括数据库连接、Kafka连接等
      produces:
      - application/json
      responses:
        "200":
          description: 成功获取健康状态
          schema:
            additionalProperties: true
            type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取拓扑服务健康状态
      tags:
      - 拓扑管理
  /api/v1/topology/stats:
    get:
      consumes:
      - application/json
      description: 获取当前拓扑图的统计信息，如节点数量和连接数量
      produces:
      - application/json
      responses:
        "200":
          description: 成功获取拓扑统计信息
          schema:
            $ref: '#/definitions/topology.TopologyStats'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取拓扑统计信息
      tags:
      - 拓扑管理
  /api/v1/traffic/chart/average:
    get:
      consumes:
      - application/json
      description: |-
        查询A-Z链路在指定时间范围内的平均流量速率时间序列。支持通过VNI进行筛选。
        **注意: `a_port_id` 和 `z_port_id` 中的特殊字符 (如 '/') 需要进行 URL 编码。**
      parameters:
      - description: A端设备ID
        example: CE1
        in: query
        name: a_switch_id
        required: true
        type: string
      - description: A端端口ID (URL Encoded)
        example: GE1%2F0%2F1
        in: query
        name: a_port_id
        required: true
        type: string
      - description: Z端设备ID
        example: CE2
        in: query
        name: z_switch_id
        required: true
        type: string
      - description: Z端端口ID (URL Encoded)
        example: GE1%2F0%2F1
        in: query
        name: z_port_id
        required: true
        type: string
      - description: 查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m',
          '1h')。
        example: 1m
        in: query
        name: granularity
        required: true
        type: string
      - description: VNI (Virtual Network Identifier) 标识符
        example: "6005002"
        in: query
        name: vni
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回平均流量数据
          schema:
            $ref: '#/definitions/traffic.TrafficChartData'
        "400":
          description: 请求参数无效
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取A-Z链路平均流量图
      tags:
      - 06-性能管理-A-Z链路流量
  /api/v1/traffic/chart/maximum:
    get:
      consumes:
      - application/json
      description: |-
        查询A-Z链路在指定时间范围内的峰值流量速率时间序列。支持通过VNI进行筛选。
        **注意: `a_port_id` 和 `z_port_id` 中的特殊字符 (如 '/') 需要进行 URL 编码。**
      parameters:
      - description: A端设备ID
        example: CE1
        in: query
        name: a_switch_id
        required: true
        type: string
      - description: A端端口ID (URL Encoded)
        example: GE1%2F0%2F1
        in: query
        name: a_port_id
        required: true
        type: string
      - description: Z端设备ID
        example: CE2
        in: query
        name: z_switch_id
        required: true
        type: string
      - description: Z端端口ID (URL Encoded)
        example: GE1%2F0%2F1
        in: query
        name: z_port_id
        required: true
        type: string
      - description: 查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m',
          '1h')。
        example: 1m
        in: query
        name: granularity
        required: true
        type: string
      - description: VNI (Virtual Network Identifier) 标识符
        example: "6005002"
        in: query
        name: vni
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回峰值流量数据
          schema:
            $ref: '#/definitions/traffic.TrafficChartData'
        "400":
          description: 请求参数无效
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取A-Z链路最大(峰值)流量图
      tags:
      - 06-性能管理-A-Z链路流量
  /api/v1/traffic/chart/minimum:
    get:
      consumes:
      - application/json
      description: |-
        查询A-Z链路在指定时间范围内的谷值流量速率时间序列。支持通过VNI进行筛选。
        **注意: `a_port_id` 和 `z_port_id` 中的特殊字符 (如 '/') 需要进行 URL 编码。**
      parameters:
      - description: A端设备ID
        example: CE1
        in: query
        name: a_switch_id
        required: true
        type: string
      - description: A端端口ID (URL Encoded)
        example: GE1%2F0%2F1
        in: query
        name: a_port_id
        required: true
        type: string
      - description: Z端设备ID
        example: CE2
        in: query
        name: z_switch_id
        required: true
        type: string
      - description: Z端端口ID (URL Encoded)
        example: GE1%2F0%2F1
        in: query
        name: z_port_id
        required: true
        type: string
      - description: 查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m',
          '1h')。
        example: 1m
        in: query
        name: granularity
        required: true
        type: string
      - description: VNI (Virtual Network Identifier) 标识符
        example: "6005002"
        in: query
        name: vni
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回谷值流量数据
          schema:
            $ref: '#/definitions/traffic.TrafficChartData'
        "400":
          description: 请求参数无效
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取A-Z链路最小(谷值)流量图
      tags:
      - 06-性能管理-A-Z链路流量
  /api/v1/traffic/summary:
    get:
      consumes:
      - application/json
      description: |-
        获取A-Z链路在指定时间范围内的流量摘要信息，如总流量、平均/峰值/谷值速率等。 **注意：此接口当前尚未实现。**
        **注意: `a_port_id` 和 `z_port_id` 中的特殊字符 (如 '/') 需要进行 URL 编码。**
      parameters:
      - description: A端设备ID
        example: CE1
        in: query
        name: a_switch_id
        required: true
        type: string
      - description: A端端口ID (URL Encoded)
        example: GE1%2F0%2F1
        in: query
        name: a_port_id
        required: true
        type: string
      - description: Z端设备ID
        example: CE2
        in: query
        name: z_switch_id
        required: true
        type: string
      - description: Z端端口ID (URL Encoded)
        example: GE1%2F0%2F1
        in: query
        name: z_port_id
        required: true
        type: string
      - description: 查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m',
          '1h')。
        example: 1m
        in: query
        name: granularity
        required: true
        type: string
      - description: VNI (Virtual Network Identifier) 标识符
        example: "6005002"
        in: query
        name: vni
        type: string
      produces:
      - application/json
      responses:
        "501":
          description: 接口未实现
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取A-Z链路流量摘要
      tags:
      - 06-性能管理-A-Z链路流量
schemes:
- http
- https
swagger: "2.0"
