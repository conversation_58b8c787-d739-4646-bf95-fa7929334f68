# DCI监控系统 - 拓扑处理器使用说明

## 概述

拓扑处理器是DCI监控系统的核心组件之一，负责收集、处理和管理网络拓扑信息。它通过消费Kafka中的LLDP数据，构建网络设备之间的连接关系，并提供拓扑图的存储、查询和变更通知功能。

## 主要功能

- **LLDP数据处理**：解析来自Telegraf的LLDP原始数据
- **设备标识符映射**：将各种设备标识符（IP、主机名、MAC等）映射到统一的系统ID
- **拓扑图构建**：基于LLDP邻居关系构建网络拓扑图
- **拓扑快照**：定期创建拓扑快照，记录网络拓扑变化历史
- **变更通知**：将拓扑变更实时发送到Kafka，供其他系统消费
- **健康监控**：提供组件健康状态监控和统计信息

## 安装与配置

### 编译

```bash
cd dci-monitor/src/dcimonitor
./build.sh -p macos
```

### 配置

配置文件示例位于`dci-monitor/src/dcimonitor/config/config.yaml`，包含以下主要配置项：

- **数据库配置**：连接到MySQL数据库
- **Kafka配置**：主题设置、消费者组、安全设置等
- **日志配置**：日志级别、文件路径等
- **拓扑处理器配置**：快照间隔、健康检查等

## 使用方法

### 启动拓扑处理器

```bash
./bin/dcimonitor topology processor --config=config/config.yaml
```

### 查询拓扑数据

```bash
# 查询当前拓扑数据
./bin/dcimonitor topology query --config=config/config.yaml

# 指定快照ID查询
./bin/dcimonitor topology query --config=config/config.yaml --snapshot=<snapshot-id>

# 导出为DOT格式
./bin/dcimonitor topology query --config=config/config.yaml --format=dot --output=topology.dot
```

### 导出拓扑数据

```bash
# 导出为JSON格式
./bin/dcimonitor topology export --config=config/config.yaml --output=topology.json

# 导出为DOT格式
./bin/dcimonitor topology export --config=config/config.yaml --format=dot --output=topology.dot
```

### 管理拓扑快照

```bash
# 列出所有快照
./bin/dcimonitor topology list-snapshots --config=config/config.yaml

# 创建新快照
./bin/dcimonitor topology manage create-snapshot --config=config/config.yaml --name="每日快照" --desc="日常备份"

# 清理历史数据
./bin/dcimonitor topology manage cleanup --config=config/config.yaml --days=30
```

## 数据流

1. Telegraf采集LLDP数据并发送到Kafka
2. 拓扑处理器从Kafka消费LLDP数据
3. LLDP处理器解析数据，提取邻居关系
4. 标识符服务将设备标识符映射为系统ID
5. 拓扑构建器更新拓扑图
6. 拓扑变更通过Kafka发送到下游系统

## 日志与监控

拓扑处理器的日志默认保存在`logs/`目录下，可通过调整配置文件中的日志级别获取更详细的调试信息。

## 常见问题排查

### 连接数据库失败

- 检查数据库配置是否正确
- 确认数据库是否已启动
- 验证数据库用户是否有足够权限

### Kafka连接问题

- 确认Kafka服务是否可达
- 检查主题是否已创建
- 验证消费者组是否正确

### LLDP数据不完整

- 检查Telegraf采集配置
- 确保路由器/交换机已启用LLDP
- 查看LLDP缓存表中的映射状态

## 附录

### 支持的LLDP OID列表

拓扑处理器支持处理以下LLDP MIB OID采集的数据：

- `lldpLocPortId`：本地端口ID
- `lldpLocPortDesc`：本地端口描述
- `lldpRemChassisId`：远程设备机箱ID
- `lldpRemPortId`：远程端口ID
- `lldpRemPortDesc`：远程端口描述
- `lldpRemSysName`：远程系统名称

### 数据库表结构

详细的数据库表结构可以在`dci-monitor/sqls/topology_lldp_tables.sql`文件中查看。

### 相关资源

- [LLDP协议介绍](https://en.wikipedia.org/wiki/Link_Layer_Discovery_Protocol)
- [SNMP LLDP MIB](http://www.oid-info.com/get/1.0.8802.1.1.2)
- [拓扑数据技术设计文档](../../documents/designs/03-dci_拓扑数据技术设计.md) 