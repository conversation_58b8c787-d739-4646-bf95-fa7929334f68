#!/bin/bash

# 脚本：创建并推送一个 Git 附注标签 (Annotated Tag)

# 退出设置：如果任何命令失败，则立即退出
set -e

# --- 检查是否在 Git 仓库中 ---
if ! git rev-parse --is-inside-work-tree > /dev/null 2>&1; then
    echo "错误：当前目录不是一个 Git 仓库。"
    exit 1
fi

# --- 检查是否有未提交的更改 ---
if ! git diff-index --quiet HEAD --; then
    echo "警告：您有未提交的更改。建议在打标签前提交所有更改。"
    read -p "是否仍然继续？[y/N]: " confirm_uncommitted
    if [[ ! "$confirm_uncommitted" =~ ^[yY]$ ]]; then
        echo "操作已取消。"
        exit 1
    fi
fi

# --- 获取版本标签 ---
read -p "请输入要创建的版本标签 (例如, 0.0.4 或 v0.0.4): " VERSION_TAG
if [ -z "$VERSION_TAG" ]; then
    echo "错误：版本标签不能为空。"
    exit 1
fi

# --- 检查标签是否已存在 ---
if git rev-parse "$VERSION_TAG" >/dev/null 2>&1; then
    echo "错误：标签 '$VERSION_TAG' 已经存在。"
    exit 1
fi

echo "--------------------------------------------------"
echo "即将创建标签: $VERSION_TAG"
echo "接下来将打开您的默认 Git 编辑器，请在其中输入详细的发布说明。"
echo "保存并关闭编辑器后，标签将被创建。"
echo "--------------------------------------------------"
read -p "准备好了吗？按 Enter 继续，或按 Ctrl+C 取消..."

# --- 创建附注标签 (打开编辑器) ---
# '-a' 创建附注标签，不带 '-m' 或 '-F' 会自动打开编辑器
git tag -a "$VERSION_TAG"
if [ $? -ne 0 ]; then
    echo "错误：创建标签失败。可能是编辑器被异常关闭或 Git 出现问题。"
    exit 1
fi
echo "标签 '$VERSION_TAG' 已在本地成功创建。"
echo ""

# --- 确认是否推送标签 ---
read -p "是否要将标签 '$VERSION_TAG' 推送到远程仓库 'origin'？[y/N]: " confirm_push
if [[ "$confirm_push" =~ ^[yY]$ ]]; then
    echo "正在推送标签 '$VERSION_TAG' 到 origin..."
    git push origin "$VERSION_TAG"
    if [ $? -ne 0 ]; then
        echo "错误：推送标签失败。请检查您的网络连接和远程仓库权限。"
        exit 1
    fi
    echo "标签成功推送到 origin！"
else
    echo "跳过推送标签。您之后可以手动运行 'git push origin $VERSION_TAG'。"
fi

echo "脚本执行完毕。"
exit 0
