package topology

import (
	"common/logger"
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"dcimonitor/internal/topology"

	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

var (
	snapshotID string
	outputFile string
	format     string
)

// queryCmd 表示拓扑查询命令
var queryCmd = &cobra.Command{
	Use:   "query",
	Short: "查询拓扑数据",
	Long:  `查询拓扑数据，支持从数据库获取拓扑数据并导出为JSON或DOT格式。`,
	RunE: func(cmd *cobra.Command, args []string) error {
		return queryTopology()
	},
}

// exportCmd 表示拓扑导出命令
var exportCmd = &cobra.Command{
	Use:   "export",
	Short: "导出拓扑数据",
	Long:  `导出拓扑数据为指定格式，支持JSON和DOT格式。`,
	RunE: func(cmd *cobra.Command, args []string) error {
		return exportTopology()
	},
}

// listSnapshotsCmd 表示列出拓扑快照命令
var listSnapshotsCmd = &cobra.Command{
	Use:   "list-snapshots",
	Short: "列出所有拓扑快照",
	Long:  `列出数据库中所有可用的拓扑快照信息。`,
	RunE: func(cmd *cobra.Command, args []string) error {
		return listTopologySnapshots()
	},
}

func init() {
	// 将查询命令添加到拓扑命令
	TopologyCmd.AddCommand(queryCmd)
	TopologyCmd.AddCommand(exportCmd)
	TopologyCmd.AddCommand(listSnapshotsCmd)

	// 添加查询命令的标志
	queryCmd.Flags().StringVar(&configFile, "config", "", "配置文件路径 (必需)")
	queryCmd.MarkFlagRequired("config")
	queryCmd.Flags().StringVar(&snapshotID, "snapshot", "", "指定快照ID (可选，默认使用当前快照)")
	queryCmd.Flags().StringVarP(&outputFile, "output", "o", "", "输出文件路径 (可选，默认输出到控制台)")
	queryCmd.Flags().StringVarP(&format, "format", "f", "json", "输出格式 (json 或 dot)")

	// 添加导出命令的标志
	exportCmd.Flags().StringVar(&configFile, "config", "", "配置文件路径 (必需)")
	exportCmd.MarkFlagRequired("config")
	exportCmd.Flags().StringVar(&snapshotID, "snapshot", "", "指定快照ID (可选，默认使用当前快照)")
	exportCmd.Flags().StringVarP(&outputFile, "output", "o", "", "输出文件路径 (必需)")
	exportCmd.MarkFlagRequired("output")
	exportCmd.Flags().StringVarP(&format, "format", "f", "json", "输出格式 (json 或 dot)")

	// 添加列出快照命令的标志
	listSnapshotsCmd.Flags().StringVar(&configFile, "config", "", "配置文件路径 (必需)")
	listSnapshotsCmd.MarkFlagRequired("config")
}

// queryTopology 查询拓扑数据
func queryTopology() error {
	// 加载配置文件
	config, err := loadConfig(configFile)
	if err != nil {
		fmt.Printf("加载配置文件失败: %v\n", err)
		return err
	}

	// 初始化日志
	if err := initLogger(config.Log); err != nil {
		fmt.Printf("初始化日志失败: %v\n", err)
		return err
	}

	// 连接数据库
	db, err := connectDB(config.Database)
	if err != nil {
		logger.Error("连接数据库失败", zap.Error(err))
		return err
	}
	defer db.Close()

	// 获取拓扑数据
	topology, err := getTopologyData(db, snapshotID)
	if err != nil {
		logger.Error("获取拓扑数据失败", zap.Error(err))
		return err
	}

	// 根据格式导出拓扑数据
	var data []byte
	switch format {
	case "json":
		data, err = jsoniter.MarshalIndent(topology, "", "  ")
		if err != nil {
			logger.Error("序列化拓扑数据失败", zap.Error(err))
			return err
		}
	case "dot":
		data = []byte(convertToDOT(topology))
	default:
		return fmt.Errorf("不支持的输出格式: %s", format)
	}

	// 输出结果
	if outputFile != "" {
		// 确保输出目录存在
		dir := filepath.Dir(outputFile)
		if err := os.MkdirAll(dir, os.ModePerm); err != nil {
			return fmt.Errorf("创建输出目录失败: %w", err)
		}

		// 写入文件
		if err := os.WriteFile(outputFile, data, 0644); err != nil {
			return fmt.Errorf("写入输出文件失败: %w", err)
		}

		fmt.Printf("拓扑数据已导出到: %s\n", outputFile)
	} else {
		// 输出到控制台
		fmt.Println(string(data))
	}

	return nil
}

// exportTopology 导出拓扑数据
func exportTopology() error {
	// 直接调用查询功能
	return queryTopology()
}

// listTopologySnapshots 列出所有拓扑快照
func listTopologySnapshots() error {
	// 加载配置文件
	config, err := loadConfig(configFile)
	if err != nil {
		fmt.Printf("加载配置文件失败: %v\n", err)
		return err
	}

	// 初始化日志
	if err := initLogger(config.Log); err != nil {
		fmt.Printf("初始化日志失败: %v\n", err)
		return err
	}

	// 连接数据库
	db, err := connectDB(config.Database)
	if err != nil {
		logger.Error("连接数据库失败", zap.Error(err))
		return err
	}
	defer db.Close()

	// 查询快照列表
	rows, err := db.Query(`
		SELECT id, name, description, node_count, edge_count, is_current, create_time
		FROM monitor_topology_snapshot 
		ORDER BY create_time DESC
	`)
	if err != nil {
		logger.Error("查询拓扑快照失败", zap.Error(err))
		return err
	}
	defer rows.Close()

	// 打印表头
	fmt.Printf("%-36s %-30s %-10s %-10s %-10s %-20s\n", "快照ID", "名称", "节点数", "边数", "当前状态", "创建时间")
	fmt.Println(strings.Repeat("-", 120))

	// 遍历结果
	for rows.Next() {
		var (
			id          string
			name        string
			description sql.NullString
			nodeCount   int
			edgeCount   int
			isCurrent   bool
			createTime  time.Time
		)

		if err := rows.Scan(&id, &name, &description, &nodeCount, &edgeCount, &isCurrent, &createTime); err != nil {
			logger.Error("扫描行数据失败", zap.Error(err))
			continue
		}

		// 当前状态显示
		currentStatus := "否"
		if isCurrent {
			currentStatus = "是 *"
		}

		// 格式化输出
		fmt.Printf("%-36s %-30s %-10d %-10d %-10s %-20s\n",
			id, name, nodeCount, edgeCount, currentStatus, createTime.Format("2006-01-02 15:04:05"))
	}

	if err := rows.Err(); err != nil {
		logger.Error("遍历结果集时发生错误", zap.Error(err))
		return err
	}

	return nil
}

// getTopologyData 从数据库获取拓扑数据
func getTopologyData(db *sql.DB, snapshotID string) (*topology.TopologyGraph, error) {
	// 如果未指定快照ID，获取当前快照ID
	if snapshotID == "" {
		err := db.QueryRow(`
			SELECT id FROM monitor_topology_snapshot 
			WHERE is_current = 1 
			ORDER BY create_time DESC 
			LIMIT 1
		`).Scan(&snapshotID)
		if err != nil {
			return nil, fmt.Errorf("无法获取当前快照ID: %w", err)
		}
	}

	// 获取快照基本信息
	var name string
	var createdAt time.Time
	err := db.QueryRow(`
		SELECT name, create_time
		FROM monitor_topology_snapshot
		WHERE id = ?
	`, snapshotID).Scan(&name, &createdAt)
	if err != nil {
		return nil, fmt.Errorf("获取快照信息失败: %w", err)
	}

	// 初始化拓扑图
	topo := &topology.TopologyGraph{
		ID:        snapshotID,
		Name:      name,
		CreatedAt: createdAt,
		Nodes:     []*topology.TopologyNode{},
		Edges:     []*topology.TopologyEdge{},
	}

	// 获取节点数据
	nodeRows, err := db.Query(`
		SELECT id, device_id, node_type, properties 
		FROM monitor_topology_node 
		WHERE snapshot_id = ?
	`, snapshotID)
	if err != nil {
		return nil, fmt.Errorf("查询节点数据失败: %w", err)
	}
	defer nodeRows.Close()

	// 遍历节点数据
	for nodeRows.Next() {
		var node topology.TopologyNode
		var propertiesJSON []byte
		var deviceID string

		if err := nodeRows.Scan(&node.ID, &deviceID, &node.Type, &propertiesJSON); err != nil {
			return nil, fmt.Errorf("扫描节点行数据失败: %w", err)
		}

		if len(propertiesJSON) > 0 {
			node.Data = propertiesJSON
		}

		// 查询设备名称
		var deviceName string
		err := db.QueryRow(`
			SELECT device_name FROM monitor_device_info WHERE id = ?
		`, deviceID).Scan(&deviceName)
		if err == nil {
			node.Name = deviceName
		} else if err != sql.ErrNoRows {
			logger.Warn("获取设备名称失败", zap.String("deviceID", deviceID), zap.Error(err))
		}

		topo.Nodes = append(topo.Nodes, &node)
	}

	// 获取边数据
	edgeRows, err := db.Query(`
		SELECT id, source_node_id, target_node_id, source_port_id, target_port_id, edge_type, properties 
		FROM monitor_topology_edge 
		WHERE snapshot_id = ?
	`, snapshotID)
	if err != nil {
		return nil, fmt.Errorf("查询边数据失败: %w", err)
	}
	defer edgeRows.Close()

	// 遍历边数据
	for edgeRows.Next() {
		var edge topology.TopologyEdge
		var propertiesJSON []byte

		if err := edgeRows.Scan(&edge.ID, &edge.Source, &edge.Target, &edge.SourcePort, &edge.TargetPort, &edge.Type, &propertiesJSON); err != nil {
			return nil, fmt.Errorf("扫描边行数据失败: %w", err)
		}

		if len(propertiesJSON) > 0 {
			edge.Data = propertiesJSON
		}

		topo.Edges = append(topo.Edges, &edge)
	}

	return topo, nil
}

// convertToDOT 将拓扑数据转换为DOT格式
func convertToDOT(topo *topology.TopologyGraph) string {
	// 创建DOT格式的图表示
	var sb strings.Builder

	sb.WriteString(fmt.Sprintf("digraph %s {\n", strings.Replace(topo.Name, " ", "_", -1)))
	sb.WriteString("  // Graph settings\n")
	sb.WriteString("  graph [fontname=\"SimHei\", rankdir=LR, splines=true];\n")
	sb.WriteString("  node [shape=box, style=filled, fillcolor=lightblue, fontname=\"SimHei\"];\n")
	sb.WriteString("  edge [fontname=\"SimHei\"];\n\n")

	// 添加节点
	sb.WriteString("  // Nodes\n")
	nodeNames := make(map[string]string)
	for _, node := range topo.Nodes {
		nodeName := fmt.Sprintf("node_%s", node.ID)
		nodeNames[node.ID] = nodeName

		// 节点标签
		label := node.Name
		if label == "" {
			label = node.ID
		}

		// 节点样式
		nodeStyle := "fillcolor=lightblue"
		if node.Type == "device" {
			nodeStyle = "fillcolor=lightgreen"
		}

		sb.WriteString(fmt.Sprintf("  %s [label=\"%s\", %s];\n", nodeName, label, nodeStyle))
	}
	sb.WriteString("\n")

	// 添加边
	sb.WriteString("  // Edges\n")
	for _, edge := range topo.Edges {
		sourceNode, sourceExists := nodeNames[edge.Source]
		targetNode, targetExists := nodeNames[edge.Target]

		if !sourceExists || !targetExists {
			continue
		}

		// 边标签
		label := ""
		if edge.SourcePort != "" && edge.TargetPort != "" {
			label = fmt.Sprintf("%s -> %s", edge.SourcePort, edge.TargetPort)
		}

		// 边样式
		edgeStyle := ""
		if edge.Type == "physical" {
			edgeStyle = "color=blue"
		} else if edge.Type == "logical" {
			edgeStyle = "color=green, style=dashed"
		}

		// 添加边
		if label != "" {
			sb.WriteString(fmt.Sprintf("  %s -> %s [label=\"%s\", %s];\n", sourceNode, targetNode, label, edgeStyle))
		} else {
			sb.WriteString(fmt.Sprintf("  %s -> %s [%s];\n", sourceNode, targetNode, edgeStyle))
		}
	}

	sb.WriteString("}\n")
	return sb.String()
}
