package topology

import (
	"common/logger"
	"context"
	"database/sql"
	"fmt"

	"dcimonitor/internal/topology"

	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

var (
	snapshotName        string
	snapshotDescription string
	cleanupDays         int
)

// manageCmd 表示拓扑管理命令
var manageCmd = &cobra.Command{
	Use:   "manage",
	Short: "管理拓扑数据",
	Long:  `管理拓扑数据，包括创建快照、清理历史数据等操作。`,
}

// createSnapshotCmd 表示创建拓扑快照命令
var createSnapshotCmd = &cobra.Command{
	Use:   "create-snapshot",
	Short: "创建拓扑快照",
	Long:  `从当前拓扑数据创建新的快照。`,
	RunE: func(cmd *cobra.Command, args []string) error {
		return createTopologySnapshot()
	},
}

// cleanupCmd 表示清理拓扑数据命令
var cleanupCmd = &cobra.Command{
	Use:   "cleanup",
	Short: "清理历史拓扑数据",
	Long:  `清理指定天数之前的历史拓扑数据，包括LLDP缓存和过期的标识符映射。`,
	RunE: func(cmd *cobra.Command, args []string) error {
		return cleanupTopologyData()
	},
}

func init() {
	// 将管理命令添加到拓扑命令
	TopologyCmd.AddCommand(manageCmd)

	// 将子命令添加到管理命令
	manageCmd.AddCommand(createSnapshotCmd)
	manageCmd.AddCommand(cleanupCmd)

	// 添加创建快照命令的标志
	createSnapshotCmd.Flags().StringVar(&configFile, "config", "", "配置文件路径 (必需)")
	createSnapshotCmd.MarkFlagRequired("config")
	createSnapshotCmd.Flags().StringVar(&snapshotName, "name", "", "快照名称 (必需)")
	createSnapshotCmd.MarkFlagRequired("name")
	createSnapshotCmd.Flags().StringVar(&snapshotDescription, "desc", "", "快照描述 (可选)")

	// 添加清理命令的标志
	cleanupCmd.Flags().StringVar(&configFile, "config", "", "配置文件路径 (必需)")
	cleanupCmd.MarkFlagRequired("config")
	cleanupCmd.Flags().IntVar(&cleanupDays, "days", 30, "清理指定天数前的数据 (默认30天)")
}

// createTopologySnapshot 创建拓扑快照
func createTopologySnapshot() error {
	// 加载配置文件
	config, err := loadConfig(configFile)
	if err != nil {
		fmt.Printf("加载配置文件失败: %v\n", err)
		return err
	}

	// 初始化日志
	if err := initLogger(config.Log); err != nil {
		fmt.Printf("初始化日志失败: %v\n", err)
		return err
	}

	// 连接数据库
	db, err := connectDB(config.Database)
	if err != nil {
		logger.Error("连接数据库失败", zap.Error(err))
		return err
	}
	defer db.Close()

	// 创建拓扑构建器
	builder := topology.NewTopologyBuilder(db)

	// 初始化拓扑构建器
	if err := builder.Init(context.Background()); err != nil {
		logger.Error("初始化拓扑构建器失败", zap.Error(err))
		return err
	}

	// 创建新快照
	if err := builder.CreateNewSnapshot(context.Background(), snapshotName, snapshotDescription); err != nil {
		logger.Error("创建新快照失败", zap.Error(err))
		return err
	}

	fmt.Printf("成功创建拓扑快照: %s\n", snapshotName)
	return nil
}

// cleanupTopologyData 清理拓扑数据
func cleanupTopologyData() error {
	// 加载配置文件
	config, err := loadConfig(configFile)
	if err != nil {
		fmt.Printf("加载配置文件失败: %v\n", err)
		return err
	}

	// 初始化日志
	if err := initLogger(config.Log); err != nil {
		fmt.Printf("初始化日志失败: %v\n", err)
		return err
	}

	// 连接数据库
	db, err := connectDB(config.Database)
	if err != nil {
		logger.Error("连接数据库失败", zap.Error(err))
		return err
	}
	defer db.Close()

	// 创建标识符服务
	identifierService := topology.NewIdentifierService(db, logger.GetLogger())

	// 清理不活跃标识符
	identifierCount, err := identifierService.CleanupInactiveIdentifiers(context.Background(), cleanupDays)
	if err != nil {
		logger.Error("清理不活跃标识符失败", zap.Error(err))
	} else {
		fmt.Printf("已清理 %d 个不活跃的标识符映射\n", identifierCount)
	}

	// 清理LLDP邻居缓存
	lldpCount, err := identifierService.CleanupOldLLDPCache(context.Background(), cleanupDays)
	if err != nil {
		logger.Error("清理LLDP缓存失败", zap.Error(err))
	} else {
		fmt.Printf("已清理 %d 条过期的LLDP邻居缓存\n", lldpCount)
	}

	// 清理旧的拓扑快照
	snapshotCount, err := cleanupOldSnapshots(db, cleanupDays)
	if err != nil {
		logger.Error("清理拓扑快照失败", zap.Error(err))
	} else {
		fmt.Printf("已清理 %d 个过期的拓扑快照\n", snapshotCount)
	}

	return nil
}

// cleanupOldSnapshots 清理旧的拓扑快照
func cleanupOldSnapshots(db *sql.DB, days int) (int, error) {
	// 获取所有非当前状态且超过指定天数的快照
	ctx := context.Background()
	result, err := db.ExecContext(ctx, `
		DELETE FROM monitor_topology_snapshot 
		WHERE is_current = 0 
		AND create_time < DATE_SUB(NOW(), INTERVAL ? DAY)
	`, days)

	if err != nil {
		return 0, fmt.Errorf("删除旧快照失败: %w", err)
	}

	count, _ := result.RowsAffected()
	return int(count), nil
}
