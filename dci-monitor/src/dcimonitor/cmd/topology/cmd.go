package topology

import (
	"common/logger"
	"context"
	"database/sql"
	"fmt"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"

	"dcimonitor/internal/kafka"
	"dcimonitor/internal/topology"

	_ "github.com/go-sql-driver/mysql"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"gopkg.in/yaml.v3"
)

var configFile string
var logLevel string

// TopologyConfig 定义拓扑处理器通用配置
type TopologyConfig struct {
	// 通用配置
	Database struct {
		Driver   string `yaml:"driver"`
		Host     string `yaml:"host"`
		Port     int    `yaml:"port"`
		Username string `yaml:"username"`
		Password string `yaml:"password"`
		DBName   string `yaml:"dbname"`
		Charset  string `yaml:"charset"`
	} `yaml:"database"`

	Kafka struct {
		Brokers  []string `yaml:"brokers"`
		Security struct {
			TLS struct {
				Enabled  bool   `yaml:"enabled"`
				CertFile string `yaml:"certFile"`
				KeyFile  string `yaml:"keyFile"`
				CAFile   string `yaml:"caFile"`
			} `yaml:"tls"`
			SASL struct {
				Enabled   bool   `yaml:"enabled"`
				Mechanism string `yaml:"mechanism"`
				Username  string `yaml:"username"`
				Password  string `yaml:"password"`
			} `yaml:"sasl"`
		} `yaml:"security"`
		Topics struct {
			RawTopology       string `yaml:"rawTopology"`
			RawMetrics        string `yaml:"rawMetrics"`
			ProcessedTopology string `yaml:"processedTopology"`
			ProcessedEvents   string `yaml:"processedEvents"`
		} `yaml:"topics"`
		ConsumerGroups struct {
			TopologyProcessor string `yaml:"topologyProcessor"`
		} `yaml:"consumerGroups"`
	} `yaml:"kafka"`

	// 日志配置
	Log struct {
		Level      string `yaml:"level"`
		Filename   string `yaml:"filename"`
		MaxSize    int    `yaml:"maxSize"`
		MaxBackups int    `yaml:"maxBackups"`
		MaxAge     int    `yaml:"maxAge"`
		Compress   bool   `yaml:"compress"`
	} `yaml:"log"`

	// 拓扑处理器配置
	Topology struct {
		SnapshotInterval    time.Duration `yaml:"snapshotInterval"`
		HealthCheckInterval time.Duration `yaml:"healthCheckInterval"`
	} `yaml:"topology"`
}

// Config 定义拓扑处理器的配置
type Config struct {
	// 数据库配置
	Database struct {
		Driver   string `yaml:"driver"`
		Host     string `yaml:"host"`
		Port     int    `yaml:"port"`
		Username string `yaml:"username"`
		Password string `yaml:"password"`
		DBName   string `yaml:"dbname"`
		Charset  string `yaml:"charset"`
	} `yaml:"database"`

	// Kafka 输入配置
	KafkaInput struct {
		Brokers        []string      `yaml:"brokers"`
		ConsumerGroup  string        `yaml:"consumerGroup"`
		Topics         []string      `yaml:"topics"`
		AutoCommit     bool          `yaml:"autoCommit"`
		CommitInterval time.Duration `yaml:"commitInterval"`
		InitialOffset  string        `yaml:"initialOffset"`
		TLS            struct {
			Enabled  bool   `yaml:"enabled"`
			CertFile string `yaml:"certFile"`
			KeyFile  string `yaml:"keyFile"`
			CAFile   string `yaml:"caFile"`
		} `yaml:"tls"`
	} `yaml:"kafkaInput"`

	// Kafka 输出配置
	KafkaOutput struct {
		Brokers       []string      `yaml:"brokers"`
		RequiredAcks  string        `yaml:"requiredAcks"`
		Compression   string        `yaml:"compression"`
		FlushInterval time.Duration `yaml:"flushInterval"`
		TLS           struct {
			Enabled  bool   `yaml:"enabled"`
			CertFile string `yaml:"certFile"`
			KeyFile  string `yaml:"keyFile"`
			CAFile   string `yaml:"caFile"`
		} `yaml:"tls"`
	} `yaml:"kafkaOutput"`

	// 拓扑处理器配置
	Topology struct {
		SnapshotInterval    time.Duration `yaml:"snapshotInterval"`
		HealthCheckInterval time.Duration `yaml:"healthCheckInterval"`
	} `yaml:"topology"`

	// 日志配置
	Log struct {
		Level      string `yaml:"level"`
		Filename   string `yaml:"filename"`
		MaxSize    int    `yaml:"maxSize"`
		MaxBackups int    `yaml:"maxBackups"`
		MaxAge     int    `yaml:"maxAge"`
		Compress   bool   `yaml:"compress"`
	} `yaml:"log"`
}

// TopologyCmd 表示拓扑处理命令
var TopologyCmd = &cobra.Command{
	Use:   "topology",
	Short: "拓扑数据处理服务",
	Long:  `处理来自Kafka的LLDP数据，构建和维护网络拓扑关系。`,
}

// processorCmd 表示启动拓扑处理器的命令
var processorCmd = &cobra.Command{
	Use:   "processor",
	Short: "启动拓扑处理器服务",
	Long:  `启动拓扑处理器服务，从Kafka消费LLDP数据，构建网络拓扑关系，并提供拓扑数据存储和查询。`,
	PreRunE: func(cmd *cobra.Command, args []string) error {
		if configFile == "" {
			return fmt.Errorf("必须提供配置文件路径")
		}
		return nil
	},
	Run: func(cmd *cobra.Command, args []string) {
		startTopologyProcessor()
	},
}

func init() {
	// 将处理器命令添加到拓扑命令
	TopologyCmd.AddCommand(processorCmd)

	// 添加处理器命令的标志
	processorCmd.Flags().StringVar(&configFile, "config", "", "配置文件路径 (必需)")
	processorCmd.MarkFlagRequired("config")
	processorCmd.Flags().StringVar(&logLevel, "log-level", "", "日志级别 (可选，覆盖配置文件)")
}

// startTopologyProcessor 启动拓扑处理器
func startTopologyProcessor() {
	// 加载配置文件
	config, err := loadConfig(configFile)
	if err != nil {
		fmt.Printf("加载配置文件失败: %v\n", err)
		os.Exit(1)
	}

	// 如果命令行指定了日志级别，覆盖配置文件
	if logLevel != "" {
		config.Log.Level = logLevel
	}

	// 初始化日志
	if err := initLogger(config.Log); err != nil {
		fmt.Printf("初始化日志失败: %v\n", err)
		os.Exit(1)
	}

	// 连接数据库
	db, err := connectDB(config.Database)
	if err != nil {
		logger.Error("连接数据库失败", zap.Error(err))
		os.Exit(1)
	}
	defer db.Close()

	// 创建Kafka输入配置
	inputConfig := &kafka.ConsumerConfig{
		Brokers:        config.Kafka.Brokers,
		ConsumerGroup:  config.Kafka.ConsumerGroups.TopologyProcessor,
		Topics:         []string{config.Kafka.Topics.RawTopology},
		AutoCommit:     true,
		CommitInterval: 5 * time.Second,
		InitialOffset:  -1, // 使用最新偏移量
		MaxRetries:     3,
		RetryInterval:  time.Second,
	}

	// TLS配置
	if config.Kafka.Security.TLS.Enabled {
		inputConfig.TLS = &kafka.KafkaTLSConfig{
			Enabled:  true,
			CertFile: config.Kafka.Security.TLS.CertFile,
			KeyFile:  config.Kafka.Security.TLS.KeyFile,
			CAFile:   config.Kafka.Security.TLS.CAFile,
		}
	}

	// SASL配置
	if config.Kafka.Security.SASL.Enabled {
		inputConfig.SASL = &kafka.KafkaSASLConfig{
			Enabled:   true,
			Mechanism: config.Kafka.Security.SASL.Mechanism,
			Username:  config.Kafka.Security.SASL.Username,
			Password:  config.Kafka.Security.SASL.Password,
		}
	}

	// 创建Kafka输出配置
	outputConfig := &kafka.ProducerConfig{
		Brokers:       config.Kafka.Brokers,
		RequiredAcks:  1, // WaitForLocal
		Compression:   0, // CompressionNone
		FlushInterval: time.Second,
		FlushMessages: 100,
		MaxRetries:    3,
		RetryInterval: time.Second,
	}

	// TLS配置
	if config.Kafka.Security.TLS.Enabled {
		outputConfig.TLS = &kafka.KafkaTLSConfig{
			Enabled:  true,
			CertFile: config.Kafka.Security.TLS.CertFile,
			KeyFile:  config.Kafka.Security.TLS.KeyFile,
			CAFile:   config.Kafka.Security.TLS.CAFile,
		}
	}

	// SASL配置
	if config.Kafka.Security.SASL.Enabled {
		outputConfig.SASL = &kafka.KafkaSASLConfig{
			Enabled:   true,
			Mechanism: config.Kafka.Security.SASL.Mechanism,
			Username:  config.Kafka.Security.SASL.Username,
			Password:  config.Kafka.Security.SASL.Password,
		}
	}

	// 创建拓扑处理器配置
	processorConfig := &topology.TopologyProcessorConfig{
		InputKafkaConfig: map[string]interface{}{
			"brokers":        config.Kafka.Brokers,
			"consumer_group": config.Kafka.ConsumerGroups.TopologyProcessor,
		},
		OutputKafkaConfig: map[string]interface{}{
			"brokers": config.Kafka.Brokers,
		},
		SnapshotInterval:    config.Topology.SnapshotInterval.String(),
		HealthCheckInterval: config.Topology.HealthCheckInterval.String(),
	}

	// 创建拓扑处理器
	processor, err := topology.NewTopologyProcessor(db, processorConfig)
	if err != nil {
		logger.Error("创建拓扑处理器失败", zap.Error(err))
		os.Exit(1)
	}

	// 初始化拓扑处理器
	if err := processor.Init(); err != nil {
		logger.Error("初始化拓扑处理器失败", zap.Error(err))
		os.Exit(1)
	}

	// 启动拓扑处理器
	if err := processor.Start(); err != nil {
		logger.Error("启动拓扑处理器失败", zap.Error(err))
		os.Exit(1)
	}

	logger.Info("拓扑处理服务已启动")

	// 设置优雅关闭
	setupGracefulShutdown(processor)
}

// setupGracefulShutdown 设置优雅关闭机制
func setupGracefulShutdown(processor *topology.TopologyProcessor) {
	// 创建一个通道用于接收信号
	sigChan := make(chan os.Signal, 1)
	// 监听中断和终止信号
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 接收信号
	sig := <-sigChan
	logger.Info("收到终止信号，正在关闭服务...", zap.String("signal", sig.String()))

	// 创建一个带有超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 创建一个通道用于通知关闭完成
	done := make(chan struct{})

	// 在一个新的goroutine中执行关闭操作
	go func() {
		// 停止拓扑处理器
		if err := processor.Stop(); err != nil {
			logger.Error("停止拓扑处理器失败", zap.Error(err))
		}

		// 关闭日志
		logger.Sync()

		// 通知关闭完成
		close(done)
	}()

	// 等待关闭完成或超时
	select {
	case <-done:
		logger.Info("拓扑处理服务已正常关闭")
	case <-ctx.Done():
		logger.Warn("关闭服务超时，强制退出")
	}

	// 确保程序退出
	os.Exit(0)
}

// loadConfig 从文件加载配置
func loadConfig(configFile string) (*TopologyConfig, error) {
	// 确保配置文件存在
	absPath, err := filepath.Abs(configFile)
	if err != nil {
		return nil, fmt.Errorf("获取配置文件绝对路径失败: %w", err)
	}

	// 读取配置文件
	data, err := os.ReadFile(absPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析YAML
	config := &TopologyConfig{}
	if err := yaml.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	return config, nil
}

// connectDB 连接数据库
func connectDB(dbConfig struct {
	Driver   string `yaml:"driver"`
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	DBName   string `yaml:"dbname"`
	Charset  string `yaml:"charset"`
}) (*sql.DB, error) {
	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=true&loc=Local",
		dbConfig.Username, dbConfig.Password, dbConfig.Host, dbConfig.Port, dbConfig.DBName, dbConfig.Charset)

	// 连接数据库
	db, err := sql.Open(dbConfig.Driver, dsn)
	if err != nil {
		return nil, fmt.Errorf("无法连接数据库: %w", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %w", err)
	}

	// 配置连接池
	db.SetMaxOpenConns(20)
	db.SetMaxIdleConns(10)
	db.SetConnMaxLifetime(time.Hour)

	return db, nil
}

// initLogger 初始化日志
func initLogger(logConfig struct {
	Level      string `yaml:"level"`
	Filename   string `yaml:"filename"`
	MaxSize    int    `yaml:"maxSize"`
	MaxBackups int    `yaml:"maxBackups"`
	MaxAge     int    `yaml:"maxAge"`
	Compress   bool   `yaml:"compress"`
}) error {
	// 提取日志文件目录
	logDir := filepath.Dir(logConfig.Filename)
	if logDir == "." {
		logDir = "./logs"
	}

	// 确保日志目录存在
	if err := os.MkdirAll(logDir, os.ModePerm); err != nil {
		fmt.Fprintf(os.Stderr, "无法创建日志目录 %s: %v\n", logDir, err)
		return err
	}

	// 设置viper配置，用于common/logger初始化
	viper.Set("logger.level", logConfig.Level)
	viper.Set("logger.dir", logDir)
	viper.Set("logger.maxSize", logConfig.MaxSize)
	viper.Set("logger.maxBackups", logConfig.MaxBackups)
	viper.Set("logger.maxAge", logConfig.MaxAge)
	viper.Set("logger.compress", logConfig.Compress)

	// 初始化common/logger
	logger.InitLogger()

	return nil
}
