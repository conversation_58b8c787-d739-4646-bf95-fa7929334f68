package cmd

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"common/logger"
	"dcimonitor/internal/alert"
	"dcimonitor/internal/device"
	"dcimonitor/internal/metrics_monitoring"
	"dcimonitor/internal/middleware"
	"dcimonitor/internal/task_collaboration"
	"dcimonitor/internal/topology"
	"dcimonitor/internal/traffic"
	"dcimonitor/internal/utils/timeutil"

	"github.com/gin-gonic/gin"
	"github.com/go-sql-driver/mysql"
	"github.com/prometheus/client_golang/api"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"go.uber.org/zap"

	_ "dcimonitor/docs" // 导入生成的 docs 包

	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

var serverCmd = &cobra.Command{
	Use:   "server",
	Short: "启动 API 服务器",
	Long:  `启动 Gin HTTP 服务器来提供网络设备监控 API。需要通过 --config 标志指定配置文件。`,
	PreRunE: func(cmd *cobra.Command, args []string) error {
		if !cmd.Flags().Changed("config") {
			return errors.New("错误：必须使用 --config 标志指定配置文件路径")
		}
		configPath, _ := cmd.Flags().GetString("config")
		if configPath == "" {
			return errors.New("错误：--config 标志的值不能为空")
		}
		return nil
	},
	Run: func(cmd *cobra.Command, args []string) {
		startServer()
	},
}

func init() {
	rootCmd.AddCommand(serverCmd)
}

// cleanPathMiddleware 移除请求路径中多余的前导斜杠
func cleanPathMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if len(c.Request.URL.Path) > 1 && strings.HasPrefix(c.Request.URL.Path, "//") {
			c.Request.URL.Path = "/" + strings.TrimLeft(c.Request.URL.Path, "/")
		}
		c.Next()
	}
}

// startServer a long-running service to handle HTTP requests
func startServer() {
	// 初始化 Zap Logger
	logger.InitLogger()
	defer logger.GetLogger().Sync()

	// 设置全局时区为北京时间(Asia/Shanghai)
	if err := initTimeZone(); err != nil {
		logger.Fatal("初始化时区失败", zap.Error(err))
	}

	// --- 初始化服务依赖 ---
	// 1. 初始化数据库连接
	db, err := initDatabase()
	if err != nil {
		logger.Fatal("初始化数据库连接失败", zap.Error(err))
	}
	defer db.Close()

	// 2. 初始化 Prometheus 客户端
	promClient, err := initPrometheusClient()
	if err != nil {
		logger.Fatal("初始化 Prometheus 客户端失败", zap.Error(err))
	}

	// --- 初始化各功能模块 ---

	// 初始化告警模块
	alertDAO := alert.NewDAO(db, logger.GetLogger())
	taskDAO := alert.NewTaskDAO(db, logger.GetLogger(), nil)
	alertService := alert.NewService(alertDAO, taskDAO, logger.GetLogger(), nil)
	alertHandler := alert.NewHandler(alertService, logger.GetLogger())

	// 初始化设备模块
	deviceDAO := device.NewDAO(db, logger.GetLogger())
	deviceService := device.NewService(deviceDAO)
	deviceService.LoadSwitchesFromConfig()
	deviceHandler := device.NewHandler(deviceService)

	// 初始化流量模块
	trafficDAO := traffic.NewDAO(db, promClient, logger.GetLogger())
	trafficService := traffic.NewService(trafficDAO, nil, nil, logger.GetLogger())
	trafficHandler := traffic.NewHandler(trafficService, logger.GetLogger())

	// 初始化拓扑模块
	topologyDAO := topology.NewDAO(db, logger.GetLogger())
	topologyService, err := topology.NewService(topologyDAO, db, logger.GetLogger())
	if err != nil {
		logger.Error("初始化拓扑服务失败", zap.Error(err))
	} else {
		// 配置拓扑处理器
		topologyConfig := &topology.TopologyProcessorConfig{
			SnapshotInterval:    "1h",
			HealthCheckInterval: "30s",
		}
		if err := topologyService.Init(topologyConfig); err != nil {
			logger.Error("初始化拓扑服务配置失败", zap.Error(err))
		} else {
			if err := topologyService.Start(); err != nil {
				logger.Error("启动拓扑服务失败", zap.Error(err))
			}
		}
	}
	topologyHandler := topology.NewHandler(topologyService, logger.GetLogger())

	// 初始化任务协同监控模块
	var taskCollaborationModule *task_collaboration.Module
	taskCollaborationConfig := getTaskCollaborationConfig()
	if taskCollaborationConfig.Enabled {
		taskCollaborationModule, err = task_collaboration.NewModule(
			db, taskDAO, alertDAO, taskCollaborationConfig, logger.GetLogger())
		if err != nil {
			logger.Error("初始化任务协同监控模块失败", zap.Error(err))
		} else {
			if err := taskCollaborationModule.Start(); err != nil {
				logger.Error("启动任务协同监控模块失败", zap.Error(err))
			} else {
				logger.Info("任务协同监控模块启动成功")
			}
		}
	}

	// 初始化指标监测模块
	metricsConfig := getMetricsMonitoringConfig()
	metricsService := metrics_monitoring.NewService(db, trafficDAO, metricsConfig, logger.GetLogger())
	metricsHandler := metrics_monitoring.NewHandler(metricsService, logger.GetLogger())
	logger.Info("指标监测模块初始化成功")

	// 如果任务协同监控模块已启用，集成指标监测功能
	if taskCollaborationModule != nil && taskCollaborationModule.IsEnabled() {
		// 创建适配器包装metrics_monitoring.Service以符合task_collaboration.MetricsService接口
		metricsAdapter := &metricsServiceAdapter{service: metricsService}
		taskCollaborationModule.Handler.SetMetricsService(metricsAdapter)
		logger.Info("任务协同模块与指标监测模块集成成功")
	}

	// 设置 Gin 模式并创建引擎
	ginMode := viper.GetString("server.mode")
	gin.SetMode(ginMode)
	router := gin.New()
	router.Use(
		cleanPathMiddleware(),
		middleware.RequestIDMiddleware(),
		middleware.GinLogger(),
		middleware.GinRecovery(true),
	)

	// --- 设置 Swagger UI 路由 ---
	// 安全风险处置：使用静态但复杂的路径，避免在生产环境暴露 API 细节。
	if ginMode != gin.ReleaseMode {
		const secretPathHeader = "baibia378nDNID9938h77897"           // 随机字符串
		const secretPathPrefix = "doin78nDNID90jsnJBaaui3ldlDskUOnn1" // 随机字符串
		const secretTailPath = "JsorNID90jsnJBaaui3lpahtT"            // 随机字符串

		// 将 UI 注册在 /<secretPathPrefix>/*any 路径下
		swaggerUIPath := fmt.Sprintf("/%s/%s/%s/*any", secretPathHeader, secretPathPrefix, secretTailPath)
		// 构造 API spec JSON 文件的完整访问路径
		swaggerJSONURL := fmt.Sprintf("/%s/%s/%s/doc.json", secretPathHeader, secretPathPrefix, secretTailPath)

		// 告知 swagger UI 从我们定义的复杂路径加载 API spec
		urlConfig := ginSwagger.URL(swaggerJSONURL)
		router.GET(swaggerUIPath, ginSwagger.WrapHandler(swaggerFiles.Handler, urlConfig))

		// 方便开发者点击的完整 URL
		serverPort := viper.GetInt("server.port")
		fullURL := fmt.Sprintf("http://localhost:%d/%s/%s/%s/index.html", serverPort, secretPathHeader, secretPathPrefix, secretTailPath)
		logger.Info("非生产模式，已启用 Swagger UI，请通过以下静态保密地址访问", zap.String("url", fullURL))

	} else {
		logger.Info("生产模式 (release mode)，Swagger UI 已禁用。")
	}

	// 添加健康检查路由
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// 设置 API 路由
	apiV1 := router.Group("/api/v1")
	{
		// 交换机相关的路由
		switchesGroup := apiV1.Group("/switches")
		{
			// 设备路由
			switchesGroup.GET("/:deviceID", deviceHandler.GetSwitchStatus)
			switchesGroup.GET("/:deviceID/ports/:ifName", deviceHandler.GetPortData)
			switchesGroup.GET("/:deviceID/ports/:ifName/admin", deviceHandler.GetPortAdminStatus)

			// 流量查询路由
			switchesGroup.GET("/:deviceID/ports/:ifName/flow", trafficHandler.GetPortFlow)
			switchesGroup.GET("/:deviceID/ports/:ifName/flow/history", trafficHandler.GetPortFlowHistory)
		}

		// 流量图和摘要相关的路由
		trafficGroup := apiV1.Group("/traffic")
		{
			trafficGroup.GET("/chart/average", trafficHandler.GetAverageTrafficChart)
			trafficGroup.GET("/chart/maximum", trafficHandler.GetMaximumTrafficChart)
			trafficGroup.GET("/chart/minimum", trafficHandler.GetMinimumTrafficChart)
			trafficGroup.GET("/summary", trafficHandler.GetTrafficSummary)
		}

		// 注册告警模块路由
		alertHandler.RegisterRoutes(router)

		// 注册任务协同监控模块路由
		if taskCollaborationModule != nil && taskCollaborationModule.IsEnabled() {
			taskCollaborationModule.Handler.RegisterRoutes(apiV1)
		} else {
			// 如果模块未启用或初始化失败，记录日志但不影响其他功能
			logger.Warn("任务协同监控模块未启用或初始化失败，相关API接口不可用")
		}

		// 注册指标监测模块路由
		metricsHandler.RegisterRoutes(apiV1)

		// 注册拓扑模块路由
		topologyGroup := apiV1.Group("/topology")
		{
			topologyGroup.GET("", topologyHandler.GetTopology)
			topologyGroup.GET("/stats", topologyHandler.GetTopologyStats)
			topologyGroup.GET("/health", topologyHandler.GetTopologyHealth)
		}
	}

	// --- 启动 HTTP 服务器 ---
	serverPort := viper.GetInt("server.port")
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", serverPort),
		Handler: router,
	}

	// 在 goroutine 中启动服务器
	go func() {
		logger.Info("HTTP 服务器启动成功", zap.Int("port", serverPort))
		if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			logger.Fatal("启动服务器失败", zap.Error(err))
		}
	}()

	// 等待中断信号来优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info("正在关闭服务器...")

	// 优雅停止任务协同监控模块
	if taskCollaborationModule != nil && taskCollaborationModule.IsEnabled() {
		if err := taskCollaborationModule.Stop(); err != nil {
			logger.Error("停止任务协同监控模块失败", zap.Error(err))
		} else {
			logger.Info("任务协同监控模块已停止")
		}
	}

	// 设置关闭超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(ctx); err != nil {
		logger.Fatal("服务器强制关闭", zap.Error(err))
	}

	logger.Info("服务器已安全关闭")
}

// initDatabase 根据配置初始化数据库连接
func initDatabase() (*sql.DB, error) {
	cfg := mysql.Config{
		User:                 viper.GetString("database.username"),
		Passwd:               viper.GetString("database.password"),
		Net:                  "tcp",
		Addr:                 fmt.Sprintf("%s:%d", viper.GetString("database.host"), viper.GetInt("database.port")),
		DBName:               viper.GetString("database.dbname"),
		AllowNativePasswords: true,
		ParseTime:            true,
		Loc:                  timeutil.GetLocalTimeZone(), // 使用应用配置的本地时区
		Collation:            "utf8mb4_general_ci",
		Timeout:              30 * time.Second,
	}

	db, err := sql.Open("mysql", cfg.FormatDSN())
	if err != nil {
		return nil, err
	}
	db.SetConnMaxLifetime(time.Minute * 3)
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(10)
	if err := db.Ping(); err != nil {
		return nil, err
	}

	// 记录数据库连接信息
	logger.Info("数据库连接成功",
		zap.String("host", viper.GetString("database.host")),
		zap.Int("port", viper.GetInt("database.port")),
		zap.String("database", viper.GetString("database.dbname")),
		zap.String("timezone", timeutil.GetLocalTimeZone().String()))

	return db, nil
}

// initPrometheusClient 根据配置初始化 Prometheus 客户端
func initPrometheusClient() (api.Client, error) {
	// 从配置文件中获取Prometheus地址和认证信息
	address := viper.GetString("prometheus.address")
	username := viper.GetString("prometheus.username")
	password := viper.GetString("prometheus.password")

	// 创建包含基本信息的配置
	config := api.Config{
		Address: address,
	}

	// 如果提供了用户名和密码，添加Basic Auth认证
	if username != "" && password != "" {
		logger.Info("Prometheus客户端配置了认证信息",
			zap.String("address", address),
			zap.String("username", username))

		// 创建自定义的HTTP传输层，添加Basic Auth头
		rt := &promAuthRoundTripper{
			username: username,
			password: password,
			rt:       http.DefaultTransport,
		}
		config.RoundTripper = rt
	} else {
		logger.Warn("Prometheus客户端未配置认证信息，这可能会导致认证失败")
	}

	client, err := api.NewClient(config)
	if err != nil {
		return nil, fmt.Errorf("创建 prometheus client 失败: %w", err)
	}
	return client, nil
}

// promAuthRoundTripper 是一个自定义的HTTP传输层，用于添加Basic Auth认证头
type promAuthRoundTripper struct {
	username, password string
	rt                 http.RoundTripper
}

// RoundTrip 实现http.RoundTripper接口，为请求添加Basic Auth头
func (p *promAuthRoundTripper) RoundTrip(req *http.Request) (*http.Response, error) {
	req.SetBasicAuth(p.username, p.password)
	return p.rt.RoundTrip(req)
}

// initTimeZone 初始化应用的时区设置为北京时间
func initTimeZone() error {
	// 设置为北京时间
	err := timeutil.SetLocalTimeZone("Asia/Shanghai")
	if err != nil {
		return fmt.Errorf("设置北京时区失败: %w", err)
	}

	// 记录当前使用的时区
	location := timeutil.GetLocalTimeZone()
	logger.Info("成功设置应用时区", zap.String("timezone", location.String()))

	return nil
}

// getTaskCollaborationConfig 获取任务协同监控模块配置
func getTaskCollaborationConfig() *task_collaboration.ModuleConfig {
	// 从配置文件或环境变量读取配置
	enabled := viper.GetBool("task_collaboration.enabled")
	if !enabled {
		// 如果未配置或配置为false，使用默认配置但禁用模块
		config := task_collaboration.GetDefaultConfig()
		config.Enabled = false
		return config
	}

	config := &task_collaboration.ModuleConfig{
		Enabled: enabled,
		DataCollector: &task_collaboration.DataCollectorConfig{
			CollectInterval:          viper.GetString("task_collaboration.data_collector.collect_interval"),
			MetricsEnabled:           viper.GetBool("task_collaboration.data_collector.metrics_enabled"),
			LogsEnabled:              viper.GetBool("task_collaboration.data_collector.logs_enabled"),
			AlertsEnabled:            viper.GetBool("task_collaboration.data_collector.alerts_enabled"),
			EventsEnabled:            viper.GetBool("task_collaboration.data_collector.events_enabled"),
			MaxConcurrentCollections: viper.GetInt("task_collaboration.data_collector.max_concurrent_collections"),
			PrometheusEndpoint:       viper.GetString("prometheus.address"), // 使用全局Prometheus配置
			ElasticsearchEndpoint:    viper.GetString("task_collaboration.data_collector.elasticsearch_endpoint"),
		},
	}

	// 设置默认值
	if config.DataCollector.CollectInterval == "" {
		config.DataCollector.CollectInterval = "30s"
	}
	if config.DataCollector.MaxConcurrentCollections == 0 {
		config.DataCollector.MaxConcurrentCollections = 10
	}
	if config.DataCollector.PrometheusEndpoint == "" {
		config.DataCollector.PrometheusEndpoint = "http://localhost:9090"
	}
	if config.DataCollector.ElasticsearchEndpoint == "" {
		config.DataCollector.ElasticsearchEndpoint = "http://localhost:9200"
	}

	// 验证配置
	if err := task_collaboration.ValidateConfig(config); err != nil {
		logger.Error("任务协同监控模块配置无效", zap.Error(err))
		// 返回禁用的配置
		config.Enabled = false
	}

	return config
}

// getMetricsMonitoringConfig 获取指标监测模块配置
func getMetricsMonitoringConfig() *metrics_monitoring.MetricsMonitoringConfig {
	// 使用默认配置
	config := &metrics_monitoring.MetricsMonitoringConfig{
		BaselineDuration: viper.GetString("metrics_monitoring.baseline_duration"),
		ExtendedDuration: viper.GetString("metrics_monitoring.extended_duration"),
		QueryInterval:    viper.GetString("metrics_monitoring.query_interval"),
		BatchSize:        viper.GetInt("metrics_monitoring.batch_size"),
		Timeout:          viper.GetString("metrics_monitoring.timeout"),
		SupportedMetrics: viper.GetStringSlice("metrics_monitoring.supported_metrics"),
	}

	// 设置默认值
	if config.BaselineDuration == "" {
		config.BaselineDuration = "30m"
	}
	if config.ExtendedDuration == "" {
		config.ExtendedDuration = "1h"
	}
	if config.QueryInterval == "" {
		config.QueryInterval = "1m"
	}
	if config.BatchSize == 0 {
		config.BatchSize = 100
	}
	if config.Timeout == "" {
		config.Timeout = "30s"
	}
	if len(config.SupportedMetrics) == 0 {
		config.SupportedMetrics = []string{"cpu", "memory", "traffic"}
	}

	return config
}

// metricsServiceAdapter 适配器，将metrics_monitoring.Service包装为task_collaboration.MetricsService
type metricsServiceAdapter struct {
	service *metrics_monitoring.Service
}

func (m *metricsServiceAdapter) StartMetricsMonitoring(ctx context.Context, taskID, taskSessionID string, deviceIDs []string, metricTypes []string) error {
	_, err := m.service.StartMetricsMonitoring(ctx, taskID, taskSessionID, deviceIDs, metricTypes)
	return err
}

func (m *metricsServiceAdapter) StopMetricsMonitoring(ctx context.Context, taskID string) error {
	_, err := m.service.StopMetricsMonitoring(ctx, taskID)
	return err
}
