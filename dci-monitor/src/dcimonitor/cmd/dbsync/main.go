package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"dcimonitor/internal/dbsync"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

func main() {
	// 解析命令行参数
	configFile := flag.String("config", "config/dbsync.yaml", "配置文件路径")
	testMode := flag.Bool("test", false, "测试模式，仅验证配置文件和代码逻辑")
	disableKafka := flag.Bool("disable-kafka", false, "禁用Kafka功能")
	flag.Parse()

	// 初始化日志记录器
	logger, err := initLogger()
	if err != nil {
		panic(fmt.Sprintf("初始化日志记录器失败: %v", err))
	}
	defer logger.Sync()

	// 如果是测试模式，仅验证配置文件
	if *testMode {
		logger.Info("测试模式：验证配置文件...", zap.String("configFile", *configFile))
		config, err := dbsync.LoadConfigWithTestMode(*configFile)
		if err != nil {
			logger.Fatal("配置文件验证失败", zap.Error(err))
		}
		logger.Info("配置文件验证成功",
			zap.String("source_db", config.SourceDB.Host),
			zap.Int("source_port", config.SourceDB.Port),
			zap.String("source_user", config.SourceDB.Username),
			zap.String("source_dbname", config.SourceDB.DBName),
			zap.String("target_db", config.TargetDB.Host),
			zap.Int("target_port", config.TargetDB.Port),
			zap.String("target_dbname", config.TargetDB.DBName),
			zap.Int("tables", len(config.Tables)))
		logger.Info("测试模式完成，程序退出")
		return
	}

	// 加载配置
	logger.Info("正在加载配置...", zap.String("config_file", *configFile))
	config, err := dbsync.LoadConfig(*configFile)
	if err != nil {
		logger.Fatal("加载配置失败", zap.Error(err))
	}

	// 打印特殊映射配置信息
	logger.Info("特殊映射配置数量", zap.Int("count", len(config.SpecialMappings)))
	for i, mapping := range config.SpecialMappings {
		logger.Info("特殊映射配置",
			zap.Int("index", i),
			zap.String("source_table", mapping.SourceTable),
			zap.String("target_table", mapping.TargetTable),
			zap.Int("columns", len(mapping.MappingColumns)))
	}

	// 如果指定了禁用Kafka，则禁用Kafka功能
	if *disableKafka && config.Kafka != nil {
		config.Kafka.Enabled = false
		logger.Info("已禁用Kafka功能")
	}

	// 创建数据库同步服务
	syncService, err := dbsync.NewDBSyncService(config, logger)
	if err != nil {
		logger.Fatal("创建数据库同步服务失败", zap.Error(err))
	}
	defer syncService.Close()

	// 创建上下文和取消函数
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动接口索引更新器(如果启用了Kafka)
	var updater *dbsync.InterfaceIndexUpdater
	if config.Kafka != nil && config.Kafka.Enabled {
		logger.Info("启动接口索引更新器...")
		updater, err = dbsync.NewInterfaceIndexUpdater(config, syncService, logger)
		if err != nil {
			logger.Fatal("创建接口索引更新器失败", zap.Error(err))
		}
		if err := updater.Start(ctx); err != nil {
			logger.Fatal("启动接口索引更新器失败", zap.Error(err))
		}
	} else {
		logger.Info("Kafka功能已禁用，跳过接口索引更新器")
	}

	// 启动同步调度器
	logger.Info("启动同步调度器...")
	scheduler := dbsync.NewSyncScheduler(config, syncService, logger)
	if err := scheduler.Start(ctx); err != nil {
		logger.Fatal("启动同步调度器失败", zap.Error(err))
	}

	// 启动API服务
	logger.Info("启动API服务...")
	// 创建数据映射器
	dataMapper := dbsync.NewDataMapper(syncService.GetSourceDB(), syncService.GetTargetDB(), logger)
	apiServer := dbsync.NewAPIServer(config, syncService, scheduler, dataMapper, logger)
	if err := apiServer.Start(); err != nil {
		logger.Fatal("启动API服务失败", zap.Error(err))
	}

	logger.Info("数据库同步服务已启动")

	// 监听退出信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan

	logger.Info("接收到退出信号，正在关闭服务...")

	// 停止API服务
	if err := apiServer.Stop(); err != nil {
		logger.Error("停止API服务失败", zap.Error(err))
	}

	// 停止同步调度器
	scheduler.Stop()

	// 停止接口索引更新器
	if updater != nil {
		if err := updater.Stop(); err != nil {
			logger.Error("停止接口索引更新器失败", zap.Error(err))
		}
	}

	logger.Info("服务已停止")
}

// initLogger 初始化日志记录器
func initLogger() (*zap.Logger, error) {
	// 日志编码器配置
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	// 日志配置
	config := zap.Config{
		Level:             zap.NewAtomicLevelAt(zap.InfoLevel),
		Development:       false,
		DisableCaller:     false,
		DisableStacktrace: false,
		Sampling:          nil,
		Encoding:          "json",
		EncoderConfig:     encoderConfig,
		OutputPaths:       []string{"stdout", "logs/dbsync.log"},
		ErrorOutputPaths:  []string{"stderr", "logs/dbsync-error.log"},
	}

	// 创建日志记录器
	return config.Build()
}
