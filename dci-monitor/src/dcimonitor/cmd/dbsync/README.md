# 数据库同步模块

此模块实现了 DCI 设备端口及 VNI 基础信息的数据库同步功能，主要职责是从 `dci` 数据库同步设备和端口信息到 `dci_monitor` 数据库，并维护接口索引与设备端口的映射关系。

## 功能特性

1. **数据库同步**：
   - 全量同步：一次性同步所有配置的表
   - 增量同步：只同步上次同步后有更新的数据
   - 定时自动同步：按配置的时间间隔自动执行增量同步
   - 手动触发同步：通过 API 接口手动触发同步

2. **接口索引映射**：
   - 从 Kafka 接收 Telegraf SNMP 消息，更新接口索引与设备端口的映射
   - 自动维护设备 IP、端口名称与接口索引之间的关系

3. **映射查询服务**：
   - 提供 HTTP API 接口，用于查询设备端口和 VNI 的映射关系
   - 支持按设备 IP、接口索引查询端口映射信息

4. **同步监控**：
   - 记录同步状态、时间和执行结果
   - 提供 API 接口查询同步状态

## 配置说明

配置文件位于 `config/dbsync.yaml`，主要包含以下配置：

1. **数据库配置**：
   - 源数据库（dci）和目标数据库（dci_monitor）的连接信息

2. **同步配置**：
   - 同步间隔、启动时是否同步、最大重试次数
   - 需要同步的表及其主键
   - 特殊映射关系配置

3. **Kafka 配置**：
   - Kafka 集群连接信息
   - 消费主题和消费者组
   - 安全配置（TLS、SASL）

4. **API 配置**：
   - 是否启用 API 服务
   - API 端口和支持的端点

5. **日志配置**：
   - 日志级别、格式、存储目录等

## API 接口

服务提供以下 API 接口：

1. `GET /api/sync/status` - 获取同步状态
2. `POST /api/sync/trigger?full=<true|false>` - 触发同步
3. `GET /api/mapping/port?device_ip=<ip>&if_index=<index>` - 查询端口映射
4. `GET /api/mapping/interfaces?device_ip=<ip>` - 查询设备所有接口

## 使用方法

### 启动服务

```bash
# 使用默认配置文件路径
./dbsync

# 指定配置文件路径
./dbsync -config=/path/to/config.yaml
```

### 触发同步

```bash
# 触发全量同步
curl -X POST "http://localhost:8090/api/sync/trigger?full=true"

# 触发增量同步
curl -X POST "http://localhost:8090/api/sync/trigger?full=false"
```

### 查询同步状态

```bash
curl "http://localhost:8090/api/sync/status"
```

### 查询端口映射

```bash
curl "http://localhost:8090/api/mapping/port?device_ip=********&if_index=10101"
```

### 查询设备接口

```bash
curl "http://localhost:8090/api/mapping/interfaces?device_ip=********"
```

## 日志文件

服务产生的日志文件存储在 `logs` 目录下，主要包含：

- `dbsync.log` - 主日志文件

## 依赖组件

- MySQL 数据库
- Kafka 消息队列
- Go 1.18+

## 构建方法

```bash
cd dci-monitor/src/dcimonitor/cmd/dbsync
go build -o dbsync
``` 