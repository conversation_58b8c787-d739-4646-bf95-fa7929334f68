package cmd

import (
	"context"
	"fmt"
	"io"
	"net"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"common/logger"
	"dcimonitor/internal/utils"

	"github.com/IBM/sarama"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

var (
	kafkaBootstrapServers   string
	topic                   string
	isProducer              bool
	isConsumer              bool
	isMetadata              bool
	verbose                 bool
	messageContent          string
	isDnsCheck              bool
	isNetCheck              bool
	metadataRefreshInterval int
	isMetadataMonitor       bool // 持续监控元数据变化
	isValidateMetadata      bool // 验证多broker元数据一致性
	monitorInterval         int  // 元数据监控间隔（秒）
)

// kafkaClientCmd 表示 kafka-client 命令
var kafkaClientCmd = &cobra.Command{
	Use:   "kafka-client",
	Short: "测试Kafka连接并查看集群元数据",
	Long: `用于测试Kafka连接和诊断advertised.listeners问题的客户端工具。
可以作为生产者发送消息、作为消费者接收消息，或查询Kafka集群的元数据信息。
示例:
  dci-monitor kafka-client --bootstrap=10.247.33.12:30002 --metadata --verbose
  dci-monitor kafka-client --bootstrap=10.247.33.12:30002 --producer --topic=test --message="测试消息"
  dci-monitor kafka-client --bootstrap=10.247.33.12:30002 --consumer --topic=test
  dci-monitor kafka-client --bootstrap=10.247.33.12:30002 --dnscheck --netcheck
  dci-monitor kafka-client --bootstrap=10.247.33.12:30002 --consumer --topic=test --refresh=5
  dci-monitor kafka-client --bootstrap=10.247.33.12:30002 --metadatamonitor --interval=10
  dci-monitor kafka-client --bootstrap=10.247.33.12:30002 --validatemetadata`,
	Run: func(cmd *cobra.Command, args []string) {
		// 初始化默认日志记录器
		logger.InitDefaultLogger()

		// 执行DNS检查（如果请求）
		if isDnsCheck {
			checkDnsResolution(kafkaBootstrapServers)
		}

		// 执行网络连通性检查（如果请求）
		if isNetCheck {
			checkNetworkConnectivity(kafkaBootstrapServers)
		}

		// 如果只请求网络检查，则不创建Kafka客户端
		if isDnsCheck && isNetCheck && !isMetadata && !isProducer && !isConsumer && !isMetadataMonitor && !isValidateMetadata {
			return
		}

		// 创建Kafka配置
		config := &utils.KafkaClientConfig{
			BootstrapServers:        kafkaBootstrapServers,
			ClientID:                "dci-kafka-client",
			Debug:                   verbose,
			ConnectTimeout:          30 * time.Second,
			RefreshMetadataInterval: time.Duration(metadataRefreshInterval) * time.Second,
		}

		// 创建Kafka客户端
		client, err := utils.CreateKafkaClient(config)
		if err != nil {
			logger.Error("创建Kafka客户端失败", zap.Error(err))
			return
		}
		defer func() {
			if err := client.Close(); err != nil {
				logger.Error("关闭Kafka客户端失败", zap.Error(err))
			}
		}()

		// 处理元数据请求
		if isMetadata {
			fetchMetadata(client)
		}

		// 作为生产者
		if isProducer {
			produceMessage(client, topic, messageContent)
		}

		// 作为消费者
		if isConsumer {
			consumeMessages(client, topic)
		}

		// 监控元数据变化
		if isMetadataMonitor {
			metadataMonitor(client, time.Duration(monitorInterval)*time.Second)
		}

		// 验证多broker元数据一致性
		if isValidateMetadata {
			validateMetadata(client)
		}
	},
}

// 在init()函数中添加命令和标志
func init() {
	rootCmd.AddCommand(kafkaClientCmd)

	// 添加命令行标志
	kafkaClientCmd.Flags().StringVar(&kafkaBootstrapServers, "bootstrap", "", "Kafka引导服务器地址，格式为host1:port1,host2:port2")
	kafkaClientCmd.Flags().StringVar(&topic, "topic", "test", "要使用的Kafka主题")
	kafkaClientCmd.Flags().BoolVar(&isProducer, "producer", false, "以生产者模式运行")
	kafkaClientCmd.Flags().BoolVar(&isConsumer, "consumer", false, "以消费者模式运行")
	kafkaClientCmd.Flags().BoolVar(&isMetadata, "metadata", false, "查询Kafka集群元数据")
	kafkaClientCmd.Flags().BoolVar(&verbose, "verbose", false, "启用详细日志")
	kafkaClientCmd.Flags().StringVar(&messageContent, "message", "测试消息", "生产者模式下要发送的消息内容")
	kafkaClientCmd.Flags().BoolVar(&isDnsCheck, "dnscheck", false, "执行DNS解析检查")
	kafkaClientCmd.Flags().BoolVar(&isNetCheck, "netcheck", false, "执行网络连通性检查")
	kafkaClientCmd.Flags().IntVar(&metadataRefreshInterval, "refresh", 30, "元数据刷新间隔（秒），值越小越频繁刷新元数据")
	kafkaClientCmd.Flags().BoolVar(&isMetadataMonitor, "metadatamonitor", false, "持续监控元数据变化")
	kafkaClientCmd.Flags().BoolVar(&isValidateMetadata, "validatemetadata", false, "验证多broker元数据一致性")
	kafkaClientCmd.Flags().IntVar(&monitorInterval, "interval", 30, "元数据监控间隔（秒）")

	// 标记必需标志
	kafkaClientCmd.MarkFlagRequired("bootstrap")
}

// fetchMetadata 获取并打印Kafka集群元数据
func fetchMetadata(client sarama.Client) {
	// 使用工具类获取元数据
	metadata, err := utils.GetKafkaMetadata(client)
	if err != nil {
		logger.Error("获取Kafka元数据失败", zap.Error(err))
		return
	}

	// 获取所有主题
	topics, err := client.Topics()
	if err != nil {
		logger.Error("获取主题列表失败", zap.Error(err))
		return
	}

	logger.Info("获取集群元数据成功", zap.Int("主题数量", len(topics)))

	// 打印基本元数据信息
	if brokerInfos, ok := metadata["brokers"].([]map[string]interface{}); ok {
		logger.Info("元数据中的Broker数量", zap.Int("数量", len(brokerInfos)))
	}

	// 打印Broker信息
	brokers := client.Brokers()
	logger.Info("集群Broker信息:", zap.Int("Broker数量", len(brokers)))
	for i, broker := range brokers {
		connected, err := broker.Connected()
		if err != nil {
			logger.Error("检查Broker连接失败", zap.Error(err))
		}

		logger.Info(fmt.Sprintf("Broker #%d: ", i),
			zap.String("地址", broker.Addr()),
			zap.Int32("ID", broker.ID()),
			zap.Bool("已连接", connected),
		)
	}

	// 打印主题和分区信息
	logger.Info("集群主题信息:")
	for _, topic := range topics {
		partitions, err := client.Partitions(topic)
		if err != nil {
			logger.Error("获取主题分区失败", zap.String("主题", topic), zap.Error(err))
			continue
		}

		// 将int32切片转换为[]int以适配zap.Ints
		intPartitions := make([]int, len(partitions))
		for i, p := range partitions {
			intPartitions[i] = int(p)
		}
		logger.Info(fmt.Sprintf("主题: %s", topic), zap.Ints("分区", intPartitions))

		// 获取每个分区的详细信息
		for _, partition := range partitions {
			leader, err := client.Leader(topic, partition)
			if err != nil {
				logger.Error("获取分区Leader失败",
					zap.String("主题", topic),
					zap.Int32("分区", partition),
					zap.Error(err))
				continue
			}

			replicas, err := client.Replicas(topic, partition)
			if err != nil {
				logger.Error("获取分区副本失败",
					zap.String("主题", topic),
					zap.Int32("分区", partition),
					zap.Error(err))
				continue
			}

			isr, err := client.InSyncReplicas(topic, partition)
			if err != nil {
				logger.Error("获取同步副本失败",
					zap.String("主题", topic),
					zap.Int32("分区", partition),
					zap.Error(err))
				continue
			}

			// 转换int32切片为int切片
			intReplicas := make([]int, len(replicas))
			for i, r := range replicas {
				intReplicas[i] = int(r)
			}

			intISR := make([]int, len(isr))
			for i, r := range isr {
				intISR[i] = int(r)
			}

			logger.Info(fmt.Sprintf("  分区 %d:", partition),
				zap.String("Leader地址", leader.Addr()),
				zap.Int32("LeaderID", leader.ID()),
				zap.Ints("副本", intReplicas),
				zap.Ints("同步副本", intISR),
			)

			// 检查advertised.listeners配置问题 - 关键检测点
			if leader != nil {
				logger.Info("检查advertised.listeners配置",
					zap.String("主题", topic),
					zap.Int32("分区", partition),
					zap.String("Leader地址", leader.Addr()),
				)

				// 解析Leader地址
				addrParts := strings.Split(leader.Addr(), ":")
				if len(addrParts) == 2 {
					host := addrParts[0]

					// 检查是否为内部Kubernetes DNS名称
					if strings.Contains(host, "kafka-") && strings.Contains(host, ".kafka-headless.") {
						logger.Info("⚠️ Leader使用了Kubernetes内部DNS名称，这可能会导致外部客户端无法连接",
							zap.String("内部DNS名称", host),
							zap.String("建议", "检查advertised.listeners配置，确保使用了客户端可访问的IP地址"),
						)
					}

					// 检查IP地址是否为私有地址但不是客户端可路由的地址
					if (strings.HasPrefix(host, "10.") || strings.HasPrefix(host, "172.") || strings.HasPrefix(host, "192.168.")) &&
						!strings.HasPrefix(host, "10.247.33.") {
						logger.Info("⚠️ Leader使用了可能无法从外部访问的私有IP地址",
							zap.String("私有IP", host),
							zap.String("建议", "确保advertised.listeners中使用的是客户端可访问的IP地址"),
						)
					}
				}
			}
		}
	}
}

// produceMessage 作为生产者发送消息
func produceMessage(client sarama.Client, topic, message string) {
	producer, err := sarama.NewSyncProducerFromClient(client)
	if err != nil {
		logger.Error("创建生产者失败", zap.Error(err))
		return
	}
	defer producer.Close()

	logger.Info("创建生产者成功，准备发送消息")

	// 准备消息
	msg := &sarama.ProducerMessage{
		Topic: topic,
		Value: sarama.StringEncoder(message),
	}

	// 发送消息
	partition, offset, err := producer.SendMessage(msg)
	if err != nil {
		logger.Error("发送消息失败", zap.Error(err))
		return
	}

	logger.Info("消息发送成功",
		zap.String("主题", topic),
		zap.Int32("分区", partition),
		zap.Int64("偏移量", offset),
		zap.String("消息内容", message),
	)
}

// consumeMessages 作为消费者接收消息
func consumeMessages(client sarama.Client, topic string) {
	// 创建一个消费者
	consumer, err := sarama.NewConsumerFromClient(client)
	if err != nil {
		logger.Error("创建消费者失败", zap.Error(err))
		return
	}
	defer consumer.Close()

	// 获取主题的所有分区，添加重试逻辑
	var partitions []int32
	var getPartitionsErr error

	// 元数据重试配置
	maxRetries := 5
	retryBackoff := 500 * time.Millisecond

	// 使用退避策略重试获取分区
	for retry := 0; retry < maxRetries; retry++ {
		if retry > 0 {
			// 计算退避时间
			backoff := time.Duration(retry) * retryBackoff
			logger.Info("重试获取分区",
				zap.String("主题", topic),
				zap.Int("重试次数", retry),
				zap.Duration("等待时间", backoff))
			time.Sleep(backoff)

			// 尝试主动刷新元数据
			logger.Info("尝试刷新元数据", zap.String("主题", topic))
			if err := client.RefreshMetadata(topic); err != nil {
				logger.Error("刷新元数据失败",
					zap.String("主题", topic),
					zap.Error(err))
			}
		}

		partitions, getPartitionsErr = consumer.Partitions(topic)
		if getPartitionsErr == nil {
			break
		}

		logger.Error("获取主题分区失败，准备重试",
			zap.String("主题", topic),
			zap.Error(getPartitionsErr),
			zap.Int("当前重试次数", retry+1),
			zap.Int("最大重试次数", maxRetries))

		// 检查错误类型，对特定错误采取不同策略
		if strings.Contains(getPartitionsErr.Error(), "metadata") {
			logger.Error("元数据错误，将主动刷新元数据",
				zap.String("主题", topic),
				zap.Error(getPartitionsErr))

			// 对于元数据相关错误，尝试刷新特定主题的元数据
			if err := client.RefreshMetadata(topic); err != nil {
				logger.Error("刷新主题元数据失败",
					zap.String("主题", topic),
					zap.Error(err))
			}
		}
	}

	// 如果重试后仍然失败，则终止
	if getPartitionsErr != nil {
		logger.Error("获取主题分区失败，已达最大重试次数，终止消费",
			zap.String("主题", topic),
			zap.Error(getPartitionsErr),
			zap.Int("最大重试次数", maxRetries))
		return
	}

	// 处理信号以优雅退出
	ctx, cancel := context.WithCancel(context.Background())
	signals := make(chan os.Signal, 1)
	signal.Notify(signals, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-signals
		logger.Info("接收到退出信号，正在优雅关闭")
		cancel()
	}()

	// 转换int32切片为int切片以适配zap.Ints
	intPartitions := make([]int, len(partitions))
	for i, p := range partitions {
		intPartitions[i] = int(p)
	}
	logger.Info("开始消费消息", zap.String("主题", topic), zap.Ints("分区", intPartitions))
	logger.Info("按 Ctrl+C 停止消费")

	// 为每个分区创建一个分区消费者
	for _, partition := range partitions {
		// 添加创建分区消费者的重试逻辑
		var pc sarama.PartitionConsumer
		var pcErr error

		for retry := 0; retry < maxRetries; retry++ {
			if retry > 0 {
				backoff := time.Duration(retry) * retryBackoff
				logger.Info("重试创建分区消费者",
					zap.String("主题", topic),
					zap.Int32("分区", partition),
					zap.Int("重试次数", retry),
					zap.Duration("等待时间", backoff))
				time.Sleep(backoff)

				// 尝试刷新元数据
				if err := client.RefreshMetadata(topic); err != nil {
					logger.Error("刷新元数据失败",
						zap.String("主题", topic),
						zap.Int32("分区", partition),
						zap.Error(err))
				}
			}

			pc, pcErr = consumer.ConsumePartition(topic, partition, sarama.OffsetNewest)
			if pcErr == nil {
				break
			}

			logger.Error("创建分区消费者失败，准备重试",
				zap.String("主题", topic),
				zap.Int32("分区", partition),
				zap.Error(pcErr),
				zap.Int("当前重试次数", retry+1),
				zap.Int("最大重试次数", maxRetries))

			// 检查错误类型
			if strings.Contains(pcErr.Error(), "metadata") {
				logger.Error("元数据错误，将主动刷新元数据",
					zap.String("主题", topic),
					zap.Int32("分区", partition),
					zap.Error(pcErr))

				// 对于元数据相关错误，尝试刷新特定主题的元数据
				if err := client.RefreshMetadata(topic); err != nil {
					logger.Error("刷新主题元数据失败",
						zap.String("主题", topic),
						zap.Error(err))
				}
			}
		}

		// 如果重试后仍然失败，则跳过此分区
		if pcErr != nil {
			logger.Error("创建分区消费者失败，已达最大重试次数，跳过此分区",
				zap.String("主题", topic),
				zap.Int32("分区", partition),
				zap.Error(pcErr),
				zap.Int("最大重试次数", maxRetries))
			continue
		}

		go func(pc sarama.PartitionConsumer) {
			defer pc.Close()
			for {
				select {
				case msg := <-pc.Messages():
					logger.Info("收到消息",
						zap.String("主题", msg.Topic),
						zap.Int32("分区", msg.Partition),
						zap.Int64("偏移量", msg.Offset),
						zap.Time("时间戳", msg.Timestamp),
						zap.String("键", string(msg.Key)),
						zap.String("内容", string(msg.Value)),
					)
				case err := <-pc.Errors():
					logger.Error("消费消息错误", zap.Error(err))

					// 检查是否为元数据错误
					if strings.Contains(err.Error(), "metadata") ||
						strings.Contains(err.Error(), "leader") ||
						strings.Contains(err.Error(), "connection") {
						logger.Error("检测到可能的元数据错误，尝试刷新元数据",
							zap.Error(err))

						// 尝试刷新元数据
						if refreshErr := client.RefreshMetadata(topic); refreshErr != nil {
							logger.Error("刷新元数据失败",
								zap.String("主题", topic),
								zap.Error(refreshErr))
						} else {
							logger.Info("元数据刷新成功",
								zap.String("主题", topic))
						}
					}
				case <-ctx.Done():
					return
				}
			}
		}(pc)
	}

	// 创建定期刷新元数据的goroutine
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				logger.Info("定期刷新元数据", zap.String("主题", topic))
				if err := client.RefreshMetadata(topic); err != nil {
					logger.Error("定期刷新元数据失败",
						zap.String("主题", topic),
						zap.Error(err))
				}
			case <-ctx.Done():
				return
			}
		}
	}()

	// 等待取消信号
	<-ctx.Done()
	logger.Info("消费者已停止")
}

// checkDnsResolution 检查DNS解析
func checkDnsResolution(bootstrapServers string) {
	logger.Info("开始DNS解析检查")

	// 分割服务器列表
	servers := strings.Split(bootstrapServers, ",")

	for _, server := range servers {
		// 分离主机名和端口
		parts := strings.Split(server, ":")
		if len(parts) != 2 {
			logger.Error("服务器地址格式无效", zap.String("地址", server))
			continue
		}

		host := parts[0]
		port := parts[1]

		// 检查是否是IP地址
		if net.ParseIP(host) != nil {
			logger.Info("引导服务器使用IP地址，跳过DNS解析检查", zap.String("IP", host))
			continue
		}

		// 尝试DNS解析
		logger.Info("尝试解析主机名", zap.String("主机名", host))
		ips, err := net.LookupIP(host)
		if err != nil {
			logger.Error("DNS解析失败", zap.String("主机名", host), zap.Error(err))
		} else {
			for _, ip := range ips {
				logger.Info("DNS解析成功", zap.String("主机名", host), zap.String("IP", ip.String()))
			}
		}

		// 尝试解析服务
		_, err = net.LookupPort("tcp", port)
		if err != nil {
			logger.Error("端口解析失败", zap.String("端口", port), zap.Error(err))
		} else {
			logger.Info("端口解析成功", zap.String("端口", port))
		}

		// 完整服务地址查找
		_, addrs, err := net.LookupSRV("kafka", "tcp", host)
		if err != nil {
			logger.Info("SRV记录查找失败（这是预期的，除非配置了SRV记录）", zap.String("主机名", host), zap.Error(err))
		} else {
			for _, addr := range addrs {
				logger.Info("找到SRV记录",
					zap.String("目标", addr.Target),
					zap.Uint16("端口", addr.Port),
					zap.Uint16("优先级", addr.Priority),
					zap.Uint16("权重", addr.Weight))
			}
		}
	}

	logger.Info("DNS解析检查完成")
}

// checkNetworkConnectivity 检查网络连通性
func checkNetworkConnectivity(bootstrapServers string) {
	logger.Info("开始网络连通性检查")

	// 分割服务器列表
	servers := strings.Split(bootstrapServers, ",")

	for _, server := range servers {
		logger.Info("检查到服务器的网络连通性", zap.String("服务器", server))

		// 尝试TCP连接
		startTime := time.Now()
		conn, err := net.DialTimeout("tcp", server, 5*time.Second)
		duration := time.Since(startTime)

		if err != nil {
			logger.Error("TCP连接失败", zap.String("服务器", server), zap.Duration("耗时", duration), zap.Error(err))
		} else {
			logger.Info("TCP连接成功", zap.String("服务器", server), zap.Duration("耗时", duration))
			// 尝试延迟测试
			for i := 0; i < 3; i++ {
				start := time.Now()
				_, err := conn.Write([]byte("ping"))
				if err != nil {
					logger.Error("网络写入失败", zap.String("服务器", server), zap.Error(err))
					break
				}

				// 设置读取超时
				conn.SetReadDeadline(time.Now().Add(2 * time.Second))

				buffer := make([]byte, 1024)
				_, err = conn.Read(buffer)
				pingDuration := time.Since(start)

				if err != nil {
					if err == io.EOF {
						logger.Info("连接关闭", zap.String("服务器", server))
					} else {
						logger.Error("网络读取失败(这可能是预期行为，因为Kafka不响应原始TCP ping)",
							zap.String("服务器", server),
							zap.Duration("耗时", pingDuration),
							zap.Error(err))
					}
					break
				}

				logger.Info("网络往返测试", zap.String("服务器", server), zap.Duration("耗时", pingDuration))
				time.Sleep(500 * time.Millisecond)
			}

			conn.Close()
		}
	}

	logger.Info("网络连通性检查完成")
}

// metadataMonitor 持续监控元数据变化
func metadataMonitor(client sarama.Client, interval time.Duration) {
	logger.Info("开始持续监控Kafka元数据",
		zap.Duration("监控间隔", interval))

	// 初始化记录上一次的元数据状态
	var lastBrokers []map[string]interface{}
	var lastTopicsPartitions = make(map[string]map[int32]int32) // 主题 -> {分区 -> leaderID}

	// 记录初始元数据
	metadata, err := utils.GetKafkaMetadata(client)
	if err != nil {
		logger.Error("获取初始元数据失败", zap.Error(err))
		return
	}

	// 初始化元数据记录
	if brokerInfos, ok := metadata["brokers"].([]map[string]interface{}); ok {
		lastBrokers = brokerInfos
	}

	// 初始化主题分区映射
	topics, err := client.Topics()
	if err != nil {
		logger.Error("获取主题列表失败", zap.Error(err))
		return
	}

	for _, topic := range topics {
		partitions, err := client.Partitions(topic)
		if err != nil {
			logger.Error("获取主题分区失败", zap.String("主题", topic), zap.Error(err))
			continue
		}

		partitionLeaders := make(map[int32]int32)
		for _, partition := range partitions {
			leader, err := client.Leader(topic, partition)
			if err != nil {
				logger.Error("获取分区Leader失败",
					zap.String("主题", topic),
					zap.Int32("分区", partition),
					zap.Error(err))
				continue
			}
			partitionLeaders[partition] = leader.ID()
		}
		lastTopicsPartitions[topic] = partitionLeaders
	}

	logger.Info("初始化元数据状态完成，开始监控变化")

	// 处理信号以优雅退出
	ctx, cancel := context.WithCancel(context.Background())
	signals := make(chan os.Signal, 1)
	signal.Notify(signals, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-signals
		logger.Info("接收到退出信号，正在优雅关闭")
		cancel()
	}()

	// 定期刷新元数据并比较变化
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			err := client.RefreshMetadata()
			if err != nil {
				logger.Error("刷新元数据失败", zap.Error(err))
				continue
			}

			newMetadata, err := utils.GetKafkaMetadata(client)
			if err != nil {
				logger.Error("获取更新后的元数据失败", zap.Error(err))
				continue
			}

			// 比较broker变化
			var newBrokers []map[string]interface{}
			if brokerInfos, ok := newMetadata["brokers"].([]map[string]interface{}); ok {
				newBrokers = brokerInfos
			}

			brokerChanges := compareMetadataBrokers(lastBrokers, newBrokers)
			if len(brokerChanges) > 0 {
				logger.Warn("检测到Broker变化", zap.Any("变化详情", brokerChanges))
			}
			lastBrokers = newBrokers

			// 比较主题分区的leader变化
			newTopicsPartitions := make(map[string]map[int32]int32)
			newTopics, err := client.Topics()
			if err != nil {
				logger.Error("获取主题列表失败", zap.Error(err))
				continue
			}

			var hasLeaderChanges bool
			var leaderChangesDetails []map[string]interface{}

			for _, topic := range newTopics {
				partitions, err := client.Partitions(topic)
				if err != nil {
					logger.Error("获取主题分区失败", zap.String("主题", topic), zap.Error(err))
					continue
				}

				partitionLeaders := make(map[int32]int32)
				for _, partition := range partitions {
					leader, err := client.Leader(topic, partition)
					if err != nil {
						logger.Error("获取分区Leader失败",
							zap.String("主题", topic),
							zap.Int32("分区", partition),
							zap.Error(err))
						continue
					}

					partitionLeaders[partition] = leader.ID()

					// 检查leader是否变化
					if oldLeaderID, exists := lastTopicsPartitions[topic]; exists {
						if oldPartitionLeaderID, exists := oldLeaderID[partition]; exists {
							if oldPartitionLeaderID != leader.ID() {
								hasLeaderChanges = true
								leaderChangesDetails = append(leaderChangesDetails, map[string]interface{}{
									"topic":     topic,
									"partition": partition,
									"oldLeader": oldPartitionLeaderID,
									"newLeader": leader.ID(),
								})
							}
						}
					}
				}
				newTopicsPartitions[topic] = partitionLeaders
			}

			if hasLeaderChanges {
				logger.Warn("检测到分区Leader变化", zap.Any("变化详情", leaderChangesDetails))
			}

			// 检查是否有新增的主题
			for _, topic := range newTopics {
				if _, exists := lastTopicsPartitions[topic]; !exists {
					logger.Info("检测到新主题", zap.String("主题", topic))
				}
			}

			// 检查是否有删除的主题
			for topic := range lastTopicsPartitions {
				found := false
				for _, newTopic := range newTopics {
					if topic == newTopic {
						found = true
						break
					}
				}
				if !found {
					logger.Warn("检测到主题被删除", zap.String("主题", topic))
				}
			}

			lastTopicsPartitions = newTopicsPartitions

		case <-ctx.Done():
			logger.Info("元数据监控已停止")
			return
		}
	}
}

// compareMetadataBrokers 比较两次获取的broker元数据，返回变化详情
func compareMetadataBrokers(oldBrokers, newBrokers []map[string]interface{}) []map[string]interface{} {
	var changes []map[string]interface{}

	// 使用map记录broker信息，方便查找
	oldBrokerMap := make(map[int32]map[string]interface{})
	for _, broker := range oldBrokers {
		if id, ok := broker["id"].(int32); ok {
			oldBrokerMap[id] = broker
		}
	}

	// 查找新增和变化的broker
	for _, newBroker := range newBrokers {
		newID, ok := newBroker["id"].(int32)
		if !ok {
			continue
		}

		if oldBroker, exists := oldBrokerMap[newID]; exists {
			// broker存在，检查属性是否变化
			if oldBroker["addr"] != newBroker["addr"] || oldBroker["connected"] != newBroker["connected"] {
				changes = append(changes, map[string]interface{}{
					"type":   "modified",
					"id":     newID,
					"before": oldBroker,
					"after":  newBroker,
				})
			}
			// 标记为已处理
			delete(oldBrokerMap, newID)
		} else {
			// 新增的broker
			changes = append(changes, map[string]interface{}{
				"type":   "added",
				"id":     newID,
				"broker": newBroker,
			})
		}
	}

	// 剩余在oldBrokerMap中的是已删除的broker
	for id, broker := range oldBrokerMap {
		changes = append(changes, map[string]interface{}{
			"type":   "removed",
			"id":     id,
			"broker": broker,
		})
	}

	return changes
}

// validateMetadata 验证多broker元数据一致性
func validateMetadata(client sarama.Client) {
	logger.Info("开始验证Kafka元数据一致性")

	// 获取所有brokers
	brokers := client.Brokers()
	if len(brokers) == 0 {
		logger.Error("无法获取Broker列表")
		return
	}

	logger.Info("获取到Broker列表", zap.Int("数量", len(brokers)))

	// 获取所有主题数量(使用当前连接的客户端)
	topics, err := client.Topics()
	if err != nil {
		logger.Error("获取主题列表失败", zap.Error(err))
		return
	}

	logger.Info("获取到主题列表", zap.Int("数量", len(topics)))

	// 依次连接每个broker并比较元数据
	var firstBrokerMetadata map[string]interface{}
	var firstBrokerID int32
	var firstBrokerAddr string
	var inconsistencies []map[string]interface{}
	var responseTimeDiscrepancies []map[string]interface{}

	// 连接第一个broker获取基准元数据
	firstBroker := brokers[0]
	firstBrokerID = firstBroker.ID()
	firstBrokerAddr = firstBroker.Addr()

	// 创建连接到第一个broker的客户端配置
	config := sarama.NewConfig()
	config.Version = sarama.V3_0_0_0
	config.Metadata.Full = true

	startTime := time.Now()
	// 使用单个broker作为引导服务器创建客户端
	firstClient, err := sarama.NewClient([]string{firstBrokerAddr}, config)
	if err != nil {
		logger.Error("连接到第一个Broker失败",
			zap.Int32("BrokerID", firstBrokerID),
			zap.String("BrokerAddr", firstBrokerAddr),
			zap.Error(err))
		return
	}
	firstResponseTime := time.Since(startTime)
	defer firstClient.Close()

	// 获取第一个broker的元数据作为基准
	firstBrokerMetadata, err = utils.GetKafkaMetadata(firstClient)
	if err != nil {
		logger.Error("从第一个Broker获取元数据失败",
			zap.Int32("BrokerID", firstBrokerID),
			zap.String("BrokerAddr", firstBrokerAddr),
			zap.Error(err))
		return
	}

	logger.Info("获取到基准元数据",
		zap.Int32("基准BrokerID", firstBrokerID),
		zap.String("基准BrokerAddr", firstBrokerAddr),
		zap.Duration("响应时间", firstResponseTime))

	// 遍历其他brokers并比较元数据
	for i := 1; i < len(brokers); i++ {
		broker := brokers[i]
		brokerAddr := broker.Addr()
		brokerID := broker.ID()

		logger.Info("正在验证Broker的元数据",
			zap.Int32("BrokerID", brokerID),
			zap.String("BrokerAddr", brokerAddr))

		// 创建连接到此broker的客户端
		startTime := time.Now()
		brokerClient, err := sarama.NewClient([]string{brokerAddr}, config)
		if err != nil {
			logger.Error("连接到Broker失败",
				zap.Int32("BrokerID", brokerID),
				zap.String("BrokerAddr", brokerAddr),
				zap.Error(err))

			inconsistencies = append(inconsistencies, map[string]interface{}{
				"brokerID":     brokerID,
				"brokerAddr":   brokerAddr,
				"errorMessage": "连接失败: " + err.Error(),
				"type":         "connection",
			})
			continue
		}
		brokerResponseTime := time.Since(startTime)
		defer brokerClient.Close()

		// 记录响应时间差异
		timeDiff := brokerResponseTime - firstResponseTime
		if timeDiff > 500*time.Millisecond || timeDiff < -500*time.Millisecond {
			responseTimeDiscrepancies = append(responseTimeDiscrepancies, map[string]interface{}{
				"brokerID":         brokerID,
				"brokerAddr":       brokerAddr,
				"responseTime":     brokerResponseTime,
				"baseResponseTime": firstResponseTime,
				"difference":       timeDiff,
			})
		}

		// 获取该broker的元数据
		brokerMetadata, err := utils.GetKafkaMetadata(brokerClient)
		if err != nil {
			logger.Error("从Broker获取元数据失败",
				zap.Int32("BrokerID", brokerID),
				zap.String("BrokerAddr", brokerAddr),
				zap.Error(err))

			inconsistencies = append(inconsistencies, map[string]interface{}{
				"brokerID":     brokerID,
				"brokerAddr":   brokerAddr,
				"errorMessage": "获取元数据失败: " + err.Error(),
				"type":         "metadata_fetch",
			})
			continue
		}

		// 验证advertised.listeners配置一致性
		// 比较broker列表
		firstBrokersList, _ := firstBrokerMetadata["brokers"].([]map[string]interface{})
		currentBrokersList, _ := brokerMetadata["brokers"].([]map[string]interface{})

		// 创建映射以便快速查找
		firstBrokersMap := make(map[int32]map[string]interface{})
		for _, b := range firstBrokersList {
			if id, ok := b["id"].(int32); ok {
				firstBrokersMap[id] = b
			}
		}

		// 比较每个broker的地址
		for _, b := range currentBrokersList {
			if id, ok := b["id"].(int32); ok {
				if firstBroker, exists := firstBrokersMap[id]; exists {
					// 检查地址是否一致
					if firstBroker["addr"] != b["addr"] {
						logger.Warn("检测到不一致的broker地址配置",
							zap.Int32("BrokerID", id),
							zap.String("基准Broker地址", fmt.Sprint(firstBroker["addr"])),
							zap.String("当前Broker地址", fmt.Sprint(b["addr"])))

						inconsistencies = append(inconsistencies, map[string]interface{}{
							"type":           "advertised_address",
							"brokerID":       id,
							"referenceAddr":  firstBroker["addr"],
							"reportedAddr":   b["addr"],
							"reportedBy":     brokerID,
							"reportedByAddr": brokerAddr,
						})
					}
				}
			}
		}

		// 验证主题分区分配
		firstTopics, _ := firstBrokerMetadata["topics"].(map[string]map[string]interface{})
		currentTopics, _ := brokerMetadata["topics"].(map[string]map[string]interface{})

		// 检查主题数量是否一致
		if len(firstTopics) != len(currentTopics) {
			logger.Warn("主题数量不一致",
				zap.Int("基准主题数量", len(firstTopics)),
				zap.Int("当前主题数量", len(currentTopics)))

			inconsistencies = append(inconsistencies, map[string]interface{}{
				"type":           "topic_count",
				"brokerID":       brokerID,
				"brokerAddr":     brokerAddr,
				"referenceCount": len(firstTopics),
				"reportedCount":  len(currentTopics),
			})
		}

		// 检查主题分区分配
		for topicName, firstTopicInfo := range firstTopics {
			if currentTopicInfo, exists := currentTopics[topicName]; exists {
				firstPartitions, _ := firstTopicInfo["partitions"].([]map[string]interface{})
				currentPartitions, _ := currentTopicInfo["partitions"].([]map[string]interface{})

				// 检查分区数量
				if len(firstPartitions) != len(currentPartitions) {
					logger.Warn("主题分区数量不一致",
						zap.String("主题", topicName),
						zap.Int("基准分区数量", len(firstPartitions)),
						zap.Int("当前分区数量", len(currentPartitions)))

					inconsistencies = append(inconsistencies, map[string]interface{}{
						"type":           "partition_count",
						"brokerID":       brokerID,
						"brokerAddr":     brokerAddr,
						"topic":          topicName,
						"referenceCount": len(firstPartitions),
						"reportedCount":  len(currentPartitions),
					})
				}

				// 创建映射以便快速比较
				firstPartitionsMap := make(map[int32]map[string]interface{})
				for _, p := range firstPartitions {
					if id, ok := p["id"].(int32); ok {
						firstPartitionsMap[id] = p
					}
				}

				// 比较每个分区的Leader和副本
				for _, p := range currentPartitions {
					if id, ok := p["id"].(int32); ok {
						if firstPartition, exists := firstPartitionsMap[id]; exists {
							// 检查Leader是否一致
							firstLeader, _ := firstPartition["leader"].(map[string]interface{})
							currentLeader, _ := p["leader"].(map[string]interface{})

							if firstLeader != nil && currentLeader != nil {
								firstLeaderID, _ := firstLeader["id"].(int32)
								currentLeaderID, _ := currentLeader["id"].(int32)

								if firstLeaderID != currentLeaderID {
									logger.Warn("分区Leader不一致",
										zap.String("主题", topicName),
										zap.Int32("分区", id),
										zap.Int32("基准LeaderID", firstLeaderID),
										zap.Int32("当前LeaderID", currentLeaderID))

									inconsistencies = append(inconsistencies, map[string]interface{}{
										"type":            "leader_inconsistency",
										"brokerID":        brokerID,
										"brokerAddr":      brokerAddr,
										"topic":           topicName,
										"partition":       id,
										"referenceLeader": firstLeaderID,
										"reportedLeader":  currentLeaderID,
									})
								}
							}
						}
					}
				}
			}
		}
	}

	// 报告验证结果
	if len(inconsistencies) > 0 {
		logger.Error("检测到元数据不一致问题",
			zap.Int("问题数量", len(inconsistencies)),
			zap.Any("问题详情", inconsistencies))
	} else {
		logger.Info("所有Broker元数据一致，验证通过")
	}

	if len(responseTimeDiscrepancies) > 0 {
		logger.Warn("检测到Broker响应时间差异较大",
			zap.Int("问题数量", len(responseTimeDiscrepancies)),
			zap.Any("详情", responseTimeDiscrepancies))
	}
}
