package kafka

import (
	"github.com/spf13/cobra"
)

var kafkaConfigFile string

// KafkaCmd 表示 kafka 命令
var KafkaCmd = &cobra.Command{
	Use:   "kafka",
	Short: "Kafka 管理工具",
	Long:  `用于管理 DCI 监测系统使用的 Kafka 主题，包括创建、查看和删除等操作。`,
}

func init() {
	// 添加全局 Kafka 相关标志
	KafkaCmd.PersistentFlags().StringVar(&kafkaConfigFile, "kafka-config", "", "Kafka 配置文件路径（默认为 ./config/kafka.yaml）")
	KafkaCmd.PersistentFlags().String("brokers", "", "Kafka broker 连接字符串，如 host1:9092,host2:9092")
}
