# Syslog 数据格式规范说明

## 1. BSD Syslog (RFC 3164) 格式
格式通常是：`<PRI>TIMESTAMP HOSTNAME TAG: MESSAGE`

示例：
```
<34>Oct 11 22:14:15 mymachine su: 'su root' failed for lonvick on /dev/pts/8
<13>Feb 25 10:30:01 router_fw %ASA-6-302014: Teardown TCP connection 12345 for outside:*************/1234 to inside:********/80 duration 0:00:30 bytes 1024
<166>Mar  1 08:00:00 appserver app[12345]: User 'admin' logged in successfully from ************
```

### 组成部分解释 (RFC 3164)

#### `<PRI>` (Priority Value)
- 用尖括号包裹的数字，范围 0-191
- 由 `Facility * 8 + Severity` 计算得出
- **Facility (设施值):** 表示消息来源的类型
  - 0=kern
  - 1=user
  - 3=daemon
  - 4=auth
  - 10=authpriv
  - 16-23=local0-local7
- **Severity (严重级别):** 表示消息的重要性
  - 0=Emergency
  - 1=Alert
  - 2=Critical
  - 3=Error
  - 4=Warning
  - 5=Notice
  - 6=Informational
  - 7=Debug
- 示例中:
  - `<34>` = 4 (auth) * 8 + 2 (Critical) -> 认证相关的严重错误
  - `<13>` = 1 (user) * 8 + 5 (Notice) -> 用户级别的通知
  - `<166>` = 20 (local4) * 8 + 6 (Informational) -> local4 设施的信息性消息

#### TIMESTAMP
- 通常是 `Mmm dd hh:mm:ss` 格式（例如 `Oct 11 22:14:15`）
- **注意:** RFC 3164 没有规定年份和时区，这可能导致解析困难。不同的设备实现可能不同

#### HOSTNAME
- 发送消息的主机名或 IP 地址（例如 `mymachine`, `router_fw`, `appserver`）

#### TAG (可选)
- 通常是产生消息的程序名称，有时会包含进程 ID (`[pid]`)，后面常跟一个冒号 `:`
- 示例：`su:`, `%ASA-6-302014:`, `app[12345]:`

#### MESSAGE
- 实际的日志消息内容，格式自由，没有严格规定
- 示例：
  - `'su root' failed...`
  - `Teardown TCP connection...`
  - `User 'admin' logged in...`

## 2. IETF Syslog (RFC 5424) 格式
格式通常是：`<PRI>VERSION TIMESTAMP HOSTNAME APP-NAME PROCID MSGID STRUCTURED-DATA MSG`

示例：
```
<165>1 2003-10-11T22:14:15.003Z mymachine.example.com evntslog - ID47 [exampleSDID@32473 iut="3" eventSource="Application" eventID="1011"] An application event log entry...
<30>1 2023-03-01T13:45:00.123+08:00 webserver nginx 12345 HTTP-ACCESS [origin ip="************" user="-"] GET /index.html HTTP/1.1 200 456
<14>1 2019-12-31T23:59:59.999Z secure-gw sshd - - - Client disconnected: Broken pipe
```

### 组成部分解释 (RFC 5424)

#### `<PRI>`
与 RFC 3164 相同，示例中:
- `<165>` = 20 (local4) * 8 + 5 (Notice)
- `<30>` = 3 (daemon) * 8 + 6 (Informational)
- `<14>` = 1 (user) * 8 + 6 (Informational)

#### VERSION
- Syslog 协议版本，对于 RFC 5424 总是 `1`

#### TIMESTAMP
- 高精度时间戳，遵循 **ISO 8601** 格式 (例如 `YYYY-MM-DDTHH:mm:ss.sssZ` 或 `YYYY-MM-DDTHH:mm:ss.sss+/-HH:MM`)
- `Z` 表示 UTC 时间，`+/-HH:MM` 表示时区偏移
- 示例: `2003-10-11T22:14:15.003Z`, `2023-03-01T13:45:00.123+08:00`, `2019-12-31T23:59:59.999Z`

#### HOSTNAME
- 发送方主机名、FQDN 或 IP 地址 (例如 `mymachine.example.com`, `webserver`, `secure-gw`)

#### APP-NAME
- 产生消息的应用程序或设备名称 (例如 `evntslog`, `nginx`, `sshd`)
- 如果未知，则为 `-`

#### PROCID
- 进程 ID (例如 `12345`)
- 如果未知或不适用，则为 `-`

#### MSGID
- 消息类型标识符 (例如 `ID47`, `HTTP-ACCESS`)
- 用于区分不同类型的消息
- 如果未知，则为 `-`

#### STRUCTURED-DATA
- 结构化数据字段，这是 RFC 5424 的一个重要增强
- 格式为 `[id key1="value1" key2="value2"]`
- 可以有零个或多个这样的块
- 如果不存在结构化数据，则为 `-`
- 示例: `[exampleSDID@32473 iut="3" eventSource="Application" eventID="1011"]`, `[origin ip="************" user="-"]`

#### MSG
- 原始的日志消息内容，采用 **UTF-8** 编码
- 如果前面提供了所有结构化信息，这部分可能为空
- 示例: `An application event log entry...`, `GET /index.html HTTP/1.1 200 456`, `Client disconnected: Broken pipe`
