package main

import (
	"bufio"
	"bytes"
	"flag"
	"fmt"
	"log"
	"net" // Import net package for direct UDP communication
	"os"
	"os/exec"
	"strings"
	"time"
)

// 默认配置常量
const (
	defaultHost = "*************"   // 默认 Logstash 服务器地址
	defaultPort = 30005             // 默认 Logstash Syslog UDP 端口
	interval    = 10 * time.Second  // 数据发送间隔
	appName     = "netstat-monitor" // APP-NAME field for RFC 5424
	msgID       = "NETSTAT_ROW"     // MSGID field for RFC 5424
)

// Calculate PRI value (Facility * 8 + Severity)
// User-level messages (1) + Informational severity (6) = 1*8 + 6 = 14
const pri = "<14>" // <Facility*8+Severity>

// 显示使用帮助
func showUsage() {
	fmt.Printf("用法: %s [选项]\n", os.Args[0])
	fmt.Println("选项:")
	flag.PrintDefaults()
	fmt.Println("\n示例:")
	fmt.Printf("  %s -host ************* -port 30005\n", os.Args[0])
	fmt.Printf("  %s -interval 5s\n", os.Args[0])
}

// Function to fetch netstat data and send it via UDP
func sendNetstatData(conn net.Conn, hostname string, pid int) {
	cmd := exec.Command("netstat", "-ib")
	output, err := cmd.Output()
	if err != nil {
		log.Printf("执行 netstat -ib 失败: %v", err)
		return // Skip sending if command fails
	}

	// 逐行发送 netstat 输出
	scanner := bufio.NewScanner(bytes.NewReader(output))
	lineNum := 0
	for scanner.Scan() {
		line := scanner.Text()
		trimmedLine := strings.TrimSpace(line)
		lineNum++

		// 跳过空行和表头行
		if trimmedLine == "" || lineNum == 1 {
			continue
		}

		// 测试
		// if lineNum == 3 {
		// 	break
		// }

		// Get current timestamp in RFC 3339 format (required by RFC 5424)
		// Using RFC3339Nano for higher precision if available
		timestamp := time.Now().Format(time.RFC3339Nano)

		// Construct the RFC 5424 message string
		// Format: <PRI>VERSION TIMESTAMP HOSTNAME APP-NAME PROCID MSGID STRUCTURED-DATA MSG
		rfc5424Msg := fmt.Sprintf("%s1 %s %s %s %d %s - %s",
			pri,         // Priority
			timestamp,   // Version 1, Timestamp
			hostname,    // Hostname
			appName,     // Application Name
			pid,         // Process ID
			msgID,       // Message ID
			trimmedLine, // The actual netstat line as MSG
		)
		log.Printf("trimmedLine 行长度: %d 字节", len(trimmedLine))

		// Send the formatted message over UDP
		_, err = conn.Write([]byte(rfc5424Msg))
		if err != nil {
			// Log error but continue running
			log.Printf("发送 Syslog (RFC 5424) 失败 (行: %d): %v", lineNum, err)
		} else {
			log.Printf("%s", rfc5424Msg)
		}
	}

	if err := scanner.Err(); err != nil {
		log.Printf("扫描 netstat 输出时出错: %v", err)
	}
}

func main() {
	// 定义命令行参数
	host := flag.String("host", defaultHost, "Logstash 服务器地址")
	port := flag.Int("port", defaultPort, "Logstash Syslog UDP 端口")
	sendInterval := flag.Duration("interval", interval, "数据发送间隔 (例如: 5s, 1m)")
	help := flag.Bool("h", false, "显示帮助信息")

	// 自定义帮助信息
	flag.Usage = showUsage

	// 解析命令行参数
	flag.Parse()

	// 如果指定了 -h 参数，显示帮助信息并退出
	if *help {
		showUsage()
		os.Exit(0)
	}

	// Establish UDP connection directly
	serverAddr := fmt.Sprintf("%s:%d", *host, *port)
	conn, err := net.Dial("udp", serverAddr)
	if err != nil {
		log.Fatalf("无法建立 UDP 连接到 %s: %v", serverAddr, err)
	}
	defer conn.Close()

	log.Printf("已连接到 Syslog 服务器 %s (UDP)，准备发送首次数据并每 %s 再次发送...", serverAddr, *sendInterval)

	// Get hostname and PID once
	hostnameBytes, _ := exec.Command("hostname").Output()
	hostname := strings.TrimSpace(string(hostnameBytes))
	if hostname == "" {
		hostname = "-" // Use '-' if hostname is unavailable
	}
	pid := os.Getpid()

	// --- Send data immediately upon starting ---
	log.Printf("发送 netstat 数据...")
	sendNetstatData(conn, hostname, pid)
	// --- End initial send ---

	// 定时执行 subsequent sends
	ticker := time.NewTicker(*sendInterval)
	defer ticker.Stop()

	for range ticker.C {
		log.Printf("发送 netstat 数据...")
		sendNetstatData(conn, hostname, pid)
	}
}
