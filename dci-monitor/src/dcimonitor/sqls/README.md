# DCI数据监测系统数据库脚本

本目录包含DCI数据监测系统所需的MySQL数据库初始化脚本。**初始化 dcimonitor 服务时，执行 init_database.sql 脚本**。

## 文件说明

- `init_database.sql`：主脚本，用于创建数据库并执行所有初始化脚本
- `create_tables.sql`：创建所有数据表结构的脚本
- `create_constraints.sql`：创建外键约束的脚本
- `alert_tables.sql`：创建告警相关表结构的脚本
- `topology_lldp_tables.sql`：创建拓扑和LLDP相关表结构的脚本
- `import_alert_tables.sh`：导入告警表结构的辅助脚本

## 数据库表说明

脚本将创建以下数据表：

1. `monitor_client_registry`：客户端注册与状态管理表
2. `monitor_config_store`：Telegraf配置存储表
3. `monitor_task_metadata`：自动化任务元数据表
4. `monitor_report_metadata`：报告元数据表
5. `monitor_tenant_map`：租户资源映射表
6. `monitor_alert`：告警记录表，存储系统中的告警信息
7. `monitor_alert_rule`：告警规则表，存储告警触发规则
8. `monitor_alert_notification`：告警通知表，存储告警通知配置

所有表都使用`monitor_`前缀命名。

## 执行方法

### 方法一：使用主脚本（推荐）

使用MySQL客户端执行主脚本：

```bash
mysql -u用户名 -p密码 < init_database.sql

```

### 方法二：分步执行

1. 首先创建数据库：

```bash
mysql -u用户名 -p密码 -e "CREATE DATABASE IF NOT EXISTS dci_monitor CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;"
```

2. 然后执行表创建脚本：

```bash
mysql -u用户名 -p密码 dci_monitor < create_tables.sql
```

3. 最后执行外键约束创建脚本：

```bash
mysql -u用户名 -p密码 dci_monitor < create_constraints.sql
```

### 方法三：仅导入告警表结构

使用提供的辅助脚本导入告警表结构：

```bash
# 使用默认参数
./import_alert_tables.sh

# 指定数据库连接参数（密码将在执行时提示输入）
./import_alert_tables.sh -h 数据库主机 -P 3306 -d dci -u root -p
```

或者直接使用MySQL客户端：

```bash
mysql -u用户名 -p 数据库名 < alert_tables.sql
```

## 注意事项

- 脚本执行前请确保已备份现有数据库
- 所有脚本使用事务包装，保证执行的原子性
- 创建表时使用了`IF NOT EXISTS`条件，避免重复创建表结构
- 外键约束需要在所有表创建完成后才能添加
- 告警表结构包含了最新的时间维度设计，支持告警的完整生命周期管理
- **安全提示**: 
  - 避免在命令行中直接输入密码，使用`-p`参数时不要直接跟密码，而是在提示时输入
  - 或者使用环境变量设置密码: `export DB_PASS="您的密码"; ./import_alert_tables.sh -h 主机 -P 端口 -d 数据库 -u 用户名` 