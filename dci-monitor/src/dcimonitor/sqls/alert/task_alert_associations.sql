-- 任务告警关联功能数据库变更脚本
-- 适用于现有系统升级，增加任务协同监控功能

-- 1. 为monitor_alert表添加device_id字段(如果不存在)
ALTER TABLE monitor_alert ADD COLUMN device_id VARCHAR(128) NULL COMMENT '设备ID(从labels中提取并存储，便于查询优化)';
ALTER TABLE monitor_alert ADD INDEX idx_device_id (device_id);

-- 2. 创建任务告警关联表
CREATE TABLE IF NOT EXISTS monitor_task_alert_associations (
    id VARCHAR(36) PRIMARY KEY COMMENT '关联记录ID',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    alert_id VARCHAR(36) NOT NULL COMMENT '告警ID(关联monitor_alert.id)',
    association_type ENUM('active','post_task') NOT NULL COMMENT '关联类型(active=任务执行期间,post_task=任务后续影响)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '关联时间',
    INDEX idx_task_id (task_id),
    INDEX idx_alert_id (alert_id),
    INDEX idx_association_type (association_type),
    INDEX idx_task_type (task_id, association_type),
    FOREIGN KEY (alert_id) REFERENCES monitor_alert(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务告警关联表';

-- 3. 创建网络自动化协同任务监控会话表(与17号文档保持一致)
CREATE TABLE IF NOT EXISTS monitor_network_auto_task_monitoring_sessions (
    session_id VARCHAR(64) PRIMARY KEY COMMENT '会话唯一标识符',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    device_ids JSON NOT NULL COMMENT '监控设备ID列表',
    start_time TIMESTAMP NOT NULL COMMENT '监控开始时间(Asia/Shanghai)',
    end_time TIMESTAMP NULL COMMENT '监控结束时间(Asia/Shanghai)',
    status ENUM('active', 'completed') NOT NULL DEFAULT 'active' COMMENT '会话状态',
    end_method ENUM('auto', 'manual') NULL COMMENT '结束方式：auto-自动结束，manual-手动停止',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(Asia/Shanghai)',
    UNIQUE KEY uk_task_id (task_id) COMMENT '任务ID唯一性约束',
    INDEX idx_status_time (status, start_time),
    INDEX idx_end_time (end_time) COMMENT '支持延展监测查询优化'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网络自动化协同任务监控会话表'; 