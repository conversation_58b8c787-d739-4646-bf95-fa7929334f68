-- ==================================================
-- 任务协同期指标类数据联动数据库表结构
-- 创建时间: 2025-08-20
-- 说明: 包含指标监测会话、基线统计、数据记录三个核心表
-- ==================================================

-- 任务指标监测会话表
CREATE TABLE IF NOT EXISTS monitor_task_metrics_sessions (
    session_id VARCHAR(64) PRIMARY KEY COMMENT '指标监测会话ID',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    task_session_id VARCHAR(64) NOT NULL COMMENT '关联任务会话ID',
    device_ids JSON NOT NULL COMMENT '监测设备ID列表',
    metrics_types JSON NOT NULL COMMENT '监测指标类型列表',
    baseline_start TIMESTAMP NOT NULL COMMENT '基线统计开始时间(Asia/Shanghai)',
    baseline_end TIMESTAMP NOT NULL COMMENT '基线统计结束时间(Asia/Shanghai)',
    monitoring_start TIMESTAMP NOT NULL COMMENT '任务数据记录开始时间(Asia/Shanghai)',
    monitoring_end TIMESTAMP NULL COMMENT '任务数据记录结束时间(Asia/Shanghai)',
    extended_end TIMESTAMP NULL COMMENT '延展数据记录结束时间(Asia/Shanghai)',
    status ENUM('monitoring_active', 'baseline_analyzing', 'fully_active', 'extended_monitoring', 'completed') NOT NULL DEFAULT 'monitoring_active' COMMENT '数据记录状态：monitoring_active=实时数据记录已启动，baseline_analyzing=基线统计中，fully_active=基线统计+数据记录全部就绪，extended_monitoring=延展数据记录中，completed=已完成',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(Asia/Shanghai)',
    INDEX idx_task_id (task_id),
    INDEX idx_status (status),
    INDEX idx_status_time (status, monitoring_start),
    FOREIGN KEY (task_session_id) REFERENCES monitor_network_auto_task_monitoring_sessions(session_id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务指标监测会话表，与任务协同监控会话关联';

-- 任务指标基线统计表
CREATE TABLE IF NOT EXISTS monitor_task_metrics_baselines (
    id VARCHAR(36) PRIMARY KEY COMMENT '基线记录ID',
    metrics_session_id VARCHAR(64) NOT NULL COMMENT '指标监测会话ID',
    device_id VARCHAR(64) NOT NULL COMMENT '设备ID',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    avg_value DOUBLE COMMENT '平均值',
    max_value DOUBLE COMMENT '最大值',
    min_value DOUBLE COMMENT '最小值',
    std_dev DOUBLE COMMENT '标准差',
    sample_count INT COMMENT '样本数量',
    time_range_start TIMESTAMP NOT NULL COMMENT '时间范围开始(Asia/Shanghai)',
    time_range_end TIMESTAMP NOT NULL COMMENT '时间范围结束(Asia/Shanghai)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(Asia/Shanghai)',
    INDEX idx_session_device (metrics_session_id, device_id),
    INDEX idx_metric_name (metric_name),
    INDEX idx_time_range (time_range_start, time_range_end),
    FOREIGN KEY (metrics_session_id) REFERENCES monitor_task_metrics_sessions(session_id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务指标基线统计表，存储任务执行前30分钟的指标统计特征';

-- 任务指标数据记录表
CREATE TABLE IF NOT EXISTS monitor_task_metrics_changes (
    id VARCHAR(36) PRIMARY KEY COMMENT '数据记录ID',
    metrics_session_id VARCHAR(64) NOT NULL COMMENT '指标监测会话ID',
    device_id VARCHAR(64) NOT NULL COMMENT '设备ID',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    change_type ENUM('data_record', 'threshold_exceeded', 'trend_change', 'sudden_change', 'pattern_anomaly') NOT NULL DEFAULT 'data_record' COMMENT '记录类型：当前阶段统一使用data_record，其他类型为后续异常检测算法预留',
    current_value DOUBLE NOT NULL COMMENT '当前值',
    baseline_value DOUBLE COMMENT '基线值（基线就绪后填充）',
    change_magnitude DOUBLE COMMENT '变化幅度（当前值-基线值）',
    detection_time TIMESTAMP NOT NULL COMMENT '数据记录时间(Asia/Shanghai)',
    monitoring_phase ENUM('baseline', 'task_execution', 'extended') NOT NULL COMMENT '监测阶段',
    severity ENUM('info', 'low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'info' COMMENT '当前阶段统一使用info，其他级别为后续算法预留',
    description TEXT COMMENT '数据描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(Asia/Shanghai)',
    INDEX idx_session_device (metrics_session_id, device_id),
    INDEX idx_detection_time (detection_time),
    INDEX idx_severity (severity),
    INDEX idx_phase_time (monitoring_phase, detection_time),
    FOREIGN KEY (metrics_session_id) REFERENCES monitor_task_metrics_sessions(session_id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务指标数据记录表，当前阶段仅记录指标数据，为后续异常检测算法预留扩展字段';

-- ==================================================
-- 创建完成，3个核心表及相关索引已定义
-- ================================================== 