-- 任务协同监控模块数据库表结构
-- 
-- 功能说明：支持网络自动化任务的协同监控功能
-- 创建时间：2025-07-31
-- 时区设置：Asia/Shanghai

-- 设置时区为Asia/Shanghai
SET time_zone = '+08:00';

-- =====================================================================
-- 任务告警关联表 (monitor_task_alert_associations)
-- =====================================================================
-- 功能作用：建立网络自动化任务与系统中已存在告警的关联关系
-- 
-- 核心概念：
-- 1. 关联对象：将已经发生并存储在monitor_alert表中的告警记录与具体任务建立关联
-- 2. 关联时机：告警处理系统发现告警时，通过设备ID查询是否有相关任务，如有则创建关联
-- 3. 延展监测：支持任务完成后1小时内的告警仍与任务关联(post_completion类型)
-- 
-- 业务场景：
-- - 任务执行期间设备产生告警，系统自动关联到任务
-- - 任务完成后短时间内出现的告警仍然关联到任务，用于评估任务后续影响
-- - 为任务执行报告提供告警统计和分析数据
-- 
-- 与其他表关系：
-- - 外键关联monitor_alert表，获取具体告警信息
-- - 通过task_id关联任务监控会话表
-- - 用于view_task_alert_summary视图的数据源
-- =====================================================================
-- 任务告警关联表
CREATE TABLE IF NOT EXISTS monitor_task_alert_associations (
    id VARCHAR(64) PRIMARY KEY COMMENT '关联记录唯一标识符',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    alert_id VARCHAR(64) NOT NULL COMMENT '告警ID',
    association_type ENUM('active_execution', 'post_completion') NOT NULL COMMENT '关联类型: active_execution-任务执行期间, post_completion-任务完成后延展监测',
    device_id VARCHAR(64) NOT NULL COMMENT '设备ID，记录告警来源设备',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(Asia/Shanghai)',
    
    -- 索引设计
    INDEX idx_task_id (task_id) COMMENT '任务ID索引，用于查询任务相关告警',
    INDEX idx_alert_id (alert_id) COMMENT '告警ID索引，用于查询告警相关任务',
    INDEX idx_device_id (device_id) COMMENT '设备ID索引，用于查询设备相关关联',
    INDEX idx_association_type (association_type) COMMENT '关联类型索引，用于分类查询',
    INDEX idx_task_device (task_id, device_id) COMMENT '复合索引，用于任务设备组合查询',
    INDEX idx_created_at (created_at) COMMENT '创建时间索引，用于时间范围查询',
    
    -- 外键约束
    FOREIGN KEY (alert_id) REFERENCES monitor_alert(id) ON DELETE CASCADE COMMENT '关联monitor_alert表，级联删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务告警关联表，记录网络自动化任务与告警的关联关系及告警来源设备';

-- =====================================================================
-- 任务数据收集记录表 (monitor_task_data_collections)
-- =====================================================================
-- 功能作用：记录和跟踪任务执行期间数据收集过程的状态和统计信息
-- 
-- 核心概念：
-- 1. 收集跟踪：记录数据收集过程本身，而不是存储具体的数据内容
-- 2. 分类管理：按数据类型(metrics/logs/alerts/events)分别记录收集情况
-- 3. 状态管理：跟踪每个收集任务的执行状态(collecting/completed/failed)
-- 
-- 业务场景：
-- - 任务开始时为每个设备的每种数据类型创建收集记录
-- - 数据收集组件定期更新收集状态和数据计数
-- - 任务结束时统计各类数据的收集完成情况
-- - 为系统状态查询和收集报告提供基础数据
-- 
-- 与告警关联表的区别：
-- - 告警关联表：处理已存在的告警与任务的关系映射
-- - 数据收集表：跟踪主动收集数据的过程状态
-- - 前者是结果关联，后者是过程管理
-- 
-- 与其他表关系：
-- - 外键关联monitor_network_auto_task_monitoring_sessions表
-- - 用于view_task_collection_summary视图的数据源
-- - 为/api/v1/system/task-collaboration/collections/{sessionId}接口提供数据
-- =====================================================================
-- 任务数据收集记录表
CREATE TABLE IF NOT EXISTS monitor_task_data_collections (
    id VARCHAR(64) PRIMARY KEY COMMENT '记录唯一标识符',
    session_id VARCHAR(64) NOT NULL COMMENT '任务会话ID，关联monitor_network_auto_task_monitoring_sessions表',
    data_type ENUM('metrics', 'logs', 'alerts', 'events') NOT NULL COMMENT '数据类型: metrics-性能指标, logs-日志数据, alerts-告警数据, events-事件数据',
    device_id VARCHAR(64) NOT NULL COMMENT '设备ID',
    collection_start TIMESTAMP NOT NULL COMMENT '收集开始时间(Asia/Shanghai)',
    collection_end TIMESTAMP NULL COMMENT '收集结束时间(Asia/Shanghai)',
    data_count INT DEFAULT 0 COMMENT '收集的数据条数',
    status ENUM('collecting', 'completed', 'failed') NOT NULL DEFAULT 'collecting' COMMENT '收集状态: collecting-收集中, completed-已完成, failed-失败',
    error_message TEXT COMMENT '错误信息，仅在status为failed时使用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(Asia/Shanghai)',
    
    -- 索引设计
    INDEX idx_session_id (session_id) COMMENT '会话ID索引，用于查询会话相关数据收集',
    INDEX idx_data_type (data_type) COMMENT '数据类型索引，用于分类查询',
    INDEX idx_device_id (device_id) COMMENT '设备ID索引，用于查询设备相关收集',
    INDEX idx_status (status) COMMENT '状态索引，用于查询特定状态的收集',
    INDEX idx_session_device (session_id, device_id) COMMENT '复合索引，用于会话设备组合查询',
    INDEX idx_collection_start (collection_start) COMMENT '收集开始时间索引，用于时间范围查询',
    INDEX idx_collection_end (collection_end) COMMENT '收集结束时间索引，用于时间范围查询',
    
    -- 外键约束
    FOREIGN KEY (session_id) REFERENCES monitor_network_auto_task_monitoring_sessions(session_id) ON DELETE CASCADE COMMENT '关联任务监控会话表，级联删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务数据收集记录表，记录任务执行期间的数据收集情况';

-- 创建视图：任务告警汇总视图
CREATE OR REPLACE VIEW view_task_alert_summary AS
SELECT 
    taa.task_id,
    COUNT(*) as total_alerts,
    SUM(CASE WHEN a.level = 'critical' THEN 1 ELSE 0 END) as critical_alerts,
    SUM(CASE WHEN a.level = 'warning' THEN 1 ELSE 0 END) as warning_alerts,
    SUM(CASE WHEN a.level = 'info' THEN 1 ELSE 0 END) as info_alerts,
    SUM(CASE WHEN taa.association_type = 'active_execution' THEN 1 ELSE 0 END) as active_alerts,
    SUM(CASE WHEN taa.association_type = 'post_completion' THEN 1 ELSE 0 END) as post_alerts,
    COUNT(DISTINCT taa.device_id) as affected_devices,
    MIN(a.starts_at) as first_alert_time,
    MAX(a.starts_at) as last_alert_time
FROM monitor_task_alert_associations taa
INNER JOIN monitor_alert a ON taa.alert_id = a.id
GROUP BY taa.task_id;

-- 创建视图：任务数据收集汇总视图
CREATE OR REPLACE VIEW view_task_collection_summary AS
SELECT 
    tdc.session_id,
    COUNT(*) as total_collections,
    SUM(CASE WHEN tdc.data_type = 'metrics' THEN tdc.data_count ELSE 0 END) as total_metrics,
    SUM(CASE WHEN tdc.data_type = 'logs' THEN tdc.data_count ELSE 0 END) as total_logs,
    SUM(CASE WHEN tdc.data_type = 'alerts' THEN tdc.data_count ELSE 0 END) as total_alerts,
    SUM(CASE WHEN tdc.data_type = 'events' THEN tdc.data_count ELSE 0 END) as total_events,
    SUM(CASE WHEN tdc.status = 'completed' THEN 1 ELSE 0 END) as completed_collections,
    SUM(CASE WHEN tdc.status = 'failed' THEN 1 ELSE 0 END) as failed_collections,
    COUNT(DISTINCT tdc.device_id) as monitored_devices,
    MIN(tdc.collection_start) as first_collection_start,
    MAX(tdc.collection_end) as last_collection_end
FROM monitor_task_data_collections tdc
GROUP BY tdc.session_id;

-- 数据表初始化完成提示
SELECT 
    'Task Collaboration Module Database Tables Created Successfully' as message,
    NOW() as created_at,
    @@time_zone as timezone; 