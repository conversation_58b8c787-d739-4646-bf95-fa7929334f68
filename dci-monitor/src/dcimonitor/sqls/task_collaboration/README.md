# 任务协同监控模块数据库设计

## 概述

本目录包含任务协同监控模块的数据库表结构设计，支持网络自动化任务与DCI监控系统的协同工作。

## 文件说明

### task_collaboration.sql
包含完整的数据库表结构、索引设计和视图定义，支持：
- 任务告警关联管理
- 任务数据收集记录
- 统计查询视图
- 时区配置（Asia/Shanghai）

## 表结构设计

### 1. monitor_task_alert_associations
**功能**：记录网络自动化任务与告警的关联关系

**字段说明**：
- `id` - 关联记录唯一标识符（VARCHAR(64)）
- `task_id` - 任务ID（VARCHAR(64)）
- `alert_id` - 告警ID（VARCHAR(64)）
- `association_type` - 关联类型（ENUM）
  - `active_execution` - 任务执行期间
  - `post_completion` - 任务完成后延展监测
- `device_id` - 设备ID（VARCHAR(64)）
- `created_at` - 创建时间（TIMESTAMP）

**索引设计**：
- `idx_task_id` - 任务ID索引，用于查询任务相关告警
- `idx_alert_id` - 告警ID索引，用于查询告警相关任务
- `idx_device_id` - 设备ID索引，用于查询设备相关关联
- `idx_association_type` - 关联类型索引，用于分类查询
- `idx_task_device` - 复合索引，用于任务设备组合查询
- `idx_created_at` - 创建时间索引，用于时间范围查询

**外键约束**：
- `alert_id` 关联 `monitor_alert(id)`，级联删除

### 2. monitor_task_data_collections
**功能**：记录任务执行期间的数据收集情况

**字段说明**：
- `id` - 记录唯一标识符（VARCHAR(64)）
- `session_id` - 任务会话ID（VARCHAR(64)）
- `data_type` - 数据类型（ENUM）
  - `metrics` - 性能指标
  - `logs` - 日志数据
  - `alerts` - 告警数据
  - `events` - 事件数据
- `device_id` - 设备ID（VARCHAR(64)）
- `collection_start` - 收集开始时间（TIMESTAMP）
- `collection_end` - 收集结束时间（TIMESTAMP，可为NULL）
- `data_count` - 收集的数据条数（INT）
- `status` - 收集状态（ENUM）
  - `collecting` - 收集中
  - `completed` - 已完成
  - `failed` - 失败
- `error_message` - 错误信息（TEXT）
- `created_at` - 创建时间（TIMESTAMP）

**索引设计**：
- `idx_session_id` - 会话ID索引，用于查询会话相关数据收集
- `idx_data_type` - 数据类型索引，用于分类查询
- `idx_device_id` - 设备ID索引，用于查询设备相关收集
- `idx_status` - 状态索引，用于查询特定状态的收集
- `idx_session_device` - 复合索引，用于会话设备组合查询
- `idx_collection_start` - 收集开始时间索引
- `idx_collection_end` - 收集结束时间索引

**外键约束**：
- `session_id` 关联 `monitor_network_auto_task_monitoring_sessions(session_id)`，级联删除

## 视图设计

### 1. view_task_alert_summary
**功能**：任务告警汇总视图

**字段说明**：
- `task_id` - 任务ID
- `total_alerts` - 总告警数
- `critical_alerts` - 严重告警数
- `warning_alerts` - 警告告警数
- `info_alerts` - 信息告警数
- `active_alerts` - 执行期间告警数
- `post_alerts` - 延展监测告警数
- `affected_devices` - 受影响设备数
- `first_alert_time` - 首个告警时间
- `last_alert_time` - 最后告警时间

### 2. view_task_collection_summary
**功能**：任务数据收集汇总视图

**字段说明**：
- `session_id` - 会话ID
- `total_collections` - 总收集记录数
- `total_metrics` - 总指标数
- `total_logs` - 总日志数
- `total_alerts` - 总告警数
- `total_events` - 总事件数
- `completed_collections` - 已完成收集数
- `failed_collections` - 失败收集数
- `monitored_devices` - 监控设备数
- `first_collection_start` - 首次收集开始时间
- `last_collection_end` - 最后收集结束时间

## 查询示例

### 查询任务相关告警统计
```sql
SELECT 
    task_id,
    total_alerts,
    critical_alerts,
    warning_alerts,
    affected_devices
FROM view_task_alert_summary 
WHERE task_id = 'task-001';
```

### 查询任务数据收集汇总
```sql
SELECT 
    session_id,
    total_metrics,
    total_logs,
    completed_collections,
    monitored_devices
FROM view_task_collection_summary 
WHERE session_id = 'session-001';
```

### 查询任务执行期间的告警
```sql
SELECT 
    a.id,
    a.name,
    a.level,
    a.starts_at,
    taa.device_id
FROM monitor_task_alert_associations taa
INNER JOIN monitor_alert a ON taa.alert_id = a.id
WHERE taa.task_id = 'task-001' 
  AND taa.association_type = 'active_execution'
ORDER BY a.starts_at DESC;
```

### 查询设备的数据收集状态
```sql
SELECT 
    device_id,
    data_type,
    status,
    data_count,
    collection_start,
    collection_end
FROM monitor_task_data_collections
WHERE session_id = 'session-001'
  AND device_id = 'device-001'
ORDER BY data_type;
```

## 性能优化建议

### 索引使用
1. **高频查询字段**已建立单列索引：task_id、device_id、session_id
2. **复合查询**使用复合索引：(task_id, device_id)、(session_id, device_id)
3. **时间范围查询**使用时间字段索引：created_at、collection_start、collection_end

### 查询优化
1. **使用索引覆盖**：尽量使用已建立索引的字段进行查询
2. **分页查询**：大数据量查询时使用LIMIT和OFFSET
3. **时间范围限制**：长时间跨度查询时添加时间范围限制

### 数据维护
1. **定期清理**：清理超过保留期的历史数据
2. **统计信息更新**：定期更新表的统计信息
3. **索引维护**：监控和重建必要的索引

## 部署说明

### 执行顺序
1. 确保依赖表已存在：
   - `monitor_alert`
   - `monitor_network_auto_task_monitoring_sessions`
2. 执行 `task_collaboration.sql`
3. 验证表结构和索引创建成功

### 权限要求
- 需要具有CREATE TABLE权限
- 需要具有CREATE INDEX权限  
- 需要具有CREATE VIEW权限
- 需要具有REFERENCES权限（创建外键）

### 验证方法
```sql
-- 检查表是否创建成功
SHOW TABLES LIKE 'monitor_task%';

-- 检查索引是否创建成功
SHOW INDEX FROM monitor_task_alert_associations;
SHOW INDEX FROM monitor_task_data_collections;

-- 检查视图是否创建成功
SHOW FULL TABLES WHERE Table_type = 'VIEW' AND Tables_in_database LIKE 'view_task%';

-- 验证时区设置
SELECT @@time_zone;
```

## 数据备份建议

### 备份策略
1. **定期全量备份**：每日备份整个数据库
2. **增量备份**：每小时备份增量数据
3. **关键表备份**：关键操作前备份相关表

### 恢复测试
1. **定期恢复测试**：验证备份数据完整性
2. **性能测试**：恢复后验证查询性能
3. **数据一致性检查**：验证外键关系完整性 