# dcimonitor 二进制文件更新指南

本文档提供了在Kubernetes环境中更新dcimonitor二进制文件的完整指南。

## 背景

在Kubernetes环境中，更新容器内的二进制文件通常需要重新构建和推送镜像，这个过程可能比较耗时。为了简化这一流程，我们实现了一种通过PersistentVolumeClaim (PVC)更新二进制文件的机制，无需重新构建镜像。

## 文件结构

- `dcimonitor-k8s.yaml` - 主部署配置文件
- `dcimonitor-pvc.yaml` - PVC定义文件（仅首次部署时应用）
- `replace_dcimonitor_bin.sh` - 二进制文件更新脚本

## 首次部署流程

1. 创建PVC（仅首次部署时需要）：

```bash
kubectl apply -f dcimonitor-pvc.yaml
```

2. 部署应用：

```bash
kubectl apply -f dcimonitor-k8s.yaml
```

## 更新二进制文件

使用`replace_dcimonitor_bin.sh`脚本更新二进制文件：

1. 将新的二进制文件复制到临时目录：

```bash
cp path/to/new/dcimonitor /tmp/dcimonitor
```

2. 运行更新脚本：

```bash
./replace_dcimonitor_bin.sh
```

或者指定版本：

```bash
./replace_dcimonitor_bin.sh --version 0.1.2
```

## 恢复原始二进制文件

如果需要恢复到原始的二进制文件（镜像中打包的版本）：

```bash
./replace_dcimonitor_bin.sh --restore
```

## 检查当前状态

检查PVC中的二进制文件状态：

```bash
./replace_dcimonitor_bin.sh --check
```

## 工作原理

1. **初始化容器**：Pod启动时，initContainer会检查PVC中是否有与当前部署版本匹配的二进制文件。
   - 如果找到匹配版本的文件，则使用PVC中的二进制文件。
   - 如果没有找到或版本不匹配，则使用镜像中的原始二进制文件。

2. **版本控制**：通过在Pod注解中定义版本号，确保只使用与当前部署匹配的二进制文件。

3. **共享卷**：使用emptyDir卷在initContainer和主容器之间共享二进制文件。

## 故障排除

### 常见问题

1. **Pod初始化失败**：
   - 检查PVC是否正确创建：`kubectl get pvc dcimonitor-binary-pvc -n dci`
   - 查看initContainer日志：`kubectl logs <pod-name> -c binary-init -n dci`

2. **二进制文件不存在**：
   - 确保已将二进制文件复制到`/tmp/dcimonitor`
   - 检查PVC中的文件：`./replace_dcimonitor_bin.sh --check`

3. **版本不匹配**：
   - 检查当前部署的版本：`kubectl get deployment dcimonitor -n dci -o jsonpath='{.spec.template.metadata.annotations.dcimonitor\.version}'`
   - 更新时指定正确的版本：`./replace_dcimonitor_bin.sh --version <版本号>`

### 注意事项

- 更新部署配置文件时，不要重新应用PVC定义，否则可能会丢失已存储的二进制文件。
- 确保二进制文件具有执行权限。
- 版本号必须与部署配置中的`dcimonitor.version`注解匹配。

## 高级用法

### 初始化PVC（首次部署）

```bash
./replace_dcimonitor_bin.sh --init
```

### 在新环境中部署

1. 创建PVC：`./replace_dcimonitor_bin.sh --init`
2. 更新二进制文件：`./replace_dcimonitor_bin.sh`
3. 部署应用：`kubectl apply -f dcimonitor-k8s.yaml` 