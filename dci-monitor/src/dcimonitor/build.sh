#!/bin/bash

# 设置 -e 以在任何命令失败时退出
set -e

# --- 配置项 ---
# 定义输出目录
OUTPUT_DIR="bin"
# Go 二进制文件名
BINARY_NAME="dcimonitor"
# 默认的 Docker 镜像基础名称 (不含标签)
DEFAULT_IMAGE_BASE_NAME="cr.registry.pcloud.citic.com/dci/dcimonitor"
# 临时文件名，用于传递预编译的二进制文件给 Docker
PREBUILT_BINARY_TEMP_NAME="dcimonitor_prebuilt_for_docker"
# -- 配置项结束 --

# 显示用法信息
usage() {
  # 尝试获取最新的 Git 标签
  LATEST_TAG=$(git describe --tags --abbrev=0 2>/dev/null) || LATEST_TAG="<latest_tag>" # 如果失败，使用占位符

  echo "用法: $0 [-p <platform>] [-d [-i <image_name>] -t <tag>] [-h]"
  echo "选项:"
  echo "  -p <platform>    : 指定编译的目标平台 (macos, linux)。"
  echo "                     如果同时提供 -d，且 platform=linux，则会使用此预编译二进制文件构建 Docker 镜像。"
  echo "  -d               : 构建 Docker 镜像并导出为 tar 包。"
  echo "  -i <image_name>  : Docker 镜像基础名称 (可选, 配合 -d)。默认: $DEFAULT_IMAGE_BASE_NAME"
  echo "  -t <tag>         : Docker 镜像标签 (必需, 配合 -d 使用)。例如: 0.0.1"
  echo "  -h               : 显示此帮助信息。"
  echo " "
  echo "  构建镜像示例 (需要root，使用最新标签 '$LATEST_TAG')："
  echo "  sudo ./build.sh -p linux -d -t $LATEST_TAG"
  echo " "
  echo "  构建二进制文件示例："
  echo "  ./build.sh -p linux"
  echo "  ./build.sh -p macos"
  exit 1
}

# --- 参数数量检查 ---
# 如果没有提供任何参数，则显示帮助信息并退出
if [ $# -eq 0 ]; then
    echo "错误：未提供任何参数。"
    usage
fi
# --- 参数数量检查结束 ---

# 初始化变量
PLATFORM=""
BUILD_DOCKER=false
IMAGE_NAME="" # 用户可能通过 -i 提供
IMAGE_TAG=""

# 解析命令行参数
while getopts "hp:di:t:" opt; do
  case $opt in
    h)
      usage
      ;;
    p)
      PLATFORM="$OPTARG"
      ;;
    d)
      BUILD_DOCKER=true
      ;;
    i)
      IMAGE_NAME="$OPTARG"
      ;;
    t)
      IMAGE_TAG="$OPTARG"
      ;;
    ?)
      echo "无效选项: -$OPTARG" >&2
      usage
      ;;
  esac
done

# --- Docker 镜像构建的权限检查（提前检查） ---
if $BUILD_DOCKER; then
    # 检查 Docker 标签是否提供
    if [ -z "$IMAGE_TAG" ]; then
        echo "错误：当使用 -d 标志时，必须使用 -t <tag> 提供镜像标签。" >&2
        usage
    fi
    
    # 权限检查
    if [ "$(id -u)" != "0" ]; then
        echo "错误：执行 Docker 操作需要 root 权限或用户属于 'docker' 组。"
        echo "请使用 'sudo ./build.sh $*' 重试。"
        exit 1
    fi
fi
# --- Docker 权限检查结束 ---

# --- 更新 Swagger 文档 --- 
# 仅当请求了编译或 Docker 构建时才更新 Swagger
if [ -n "$PLATFORM" ] || $BUILD_DOCKER; then
    echo "--- 正在检查并更新 Swagger 文档 ---"
    # 检查 swag 命令是否存在
    if ! command -v swag >/dev/null 2>&1; then
        echo "错误：未找到 'swag' 命令。请先安装 swag: go install github.com/swaggo/swag/cmd/swag@latest" >&2
        exit 1
    fi
    
    # 执行 swag init
    echo "正在运行 swag init..."
    swag init
    if [ $? -ne 0 ]; then
        echo "错误：swag init 命令执行失败。" >&2
        exit 1
    fi
    echo "Swagger 文档更新成功！"
    # 打印静态的、复杂的访问路径
    SECRET_SWAGGER_PATH="doin78nDNID90jsnJBaaui3ldlDskUOnn1"
    SECRET_SWAGGER_HEADER="baibia378nDNID9938h77897"
    SECRET_SWAGGER_TAIL="JsorNID90jsnJBaaui3lpahtT"
    SWAGGER_URL="http://localhost:8080/${SECRET_SWAGGER_HEADER}/${SECRET_SWAGGER_PATH}/${SECRET_SWAGGER_TAIL}/index.html"
    echo "Swagger UI 的访问路径已设置为静态复杂路径以提高安全性。"
    echo "服务启动后，请访问以下地址："
    echo "${SWAGGER_URL}"
    echo "---------------------------------"
fi

# 标志位：是否使用了预编译的 Linux 二进制文件
USED_PREBUILT_LINUX=false
BINARY_OUTPUT_PATH="$OUTPUT_DIR/$BINARY_NAME"

# --- 原生 Go 编译部分 (可选) ---
# 仅当明确指定了平台时才执行原生编译
if [ -n "$PLATFORM" ]; then
    # 根据平台设置 GOOS 和 GOARCH
    case $PLATFORM in
      macos)
        GOOS=darwin
        GOARCH=amd64
        ;;
      linux)
        GOOS=linux
        GOARCH=amd64
        # 如果同时请求了 Docker 构建，标记我们有一个预编译的 Linux 版本
        if $BUILD_DOCKER; then
            USED_PREBUILT_LINUX=true
        fi
        ;;
      *)
        echo "错误：不支持的原生编译平台 '$PLATFORM'。支持的平台: macos, linux" >&2
        exit 1
        ;;
    esac

    # 创建输出目录（如果不存在）
    mkdir -p "$OUTPUT_DIR"

    # 定义输出路径
    BINARY_OUTPUT_PATH="$OUTPUT_DIR/$BINARY_NAME"

    # 执行编译
    echo "正在为 $PLATFORM ($GOOS/$GOARCH) 编译到 $BINARY_OUTPUT_PATH ..."
    CGO_ENABLED=0 GOOS=$GOOS GOARCH=$GOARCH go build -ldflags="-s -w" -o "$BINARY_OUTPUT_PATH" ./main.go

    # 检查编译是否成功
    if [ $? -eq 0 ]; then
      echo "原生编译成功！二进制文件位于: $BINARY_OUTPUT_PATH"

      # --- 修改原生二进制文件所有权 ---
      # 同样检查是否以 root 运行，并尝试改回原始用户
      if [ "$(id -u)" = "0" ]; then
          ORIGINAL_UID="${SUDO_UID:-0}"
          ORIGINAL_GID="${SUDO_GID:-0}"
          if [ "$ORIGINAL_UID" != "0" ] && [ "$ORIGINAL_GID" != "0" ]; then
              echo "检测到脚本以 root 权限运行，尝试将 $BINARY_OUTPUT_PATH 的所有权更改回原始用户 (UID:$ORIGINAL_UID, GID:$ORIGINAL_GID)..."
              chown "$ORIGINAL_UID:$ORIGINAL_GID" "$BINARY_OUTPUT_PATH"
              if [ $? -eq 0 ]; then
                  echo "原生二进制文件所有权已更改。"
              else
                  echo "警告：更改原生二进制文件所有权失败。文件可能仍然由 root 拥有。"
              fi
          fi
          # 不需要 else 分支，因为如果原始用户是 root 或未检测到，则保留 root 所有权是正常的
      fi
      # --- 原生二进制文件所有权修改结束 ---

      # --- 询问是否立即运行测试 (仅当不构建 Docker 时) ---
      if ! $BUILD_DOCKER; then
          echo "编译成功！"
          echo "测试环境下，访问 http://localhost:8080/${SECRET_SWAGGER_HEADER}/${SECRET_SWAGGER_PATH}/${SECRET_SWAGGER_TAIL}/doc.json 查看在线 API 文档。"
          read -p "是否立即运行测试 (./bin/dcimonitor --config ./config/config.yaml server)？[y/N]: " confirm
          echo # 换行
          if [[ "$confirm" == [yY] ]]; then
              echo "正在执行测试命令..."
              # 检查是否以 root 权限运行，并且是通过 sudo 调用的
              if [ "$(id -u)" = "0" ] && [ -n "$SUDO_UID" ] && [ "$SUDO_UID" != "0" ]; then
                  ORIGINAL_USER="${SUDO_USER:-$(id -un "$SUDO_UID")}" # 获取原始用户名
                  if [ -n "$ORIGINAL_USER" ]; then
                      echo "检测到以 root 权限运行，将切换到原始用户 '$ORIGINAL_USER' 执行测试命令..."
                      # 使用原始用户执行
                      sudo -u "$ORIGINAL_USER" "$BINARY_OUTPUT_PATH" --config ./config/config.yaml server
                  else
                      echo "警告：无法获取原始用户名，将尝试以当前用户 (root) 运行。"
                      "$BINARY_OUTPUT_PATH" --config ./config/config.yaml server
                  fi
              else
                  # 如果不是通过 sudo 以 root 运行，或本身就是普通用户，则直接执行
                  echo "以当前用户 ($(whoami)) 运行测试命令..."
                  "$BINARY_OUTPUT_PATH" --config ./config/config.yaml server
              fi
              # 注意：下面的 echo 可能在服务启动后不会立即显示，取决于服务是否立即返回
              echo "测试命令已启动 (如果服务未退出，脚本将在此等待)。"
          else
              echo "跳过立即运行测试。"
          fi
      fi # 结束 ! $BUILD_DOCKER 判断
      # --- 运行测试结束 ---

    else
      echo "原生编译失败。"
      exit 1
    fi
fi

# --- Docker 镜像构建与导出部分 ---
if $BUILD_DOCKER; then
    echo "--- 开始 Docker 构建与导出 ---"
    # 确定要使用的镜像基础名称 (如果用户没提供 -i，则使用默认值)
    FINAL_IMAGE_BASE_NAME="${IMAGE_NAME:-$DEFAULT_IMAGE_BASE_NAME}"

    FULL_IMAGE_NAME="$FINAL_IMAGE_BASE_NAME:$IMAGE_TAG"
    DOCKER_BUILD_ARGS=""

    # 如果我们有预编译的 Linux 二进制文件
    if $USED_PREBUILT_LINUX; then
        echo "检测到预编译的 Linux 二进制文件 ($BINARY_OUTPUT_PATH)，将尝试用于 Docker 构建。"
        # 检查预编译文件是否存在
        if [ ! -f "$BINARY_OUTPUT_PATH" ]; then
            echo "错误：预编译的 Linux 文件 $BINARY_OUTPUT_PATH 未找到！请先执行原生编译。"
            exit 1
        fi
        # 复制到 Docker 上下文的临时位置
        echo "复制 $BINARY_OUTPUT_PATH 到 $PREBUILT_BINARY_TEMP_NAME ..."
        cp "$BINARY_OUTPUT_PATH" "$PREBUILT_BINARY_TEMP_NAME"
        # 添加 Docker 构建参数
        DOCKER_BUILD_ARGS="--build-arg USE_PREBUILT=true"
    else
        echo "未提供预编译的 Linux 二进制文件，Docker 将在内部进行编译。"
    fi

    # --- Docker 构建修复 ---
    # 临时将'common'模块复制到当前目录，以解决Docker构建上下文问题。
    # 这样 'common' 目录就可以在 Dockerfile 中被访问和复制了。
    echo "为 Docker 构建准备：临时复制 '../common' 模块..."
    cp -R ../common ./common

    # 1. 构建 Docker 镜像
    echo "正在构建 Docker 镜像: $FULL_IMAGE_NAME (附加参数: $DOCKER_BUILD_ARGS)..."
    # 注意在命令中加入 $DOCKER_BUILD_ARGS
    # shellcheck disable=SC2086 # 允许 word splitting for build args
    docker build $DOCKER_BUILD_ARGS -t "$FULL_IMAGE_NAME" .
    BUILD_EXIT_CODE=$?

    # --- Docker 构建清理 ---
    # 无论构建成功与否，都删除临时的 'common' 目录
    echo "构建后清理：删除临时的 'common' 模块..."
    rm -rf ./common

    # 清理：无论构建成功与否，如果复制了临时文件，都要删除
    if $USED_PREBUILT_LINUX; then
        echo "清理临时文件 $PREBUILT_BINARY_TEMP_NAME ..."
        rm "$PREBUILT_BINARY_TEMP_NAME"
    fi

    # 检查 Docker 构建结果
    if [ $BUILD_EXIT_CODE -ne 0 ]; then
        echo "Docker 镜像构建失败。"
        exit 1
    fi
    echo "Docker 镜像构建成功: $FULL_IMAGE_NAME"

    # 2. 导出 Docker 镜像为 tar 包
    # 创建输出目录（如果不存在）
    mkdir -p "$OUTPUT_DIR"
    # 处理镜像名中的斜杠，替换为下划线，用于生成文件名
    SANITIZED_IMAGE_BASE_NAME=$(echo "$FINAL_IMAGE_BASE_NAME" | tr '/' '_')
    TAR_FILENAME="${SANITIZED_IMAGE_BASE_NAME}_${IMAGE_TAG}.tar"
    TAR_FILEPATH="$OUTPUT_DIR/$TAR_FILENAME"

    echo "正在导出 Docker 镜像到: $TAR_FILEPATH ..."
    docker save -o "$TAR_FILEPATH" "$FULL_IMAGE_NAME"
    if [ $? -ne 0 ]; then
        echo "Docker 镜像导出失败。"
        exit 1
    fi
    echo "Docker 镜像导出成功: $TAR_FILEPATH"

    # --- 修改文件所有权 ---
    # 检查脚本是否以 root 权限运行 (EUID=0)，因为只有 root 才能 chown
    if [ "$(id -u)" = "0" ]; then
        # 检查是否通过 sudo 运行，并获取原始用户的 UID 和 GID
        # 如果没有通过 sudo (直接用 root 登录)，则 SUDO_UID/SUDO_GID 为空，保留为 root (0)
        ORIGINAL_UID="${SUDO_UID:-0}"
        ORIGINAL_GID="${SUDO_GID:-0}"

        # 只有当原始用户不是 root 时才尝试修改所有权
        # (避免 SUDO_UID/GID 未设置或原始用户就是 root 时，执行 chown 0:0)
        if [ "$ORIGINAL_UID" != "0" ] && [ "$ORIGINAL_GID" != "0" ]; then
            echo "检测到脚本以 root 权限运行，尝试将 $TAR_FILEPATH 的所有权更改回原始用户 (UID:$ORIGINAL_UID, GID:$ORIGINAL_GID)..."
            chown "$ORIGINAL_UID:$ORIGINAL_GID" "$TAR_FILEPATH"
            if [ $? -eq 0 ]; then
                echo "文件所有权已更改。"
            else
                # 如果 chown 失败，给出警告，但不中断脚本
                echo "警告：更改文件所有权失败。文件可能仍然由 root 拥有。"
            fi
        else
             echo "信息：脚本以 root 权限运行，但未检测到有效的原始用户信息 (或原始用户就是 root)，将跳过所有权更改。"
        fi
    fi
    # --- 文件所有权修改结束 ---

    echo "--- Docker 构建与导出完成 ---"
fi

# 最终完成消息
echo "构建脚本执行完毕。"

# 正常退出
exit 0 