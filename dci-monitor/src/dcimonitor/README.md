# DCI Monitor

`dcimonitor` 是一个用于监控数据中心互联 (DCI) 交换机状态和流量的 Go 应用程序。

## 项目结构

```
src/dcimonitor/
├── bin/                     # 编译后的二进制文件 (被 .gitignore 忽略)
├── build.sh                 # 构建脚本 (编译, Docker 构建/导出)
├── cmd/
│   ├── root.go              # Cobra 根命令定义
│   └── server.go            # Cobra 'server' 子命令，启动 API 服务器
├── config/
│   └── config.yaml          # 应用配置文件模板或默认配置
├── docs/                    # Swagger API 文档 (由 swag init 生成)
├── Dockerfile               # 用于构建 Docker 镜像
├── go.mod                   # Go 模块依赖定义
├── go.sum                   # Go 模块依赖校验和
├── internal/                # 项目内部代码 (不应被外部项目导入)
│   ├── alert/               # 告警模块 (handler.go, service.go, dao.go, model.go)
│   ├── device/              # 设备模块 (handler.go, service.go, dao.go, model.go)
│   ├── dbsync/              # 数据库同步模块 (service.go, scheduler.go, mapper.go)
│   ├── kafka/               # Kafka客户端封装 (client.go, producer.go, consumer.go)
│   ├── middleware/          # Gin 中间件 (gin_logger.go, request_id.go)
│   ├── models/              # 跨模块通用模型 (request.go, response.go, error.go)
│   ├── snmp_processor/      # SNMP 数据处理核心
│   ├── task_collaboration/  # 任务协同监控模块 (handler.go, service.go, dao.go, model.go)
│   ├── topology/            # 网络拓扑模块 (handler.go, service.go, dao.go)
│   ├── traffic/             # 流量监控模块 (handler.go, service.go, dao.go)
│   └── utils/               # 通用工具 (timeutil/timeutil.go)
├── logs/                    # 日志文件存储目录 (被 .gitignore 忽略)
├── main.go                  # 程序主入口，调用 cmd 包执行命令
├── sqls/                    # 数据库初始化与迁移脚本
│   ├── README.md
│   ├── alert/               # 告警相关表结构
│   ├── create_constraints.sql
│   ├── create_task_tables.sql
│   ├── init_database.sql
│   ├── task_collaboration/  # 任务协同相关表结构
│   └── topology_lldp_tables.sql
├── tools/                   # 辅助工具脚本
│   └── dciimage_deploy.sh   # 部署脚本示例 (加载并推送 Docker 镜像)
├── .gitignore               # 定义 Git 应忽略的文件和目录
├── README.md                # 项目说明文档 (本文件)
├── dcimonitor-k8s.yaml      # Kubernetes Deployment 和 ConfigMap 示例
└── dcimonitor-service.yaml  # Kubernetes Service 示例 (可选, 可能合并到 k8s.yaml)
```

## 快速开始

1.  **克隆仓库:**
    ```bash
    git clone <your-repository-url>
    cd src/dcimonitor
    ```

2.  **配置:**
    *   复制 `config/config.yaml.example` (如果存在) 为 `config/config.yaml`。
    *   编辑 `config/config.yaml`，至少配置 `server.port` 和 `switches` 列表 (如果需要监控)。对于模拟数据，`switches` 部分可以留空或注释掉。

3.  **数据库初始化 (可选):**
    *   如果需要使用告警、任务协同等功能，需要初始化数据库表结构。
    *   执行 `sqls/init_database.sql` 创建基础表结构。
    *   执行 `sqls/create_task_tables.sql` 创建任务协同相关表。
    *   执行 `sqls/alert/` 目录下的脚本创建告警相关表。

4.  **(可选) 安装 Swag (用于生成 API 文档):**
    ```bash
    go install github.com/swaggo/swag/cmd/swag@latest
    ```

5.  **编译:**
    ```bash
    ./build.sh -p <your_platform> # 例如: ./build.sh -p macos 或 ./build.sh -p linux
    ```
    编译后的二进制文件位于 `./bin/dcimonitor`。

6.  **运行服务:**
    ```bash
    ./bin/dcimonitor --config ./config/config.yaml server
    ```
    服务器将在 `config.yaml` 中指定的端口 (默认为 8080) 上启动。

7.  **访问 API:**
    *   **健康检查:** `curl http://localhost:8080/health`
    *   **交换机状态 (示例):** `curl http://localhost:8080/api/v1/switches/CE1`
    *   **端口数据 (示例):** `curl http://localhost:8080/api/v1/switches/CE1/ports/GE1%2F0%2F1`
    *   **流量图 (示例):** `curl "http://localhost:8080/api/v1/traffic/chart/average?granularity=5m&time_range=1h&a_switch_id=CE1&a_port_id=GE1%2F0%2F1&z_switch_id=CE2&z_port_id=GE1%2F0%2F2"`
    *   **告警列表:** `curl http://localhost:8080/api/v1/alerts`
    *   **任务协同状态:** `curl http://localhost:8080/api/v1/system/task-collaboration/status`
    *   **网络拓扑:** `curl http://localhost:8080/api/v1/topology`
    *   **API 文档:** 打开浏览器访问 `http://localhost:8080/swagger/index.html`

## API 端点

详细的 API 端点说明请参考通过 Swagger UI 提供的在线文档 (`/swagger/index.html`)。

主要端点包括：

*   `GET /health`: 健康检查。
*   `GET /api/v1/switches/{switchID}`: 获取指定交换机的状态。
*   `GET /api/v1/switches/{switchID}/ports/{portID}`: 获取指定端口的数据 (状态/上行/下行流量)。
*   `GET /api/v1/traffic/chart/average`: 获取平均流量图数据。
*   `GET /api/v1/traffic/chart/maximum`: 获取最大流量图数据。
*   `GET /api/v1/traffic/chart/minimum`: 获取最小流量图数据。
*   `GET /api/v1/alerts`: 获取告警列表。
*   `GET /api/v1/alerts/{id}`: 获取指定告警详情。
*   `POST /api/v1/alerts/webhook/prometheus`: 接收Prometheus告警webhook。
*   `GET /api/v1/alert-rules`: 获取告警规则列表。
*   `POST /api/v1/tasks/signals`: 接收任务协同信号。
*   `GET /api/v1/tasks/{taskId}/alerts`: 获取任务相关告警。
*   `GET /api/v1/tasks/{taskId}/alerts/statistics`: 获取任务告警统计。
*   `GET /api/v1/topology`: 获取网络拓扑信息。
*   `GET /api/v1/system/task-collaboration/status`: 获取任务协同系统状态。

### 示例

```bash
# 获取 CE1 状态
curl http://localhost:8080/api/v1/switches/CE1

# 获取告警列表
curl http://localhost:8080/api/v1/alerts?page=1&page_size=20

# 获取任务相关告警
curl http://localhost:8080/api/v1/tasks/0001-112233/alerts

# 获取任务告警统计
curl http://localhost:8080/api/v1/tasks/0001-112233/alerts/statistics

# 获取网络拓扑信息
curl http://localhost:8080/api/v1/topology

# 获取任务协同系统状态
curl http://localhost:8080/api/v1/system/task-collaboration/status
```

# DCI Monitor k8s 集群部署

本文档介绍如何将 `dcimonitor` 服务构建为 Docker 镜像，并将其部署到 Kubernetes 集群。

## 先决条件

*   **Docker:** 用于构建和推送 Docker 镜像。 [安装 Docker](https://docs.docker.com/get-docker/)
*   **kubectl:** Kubernetes 命令行工具。 [安装 kubectl](https://kubernetes.io/docs/tasks/tools/install-kubectl/)
*   **Kubernetes 集群:** 您需要一个可以访问的 Kubernetes 集群（例如 Minikube, kind, GKE, EKS, AKS 或自建集群）。
*   **镜像仓库:** 您需要一个 Docker 镜像仓库（例如 Docker Hub, Google Container Registry (GCR), AWS Elastic Container Registry (ECR), Harbor 等）来存储构建的镜像。确保您有权限推送到该仓库。
*   **Go 环境:** (可选) 如果您需要本地编译或修改代码，需要安装 Go 1.21 或更高版本。

## 步骤

### 1. 构建 Docker 镜像和导出 Tar 包

使用项目根目录下的 `build.sh` 脚本同时编译 Linux 二进制文件、构建 Docker 镜像并导出为 `.tar` 文件。

```bash
cd src/dcimonitor

# 将 <tag> 替换为您的版本标签，例如 0.0.1
./build.sh -p linux -d -t <tag>
# 例如: ./build.sh -p linux -d -t 0.0.1
```

这将：
1.  运行 `swag init` 更新 API 文档。
2.  编译适用于 Linux amd64 的二进制文件到 `bin/dcimonitor`。
3.  使用此二进制文件构建 Docker 镜像 (标签由 `-t` 指定)。
4.  将构建好的镜像导出为 `.tar` 文件到 `bin/` 目录下 (文件名类似 `cr.registry.pcloud.citic.com_dci_dcimonitor_<tag>.tar`)。

### 2. 传输并部署到服务器

1.  **传输 Tar 文件:** 将 `bin/` 目录下生成的 `.tar` 文件复制到您的目标服务器的 `/tmp` 目录下。
2.  **传输部署脚本:** 将 `tools/dciimage_deploy.sh` 脚本复制到目标服务器，并确保其可执行 (`chmod +x dciimage_deploy.sh`)。
3.  **(首次) 登录 Docker 仓库:** 在目标服务器上，登录到您的私有 Docker 仓库 (如果需要认证)：
    ```bash
    docker login cr.registry.pcloud.citic.com
    ```
4.  **执行部署脚本:** 在目标服务器上运行部署脚本。它会自动查找 `/tmp` 下的 tar 文件，移动，加载并推送镜像。
    ```bash
    ./tools/dciimage_deploy.sh
    ```

### 3. 配置 Kubernetes YAML

Kubernetes 相关的配置文件是 `src/dcimonitor/dcimonitor-k8s.yaml`。您需要对其进行编辑：

*   **镜像地址:** 找到 `Deployment` 部分 (`kind: Deployment`)，并将 `spec.template.spec.containers[0].image` 的值修改为您在步骤 1 中使用的镜像地址和标签 (例如 `cr.registry.pcloud.citic.com/dci/dcimonitor:0.0.1`)。
*   **配置内容:** 找到 `ConfigMap` (`kind: ConfigMap`, `name: dcimonitor-config`) 部分，在 `data."config.yaml"` 下粘贴您的 `dcimonitor` 应用程序的实际配置文件 (`config/config.yaml`) 内容。确保至少配置了 `server.port` 和 `switches` (如果需要监控交换机)。
*   **命名空间:** 确保所有资源 (`ConfigMap`, `Deployment`, `Service`) 的 `metadata.namespace` 设置为您期望部署的 Kubernetes 命名空间 (示例中为 `dci`)。

### 4. 部署到 Kubernetes

确保您的 `kubectl` 上下文指向正确的集群和命名空间。

```bash
# 切换到包含 Kubernetes YAML 文件的目录
cd path/to/your/project/src/dcimonitor

# 1. (如果需要) 创建命名空间
kubectl create namespace dci

# 2. 应用配置 (会创建或更新 ConfigMap, Deployment, Service)
kubectl apply -f dcimonitor-k8s.yaml

# (可选) 应用 Service (如果它在单独的文件中)
# kubectl apply -f dcimonitor-service.yaml
```

### 5. 验证部署

检查 Pod 和 Service 的状态：

```bash
# 检查 Pod 状态 (应为 Running)
kubectl get pods -n dci -l app=dcimonitor

# 检查 Service 是否创建成功
kubectl get svc -n dci dcimonitor-service

# 查看 Pod 日志以确认服务启动且无错误
kubectl logs -n dci -l app=dcimonitor

# (可选) 端口转发以本地访问服务
# kubectl port-forward service/dcimonitor-service -n dci 8080:80
# 然后可以通过 http://localhost:8080/health 访问服务的 /health 端点等
```

# API 介绍

## Swag 用法 (生成/更新 API 文档)

```bash
cd src/dcimonitor
go mod tidy # 确保依赖最新
swag init -g cmd/server.go # -g 指定包含通用 API 注释的入口文件
```
