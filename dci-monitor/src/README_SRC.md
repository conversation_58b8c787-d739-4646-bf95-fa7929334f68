# DCI监控系统源代码说明文档

## 项目概述

DCI监控系统是DCI整体解决方案的一部分，与多云管理系统和网络自动化控制系统协同工作。该系统采用分布式Agent+服务端架构，主要用于数据中心基础设施的监控、数据收集和分析。

## 技术栈

- **编程语言**：Go
- **消息队列**：Kafka
- **时序数据库**：Prometheus/Thanos (替代了原计划的TDengine)
- **搜索引擎**：Elasticsearch
- **关系型数据库**：MySQL
- **数据采集**：Telegraf

## 项目结构

```
src/
├── common/                  # 公共代码库
│   └── logger/              # 日志组件（供dciagent和dcimonitor共用）
├── dciagent/                # Agent端代码
│   ├── cmd/                 # 命令行工具
│   ├── configs/             # 配置文件模板
│   └── internal/            # 内部实现
│       └── telegraf/        # Telegraf管理相关
├── dcimonitor/              # 监控服务端代码
│   ├── cmd/                 # 命令行工具
│   │   ├── kafka/           # Kafka相关命令
│   │   └── topology/        # 拓扑相关命令
│   ├── config/              # 配置文件
│   ├── internal/            # 内部实现
│   │   ├── kafka/           # Kafka客户端
│   │   ├── middleware/      # 中间件
│   │   ├── models/          # 数据模型
│   │   ├── monitors/        # 监控实现
│   │   │   └── topology/    # 拓扑监控
│   │   ├── services/        # 业务服务
│   │   └── utils/           # 工具函数
│   └── pkg/                 # 可重用包
│       └── cronjobs/        # 定时任务
└── TDengine_sql/            # 数据库脚本
```

## 核心组件说明

### 1. 公共模块 (common)

#### 1.1 日志组件 (logger)

- **文件**：`common/logger/logger.go`, `common/logger/adapter.go`, `common/logger/logger_test.go`
- **功能**：提供统一的日志记录功能，基于zap实现，支持日志级别、文件轮转、多格式输出等
- **特点**：
  - 支持按日志级别分文件存储
  - 支持日志文件自动轮转和压缩
  - 提供兼容接口，方便不同组件集成

### 2. Agent端 (dciagent)

#### 2.1 主程序

- **文件**：`dciagent/main.go`
- **功能**：Agent端入口程序，负责初始化和启动各组件

#### 2.2 命令行工具

- **文件**：`dciagent/cmd/*.go`
- **功能**：提供命令行接口，用于管理Agent

#### 2.3 Telegraf管理

- **目录**：`dciagent/internal/telegraf/`
- **功能**：
  - 安装、配置、启动、停止Telegraf服务
  - 管理Telegraf配置文件
  - 注册Agent到监控服务端
  - 备份和恢复配置

### 3. 监控服务端 (dcimonitor)

#### 3.1 主程序

- **文件**：`dcimonitor/main.go`
- **功能**：服务端入口程序，初始化各组件并启动HTTP服务

#### 3.2 Kafka客户端

- **目录**：`dcimonitor/internal/kafka/`
- **功能**：
  - 连接Kafka集群
  - 生产和消费消息
  - 管理主题
  - TLS安全连接

#### 3.3 拓扑监控

- **目录**：`dcimonitor/internal/monitors/topology/`
- **文件**：
  - `topology_processor.go`: 处理拓扑数据
  - `topology_builder.go`: 构建网络拓扑
  - `lldp_processor.go`: 处理LLDP数据
- **功能**：收集和分析网络设备LLDP信息，构建网络拓扑图

#### 3.4 交换机监控

- **文件**：`dcimonitor/internal/monitors/switch_monitor.go`
- **功能**：监控交换机状态、性能和配置变更

#### 3.5 流量监控

- **文件**：`dcimonitor/internal/monitors/traffic_monitor.go`
- **功能**：监控网络流量，分析流量模式和异常

#### 3.6 服务层

- **目录**：`dcimonitor/internal/services/`
- **文件**：
  - `identifier_service.go`: 设备标识服务
  - `switch_service.go`: 交换机管理服务
- **功能**：提供业务逻辑实现，处理设备管理和数据分析

#### 3.7 数据模型

- **目录**：`dcimonitor/internal/models/`
- **文件**：
  - `switch.go`: 交换机数据模型
  - `lldp.go`: LLDP数据模型
  - `error.go`: 错误定义
  - `response.go`: API响应结构
- **功能**：定义系统中使用的数据结构

#### 3.8 定时任务

- **文件**：`dcimonitor/pkg/cronjobs/jobs.go`
- **功能**：实现系统定时任务，如数据清理、报告生成等

## 数据流

1. **数据采集**：Telegraf采集设备数据
2. **数据传输**：通过Kafka消息队列传输数据
3. **数据处理**：dcimonitor服务处理和分析数据
4. **数据存储**：
   - 时序数据存储在Prometheus/Thanos
   - 结构化数据存储在MySQL
   - 日志和事件存储在Elasticsearch
5. **数据展示**：通过API提供数据查询和可视化接口

## 开发进展

### 已完成功能

1. 基础架构搭建
2. 日志组件实现
3. Kafka客户端开发
4. Telegraf管理功能
5. 拓扑数据处理
6. 设备标识服务

## 集成指南

### 添加新监控项

1. 在`dcimonitor/internal/monitors/`下创建新的监控模块
2. 在`dcimonitor/cmd/`下添加相应的命令行接口
3. 在`dcimonitor/internal/models/`中定义数据模型
4. 在`dcimonitor/internal/services/`中实现业务逻辑
5. 在`dciagent/configs/templates/`中添加Telegraf配置模板

### 添加新API

1. 在`dcimonitor/internal/services/`中实现业务逻辑
2. 在`dcimonitor/cmd/server.go`中注册路由
3. 更新Swagger文档

## 配置说明

### Agent配置

- Telegraf配置位于`dciagent/configs/templates/`
- 支持通过命令行参数或配置文件进行配置

### 服务端配置

- 主配置文件：`dcimonitor/config/config.yaml`
- Kafka配置：`dcimonitor/config/kafka.yaml`
- 日志配置通过主配置文件的logger部分设置

## 部署说明

系统支持以下部署方式：

1. **容器化部署**：
   - Dockerfile位于`dcimonitor/Dockerfile`
   - K8s配置位于`dcimonitor/dcimonitor-k8s.yaml`

2. **二进制部署**：
   - 使用`build.sh`脚本构建二进制文件
   - 二进制文件位于`bin/`目录

## 测试

- 单元测试位于各模块的`*_test.go`文件中
- 集成测试位于`dcimonitor/internal/tests/`目录

## 注意事项

1. 确保Kafka和数据库配置正确
2. Agent需要适当权限来管理Telegraf服务
3. 日志目录需要写入权限
