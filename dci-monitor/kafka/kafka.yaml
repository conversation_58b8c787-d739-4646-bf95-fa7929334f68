---
# Kafka ConfigMap (KRaft Mode) - Minimal base config
apiVersion: v1
kind: ConfigMap
metadata:
  name: kafka-conf
  namespace: dci
data:
  # A very minimal base template that will be used to generate
  # the actual server-{ordinal}.properties files
  server-template.properties: |
    # Common settings for all nodes
    # Critical KRaft settings like node.id will be added dynamically
    process.roles=broker,controller
    
    # Network settings - UPDATED FOR SECURITY
    listener.security.protocol.map=INTERNAL:SSL,EXTERNAL:SASL_SSL,CONTROLLER:SSL
    inter.broker.listener.name=INTERNAL

    # SASL & ACL Configuration
    sasl.enabled.mechanisms=PLAIN
    sasl.mechanism.inter.broker.protocol=PLAIN
    # authorizer.class.name=org.apache.kafka.server.authorizer.StandardAuthorizer
    # super.users=User:kafka-admin

    # TLS Configuration (as per security design doc)
    ssl.keystore.type=PEM
    ssl.keystore.location=/etc/kafka/tls/kafka.server.keystore.pem
    # ssl.key.password= # Key is not password-protected
    ssl.truststore.location=/etc/kafka/tls/intermediate-ca.crt
    ssl.truststore.type=PEM
    ssl.client.auth=required
    
    # 允许所有主机访问
    # 不限制访问IP
    socket.send.buffer.bytes=102400
    socket.receive.buffer.bytes=102400
    socket.request.max.bytes=104857600
    
    # Topic自动创建和选举设置
    auto.create.topics.enable=true
    auto.leader.rebalance.enable=true
    leader.imbalance.check.interval.seconds=30
    
    # 连接超时和网络设置
    connections.max.idle.ms=600000
    socket.connection.setup.timeout.ms=100000
    socket.connection.setup.timeout.max.ms=300000
    socket.listen.backlog.size=1000
    
    # 客户端连接重试设置 - 增加重试间隔和次数，提高稳定性
    reconnect.backoff.ms=5000
    reconnect.backoff.max.ms=30000
    retry.backoff.ms=5000
    request.timeout.ms=60000
    # 增加重试次数，防止临时网络问题导致连接失败
    message.send.max.retries=10
    retries=10
    delivery.timeout.ms=300000
    # 添加max.poll.interval.ms避免消费者被踢出消费组
    max.poll.interval.ms=300000
    
    # 客户端额外设置
    metadata.max.age.ms=10000
    max.poll.records=500
    fetch.max.wait.ms=500
    
    # 网络处理线程增加
    num.network.threads=8
    num.io.threads=16
    
    # Storage settings
    log.dirs=/bitnami/kafka/data
    # 自动清理掉超过 7 天的旧数据
    log.retention.hours=168
    
    # 3节点集群配置
    num.partitions=3
    default.replication.factor=3
    min.insync.replicas=2
    offsets.topic.replication.factor=3
    transaction.state.log.replication.factor=3
    transaction.state.log.min.isr=2
    
    # KRaft集群稳定性配置
    controller.quorum.election.timeout.ms=5000
    controller.quorum.fetch.timeout.ms=3000
    controller.quorum.append.linger.ms=25
    replica.lag.time.max.ms=30000
    replica.fetch.max.bytes=10485760
    replica.fetch.response.max.bytes=10485760

    # General performance settings
    num.recovery.threads.per.data.dir=1
    log.segment.bytes=1073741824
    log.retention.check.interval.ms=300000
    group.initial.rebalance.delay.ms=0

---
# Kafka StatefulSet with direct KRaft config
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: kafka
  namespace: dci
spec:
  serviceName: kafka-headless
  replicas: 3
  podManagementPolicy: Parallel
  selector:
    matchLabels:
      app: kafka
  template:
    metadata:
      labels:
        app: kafka
    spec:
      # Add dnsConfig to force correct DNS settings within the Pod
      dnsPolicy: ClusterFirst # Default, but make explicit
      dnsConfig:
        options:
          - name: ndots
            value: "5"
          - name: single-request-reopen
      securityContext:
        fsGroup: 1001
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - kafka
              topologyKey: "kubernetes.io/hostname"
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: kubernetes.io/arch
                operator: In
                values:
                - amd64
                - arm64
      imagePullSecrets:
        - name: dci-images-key
      containers:
      - name: kafka
        image: cr.registry.pcloud.citic.com/dci/kafka:4.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - name: replication
          containerPort: 9092
        - name: internal-client
          containerPort: 30002
        - name: external-client
          containerPort: 30003
        - name: controller
          containerPort: 9093
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: KAFKA_HEAP_OPTS
          value: "-Xms1g -Xmx1g -XX:+UseG1GC -XX:MaxGCPauseMillis=20 -XX:InitiatingHeapOccupancyPercent=35"
        - name: KAFKA_OPTS
          value: "-Djava.security.auth.login.config=/etc/kafka/tls/kafka_server_jaas.conf"
        - name: BITNAMI_DEBUG
          value: "true"
        - name: KAFKA_KRAFT_CLUSTER_ID
          value: "Dc1Mon1torKraftCluster1dABC"
        command:
        - /bin/bash
        - -xec
        args:
        - |
          # Extract ordinal from hostname
          ORDINAL="${HOSTNAME##*-}"
          echo "Pod ordinal: $ORDINAL"
          
          # Define paths
          DATA_DIR="/bitnami/kafka/data"
          CONFIG_DIR="/opt/bitnami/kafka/config"
          FINAL_CONFIG="${CONFIG_DIR}/server-${ORDINAL}.properties"
          
          echo "Preparing data and config directories..."
          
          # Create data directory WITHOUT changing ownership
          mkdir -p "$DATA_DIR"
          # Remove chown commands completely - rely on fsGroup
          # chmod also not needed, fsGroup should handle this
          
          # 确保DNS解析服务正常
          echo "Checking DNS resolution..."
          for i in {1..5}; do
            if getent hosts kafka-0.kafka-headless.${POD_NAMESPACE}.svc.cluster.local >/dev/null 2>&1; then
              echo "DNS resolution successful."
              break
            fi
            echo "Waiting for DNS to be ready (attempt $i/5)..."
            sleep 4
          done
          
          # 验证广播地址格式的函数
          validate_advertised_address() {
            local addr="$1"
            echo "验证广播地址: $addr" >&2
            
            # 检查格式是否为 host:port
            if [[ ! "$addr" =~ ^[^:]+:[0-9]+$ ]]; then
              echo "错误: 广播地址格式不正确 '$addr', 应为 'host:port'" >&2
              return 1
            fi
            
            # 提取主机和端口
            local host="${addr%%:*}"
            local port="${addr##*:}"
            
            # 验证端口范围
            if [[ "$port" -lt 30010 || "$port" -gt 30012 ]]; then
              echo "警告: 端口 '$port' 不在预期范围 (30010-30012)" >&2
            fi
            
            # 检查主机名是否可解析
            if [[ "$host" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
              # IP地址格式，无需DNS解析
              echo "主机部分是IP地址，跳过DNS解析" >&2
            else
              # 尝试解析主机名
              echo "尝试解析主机名: $host" >&2
              if ! getent hosts "$host" >/dev/null 2>&1; then
                echo "警告: 无法解析主机名 '$host'" >&2
              else
                echo "主机名 '$host' 解析成功" >&2
              fi
            fi
            
            return 0
          }
          
          # 执行广播地址可达性验证和一致性检查
          startup_checks() {
            local advertised_addr="$1"
            echo "执行启动前检查..." >&2
            
            # 验证广播地址
            echo "验证广播地址可达性..." >&2
            local host="${advertised_addr%%:*}"
            local port="${advertised_addr##*:}"
            
            # 检查广播地址端口和Pod序号关系
            local expected_port=$((30010 + ORDINAL_NUM))
            if [[ "$port" != "$expected_port" ]]; then
              echo "警告: 广播地址端口 '$port' 与Pod序号 '$ORDINAL_NUM' 不匹配，预期端口: $expected_port" >&2
            fi
            
            # 尝试TCP连接测试可达性（超时5秒）
            if command -v nc &>/dev/null; then
              echo "测试广播地址TCP可达性: $host:$port..." >&2
              if timeout 5 nc -z "$host" "$port" >/dev/null 2>&1; then
                echo "成功: 广播地址 $host:$port 可访问" >&2
              else
                echo "警告: 广播地址 $host:$port 不可访问" >&2
              fi
            else
              echo "未找到nc命令，跳过TCP可达性测试" >&2
            fi
            
            # 记录生成的配置摘要，用于后续一致性检查
            echo "KAFKA_ADVERTISED_ADDRESS=$advertised_addr" > "/tmp/kafka_config_${ORDINAL}.info"
            echo "KAFKA_NODE_ID=$ORDINAL" >> "/tmp/kafka_config_${ORDINAL}.info"
            echo "KAFKA_EXTERNAL_PORT=$port" >> "/tmp/kafka_config_${ORDINAL}.info"
            
            return 0
          }
          
          # Generate the final config file from template
          cp "${CONFIG_DIR}/server-template.properties" "${FINAL_CONFIG}"
          
          # Add node.id - the most critical parameter
          echo "node.id=${ORDINAL}" >> "${FINAL_CONFIG}"
          
          # 设置监听器，区分Broker间、内部客户端、外部客户端和控制器
          echo "listener.security.protocol.map=REPLICATION:SSL,INTERNAL_CLIENT:SASL_SSL,EXTERNAL_CLIENT:SASL_SSL,CONTROLLER:SSL" >> "${FINAL_CONFIG}"
          echo "listeners=REPLICATION://0.0.0.0:9092,INTERNAL_CLIENT://0.0.0.0:30002,EXTERNAL_CLIENT://0.0.0.0:30003,CONTROLLER://0.0.0.0:9093" >> "${FINAL_CONFIG}"
          
          # 动态获取外部广播地址的函数
          get_external_advertised_address() {
              local BASE_PORT=30010
              local ORDINAL_NUM=$(echo ${ORDINAL} | sed 's/[^0-9]//g')
              local EXTERNAL_NODE_PORT=$((BASE_PORT + ORDINAL_NUM))
              
              echo "dcikafka.intra.citic-x.com:${EXTERNAL_NODE_PORT}"
              return 0
          }
          
          # 获取动态广播地址
          EXTERNAL_ADVERTISED_ADDRESS=$(get_external_advertised_address)
          INTERNAL_ADVERTISED_ADDRESS_HOST="${HOSTNAME}.kafka-headless.${POD_NAMESPACE}.svc.cluster.local"

          echo "Using External Advertised Address: ${EXTERNAL_ADVERTISED_ADDRESS}" >&2
          echo "Using Internal Advertised Address Host: ${INTERNAL_ADVERTISED_ADDRESS_HOST}" >&2

          # 设置动态的广播地址 - 为每个监听器指定不同的广播地址
          echo "advertised.listeners=REPLICATION://${INTERNAL_ADVERTISED_ADDRESS_HOST}:9092,INTERNAL_CLIENT://${INTERNAL_ADVERTISED_ADDRESS_HOST}:30002,EXTERNAL_CLIENT://${EXTERNAL_ADVERTISED_ADDRESS},CONTROLLER://${INTERNAL_ADVERTISED_ADDRESS_HOST}:9093" >> "${FINAL_CONFIG}"
          
          echo "inter.broker.listener.name=REPLICATION" >> "${FINAL_CONFIG}"
          echo "controller.listener.names=CONTROLLER" >> "${FINAL_CONFIG}"
          
          # 添加集群ID
          echo "kafka.cluster.id=DciKfkCluster00001" >> "${FINAL_CONFIG}"
          
          # 3节点KRaft集群配置 - 使用DNS名称，保证可移植性
          VOTERS="<EMAIL>-headless.${POD_NAMESPACE}.svc.cluster.local:9093,<EMAIL>-headless.${POD_NAMESPACE}.svc.cluster.local:9093,<EMAIL>-headless.${POD_NAMESPACE}.svc.cluster.local:9093"
          echo "controller.quorum.voters=${VOTERS}" >> "${FINAL_CONFIG}"
          
          # 添加更多连接稳定性配置
          echo "num.partitions=3" >> "${FINAL_CONFIG}"
          echo "default.replication.factor=3" >> "${FINAL_CONFIG}"
          echo "min.insync.replicas=2" >> "${FINAL_CONFIG}"
          echo "replica.lag.time.max.ms=30000" >> "${FINAL_CONFIG}"
          
          # Verify the final configuration
          echo "=== FINAL KAFKA CONFIGURATION ==="
          cat "${FINAL_CONFIG}"
          echo "================================="
          
          # 输出关键变量用于调试
          echo "===== DEBUG INFO ====="
          echo "HOSTNAME: ${HOSTNAME}"
          echo "NODE_NAME: ${NODE_NAME}"
          echo "ADVERTISED_ADDRESS: ${EXTERNAL_ADVERTISED_ADDRESS}"
          echo "VOTERS: ${VOTERS}"
          echo "======================"
          
          # Check if storage needs formatting (should run only on first start)
          if [ ! -f "$DATA_DIR/meta.properties" ]; then
            echo "KRaft storage needs formatting. Running kafka-storage.sh format..."
            # Format the storage with proper cluster ID
            KAFKA_CLUSTER_ID="${KAFKA_KRAFT_CLUSTER_ID:-$(kafka-storage.sh random-uuid)}"
            echo "Using Cluster ID: $KAFKA_CLUSTER_ID"
            
            # Run the format command, explicitly passing the config file
            kafka-storage.sh format -t "$KAFKA_CLUSTER_ID" -c "${FINAL_CONFIG}"
            
            # Check result of format command
            if [ $? -ne 0 ]; then
              echo "ERROR: Failed to format KRaft storage. Exiting."
              exit 1
            fi
            
            echo "KRaft storage successfully formatted."
          else
            echo "KRaft storage already formatted, skipping format step."
          fi
          
          # 初始化默认Topic - 仅在kafka-0上执行
          if [ "${HOSTNAME}" = "kafka-0" ]; then
            echo "This is the first pod, creating initial topics after server starts..."
            
            # 简化脚本创建方式，避免here-document问题
            echo '#!/bin/bash' > /tmp/init_topics.sh
            echo '' >> /tmp/init_topics.sh
            echo '# 等待Kafka集群完全启动' >> /tmp/init_topics.sh
            echo 'echo "Waiting for Kafka cluster to be ready..."' >> /tmp/init_topics.sh
            echo 'sleep 60' >> /tmp/init_topics.sh
            echo '' >> /tmp/init_topics.sh
            echo '# 检查集群是否就绪' >> /tmp/init_topics.sh
            echo 'READY=false' >> /tmp/init_topics.sh
            echo 'MAX_ATTEMPTS=12' >> /tmp/init_topics.sh
            echo 'ATTEMPT=0' >> /tmp/init_topics.sh
            echo 'while [[ $READY == "false" && $ATTEMPT -lt $MAX_ATTEMPTS ]]; do' >> /tmp/init_topics.sh
            echo '  ((ATTEMPT++))' >> /tmp/init_topics.sh
            echo '  if kafka-topics.sh --bootstrap-server localhost:9092 --list >/dev/null 2>&1; then' >> /tmp/init_topics.sh
            echo '    echo "Kafka cluster is ready"' >> /tmp/init_topics.sh
            echo '    READY=true' >> /tmp/init_topics.sh
            echo '  else' >> /tmp/init_topics.sh
            echo '    echo "Waiting for Kafka cluster (attempt $ATTEMPT/$MAX_ATTEMPTS)..."' >> /tmp/init_topics.sh
            echo '    sleep 30' >> /tmp/init_topics.sh
            echo '  fi' >> /tmp/init_topics.sh
            echo 'done' >> /tmp/init_topics.sh
            echo '' >> /tmp/init_topics.sh
            echo '# 定义Topic创建函数，包含重试逻辑' >> /tmp/init_topics.sh
            echo 'function create_topic() {' >> /tmp/init_topics.sh
            echo '  local topic_name=$1' >> /tmp/init_topics.sh
            echo '  local partitions=$2' >> /tmp/init_topics.sh
            echo '  local max_attempts=3' >> /tmp/init_topics.sh
            echo '  local attempt=1' >> /tmp/init_topics.sh
            echo '  local success=false' >> /tmp/init_topics.sh
            echo '' >> /tmp/init_topics.sh
            echo '  while [[ $attempt -le $max_attempts && $success == "false" ]]; do' >> /tmp/init_topics.sh
            echo '    echo "尝试创建Topic: $topic_name (尝试 $attempt/$max_attempts)"' >> /tmp/init_topics.sh
            
            # 使用本地地址创建topic，避免依赖外部地址
            echo '    # 使用本地地址创建Topic，确保命令可靠执行' >> /tmp/init_topics.sh
            echo '    if kafka-topics.sh --bootstrap-server localhost:9092 --create --if-not-exists --topic "$topic_name" --partitions "$partitions" --replication-factor 3; then' >> /tmp/init_topics.sh
            echo '      echo "成功创建Topic: $topic_name"' >> /tmp/init_topics.sh
            echo '      success=true' >> /tmp/init_topics.sh
            echo '    else' >> /tmp/init_topics.sh
            echo '      echo "创建Topic失败，将重试... (尝试 $attempt/$max_attempts)"' >> /tmp/init_topics.sh
            echo '      attempt=$((attempt + 1))' >> /tmp/init_topics.sh
            echo '      sleep 5' >> /tmp/init_topics.sh
            echo '    fi' >> /tmp/init_topics.sh
            echo '  done' >> /tmp/init_topics.sh
            echo '' >> /tmp/init_topics.sh
            echo '  if [[ $success == "false" ]]; then' >> /tmp/init_topics.sh
            echo '    echo "警告：无法创建Topic: $topic_name，已达到最大重试次数"' >> /tmp/init_topics.sh
            echo '    return 1' >> /tmp/init_topics.sh
            echo '  fi' >> /tmp/init_topics.sh
            echo '  return 0' >> /tmp/init_topics.sh
            echo '}' >> /tmp/init_topics.sh
            echo '' >> /tmp/init_topics.sh
            echo '# 创建测试Topic' >> /tmp/init_topics.sh
            echo 'echo "Creating test topics..."' >> /tmp/init_topics.sh
            echo 'create_topic "test" 3' >> /tmp/init_topics.sh
            echo '' >> /tmp/init_topics.sh
            echo '# 创建DCIMonitor所需的Topics' >> /tmp/init_topics.sh
            echo 'TOPICS=(' >> /tmp/init_topics.sh
            echo '  "dci.monitor.v1.defaultchannel.topology.lldp"' >> /tmp/init_topics.sh
            echo '  "dci.monitor.v1.defaultchannel.metrics.telegraf"' >> /tmp/init_topics.sh
            echo '  "dci.monitor.v1.defaultchannel.logs.syslog"' >> /tmp/init_topics.sh
            echo '  "dci.monitor.v1.defaultchannel.tasks.control"' >> /tmp/init_topics.sh
            echo ')' >> /tmp/init_topics.sh
            echo '' >> /tmp/init_topics.sh
            echo 'FAILED_TOPICS=()' >> /tmp/init_topics.sh
            echo 'for topic in "${TOPICS[@]}"; do' >> /tmp/init_topics.sh
            echo '  echo "Creating topic: $topic"' >> /tmp/init_topics.sh
            echo '  if ! create_topic "$topic" 3; then' >> /tmp/init_topics.sh
            echo '    FAILED_TOPICS+=("$topic")' >> /tmp/init_topics.sh
            echo '  fi' >> /tmp/init_topics.sh
            echo 'done' >> /tmp/init_topics.sh
            echo '' >> /tmp/init_topics.sh
            echo '# 列出所有创建的Topics' >> /tmp/init_topics.sh
            echo 'echo "Listing all topics:"' >> /tmp/init_topics.sh
            echo 'kafka-topics.sh --bootstrap-server localhost:9092 --list' >> /tmp/init_topics.sh
            echo '' >> /tmp/init_topics.sh
            echo '# 报告结果' >> /tmp/init_topics.sh
            echo 'if [[ ${#FAILED_TOPICS[@]} -eq 0 ]]; then' >> /tmp/init_topics.sh
            echo '  echo "Topic initialization completed successfully"' >> /tmp/init_topics.sh
            echo 'else' >> /tmp/init_topics.sh
            echo '  echo "WARNING: Failed to create some topics: ${FAILED_TOPICS[*]}"' >> /tmp/init_topics.sh
            echo 'fi' >> /tmp/init_topics.sh
            
            chmod +x /tmp/init_topics.sh
            nohup /tmp/init_topics.sh > /tmp/init_topics.log 2>&1 &
            echo "Topic initialization script started in background"
          fi
          
          # Start Kafka with the specific config file
          echo "Starting Kafka with config: ${FINAL_CONFIG}"
          
          # 单节点不需要重试逻辑，直接启动
          exec kafka-server-start.sh "${FINAL_CONFIG}"
        
        resources:
          requests:
            memory: "1Gi" 
            cpu: "110m"
          limits:
            memory: "2Gi"  # 减小内存限制
            cpu: "1"       # 减小CPU限制
        readinessProbe:
          tcpSocket:
            port: 9092
          initialDelaySeconds: 120
          timeoutSeconds: 15
          periodSeconds: 30
          failureThreshold: 6
        livenessProbe:
          tcpSocket:
            port: 9092
          initialDelaySeconds: 300
          timeoutSeconds: 20
          periodSeconds: 60
          failureThreshold: 8
        volumeMounts:
        - name: kafka-data
          mountPath: /bitnami/kafka
        - name: config-volume
          mountPath: /opt/bitnami/kafka/config/server-template.properties
          subPath: server-template.properties
        - name: kafka-tls-volume
          mountPath: /etc/kafka/tls
          readOnly: true
      volumes:
      - name: config-volume
        configMap:
          name: kafka-conf
      - name: kafka-tls-volume
        secret:
          secretName: kafka-tls-secret
  volumeClaimTemplates:
  - metadata:
      name: kafka-data
      annotations:
        volume.beta.kubernetes.io/storage-class: "dci-nfs-storage" 
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: dci-nfs-storage
      resources:
        requests:
          storage: 10Gi # Adjust storage size as needed
