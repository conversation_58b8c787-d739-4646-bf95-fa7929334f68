# DCI Kafka 安全证书

**警告: 此目录及其所有内容均为自动生成，请勿手动修改！**

此目录包含由 `generate_certs.sh` 脚本为 DCI Kafka 集群生成的全部安全证书、私钥和配置文件。
如果需要进行任何变更，请修改该脚本后重新运行，而不是直接编辑此目录下的文件。

## 何时需要重新生成?

在以下场景下，您必须重新运行 `generate_certs.sh` 脚本以生成一套全新的证书：

1.  **证书即将过期**: 所有证书均有有效期 (服务器/客户端证书当前设置为1800天)。在过期前，需要重新生成并部署。
2.  **私钥泄露**: 如果怀疑任何私钥 (尤其是 `root-ca.key` 或 `intermediate-ca.key`) 已被泄露，必须立即重新生成。
3.  **服务地址变更**: 如果 Kafka Broker 的服务地址或 Kubernetes SAN (Subject Alternative Names) 发生变化，需要更新服务器证书。
4.  **客户端变更**: 当需要为新的客户端服务授权，或移除现有客户端时，需要更新客户端证书和JAAS配置。

## 如何重新生成?

1.  返回上一级目录 (`cd ..`)。
2.  直接运行 `./generate_certs.sh`。
3.  脚本会自动删除当前的 `./dci-kafka-certs` 目录，并创建一个全新的。
4.  进入新生成的目录，并执行 `./apply_secrets.sh` 将新证书应用到 Kubernetes。
5.  **重要**: 应用Secret后，需要滚动重启相关的Pod (如Kafka, flowdata等) 来加载新的证书。

