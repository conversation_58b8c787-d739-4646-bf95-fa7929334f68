KafkaServer {
    org.apache.kafka.common.security.plain.PlainLoginModule required
    user_kafka-admin="kafka-admin-secret"
    user_dci-telegraf="telegraf-secret"
    user_dci-flowdata="flowdata-secret"
    user_user_test_service="fO5A9cM8xPGxtyxsfloQgw==i"
    user_user_test_service_2="YpjXcH3hg4Qts4UTXJ0MLg=="
    user_telegraf_clinet_1="2iPs+mm67xH6pInz3Q4FwA=="
    user_telegraf_clinet_3="lu3KLS9mvWBJ6Y1kNDGPAA==";
};

KafkaClient {
    org.apache.kafka.common.security.plain.PlainLoginModule required
    username="kafka-admin"
    password="kafka-admin-secret"
    user_user_test_service_2="YpjXcH3hg4Qts4UTXJ0MLg=="
    user_telegraf_clinet_1="2iPs+mm67xH6pInz3Q4FwA=="
    user_telegraf_clinet_3="lu3KLS9mvWBJ6Y1kNDGPAA==";
};
