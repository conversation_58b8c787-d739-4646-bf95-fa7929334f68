#!/bin/bash
#
# DCI Kafka 新客户端证书签发脚本
#
# 使用方法: ./issue_new_client_cert.sh <new-client-name>
#
# 本脚本用于在现有的CA体系下，为新的客户端(生产者/消费者)快速签发证书和生成凭证，
# 而无需重新生成所有证书，从而实现对集群的无中断更新。
#

set -e
set -o pipefail

# --- 配置 ---
CERTS_DIR="./dci-kafka-certs"
LEAF_CERT_DAYS=1800 # 默认有效期
CLIENT_SUBJ_PREFIX="/C=CN/O=CITIC/OU=DCI/CN="

# --- 辅助函数 ---
log_info() { echo -e "\033[0;32m[INFO]\033[0m $1"; }
log_warn() { echo -e "\033[0;33m[WARN]\033[0m $1"; }
log_error() { echo -e "\033[0;31m[ERROR]\033[0m $1" >&2; }

show_usage() {
  echo "用法: ./issue_new_client_cert.sh [选项] <client-name>"
  echo "例如: ./issue_new_client_cert.sh --days 365 my-new-service"
  echo
  echo "选项:"
  echo "  --days DAYS    设置客户端证书的有效期 (默认: ${LEAF_CERT_DAYS})"
  echo "  -h, --help     显示此帮助信息并退出。"
  echo
  echo "此脚本将在 '${CERTS_DIR}' 目录中为新客户端生成证书、密钥和密码,"
  echo "并更新 'kafka_server_jaas.conf' 和 'kafka_credentials.txt'。"
  echo "您必须在 'generate_certs.sh' 脚本所在的目录中运行此脚本。"
}

# --- 参数解析 ---
# 循环解析选项，直到只剩下非选项参数(客户端名称)
CLIENT_NAME=""
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --days)
            LEAF_CERT_DAYS="$2"
            shift # 消耗参数值
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            # 如果不是一个选项，假定它是客户端名称
            if [ -z "$CLIENT_NAME" ]; then
                CLIENT_NAME="$1"
            else
                log_error "错误: 提供了多个客户端名称 ('${CLIENT_NAME}' 和 '$1') 或未知参数 '$1'。"
                show_usage
                exit 1
            fi
            ;;
    esac
    shift # 消耗选项名或客户端名
done


# --- 环境检查 ---
if [ -z "$CLIENT_NAME" ]; then
    log_error "错误: 未提供客户端名称。"
    show_usage
    exit 1
fi

if [ ! -d "$CERTS_DIR" ]; then
    log_error "证书目录 '${CERTS_DIR}' 未找到。"
    log_error "请首先运行 './generate_certs.sh' 来创建CA和初始证书体系。"
    exit 1
fi

if [ ! -f "${CERTS_DIR}/intermediate-ca.key" ]; then
    log_error "中间CA私钥 '${CERTS_DIR}/intermediate-ca.key' 未找到。"
    log_error "证书目录不完整，请考虑重新运行 './generate_certs.sh'。"
    exit 1
fi

cd "${CERTS_DIR}"

if [ -f "${CLIENT_NAME}.client.key" ]; then
    log_error "客户端 '${CLIENT_NAME}' 的证书已经存在于 '$(pwd)'。"
    log_error "请选择一个不同的客户端名称。"
    exit 1
fi

log_info "开始为新客户端 '${CLIENT_NAME}' 签发证书..."

# --- 1. 生成密钥和证书 ---
log_info "步骤 1/4: 生成客户端私钥和证书签名请求 (CSR)..."
openssl genpkey -algorithm RSA -pkeyopt rsa_keygen_bits:2048 -out "${CLIENT_NAME}.client.key"
echo "--> 转换为 PKCS#8 格式..."
mv "${CLIENT_NAME}.client.key" "${CLIENT_NAME}.client.key.original"
openssl pkcs8 -topk8 -inform PEM -in "${CLIENT_NAME}.client.key.original" -outform PEM -nocrypt -out "${CLIENT_NAME}.client.key"
rm "${CLIENT_NAME}.client.key.original"

openssl req -new -key "${CLIENT_NAME}.client.key" -out "${CLIENT_NAME}.client.csr" -subj "${CLIENT_SUBJ_PREFIX}${CLIENT_NAME}"

log_info "步骤 2/4: 使用中间CA签发客户端证书..."
openssl x509 -req -in "${CLIENT_NAME}.client.csr" -CA intermediate-ca.crt -CAkey intermediate-ca.key -CAcreateserial -out "${CLIENT_NAME}.client.crt" -days ${LEAF_CERT_DAYS}
rm "${CLIENT_NAME}.client.csr" # 清理CSR文件
log_info "客户端 '${CLIENT_NAME}' 证书创建成功: ${CLIENT_NAME}.client.crt"

# --- 2. 生成密码并更新JAAS配置 ---
log_info "步骤 3/4: 生成SASL密码并更新JAAS配置文件..."
PASSWORD=$(openssl rand -base64 16)

# --- 更为稳健的JAAS更新逻辑（两步法） ---
# 步骤 A: 移除当前最后一个 user_... 行末尾的分号。
#         这使得添加新用户成为一个独立的操作。
sed -i.bak 's/\(user_.*"\);/\1/' kafka_server_jaas.conf
rm kafka_server_jaas.conf.bak

# 步骤 B: 在 KafkaServer 配置块的 `};` 之前，插入新的用户行（带分号）。
#         这确保了新用户总是作为新的最后一行被正确添加。
sed -i.bak '/^};/i \
    user_'"${CLIENT_NAME}"'=\"'${PASSWORD}'\";' kafka_server_jaas.conf
rm kafka_server_jaas.conf.bak


log_info "'kafka_server_jaas.conf' 已更新。"

# --- 3. 更新密码记录文件 ---
log_info "步骤 4/4: 将新密码记录到 'kafka_credentials.txt'..."
# 统一使用大写变量名
CLIENT_NAME_UPPER=$(echo "$CLIENT_NAME" | tr '[:lower:]' '[:upper:]' | tr '-' '_')
echo "DCI_${CLIENT_NAME_UPPER}_PASSWORD=${PASSWORD}" >> kafka_credentials.txt
log_info "'kafka_credentials.txt' 已更新。"

echo
echo -e "\033[0;32m--- 成功! ---\033[0m"
echo "新客户端 '${CLIENT_NAME}' 的证书和凭证已生成。"
echo -e "用户名: \033[1m${CLIENT_NAME}\033[0m"
echo -e "密  码: \033[1m${PASSWORD}\033[0m"
echo
echo "生成文件:"
echo "  - ${CLIENT_NAME}.client.crt"
echo "  - ${CLIENT_NAME}.client.key"
echo
echo "已更新文件:"
echo "  - kafka_server_jaas.conf"
echo "  - kafka_credentials.txt"
echo
echo -e "\033[0;34m--- 下一步操作 ---\033[0m"
echo "1. **更新Kubernetes Secrets**: 在 dci-kafka-certs 目录下执行以下脚本来应用所有变更。"
echo "   此操作会更新JAAS配置并添加新的客户端证书。"
echo -e "   \033[1;36m./dci-kafka-certs/apply_secrets.sh\033[0m"
echo
echo "2. **重启Kafka Brokers**: 为了加载更新后的JAAS配置，必须滚动重启Kafka集群。"
echo -e "   \033[1;36mkubectl rollout restart statefulset/kafka -n dci\033[0m"
echo "   或者"
echo -e "   \033[1;36mkubectl replace -f /data/DCI/siem/kafka/kafka.yaml --force\033[0m"
echo
echo "3. **配置新客户端**: 新的服务现在可以使用生成的证书、密钥、用户名和密码连接到Kafka。"
echo "   (现有的客户端服务不受影响，无需重启)"
cat << EOF

   举例 (可直接复制, 在项目根目录执行):
kcat -b dcikafka.intra.citic-x.com:30010 \\
     -t dci.monitor.v1.defaultchannel.flows.snmp \\
     -C -e \\
     -X security.protocol=SASL_SSL \\
     -X sasl.mechanisms=PLAIN \\
     -X sasl.username=${CLIENT_NAME} \\
     -X sasl.password=${PASSWORD} \\
     -X ssl.ca.location=dci-monitor/kafka/scripts/dci-kafka-certs/ca-chain.crt \\
     -X ssl.certificate.location=dci-monitor/kafka/scripts/dci-kafka-certs/${CLIENT_NAME}.client.crt \\
     -X ssl.key.location=dci-monitor/kafka/scripts/dci-kafka-certs/${CLIENT_NAME}.client.key
EOF
echo
cd .. 