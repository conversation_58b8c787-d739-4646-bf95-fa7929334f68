# Kafka主题创建工具

本目录包含用于创建DCI数据监测系统所需Kafka主题的脚本工具。

## 主要文件

- `create_kafka_topics.sh`: 用于创建Kafka主题的Shell脚本，支持K8s和外部连接两种模式
- `check_config_consistency.sh`: 用于验证多个Kafka Broker的配置一致性，特别是广播地址配置

## 主题配置

脚本已预先配置以下主题（基于[DCI-Kafka主题规划及负载均衡连接设计](../../../documents/designs/analysis/04-DCI-Kafka主题规划及负载均衡连接设计.md)文档）：

| 主题名称 | 分区数 | 副本因子 | 用途 |
|---------|-------|---------|------|
| dci.monitor.v1.defaultchannel.topology.lldp | 3 | 2 | 接收LLDP原始数据 |
| dci.monitor.v1.defaultchannel.metrics.telegraf | 6 | 2 | 接收监控指标数据 |
| dci.monitor.v1.defaultchannel.logs.syslog | 3 | 2 | 接收日志数据 |
| dci.monitor.v1.defaultchannel.tasks.control | 3 | 2 | 传输任务控制信号 |

## 使用方法

### 前提条件

- **Kubernetes环境**：需要有权限访问运行Kafka的Kubernetes集群，已安装kubectl工具
- **外部连接模式**：需要安装Kafka命令行工具，并且能够连接到Kafka集群

### 基本用法

```bash
# 使用默认配置创建所有主题（K8s模式）
./create_kafka_topics.sh -a

# 创建指定主题
./create_kafka_topics.sh -t dci.monitor.v1.defaultchannel.metrics.telegraf

# 指定特定的Kafka Pod
./create_kafka_topics.sh -a -p kafka-0

# 使用不同的命名空间
./create_kafka_topics.sh -a -n custom-namespace

# 演示模式（不实际创建）
./create_kafka_topics.sh -a -d

# 外部连接模式（无需kubectl）
./create_kafka_topics.sh -e -a
```

### 参数说明

| 选项 | 长选项 | 描述 |
|------|--------|------|
| -n | --namespace | Kubernetes命名空间，默认为'dci' |
| -p | --pod | 指定Kafka Pod名称（如不指定则自动选择第一个） |
| -a | --all | 创建所有主题 |
| -t | --topic | 创建指定的主题 |
| -d | --dry-run | 演示运行，不实际创建主题 |
| -e | --external | 使用外部连接模式（不使用kubectl） |
| -h | --help | 显示帮助信息 |

## 脚本工作原理

1. 解析命令行参数
2. 根据参数选择连接模式（K8s或外部）
3. 检查每个主题是否已存在（幂等操作）
4. 对不存在的主题执行创建操作
5. 报告执行结果

## 自定义配置

如需添加或修改主题配置，请编辑脚本中的`TOPICS`数组：

```bash
TOPICS=(
  "主题名称:分区数:副本因子"
  # 添加更多主题
)
```

## 故障排除

1. **无法连接到Kafka**:
   - 检查Kubernetes连接配置
   - 确认Kafka Pod名称正确
   - 如使用外部模式，确认地址和端口正确

2. **主题创建失败**:
   - 检查Kafka集群健康状态
   - 确认副本因子不大于集群节点数
   - 查看权限是否足够

3. **找不到kubectl命令**:
   - 安装kubectl或使用-e选项使用外部连接模式 

# Kafka配置一致性检查工具【已验证】

本工具用于验证多个Kafka Broker的配置一致性，特别是广播地址配置，确保集群正常运行。

## 使用方法

```bash
# 使用默认参数检查Kafka配置一致性
./check_config_consistency.sh

# 指定命名空间并显示详细输出
./check_config_consistency.sh --namespace dci --verbose

# 仅检查配置内容而不检查文件存在
./check_config_consistency.sh --config-only

# 仅检查配置文件存在而不验证内容
./check_config_consistency.sh --files-only

# 指定不同的Pod前缀和Broker数量
./check_config_consistency.sh --pod-prefix my-kafka --count 5
```

## 参数说明

| 选项 | 长选项 | 描述 |
|------|--------|------|
| -n | --namespace | 指定Kafka Pod所在的命名空间（默认: dci） |
| -p | --pod-prefix | 指定Kafka Pod名称前缀（默认: kafka） |
| -c | --count | 指定Broker数量（默认: 3） |
| -f | --files-only | 仅检查配置文件存在，不验证内容 |
| -C | --config-only | 仅验证配置内容，不检查文件存在 |
| -v | --verbose | 显示详细输出 |
| -h | --help | 显示帮助信息 |

## 工具功能

该脚本执行以下检查：

1. **Pod存在性检查**：验证指定前缀和数量的所有Kafka Pod是否存在
2. **配置文件检查**：验证每个Pod中是否存在必要的配置文件
3. **广播地址配置一致性**：检查所有Broker的advertised.listeners配置是否一致
   - 确保所有广播地址使用相同的主机名
   - 验证各Broker端口是否符合预期（30010+序号）
4. **服务端口映射检查**：验证Kubernetes服务的NodePort配置是否与广播地址端口一致

## 故障排除

如果检查失败，脚本会提供详细的错误信息，帮助您快速定位问题：

- **Pod不存在**：检查Pod的命名空间和前缀是否正确
- **配置文件缺失**：检查服务器配置路径是否正确
- **广播地址不一致**：查看不同Broker的配置文件中advertised.listeners配置
- **服务端口映射错误**：检查Kubernetes服务的NodePort设置

# Kafka DNS分流方案部署指南

本方案解决了Kafka在Kubernetes环境中的元数据过期问题，主要通过DNS分流策略，让内外部客户端使用相同域名，但解析到不同IP地址，统一了连接路径。

## 问题背景

1. **问题核心**：客户端通过SLB访问Kafka，但broker之间通过K8s pod网络通信，导致不一致的网络路径
2. **元数据过期**：客户端接收到的broker地址可能无法直接访问，造成连接失败

## 解决方案：DNS分流

### 核心思路

1. **统一域名方案**：内外部客户端均使用相同格式域名(dcikafka-X.intra.citic-x.com)和端口(30010-30012)
2. **内外部IP解析分离**：
   - 内部Pod：通过initContainer配置hosts文件，将域名解析到Pod IP
   - 外部客户端：通过外部DNS设置脚本，将相同域名解析到SLB IP

## 部署步骤

### 1. 创建DNS分流ConfigMap

```bash
kubectl apply -f kafka-dns-override.yaml -n dci
```

该ConfigMap包含更新各Pod hosts文件的脚本，确保内部通信使用Pod IP。

### 2. 更新Kafka配置

```bash
# 先应用configmap
kubectl apply -f kafka-conf.yaml -n dci

# 然后部署kafka
kubectl apply -f kafka.yaml -n dci
```

Kafka配置已修改：
- 使用统一域名格式(dcikafka-X.intra.citic-x.com)
- 为每个broker配置专用端口(30010+X)
- 使用DNS分流策略实现内外统一访问

### 3. 部署Kafka服务

```bash
kubectl apply -f kafka-service.yaml -n dci
```

服务配置包括：
- Headless服务(kafka-headless)用于集群内部通信
- 三个NodePort服务用于外部访问，每个broker配置专用端口(30010-30012)

### 4. 配置外部客户端

在外部客户端上运行以下脚本配置DNS：

```bash
sudo ./external-dns-setup.sh
```

脚本会将dcikafka-X.intra.citic-x.com域名解析到SLB IP地址。

### 5. 测试连接

使用测试脚本验证连接：

```bash
# 查看Topic列表
./kafka-client-test.sh --list

# 发送消息
./kafka-client-test.sh --produce --topic test

# 消费消息
./kafka-client-test.sh --consume --topic test
```

## 故障排查

如果遇到连接问题，请检查：

1. **ConfigMap是否存在**：确保先创建kafka-dns-override和kafka-conf，再启动Kafka Pod
```bash
kubectl get configmap -n dci | grep kafka
```

2. **DNS解析是否生效**：
```bash
# 内部Pod
kubectl exec -it kafka-0 -n dci -- cat /etc/hosts

# 外部客户端
getent hosts dcikafka-0.intra.citic-x.com
```

3. **端口映射是否正确**：
```bash
kubectl get svc -n dci | grep kafka
```

4. **日志检查**：
```bash
kubectl logs kafka-0 -n dci
```

## 相关文件

- `kafka-dns-override.yaml`: DNS分流ConfigMap
- `kafka.yaml`: Kafka StatefulSet配置
- `kafka-service.yaml`: Kafka服务配置
- `external-dns-setup.sh`: 外部DNS配置脚本
- `kafka-client-test.sh`: 连接测试脚本 

----

kcat -b dcikafka.intra.citic-x.com:30010 \
     -t dci.monitor.v1.defaultchannel.flows.snmp \
     -C -e \
     -X security.protocol=SASL_SSL \
     -X sasl.mechanisms=PLAIN \
     -X sasl.username=user_test_service \
     -X sasl.password=fO5A9cM8xPGxtyxsfloQgw==i \
     -X ssl.ca.location=dci-monitor/kafka/scripts/dci-kafka-certs/ca-chain.crt \
     -X ssl.certificate.location=dci-monitor/kafka/scripts/dci-kafka-certs/user_test_service.client.crt \
     -X ssl.key.location=dci-monitor/kafka/scripts/dci-kafka-certs/user_test_service.client.key