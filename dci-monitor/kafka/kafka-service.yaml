---
# Kafka Headless Service (for broker discovery and stable DNS)
apiVersion: v1
kind: Service
metadata:
  name: kafka-headless
  namespace: dci
  labels:
    app: kafka
    role: kafka-service
  annotations:
    service.beta.kubernetes.io/qcloud-loadbalancer-internal-subnetid: subnet-xxxx
    service.beta.kubernetes.io/qcloud-loadbalancer-health-check-switch: "on"
    service.beta.kubernetes.io/qcloud-loadbalancer-health-check-timeout: "10"
    service.beta.kubernetes.io/qcloud-loadbalancer-health-check-interval: "30"
spec:
  ports:
  - name: internal
    port: 9092
    protocol: TCP
  clusterIP: None # Headless
  selector:
    app: kafka
  publishNotReadyAddresses: true

---
# Kafka Client Service (Generic external access, if needed, via NodePort 30002)
# This service will now correctly target the pod's EXTERNAL listener (port 30002).
apiVersion: v1
kind: Service
metadata:
  name: kafka
  namespace: dci
  labels:
    app: kafka
    role: kafka-service
  annotations:
    service.kubernetes.io/qcloud-loadbalancer-internal-subnetid: subnet-xxxx
    service.beta.kubernetes.io/qcloud-loadbalancer-health-check-switch: "on"
    service.beta.kubernetes.io/qcloud-loadbalancer-health-check-timeout: "10"
    service.beta.kubernetes.io/qcloud-loadbalancer-health-check-interval: "30"
spec:
  type: NodePort
  ports:
  - name: external-generic
    port: 9092
    targetPort: 30003
    nodePort: 30002
    protocol: TCP
  selector:
    app: kafka

---
# Kafka Broker 0 External Service
apiVersion: v1
kind: Service
metadata:
  name: kafka-0-external
  namespace: dci
  labels:
    app: kafka
    role: kafka-broker-0-external-service
spec:
  type: NodePort
  selector:
    app: kafka
    statefulset.kubernetes.io/pod-name: kafka-0
  ports:
  - name: externalbroker0
    port: 30010
    targetPort: 30003
    nodePort: 30010
    protocol: TCP

---
# Kafka Broker 1 External Service
apiVersion: v1
kind: Service
metadata:
  name: kafka-1-external
  namespace: dci
  labels:
    app: kafka
    role: kafka-broker-1-external-service
spec:
  type: NodePort
  selector:
    app: kafka
    statefulset.kubernetes.io/pod-name: kafka-1
  ports:
  - name: externalbroker1
    port: 30011
    targetPort: 30003
    nodePort: 30011
    protocol: TCP

---
# Kafka Broker 2 External Service
apiVersion: v1
kind: Service
metadata:
  name: kafka-2-external
  namespace: dci
  labels:
    app: kafka
    role: kafka-broker-2-external-service
spec:
  type: NodePort
  selector:
    app: kafka
    statefulset.kubernetes.io/pod-name: kafka-2
  ports:
  - name: externalbroker2
    port: 30012
    targetPort: 30003
    nodePort: 30012
    protocol: TCP
