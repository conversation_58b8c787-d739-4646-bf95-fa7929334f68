apiVersion: v1
kind: ConfigMap
metadata:
  name: taosd-config # ConfigMap 名称
  namespace: dci
data:
  taos.cfg: |
    # 从 StatefulSet 环境变量继承的关键配置 (确保与 env 部分一致)
    # firstEp $(STS_NAME)-0.$(SERVICE_NAME).$(STS_NAMESPACE).svc.cluster.local:$(TAOS_SERVER_PORT)
    # fqdn $(POD_NAME).$(SERVICE_NAME).$(STS_NAMESPACE).svc.cluster.local
    serverPort 6030
    
    # 显式启用和配置 HTTP 端口
    httpPort 6041
    
    # 其他推荐或必要的配置 (可以从 TDengine 文档或默认配置中获取)
    # 例如:
    timezone Asia/Shanghai
    # telemetry 0 # 如果想禁用遥测
    # ... 其他参数 ... 