---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: "tdengine"
  namespace: dci
  labels:
    app: "tdengine"
spec:
  serviceName: "taosd"
  replicas: 3
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: "tdengine"
  template:
    metadata:
      name: "tdengine"
      labels:
        app: "tdengine"
    spec:
      # Add dnsConfig to force correct DNS settings within the Pod
      dnsPolicy: ClusterFirst # Default, but make explicit
      dnsConfig:
        nameservers:
          - "***********" # Kubernetes cluster DNS service IP
        searches:
          - dci.svc.cluster.local
          - svc.cluster.local
          - cluster.local # Adjust cluster.local if your cluster domain is different
        options:
          - name: ndots
            value: "5"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - tdengine
                topologyKey: kubernetes.io/hostname
      imagePullSecrets:
        - name: dci-images-key
      containers:
        - name: "tdengine"
          image: cr.registry.pcloud.citic.com/dci/tdengine:*******
          imagePullPolicy: IfNotPresent
          ports:
            - name: tcp6030
              protocol: "TCP"
              containerPort: 6030
            - name: tcp6041
              protocol: "TCP"
              containerPort: 6041
          resources:
            requests:
              memory: "2Gi"
              cpu: "100m"
            limits:
              memory: "4Gi"
              cpu: "2G"
          env:
            # POD_NAME for FQDN config
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            # SERVICE_NAME and NAMESPACE for fqdn resolve
            - name: SERVICE_NAME
              value: "taosd"
            - name: STS_NAME
              value: "tdengine"
            - name: STS_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            # TZ for timezone settings, we recommend to always set it.
            - name: TZ
              value: "Asia/Shanghai"
            # Environment variables with prefix TAOS_ will be parsed and converted into corresponding parameter in taos.cfg. For example, serverPort in taos.cfg should be configured by TAOS_SERVER_PORT when using K8S to deploy
            - name: TAOS_SERVER_PORT
              value: "6030"
            # Must set if you want a cluster.
            - name: TAOS_FIRST_EP
              value: "$(STS_NAME)-0.$(SERVICE_NAME).$(STS_NAMESPACE).svc.cluster.local:$(TAOS_SERVER_PORT)"
            # TAOS_FQND should always be set in k8s env.
            - name: TAOS_FQDN
              value: "$(POD_NAME).$(SERVICE_NAME).$(STS_NAMESPACE).svc.cluster.local"
            # Explicitly enable and set the HTTP port
            - name: TAOS_HTTP_PORT
              value: "6041"
          volumeMounts:
            - name: taosdata
              mountPath: /var/lib/taos
            - name: taosd-config-volume
              mountPath: /etc/taos/taos.cfg
              subPath: taos.cfg
          readinessProbe:
            exec:
              command:
                - taos-check
            initialDelaySeconds: 5
            timeoutSeconds: 10
          livenessProbe:
            exec:
              command:
                - taos-check
            initialDelaySeconds: 15
            periodSeconds: 20
            timeoutSeconds: 10
        - name: "taosadapter"
          image: cr.registry.pcloud.citic.com/dci/tdengine:*******
          imagePullPolicy: IfNotPresent
          command: ["taosadapter"]
          resources:
            requests:
              memory: "2Gi"
              cpu: "100m"
            limits:
              memory: "4Gi"
              cpu: "1"
          volumeMounts:
            - name: taosdata
              mountPath: /var/lib/taos
            - name: taosd-config-volume
              mountPath: /etc/taos/taos.cfg
              subPath: taos.cfg
          ports:
            - name: http
              containerPort: 6041
              protocol: TCP
      volumes:
        - name: taosd-config-volume
          configMap:
            name: taosd-config
            items:
              - key: taos.cfg
                path: taos.cfg
  volumeClaimTemplates:
    - metadata:
        name: taosdata
      spec:
        accessModes:
          - "ReadWriteOnce"
        storageClassName: "dci-nfs-storage"
        resources:
          requests:
            storage: "10Gi"