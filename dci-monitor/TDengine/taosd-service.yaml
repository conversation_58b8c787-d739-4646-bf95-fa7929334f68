---
apiVersion: v1
kind: Service
metadata:
  name: "taosd" # Should match serviceName in StatefulSet
  namespace: dci # Use the same namespace
  labels:
    app: "tdengine"
spec:
  type: NodePort # Specify the service type
  selector:
    app: tdengine # Selects the TDengine pods
  ports:
    - name: tdengine-client
      protocol: TCP
      port: 6030       # Service's internal port
      targetPort: 6030 # Port on the TDengine container
      nodePort: 30003  # The static port on the node 
    - name: tdengine-http
      protocol: TCP
      port: 6041       # Service's internal port
      targetPort: 6041 # Port on the TDengine container
      nodePort: 30004  # The static port on the node 
