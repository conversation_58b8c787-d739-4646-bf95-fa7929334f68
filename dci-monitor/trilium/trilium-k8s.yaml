---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: trilium-data-pvc
  namespace: dci
  annotations:
    description: "用于存储 Trilium Notes 笔记数据的持久卷"
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: dci-nfs-storage
  resources:
    requests:
      storage: 10Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trilium
  namespace: dci
  labels:
    app: trilium
spec:
  replicas: 1
  selector:
    matchLabels:
      app: trilium
  template:
    metadata:
      labels:
        app: trilium
    spec:
      imagePullSecrets:
        - name: dci-images-key
      containers:
      - name: trilium
        image: cr.registry.pcloud.citic.com/dci/trilium:v0.98.0
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: TZ
          value: "Asia/Shanghai"
        - name: TRILIUM_DATA_DIR
          value: "/home/<USER>/trilium-data"
        - name: USER_UID
          value: "1000"
        - name: USER_GID
          value: "1000"
        volumeMounts:
        - name: trilium-data
          mountPath: /home/<USER>/trilium-data
        - name: timezone
          mountPath: /etc/timezone
          readOnly: true
        - name: localtime
          mountPath: /etc/localtime
          readOnly: true
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        resources:
          requests:
            cpu: "100m"
            memory: "256Mi"
          limits:
            cpu: "1G"
            memory: "2Gi"
      volumes:
      - name: trilium-data
        persistentVolumeClaim:
          claimName: trilium-data-pvc
      - name: timezone
        hostPath:
          path: /etc/timezone
      - name: localtime
        hostPath:
          path: /etc/localtime
