---
apiVersion: v1
kind: ConfigMap
metadata:
  name: trilium-config
  namespace: dci
data:
  # Trilium Notes 配置文件
  # 这些是Trilium的基本配置选项
  
  # 数据库配置
  database: |
    # Trilium使用SQLite作为默认数据库
    # 数据将存储在持久化卷中
    
  # 服务器配置
  server: |
    # 服务器端口配置
    port: 8080
    
    # 时区设置
    timezone: "Asia/Shanghai"
    
    # 数据目录
    dataDir: "/home/<USER>/trilium-data"
    
  # 安全配置
  security: |
    # 认证设置
    # 默认情况下，Trilium会创建第一个用户作为管理员
    
    # 多因素认证配置
    # 可以在Trilium Web界面中启用
    
  # 同步配置
  sync: |
    # 同步服务器配置
    # 可以配置自托管的同步服务器
    
    # 客户端同步设置
    # 支持多设备同步 