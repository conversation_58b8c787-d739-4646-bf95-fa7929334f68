# Trilium Notes Kubernetes 部署指南

本文档说明如何在Kubernetes集群中部署Trilium Notes应用。

## 概述

Trilium Notes是一个免费开源的层次化笔记应用，专注于构建大型个人知识库。它支持：
- 层次化笔记组织
- 富文本编辑器
- 代码笔记支持
- 思维导图和关系图
- 多设备同步
- REST API

## 部署文件说明

### 1. trilium-k8s.yaml
包含Trilium的Deployment和PersistentVolumeClaim配置：
- **Deployment**: 部署Trilium应用，使用官方Docker镜像
- **PVC**: 为Trilium数据创建10GB持久化存储
- **资源配置**: 定义了CPU和内存的请求和限制
- **健康检查**: 配置了readiness和liveness探针

### 2. trilium-service.yaml
定义Trilium的Service配置：
- **类型**: NodePort，便于从集群外部访问
- **端口**: 8080 (应用端口) -> 30001 (节点端口)

### 3. trilium-config.yaml
Trilium的配置说明文档，包含：
- 数据库配置
- 服务器设置
- 安全配置
- 同步设置

## 部署步骤

### 1. 使用部署脚本（推荐）
```bash
# 给脚本添加执行权限
chmod +x deploy_trilium.sh

# 运行部署脚本
./deploy_trilium.sh
```

### 2. 手动应用配置文件
```bash
# 创建命名空间（如果不存在）
kubectl create namespace dci

# 应用Trilium配置
kubectl apply -f trilium-config.yaml
kubectl apply -f trilium-k8s.yaml
kubectl apply -f trilium-service.yaml
```

### 2. 验证部署
```bash
# 检查Pod状态
kubectl get pods -n dci -l app=trilium

# 检查Service状态
kubectl get svc -n dci -l app=trilium

# 检查PVC状态
kubectl get pvc -n dci -l app=trilium
```

### 3. 访问应用
Trilium将通过NodePort 30008提供服务，可以通过以下方式访问：
- 集群内: `http://trilium-service.dci.svc.cluster.local:8080`
- 集群外: `http://<节点IP>:30008`

## 配置说明

### 环境变量
- `TZ`: 设置时区为Asia/Shanghai
- `TRILIUM_DATA_DIR`: 数据目录路径
- `USER_UID/USER_GID`: 用户ID和组ID

### 存储配置
- 使用NFS存储类 `dci-nfs-storage`
- 数据目录: `/home/<USER>/trilium-data`
- 存储大小: 10GB

### 资源限制
- CPU请求: 100m，限制: 1G
- 内存请求: 256Mi，限制: 2Gi

## 注意事项

1. **权限要求**: Trilium容器需要root权限运行，但内部会切换到UID/GID 1000:1000
2. **数据持久化**: 确保NFS存储类可用，数据将持久化保存
3. **时区同步**: 挂载了主机时区文件，确保时间显示正确
4. **健康检查**: 使用根路径 `/` 进行健康检查

## 故障排除

### 常见问题
1. **Pod启动失败**: 检查存储类是否可用，PVC是否成功创建
2. **无法访问**: 确认Service配置正确，NodePort端口未被占用
3. **数据丢失**: 检查PVC状态和存储类配置

### 日志查看
```bash
# 查看Pod日志
kubectl logs -n dci -l app=trilium

# 查看Pod详细信息
kubectl describe pod -n dci -l app=trilium
```

## 升级和维护

### 版本升级
```bash
# 更新镜像版本
kubectl set image deployment/trilium trilium=triliumnext/trilium:v0.98.0 -n dci
```

### 数据备份
Trilium数据存储在PVC中，可以通过以下方式备份：
1. 备份PVC数据
2. 使用Trilium内置的导出功能
3. 配置外部备份策略

## 参考链接

- [Trilium官方文档](https://triliumnext.github.io/Docs/)
- [Trilium GitHub](https://github.com/TriliumNext/Trilium)
- [Docker Hub镜像](https://hub.docker.com/r/triliumnext/trilium)
- [Kubernetes部署最佳实践](https://kubernetes.io/docs/concepts/workloads/controllers/deployment/) 