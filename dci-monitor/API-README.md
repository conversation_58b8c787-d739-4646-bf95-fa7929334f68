# DCI监控系统API使用说明

## 接口状态API

DCI监控系统提供了以下API用于查询设备接口状态信息：

### 1. 获取接口状态

```
GET /api/v1/switches/{deviceID}/ports/{ifName}?data=status
```

- `deviceID`: 设备ID，如 "210"
- `ifName`: **使用Base64编码的接口名称**
- `data`: 查询的数据类型，可选值: status(默认)、upstream、downstream

### 2. 获取接口管理状态

```
GET /api/v1/switches/{deviceID}/ports/{ifName}/admin
```

- `deviceID`: 设备ID，如 "210"
- `ifName`: **使用Base64编码的接口名称**

### 3. 获取单端口实时流量

```
GET /api/v1/switches/{deviceID}/ports/{ifName}/flow
```

- `deviceID`: 设备ID，如 "210"
- `ifName`: **使用Base64编码的接口名称**
- `vni`: (可选) 虚拟网络标识符，用于进一步筛选流量
- `start_time`: (可选) RFC3339 UTC格式的查询起始时间，如未提供则默认为15分钟前
- `end_time`: (可选) RFC3339 UTC格式的查询结束时间，如未提供则默认为当前时间

### 4. 获取单端口历史流量

```
GET /api/v1/switches/{deviceID}/ports/{ifName}/flow/history
```

- `deviceID`: 设备ID，如 "210"
- `ifName`: **使用Base64编码的接口名称**
- `vni`: (可选) 虚拟网络标识符，用于进一步筛选流量
- `start_time`: (必填) RFC3339 UTC格式的查询起始时间
- `end_time`: (必填) RFC3339 UTC格式的查询结束时间
- `step`: (可选) 查询步长，格式为Prometheus的duration string (例如'1m', '5m', '1h')，默认为1m

## Base64编码说明

系统不再支持URL编码形式的接口名称，改为使用Base64编码，避免处理斜杠等特殊字符时的问题。

### 编码示例

| 原始接口名称 | Base64编码 |
|-------------|-----------|
| 100GE1/0/6 | MTAwR0UxLzAvNg== |
| GE1/0/1 | R0UxLzAvMQ== |
| 10GE1/0/1.6005002 | MTBHRTEvMC8xLjYwMDUwMDI= |

### 编码方法

#### bash/终端

```bash
# 编码
echo -n "100GE1/0/6" | base64
# 输出: MTAwR0UxLzAvNg==

# 解码
echo -n "MTAwR0UxLzAvNg==" | base64 -d
# 输出: 100GE1/0/6
```

#### Python

```python
import base64

# 编码
encoded = base64.b64encode("100GE1/0/6".encode()).decode()
print(encoded)  # MTAwR0UxLzAvNg==

# 解码
decoded = base64.b64decode("MTAwR0UxLzAvNg==").decode()
print(decoded)  # 100GE1/0/6
```

#### Go

```go
import (
    "encoding/base64"
    "fmt"
)

// 编码
encoded := base64.StdEncoding.EncodeToString([]byte("100GE1/0/6"))
fmt.Println(encoded)  // MTAwR0UxLzAvNg==

// 解码
decoded, err := base64.StdEncoding.DecodeString("MTAwR0UxLzAvNg==")
if err == nil {
    fmt.Println(string(decoded))  // 100GE1/0/6
}
```

## 响应示例

### 接口状态

```json
{
  "deviceId": "210",
  "portId": "GE1/0/1",
  "name": "GigabitEthernet1/0/1",
  "physicalState": "up",  # 物理接口状态
  "protocolState": "up",  # 协议接口状态
  "inErrors": 0,
  "outErrors": 0,
  "description": "Prometheus端口数据",
  "totalInBytes": 132795002,
  "totalOutBytes": 37980032,
  "totalInPkts": 0,
  "totalOutPkts": 0,
  "inRate": 77,
  "outRate": 66,
  "rateUnit": "bps",
  "timestamp": "2025-06-19T15:39:05.246Z"
}
```

### 接口管理状态

```json
{
  "deviceId": "210",
  "portId": "100GE1/0/6",
  "name": "100GigabitEthernet1/0/6",
  "adminState": "up",
  "adminStateRaw": 1,
  "timestamp": "2025-06-19T01:58:47.623Z"
}
```

### 单端口实时流量

```json
{
  "requestID": "e5dfe8a0-6954-4c9b-8dfe-9b2f92a2e8db",
  "queryDetails": {
    "deviceID": "210",
    "portID": "GE1/0/1",
    "vni": "6005002",
    "startTime": "2025-06-19T01:50:00Z",
    "endTime": "2025-06-19T02:05:00Z"
  },
  "flowData": {
    "unitRate": "bps",
    "unitTotal": "Bytes",
    "inRate": 324,
    "outRate": 198,
    "inTotal": 243420000,
    "outTotal": 149062500
  }
}
```

### 单端口历史流量

```json
{
  "requestID": "f7a45b12-9e82-4d1c-b568-761a32e59f03",
  "queryDetails": {
    "deviceID": "210",
    "portID": "GE1/0/1",
    "vni": "6005002",
    "startTime": "2025-06-19T00:00:00Z",
    "endTime": "2025-06-19T02:00:00Z",
    "step": "5m"
  },
  "historyData": {
    "unit": "Mbps",
    "timestamps": [
      "2025-06-19T00:00:00Z",
      "2025-06-19T00:05:00Z",
      "2025-06-19T00:10:00Z",
      "2025-06-19T00:15:00Z",
      "..."
    ],
    "series": [
      {
        "name": "入流量",
        "data": [245.67, 267.89, 289.34, 312.56, "..."]
      },
      {
        "name": "出流量",
        "data": [156.43, 167.21, 178.92, 186.34, "..."]
      }
    ]
  }
}
``` 