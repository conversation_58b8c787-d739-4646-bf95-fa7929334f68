IF-MIB::ifNumber.0 = INTEGER: 61
IF-MIB::ifIndex.2 = INTEGER: 2
IF-MIB::ifIndex.3 = INTEGER: 3
IF-MIB::ifIndex.4 = INTEGER: 4
IF-MIB::ifIndex.5 = INTEGER: 5
IF-MIB::ifIndex.6 = INTEGER: 6
IF-MIB::ifIndex.7 = INTEGER: 7
IF-MIB::ifIndex.8 = INTEGER: 8
IF-MIB::ifIndex.9 = INTEGER: 9
IF-MIB::ifIndex.10 = INTEGER: 10
IF-MIB::ifIndex.11 = INTEGER: 11
IF-MIB::ifIndex.12 = INTEGER: 12
IF-MIB::ifIndex.13 = INTEGER: 13
IF-MIB::ifIndex.14 = INTEGER: 14
IF-MIB::ifIndex.15 = INTEGER: 15
IF-MIB::ifIndex.16 = INTEGER: 16
IF-MIB::ifIndex.17 = INTEGER: 17
IF-MIB::ifIndex.18 = INTEGER: 18
IF-MIB::ifIndex.19 = INTEGER: 19
IF-MIB::ifIndex.20 = INTEGER: 20
IF-MIB::ifIndex.21 = INTEGER: 21
IF-MIB::ifIndex.22 = INTEGER: 22
IF-MIB::ifIndex.23 = INTEGER: 23
IF-MIB::ifIndex.24 = INTEGER: 24
IF-MIB::ifIndex.25 = INTEGER: 25
IF-MIB::ifIndex.26 = INTEGER: 26
IF-MIB::ifIndex.27 = INTEGER: 27
IF-MIB::ifIndex.28 = INTEGER: 28
IF-MIB::ifIndex.29 = INTEGER: 29
IF-MIB::ifIndex.30 = INTEGER: 30
IF-MIB::ifIndex.31 = INTEGER: 31
IF-MIB::ifIndex.32 = INTEGER: 32
IF-MIB::ifIndex.33 = INTEGER: 33
IF-MIB::ifIndex.34 = INTEGER: 34
IF-MIB::ifIndex.35 = INTEGER: 35
IF-MIB::ifIndex.36 = INTEGER: 36
IF-MIB::ifIndex.37 = INTEGER: 37
IF-MIB::ifIndex.38 = INTEGER: 38
IF-MIB::ifIndex.39 = INTEGER: 39
IF-MIB::ifIndex.40 = INTEGER: 40
IF-MIB::ifIndex.41 = INTEGER: 41
IF-MIB::ifIndex.42 = INTEGER: 42
IF-MIB::ifIndex.43 = INTEGER: 43
IF-MIB::ifIndex.44 = INTEGER: 44
IF-MIB::ifIndex.45 = INTEGER: 45
IF-MIB::ifIndex.46 = INTEGER: 46
IF-MIB::ifIndex.47 = INTEGER: 47
IF-MIB::ifIndex.48 = INTEGER: 48
IF-MIB::ifIndex.49 = INTEGER: 49
IF-MIB::ifIndex.50 = INTEGER: 50
IF-MIB::ifIndex.51 = INTEGER: 51
IF-MIB::ifIndex.52 = INTEGER: 52
IF-MIB::ifIndex.53 = INTEGER: 53
IF-MIB::ifIndex.54 = INTEGER: 54
IF-MIB::ifIndex.55 = INTEGER: 55
IF-MIB::ifIndex.56 = INTEGER: 56
IF-MIB::ifIndex.57 = INTEGER: 57
IF-MIB::ifIndex.58 = INTEGER: 58
IF-MIB::ifIndex.59 = INTEGER: 59
IF-MIB::ifIndex.61 = INTEGER: 61
IF-MIB::ifIndex.62 = INTEGER: 62
IF-MIB::ifIndex.64 = INTEGER: 64
IF-MIB::ifDescr.2 = STRING: NULL0
IF-MIB::ifDescr.3 = STRING: InLoopBack0
IF-MIB::ifDescr.4 = STRING: MEth0/0/0
IF-MIB::ifDescr.5 = STRING: 100GE1/0/1
IF-MIB::ifDescr.6 = STRING: 100GE1/0/2
IF-MIB::ifDescr.7 = STRING: 100GE1/0/3
IF-MIB::ifDescr.8 = STRING: 100GE1/0/4
IF-MIB::ifDescr.9 = STRING: 100GE1/0/5
IF-MIB::ifDescr.10 = STRING: 100GE1/0/6
IF-MIB::ifDescr.11 = STRING: 10GE1/0/1
IF-MIB::ifDescr.12 = STRING: 10GE1/0/2
IF-MIB::ifDescr.13 = STRING: 10GE1/0/3
IF-MIB::ifDescr.14 = STRING: 10GE1/0/4
IF-MIB::ifDescr.15 = STRING: 10GE1/0/5
IF-MIB::ifDescr.16 = STRING: 10GE1/0/6
IF-MIB::ifDescr.17 = STRING: 10GE1/0/7
IF-MIB::ifDescr.18 = STRING: 10GE1/0/8
IF-MIB::ifDescr.19 = STRING: 10GE1/0/9
IF-MIB::ifDescr.20 = STRING: 10GE1/0/10
IF-MIB::ifDescr.21 = STRING: 10GE1/0/11
IF-MIB::ifDescr.22 = STRING: 10GE1/0/12
IF-MIB::ifDescr.23 = STRING: 10GE1/0/13
IF-MIB::ifDescr.24 = STRING: 10GE1/0/14
IF-MIB::ifDescr.25 = STRING: 10GE1/0/15
IF-MIB::ifDescr.26 = STRING: 10GE1/0/16
IF-MIB::ifDescr.27 = STRING: 10GE1/0/17
IF-MIB::ifDescr.28 = STRING: 10GE1/0/18
IF-MIB::ifDescr.29 = STRING: 10GE1/0/19
IF-MIB::ifDescr.30 = STRING: 10GE1/0/20
IF-MIB::ifDescr.31 = STRING: 10GE1/0/21
IF-MIB::ifDescr.32 = STRING: 10GE1/0/22
IF-MIB::ifDescr.33 = STRING: 10GE1/0/23
IF-MIB::ifDescr.34 = STRING: 10GE1/0/24
IF-MIB::ifDescr.35 = STRING: 10GE1/0/25
IF-MIB::ifDescr.36 = STRING: 10GE1/0/26
IF-MIB::ifDescr.37 = STRING: 10GE1/0/27
IF-MIB::ifDescr.38 = STRING: 10GE1/0/28
IF-MIB::ifDescr.39 = STRING: 10GE1/0/29
IF-MIB::ifDescr.40 = STRING: 10GE1/0/30
IF-MIB::ifDescr.41 = STRING: 10GE1/0/31
IF-MIB::ifDescr.42 = STRING: 10GE1/0/32
IF-MIB::ifDescr.43 = STRING: 10GE1/0/33
IF-MIB::ifDescr.44 = STRING: 10GE1/0/34
IF-MIB::ifDescr.45 = STRING: 10GE1/0/35
IF-MIB::ifDescr.46 = STRING: 10GE1/0/36
IF-MIB::ifDescr.47 = STRING: 10GE1/0/37
IF-MIB::ifDescr.48 = STRING: 10GE1/0/38
IF-MIB::ifDescr.49 = STRING: 10GE1/0/39
IF-MIB::ifDescr.50 = STRING: 10GE1/0/40
IF-MIB::ifDescr.51 = STRING: 10GE1/0/41
IF-MIB::ifDescr.52 = STRING: 10GE1/0/42
IF-MIB::ifDescr.53 = STRING: 10GE1/0/43
IF-MIB::ifDescr.54 = STRING: 10GE1/0/44
IF-MIB::ifDescr.55 = STRING: 10GE1/0/45
IF-MIB::ifDescr.56 = STRING: 10GE1/0/46
IF-MIB::ifDescr.57 = STRING: 10GE1/0/47
IF-MIB::ifDescr.58 = STRING: 10GE1/0/48
IF-MIB::ifDescr.59 = STRING: Vlanif1
IF-MIB::ifDescr.61 = STRING: Nve1
IF-MIB::ifDescr.62 = STRING: 10GE1/0/34.12138
IF-MIB::ifDescr.64 = STRING: 10GE1/0/48.12138
IF-MIB::ifType.2 = INTEGER: other(1)
IF-MIB::ifType.3 = INTEGER: softwareLoopback(24)
IF-MIB::ifType.4 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.5 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.6 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.7 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.8 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.9 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.10 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.11 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.12 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.13 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.14 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.15 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.16 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.17 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.18 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.19 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.20 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.21 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.22 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.23 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.24 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.25 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.26 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.27 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.28 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.29 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.30 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.31 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.32 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.33 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.34 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.35 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.36 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.37 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.38 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.39 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.40 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.41 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.42 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.43 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.44 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.45 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.46 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.47 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.48 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.49 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.50 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.51 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.52 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.53 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.54 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.55 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.56 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.57 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.58 = INTEGER: ethernetCsmacd(6)
IF-MIB::ifType.59 = INTEGER: propVirtual(53)
IF-MIB::ifType.61 = INTEGER: other(1)
IF-MIB::ifType.62 = INTEGER: l2vlan(135)
IF-MIB::ifType.64 = INTEGER: l2vlan(135)
IF-MIB::ifMtu.2 = INTEGER: 1500
IF-MIB::ifMtu.3 = INTEGER: 1500
IF-MIB::ifMtu.4 = INTEGER: 1500
IF-MIB::ifMtu.5 = INTEGER: 1500
IF-MIB::ifMtu.6 = INTEGER: 1500
IF-MIB::ifMtu.7 = INTEGER: 1500
IF-MIB::ifMtu.8 = INTEGER: 1500
IF-MIB::ifMtu.9 = INTEGER: 1500
IF-MIB::ifMtu.10 = INTEGER: 1500
IF-MIB::ifMtu.11 = INTEGER: 1500
IF-MIB::ifMtu.12 = INTEGER: 1500
IF-MIB::ifMtu.13 = INTEGER: 1500
IF-MIB::ifMtu.14 = INTEGER: 1500
IF-MIB::ifMtu.15 = INTEGER: 1500
IF-MIB::ifMtu.16 = INTEGER: 1500
IF-MIB::ifMtu.17 = INTEGER: 1500
IF-MIB::ifMtu.18 = INTEGER: 1500
IF-MIB::ifMtu.19 = INTEGER: 1500
IF-MIB::ifMtu.20 = INTEGER: 1500
IF-MIB::ifMtu.21 = INTEGER: 1500
IF-MIB::ifMtu.22 = INTEGER: 1500
IF-MIB::ifMtu.23 = INTEGER: 1500
IF-MIB::ifMtu.24 = INTEGER: 1500
IF-MIB::ifMtu.25 = INTEGER: 1500
IF-MIB::ifMtu.26 = INTEGER: 1500
IF-MIB::ifMtu.27 = INTEGER: 1500
IF-MIB::ifMtu.28 = INTEGER: 1500
IF-MIB::ifMtu.29 = INTEGER: 1500
IF-MIB::ifMtu.30 = INTEGER: 1500
IF-MIB::ifMtu.31 = INTEGER: 1500
IF-MIB::ifMtu.32 = INTEGER: 1500
IF-MIB::ifMtu.33 = INTEGER: 1500
IF-MIB::ifMtu.34 = INTEGER: 1500
IF-MIB::ifMtu.35 = INTEGER: 1500
IF-MIB::ifMtu.36 = INTEGER: 1500
IF-MIB::ifMtu.37 = INTEGER: 1500
IF-MIB::ifMtu.38 = INTEGER: 1500
IF-MIB::ifMtu.39 = INTEGER: 1500
IF-MIB::ifMtu.40 = INTEGER: 1500
IF-MIB::ifMtu.41 = INTEGER: 1500
IF-MIB::ifMtu.42 = INTEGER: 1500
IF-MIB::ifMtu.43 = INTEGER: 1500
IF-MIB::ifMtu.44 = INTEGER: 1518
IF-MIB::ifMtu.45 = INTEGER: 1500
IF-MIB::ifMtu.46 = INTEGER: 1500
IF-MIB::ifMtu.47 = INTEGER: 1500
IF-MIB::ifMtu.48 = INTEGER: 1500
IF-MIB::ifMtu.49 = INTEGER: 1500
IF-MIB::ifMtu.50 = INTEGER: 1500
IF-MIB::ifMtu.51 = INTEGER: 1500
IF-MIB::ifMtu.52 = INTEGER: 1500
IF-MIB::ifMtu.53 = INTEGER: 1500
IF-MIB::ifMtu.54 = INTEGER: 1500
IF-MIB::ifMtu.55 = INTEGER: 1500
IF-MIB::ifMtu.56 = INTEGER: 1500
IF-MIB::ifMtu.57 = INTEGER: 1500
IF-MIB::ifMtu.58 = INTEGER: 1500
IF-MIB::ifMtu.59 = INTEGER: 1500
IF-MIB::ifMtu.61 = INTEGER: 1500
IF-MIB::ifMtu.62 = INTEGER: 1500
IF-MIB::ifMtu.64 = INTEGER: 1500
IF-MIB::ifSpeed.2 = Gauge32: 0
IF-MIB::ifSpeed.3 = Gauge32: 0
IF-MIB::ifSpeed.4 = Gauge32: 1000000000
IF-MIB::ifSpeed.5 = Gauge32: 4294967295
IF-MIB::ifSpeed.6 = Gauge32: 4294967295
IF-MIB::ifSpeed.7 = Gauge32: 4294967295
IF-MIB::ifSpeed.8 = Gauge32: 4294967295
IF-MIB::ifSpeed.9 = Gauge32: 4294967295
IF-MIB::ifSpeed.10 = Gauge32: 4294967295
IF-MIB::ifSpeed.11 = Gauge32: 4294967295
IF-MIB::ifSpeed.12 = Gauge32: 4294967295
IF-MIB::ifSpeed.13 = Gauge32: 4294967295
IF-MIB::ifSpeed.14 = Gauge32: 4294967295
IF-MIB::ifSpeed.15 = Gauge32: 4294967295
IF-MIB::ifSpeed.16 = Gauge32: 4294967295
IF-MIB::ifSpeed.17 = Gauge32: 4294967295
IF-MIB::ifSpeed.18 = Gauge32: 4294967295
IF-MIB::ifSpeed.19 = Gauge32: 4294967295
IF-MIB::ifSpeed.20 = Gauge32: 4294967295
IF-MIB::ifSpeed.21 = Gauge32: 4294967295
IF-MIB::ifSpeed.22 = Gauge32: 4294967295
IF-MIB::ifSpeed.23 = Gauge32: 4294967295
IF-MIB::ifSpeed.24 = Gauge32: 4294967295
IF-MIB::ifSpeed.25 = Gauge32: 4294967295
IF-MIB::ifSpeed.26 = Gauge32: 4294967295
IF-MIB::ifSpeed.27 = Gauge32: 4294967295
IF-MIB::ifSpeed.28 = Gauge32: 4294967295
IF-MIB::ifSpeed.29 = Gauge32: 4294967295
IF-MIB::ifSpeed.30 = Gauge32: 4294967295
IF-MIB::ifSpeed.31 = Gauge32: 4294967295
IF-MIB::ifSpeed.32 = Gauge32: 4294967295
IF-MIB::ifSpeed.33 = Gauge32: 4294967295
IF-MIB::ifSpeed.34 = Gauge32: 4294967295
IF-MIB::ifSpeed.35 = Gauge32: 4294967295
IF-MIB::ifSpeed.36 = Gauge32: 4294967295
IF-MIB::ifSpeed.37 = Gauge32: 4294967295
IF-MIB::ifSpeed.38 = Gauge32: 4294967295
IF-MIB::ifSpeed.39 = Gauge32: 4294967295
IF-MIB::ifSpeed.40 = Gauge32: 4294967295
IF-MIB::ifSpeed.41 = Gauge32: 4294967295
IF-MIB::ifSpeed.42 = Gauge32: 4294967295
IF-MIB::ifSpeed.43 = Gauge32: 4294967295
IF-MIB::ifSpeed.44 = Gauge32: 4294967295
IF-MIB::ifSpeed.45 = Gauge32: 4294967295
IF-MIB::ifSpeed.46 = Gauge32: 4294967295
IF-MIB::ifSpeed.47 = Gauge32: 4294967295
IF-MIB::ifSpeed.48 = Gauge32: 4294967295
IF-MIB::ifSpeed.49 = Gauge32: 4294967295
IF-MIB::ifSpeed.50 = Gauge32: 4294967295
IF-MIB::ifSpeed.51 = Gauge32: 4294967295
IF-MIB::ifSpeed.52 = Gauge32: 4294967295
IF-MIB::ifSpeed.53 = Gauge32: 4294967295
IF-MIB::ifSpeed.54 = Gauge32: 4294967295
IF-MIB::ifSpeed.55 = Gauge32: 4294967295
IF-MIB::ifSpeed.56 = Gauge32: 4294967295
IF-MIB::ifSpeed.57 = Gauge32: 4294967295
IF-MIB::ifSpeed.58 = Gauge32: 4294967295
IF-MIB::ifSpeed.59 = Gauge32: 1000000000
IF-MIB::ifSpeed.61 = Gauge32: 0
IF-MIB::ifSpeed.62 = Gauge32: 4294967295
IF-MIB::ifSpeed.64 = Gauge32: 4294967295
IF-MIB::ifPhysAddress.2 = STRING: 0:0:0:0:0:0
IF-MIB::ifPhysAddress.3 = STRING: 0:0:0:0:0:0
IF-MIB::ifPhysAddress.4 = STRING: 44:9b:c1:7:8f:50
IF-MIB::ifPhysAddress.5 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.6 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.7 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.8 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.9 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.10 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.11 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.12 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.13 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.14 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.15 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.16 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.17 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.18 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.19 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.20 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.21 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.22 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.23 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.24 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.25 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.26 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.27 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.28 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.29 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.30 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.31 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.32 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.33 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.34 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.35 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.36 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.37 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.38 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.39 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.40 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.41 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.42 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.43 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.44 = STRING: 44:9b:c1:7:8f:51
IF-MIB::ifPhysAddress.45 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.46 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.47 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.48 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.49 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.50 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.51 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.52 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.53 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.54 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.55 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.56 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.57 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.58 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifPhysAddress.59 = STRING: 44:9b:c1:7:8f:53
IF-MIB::ifPhysAddress.61 = STRING: 44:9b:c1:7:8f:51
IF-MIB::ifPhysAddress.62 = STRING: 44:9b:c1:7:8f:51
IF-MIB::ifPhysAddress.64 = STRING: 44:9b:c1:7:8f:52
IF-MIB::ifAdminStatus.2 = INTEGER: up(1)
IF-MIB::ifAdminStatus.3 = INTEGER: up(1)
IF-MIB::ifAdminStatus.4 = INTEGER: up(1)
IF-MIB::ifAdminStatus.5 = INTEGER: up(1)
IF-MIB::ifAdminStatus.6 = INTEGER: up(1)
IF-MIB::ifAdminStatus.7 = INTEGER: up(1)
IF-MIB::ifAdminStatus.8 = INTEGER: up(1)
IF-MIB::ifAdminStatus.9 = INTEGER: up(1)
IF-MIB::ifAdminStatus.10 = INTEGER: up(1)
IF-MIB::ifAdminStatus.11 = INTEGER: up(1)
IF-MIB::ifAdminStatus.12 = INTEGER: up(1)
IF-MIB::ifAdminStatus.13 = INTEGER: up(1)
IF-MIB::ifAdminStatus.14 = INTEGER: up(1)
IF-MIB::ifAdminStatus.15 = INTEGER: up(1)
IF-MIB::ifAdminStatus.16 = INTEGER: up(1)
IF-MIB::ifAdminStatus.17 = INTEGER: up(1)
IF-MIB::ifAdminStatus.18 = INTEGER: up(1)
IF-MIB::ifAdminStatus.19 = INTEGER: up(1)
IF-MIB::ifAdminStatus.20 = INTEGER: up(1)
IF-MIB::ifAdminStatus.21 = INTEGER: up(1)
IF-MIB::ifAdminStatus.22 = INTEGER: up(1)
IF-MIB::ifAdminStatus.23 = INTEGER: up(1)
IF-MIB::ifAdminStatus.24 = INTEGER: up(1)
IF-MIB::ifAdminStatus.25 = INTEGER: up(1)
IF-MIB::ifAdminStatus.26 = INTEGER: up(1)
IF-MIB::ifAdminStatus.27 = INTEGER: up(1)
IF-MIB::ifAdminStatus.28 = INTEGER: up(1)
IF-MIB::ifAdminStatus.29 = INTEGER: up(1)
IF-MIB::ifAdminStatus.30 = INTEGER: up(1)
IF-MIB::ifAdminStatus.31 = INTEGER: up(1)
IF-MIB::ifAdminStatus.32 = INTEGER: up(1)
IF-MIB::ifAdminStatus.33 = INTEGER: up(1)
IF-MIB::ifAdminStatus.34 = INTEGER: up(1)
IF-MIB::ifAdminStatus.35 = INTEGER: up(1)
IF-MIB::ifAdminStatus.36 = INTEGER: up(1)
IF-MIB::ifAdminStatus.37 = INTEGER: up(1)
IF-MIB::ifAdminStatus.38 = INTEGER: up(1)
IF-MIB::ifAdminStatus.39 = INTEGER: up(1)
IF-MIB::ifAdminStatus.40 = INTEGER: up(1)
IF-MIB::ifAdminStatus.41 = INTEGER: up(1)
IF-MIB::ifAdminStatus.42 = INTEGER: up(1)
IF-MIB::ifAdminStatus.43 = INTEGER: up(1)
IF-MIB::ifAdminStatus.44 = INTEGER: up(1)
IF-MIB::ifAdminStatus.45 = INTEGER: up(1)
IF-MIB::ifAdminStatus.46 = INTEGER: up(1)
IF-MIB::ifAdminStatus.47 = INTEGER: up(1)
IF-MIB::ifAdminStatus.48 = INTEGER: up(1)
IF-MIB::ifAdminStatus.49 = INTEGER: up(1)
IF-MIB::ifAdminStatus.50 = INTEGER: up(1)
IF-MIB::ifAdminStatus.51 = INTEGER: up(1)
IF-MIB::ifAdminStatus.52 = INTEGER: up(1)
IF-MIB::ifAdminStatus.53 = INTEGER: up(1)
IF-MIB::ifAdminStatus.54 = INTEGER: up(1)
IF-MIB::ifAdminStatus.55 = INTEGER: up(1)
IF-MIB::ifAdminStatus.56 = INTEGER: up(1)
IF-MIB::ifAdminStatus.57 = INTEGER: up(1)
IF-MIB::ifAdminStatus.58 = INTEGER: up(1)
IF-MIB::ifAdminStatus.59 = INTEGER: up(1)
IF-MIB::ifAdminStatus.61 = INTEGER: up(1)
IF-MIB::ifAdminStatus.62 = INTEGER: up(1)
IF-MIB::ifAdminStatus.64 = INTEGER: up(1)
IF-MIB::ifOperStatus.2 = INTEGER: up(1)
IF-MIB::ifOperStatus.3 = INTEGER: up(1)
IF-MIB::ifOperStatus.4 = INTEGER: up(1)
IF-MIB::ifOperStatus.5 = INTEGER: down(2)
IF-MIB::ifOperStatus.6 = INTEGER: down(2)
IF-MIB::ifOperStatus.7 = INTEGER: down(2)
IF-MIB::ifOperStatus.8 = INTEGER: down(2)
IF-MIB::ifOperStatus.9 = INTEGER: down(2)
IF-MIB::ifOperStatus.10 = INTEGER: down(2)
IF-MIB::ifOperStatus.11 = INTEGER: down(2)
IF-MIB::ifOperStatus.12 = INTEGER: down(2)
IF-MIB::ifOperStatus.13 = INTEGER: down(2)
IF-MIB::ifOperStatus.14 = INTEGER: down(2)
IF-MIB::ifOperStatus.15 = INTEGER: down(2)
IF-MIB::ifOperStatus.16 = INTEGER: down(2)
IF-MIB::ifOperStatus.17 = INTEGER: down(2)
IF-MIB::ifOperStatus.18 = INTEGER: down(2)
IF-MIB::ifOperStatus.19 = INTEGER: down(2)
IF-MIB::ifOperStatus.20 = INTEGER: down(2)
IF-MIB::ifOperStatus.21 = INTEGER: down(2)
IF-MIB::ifOperStatus.22 = INTEGER: down(2)
IF-MIB::ifOperStatus.23 = INTEGER: down(2)
IF-MIB::ifOperStatus.24 = INTEGER: down(2)
IF-MIB::ifOperStatus.25 = INTEGER: down(2)
IF-MIB::ifOperStatus.26 = INTEGER: down(2)
IF-MIB::ifOperStatus.27 = INTEGER: down(2)
IF-MIB::ifOperStatus.28 = INTEGER: down(2)
IF-MIB::ifOperStatus.29 = INTEGER: down(2)
IF-MIB::ifOperStatus.30 = INTEGER: down(2)
IF-MIB::ifOperStatus.31 = INTEGER: down(2)
IF-MIB::ifOperStatus.32 = INTEGER: down(2)
IF-MIB::ifOperStatus.33 = INTEGER: down(2)
IF-MIB::ifOperStatus.34 = INTEGER: down(2)
IF-MIB::ifOperStatus.35 = INTEGER: down(2)
IF-MIB::ifOperStatus.36 = INTEGER: down(2)
IF-MIB::ifOperStatus.37 = INTEGER: down(2)
IF-MIB::ifOperStatus.38 = INTEGER: down(2)
IF-MIB::ifOperStatus.39 = INTEGER: down(2)
IF-MIB::ifOperStatus.40 = INTEGER: down(2)
IF-MIB::ifOperStatus.41 = INTEGER: down(2)
IF-MIB::ifOperStatus.42 = INTEGER: down(2)
IF-MIB::ifOperStatus.43 = INTEGER: down(2)
IF-MIB::ifOperStatus.44 = INTEGER: down(2)
IF-MIB::ifOperStatus.45 = INTEGER: down(2)
IF-MIB::ifOperStatus.46 = INTEGER: down(2)
IF-MIB::ifOperStatus.47 = INTEGER: down(2)
IF-MIB::ifOperStatus.48 = INTEGER: down(2)
IF-MIB::ifOperStatus.49 = INTEGER: down(2)
IF-MIB::ifOperStatus.50 = INTEGER: down(2)
IF-MIB::ifOperStatus.51 = INTEGER: down(2)
IF-MIB::ifOperStatus.52 = INTEGER: down(2)
IF-MIB::ifOperStatus.53 = INTEGER: down(2)
IF-MIB::ifOperStatus.54 = INTEGER: down(2)
IF-MIB::ifOperStatus.55 = INTEGER: down(2)
IF-MIB::ifOperStatus.56 = INTEGER: down(2)
IF-MIB::ifOperStatus.57 = INTEGER: down(2)
IF-MIB::ifOperStatus.58 = INTEGER: down(2)
IF-MIB::ifOperStatus.59 = INTEGER: down(2)
IF-MIB::ifOperStatus.61 = INTEGER: up(1)
IF-MIB::ifOperStatus.62 = INTEGER: down(2)
IF-MIB::ifOperStatus.64 = INTEGER: down(2)
IF-MIB::ifLastChange.2 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.3 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.4 = Timeticks: (146713) 0:24:27.13
IF-MIB::ifLastChange.5 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.6 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.7 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.8 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.9 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.10 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.11 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.12 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.13 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.14 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.15 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.16 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.17 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.18 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.19 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.20 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.21 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.22 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.23 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.24 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.25 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.26 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.27 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.28 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.29 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.30 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.31 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.32 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.33 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.34 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.35 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.36 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.37 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.38 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.39 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.40 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.41 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.42 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.43 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.44 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.45 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.46 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.47 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.48 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.49 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.50 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.51 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.52 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.53 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.54 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.55 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.56 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.57 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.58 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.59 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.61 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.62 = Timeticks: (0) 0:00:00.00
IF-MIB::ifLastChange.64 = Timeticks: (0) 0:00:00.00
IF-MIB::ifInOctets.2 = Counter32: 0
IF-MIB::ifInOctets.3 = Counter32: 0
IF-MIB::ifInOctets.4 = Counter32: 124978053
IF-MIB::ifInOctets.5 = Counter32: 0
IF-MIB::ifInOctets.6 = Counter32: 0
IF-MIB::ifInOctets.7 = Counter32: 0
IF-MIB::ifInOctets.8 = Counter32: 0
IF-MIB::ifInOctets.9 = Counter32: 0
IF-MIB::ifInOctets.10 = Counter32: 0
IF-MIB::ifInOctets.11 = Counter32: 0
IF-MIB::ifInOctets.12 = Counter32: 0
IF-MIB::ifInOctets.13 = Counter32: 0
IF-MIB::ifInOctets.14 = Counter32: 0
IF-MIB::ifInOctets.15 = Counter32: 0
IF-MIB::ifInOctets.16 = Counter32: 0
IF-MIB::ifInOctets.17 = Counter32: 0
IF-MIB::ifInOctets.18 = Counter32: 0
IF-MIB::ifInOctets.19 = Counter32: 0
IF-MIB::ifInOctets.20 = Counter32: 0
IF-MIB::ifInOctets.21 = Counter32: 0
IF-MIB::ifInOctets.22 = Counter32: 0
IF-MIB::ifInOctets.23 = Counter32: 0
IF-MIB::ifInOctets.24 = Counter32: 0
IF-MIB::ifInOctets.25 = Counter32: 0
IF-MIB::ifInOctets.26 = Counter32: 0
IF-MIB::ifInOctets.27 = Counter32: 0
IF-MIB::ifInOctets.28 = Counter32: 0
IF-MIB::ifInOctets.29 = Counter32: 0
IF-MIB::ifInOctets.30 = Counter32: 0
IF-MIB::ifInOctets.31 = Counter32: 0
IF-MIB::ifInOctets.32 = Counter32: 0
IF-MIB::ifInOctets.33 = Counter32: 0
IF-MIB::ifInOctets.34 = Counter32: 0
IF-MIB::ifInOctets.35 = Counter32: 0
IF-MIB::ifInOctets.36 = Counter32: 0
IF-MIB::ifInOctets.37 = Counter32: 0
IF-MIB::ifInOctets.38 = Counter32: 0
IF-MIB::ifInOctets.39 = Counter32: 0
IF-MIB::ifInOctets.40 = Counter32: 0
IF-MIB::ifInOctets.41 = Counter32: 0
IF-MIB::ifInOctets.42 = Counter32: 0
IF-MIB::ifInOctets.43 = Counter32: 0
IF-MIB::ifInOctets.44 = Counter32: 0
IF-MIB::ifInOctets.45 = Counter32: 0
IF-MIB::ifInOctets.46 = Counter32: 0
IF-MIB::ifInOctets.47 = Counter32: 0
IF-MIB::ifInOctets.48 = Counter32: 0
IF-MIB::ifInOctets.49 = Counter32: 0
IF-MIB::ifInOctets.50 = Counter32: 0
IF-MIB::ifInOctets.51 = Counter32: 0
IF-MIB::ifInOctets.52 = Counter32: 0
IF-MIB::ifInOctets.53 = Counter32: 0
IF-MIB::ifInOctets.54 = Counter32: 0
IF-MIB::ifInOctets.55 = Counter32: 0
IF-MIB::ifInOctets.56 = Counter32: 0
IF-MIB::ifInOctets.57 = Counter32: 0
IF-MIB::ifInOctets.58 = Counter32: 0
IF-MIB::ifInOctets.59 = Counter32: 0
IF-MIB::ifInOctets.61 = Counter32: 0
IF-MIB::ifInOctets.62 = Counter32: 0
IF-MIB::ifInOctets.64 = Counter32: 0
IF-MIB::ifInUcastPkts.2 = Counter32: 0
IF-MIB::ifInUcastPkts.3 = Counter32: 0
IF-MIB::ifInUcastPkts.4 = Counter32: 59832
IF-MIB::ifInUcastPkts.5 = Counter32: 0
IF-MIB::ifInUcastPkts.6 = Counter32: 0
IF-MIB::ifInUcastPkts.7 = Counter32: 0
IF-MIB::ifInUcastPkts.8 = Counter32: 0
IF-MIB::ifInUcastPkts.9 = Counter32: 0
IF-MIB::ifInUcastPkts.10 = Counter32: 0
IF-MIB::ifInUcastPkts.11 = Counter32: 0
IF-MIB::ifInUcastPkts.12 = Counter32: 0
IF-MIB::ifInUcastPkts.13 = Counter32: 0
IF-MIB::ifInUcastPkts.14 = Counter32: 0
IF-MIB::ifInUcastPkts.15 = Counter32: 0
IF-MIB::ifInUcastPkts.16 = Counter32: 0
IF-MIB::ifInUcastPkts.17 = Counter32: 0
IF-MIB::ifInUcastPkts.18 = Counter32: 0
IF-MIB::ifInUcastPkts.19 = Counter32: 0
IF-MIB::ifInUcastPkts.20 = Counter32: 0
IF-MIB::ifInUcastPkts.21 = Counter32: 0
IF-MIB::ifInUcastPkts.22 = Counter32: 0
IF-MIB::ifInUcastPkts.23 = Counter32: 0
IF-MIB::ifInUcastPkts.24 = Counter32: 0
IF-MIB::ifInUcastPkts.25 = Counter32: 0
IF-MIB::ifInUcastPkts.26 = Counter32: 0
IF-MIB::ifInUcastPkts.27 = Counter32: 0
IF-MIB::ifInUcastPkts.28 = Counter32: 0
IF-MIB::ifInUcastPkts.29 = Counter32: 0
IF-MIB::ifInUcastPkts.30 = Counter32: 0
IF-MIB::ifInUcastPkts.31 = Counter32: 0
IF-MIB::ifInUcastPkts.32 = Counter32: 0
IF-MIB::ifInUcastPkts.33 = Counter32: 0
IF-MIB::ifInUcastPkts.34 = Counter32: 0
IF-MIB::ifInUcastPkts.35 = Counter32: 0
IF-MIB::ifInUcastPkts.36 = Counter32: 0
IF-MIB::ifInUcastPkts.37 = Counter32: 0
IF-MIB::ifInUcastPkts.38 = Counter32: 0
IF-MIB::ifInUcastPkts.39 = Counter32: 0
IF-MIB::ifInUcastPkts.40 = Counter32: 0
IF-MIB::ifInUcastPkts.41 = Counter32: 0
IF-MIB::ifInUcastPkts.42 = Counter32: 0
IF-MIB::ifInUcastPkts.43 = Counter32: 0
IF-MIB::ifInUcastPkts.44 = Counter32: 0
IF-MIB::ifInUcastPkts.45 = Counter32: 0
IF-MIB::ifInUcastPkts.46 = Counter32: 0
IF-MIB::ifInUcastPkts.47 = Counter32: 0
IF-MIB::ifInUcastPkts.48 = Counter32: 0
IF-MIB::ifInUcastPkts.49 = Counter32: 0
IF-MIB::ifInUcastPkts.50 = Counter32: 0
IF-MIB::ifInUcastPkts.51 = Counter32: 0
IF-MIB::ifInUcastPkts.52 = Counter32: 0
IF-MIB::ifInUcastPkts.53 = Counter32: 0
IF-MIB::ifInUcastPkts.54 = Counter32: 0
IF-MIB::ifInUcastPkts.55 = Counter32: 0
IF-MIB::ifInUcastPkts.56 = Counter32: 0
IF-MIB::ifInUcastPkts.57 = Counter32: 0
IF-MIB::ifInUcastPkts.58 = Counter32: 0
IF-MIB::ifInUcastPkts.59 = Counter32: 0
IF-MIB::ifInUcastPkts.61 = Counter32: 0
IF-MIB::ifInUcastPkts.62 = Counter32: 0
IF-MIB::ifInUcastPkts.64 = Counter32: 0
IF-MIB::ifInNUcastPkts.2 = Counter32: 0
IF-MIB::ifInNUcastPkts.3 = Counter32: 0
IF-MIB::ifInNUcastPkts.4 = Counter32: 898900
IF-MIB::ifInNUcastPkts.5 = Counter32: 0
IF-MIB::ifInNUcastPkts.6 = Counter32: 0
IF-MIB::ifInNUcastPkts.7 = Counter32: 0
IF-MIB::ifInNUcastPkts.8 = Counter32: 0
IF-MIB::ifInNUcastPkts.9 = Counter32: 0
IF-MIB::ifInNUcastPkts.10 = Counter32: 0
IF-MIB::ifInNUcastPkts.11 = Counter32: 0
IF-MIB::ifInNUcastPkts.12 = Counter32: 0
IF-MIB::ifInNUcastPkts.13 = Counter32: 0
IF-MIB::ifInNUcastPkts.14 = Counter32: 0
IF-MIB::ifInNUcastPkts.15 = Counter32: 0
IF-MIB::ifInNUcastPkts.16 = Counter32: 0
IF-MIB::ifInNUcastPkts.17 = Counter32: 0
IF-MIB::ifInNUcastPkts.18 = Counter32: 0
IF-MIB::ifInNUcastPkts.19 = Counter32: 0
IF-MIB::ifInNUcastPkts.20 = Counter32: 0
IF-MIB::ifInNUcastPkts.21 = Counter32: 0
IF-MIB::ifInNUcastPkts.22 = Counter32: 0
IF-MIB::ifInNUcastPkts.23 = Counter32: 0
IF-MIB::ifInNUcastPkts.24 = Counter32: 0
IF-MIB::ifInNUcastPkts.25 = Counter32: 0
IF-MIB::ifInNUcastPkts.26 = Counter32: 0
IF-MIB::ifInNUcastPkts.27 = Counter32: 0
IF-MIB::ifInNUcastPkts.28 = Counter32: 0
IF-MIB::ifInNUcastPkts.29 = Counter32: 0
IF-MIB::ifInNUcastPkts.30 = Counter32: 0
IF-MIB::ifInNUcastPkts.31 = Counter32: 0
IF-MIB::ifInNUcastPkts.32 = Counter32: 0
IF-MIB::ifInNUcastPkts.33 = Counter32: 0
IF-MIB::ifInNUcastPkts.34 = Counter32: 0
IF-MIB::ifInNUcastPkts.35 = Counter32: 0
IF-MIB::ifInNUcastPkts.36 = Counter32: 0
IF-MIB::ifInNUcastPkts.37 = Counter32: 0
IF-MIB::ifInNUcastPkts.38 = Counter32: 0
IF-MIB::ifInNUcastPkts.39 = Counter32: 0
IF-MIB::ifInNUcastPkts.40 = Counter32: 0
IF-MIB::ifInNUcastPkts.41 = Counter32: 0
IF-MIB::ifInNUcastPkts.42 = Counter32: 0
IF-MIB::ifInNUcastPkts.43 = Counter32: 0
IF-MIB::ifInNUcastPkts.44 = Counter32: 0
IF-MIB::ifInNUcastPkts.45 = Counter32: 0
IF-MIB::ifInNUcastPkts.46 = Counter32: 0
IF-MIB::ifInNUcastPkts.47 = Counter32: 0
IF-MIB::ifInNUcastPkts.48 = Counter32: 0
IF-MIB::ifInNUcastPkts.49 = Counter32: 0
IF-MIB::ifInNUcastPkts.50 = Counter32: 0
IF-MIB::ifInNUcastPkts.51 = Counter32: 0
IF-MIB::ifInNUcastPkts.52 = Counter32: 0
IF-MIB::ifInNUcastPkts.53 = Counter32: 0
IF-MIB::ifInNUcastPkts.54 = Counter32: 0
IF-MIB::ifInNUcastPkts.55 = Counter32: 0
IF-MIB::ifInNUcastPkts.56 = Counter32: 0
IF-MIB::ifInNUcastPkts.57 = Counter32: 0
IF-MIB::ifInNUcastPkts.58 = Counter32: 0
IF-MIB::ifInNUcastPkts.59 = Counter32: 0
IF-MIB::ifInNUcastPkts.61 = Counter32: 0
IF-MIB::ifInNUcastPkts.62 = Counter32: 0
IF-MIB::ifInNUcastPkts.64 = Counter32: 0
IF-MIB::ifInDiscards.2 = Counter32: 0
IF-MIB::ifInDiscards.3 = Counter32: 0
IF-MIB::ifInDiscards.4 = Counter32: 0
IF-MIB::ifInDiscards.5 = Counter32: 0
IF-MIB::ifInDiscards.6 = Counter32: 0
IF-MIB::ifInDiscards.7 = Counter32: 0
IF-MIB::ifInDiscards.8 = Counter32: 0
IF-MIB::ifInDiscards.9 = Counter32: 0
IF-MIB::ifInDiscards.10 = Counter32: 0
IF-MIB::ifInDiscards.11 = Counter32: 0
IF-MIB::ifInDiscards.12 = Counter32: 0
IF-MIB::ifInDiscards.13 = Counter32: 0
IF-MIB::ifInDiscards.14 = Counter32: 0
IF-MIB::ifInDiscards.15 = Counter32: 0
IF-MIB::ifInDiscards.16 = Counter32: 0
IF-MIB::ifInDiscards.17 = Counter32: 0
IF-MIB::ifInDiscards.18 = Counter32: 0
IF-MIB::ifInDiscards.19 = Counter32: 0
IF-MIB::ifInDiscards.20 = Counter32: 0
IF-MIB::ifInDiscards.21 = Counter32: 0
IF-MIB::ifInDiscards.22 = Counter32: 0
IF-MIB::ifInDiscards.23 = Counter32: 0
IF-MIB::ifInDiscards.24 = Counter32: 0
IF-MIB::ifInDiscards.25 = Counter32: 0
IF-MIB::ifInDiscards.26 = Counter32: 0
IF-MIB::ifInDiscards.27 = Counter32: 0
IF-MIB::ifInDiscards.28 = Counter32: 0
IF-MIB::ifInDiscards.29 = Counter32: 0
IF-MIB::ifInDiscards.30 = Counter32: 0
IF-MIB::ifInDiscards.31 = Counter32: 0
IF-MIB::ifInDiscards.32 = Counter32: 0
IF-MIB::ifInDiscards.33 = Counter32: 0
IF-MIB::ifInDiscards.34 = Counter32: 0
IF-MIB::ifInDiscards.35 = Counter32: 0
IF-MIB::ifInDiscards.36 = Counter32: 0
IF-MIB::ifInDiscards.37 = Counter32: 0
IF-MIB::ifInDiscards.38 = Counter32: 0
IF-MIB::ifInDiscards.39 = Counter32: 0
IF-MIB::ifInDiscards.40 = Counter32: 0
IF-MIB::ifInDiscards.41 = Counter32: 0
IF-MIB::ifInDiscards.42 = Counter32: 0
IF-MIB::ifInDiscards.43 = Counter32: 0
IF-MIB::ifInDiscards.44 = Counter32: 0
IF-MIB::ifInDiscards.45 = Counter32: 0
IF-MIB::ifInDiscards.46 = Counter32: 0
IF-MIB::ifInDiscards.47 = Counter32: 0
IF-MIB::ifInDiscards.48 = Counter32: 0
IF-MIB::ifInDiscards.49 = Counter32: 0
IF-MIB::ifInDiscards.50 = Counter32: 0
IF-MIB::ifInDiscards.51 = Counter32: 0
IF-MIB::ifInDiscards.52 = Counter32: 0
IF-MIB::ifInDiscards.53 = Counter32: 0
IF-MIB::ifInDiscards.54 = Counter32: 0
IF-MIB::ifInDiscards.55 = Counter32: 0
IF-MIB::ifInDiscards.56 = Counter32: 0
IF-MIB::ifInDiscards.57 = Counter32: 0
IF-MIB::ifInDiscards.58 = Counter32: 0
IF-MIB::ifInDiscards.59 = Counter32: 0
IF-MIB::ifInDiscards.61 = Counter32: 0
IF-MIB::ifInDiscards.62 = Counter32: 0
IF-MIB::ifInDiscards.64 = Counter32: 0
IF-MIB::ifInErrors.2 = Counter32: 0
IF-MIB::ifInErrors.3 = Counter32: 0
IF-MIB::ifInErrors.4 = Counter32: 0
IF-MIB::ifInErrors.5 = Counter32: 0
IF-MIB::ifInErrors.6 = Counter32: 0
IF-MIB::ifInErrors.7 = Counter32: 0
IF-MIB::ifInErrors.8 = Counter32: 0
IF-MIB::ifInErrors.9 = Counter32: 0
IF-MIB::ifInErrors.10 = Counter32: 0
IF-MIB::ifInErrors.11 = Counter32: 0
IF-MIB::ifInErrors.12 = Counter32: 0
IF-MIB::ifInErrors.13 = Counter32: 0
IF-MIB::ifInErrors.14 = Counter32: 0
IF-MIB::ifInErrors.15 = Counter32: 0
IF-MIB::ifInErrors.16 = Counter32: 0
IF-MIB::ifInErrors.17 = Counter32: 0
IF-MIB::ifInErrors.18 = Counter32: 0
IF-MIB::ifInErrors.19 = Counter32: 0
IF-MIB::ifInErrors.20 = Counter32: 0
IF-MIB::ifInErrors.21 = Counter32: 0
IF-MIB::ifInErrors.22 = Counter32: 0
IF-MIB::ifInErrors.23 = Counter32: 0
IF-MIB::ifInErrors.24 = Counter32: 0
IF-MIB::ifInErrors.25 = Counter32: 0
IF-MIB::ifInErrors.26 = Counter32: 0
IF-MIB::ifInErrors.27 = Counter32: 0
IF-MIB::ifInErrors.28 = Counter32: 0
IF-MIB::ifInErrors.29 = Counter32: 0
IF-MIB::ifInErrors.30 = Counter32: 0
IF-MIB::ifInErrors.31 = Counter32: 0
IF-MIB::ifInErrors.32 = Counter32: 0
IF-MIB::ifInErrors.33 = Counter32: 0
IF-MIB::ifInErrors.34 = Counter32: 0
IF-MIB::ifInErrors.35 = Counter32: 0
IF-MIB::ifInErrors.36 = Counter32: 0
IF-MIB::ifInErrors.37 = Counter32: 0
IF-MIB::ifInErrors.38 = Counter32: 0
IF-MIB::ifInErrors.39 = Counter32: 0
IF-MIB::ifInErrors.40 = Counter32: 0
IF-MIB::ifInErrors.41 = Counter32: 0
IF-MIB::ifInErrors.42 = Counter32: 0
IF-MIB::ifInErrors.43 = Counter32: 0
IF-MIB::ifInErrors.44 = Counter32: 0
IF-MIB::ifInErrors.45 = Counter32: 0
IF-MIB::ifInErrors.46 = Counter32: 0
IF-MIB::ifInErrors.47 = Counter32: 0
IF-MIB::ifInErrors.48 = Counter32: 0
IF-MIB::ifInErrors.49 = Counter32: 0
IF-MIB::ifInErrors.50 = Counter32: 0
IF-MIB::ifInErrors.51 = Counter32: 0
IF-MIB::ifInErrors.52 = Counter32: 0
IF-MIB::ifInErrors.53 = Counter32: 0
IF-MIB::ifInErrors.54 = Counter32: 0
IF-MIB::ifInErrors.55 = Counter32: 0
IF-MIB::ifInErrors.56 = Counter32: 0
IF-MIB::ifInErrors.57 = Counter32: 0
IF-MIB::ifInErrors.58 = Counter32: 0
IF-MIB::ifInErrors.59 = Counter32: 0
IF-MIB::ifInErrors.61 = Counter32: 0
IF-MIB::ifInErrors.62 = Counter32: 0
IF-MIB::ifInErrors.64 = Counter32: 0
IF-MIB::ifInUnknownProtos.2 = Counter32: 0
IF-MIB::ifInUnknownProtos.3 = Counter32: 0
IF-MIB::ifInUnknownProtos.4 = Counter32: 0
IF-MIB::ifInUnknownProtos.5 = Counter32: 0
IF-MIB::ifInUnknownProtos.6 = Counter32: 0
IF-MIB::ifInUnknownProtos.7 = Counter32: 0
IF-MIB::ifInUnknownProtos.8 = Counter32: 0
IF-MIB::ifInUnknownProtos.9 = Counter32: 0
IF-MIB::ifInUnknownProtos.10 = Counter32: 0
IF-MIB::ifInUnknownProtos.11 = Counter32: 0
IF-MIB::ifInUnknownProtos.12 = Counter32: 0
IF-MIB::ifInUnknownProtos.13 = Counter32: 0
IF-MIB::ifInUnknownProtos.14 = Counter32: 0
IF-MIB::ifInUnknownProtos.15 = Counter32: 0
IF-MIB::ifInUnknownProtos.16 = Counter32: 0
IF-MIB::ifInUnknownProtos.17 = Counter32: 0
IF-MIB::ifInUnknownProtos.18 = Counter32: 0
IF-MIB::ifInUnknownProtos.19 = Counter32: 0
IF-MIB::ifInUnknownProtos.20 = Counter32: 0
IF-MIB::ifInUnknownProtos.21 = Counter32: 0
IF-MIB::ifInUnknownProtos.22 = Counter32: 0
IF-MIB::ifInUnknownProtos.23 = Counter32: 0
IF-MIB::ifInUnknownProtos.24 = Counter32: 0
IF-MIB::ifInUnknownProtos.25 = Counter32: 0
IF-MIB::ifInUnknownProtos.26 = Counter32: 0
IF-MIB::ifInUnknownProtos.27 = Counter32: 0
IF-MIB::ifInUnknownProtos.28 = Counter32: 0
IF-MIB::ifInUnknownProtos.29 = Counter32: 0
IF-MIB::ifInUnknownProtos.30 = Counter32: 0
IF-MIB::ifInUnknownProtos.31 = Counter32: 0
IF-MIB::ifInUnknownProtos.32 = Counter32: 0
IF-MIB::ifInUnknownProtos.33 = Counter32: 0
IF-MIB::ifInUnknownProtos.34 = Counter32: 0
IF-MIB::ifInUnknownProtos.35 = Counter32: 0
IF-MIB::ifInUnknownProtos.36 = Counter32: 0
IF-MIB::ifInUnknownProtos.37 = Counter32: 0
IF-MIB::ifInUnknownProtos.38 = Counter32: 0
IF-MIB::ifInUnknownProtos.39 = Counter32: 0
IF-MIB::ifInUnknownProtos.40 = Counter32: 0
IF-MIB::ifInUnknownProtos.41 = Counter32: 0
IF-MIB::ifInUnknownProtos.42 = Counter32: 0
IF-MIB::ifInUnknownProtos.43 = Counter32: 0
IF-MIB::ifInUnknownProtos.44 = Counter32: 0
IF-MIB::ifInUnknownProtos.45 = Counter32: 0
IF-MIB::ifInUnknownProtos.46 = Counter32: 0
IF-MIB::ifInUnknownProtos.47 = Counter32: 0
IF-MIB::ifInUnknownProtos.48 = Counter32: 0
IF-MIB::ifInUnknownProtos.49 = Counter32: 0
IF-MIB::ifInUnknownProtos.50 = Counter32: 0
IF-MIB::ifInUnknownProtos.51 = Counter32: 0
IF-MIB::ifInUnknownProtos.52 = Counter32: 0
IF-MIB::ifInUnknownProtos.53 = Counter32: 0
IF-MIB::ifInUnknownProtos.54 = Counter32: 0
IF-MIB::ifInUnknownProtos.55 = Counter32: 0
IF-MIB::ifInUnknownProtos.56 = Counter32: 0
IF-MIB::ifInUnknownProtos.57 = Counter32: 0
IF-MIB::ifInUnknownProtos.58 = Counter32: 0
IF-MIB::ifInUnknownProtos.59 = Counter32: 0
IF-MIB::ifInUnknownProtos.61 = Counter32: 0
IF-MIB::ifInUnknownProtos.62 = Counter32: 0
IF-MIB::ifInUnknownProtos.64 = Counter32: 0
IF-MIB::ifOutOctets.2 = Counter32: 0
IF-MIB::ifOutOctets.3 = Counter32: 0
IF-MIB::ifOutOctets.4 = Counter32: 17288307
IF-MIB::ifOutOctets.5 = Counter32: 0
IF-MIB::ifOutOctets.6 = Counter32: 0
IF-MIB::ifOutOctets.7 = Counter32: 0
IF-MIB::ifOutOctets.8 = Counter32: 0
IF-MIB::ifOutOctets.9 = Counter32: 0
IF-MIB::ifOutOctets.10 = Counter32: 0
IF-MIB::ifOutOctets.11 = Counter32: 0
IF-MIB::ifOutOctets.12 = Counter32: 0
IF-MIB::ifOutOctets.13 = Counter32: 0
IF-MIB::ifOutOctets.14 = Counter32: 0
IF-MIB::ifOutOctets.15 = Counter32: 0
IF-MIB::ifOutOctets.16 = Counter32: 0
IF-MIB::ifOutOctets.17 = Counter32: 0
IF-MIB::ifOutOctets.18 = Counter32: 0
IF-MIB::ifOutOctets.19 = Counter32: 0
IF-MIB::ifOutOctets.20 = Counter32: 0
IF-MIB::ifOutOctets.21 = Counter32: 0
IF-MIB::ifOutOctets.22 = Counter32: 0
IF-MIB::ifOutOctets.23 = Counter32: 0
IF-MIB::ifOutOctets.24 = Counter32: 0
IF-MIB::ifOutOctets.25 = Counter32: 0
IF-MIB::ifOutOctets.26 = Counter32: 0
IF-MIB::ifOutOctets.27 = Counter32: 0
IF-MIB::ifOutOctets.28 = Counter32: 0
IF-MIB::ifOutOctets.29 = Counter32: 0
IF-MIB::ifOutOctets.30 = Counter32: 0
IF-MIB::ifOutOctets.31 = Counter32: 0
IF-MIB::ifOutOctets.32 = Counter32: 0
IF-MIB::ifOutOctets.33 = Counter32: 0
IF-MIB::ifOutOctets.34 = Counter32: 0
IF-MIB::ifOutOctets.35 = Counter32: 0
IF-MIB::ifOutOctets.36 = Counter32: 0
IF-MIB::ifOutOctets.37 = Counter32: 0
IF-MIB::ifOutOctets.38 = Counter32: 0
IF-MIB::ifOutOctets.39 = Counter32: 0
IF-MIB::ifOutOctets.40 = Counter32: 0
IF-MIB::ifOutOctets.41 = Counter32: 0
IF-MIB::ifOutOctets.42 = Counter32: 0
IF-MIB::ifOutOctets.43 = Counter32: 0
IF-MIB::ifOutOctets.44 = Counter32: 0
IF-MIB::ifOutOctets.45 = Counter32: 0
IF-MIB::ifOutOctets.46 = Counter32: 0
IF-MIB::ifOutOctets.47 = Counter32: 0
IF-MIB::ifOutOctets.48 = Counter32: 0
IF-MIB::ifOutOctets.49 = Counter32: 0
IF-MIB::ifOutOctets.50 = Counter32: 0
IF-MIB::ifOutOctets.51 = Counter32: 0
IF-MIB::ifOutOctets.52 = Counter32: 0
IF-MIB::ifOutOctets.53 = Counter32: 0
IF-MIB::ifOutOctets.54 = Counter32: 0
IF-MIB::ifOutOctets.55 = Counter32: 0
IF-MIB::ifOutOctets.56 = Counter32: 0
IF-MIB::ifOutOctets.57 = Counter32: 0
IF-MIB::ifOutOctets.58 = Counter32: 0
IF-MIB::ifOutOctets.59 = Counter32: 0
IF-MIB::ifOutOctets.61 = Counter32: 0
IF-MIB::ifOutOctets.62 = Counter32: 0
IF-MIB::ifOutOctets.64 = Counter32: 0
IF-MIB::ifOutUcastPkts.2 = Counter32: 0
IF-MIB::ifOutUcastPkts.3 = Counter32: 0
IF-MIB::ifOutUcastPkts.4 = Counter32: 51344
IF-MIB::ifOutUcastPkts.5 = Counter32: 0
IF-MIB::ifOutUcastPkts.6 = Counter32: 0
IF-MIB::ifOutUcastPkts.7 = Counter32: 0
IF-MIB::ifOutUcastPkts.8 = Counter32: 0
IF-MIB::ifOutUcastPkts.9 = Counter32: 0
IF-MIB::ifOutUcastPkts.10 = Counter32: 0
IF-MIB::ifOutUcastPkts.11 = Counter32: 0
IF-MIB::ifOutUcastPkts.12 = Counter32: 0
IF-MIB::ifOutUcastPkts.13 = Counter32: 0
IF-MIB::ifOutUcastPkts.14 = Counter32: 0
IF-MIB::ifOutUcastPkts.15 = Counter32: 0
IF-MIB::ifOutUcastPkts.16 = Counter32: 0
IF-MIB::ifOutUcastPkts.17 = Counter32: 0
IF-MIB::ifOutUcastPkts.18 = Counter32: 0
IF-MIB::ifOutUcastPkts.19 = Counter32: 0
IF-MIB::ifOutUcastPkts.20 = Counter32: 0
IF-MIB::ifOutUcastPkts.21 = Counter32: 0
IF-MIB::ifOutUcastPkts.22 = Counter32: 0
IF-MIB::ifOutUcastPkts.23 = Counter32: 0
IF-MIB::ifOutUcastPkts.24 = Counter32: 0
IF-MIB::ifOutUcastPkts.25 = Counter32: 0
IF-MIB::ifOutUcastPkts.26 = Counter32: 0
IF-MIB::ifOutUcastPkts.27 = Counter32: 0
IF-MIB::ifOutUcastPkts.28 = Counter32: 0
IF-MIB::ifOutUcastPkts.29 = Counter32: 0
IF-MIB::ifOutUcastPkts.30 = Counter32: 0
IF-MIB::ifOutUcastPkts.31 = Counter32: 0
IF-MIB::ifOutUcastPkts.32 = Counter32: 0
IF-MIB::ifOutUcastPkts.33 = Counter32: 0
IF-MIB::ifOutUcastPkts.34 = Counter32: 0
IF-MIB::ifOutUcastPkts.35 = Counter32: 0
IF-MIB::ifOutUcastPkts.36 = Counter32: 0
IF-MIB::ifOutUcastPkts.37 = Counter32: 0
IF-MIB::ifOutUcastPkts.38 = Counter32: 0
IF-MIB::ifOutUcastPkts.39 = Counter32: 0
IF-MIB::ifOutUcastPkts.40 = Counter32: 0
IF-MIB::ifOutUcastPkts.41 = Counter32: 0
IF-MIB::ifOutUcastPkts.42 = Counter32: 0
IF-MIB::ifOutUcastPkts.43 = Counter32: 0
IF-MIB::ifOutUcastPkts.44 = Counter32: 0
IF-MIB::ifOutUcastPkts.45 = Counter32: 0
IF-MIB::ifOutUcastPkts.46 = Counter32: 0
IF-MIB::ifOutUcastPkts.47 = Counter32: 0
IF-MIB::ifOutUcastPkts.48 = Counter32: 0
IF-MIB::ifOutUcastPkts.49 = Counter32: 0
IF-MIB::ifOutUcastPkts.50 = Counter32: 0
IF-MIB::ifOutUcastPkts.51 = Counter32: 0
IF-MIB::ifOutUcastPkts.52 = Counter32: 0
IF-MIB::ifOutUcastPkts.53 = Counter32: 0
IF-MIB::ifOutUcastPkts.54 = Counter32: 0
IF-MIB::ifOutUcastPkts.55 = Counter32: 0
IF-MIB::ifOutUcastPkts.56 = Counter32: 0
IF-MIB::ifOutUcastPkts.57 = Counter32: 0
IF-MIB::ifOutUcastPkts.58 = Counter32: 0
IF-MIB::ifOutUcastPkts.59 = Counter32: 0
IF-MIB::ifOutUcastPkts.61 = Counter32: 0
IF-MIB::ifOutUcastPkts.62 = Counter32: 0
IF-MIB::ifOutUcastPkts.64 = Counter32: 0
IF-MIB::ifOutNUcastPkts.2 = Counter32: 0
IF-MIB::ifOutNUcastPkts.3 = Counter32: 0
IF-MIB::ifOutNUcastPkts.4 = Counter32: 35275
IF-MIB::ifOutNUcastPkts.5 = Counter32: 0
IF-MIB::ifOutNUcastPkts.6 = Counter32: 0
IF-MIB::ifOutNUcastPkts.7 = Counter32: 0
IF-MIB::ifOutNUcastPkts.8 = Counter32: 0
IF-MIB::ifOutNUcastPkts.9 = Counter32: 0
IF-MIB::ifOutNUcastPkts.10 = Counter32: 0
IF-MIB::ifOutNUcastPkts.11 = Counter32: 0
IF-MIB::ifOutNUcastPkts.12 = Counter32: 0
IF-MIB::ifOutNUcastPkts.13 = Counter32: 0
IF-MIB::ifOutNUcastPkts.14 = Counter32: 0
IF-MIB::ifOutNUcastPkts.15 = Counter32: 0
IF-MIB::ifOutNUcastPkts.16 = Counter32: 0
IF-MIB::ifOutNUcastPkts.17 = Counter32: 0
IF-MIB::ifOutNUcastPkts.18 = Counter32: 0
IF-MIB::ifOutNUcastPkts.19 = Counter32: 0
IF-MIB::ifOutNUcastPkts.20 = Counter32: 0
IF-MIB::ifOutNUcastPkts.21 = Counter32: 0
IF-MIB::ifOutNUcastPkts.22 = Counter32: 0
IF-MIB::ifOutNUcastPkts.23 = Counter32: 0
IF-MIB::ifOutNUcastPkts.24 = Counter32: 0
IF-MIB::ifOutNUcastPkts.25 = Counter32: 0
IF-MIB::ifOutNUcastPkts.26 = Counter32: 0
IF-MIB::ifOutNUcastPkts.27 = Counter32: 0
IF-MIB::ifOutNUcastPkts.28 = Counter32: 0
IF-MIB::ifOutNUcastPkts.29 = Counter32: 0
IF-MIB::ifOutNUcastPkts.30 = Counter32: 0
IF-MIB::ifOutNUcastPkts.31 = Counter32: 0
IF-MIB::ifOutNUcastPkts.32 = Counter32: 0
IF-MIB::ifOutNUcastPkts.33 = Counter32: 0
IF-MIB::ifOutNUcastPkts.34 = Counter32: 0
IF-MIB::ifOutNUcastPkts.35 = Counter32: 0
IF-MIB::ifOutNUcastPkts.36 = Counter32: 0
IF-MIB::ifOutNUcastPkts.37 = Counter32: 0
IF-MIB::ifOutNUcastPkts.38 = Counter32: 0
IF-MIB::ifOutNUcastPkts.39 = Counter32: 0
IF-MIB::ifOutNUcastPkts.40 = Counter32: 0
IF-MIB::ifOutNUcastPkts.41 = Counter32: 0
IF-MIB::ifOutNUcastPkts.42 = Counter32: 0
IF-MIB::ifOutNUcastPkts.43 = Counter32: 0
IF-MIB::ifOutNUcastPkts.44 = Counter32: 0
IF-MIB::ifOutNUcastPkts.45 = Counter32: 0
IF-MIB::ifOutNUcastPkts.46 = Counter32: 0
IF-MIB::ifOutNUcastPkts.47 = Counter32: 0
IF-MIB::ifOutNUcastPkts.48 = Counter32: 0
IF-MIB::ifOutNUcastPkts.49 = Counter32: 0
IF-MIB::ifOutNUcastPkts.50 = Counter32: 0
IF-MIB::ifOutNUcastPkts.51 = Counter32: 0
IF-MIB::ifOutNUcastPkts.52 = Counter32: 0
IF-MIB::ifOutNUcastPkts.53 = Counter32: 0
IF-MIB::ifOutNUcastPkts.54 = Counter32: 0
IF-MIB::ifOutNUcastPkts.55 = Counter32: 0
IF-MIB::ifOutNUcastPkts.56 = Counter32: 0
IF-MIB::ifOutNUcastPkts.57 = Counter32: 0
IF-MIB::ifOutNUcastPkts.58 = Counter32: 0
IF-MIB::ifOutNUcastPkts.59 = Counter32: 0
IF-MIB::ifOutNUcastPkts.61 = Counter32: 0
IF-MIB::ifOutNUcastPkts.62 = Counter32: 0
IF-MIB::ifOutNUcastPkts.64 = Counter32: 0
IF-MIB::ifOutDiscards.2 = Counter32: 0
IF-MIB::ifOutDiscards.3 = Counter32: 0
IF-MIB::ifOutDiscards.4 = Counter32: 0
IF-MIB::ifOutDiscards.5 = Counter32: 0
IF-MIB::ifOutDiscards.6 = Counter32: 0
IF-MIB::ifOutDiscards.7 = Counter32: 0
IF-MIB::ifOutDiscards.8 = Counter32: 0
IF-MIB::ifOutDiscards.9 = Counter32: 0
IF-MIB::ifOutDiscards.10 = Counter32: 0
IF-MIB::ifOutDiscards.11 = Counter32: 0
IF-MIB::ifOutDiscards.12 = Counter32: 0
IF-MIB::ifOutDiscards.13 = Counter32: 0
IF-MIB::ifOutDiscards.14 = Counter32: 0
IF-MIB::ifOutDiscards.15 = Counter32: 0
IF-MIB::ifOutDiscards.16 = Counter32: 0
IF-MIB::ifOutDiscards.17 = Counter32: 0
IF-MIB::ifOutDiscards.18 = Counter32: 0
IF-MIB::ifOutDiscards.19 = Counter32: 0
IF-MIB::ifOutDiscards.20 = Counter32: 0
IF-MIB::ifOutDiscards.21 = Counter32: 0
IF-MIB::ifOutDiscards.22 = Counter32: 0
IF-MIB::ifOutDiscards.23 = Counter32: 0
IF-MIB::ifOutDiscards.24 = Counter32: 0
IF-MIB::ifOutDiscards.25 = Counter32: 0
IF-MIB::ifOutDiscards.26 = Counter32: 0
IF-MIB::ifOutDiscards.27 = Counter32: 0
IF-MIB::ifOutDiscards.28 = Counter32: 0
IF-MIB::ifOutDiscards.29 = Counter32: 0
IF-MIB::ifOutDiscards.30 = Counter32: 0
IF-MIB::ifOutDiscards.31 = Counter32: 0
IF-MIB::ifOutDiscards.32 = Counter32: 0
IF-MIB::ifOutDiscards.33 = Counter32: 0
IF-MIB::ifOutDiscards.34 = Counter32: 0
IF-MIB::ifOutDiscards.35 = Counter32: 0
IF-MIB::ifOutDiscards.36 = Counter32: 0
IF-MIB::ifOutDiscards.37 = Counter32: 0
IF-MIB::ifOutDiscards.38 = Counter32: 0
IF-MIB::ifOutDiscards.39 = Counter32: 0
IF-MIB::ifOutDiscards.40 = Counter32: 0
IF-MIB::ifOutDiscards.41 = Counter32: 0
IF-MIB::ifOutDiscards.42 = Counter32: 0
IF-MIB::ifOutDiscards.43 = Counter32: 0
IF-MIB::ifOutDiscards.44 = Counter32: 0
IF-MIB::ifOutDiscards.45 = Counter32: 0
IF-MIB::ifOutDiscards.46 = Counter32: 0
IF-MIB::ifOutDiscards.47 = Counter32: 0
IF-MIB::ifOutDiscards.48 = Counter32: 0
IF-MIB::ifOutDiscards.49 = Counter32: 0
IF-MIB::ifOutDiscards.50 = Counter32: 0
IF-MIB::ifOutDiscards.51 = Counter32: 0
IF-MIB::ifOutDiscards.52 = Counter32: 0
IF-MIB::ifOutDiscards.53 = Counter32: 0
IF-MIB::ifOutDiscards.54 = Counter32: 0
IF-MIB::ifOutDiscards.55 = Counter32: 0
IF-MIB::ifOutDiscards.56 = Counter32: 0
IF-MIB::ifOutDiscards.57 = Counter32: 0
IF-MIB::ifOutDiscards.58 = Counter32: 0
IF-MIB::ifOutDiscards.59 = Counter32: 0
IF-MIB::ifOutDiscards.61 = Counter32: 0
IF-MIB::ifOutDiscards.62 = Counter32: 0
IF-MIB::ifOutDiscards.64 = Counter32: 0
IF-MIB::ifOutErrors.2 = Counter32: 0
IF-MIB::ifOutErrors.3 = Counter32: 0
IF-MIB::ifOutErrors.4 = Counter32: 0
IF-MIB::ifOutErrors.5 = Counter32: 0
IF-MIB::ifOutErrors.6 = Counter32: 0
IF-MIB::ifOutErrors.7 = Counter32: 0
IF-MIB::ifOutErrors.8 = Counter32: 0
IF-MIB::ifOutErrors.9 = Counter32: 0
IF-MIB::ifOutErrors.10 = Counter32: 0
IF-MIB::ifOutErrors.11 = Counter32: 0
IF-MIB::ifOutErrors.12 = Counter32: 0
IF-MIB::ifOutErrors.13 = Counter32: 0
IF-MIB::ifOutErrors.14 = Counter32: 0
IF-MIB::ifOutErrors.15 = Counter32: 0
IF-MIB::ifOutErrors.16 = Counter32: 0
IF-MIB::ifOutErrors.17 = Counter32: 0
IF-MIB::ifOutErrors.18 = Counter32: 0
IF-MIB::ifOutErrors.19 = Counter32: 0
IF-MIB::ifOutErrors.20 = Counter32: 0
IF-MIB::ifOutErrors.21 = Counter32: 0
IF-MIB::ifOutErrors.22 = Counter32: 0
IF-MIB::ifOutErrors.23 = Counter32: 0
IF-MIB::ifOutErrors.24 = Counter32: 0
IF-MIB::ifOutErrors.25 = Counter32: 0
IF-MIB::ifOutErrors.26 = Counter32: 0
IF-MIB::ifOutErrors.27 = Counter32: 0
IF-MIB::ifOutErrors.28 = Counter32: 0
IF-MIB::ifOutErrors.29 = Counter32: 0
IF-MIB::ifOutErrors.30 = Counter32: 0
IF-MIB::ifOutErrors.31 = Counter32: 0
IF-MIB::ifOutErrors.32 = Counter32: 0
IF-MIB::ifOutErrors.33 = Counter32: 0
IF-MIB::ifOutErrors.34 = Counter32: 0
IF-MIB::ifOutErrors.35 = Counter32: 0
IF-MIB::ifOutErrors.36 = Counter32: 0
IF-MIB::ifOutErrors.37 = Counter32: 0
IF-MIB::ifOutErrors.38 = Counter32: 0
IF-MIB::ifOutErrors.39 = Counter32: 0
IF-MIB::ifOutErrors.40 = Counter32: 0
IF-MIB::ifOutErrors.41 = Counter32: 0
IF-MIB::ifOutErrors.42 = Counter32: 0
IF-MIB::ifOutErrors.43 = Counter32: 0
IF-MIB::ifOutErrors.44 = Counter32: 0
IF-MIB::ifOutErrors.45 = Counter32: 0
IF-MIB::ifOutErrors.46 = Counter32: 0
IF-MIB::ifOutErrors.47 = Counter32: 0
IF-MIB::ifOutErrors.48 = Counter32: 0
IF-MIB::ifOutErrors.49 = Counter32: 0
IF-MIB::ifOutErrors.50 = Counter32: 0
IF-MIB::ifOutErrors.51 = Counter32: 0
IF-MIB::ifOutErrors.52 = Counter32: 0
IF-MIB::ifOutErrors.53 = Counter32: 0
IF-MIB::ifOutErrors.54 = Counter32: 0
IF-MIB::ifOutErrors.55 = Counter32: 0
IF-MIB::ifOutErrors.56 = Counter32: 0
IF-MIB::ifOutErrors.57 = Counter32: 0
IF-MIB::ifOutErrors.58 = Counter32: 0
IF-MIB::ifOutErrors.59 = Counter32: 0
IF-MIB::ifOutErrors.61 = Counter32: 0
IF-MIB::ifOutErrors.62 = Counter32: 0
IF-MIB::ifOutErrors.64 = Counter32: 0
IF-MIB::ifOutQLen.2 = Gauge32: 0
IF-MIB::ifOutQLen.3 = Gauge32: 0
IF-MIB::ifOutQLen.4 = Gauge32: 0
IF-MIB::ifOutQLen.5 = Gauge32: 0
IF-MIB::ifOutQLen.6 = Gauge32: 0
IF-MIB::ifOutQLen.7 = Gauge32: 0
IF-MIB::ifOutQLen.8 = Gauge32: 0
IF-MIB::ifOutQLen.9 = Gauge32: 0
IF-MIB::ifOutQLen.10 = Gauge32: 0
IF-MIB::ifOutQLen.11 = Gauge32: 0
IF-MIB::ifOutQLen.12 = Gauge32: 0
IF-MIB::ifOutQLen.13 = Gauge32: 0
IF-MIB::ifOutQLen.14 = Gauge32: 0
IF-MIB::ifOutQLen.15 = Gauge32: 0
IF-MIB::ifOutQLen.16 = Gauge32: 0
IF-MIB::ifOutQLen.17 = Gauge32: 0
IF-MIB::ifOutQLen.18 = Gauge32: 0
IF-MIB::ifOutQLen.19 = Gauge32: 0
IF-MIB::ifOutQLen.20 = Gauge32: 0
IF-MIB::ifOutQLen.21 = Gauge32: 0
IF-MIB::ifOutQLen.22 = Gauge32: 0
IF-MIB::ifOutQLen.23 = Gauge32: 0
IF-MIB::ifOutQLen.24 = Gauge32: 0
IF-MIB::ifOutQLen.25 = Gauge32: 0
IF-MIB::ifOutQLen.26 = Gauge32: 0
IF-MIB::ifOutQLen.27 = Gauge32: 0
IF-MIB::ifOutQLen.28 = Gauge32: 0
IF-MIB::ifOutQLen.29 = Gauge32: 0
IF-MIB::ifOutQLen.30 = Gauge32: 0
IF-MIB::ifOutQLen.31 = Gauge32: 0
IF-MIB::ifOutQLen.32 = Gauge32: 0
IF-MIB::ifOutQLen.33 = Gauge32: 0
IF-MIB::ifOutQLen.34 = Gauge32: 0
IF-MIB::ifOutQLen.35 = Gauge32: 0
IF-MIB::ifOutQLen.36 = Gauge32: 0
IF-MIB::ifOutQLen.37 = Gauge32: 0
IF-MIB::ifOutQLen.38 = Gauge32: 0
IF-MIB::ifOutQLen.39 = Gauge32: 0
IF-MIB::ifOutQLen.40 = Gauge32: 0
IF-MIB::ifOutQLen.41 = Gauge32: 0
IF-MIB::ifOutQLen.42 = Gauge32: 0
IF-MIB::ifOutQLen.43 = Gauge32: 0
IF-MIB::ifOutQLen.44 = Gauge32: 0
IF-MIB::ifOutQLen.45 = Gauge32: 0
IF-MIB::ifOutQLen.46 = Gauge32: 0
IF-MIB::ifOutQLen.47 = Gauge32: 0
IF-MIB::ifOutQLen.48 = Gauge32: 0
IF-MIB::ifOutQLen.49 = Gauge32: 0
IF-MIB::ifOutQLen.50 = Gauge32: 0
IF-MIB::ifOutQLen.51 = Gauge32: 0
IF-MIB::ifOutQLen.52 = Gauge32: 0
IF-MIB::ifOutQLen.53 = Gauge32: 0
IF-MIB::ifOutQLen.54 = Gauge32: 0
IF-MIB::ifOutQLen.55 = Gauge32: 0
IF-MIB::ifOutQLen.56 = Gauge32: 0
IF-MIB::ifOutQLen.57 = Gauge32: 0
IF-MIB::ifOutQLen.58 = Gauge32: 0
IF-MIB::ifOutQLen.59 = Gauge32: 0
IF-MIB::ifOutQLen.61 = Gauge32: 0
IF-MIB::ifOutQLen.62 = Gauge32: 0
IF-MIB::ifOutQLen.64 = Gauge32: 0
IF-MIB::ifSpecific.2 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.3 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.4 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.5 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.6 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.7 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.8 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.9 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.10 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.11 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.12 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.13 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.14 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.15 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.16 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.17 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.18 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.19 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.20 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.21 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.22 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.23 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.24 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.25 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.26 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.27 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.28 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.29 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.30 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.31 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.32 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.33 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.34 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.35 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.36 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.37 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.38 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.39 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.40 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.41 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.42 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.43 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.44 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.45 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.46 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.47 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.48 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.49 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.50 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.51 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.52 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.53 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.54 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.55 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.56 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.57 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.58 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.59 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.61 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.62 = OID: SNMPv2-SMI::zeroDotZero
IF-MIB::ifSpecific.64 = OID: SNMPv2-SMI::zeroDotZero
