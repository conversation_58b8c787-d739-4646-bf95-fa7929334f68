
采集所需的 SNMP 数据，并将结果保存为文本文件，作为离线数据源。

基于您提供的设计文档（`02-网络自动化平台-数据监测系统技术概要设计.md`, `03-dci_拓扑数据技术设计.md`, `04-指标数据基线监测及告警技术方案设计.md`, `05-流量统计及流量速率监测算法及技术方案.md`），我们需要采集以下几类关键信息：

1.  **设备基本信息 (System):** 用于识别设备。
2.  **接口信息 (IF-MIB):** 包括接口状态、描述以及关键的64位流量计数器（用于流量统计和速率计算）。
3.  **LLDP 信息 (LLDP-MIB):** 用于拓扑发现和处理。
4.  **设备健康状况:** 如 CPU 使用率、内存使用率等（用于基础告警和监控），这部分通常依赖于厂商的私有 MIB。

**交换机SSH方式：**

```bash
SW1 10.36.46.188 
SW2 10.36.46.189
用户名 huawei 密码 DCILab@2025
snmp community dcilab2025
```

**梳理所需的关键 MIB OID 列表:**

以下是根据设计文档推断出的核心 OID 列表。请注意，设备健康相关的 OID (CPU, 内存等) 依赖于华为的私有 MIB，您需要参考您提到的 `CloudEngine-9800-8800-6800-5800-V200R024C00-mib参考.md` 文件来确认这些私有 OID 的准确性。

*   **System MIB (`.1.3.6.1.2.1.1`):**
    *   `sysDescr` (.1.3.6.1.2.1.1.1.0): 系统描述
    *   `sysObjectID` (.1.3.6.1.2.1.1.2.0): 系统对象 ID
    *   `sysUpTime` (.1.3.6.1.2.1.1.3.0): 系统启动时间
    *   `sysName` (.1.3.6.1.2.1.1.5.0): 系统名称
*   **IF-MIB - 接口表 (`.1.3.6.1.2.1.2.2.1` - ifEntry):**
    *   `ifIndex` (.1.3.6.1.2.1.2.2.1.1): 接口索引
    *   `ifDescr` (.1.3.6.1.2.1.2.2.1.2): 接口描述
    *   `ifType` (.1.3.6.1.2.1.2.2.1.3): 接口类型
    *   `ifAdminStatus` (.1.3.6.1.2.1.2.2.1.7): 管理状态
    *   `ifOperStatus` (.1.3.6.1.2.1.2.2.1.8): 操作状态
*   **IF-MIB - 接口扩展表 (`.1.3.6.1.2.1.31.1.1.1` - ifXEntry):**
    *   `ifName` (.1.3.6.1.2.1.31.1.1.1.1): 接口名称 (推荐使用)
    *   `ifHCInOctets` (.1.3.6.1.2.1.31.1.1.1.6): 64位入方向字节数
    *   `ifHCOutOctets` (.1.3.6.1.2.1.31.1.1.1.10): 64位出方向字节数
    *   `ifHCInUcastPkts` (.1.3.6.1.2.1.31.1.1.1.7): 64位入方向单播包数
    *   `ifHCOutUcastPkts` (.1.3.6.1.2.1.31.1.1.1.11): 64位出方向单播包数
*   **LLDP-MIB (`.1.0.8802.1.1.2`):**
    *   **本地端口信息 (`.1.0.8802.1.1.2.1.3.7.1` - lldpLocPortEntry):**
        *   `lldpLocPortIdSubtype` (.1.0.8802.1.1.2.1.3.7.1.2)
        *   `lldpLocPortId` (.1.0.8802.1.1.2.1.3.7.1.3)
    *   **远端设备信息 (`.1.0.8802.1.1.2.1.4.1.1` - lldpRemEntry):**
        *   `lldpRemChassisIdSubtype` (.1.0.8802.1.1.2.1.4.1.1.4)
        *   `lldpRemChassisId` (.1.0.8802.1.1.2.1.4.1.1.5)
        *   `lldpRemPortIdSubtype` (.1.0.8802.1.1.2.1.4.1.1.6)
        *   `lldpRemPortId` (.1.0.8802.1.1.2.1.4.1.1.7)
        *   `lldpRemSysName` (.1.0.8802.1.1.2.1.4.1.1.9)
*   **设备健康状况 (需要根据华为 MIB 文件确认):**
    *   **CPU 使用率:** 查找与 CPU Utilization 相关的 OID。可能是华为实体 MIB (`HUAWEI-ENTITY-EXTENT-MIB`) 中的 `hwEntityCpuUsage` (例如 `.1.3.6.1.4.1.2011.5.25.31.1.1.1.1.5`) 或类似 OID。
    *   **内存使用率:** 查找与 Memory Utilization 相关的 OID。可能是华为实体 MIB 中的 `hwEntityMemUsage` (例如 `.1.3.6.1.4.1.2011.5.25.31.1.1.1.1.7`) 或类似 OID。
    *   **(可选) 温度:** `hwEntityTemperature` (例如 `.1.3.6.1.4.1.2011.5.25.31.1.1.1.1.11`)。
    *   **(可选) 风扇状态、电源状态等:** 通常也在实体 MIB 或专门的硬件状态 MIB 中。

**采集方案与命令:**

对每台交换机，分模块执行 `snmpwalk` 并将结果保存到不同的文件中，方便管理和后续解析。

```bash
timestamp=$(date +%Y%m%d%H%M%S); dir=snmp_data_$(date +%Y%m%d); mkdir -p $dir; echo "采集 SW1 (10.36.46.188) 的 System MIB..."; snmpwalk -v 2c -c dcilab2025 10.36.46.188 .1.3.6.1.2.1.1 > $dir/sw1_system_$timestamp.txt; echo "采集 SW1 (10.36.46.188) 的 IF-MIB (标准接口表)..."; snmpwalk -v 2c -c dcilab2025 10.36.46.188 .1.3.6.1.2.1.2 > $dir/sw1_ifTable_$timestamp.txt; echo "采集 SW1 (10.36.46.188) 的 IF-MIB (扩展接口表)..."; snmpwalk -v 2c -c dcilab2025 10.36.46.188 .1.3.6.1.2.1.31 > $dir/sw1_ifXTable_$timestamp.txt; echo "采集 SW1 (10.36.46.188) 的 LLDP-MIB..."; snmpwalk -v 2c -c dcilab2025 10.36.46.188 .1.0.8802.1.1.2.1.4.1.1 > $dir/sw1_lldp_$timestamp.txt; echo "SW1 采集完成，文件保存在 $dir 目录";
```

**关于设备健康状况 (CPU, 内存等):**

请务必查阅 `CloudEngine-9800-8800-6800-5800-V200R024C00-mib参考.md` 文件，找到准确的 CPU 使用率、内存使用率等指标的 OID。找到后，您可以手动执行类似的 `snmpwalk` 命令来采集这些私有 MIB 数据，例如 (假设 CPU OID 根为 `.1.3.6.1.4.1.2011.5.25.31.1.1.1.1.5`):

```bash
# (请替换为实际 OID)
snmpwalk -v 2c -c dcilab2025 10.36.46.188 .1.3.6.1.4.1.2011.5.25.31.1.1.1.1.5 > snmp_data_20250430/sw1_cpu_usage_$(date +%Y%m%d%H%M%S).txt
snmpwalk -v 2c -c dcilab2025 10.36.46.189 .1.3.6.1.4.1.2011.5.25.31.1.1.1.1.5 > snmp_data_20250430/sw2_cpu_usage_$(date +%Y%m%d%H%M%S).txt
```
对内存等其他私有指标执行类似操作。

**数据使用:**

1.  编写解析脚本，读取这些文件的内容。
2.  模拟 Telegraf 的输出格式 (例如 JSON Lines)，将解析后的数据提供给 DCI-Monitor 的数据处理管道 (如 Kafka Topic)。
3.  这样就可以在没有真实设备连接的情况下，测试数据解析、ID 转换、存储和 API 查询等功能。


# SNMP 数据采集状态 (截至 2025-04-30 15:30)

本目录包含从以下设备采集的 SNMP 数据快照，用于 DCI-Monitor 项目的离线开发和测试。
