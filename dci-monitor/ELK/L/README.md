
kubectl logs  $(kube<PERSON>l get pods -n dci -l app=logstash -o jsonpath='{.items[0].metadata.name}') -ndci

# 创建 Logstash 凭证 Secret
kubectl create secret generic logstash-credentials -n dci \
  --from-literal=ELASTICSEARCH_USERNAME="logstash_system" \
  --from-literal=ELASTICSEARCH_PASSWORD="zANmcQOtgTV7H3MnwlGh"


# 获取 ELASTICSEARCH_USERNAME 的值
kubectl get secret logstash-credentials -n dci -o jsonpath='{.data.ELASTICSEARCH_USERNAME}' | base64 --decode
echo

# 获取 ELASTICSEARCH_PASSWORD 的值
kubectl get secret logstash-credentials -n dci -o jsonpath='{.data.ELASTICSEARCH_PASSWORD}' | base64 --decode
echo

