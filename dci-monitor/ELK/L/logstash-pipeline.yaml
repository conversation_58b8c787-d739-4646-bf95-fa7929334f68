---
apiVersion: v1
kind: ConfigMap
metadata:
  name: logstash-pipeline
  namespace: dci
data:
  logstash.conf: |
    input {
      beats {
        id => "input_beats"
        port => 5044
      }
      http {
        id => "input_http"
        port => 8080
      }
      syslog {
        id => "input_syslog"
        port => 5140
        tags => ["syslog_input"]
        type => "syslog"
      }
    }
    
    filter {
      if [fields][log_type] == "application" {
        grok {
          id => "app_log_parser"
          match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:log_level} %{GREEDYDATA:log_message}" }
          overwrite => [ "message" ]
        }
        date {
          id => "app_timestamp_setter"
          match => [ "timestamp", "ISO8601" ]
          target => "@timestamp"
          remove_field => ["timestamp"]
        }
      }

      # 输入: input_syslog
      #   ↓
      # 解析: syslog_rfc5424_parser [添加 _rfc5424_groksucceed 标签]
      #   ↓
      # 初始化: netstat_tag_initializer [添加 raw_netstat_syslog 标签]
      #   ↓
      # 解析: netstat_line_parser [成功时无标签，失败时添加 _netstat_grokfailure]
      #   ↓
      # 成功路径: netstat_field_converter [添加 processed_netstat 标签]
      #   ↓                              
      # 失败路径: netstat_parse_failure_marker [添加 failed_netstat_parse 标签]
      #   ↓
      # 输出: output_elasticsearch 和 output_debug_netstat
      
      if [type] == "syslog" {
        # Attempt to parse the full message as RFC 5424 if input plugin failed
        grok {
          id => "syslog_rfc5424_parser"
          match => { "message" => "<%{POSINT:syslog_pri}>%{NUMBER:syslog_version} %{TIMESTAMP_ISO8601:syslog_timestamp} %{SYSLOGHOST:syslog_hostname} %{NOTSPACE:program} %{WORD:syslog_procid} %{WORD:syslog_msgid} - %{GREEDYDATA:message}" }
          overwrite => [ "message" ]
          add_field => { "original_syslog_message" => "%{[message]}" }
          tag_on_failure => ["_rfc5424_grokfailure"]
          add_tag => ["_rfc5424_groksucceed"]
        }

        # Now check the 'program' field populated by the grok filter above
        if [program] == "netstat-monitor" { 
          mutate {
            id => "netstat_tag_initializer"
            # Remove tags potentially added before this point
            remove_tag => ["raw_other_syslog", "_grokparsefailure_sysloginput", "_rfc5424_grokfailure"] 
            add_tag => [ "raw_netstat_syslog" ] 
          }

          # 上面成功
          
          if [message] =~ /^Name +Mtu +Network +Address +Ipkts +Ierrs/ {
            mutate {
              add_tag => [ "netstat_header_syslog" ]
              replace => { "type" => "netstat" }
            }
          } else if [message] =~ / +\d+ +/ {
            grok {
              id => "netstat_row_parser"
              match => { 
                "message" => "^%{DATA:interface}\s+%{NUMBER:mtu}\s+(?<network>(?:(?:<Link#\d+>\s+)|(?:[^\s]+)))\s+(?<address>[^\s]+)?\s+%{NUMBER:ipkts}\s+%{DATA:ierrs}\s+%{NUMBER:ibytes}\s+%{NUMBER:opkts}\s+%{DATA:oerrs}\s+%{NUMBER:obytes}\s+%{DATA:coll}"
              }
              tag_on_failure => ["_netstat_grokfailure"]
              add_tag => [ "raw_netstat_syslog" ]
            }
            
            if !("_netstat_grokfailure" in [tags]) {
              # 先处理可能包含"-"的字段，将其替换为"0"
              mutate {
                id => "netstat_dash_replacer"
                gsub => [
                  "ierrs", "^-$", "0",
                  "oerrs", "^-$", "0",
                  "coll", "^-$", "0"
                ]
              }
              
              # 然后将所有数值字段转换为整数
              mutate {
                id => "netstat_field_converter"
                convert => {
                  "mtu"    => "integer"
                  "ipkts"  => "integer"
                  "ibytes" => "integer"
                  "opkts"  => "integer"
                  "obytes" => "integer"
                  "ierrs"  => "integer"
                  "oerrs"  => "integer"
                  "coll"   => "integer"
                }
                
                add_tag => [ "processed_netstat" ]
                remove_tag => ["raw_netstat_syslog"]
                # Remove all potentially captured intermediate fields
                remove_field => [
                  "link_num", "net_address", "net_details", 
                  "hostname", "ipv6_address", 
                  "ipv4_net", "ipv4_address"
                ] 
              }
            } else {
               mutate { 
                  id => "netstat_parse_failure_marker"
                  add_tag => ["failed_netstat_parse"] 
               }
            }
          }
        } else {
          # Handle other syslog messages or RFC5424 parse failures
          if !("_rfc5424_grokfailure" in [tags]) {
             mutate { 
                id => "other_syslog_marker"
                add_tag => [ "raw_other_syslog_confirmed" ] 
                remove_tag => ["_grokparsefailure_sysloginput"]
             }
          } else {
             mutate { 
                id => "unknown_syslog_marker"
                add_tag => ["unknown_syslog_format"] 
                # Keep _grokparsefailure_sysloginput or _rfc5424_grokfailure tags for debugging
             }
          }
        }
      }
    }
    
    output {
      elasticsearch {
        id => "output_elasticsearch"
        hosts => ["http://elasticsearch.dci.svc.cluster.local:9200"]
        index => "logstash-netstat-%{+YYYY.MM.dd}"
      }
      
      if "_grokparsefailure" in [tags] or [parse_error] {
        stdout { 
          id => "output_debug_failures"
          codec => rubydebug { metadata => true } 
        }
      } else if [program] == "netstat-monitor" {
         stdout { 
           id => "output_debug_netstat" 
           codec => rubydebug { metadata => true } 
         }
      }
    }