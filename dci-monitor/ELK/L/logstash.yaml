---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: logstash
  namespace: dci
  labels:
    app: logstash
spec:
  replicas: 1
  selector:
    matchLabels:
      app: logstash
  template:
    metadata:
      labels:
        app: logstash
    spec:
      containers:
      - name: logstash
        image: cr.registry.pcloud.citic.com/dci/logstash:8.17.4
        resources:
          limits:
            memory: 4Gi
            cpu: "2"
          requests:
            memory: 1Gi
            cpu: "100m"
        ports:
        - containerPort: 5044
          name: beats
        - containerPort: 9600
          name: monitoring
        volumeMounts:
        - name: logstash-config
          mountPath: /usr/share/logstash/config/logstash.yml
          subPath: logstash.yml
        - name: logstash-pipeline
          mountPath: /usr/share/logstash/pipeline/logstash.conf
          subPath: logstash.conf
        # - name: shared-data
        #   mountPath: /opt/logstash/data
        #   subPath: logstash-data
        env:
        - name: LS_JAVA_OPTS
          value: "-Xmx1g -Xms1g"
      volumes:
      - name: logstash-config
        configMap:
          name: logstash-config
          items:
          - key: logstash.yml
            path: logstash.yml
      - name: logstash-pipeline
        configMap:
          name: logstash-pipeline
          items:
          - key: logstash.conf
            path: logstash.conf
      - name: shared-data
        persistentVolumeClaim:
          claimName: logstash-data-pvc  # 需要与PVC名称一致
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: logstash-config
  namespace: dci
data:
  logstash.yml: |
    api.http.host: "0.0.0.0"
    xpack.monitoring.elasticsearch.hosts: [ "http://elasticsearch.dci.svc.cluster.local:9200" ]
    xpack.monitoring.enabled: true
    path.config: /usr/share/logstash/pipeline
    pipeline.ordered: auto
    pipeline.workers: 2
    queue.type: memory
    queue.max_events: 1000
    api.http.port: 9600
    config.reload.automatic: true
    log.level: info
---
apiVersion: v1
kind: Service
metadata:
  name: logstash
  namespace: dci
  labels:
    app: logstash
spec:
  ports:
  - port: 5044
    name: beats
    protocol: TCP
    targetPort: 5044
    nodePort: 31001
  - port: 9600
    name: monitoring
    protocol: TCP
    targetPort: 9600
    nodePort: 31002
  - port: 8080
    name: http
    protocol: TCP
    targetPort: 8080
    nodePort: 30002
  - port: 5140
    name: syslog
    protocol: UDP
    targetPort: 5140
    nodePort: 30005
  selector:
    app: logstash
  type: NodePort