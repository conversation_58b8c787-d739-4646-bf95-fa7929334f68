---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kibana
  namespace: dci
  labels:
    app: kibana
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kibana
  template:
    metadata:
      labels:
        app: kibana
    spec:
      nodeSelector:
        kubernetes.io/hostname: cn-beijing-zx-d01.i-el501oq4f0r6keqcjubk
      containers:
      - name: kibana
        image: cr.registry.pcloud.citic.com/dci/kibana:8.17.3
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            memory: 4Gi  # 从2Gi提高到4Gi
            cpu: "2"
          requests:
            memory: 1Gi
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /api/status
            port: 5601
          initialDelaySeconds: 120
          periodSeconds: 10
        volumeMounts:
        - name: kibana-config  # 新增 volume 名称
          mountPath: /opt/bitnami/kibana/config/kibana.yml
          subPath: kibana.yml  # 必须与 ConfigMap 的 key 一致
        ports:
        - containerPort: 5601
          name: http
      volumes:
        - name: kibana-config
          configMap:
            name: kibana-conf # 使用已存在的 ConfigMap
            items:
              - key: kibana.yml  # 必须与 ConfigMap 中的 key 匹配
                path: kibana.yml
---
apiVersion: v1
kind: Service
metadata:
  name: kibana-service
  namespace: dci
spec:
  clusterIP: ************  # 需要保持原有clusterIP不变（请替换为实际值）
  selector:
    app: kibana
  ports:
    - protocol: TCP
      port: 5601
      targetPort: 5601
      nodePort: 30001
  type: NodePort