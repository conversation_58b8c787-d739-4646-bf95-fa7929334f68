
kubectl logs  $(kube<PERSON>l get pods -n dci -l app=kibana -o jsonpath='{.items[0].metadata.name}') -ndci




3. 创建包含服务账号令牌的 Secret
创建一个包含服务账号令牌的 Kubernetes Secret：

```bash
kubectl create secret generic kibana-service-token -n dci   --from-literal=ELASTICSEARCH_SERVICE_TOKEN="AAEAAWVsYXN0aWMva2liYW5hL2tpYmFuYS10b2tlbjpOTkhmWWdoSlR0S2k2VjJGdUJEZHhR"
```


4. 更新 Kibana 部署
更新 Kibana 部署以使用新创建的 Secret：


### 初始化ES的内置用户密码
kubectl exec -it elasticsearch-0 -n dci -- bash
/opt/bitnami/elasticsearch/bin/elasticsearch-setup-passwords auto

```bash
Changed password for user apm_system
PASSWORD apm_system = 5GEYfaoKQWrMpx6wLovT

Changed password for user kibana_system
PASSWORD kibana_system = 7rH4nCdRgsZ6BsAYlhKZ

Changed password for user kibana
PASSWORD kibana = 7rH4nCdRgsZ6BsAYlhKZ

Changed password for user logstash_system
PASSWORD logstash_system = zANmcQOtgTV7H3MnwlGh

Changed password for user beats_system
PASSWORD beats_system = Sk2NHJJLOwMsb8pvCgwp

Changed password for user remote_monitoring_user
PASSWORD remote_monitoring_user = FBv8f9i4w1jOGC1zl3Oc

Changed password for user elastic
PASSWORD elastic = QxinQJlOT0BVHEUvvEEM
```

# 创建包含 Kibana 密码的 Secret
# 将 YOUR_KIBANA_PASSWORD 替换为上面生成的 kibana_system 用户密码
kubectl create secret generic kibana-credentials -n dci \
  --from-literal=ELASTICSEARCH_PASSWORD="7rH4nCdRgsZ6BsAYlhKZ"
