apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-conf
  namespace: dci
data:
  kibana.yml: |
    server.host: "0.0.0.0"
    server.port: 5601
    elasticsearch.hosts: ["http://elasticsearch.dci.svc.cluster.local:9200"]
    # 增强连接配置
    elasticsearch.requestTimeout: 300000  # 从120秒提高到300秒
    elasticsearch.pingTimeout: 300000
    elasticsearch.healthCheck.delay: 30000  # 健康检查延迟
    elasticsearch.maxSockets: 50  # 增加连接池大小
    elasticsearch.sniffOnStart: false
    elasticsearch.sniffInterval: false  # 从true改为false（或使用数值如"5s"）
    # 日志配置
    logging.root.level: info
    i18n.locale: "zh-CN"