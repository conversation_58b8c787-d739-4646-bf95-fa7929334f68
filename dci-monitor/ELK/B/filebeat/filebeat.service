[Unit]
Description=Filebeat
Documentation=https://www.elastic.co/guide/en/beats/filebeat/current/index.html
Wants=network-online.target
After=network-online.target

[Service]
User=root
Group=root
DynamicUser=no
RemainAfterExit=yes
Environment="BEAT_PATH_CONFIG=/etc/filebeat"
Environment="BEAT_CONFIG_OPTS=-c /etc/filebeat/filebeat.yml"
Environment="BEAT_PATH_DATA=/var/lib/filebeat"
Environment="BEAT_PATH_HOME=/usr/share/filebeat"
Environment="BEAT_PATH_LOGS=/var/log/filebeat"
ExecStart=/usr/share/filebeat/bin/filebeat \
  --environment systemd \
  -E logging.files.keepfiles=7 \
  -E logging.files.rotateeverybytes=104857600 \
  $BEAT_CONFIG_OPTS
Restart=always
RestartSec=30
LimitNOFILE=65536
LimitMEMLOCK=infinity

[Install]
WantedBy=multi-user.target