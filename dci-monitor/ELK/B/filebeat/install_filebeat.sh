#!/bin/bash
# Filebeat二进制安装脚本

# 定义版本变量
VERSION="8.17.3"
FILEBEAT_DOWNLOAD_URL="https://artifacts.elastic.co/downloads/beats/filebeat/filebeat-${VERSION}-linux-x86_64.tar.gz"
FILEBEAT_SHA512_URL="https://artifacts.elastic.co/downloads/beats/filebeat/filebeat-${VERSION}-linux-x86_64.tar.gz.sha512"

SCRIPT_DIR="$( pwd )"
# 新增预检函数（在版本变量后添加）
check_root() {
    if [ "$(id -u)" != "0" ]; then
        echo "错误：必须使用root权限运行此脚本" >&2
        exit 1
    fi
}

check_existing_installation() {
    # 检查旧版本服务
    if systemctl is-active --quiet filebeat; then
        echo "发现正在运行的filebeat服务，请先执行：systemctl stop filebeat"
        exit 1
    fi
    
    # 检查残留进程
    if pgrep -x "filebeat" >/dev/null; then
        echo "发现正在运行的filebeat进程，请先停止相关进程"
        exit 1
    fi
    
    # 检查旧配置文件 - 添加用户确认
    if [ -d "/etc/filebeat" ] || [ -d "/usr/share/filebeat" ]; then
        read -p "检测到旧版本安装残留，是否删除? (y/n) " answer
        if [ "$answer" != "y" ]; then
            echo "安装已取消"
            exit 1
        fi
        echo "执行 rm -rf /etc/filebeat /usr/share/filebeat /var/lib/filebeat /var/log/filebeat"
        rm -rf /etc/filebeat /usr/share/filebeat /var/lib/filebeat /var/log/filebeat
    fi
}

# 新增依赖检查函数
check_dependencies() {
    if [ -f /etc/redhat-release ]; then
        if ! rpm -q libpcap >/dev/null; then  # 添加逻辑判断
            yum install -y libpcap || exit 1  # 自动安装依赖
        fi
    elif [ -f /etc/lsb-release ]; then
        if ! dpkg -l libpcap0.8 >/dev/null 2>&1; then
            apt-get update && apt-get install -y libpcap0.8 || exit 1
        fi
    fi
}

# 执行预检（在安装依赖前添加）
check_root
check_existing_installation
check_dependencies

# 先尝试本地安装
if [ ! -f "filebeat-${VERSION}-linux-x86_64.tar.gz" ]; then 
    # 下载并解压安装包
    cd /tmp || exit 1
    if [ ! -f "filebeat-${VERSION}-linux-x86_64.tar.gz" ]; then
        echo "正在下载 filebeat-${VERSION}-linux-x86_64.tar.gz..."
        curl -L -O --fail --retry 3 $FILEBEAT_DOWNLOAD_URL || exit 1
    fi
    if [ ! -f "filebeat.sha512" ]; then
        # 校验
        curl -L --fail --retry 3 $FILEBEAT_SHA512_URL -o filebeat.sha512 || exit 1
        sha512sum -c filebeat.sha512 || exit 1
    fi
else
    echo "filebeat-${VERSION}-linux-x86_64.tar.gz 已存在，跳过下载"
    mv filebeat-${VERSION}-linux-x86_64.tar.gz /tmp/
fi
tar xzvf filebeat-${VERSION}-linux-x86_64.tar.gz
# 目录创建
install -d -o root -g root /usr/share/filebeat/bin
install -d -o root -g root  /usr/share/filebeat/bin/data
install -d -o root -g root /var/{lib,log}/filebeat
install -d -o root -g root  /etc/filebeat/{modules.d,module}
chmod 750 /usr/share/filebeat/bin/data  # 新增data目录权限设置

cp -r filebeat-${VERSION}-linux-x86_64/module/* /etc/filebeat/module/
cp -r filebeat-${VERSION}-linux-x86_64/modules.d/* /etc/filebeat/modules.d/

BEAT_DIR="filebeat-${VERSION}-linux-x86_64"
if [ ! -d "${BEAT_DIR}" ]; then
    echo "错误：解压目录${BEAT_DIR}不存在" >&2
    exit 1
fi

# 使用install命令复制文件
install -Dm755 "${BEAT_DIR}/filebeat" "/usr/share/filebeat/bin/filebeat"
install -Dm644 "${BEAT_DIR}/fields.yml" "/etc/filebeat/fields.yml"
# 添加二进制存在性检查
if [ ! -x "/usr/share/filebeat/bin/filebeat" ]; then
    echo "错误：filebeat可执行文件未正确安装" >&2
    ls -l /usr/share/filebeat/bin/ >&2
    exit 1
fi

# 设置权限
chown -R root:root /usr/share/filebeat /var/lib/filebeat /var/log/filebeat
find /usr/share/filebeat -type d -exec chmod 755 {} \;
chmod 750 /var/lib/filebeat /var/log/filebeat

# 检查并部署服务单元文件
if [ ! -f "${SCRIPT_DIR}/filebeat.service" ]; then
    echo "错误：缺少服务单元文件，请确保以下文件存在：" >&2
    echo -e "\033[31m├── ${SCRIPT_DIR}/filebeat.service\033[0m" >&2
    echo -e "\033[31m└── ${SCRIPT_DIR}/install_filebeat.sh\033[0m" >&2
    exit 1
fi

echo "正在部署服务单元文件..."
install -D -m 644 -o root -g root \
    "${SCRIPT_DIR}/filebeat.service" \
    /etc/systemd/system/filebeat.service

# 创建配置文件（在创建目录后添加）
if [ ! -d "/etc/filebeat" ]; then
    mkdir -p /etc/filebeat
fi

# 获取脚本所在目录
cd $SCRIPT_DIR
# 强化配置文件检查
if [ ! -f "${SCRIPT_DIR}/filebeat.yml" ]; then
    echo "错误：配置文件未找到，请确保以下文件与安装脚本在同一目录：" >&2
    echo -e "\033[31m├── ${SCRIPT_DIR}/filebeat.yml\033[0m" >&2
    echo -e "\033[31m└── ${SCRIPT_DIR}/install_filebeat.sh\033[0m" >&2
    exit 1
fi

# 复制时使用绝对路径
echo "正在部署配置文件..."
cp -v "${SCRIPT_DIR}/filebeat.yml" /etc/filebeat/

# 在设置权限后添加（修正命令执行顺序）
# 修改文件所有权（约第158行）
chown -R root:root /usr/share/filebeat /var/lib/filebeat /var/log/filebeat

# 修改密钥库创建命令（约第174行）
/usr/share/filebeat/bin/filebeat keystore create --force
/usr/share/filebeat/bin/filebeat setup
# 新增服务管理命令（在权限设置后添加）
systemctl daemon-reload

# 配置文件检查函数
check_config() {
    # 检查目录权限
    if [ ! -d "/etc/filebeat" ]; then
        echo "配置目录不存在，创建目录..."
        mkdir -p /etc/filebeat
    fi
    
    chmod 755 /etc/filebeat
    
    # 检查配置文件
    if [ ! -f "/etc/filebeat/filebeat.yml" ]; then
        echo "配置文件不存在"
        exit 1
    fi
    
    # 检查配置文件权限
    if [ "$(stat -c %a /etc/filebeat/filebeat.yml)" != "640" ]; then
        echo "修正配置文件权限..."
        chmod 640 /etc/filebeat/filebeat.yml
    fi
    
    # 检查所有权
    if [ "$(stat -c %U:%G /etc/filebeat/filebeat.yml)" != "root:filebeat" ]; then
        echo "修正配置文件所有权..."
        chown root:filebeat /etc/filebeat/filebeat.yml
    fi
    
    # 测试配置
    echo "验证配置文件..."
    if ! /usr/share/filebeat/bin/filebeat test config -c /etc/filebeat/filebeat.yml -e; then
        echo "配置文件验证失败"
        exit 1
    fi
}

# 在启动服务前调用
check_config

# 在服务启动前添加配置文件验证
if [ ! -f "/usr/share/filebeat/bin/filebeat" ]; then
    echo "关键错误：filebeat二进制文件未正确安装" >&2
    echo "请检查以下路径是否存在：" >&2
    echo "/usr/share/filebeat/bin/filebeat" >&2
    exit 1
fi

# 启动服务前添加确认
read -p "是否现在启动Filebeat服务？(y/n) " start_service
if [ "$start_service" = "y" ]; then
    systemctl start filebeat
    # 添加启动验证
    if ! systemctl is-active --quiet filebeat; then
        echo "Filebeat服务启动失败，请检查日志: journalctl -u filebeat -n 30"
        exit 1
    fi
    echo "Filebeat服务已启动，可以使用 journalctl -u filebeat -n 30 命令查看状态"
    
    # 添加进程检查
    if ! pgrep -x "filebeat" >/dev/null; then
        echo "未检测到Filebeat进程，请检查日志"
        exit 1
    fi
else
    echo "Filebeat服务未启动，您可以稍后手动启动"
fi

# 添加日志检查
if [ -f "/usr/share/filebeat/logs/filebeat" ]; then
    echo "请检查日志文件: /usr/share/filebeat/logs/filebeat"
fi
