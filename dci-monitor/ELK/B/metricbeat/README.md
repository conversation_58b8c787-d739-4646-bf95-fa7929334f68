# metricbeat

配置文件参考：https://github.com/elastic/beats/blob/main/metricbeat/metricbeat.yml

kibana-service.dci.svc.cluster.local:5601 地址由以下组成部分构成：

```bash
kibana-service    → 服务名称 (metadata.name)
      .dci        → 命名空间 (metadata.namespace)
      .svc        → 服务资源标识符
      .cluster.local → 集群默认域名
      :5601       → 服务端口 (spec.ports[0].port)
```
## k8s

进入容器命令：

```bash
kubectl exec -it -n dci metricbeat-5mbk5 -- /bin/bash
```

