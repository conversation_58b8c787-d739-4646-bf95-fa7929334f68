apiVersion: v1
kind: ConfigMap
metadata:
  name: metricbeat-config
  namespace: dci
data:
  metricbeat.yml: |-
    metricbeat.config.modules:
      path: ${path.config}/modules.d/*.yml
      reload.enabled: true

    setup.kibana:
      host: "kibana-service.dci.svc.cluster.local:5601"

    output.elasticsearch:
      hosts: ["elasticsearch.dci.svc.cluster.local:9200"]
      # 使用与Filebeat相同的索引命名规则
      indices:
        - index: "metricbeat-%{[agent.version]}-%{+yyyy.MM.dd}"

    processors:
      - add_cloud_metadata: ~
      - add_kubernetes_metadata: ~
    
    metricbeat.modules:
      - module: elasticsearch
        metricsets:
          - node
          - node_stats
          #- index
          #- index_recovery
          #- index_summary
          #- ingest_pipeline
          #- shard
          #- ml_job
        period: 10s
        hosts: ["elasticsearch.dci.svc.cluster.local:9200"]
        xpack.enabled: true
        scope: cluster
        # 禁用本地节点检测
        cluster_stats:
          endpoints: ["http://elasticsearch.dci.svc.cluster.local:9200"]
        node_stats:
          endpoints: ["http://elasticsearch.dci.svc.cluster.local:9200"]
          
        #username: "elastic"
        #password: "changeme"
        #api_key: "foo:bar"
        #ssl.certificate_authorities: ["/etc/pki/root/ca.pem"]

        #index_recovery.active_only: true
        #ingest_pipeline.processor_sample_rate: 0.25
        #xpack.enabled: false
        #scope: node

    setup.ilm:
      enabled: true
      policy_name: "metricbeat-policy"
      rollover_alias: "metricbeat"
    
