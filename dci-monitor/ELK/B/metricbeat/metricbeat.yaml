---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: metricbeat
  namespace: dci
  labels:
    k8s-app: metricbeat
spec:
  selector:
    matchLabels:
      k8s-app: metricbeat
  template:
    metadata:
      labels:
        k8s-app: metricbeat
    spec:
      serviceAccountName: metricbeat
      terminationGracePeriodSeconds: 30
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      containers:
        - name: metricbeat
          image: *************:6000/metricbeat:8.17.3
          resources:
            limits:
              memory: "200Mi"
              cpu: "200m"
            requests:
              memory: "100Mi"
              cpu: "100m"
          args: ["-c", "/usr/share/metricbeat/metricbeat.yml", "-e"]
          env:
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: PATH_CONFIG
              value: "/usr/share/metricbeat"
          volumeMounts:
            - name: metricbeat-config
              mountPath: /usr/share/metricbeat/metricbeat.yml
              subPath: metricbeat.yml
          securityContext:
            runAsUser: 0
            capabilities:
              add: ["NET_RAW"]
      volumes:
        - name: metricbeat-config
          configMap:
            name: metricbeat-config

# 补充完整的ClusterRole配置 ▼
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: metricbeat
  labels:
    k8s-app: metricbeat
rules:
  - apiGroups: [""]
    resources:
      - nodes
      - nodes/stats       # 新增监控指标权限
      - pods
      - services
      - endpoints
      - events
    verbs: ["get", "list", "watch"]
  - apiGroups: ["extensions"]
    resources:
      - deployments
      - replicasets
    verbs: ["get", "list", "watch"]

# 确保ClusterRoleBinding正确引用 ▼
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: metricbeat
subjects:
  - kind: ServiceAccount
    name: metricbeat
    namespace: dci        # 必须与DaemonSet的namespace一致
roleRef:
  kind: ClusterRole
  name: metricbeat
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: metricbeat
  labels:
    k8s-app: metricbeat
rules:
  - apiGroups: [""]
    resources:
      - nodes
      - pods
      - services
      - endpoints  # ✅ 新增必要权限
      - events
    verbs: ["get", "list", "watch"]  # ✅ 最小化权限原则
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: metricbeat
  namespace: dci
  labels:
    k8s-app: metricbeat
