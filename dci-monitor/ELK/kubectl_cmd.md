# 用到的 kubectl 命令记录
## 操作

### 命令集合

```bash
# 验证YAML语法
yamllint nfs_dci/storageclass.yaml
```

```bash
kubectl delete -f elasticsearch.yaml -n dci
kubectl delete -f elasticsearch.yml  -n dci
kubectl delete -f service.yaml -n dci

delete pvc elasticsearch-data-elasticsearch-0 elasticsearch-data-elasticsearch-1 elasticsearch-data-elasticsearch-2 -ndci

kubectl apply -f elasticsearch.yml -n dci
kubectl apply -f service.yaml -n dci
kubectl apply -f elasticsearch.yaml -n dci

kubectl get pods -n dci -l app=elasticsearch
kubectl logs elasticsearch-0 -n dci

kubectl describe pod elasticsearch-0 -n dci
```

### 强制重启

```bash
kubectl replace -f elasticsearch.yml --force
kubectl apply -f service.yaml -n dci
kubectl replace -f elasticsearch.yaml --force
```

```bash
kubectl get pods -n dci -l app=es-cluster
kubectl logs esnode-0 -n dci
```

### kubectl 主要命令

#### 节点资源利用率
```bash
kubectl describe nodes
```

#### StorageClass 配置
```bash
kubectl get storageclass
```