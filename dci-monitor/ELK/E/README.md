# ELASTICSEARCH

## 命令解释

```bash
I have no name!@elasticsearch-0:/$ ps aux | grep elas
1001           1  0.3  0.6 2615844 99188 ?       Ssl  06:26   0:07 /opt/bitnami/java/bin/java -Xms4m -Xmx64m -XX:+UseSerialGC -Dcli.name=server -Dcli.script=/opt/bitnami/elasticsearch/bin/elasticsearch -Dcli.libs=lib/tools/server-cli -Des.path.home=/opt/bitnami/elasticsearch -Des.path.conf=/opt/bitnami/elasticsearch/config -Des.distribution.type=tar -cp /opt/bitnami/elasticsearch/lib/*:/opt/bitnami/elasticsearch/lib/cli-launcher/* org.elasticsearch.launcher.CliToolLauncher -p /opt/bitnami/elasticsearch/tmp/elasticsearch.pid
```

```bash
# 完整命令参数分解
/opt/bitnami/java/bin/java 
  -Xms4m              # 初始堆内存4MB（极小，生产环境需调整）
  -Xmx64m             # 最大堆内存64MB（严重不足，建议2G+）
  -XX:+UseSerialGC    # 使用串行垃圾回收器（适用于单核环境）
  
  # Elasticsearch专用参数
  -Dcli.name=server
  -Dcli.script=/opt/bitnami/elasticsearch/bin/elasticsearch
  -Dcli.libs=lib/tools/server-cli
  
  # 路径配置
  -Des.path.home=/opt/bitnami/elasticsearch   # ES安装目录
  -Des.path.conf=/opt/bitnami/elasticsearch/config  # 配置目录
  -Des.distribution.type=tar     # 安装包类型
  
  # 类路径配置
  -cp /opt/bitnami/elasticsearch/lib/*:/opt/bitnami/elasticsearch/lib/cli-launcher/*
  
  # 主类入口
  org.elasticsearch.launcher.CliToolLauncher 
  
  # 进程ID文件
  -p /opt/bitnami/elasticsearch/tmp/elasticsearch.pid
```

## 默认配置

```bash
elasticsearch-0:/opt/bitnami/elasticsearch/config$ cat elasticsearch.yml
http:
  port: "9200"
path:
  data: /bitnami/elasticsearch/data
transport:
  port: "9300"
network:
  host: ************
  publish_host: ************
  bind_host: 0.0.0.0
node:
  name: elasticsearch-0
discovery:
  type: single-node
xpack:
  security:
```
## ES SQL

```bash
# This request searches all of your indices.
GET /_search
{
  // The query parameter indicates query context.
  "query": {
    "match_all": {} // Matches all documents.
  }
}
```

/opt/bitnami/elasticsearch/bin/elasticsearch-certutil cert \
  --ca /tmp/certs/elastic-ca.p12 \
  --ca-pass "" \
  --name elasticsearch-0 \
  --dns elasticsearch-0.elasticsearch.dci.svc.cluster.local \
  --ip 127.0.0.1 \
  --ip *********** \
  --ip *********** \ 
  --out /tmp/certs/elastic-certificates.p12 \
  --pass ""

kubectl cp -n dci elastic-cert-gen:/tmp/certs/elastic-stack-ca.p12 ca-0331.crt

kubectl cp -n dci elastic-cert-gen:/tmp/certs/elastic-certificates.p12 elastic-certificates-0331.p12

kubectl create secret generic elasticsearch-certificates -n dci \
  --from-file=elastic-certificates.p12=./elastic-certificates-0331.p12 \
  --from-file=ca.crt=./ca-0331.crt



# 生成证书命令（在临时Pod中执行）
/opt/bitnami/elasticsearch/bin/elasticsearch-certutil cert \
  --ca /tmp/certs/elastic-stack-ca.p12 \
  --ca-pass "" \
  --name elasticsearch-cluster \
  --dns elasticsearch.dci.svc.cluster.local \        
  --dns elasticsearch-0.elasticsearch.dci.svc.cluster.local \ 
  --dns elasticsearch-1.elasticsearch.dci.svc.cluster.local \
  --dns elasticsearch-2.elasticsearch.dci.svc.cluster.local \
  --dns elasticsearch \                             
  --ip 127.0.0.1 \                                  
  --ip *********** \                                
  --out /tmp/certs/elastic-certificates.p12 \
  --pass ""



## 解决 Elasticsearch 证书格式问题

使用 Elasticsearch 容器生成证书
让我们使用 Elasticsearch 自己的工具生成证书，并确保它有正确的格式：
1. 创建临时 Pod 并以 root 用户运行
```bash
# 创建临时 Pod 并以 root 用户运行（新格式）
kubectl run elastic-cert-gen --image=cr.registry.pcloud.citic.com/dci/elasticsearch:8.17.3 -n dci \
  --overrides='{"spec":{"securityContext":{"runAsUser":0}}}' \
  --restart=Never --command -- sleep 720000

# 等待 Pod 运行
kubectl wait --for=condition=Ready pod/elastic-cert-gen -n dci --timeout=60s
```

2. 在临时 Pod 中生成证书
```bash
# 进入 Pod
kubectl exec -it elastic-cert-gen -n dci -- bash

# 在 Pod 内执行以下命令
mkdir -p /tmp/certs
cd /tmp/certs

# 使用 Elasticsearch 工具生成证书
/opt/bitnami/elasticsearch/bin/elasticsearch-certutil ca --out /tmp/certs/elastic-ca.p12 --pass ""
/opt/bitnami/elasticsearch/bin/elasticsearch-certutil cert --ca /tmp/certs/elastic-ca.p12 --ca-pass "" --out /tmp/certs/elastic-certificates.p12 --pass ""




/opt/bitnami/elasticsearch/bin/elasticsearch-certutil cert \
--ca /tmp/certs/elastic-ca.p12  \
--ca-pass "" \
--name elasticsearch-cluster \
--dns elasticsearch.dci.svc.cluster.local \
--dns elasticsearch-0.elasticsearch.dci.svc.cluster.local \
--dns elasticsearch-1.elasticsearch.dci.svc.cluster.local \
--dns elasticsearch-2.elasticsearch.dci.svc.cluster.local \
--dns elasticsearch \
--ip 127.0.0.1 \
--ip *********** \
--out /tmp/certs/elastic-certificates.p12 \
--pass ""


# 检查

keytool -list -v -keystore /tmp/certs/elastic-certificates.p12 -storepass ''


# 确认证书已生成
ls -la /tmp/certs/

# 检查证书内容
keytool -list -keystore /tmp/certs/elastic-certificates.p12 -storepass ""

# 不要退出 Pod
```
3. 从临时 Pod 复制证书
```bash
kubectl cp -n dci elastic-cert-gen:/tmp/certs/elastic-certificates.p12 elastic-certificates-0331.p12

kubectl cp -n dci elastic-cert-gen:/tmp/certs/elastic-ca.p12 ca-0331-2.crt

kubectl cp -n dci elastic-cert-gen:/tmp/certs/ca/ca.key ca-0331.key


4. 重新创建 Secret
```bash
# 删除旧的 Secret
kubectl delete secret elasticsearch-certificates -n dci

# 创建新的 Secret
kubectl create secret generic elasticsearch-certificates -n dci  --from-file=elastic-certificates.p12=./elasticsearch.p12 \
--from-file=ca.crt=./ca.crt

###
kubectl create secret generic elasticsearch-certificates -n dci  --from-file=elastic-certificates.p12=./elastic-certificates.p12 \
--from-file=ca.crt=./elastic-ca.p12

# 获取所有Elasticsearch节点的Pod IP和所在Node IP（精确匹配）
kubectl get pods -n dci -l app=elasticsearch -o jsonpath='{range .items[*]}{"Pod名称: "}{.metadata.name}{"\n"}{"Pod IP: "}{.status.podIP}{"\n"}{"所在节点IP: "}{.status.hostIP}{"\n\n"}{end}'


# 验证 Secret 创建成功
kubectl get secret elasticsearch-certificates -n dci

# 删除临时 Pod
kubectl delete pod elastic-cert-gen -n dci
```
5. 重启 Elasticsearch
```bash
kubectl delete pod -n dci elasticsearch-0 elasticsearch-1 elasticsearch-2
kubectl apply -f E/elasticsearch.yaml -ndci
```

## 有 永久存储PVC 的情况下，重新部署 ES 的方式 (删存储)

### 1 删 pod
```bash
kubectl delete -f E/elasticsearch.yaml
```

### 2 删PVC

```bash
[root@iZel501oq4f0r6keqcjubkZ E]# kubectl get pvc -ndci
NAME                                 STATUS   VOLUME                                     CAPACITY   ACCESS MODES   STORAGECLASS      AGE
elasticsearch-data-elasticsearch-0   Bound    pvc-5389ef6f-0df9-11f0-b1bf-00163e0601c3   10Gi       RWO            dci-nfs-storage   41m
elasticsearch-data-elasticsearch-1   Bound    pvc-5e6b9271-0df9-11f0-b1bf-00163e0601c3   10Gi       RWO            dci-nfs-storage   41m
elasticsearch-data-elasticsearch-2   Bound    pvc-6783af14-0df9-11f0-b1bf-00163e0601c3   10Gi       RWO            dci-nfs-storage   40m
[root@iZel501oq4f0r6keqcjubkZ E]# kubectl delete pvc elasticsearch-data-elasticsearch-0 -ndci 
persistentvolumeclaim "elasticsearch-data-elasticsearch-0" deleted
[root@iZel501oq4f0r6keqcjubkZ E]# kubectl delete pvc elasticsearch-data-elasticsearch-1 -ndci 
persistentvolumeclaim "elasticsearch-data-elasticsearch-1" deleted
[root@iZel501oq4f0r6keqcjubkZ E]# kubectl delete pvc elasticsearch-data-elasticsearch-2 -ndci 
persistentvolumeclaim "elasticsearch-data-elasticsearch-2" deleted
```

### 3 删PV

```bash
[root@iZel501oq4f0r6keqcjubkZ E]# kubectl get pv -ndci
NAME                                       CAPACITY   ACCESS MODES   RECLAIM POLICY   STATUS     CLAIM                                    STORAGECLASS      REASON   AGE
pvc-5389ef6f-0df9-11f0-b1bf-00163e0601c3   10Gi       RWO            Delete           Released   dci/elasticsearch-data-elasticsearch-0   dci-nfs-storage            41m
pvc-5e6b9271-0df9-11f0-b1bf-00163e0601c3   10Gi       RWO            Delete           Released   dci/elasticsearch-data-elasticsearch-1   dci-nfs-storage            41m
pvc-6783af14-0df9-11f0-b1bf-00163e0601c3   10Gi       RWO            Delete           Released   dci/elasticsearch-data-elasticsearch-2   dci-nfs-storage            41m
[root@iZel501oq4f0r6keqcjubkZ E]# kubectl delete pv pvc-5389ef6f-0df9-11f0-b1bf-00163e0601c3 -ndci
persistentvolume "pvc-5389ef6f-0df9-11f0-b1bf-00163e0601c3" deleted
[root@iZel501oq4f0r6keqcjubkZ E]# kubectl delete pv pvc-5e6b9271-0df9-11f0-b1bf-00163e0601c3 -ndci
persistentvolume "pvc-5e6b9271-0df9-11f0-b1bf-00163e0601c3" deleted
[root@iZel501oq4f0r6keqcjubkZ E]# kubectl delete pv pvc-6783af14-0df9-11f0-b1bf-00163e0601c3 -ndci
persistentvolume "pvc-6783af14-0df9-11f0-b1bf-00163e0601c3" deleted
```

### 4 删存储文件

```bash
rm -rf /mnt/dci/dci-elasticsearch-data-elasticsearch-*
```
