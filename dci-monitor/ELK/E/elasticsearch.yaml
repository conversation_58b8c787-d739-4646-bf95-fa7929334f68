---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: elasticsearch
  namespace: dci
spec:
  serviceName: elasticsearch
  replicas: 3
  selector:
    matchLabels:
      app: elasticsearch
  template:
    metadata:
      labels:
        app: elasticsearch
    spec:
      securityContext:
        fsGroup: 1000
      initContainers:
        - name: init-sysctl
          image: cr.registry.pcloud.citic.com/dci/busybox:latest
          imagePullPolicy: IfNotPresent
          securityContext:
            privileged: true
          command: ["sysctl", "-w", "vm.max_map_count=262144"]
      imagePullSecrets:
        - name: dci-images-key
      containers:
        - name: elasticsearch
          securityContext:  # 将 capabilities 移动到 securityContext 内
            capabilities:  # 添加内存锁定能力
              add: ["IPC_LOCK"]
          image: cr.registry.pcloud.citic.com/dci/elasticsearch:8.17.3
          ports:
            - containerPort: 9200
            - containerPort: 9300
          volumeMounts:
            - name: elasticsearch-data
              mountPath: /bitnami/elasticsearch
              subPath: ""
              readOnly: false
            - name: elasticsearch-config
              mountPath: /opt/bitnami/elasticsearch/config/elasticsearch.yml
              subPath: elasticsearch.yml
            - name: elasticsearch-logs
              mountPath: /opt/bitnami/elasticsearch/logs
          env:
            - name: ES_JAVA_OPTS
              value: "-Xms1g -Xmx1g -XX:+UseG1GC -XX:G1ReservePercent=25 -XX:MaxGCPauseMillis=100"
            - name: POD_IP  # 添加 POD_IP 环境变量
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
          resources: # Add resource requests and limits
            requests:
              memory: "1.5Gi"
              cpu: "100m"
            limits:
              memory: "3Gi"
              cpu: "2G"
        - name: filebeat-es-sidecar
          image: cr.registry.pcloud.citic.com/dci/filebeat:8.17.3 
          volumeMounts:
            - name: elasticsearch-logs
              mountPath: /opt/bitnami/elasticsearch/logs
            - name: filebeat-config  # 新增 volume 名称
              mountPath: /usr/share/filebeat/filebeat.yml
              subPath: filebeat.yml  # 必须与 ConfigMap 的 key 一致
          env:
            - name: ELASTICSEARCH_HOSTS
              value: '["http://elasticsearch.dci.svc.cluster.local:9200"]'
            - name: POD_IP  # 添加 POD_IP 环境变量
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
      volumes:
        - name: elasticsearch-config
          configMap:
            name: elasticsearch-conf
            items:
              - key: elasticsearch.yml
                path: elasticsearch.yml
        - name: filebeat-config
          configMap:
            name: filebeat-es-sider-conf  # 使用已存在的 ConfigMap
            items:
              - key: filebeat.yml  # 必须与 ConfigMap 中的 key 匹配
                path: filebeat.yml
        - name: elasticsearch-logs  # 新增日志存储卷
          emptyDir: {}
  volumeClaimTemplates:
    - metadata:
        name: elasticsearch-data
        annotations:
          volume.beta.kubernetes.io/storage-class: "dci-nfs-storage"  # 确保显式指定存储类
      spec:
        accessModes: [ "ReadWriteOnce" ]
        storageClassName: dci-nfs-storage  # 新增明确声明
        resources:
          requests:
            storage: 10Gi
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: elasticsearch-conf
  namespace: dci
data:
  elasticsearch.yml: |
    cluster.name: dci-cluster
    node.name: ${HOSTNAME}
    path.data: /bitnami/elasticsearch/data
    path.logs: /opt/bitnami/elasticsearch/logs  # 必须与挂载路径一致
    network.host: 0.0.0.0
    http.port: 9200
    transport.port: 9300
    discovery.seed_hosts: 
      - "elasticsearch-0.elasticsearch.dci.svc.cluster.local:9300"
      - "elasticsearch-1.elasticsearch.dci.svc.cluster.local:9300"
      - "elasticsearch-2.elasticsearch.dci.svc.cluster.local:9300"
    cluster.initial_master_nodes: 
      - "elasticsearch-0"
      - "elasticsearch-1"
      - "elasticsearch-2"
    xpack.security.enabled: false
    action.auto_create_index: ".kibana*,.kibana_*,.tasks,.apm*,.alerts*,.internal.alerts-*,logstash-*,filebeat-*,metricbeat-*"
    indices.lifecycle.history_index_enabled: true
    stack.templates.enabled: true
    cluster:
      routing:
        allocation:
          enable: all                  # 启用所有分片分配类型
          disk:
            threshold_enabled: true    # 启用磁盘水位检测
            watermark:
              low: "80%"               # 低水位线（停止分配新分片）
              high: "85%"              # 高水位线（触发分片迁移）
    # 增加详细日志
    logger.org.elasticsearch.http: DEBUG
    logger.org.elasticsearch.transport: DEBUG
    network.publish_host: ${POD_IP} # 使用实际 Pod IP
    # 优化I/O操作
    thread_pool.write.queue_size: 1000
    indices.memory.index_buffer_size: 10%
    indices.fielddata.cache.size: 10%
    indices.queries.cache.size: 5%
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: filebeat-es-sider-conf
  namespace: dci
data:
  filebeat.yml: |
    filebeat.inputs:
    - type: container
      id: my-container-fb-id
      enabled: false
      paths:
        - /var/log/containers/*.log
    # filestream is an input for collecting log messages from files.
    - type: filestream
      # Unique ID among all inputs, an ID is required.
      id: my-filestream-id
      # Change to true to enable this input configuration.
      enabled: false
      # Paths that should be crawled and fetched. Glob based paths.
      paths:
        - /var/log/*.log
    # ============================== Filebeat modules ==============================
    filebeat.config.modules:
      # Glob pattern for configuration loading
      path: ${path.config}/modules.d/*.yml
      # Set to true to enable config reloading
      reload.enabled: true
      # Period on which files under path should be checked for changes
      reload.period: 120s
    #==========================  Modules configuration =============================
    filebeat.modules:
    #---------------------------- Elasticsearch Module ----------------------------
    - module: elasticsearch
      # Server log
      server:
        enabled: true
        # Set custom paths for the log files. If left empty,
        # Filebeat will choose the paths depending on your OS.
        var.paths:
          - /opt/bitnami/elasticsearch/logs/*.log
          - /opt/bitnami/elasticsearch/logs/*_server.json
      gc:
        enabled: true
        # Set custom paths for the log files. If left empty,
        # Filebeat will choose the paths depending on your OS.
        var.paths:
          - /opt/bitnami/elasticsearch/logs/gc.log.[0-9]*
          - /opt/bitnami/elasticsearch/logs/gc.log
      audit:
        enabled: false
        # Set custom paths for the log files. If left empty,
        # Filebeat will choose the paths depending on your OS.
        var.paths:
          - /opt/bitnami/elasticsearch/logs/*_access.log
          - /opt/bitnami/elasticsearch/logs/*_audit.json

      slowlog:
        enabled: true
        var.paths:
          - /opt/bitnami/elasticsearch/logs/*_index_search_slowlog.log
          - /opt/bitnami/elasticsearch/logs/*_index_indexing_slowlog.log
          - /opt/bitnami/elasticsearch/logs/*_index_search_slowlog.json
          - /opt/bitnami/elasticsearch/logs/*_index_indexing_slowlog.json  # JSON logs
      deprecation:
        enabled: false
        # Set custom paths for the log files. If left empty,
        # Filebeat will choose the paths depending on your OS.
        #var.paths:
    # ======================= Elasticsearch template setting =======================
    setup.template.settings:
      index.number_of_shards: 1
      #index.codec: best_compression
      #_source.enabled: false
    # =================================== Kibana ===================================
    # Starting with Beats version 6.0.0, the dashboards are loaded via the Kibana API.
    # This requires a Kibana endpoint configuration.
    setup.kibana:
      # 修改为集群内域名格式 ▼
      host: "*************:5601"
    # ================================== Outputs ===================================
    output.elasticsearch:
      # 修改为 Elasticsearch 服务域名 ▼
      hosts: ["http://elasticsearch.dci.svc.cluster.local:9200"]  
      indices:
        - index: "filebeat-%{[agent.version]}-%{+yyyy.MM.dd}"

      # Performance preset - one of "balanced", "throughput", "scale",
      # "latency", or "custom".
      preset: balanced

      # Protocol - either `http` (default) or `https`.
      #protocol: "https"

      # Authentication credentials - either API key or username/password.
      #api_key: "id:api_key"
      #username: "elastic"
      #password: "changeme"

    # ------------------------------ Logstash Output -------------------------------
    #output.logstash:
      # The Logstash hosts
      #hosts: ["localhost:5044"]

      # Optional SSL. By default is off.
      # List of root certificates for HTTPS server verifications
      #ssl.certificate_authorities: ["/etc/pki/root/ca.pem"]

      # Certificate for SSL client authentication
      #ssl.certificate: "/etc/pki/client/cert.pem"

      # Client Certificate Key
      #ssl.key: "/etc/pki/client/cert.key"

    # ================================= Processors =================================
    processors:
      - add_host_metadata:
          when.not.contains.tags: forwarded
      - add_cloud_metadata: ~
      - add_docker_metadata: ~
      - add_kubernetes_metadata: ~

    # ================================== Logging ===================================

    # Sets log level. The default log level is info.
    # Available log levels are: error, warning, info, debug
    logging.level: info
    logging.to_files: true
    logging.files:
      path: /var/log/filebeat
      name: filebeat.log
      keepfiles: 7
      permissions: 0644
    # At debug level, you can selectively enable logging only for some components.
    # To enable all selectors, use ["*"]. Examples of other selectors are "beat",
    # "publisher", "service".
    logging.selectors: ["*"]

    # ================================= Migration ==================================

    # This allows to enable 6.7 migration aliases
    #migration.6_to_7.enabled: true
    setup.ilm:
      enabled: false
    setup.template:
      name: "filebeat"
      pattern: "filebeat-*"

