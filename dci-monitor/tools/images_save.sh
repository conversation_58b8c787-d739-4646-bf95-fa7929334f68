#!/bin/bash

# 检查是否为 root 用户
if [ "$(id -u)" -eq 0 ]; then
  echo "错误：此脚本不允许以 root 用户身份运行。"
  exit 1
fi

# 定义镜像输出目录
OUTPUT_DIR="./images_hub"

# 创建输出目录（如果不存在）
mkdir -p "$OUTPUT_DIR"

# 定义镜像列表（格式：REPOSITORY:TAG）
IMAGES=(
  "10.249.149.56:6000/logstash:8.17.3"
  "10.249.149.56:6000/kibana:latest"
  "10.249.149.56:6000/elasticsearch:latest"
  "10.249.149.56:6000/filebeat:8.17.3"
  "10.249.149.56:6000/busybox:latest"
  "docker.elastic.co/logstash/logstash:8.17.4"
  "bitnami/kafka:4.0.0"
  "docker.1ms.run/prom/prometheus:v3.3.1"
)

# 登录私有仓库（若需要认证）
# docker login 10.249.149.56:6000 -u <用户名> -p <密码>

# 遍历镜像列表下载并打包
for image in "${IMAGES[@]}"; do
  # 生成安全文件名（替换特殊字符）并指定输出目录
  filename="$OUTPUT_DIR/$(echo "$image" | sed 's/[:\/]/_/g').tar"
  
  # 首先检查目标文件是否存在
  if [ -f "$filename" ]; then
    echo "文件 $filename 已存在,跳过下载和打包"
    continue # 跳到下一个镜像
  fi

  # 文件不存在，则尝试下载
  echo "正在下载镜像: $image"
  docker pull "$image"
  
  if [ $? -eq 0 ]; then
    # 下载成功，进行打包
    echo "正在打包镜像到: $filename"
    docker save -o "$filename" "$image"
    echo "镜像 $image 已保存为 $filename"
  else
    # 下载失败
    echo "错误: 下载镜像 $image 失败"
  fi
done

echo "操作完成！保存在 $OUTPUT_DIR 目录中。"
