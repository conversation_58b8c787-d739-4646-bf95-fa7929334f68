# Kafka网络检查工具

## 工具说明

Kafka网络检查工具（`kafka_connection_checker.sh`）是一个用于验证DCI Monitor Agent与Kafka集群连接状态的诊断工具。该工具可以帮助运维人员和开发人员快速定位Kafka连接问题，提供详细的网络诊断信息和问题解决建议。

## 主要功能

- **连接测试**：验证与Kafka引导服务器的基本连接
- **DNS检查**：测试主机名解析是否正常
- **网络诊断**：检测网络连通性、路由和防火墙状态
- **元数据获取**：测试是否能从Kafka集群获取元数据（常见的advertised.listeners问题诊断）
- **消息生产**：测试向指定主题发送消息
- **消息消费**：测试从指定主题接收消息
- **诊断报告**：生成结构化的问题诊断报告和解决建议

## 前提条件

- 已安装DCI Monitor工具
- 访问权限：用户需要有执行脚本和读取网络状态的权限
- 依赖命令：bash、curl、netcat (可选)、traceroute/tracepath (可选)

## 使用方法

### 基本用法

```bash
# 使用默认设置运行所有测试
./kafka_connection_checker.sh

# 指定Kafka引导服务器地址
./kafka_connection_checker.sh -b kafka-server:9092

# 启用详细输出模式
./kafka_connection_checker.sh -v

# 仅运行元数据测试
./kafka_connection_checker.sh --mode=metadata

# 同时保存日志到文件
./kafka_connection_checker.sh -l /path/to/logfile.log
```

### 参数说明

| 参数 | 长选项 | 说明 |
|------|--------|------|
| `-h` | `--help` | 显示帮助信息 |
| `-b <地址>` | `--bootstrap <地址>` | 指定Kafka引导服务器地址，格式为host:port |
| `-t <主题>` | `--topic <主题>` | 指定测试用的Topic（默认：test） |
| `-m <消息>` | `--message <消息>` | 指定测试发送的消息内容 |
| `-v` | `--verbose` | 启用详细输出模式，显示更多诊断信息 |
| `-T <秒>` | `--timeout <秒>` | 设置连接超时时间（默认：5秒） |
| `-c <秒>` | `--consumer-timeout <秒>` | 设置消费者运行时间（默认：10秒） |
| `-o <格式>` | `--output <格式>` | 指定输出格式：text或json（默认：text） |
| `-l <文件>` | `--log <文件>` | 将输出同时记录到日志文件 |
| `-p <路径>` | `--path <路径>` | 指定dci-monitor可执行文件的路径 |
| `--mode <模式>` | - | 运行模式：all, connect, dns, network, metadata, producer, consumer |

## 运行模式说明

- **all**：执行所有测试（默认）
- **connect**：仅测试基本连接
- **dns**：仅测试DNS解析
- **network**：仅测试网络连通性
- **metadata**：仅测试元数据获取
- **producer**：仅测试消息生产
- **consumer**：仅测试消息消费

## 使用场景

### 1. 初始安装验证

在新环境中部署Agent后，验证与Kafka集群的连接是否正常：

```bash
./kafka_connection_checker.sh -v -l /var/log/dci/kafka_initial_check.log
```

### 2. 问题诊断

当Agent无法连接到Kafka或出现消息丢失时，进行深入诊断：

```bash
./kafka_connection_checker.sh -v --mode=metadata
```

### 3. 定期监控

将脚本集成到监控系统中，定期验证Kafka连接状态：

```bash
./kafka_connection_checker.sh -o json > /var/log/dci/kafka_check_$(date +%Y%m%d).json
```

## 常见问题排查

### 无法连接到Bootstrap服务器

1. 检查网络连通性：确认Agent服务器与Kafka服务器之间的防火墙规则
2. 验证Bootstrap地址：确认地址格式和端口号正确
3. 检查DNS解析：如果使用主机名，确保DNS或hosts文件配置正确

### 元数据获取失败

1. 检查Kafka配置：验证advertised.listeners配置是否正确
2. 确认网络路径：确保从Agent到Kafka Broker的所有IP地址和端口都可访问
3. 查看Kafka日志：检查Kafka服务器日志中的连接错误

### 消息发送/接收失败

1. 检查主题权限：确认用户有读写指定主题的权限
2. 验证主题存在：确保测试主题已创建且可用
3. 检查配额限制：验证是否有消息大小或速率限制

## 其他说明

- 脚本执行结果以返回码形式提供，0表示成功，非0表示失败
- 详细日志中包含完整的诊断信息，包括执行的命令和结果
- JSON输出格式适合与监控系统集成，text格式适合人工分析 