#!/bin/bash

#########################################################################
# Kafka连接检查工具 (kafka_connection_checker.sh)
#
# 用途: 验证Agent与Kafka集群的连接状态，执行网络诊断，提供排障建议
# 由原有test_kafka_connection.sh脚本增强，支持更多参数和诊断功能
#########################################################################

# 启用严格模式
set -eo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 默认参数
BOOTSTRAP_SERVER="************:30002"
TOPIC="test"
MESSAGE="测试消息-$(date "+%Y%m%d%H%M%S")"
VERBOSE=false
TIMEOUT_CONNECT=5
TIMEOUT_CONSUMER=10
MODE="all"
OUTPUT_FORMAT="text"
LOG_FILE=""
DCI_MONITOR_PATH="../bin"

# 显示帮助信息
show_help() {
    echo -e "${BLUE}Kafka连接检查工具${NC}"
    echo "用于验证Agent与Kafka集群的连接状态并进行网络环境诊断"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help                显示此帮助信息"
    echo "  -b, --bootstrap <地址>     指定Kafka引导服务器地址 (默认: $BOOTSTRAP_SERVER)"
    echo "  -t, --topic <主题>         指定测试用的Topic (默认: $TOPIC)"
    echo "  -m, --message <消息>       指定测试发送的消息内容"
    echo "  -v, --verbose             启用详细输出模式"
    echo "  -T, --timeout <秒>         连接超时时间 (默认: $TIMEOUT_CONNECT)"
    echo "  -c, --consumer-timeout <秒> 消费消息的超时时间 (默认: $TIMEOUT_CONSUMER)"
    echo "  -o, --output <格式>        输出格式: text或json (默认: text)"
    echo "  -l, --log <文件>           将输出同时记录到日志文件"
    echo "  -p, --path <路径>          指定dci-monitor可执行文件的路径 (默认: $DCI_MONITOR_PATH)"
    echo "  --mode <模式>              运行模式: all, connect, dns, network, metadata, producer, consumer"
    echo
    echo "示例:"
    echo "  $0 -b kafka:9092 -t test -v"
    echo "  $0 --mode=metadata --verbose"
    echo "  $0 --mode=producer --topic=my_topic --message=\"Hello Kafka\""
    echo
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case "$1" in
            -h|--help)
                show_help
                exit 0
                ;;
            -b|--bootstrap)
                BOOTSTRAP_SERVER="$2"
                shift 2
                ;;
            -t|--topic)
                TOPIC="$2"
                shift 2
                ;;
            -m|--message)
                MESSAGE="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -T|--timeout)
                TIMEOUT_CONNECT="$2"
                shift 2
                ;;
            -c|--consumer-timeout)
                TIMEOUT_CONSUMER="$2"
                shift 2
                ;;
            -o|--output)
                OUTPUT_FORMAT="$2"
                if [[ "$OUTPUT_FORMAT" != "text" && "$OUTPUT_FORMAT" != "json" ]]; then
                    echo "错误: 输出格式必须是 'text' 或 'json'"
                    exit 1
                fi
                shift 2
                ;;
            -l|--log)
                LOG_FILE="$2"
                shift 2
                ;;
            -p|--path)
                DCI_MONITOR_PATH="$2"
                shift 2
                ;;
            --mode)
                MODE="$2"
                shift 2
                ;;
            --mode=*)
                MODE="${1#*=}"
                shift
                ;;
            *)
                echo "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 初始化日志
init_logging() {
    if [[ -n "$LOG_FILE" ]]; then
        # 确保日志目录存在
        mkdir -p "$(dirname "$LOG_FILE")"
        # 清空日志文件
        echo "Kafka连接检查 - $(date)" > "$LOG_FILE"
    fi
}

# 记录日志
log() {
    local level="$1"
    local message="$2"
    local color=""
    
    case "$level" in
        INFO)    color="$BLUE" ;;
        SUCCESS) color="$GREEN" ;;
        WARNING) color="$YELLOW" ;;
        ERROR)   color="$RED" ;;
        *)       color="$NC" ;;
    esac
    
    # 终端输出
    echo -e "${color}[$level] $message${NC}"
    
    # 写入日志文件
    if [[ -n "$LOG_FILE" ]]; then
        echo "[$level] $message" >> "$LOG_FILE"
    fi
}

# 打印章节标题
print_section() {
    local title="$1"
    log "INFO" "===== $title ====="
}

# 检查DCI Monitor工具是否可用
check_dci_monitor() {
    local dci_monitor="$DCI_MONITOR_PATH/dci-monitor"
    if [[ ! -x "$dci_monitor" ]]; then
        log "ERROR" "找不到dci-monitor工具或无执行权限: $dci_monitor"
        log "ERROR" "请使用 -p 参数指定正确的dci-monitor路径"
        return 1
    fi
    return 0
}

# 解析Kafka引导服务器地址
parse_bootstrap_server() {
    local bootstrap="$1"
    local host=""
    local port=""
    
    # 解析主机和端口
    if [[ "$bootstrap" =~ (.+):([0-9]+) ]]; then
        host="${BASH_REMATCH[1]}"
        port="${BASH_REMATCH[2]}"
    else
        log "ERROR" "无效的引导服务器地址格式: $bootstrap"
        log "ERROR" "格式必须为 host:port"
        return 1
    fi
    
    echo "$host $port"
    return 0
}

# 执行DNS检查
run_dns_check() {
    local bootstrap="$1"
    local result
    result=$(parse_bootstrap_server "$bootstrap") || return 1
    read -r host port <<< "$result"
    
    print_section "DNS检查: $host"
    
    # 检查是否为IP地址
    if [[ "$host" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        log "INFO" "$host 是一个IP地址，跳过DNS解析测试"
        return 0
    fi
    
    # 执行DNS查询
    log "INFO" "解析主机名 $host..."
    
    # 使用getent尝试获取主机地址
    getent_result=$(getent hosts "$host" 2>/dev/null || echo "")
    if [[ -z "$getent_result" ]]; then
        log "ERROR" "无法解析主机名: $host"
        log "INFO" "尝试使用nslookup..."
        
        # 尝试使用nslookup
        nslookup_result=$(nslookup "$host" 2>/dev/null || echo "")
        if [[ -z "$nslookup_result" || "$nslookup_result" =~ "can't find" ]]; then
            log "ERROR" "nslookup无法解析主机名: $host"
            return 1
        else
            log "SUCCESS" "nslookup解析成功:"
            echo "$nslookup_result" | grep -v "^$" | while read -r line; do
                log "INFO" "  $line"
            done
        fi
    else
        # 提取IP地址
        ip=$(echo "$getent_result" | awk '{print $1}')
        log "SUCCESS" "解析成功: $host -> $ip"
    fi
    
    return 0
}

# 执行网络连接检查
run_network_check() {
    local bootstrap="$1"
    local result
    result=$(parse_bootstrap_server "$bootstrap") || return 1
    read -r host port <<< "$result"
    
    print_section "网络连接检查: $bootstrap"
    
    # 检查端口连通性
    log "INFO" "检查端口连通性 $host:$port (超时: ${TIMEOUT_CONNECT}s)..."
    timeout_cmd="timeout"
    if ! command -v timeout &>/dev/null; then
        if command -v gtimeout &>/dev/null; then
            timeout_cmd="gtimeout"
        else
            log "WARNING" "命令'timeout'或'gtimeout'不可用，无法设置超时"
            timeout_cmd=""
        fi
    fi
    
    if [[ -n "$timeout_cmd" ]]; then
        if $timeout_cmd "$TIMEOUT_CONNECT" bash -c "echo > /dev/tcp/$host/$port" 2>/dev/null; then
            log "SUCCESS" "端口连通性测试成功: $host:$port 可访问"
        else
            log "ERROR" "端口连通性测试失败: 无法连接到 $host:$port"
            
            # 检查可能的防火墙问题
            log "INFO" "检查防火墙状态..."
            if command -v firewall-cmd &>/dev/null; then
                firewall_status=$(firewall-cmd --state 2>/dev/null || echo "未知")
                log "INFO" "firewalld状态: $firewall_status"
                if [[ "$firewall_status" == "running" ]]; then
                    log "WARNING" "防火墙处于活动状态，可能阻止了连接"
                fi
            elif command -v ufw &>/dev/null; then
                ufw_status=$(ufw status | grep "Status:" | awk '{print $2}')
                log "INFO" "ufw状态: $ufw_status"
                if [[ "$ufw_status" == "active" ]]; then
                    log "WARNING" "ufw防火墙处于活动状态，可能阻止了连接"
                fi
            fi
            
            # 检查网络路由
            log "INFO" "检查到 $host 的网络路由..."
            traceroute_cmd="traceroute"
            if ! command -v traceroute &>/dev/null; then
                if command -v tracepath &>/dev/null; then
                    traceroute_cmd="tracepath"
                else
                    log "WARNING" "无法检查网络路由: traceroute/tracepath命令不可用"
                    traceroute_cmd=""
                fi
            fi
            
            if [[ -n "$traceroute_cmd" ]]; then
                if $VERBOSE; then
                    $traceroute_cmd -n "$host" 2>&1 | head -5 | while read -r line; do
                        log "INFO" "  $line"
                    done
                    log "INFO" "  ..."
                else
                    log "INFO" "使用 --verbose 选项查看路由详情"
                fi
            fi
            
            return 1
        fi
    else
        # 使用更简单的方法检查端口
        if (echo > /dev/tcp/"$host"/"$port") >/dev/null 2>&1; then
            log "SUCCESS" "端口连通性测试成功: $host:$port 可访问"
        else
            log "ERROR" "端口连通性测试失败: 无法连接到 $host:$port"
            return 1
        fi
    fi
    
    return 0
}

# 运行基本连接和DNS测试
run_basic_checks() {
    local bootstrap="$1"
    print_section "Kafka基础连接检查"
    
    # 运行DNS检查
    run_dns_check "$bootstrap" || log "WARNING" "DNS检查失败但继续执行其他测试"
    
    # 运行网络连接检查
    run_network_check "$bootstrap" || log "WARNING" "网络连接检查失败但继续执行其他测试"
    
    # 使用DCI Monitor kafka-client工具进行DNS和网络检查
    log "INFO" "使用dci-monitor kafka-client进行连接检查..."
    
    local dci_monitor="$DCI_MONITOR_PATH/dci-monitor"
    check_dci_monitor || return 1
    
    if $VERBOSE; then
        log "INFO" "执行命令: $dci_monitor kafka-client --bootstrap=${bootstrap} --dnscheck --netcheck --verbose"
        $dci_monitor kafka-client --bootstrap="${bootstrap}" --dnscheck --netcheck --verbose || {
            log "ERROR" "Kafka客户端基础连接检查失败"
            return 1
        }
    else
        log "INFO" "执行命令: $dci_monitor kafka-client --bootstrap=${bootstrap} --dnscheck --netcheck"
        $dci_monitor kafka-client --bootstrap="${bootstrap}" --dnscheck --netcheck || {
            log "ERROR" "Kafka客户端基础连接检查失败"
            return 1
        }
    fi
    
    log "SUCCESS" "Kafka基础连接检查成功"
    return 0
}

# 运行元数据获取测试
run_metadata_test() {
    local bootstrap="$1"
    print_section "Kafka元数据获取测试"
    
    local dci_monitor="$DCI_MONITOR_PATH/dci-monitor"
    check_dci_monitor || return 1
    
    log "INFO" "获取Kafka集群元数据..."
    if $VERBOSE; then
        log "INFO" "执行命令: $dci_monitor kafka-client --bootstrap=${bootstrap} --metadata --verbose"
        $dci_monitor kafka-client --bootstrap="${bootstrap}" --metadata --verbose || {
            log "ERROR" "Kafka元数据获取失败"
            log "WARNING" "这通常是由于advertised.listeners配置问题导致的"
            log "INFO" "请验证Kafka集群中的advertised.listeners配置是否正确"
            return 1
        }
    else
        log "INFO" "执行命令: $dci_monitor kafka-client --bootstrap=${bootstrap} --metadata"
        $dci_monitor kafka-client --bootstrap="${bootstrap}" --metadata || {
            log "ERROR" "Kafka元数据获取失败"
            log "WARNING" "这通常是由于advertised.listeners配置问题导致的"
            log "INFO" "请验证Kafka集群中的advertised.listeners配置是否正确"
            log "INFO" "使用--verbose选项获取更多信息"
            return 1
        }
    fi
    
    log "SUCCESS" "Kafka元数据获取成功"
    return 0
}

# 测试消息生产
run_producer_test() {
    local bootstrap="$1"
    local topic="$2"
    local message="$3"
    
    print_section "Kafka生产者测试: 主题 $topic"
    
    local dci_monitor="$DCI_MONITOR_PATH/dci-monitor"
    check_dci_monitor || return 1
    
    log "INFO" "发送消息到 $topic 主题..."
    log "INFO" "消息内容: $message"
    
    if $VERBOSE; then
        log "INFO" "执行命令: $dci_monitor kafka-client --bootstrap=${bootstrap} --producer --topic=${topic} --message=\"${message}\" --verbose"
        $dci_monitor kafka-client --bootstrap="${bootstrap}" --producer --topic="${topic}" --message="${message}" --verbose || {
            log "ERROR" "Kafka消息发送失败"
            return 1
        }
    else
        log "INFO" "执行命令: $dci_monitor kafka-client --bootstrap=${bootstrap} --producer --topic=${topic} --message=\"${message}\""
        $dci_monitor kafka-client --bootstrap="${bootstrap}" --producer --topic="${topic}" --message="${message}" || {
            log "ERROR" "Kafka消息发送失败"
            return 1
        }
    fi
    
    log "SUCCESS" "成功发送消息到 $topic 主题"
    return 0
}

# 测试消息消费
run_consumer_test() {
    local bootstrap="$1"
    local topic="$2"
    local timeout="$3"
    
    print_section "Kafka消费者测试: 主题 $topic (${timeout}秒)"
    
    local dci_monitor="$DCI_MONITOR_PATH/dci-monitor"
    check_dci_monitor || return 1
    
    log "INFO" "从 $topic 主题消费消息 (最多 ${timeout}秒)..."
    
    timeout_cmd="timeout"
    if ! command -v timeout &>/dev/null; then
        if command -v gtimeout &>/dev/null; then
            timeout_cmd="gtimeout"
        else
            log "WARNING" "命令'timeout'或'gtimeout'不可用，将不限时运行消费者"
            # 直接执行，不加超时
            if $VERBOSE; then
                log "INFO" "执行命令: $dci_monitor kafka-client --bootstrap=${bootstrap} --consumer --topic=${topic} --verbose"
                $dci_monitor kafka-client --bootstrap="${bootstrap}" --consumer --topic="${topic}" --verbose
            else
                log "INFO" "执行命令: $dci_monitor kafka-client --bootstrap=${bootstrap} --consumer --topic=${topic}"
                $dci_monitor kafka-client --bootstrap="${bootstrap}" --consumer --topic="${topic}"
            fi
            log "INFO" "消费者测试完成 (手动终止)"
            return 0
        fi
    fi
    
    # 使用超时命令运行消费者
    if $VERBOSE; then
        log "INFO" "执行命令: $timeout_cmd ${timeout}s $dci_monitor kafka-client --bootstrap=${bootstrap} --consumer --topic=${topic} --verbose"
        $timeout_cmd "${timeout}s" $dci_monitor kafka-client --bootstrap="${bootstrap}" --consumer --topic="${topic}" --verbose || {
            exit_code=$?
            if [ $exit_code -eq 124 ] || [ $exit_code -eq 143 ]; then
                log "INFO" "消费者测试超时结束 (这是正常现象)"
            else
                log "ERROR" "消费者测试失败，退出码: $exit_code"
                return 1
            fi
        }
    else
        log "INFO" "执行命令: $timeout_cmd ${timeout}s $dci_monitor kafka-client --bootstrap=${bootstrap} --consumer --topic=${topic}"
        $timeout_cmd "${timeout}s" $dci_monitor kafka-client --bootstrap="${bootstrap}" --consumer --topic="${topic}" || {
            exit_code=$?
            if [ $exit_code -eq 124 ] || [ $exit_code -eq 143 ]; then
                log "INFO" "消费者测试超时结束 (这是正常现象)"
            else
                log "ERROR" "消费者测试失败，退出码: $exit_code"
                return 1
            fi
        }
    fi
    
    log "SUCCESS" "Kafka消费者测试完成"
    return 0
}

# 生成最终诊断报告
generate_report() {
    print_section "连接诊断总结"
    
    if [[ "$ERROR_COUNT" -eq 0 ]]; then
        if [[ "$WARNING_COUNT" -eq 0 ]]; then
            log "SUCCESS" "所有测试都成功完成，Kafka集群连接正常。"
        else
            log "SUCCESS" "测试完成，但有 $WARNING_COUNT 个警告，请查看日志了解详情。"
        fi
    else
        log "ERROR" "测试过程中发现 $ERROR_COUNT 个错误和 $WARNING_COUNT 个警告。"
        
        # 添加常见问题的解决建议
        log "INFO" "常见问题解决建议:"
        log "INFO" "1. 如果DNS检查失败，请检查DNS配置和/etc/hosts文件"
        log "INFO" "2. 如果网络连接失败，请检查防火墙规则和网络路由"
        log "INFO" "3. 如果元数据获取失败，通常是advertised.listeners配置问题:"
        log "INFO" "   - 确保Kafka节点的advertised.listeners中的地址可从客户端访问"
        log "INFO" "   - 确认bootstrap服务器地址正确并可访问"
        log "INFO" "4. 如果只有生产者或消费者失败，请检查权限和主题配置"
    fi
    
    if [[ -n "$LOG_FILE" ]]; then
        log "INFO" "完整日志已保存到: $LOG_FILE"
    fi
}

# 主函数
main() {
    # 初始化错误和警告计数器
    ERROR_COUNT=0
    WARNING_COUNT=0
    
    # 初始化日志
    init_logging
    
    print_section "Kafka连接检查工具"
    log "INFO" "开始执行Kafka连接测试 - $(date)"
    log "INFO" "引导服务器: $BOOTSTRAP_SERVER"
    log "INFO" "测试主题: $TOPIC"
    
    # 确认DCI Monitor工具可用
    check_dci_monitor || {
        log "ERROR" "缺少必要的工具，无法继续测试"
        exit 1
    }
    
    # 根据模式执行不同的测试
    local overall_result=0
    
    case "$MODE" in
        all)
            run_basic_checks "$BOOTSTRAP_SERVER" || overall_result=1
            run_metadata_test "$BOOTSTRAP_SERVER" || overall_result=1
            run_producer_test "$BOOTSTRAP_SERVER" "$TOPIC" "$MESSAGE" || overall_result=1
            run_consumer_test "$BOOTSTRAP_SERVER" "$TOPIC" "$TIMEOUT_CONSUMER" || overall_result=1
            ;;
        connect)
            run_basic_checks "$BOOTSTRAP_SERVER" || overall_result=1
            ;;
        dns)
            run_dns_check "$BOOTSTRAP_SERVER" || overall_result=1
            ;;
        network)
            run_network_check "$BOOTSTRAP_SERVER" || overall_result=1
            ;;
        metadata)
            run_metadata_test "$BOOTSTRAP_SERVER" || overall_result=1
            ;;
        producer)
            run_producer_test "$BOOTSTRAP_SERVER" "$TOPIC" "$MESSAGE" || overall_result=1
            ;;
        consumer)
            run_consumer_test "$BOOTSTRAP_SERVER" "$TOPIC" "$TIMEOUT_CONSUMER" || overall_result=1
            ;;
        *)
            log "ERROR" "未知的测试模式: $MODE"
            show_help
            exit 1
            ;;
    esac
    
    # 生成报告
    generate_report
    
    print_section "测试完成"
    log "INFO" "测试结束时间: $(date)"
    
    return $overall_result
}

# 解析命令行参数
parse_args "$@"

# 执行主程序
main
exit $? 