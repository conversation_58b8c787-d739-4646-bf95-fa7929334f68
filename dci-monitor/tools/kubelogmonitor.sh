#!/bin/bash
set -euo pipefail

# 显示帮助信息
show_help() {
    echo "用法: $0 [-h] [-c 容器名] [-n 命名空间] [-t 行数] <app名称>"
    echo "选项:"
    echo "  -h    显示帮助信息"
    echo "  -c    指定容器名称 (如果 Pod 中有多个容器)"
    echo "  -n    指定命名空间 (默认: dci)"
    echo "  -t    显示最近的日志行数 (默认: 不限制)"
    echo "参数:"
    echo "  app名称    要监听日志的应用标签名 (例如: elasticsearch, kibana, logstash)"
    echo "示例:"
    echo "  $0 kibana                    # 监听 kibana pod 的日志"
    echo "  $0 -c filebeat-es-sidecar elasticsearch  # 监听 elasticsearch pod 中 filebeat 容器的日志"
    echo "  $0 -n kube-system -t 100 coredns         # 监听 kube-system 命名空间中 coredns 的最近 100 行日志"
    exit 0
}

# 初始化变量
CONTAINER=""
NAMESPACE="dci"
TAIL_LINES=""

# 处理命令行参数
while getopts "hc:n:t:" opt; do
    case $opt in
        h)
            show_help
            ;;
        c)
            CONTAINER="$OPTARG"
            ;;
        n)
            NAMESPACE="$OPTARG"
            ;;
        t)
            TAIL_LINES="$OPTARG"
            ;;
        \?)
            echo "无效的选项: -$OPTARG" >&2
            show_help
            ;;
    esac
done

shift $((OPTIND-1))

if [ $# -eq 0 ]; then
    echo "错误: 缺少app名称参数" >&2
    show_help
fi

APP_NAME=$1

# 自动获取最新Pod名称
POD_NAME=$(kubectl get pods -n $NAMESPACE -l app=$APP_NAME --field-selector=status.phase=Running -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)

if [ -z "$POD_NAME" ]; then
    echo "错误: 未找到 $NAMESPACE 命名空间中运行中的 $APP_NAME pod" >&2
    exit 1
fi

echo "📋 正在监听 Pod 日志: $POD_NAME (命名空间: $NAMESPACE)"

# 构建 logs 命令
LOGS_CMD="kubectl logs -f -n $NAMESPACE $POD_NAME"

# 如果指定了容器，添加容器参数
if [ -n "$CONTAINER" ]; then
    LOGS_CMD="$LOGS_CMD -c $CONTAINER"
    echo "📦 容器: $CONTAINER"
fi

# 如果指定了行数，添加 --tail 参数
if [ -n "$TAIL_LINES" ]; then
    LOGS_CMD="$LOGS_CMD --tail=$TAIL_LINES"
    echo "📄 显示最近 $TAIL_LINES 行日志"
fi

echo "----------------------------"

# 执行命令
$LOGS_CMD

# 添加可执行权限
chmod +x "$0"
