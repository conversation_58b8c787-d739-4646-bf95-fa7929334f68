#!/bin/bash
set -euo pipefail

# 显示帮助信息
show_help() {
    echo "用法: $0 [-h] <app名称>"
    echo "选项:"
    echo "  -h    显示帮助信息"
    echo "示例:"
    echo "  $0 kibana    # 查看kibana pod详情"
    exit 0
}

# 处理命令行参数
while getopts "h" opt; do
    case $opt in
        h)
            show_help
            ;;
        \?)
            echo "无效的选项: -$OPTARG" >&2
            show_help
            ;;
    esac
done

shift $((OPTIND-1))

if [ $# -eq 0 ]; then
    echo "错误: 缺少app名称参数" >&2
    show_help
fi

# 自动获取最新Pod名称并显示详情
POD_NAME=$(kubectl get pods -n dci -l app=$1 --field-selector=status.phase!=Succeeded -o jsonpath='{.items[0].metadata.name}')

echo "🔍 正在检查Pod: $POD_NAME"
kubectl describe pod -n dci "$POD_NAME"
