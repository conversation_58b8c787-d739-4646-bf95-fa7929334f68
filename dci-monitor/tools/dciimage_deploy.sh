#!/bin/bash

# Script to find a specific dcimonitor .tar file in /tmp, move it to a
# predefined image directory, load it into Docker, and push it to a
# predefined registry. Extracts tag from the tar filename.
# To be run ON the target server after the .tar file has been transferred to /tmp.

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Fixed Configuration ---
SOURCE_DIR="/tmp"
DEST_IMAGE_DIR="/data/DCI/images" # 目标镜像存储目录
IMAGE_REPO="cr.registry.pcloud.citic.com/dci/dcimonitor" # 固定仓库名
# --- End Configuration ---


# --- Script Usage ---
usage() {
  echo "用法: $0"
  echo "       该脚本自动在 $SOURCE_DIR 目录查找形如 'cr.registry.pcloud.citic.com_dci_dcimonitor_*.tar' 的文件，"
  echo "       将其移动到 $DEST_IMAGE_DIR，然后加载并推送到 $IMAGE_REPO。"
  echo "       请确保 $SOURCE_DIR 中有且仅有一个匹配的文件。"
  exit 1
}

# --- No Arguments Expected ---
if [ $# -ne 0 ]; then
    echo "错误：该脚本不需要任何参数。"
    usage
fi

# --- Prepare Filename Pattern ---
# Sanitize repo name for filename matching (replace '/' with '_')
SANITIZED_IMAGE_REPO=$(echo "$IMAGE_REPO" | tr '/' '_')
FILENAME_PATTERN="${SANITIZED_IMAGE_REPO}_*.tar"
echo "将在 $SOURCE_DIR 中查找文件，模式: $FILENAME_PATTERN"

# --- Find the Tar File ---
# Use find to handle potential spaces or special characters safely and count accurately
mapfile -t FOUND_FILES < <(find "$SOURCE_DIR" -maxdepth 1 -name "$FILENAME_PATTERN" -print)
FILE_COUNT=${#FOUND_FILES[@]}

# Validate find results
if [ "$FILE_COUNT" -eq 0 ]; then
    echo "错误：在 $SOURCE_DIR 中未找到匹配模式 '$FILENAME_PATTERN' 的文件。"
    exit 1
elif [ "$FILE_COUNT" -gt 1 ]; then
    echo "错误：在 $SOURCE_DIR 中找到多个匹配模式 '$FILENAME_PATTERN' 的文件："
    printf " - %s\\n" "${FOUND_FILES[@]}"
    echo "请确保 $SOURCE_DIR 中只有一个目标 tar 文件。"
    exit 1
fi

# Exactly one file found
TAR_FILE_SOURCE_PATH="${FOUND_FILES[0]}"
TAR_FILENAME=$(basename "$TAR_FILE_SOURCE_PATH")
echo "找到目标文件: $TAR_FILE_SOURCE_PATH"

# --- Prepare Destination and Move ---
echo "准备目标目录: $DEST_IMAGE_DIR"
mkdir -p "$DEST_IMAGE_DIR" # Create destination dir if it doesn't exist

TAR_FILEPATH="$DEST_IMAGE_DIR/$TAR_FILENAME"
echo "正在移动文件到: $TAR_FILEPATH ..."
mv "$TAR_FILE_SOURCE_PATH" "$TAR_FILEPATH"
if [ $? -ne 0 ]; then
    echo "错误：移动文件失败。"
    exit 1
fi
echo "文件移动成功。"


# --- Extract Tag and Construct Full Image Name ---
# Extract tag from filename (part between last '_' and '.tar')
TAG_PART="${TAR_FILENAME%.tar}"
IMAGE_TAG="${TAG_PART##*_}"

# Validate if tag extraction was successful (basic check)
if [ -z "$IMAGE_TAG" ] || [ "$IMAGE_TAG" == "$TAG_PART" ]; then
    echo "错误：无法从文件名 '$TAR_FILENAME' 中提取有效的标签。文件名格式应为 '<name>_<tag>.tar'。"
    # Attempt to move the file back? Or leave it? Let's leave it for now.
    exit 1
fi

# Construct full image name
FULL_IMAGE_NAME="${IMAGE_REPO}:${IMAGE_TAG}"

echo ""
echo "--- 开始 Docker 镜像加载与推送 ---"
echo "镜像文件: $TAR_FILEPATH"
echo "目标镜像名: $FULL_IMAGE_NAME"
echo ""

# --- Check Docker Permissions ---
# Attempt a non-invasive docker command to check if we can connect to the daemon
if ! docker ps -q > /dev/null 2>&1; then
   echo "错误：无法连接到 Docker 守护进程。请确保 Docker 正在运行，并且当前用户有权限执行 Docker 命令（可能需要 root 或属于 'docker' 组）。"
   exit 1
fi
echo "Docker 权限检查通过。"
echo ""

# --- Docker Load ---
echo "正在从 $TAR_FILEPATH 加载 Docker 镜像..."
docker load -i "$TAR_FILEPATH"
if [ $? -ne 0 ]; then
    echo "错误：Docker 镜像加载失败。"
    exit 1
fi
echo "Docker 镜像加载成功。"
echo ""

# --- Docker Push ---
echo "正在将镜像推送到注册表: $FULL_IMAGE_NAME ..."
# Note: Assumes 'docker login' to the registry has already been performed if required.
docker push "$FULL_IMAGE_NAME"
if [ $? -ne 0 ]; then
    echo "错误：Docker 镜像推送失败。请确保已登录到注册表 ($IMAGE_REPO)。"
    exit 1
fi
echo "Docker 镜像推送成功！"
echo ""

# --- Optional: Cleanup ---
# echo "可选：考虑在此处添加清理步骤，例如删除已移动的 tar 文件：rm '$TAR_FILEPATH'"

# --- Optional: Deployment/Restart ---
# echo "可选：考虑在此处添加重启服务的步骤，例如："
# echo "docker stop <container_name>"
# echo "docker rm <container_name>"
# echo "docker run --name <container_name> -d -p <host_port>:<container_port> $FULL_IMAGE_NAME"
# echo "或者使用 docker-compose up -d --force-recreate"

echo "--- 部署脚本执行完毕 ---"
exit 0
