#!/bin/bash
set -eo pipefail

NAMESPACE="dci"
DEPLOY_FILE="E/elasticsearch.yaml"
STORAGE_PREFIX="/mnt/dci/dci-elasticsearch-data-elasticsearch"

# 删除Elasticsearch部署
# 在脚本开头添加危险警告
echo "[警告] 本脚本将执行高危操作，请仔细确认！"
read -p "是否继续执行？(y/N): " confirm
[[ $confirm =~ [yY] ]] || exit 1

# 修改删除部署步骤
echo "=== 步骤1/4：删除Elasticsearch资源 ==="
kubectl get -f $DEPLOY_FILE --ignore-not-found
read -p "确认删除以上资源？(y/N): " confirm
[[ $confirm =~ [yY] ]] && kubectl delete -f $DEPLOY_FILE --ignore-not-found

# 修改PVC/PV删除步骤
echo -e "\n=== 步骤2/4：elasticsearch-certificates服务/PVC/PV清理 ==="
read -p "确认删除 elasticsearch-certificates ？(y/N): " confirm
if [[ $confirm =~ [yY] ]]; then
    kubectl delete secret elasticsearch-certificates -n dci --ignore-not-found
    echo "已提交删除"
else
    echo "跳过删除"
    continue
fi

kubectl get pvc -n $NAMESPACE -o json | jq -r '.items[].metadata.name | select(startswith("elasticsearch-data-elasticsearch-"))'

for PVC_NAME in $(kubectl get pvc -n $NAMESPACE -o json | jq -r '.items[].metadata.name | select(startswith("elasticsearch-data-elasticsearch-"))')
do
    echo "发现PVC: $PVC_NAME"
    PV_NAME=$(kubectl get pvc $PVC_NAME -n $NAMESPACE -o jsonpath='{.spec.volumeName}')
    
    # PVC删除确认
    read -p "确认删除PVC $PVC_NAME？(y/N): " confirm
    if [[ $confirm =~ [yY] ]]; then
        kubectl delete pvc $PVC_NAME -n $NAMESPACE --wait=false
        echo "已提交删除"
    else
        echo "跳过PVC删除"
        continue
    fi

    # PV删除确认
    if [ ! -z "$PV_NAME" ]; then
        echo "关联PV: $PV_NAME"
        read -p "确认删除PV $PV_NAME？(y/N): " confirm
        if [[ $confirm =~ [yY] ]]; then
            kubectl patch pv $PV_NAME -p '{"metadata":{"finalizers":null}}' 2>/dev/null || true
            kubectl delete pv $PV_NAME --wait=false
            echo "已提交删除"
        else
            echo "跳过PV删除"
        fi
    fi
done

# 修改存储清理步骤
echo -e "\n=== 步骤3/4：物理存储清理 ==="
echo "即将删除的目录："
sudo ls -ld ${STORAGE_PREFIX}-* 2>/dev/null || echo "未找到匹配目录"
read -p "确认永久删除以上目录？(y/N): " confirm
if [[ $confirm =~ [yY] ]]; then
    sudo find ${STORAGE_PREFIX}-* -maxdepth 0 -type d -exec rm -rfv {} + 2>/dev/null || true
else
    echo "跳过物理存储删除"
fi

# 添加最终确认
echo -e "\n=== 步骤4/4：最终检查 ==="
sleep 8
echo "剩余资源状态："
kubectl get pvc,pv -n $NAMESPACE | grep elasticsearch || \
echo "未找到elasticsearch相关存储资源"
