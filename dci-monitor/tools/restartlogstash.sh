#!/bin/bash
set -euo pipefail

# 定义相关变量
NAMESPACE="dci"
LOGSTASH_PIPELINE_YAML="L/logstash-pipeline.yaml"
LOGSTASH_DEPLOYMENT_YAML="L/logstash.yaml"

echo "🔄 准备重启 Logstash (namespace: ${NAMESPACE})..."

kubectl get pods -n dci
# 检查 kubectl 是否可用
if ! command -v kubectl &> /dev/null; then
    echo "错误: 未找到 kubectl 命令。请确保已安装并配置 kubectl。"
    exit 1
fi

# 检查 YAML 文件是否存在
if [ ! -f "$LOGSTASH_PIPELINE_YAML" ]; then
    echo "错误: 未找到文件 '$LOGSTASH_PIPELINE_YAML'"
    exit 1
fi
if [ ! -f "$LOGSTASH_DEPLOYMENT_YAML" ]; then
    echo "错误: 未找到文件 '$LOGSTASH_DEPLOYMENT_YAML'"
    exit 1
fi

# 获取重启前正在运行的 Pod 名称
OLD_POD_NAMES=$(kubectl get pods -n "$NAMESPACE" -l app=logstash --field-selector=status.phase=Running --no-headers -o custom-columns=NAME:.metadata.name 2>/dev/null | xargs)
echo "ℹ️ 旧的运行中 Pod: ${OLD_POD_NAMES:-无}"

echo "1. 应用管道 ConfigMap '$LOGSTASH_PIPELINE_YAML'..."
if kubectl replace -f "$LOGSTASH_PIPELINE_YAML" --force -n "$NAMESPACE"; then
  echo "✅ ConfigMap 'logstash-pipeline' 已更新."
else
  echo "❌ 应用 '$LOGSTASH_PIPELINE_YAML' 失败."
  exit 1
fi

echo "2. 应用 Logstash 定义 '$LOGSTASH_DEPLOYMENT_YAML' (Deployment, Service, Config)..."
# 使用 replace --force 同时更新 Deployment, Service, 和 logstash-config ConfigMap
if kubectl replace -f "$LOGSTASH_DEPLOYMENT_YAML" --force -n "$NAMESPACE"; then
  echo "✅ Deployment/Service/ConfigMap 'logstash' 等资源已更新."
else
  echo "❌ 应用 '$LOGSTASH_DEPLOYMENT_YAML' 失败."
  exit 1
fi

# 获取 Logstash deployment 名称 (假设标签为 app=logstash)
LOGSTASH_DEPLOYMENT_NAME=$(kubectl get deployment -n "$NAMESPACE" -l app=logstash -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)

if [ -z "$LOGSTASH_DEPLOYMENT_NAME" ]; then
    echo "⚠️ 无法自动检测到 Logstash Deployment 名称 (使用标签 app=logstash)。"
    echo "   请手动检查 Pod 状态: kubectl get pods -n ${NAMESPACE} -l app=logstash"
    echo "✅ Logstash 重启命令已执行。" # 即使无法检测名称，replace 命令也已执行
fi

echo "✅ 已执行 Logstash 配置更新。请手动检查 Pod 状态确认重启:"
echo "   kubectl get pods -n ${NAMESPACE} -l app=logstash -w"

# 添加 sleep 延迟，等待 Pod 状态可能更新

# --- 阶段一: 等待旧 Pod 消失 --- 
echo "-- 等待旧 Logstash Pod 终止... --"
TIMEOUT_OLD_PODS=120 # 旧 Pod 终止的超时时间
INTERVAL=5
ELAPSED=0

while [ $ELAPSED -lt $TIMEOUT_OLD_PODS ]; do
    old_pods_still_present=false
    # 获取当前所有 Pod 名称
    CURRENT_POD_NAMES=$(kubectl get pods -n "$NAMESPACE" -l app=logstash --no-headers -o custom-columns=NAME:.metadata.name 2>/dev/null)
    
    if [ -n "$OLD_POD_NAMES" ]; then
        for old_name in $OLD_POD_NAMES; do
            # 检查旧 Pod 名称是否存在于当前 Pod 列表中
            if echo "$CURRENT_POD_NAMES" | grep -q -w "$old_name"; then
                old_pods_still_present=true
                break # 找到一个旧 Pod 存在就够了，继续等待
            fi
        done
    else
        # 如果没有旧 Pod 记录，则直接跳过此阶段
        break
    fi

    if $old_pods_still_present; then
        echo "   旧 Pod 仍然存在，等待中... ($ELAPSED/${TIMEOUT_OLD_PODS}s)"
        kubectl get pods -n "$NAMESPACE" -l app=logstash # 显示状态
        sleep $INTERVAL
        ELAPSED=$((ELAPSED + INTERVAL))
    else
        echo "✅ 旧 Pod 已终止。"
        break # 所有旧 Pod 都消失了，退出循环
    fi
done

if $old_pods_still_present; then
    echo "⚠️ 等待旧 Pod 终止超时 (${TIMEOUT_OLD_PODS}s)。可能存在问题。"
    kubectl get pods -n "$NAMESPACE" -l app=logstash
    # 即使旧 Pod 未完全消失，也尝试继续查找新 Pod
    # exit 1 # 或者选择在这里退出
fi

# --- 阶段二: 等待新 Pod 运行 --- 
# 显示最终的 Pod 状态
echo "-- 等待新的 Logstash Pod 启动... --"
sleep 5 # Reduced initial sleep slightly

kubectl get pods -n "$NAMESPACE" # Corrected kubeclt to kubectl
# 循环等待新的 Running Pod (不同于旧 Pod)
LOGSTASH_POD_NAME=""
TIMEOUT_NEW_POD=120 # 新 Pod 启动的超时时间 (可以独立设置)
INTERVAL=5  # 检查间隔（秒）
ELAPSED=0


# 在新 Pod 候选者中查找第一个 Running 的 Pod
LOGSTASH_POD_NAME=$(kubectl get pods -n "$NAMESPACE" | grep 'logstash-' | head -n 1 | awk '{print $1}')

if [ -z "$LOGSTASH_POD_NAME" ]; then
    echo "   等待新的 Pod 运行中... ($ELAPSED/${TIMEOUT_NEW_POD}s)"
    sleep $INTERVAL
    ELAPSED=$((ELAPSED + INTERVAL))
fi

if [ -z "$LOGSTASH_POD_NAME" ]; then
    echo "⚠️ 等待新的 Logstash Pod 启动超时 (${TIMEOUT_NEW_POD}s)。"
    echo "   你可以稍后手动运行: kubectl logs -n ${NAMESPACE} -l app=logstash --tail 100"
    # 显示最终状态
    kubectl get pods -n "$NAMESPACE" -l app=logstash
    exit 1 # Indicate failure due to timeout
fi

echo "✅ 检测到新的运行中 Pod: $LOGSTASH_POD_NAME"

# 获取最新的 Logstash Pod 名称（确保 Pod 正在运行）
# LOGSTASH_POD_NAME=$(kubectl get pods -n "$NAMESPACE" -l app=logstash --field-selector=status.phase=Running -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)

# 移除旧的检测逻辑，因为上面循环已完成
# if [ -z "$LOGSTASH_POD_NAME" ]; then
#     echo "⚠️ 无法自动检测到正在运行的 Logstash Pod 名称 (使用标签 app=logstash)。"
#     echo "   你可以稍后手动运行: kubectl logs -n ${NAMESPACE} -l app=logstash"
# else
    # 询问用户是否查看日志
read -p "❓ 是否查看最新的 Logstash Pod ($LOGSTASH_POD_NAME) 日志? (y/N): " view_logs

# 将输入转换为小写进行比较
case "${view_logs,,}" in # ,, 表示转换为小写 (Bash 4+)
    y|yes)
        echo "👀 正在获取 Pod ($LOGSTASH_POD_NAME) 日志... (按 Ctrl+C 停止)"
        # 使用 -f 实时跟踪日志
        kubectl logs "$LOGSTASH_POD_NAME" -n "$NAMESPACE" -f || echo "❌ 获取日志失败，请手动尝试。"
        ;;
    *)
        echo "ℹ️ 跳过查看日志。"
        ;;
esac
# fi

exit 0
