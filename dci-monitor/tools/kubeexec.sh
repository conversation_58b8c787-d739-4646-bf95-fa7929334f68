#!/bin/bash
set -euo pipefail

# 显示帮助信息
show_help() {
    echo "用法: $0 [-h] [-c 容器名] <app名称> [命令]"
    echo "选项:"
    echo "  -h    显示帮助信息"
    echo "  -c    指定容器名称 (如果 Pod 中有多个容器)"
    echo "参数:"
    echo "  app名称    要连接的应用标签名 (例如: elasticsearch, kibana)"
    echo "  命令       要执行的命令 (默认: /bin/bash 或 /bin/sh)"
    echo "示例:"
    echo "  $0 kibana                   # 进入 kibana pod 的 bash shell"
    echo "  $0 elasticsearch -c filebeat-es-sidecar    # 进入 elasticsearch pod 的 filebeat 容器"
    echo "  $0 logstash curl localhost:9600/_node/stats    # 在 logstash pod 中执行 curl 命令"
    exit 0
}

# 初始化变量
CONTAINER=""
COMMAND="/bin/bash"

# 处理命令行参数
while getopts "hc:" opt; do
    case $opt in
        h)
            show_help
            ;;
        c)
            CONTAINER="$OPTARG"
            ;;
        \?)
            echo "无效的选项: -$OPTARG" >&2
            show_help
            ;;
    esac
done

shift $((OPTIND-1))

if [ $# -eq 0 ]; then
    echo "错误: 缺少app名称参数" >&2
    show_help
fi

APP_NAME=$1
shift

# 如果提供了额外的命令，使用它们替代默认命令
if [ $# -gt 0 ]; then
    COMMAND="$@"
fi

# 自动获取最新Pod名称
POD_NAME=$(kubectl get pods -n dci -l app=$APP_NAME --field-selector=status.phase=Running -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)

if [ -z "$POD_NAME" ]; then
    echo "错误: 未找到运行中的 $APP_NAME pod" >&2
    exit 1
fi

echo "🚪 正在连接到 Pod: $POD_NAME"

# 构建 exec 命令
EXEC_CMD="kubectl exec -it -n dci $POD_NAME"

# 如果指定了容器，添加容器参数
if [ -n "$CONTAINER" ]; then
    EXEC_CMD="$EXEC_CMD -c $CONTAINER"
    echo "📦 容器: $CONTAINER"
fi

echo "🔧 执行命令: $COMMAND"
echo "----------------------------"

# 执行命令
$EXEC_CMD -- $COMMAND

# 检查是否执行成功
if [ $? -ne 0 ]; then
    echo "💥 命令执行失败，尝试使用 /bin/sh"
    $EXEC_CMD -- /bin/sh
fi 