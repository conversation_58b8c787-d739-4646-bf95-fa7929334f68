apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules
  namespace: dci
data:
  infrastructure.yaml: |
    groups:
    # 系统基础设施告警
    - name: infrastructure
      rules:
      - alert: InstanceDown
        expr: up == 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "实例不可用: {{ $labels.instance }}"
          description: "{{ $labels.job }} 的实例 {{ $labels.instance }} 已经不可用超过5分钟"
          
      - alert: HighCPULoad
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 85
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "CPU负载过高: {{ $labels.instance }}"
          description: "实例 {{ $labels.instance }} 的CPU使用率超过85%，持续10分钟"
          
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用过高: {{ $labels.instance }}"
          description: "实例 {{ $labels.instance }} 的内存使用率超过90%"
          
      - alert: LowDiskSpace
        expr: node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"} * 100 < 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "磁盘空间不足: {{ $labels.instance }}"
          description: "实例 {{ $labels.instance }} 的挂载点 {{ $labels.mountpoint }} 剩余空间不足10%"
  
  network_devices.yaml: |
    groups:
    # 网络设备监控告警
    - name: network_devices
      rules:
      - alert: InterfaceHighUtilization
        expr: rate(if_in_octets[5m]) * 8 / if_speed * 100 > 85 or rate(if_out_octets[5m]) * 8 / if_speed * 100 > 85
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "接口利用率过高: {{ $labels.instance }} - {{ $labels.interface }}"
          description: "设备 {{ $labels.device }} 上的接口 {{ $labels.interface }} 利用率超过85%，持续15分钟"
          
      - alert: InterfaceErrorsIncreasing
        expr: rate(if_in_errors[5m]) > 0 or rate(if_out_errors[5m]) > 0
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "接口错误增加: {{ $labels.instance }} - {{ $labels.interface }}"
          description: "设备 {{ $labels.device }} 上的接口 {{ $labels.interface }} 出现错误增长"
  
  prometheus_self_monitoring.yaml: |
    groups:
    # Prometheus自监控告警
    - name: prometheus_self_monitoring
      rules:
      - alert: PrometheusHighQueryLoad
        expr: rate(prometheus_engine_query_duration_seconds_count[5m]) > 10
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Prometheus查询负载高: {{ $labels.instance }}"
          description: "Prometheus实例 {{ $labels.instance }} 的查询负载超过每秒10次，持续15分钟"
          
      - alert: PrometheusTSDBReloadsFailing
        expr: increase(prometheus_tsdb_reloads_failures_total[1h]) > 0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Prometheus TSDB重载失败: {{ $labels.instance }}"
          description: "Prometheus {{ $labels.instance }} 的时序数据库重载失败，可能导致数据丢失"
          
      - alert: PrometheusNotIngestingSamples
        expr: rate(prometheus_tsdb_head_samples_appended_total[5m]) <= 0
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Prometheus不再接收样本: {{ $labels.instance }}"
          description: "Prometheus {{ $labels.instance }} 在过去10分钟内未接收任何样本"
          
      - alert: PrometheusHighMemoryUsage
        expr: (process_resident_memory_bytes{job="prometheus"} / (1024 * 1024 * 1024)) > (container_spec_memory_limit_bytes{job="prometheus"} / (1.5 * 1024 * 1024 * 1024))
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Prometheus内存使用过高: {{ $labels.instance }}"
          description: "Prometheus {{ $labels.instance }} 的内存使用超过限制的66%，持续15分钟"
  
  dci_flow_alerts.yaml: |
    groups:
    # 从dci_alerts.yml整合的DCI流量告警规则
    - name: dci_network_flow_alerts
      rules:
      - alert: InterfaceHighUtilization
        expr: dci_snmp_flow_ifHCInOctets_rate / on(device, ifIndex) dci_snmp_info_ifSpeed * 100 > 80 or dci_snmp_flow_ifHCOutOctets_rate / on(device, ifIndex) dci_snmp_info_ifSpeed * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: network
        annotations:
          summary: "接口利用率过高: {{ $labels.device }} - {{ $labels.ifName }}"
          description: "设备 {{ $labels.device }} 上的接口 {{ $labels.ifName }} 利用率超过80%，持续5分钟"
          
      - alert: InterfaceFlapping
        expr: changes(dci_snmp_info_ifOperStatus[1h]) > 3
        for: 10m
        labels:
          severity: warning
          category: network
        annotations:
          summary: "接口抖动: {{ $labels.device }} - {{ $labels.ifName }}"
          description: "设备 {{ $labels.device }} 上的接口 {{ $labels.ifName }} 在1小时内状态变化超过3次"
  
  dci_device_alerts.yaml: |
    groups:
    # 从dci_alerts.yml整合的DCI设备告警规则
    - name: dci_device_alerts
      rules:
      - alert: DeviceDown
        expr: up{job="snmp"} == 0
        for: 5m
        labels:
          severity: critical
          category: device
        annotations:
          summary: "设备不可达: {{ $labels.device }}"
          description: "设备 {{ $labels.device }} 已经不可达超过5分钟"
          
      - alert: HighCPUUtilization
        expr: dci_snmp_device_cpuUtilization > 90
        for: 10m
        labels:
          severity: warning
          category: device
        annotations:
          summary: "CPU利用率过高: {{ $labels.device }}"
          description: "设备 {{ $labels.device }} 的CPU利用率超过90%，持续10分钟"
          
      - alert: HighMemoryUtilization
        expr: dci_snmp_device_memoryUtilization > 90
        for: 10m
        labels:
          severity: warning
          category: device
        annotations:
          summary: "内存利用率过高: {{ $labels.device }}"
          description: "设备 {{ $labels.device }} 的内存利用率超过90%，持续10分钟"
  
  dci_system_alerts.yaml: |
    groups:
    # 从dci_alerts.yml整合的DCI系统告警规则
    - name: dci_system_alerts
      rules:
      - alert: KafkaConsumerLag
        expr: kafka_consumergroup_lag > 10000
        for: 15m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "Kafka消费延迟: {{ $labels.consumergroup }}"
          description: "消费组 {{ $labels.consumergroup }} 在主题 {{ $labels.topic }} 上的消费延迟超过10000条消息，持续15分钟"
          
      - alert: PrometheusTargetMissing
        expr: up == 0
        for: 10m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "监控目标丢失: {{ $labels.instance }}"
          description: "监控目标 {{ $labels.instance }} ({{ $labels.job }}) 已经不可达超过10分钟"
  
  dci_device_status.yaml: |
    groups:
    # 从dci_alerts.yml整合的设备状态告警规则
    - name: dci_device_status
      rules:
      - alert: DeviceCPUUsageHigh
        expr: dci_snmp_status_cpu_usage{device_id=~".+"} > 80
        for: 5m
        labels:
          severity: warning
          category: device
        annotations:
          summary: "设备CPU使用率过高"
          description: "设备 {{ $labels.device_id }} CPU使用率为 {{ $value }}%，已超过80%阈值持续5分钟"
          
      - alert: DeviceMemoryUsageHigh
        expr: dci_snmp_status_memory_usage{device_id=~".+"} > 85
        for: 5m
        labels:
          severity: warning
          category: device
        annotations:
          summary: "设备内存使用率过高"
          description: "设备 {{ $labels.device_id }} 内存使用率为 {{ $value }}%，已超过85%阈值持续5分钟"
  
  dci_interface_status.yaml: |
    groups:
    # 从dci_alerts.yml整合的接口状态告警规则
    - name: dci_interface_status
      rules:
      - alert: InterfaceDown
        expr: dci_snmp_status_interface{device_id=~".+"} == 2
        for: 1m
        labels:
          severity: critical
          category: interface
        annotations:
          summary: "接口状态为Down"
          description: "设备 {{ $labels.device_id }} 的接口 {{ $labels.ifName }} 状态为Down"
  
  dci_test_alerts.yaml: |
    groups:
    # 测试告警规则组
    - name: dci_test_alerts
      rules:
      - alert: TestAlwaysFiring
        expr: vector(1)
        for: 30s
        labels:
          severity: info
          category: test
          test_alert: "true"
          device_id: "211"
        annotations:
          summary: "测试告警 - 总是触发"
          description: "01这是一个测试告警，用于验证告警系统的功能。此告警总是处于触发状态，可用于测试告警通知和处理流程。设备ID: 211"
          
      - alert: TestToggleFiring
        expr: minute() % 2 == 0
        for: 15s
        labels:
          severity: info
          category: test
          test_alert: "true"
          device_id: "211"
        annotations:
          summary: "测试告警 - 交替触发"
          description: "这是一个测试告警，每分钟会交替触发和解除，用于测试告警状态变化的处理。当前分钟数: {{ $value }}。设备ID: 211" 