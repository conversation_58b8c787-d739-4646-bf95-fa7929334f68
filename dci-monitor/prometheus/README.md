# DCI监控系统 - Prometheus & AlertManager 配置

本目录包含DCI监控系统的Prometheus和AlertManager在Kubernetes环境中的部署配置文件。

## 文件说明

### Prometheus配置
- `prometheus-rbac.yaml`: Prometheus的RBAC配置
- `prometheus-configmap.yaml`: Prometheus的主配置
- `prometheus-rules-configmap.yaml`: Prometheus的告警规则配置
- `prometheus-web-config.yaml`: Prometheus的Web访问配置
- `prometheus-basic-auth-secret.yaml`: Prometheus的基本认证密钥
- `prometheus-k8s.yaml`: Prometheus的StatefulSet部署配置
- `prometheus-service.yaml`: Prometheus的Service配置

### AlertManager配置
- `alertmanager-configmap.yaml`: AlertManager的配置和告警模板
- `alertmanager-k8s.yaml`: AlertManager的Deployment和Service配置

### 部署脚本
- `deploy-monitoring.sh`: 一键部署脚本

## 部署步骤

1. 确保已经配置好kubectl并能够访问目标Kubernetes集群
2. 执行部署脚本：
   ```bash
   ./deploy-monitoring.sh
   ```
3. 脚本将自动创建所有必要的资源并检查部署状态

## 访问方式

- Prometheus UI: http://<节点IP>:30006
- AlertManager UI: http://<节点IP>:30008

## 认证信息

- Prometheus UI: 
  - 用户名: dciadmin
  - 密码: c8PI3huRud8tW

## 开发环境配置

对于开发环境，我们需要在本地配置文件中指向K8s环境中的Prometheus和AlertManager服务：

1. 修改`dci-monitor/src/dcimonitor/config/config.yaml`文件：
   ```yaml
   # Prometheus 配置
   prometheus:
     address: "http://dciprometheus.intro.citic-x.com:30006" # Prometheus NodePort服务地址
     timeout: 30s
     username: "dciadmin"
     password: "c8PI3huRud8tW"
     rule_file: "./config/prometheus/rules/dci_alerts.yml" # 本地规则文件路径
     reload_enabled: false # 本地开发环境不启用重新加载功能

   # AlertManager 配置
   alertmanager:
     address: "http://alertmanager.dci.svc.cluster.local:9093" # K8s中AlertManager服务地址
     webhook_auth_token: "your-webhook-auth-token"
   ```

2. 在本地创建告警规则文件：
   ```
   dci-monitor/src/dcimonitor/config/prometheus/rules/dci_alerts.yml
   ```

3. 本地开发时，告警规则修改后需要手动应用到K8s环境：
   ```bash
   kubectl create configmap prometheus-rules --from-file=prometheus-rules.yaml=./dci_alerts.yml -n dci --dry-run=client -o yaml | kubectl apply -f -
   kubectl rollout restart statefulset prometheus -n dci
   ```

## 规则加载

```bash
curl -X POST  -u dciadmin:c8PI3huRud8tW  http://dciprometheus.intro.citic-x.com:30006/-/reload
```

## 注意事项

1. 所有密码和认证令牌在生产环境中应该使用更安全的方式管理
2. AlertManager配置中的邮件和Webhook配置需要根据实际环境进行调整
3. 告警规则应根据实际监控需求进行定制 

## 创建密码的命令

htpasswd -nBC 12 dciadmin

