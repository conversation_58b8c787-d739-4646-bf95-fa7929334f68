apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: dci
data:
  alertmanager.yml: |
    global:
      resolve_timeout: 5m
      # 暂时注释掉邮箱配置
      # smtp_smarthost: 'smtp.example.com:587'
      # smtp_from: '<EMAIL>'
      # smtp_auth_username: 'alertmanager'
      # smtp_auth_password: 'password'
      # smtp_require_tls: true
      
    # 路由配置
    route:
      group_by: ['alertname', 'job', 'severity']
      # 告警分组等待时间  当新的告警组被创建后，Alertmanager会等待30秒，然后发送第一个通知。这允许系统在短时间内收集类似的告警并一起发送
      group_wait: 30s 

      # 如果同一组中有新的告警被触发，Alertmanager会等待5分钟后再发送关于这个组的更新通知
      group_interval: 5m

      # 对于持续处于firing状态的告警，Alertmanager会每4小时向dcimonitor重复发送一次通知
      repeat_interval: 4h
      receiver: 'default-receiver'
      routes:
      - match:
          severity: critical
        receiver: 'critical-receiver'
        continue: true
      - match:
          severity: warning
        receiver: 'warning-receiver'
        continue: true
        
    # 接收器配置
    receivers:
    - name: 'default-receiver'
      webhook_configs:
      - url: 'http://dcimonitor-service.dci.svc.cluster.local:8080/api/v1/alerts/webhook/prometheus'
        send_resolved: true
        http_config:
          bearer_token: 'dci-webhook-secret-token-2025'
          
    - name: 'critical-receiver'
      # 暂时注释掉邮件配置
      # email_configs:
      # - to: '<EMAIL>'
      #   send_resolved: true
      webhook_configs:
      - url: 'http://dcimonitor-service.dci.svc.cluster.local:8080/api/v1/alerts/webhook/prometheus'
        send_resolved: true
        http_config:
          bearer_token: 'dci-webhook-secret-token-2025'
          
    - name: 'warning-receiver'
      # 暂时注释掉邮件配置
      # email_configs:
      # - to: '<EMAIL>'
      #   send_resolved: true
      webhook_configs:
      - url: 'http://dcimonitor-service.dci.svc.cluster.local:8080/api/v1/alerts/webhook/prometheus'
        send_resolved: true
        http_config:
          bearer_token: 'dci-webhook-secret-token-2025'
          
    # 告警模板
    templates:
    - '/etc/alertmanager/templates/*.tmpl'
    
  # 默认告警模板
  default.tmpl: |
    {{ define "default.title" }}[{{ .Status | toUpper }}] {{ .CommonLabels.alertname }}{{ end }}
    
    {{ define "default.message" }}
    告警状态: {{ .Status | toUpper }}
    告警名称: {{ .CommonLabels.alertname }}
    告警级别: {{ .CommonLabels.severity }}
    告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
    {{ if eq .Status "resolved" -}}
    恢复时间: {{ .EndsAt.Format "2006-01-02 15:04:05" }}
    {{ end -}}
    
    告警详情:
    {{ range .Alerts -}}
    - 标签:
    {{ range .Labels.SortedPairs }}  {{ .Name }}: {{ .Value }}
    {{ end }}
      摘要: {{ .Annotations.summary }}
      描述: {{ .Annotations.description }}
    {{ end }}
    {{ end }} 