---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alertmanager
  namespace: dci
  labels:
    app: alertmanager
spec:
  replicas: 1
  selector:
    matchLabels:
      app: alertmanager
  template:
    metadata:
      labels:
        app: alertmanager
    spec:
      containers:
      - name: alertmanager
        image: cr.registry.pcloud.citic.com/dci/alertmanager:v0.28.1
        imagePullPolicy: IfNotPresent
        args:
        - "--config.file=/etc/alertmanager/alertmanager.yml"
        - "--storage.path=/alertmanager"
        - "--web.external-url=http://10.247.33.12:30001"
        - "--web.config.file=/etc/alertmanager/web/web-config.yml"
        ports:
        - name: web
          containerPort: 9093
        readinessProbe:
          httpGet:
            path: /-/ready
            port: 9093
            httpHeaders:
            - name: Authorization
              value: Basic YWRtaW46QWRtaW5EQ0lhbGVydE1nciEyMw==
          initialDelaySeconds: 30
          timeoutSeconds: 30
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: 9093
            httpHeaders:
            - name: Authorization
              value: Basic YWRtaW46QWRtaW5EQ0lhbGVydE1nciEyMw==
          initialDelaySeconds: 30
          timeoutSeconds: 30
        resources:
          requests:
            cpu: "100m"
            memory: "128Mi"
          limits:
            cpu: "2000m"
            memory: "2048Mi"
        volumeMounts:
        - name: config-volume
          mountPath: /etc/alertmanager
        - name: alertmanager-data
          mountPath: /alertmanager
        - name: web-config-volume
          mountPath: /etc/alertmanager/web
      imagePullSecrets:
      - name: dci-images-key
      volumes:
      - name: config-volume
        configMap:
          name: alertmanager-config
      - name: alertmanager-data
        emptyDir: {}
      - name: web-config-volume
        configMap:
          name: alertmanager-web-config
          items:
          - key: web-config.yml
            path: web-config.yml
---
apiVersion: v1
kind: Service
metadata:
  name: alertmanager
  namespace: dci
  labels:
    app: alertmanager
spec:
  type: ClusterIP
  ports:
  - name: web
    port: 9093
    targetPort: web
    protocol: TCP
  selector:
    app: alertmanager
