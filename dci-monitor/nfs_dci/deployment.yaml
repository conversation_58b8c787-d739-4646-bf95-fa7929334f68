apiVersion: v1
kind: ServiceAccount
metadata:
  name: nfs-client-provisioner
  namespace: dci  # 命名空间修改
---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: nfs-client-provisioner
  namespace: dci  # 命名空间
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: nfs-client-provisioner
  template:
    metadata:
      labels:
        app: nfs-client-provisioner
    spec:
      serviceAccount: nfs-client-provisioner
      securityContext:
        runAsUser: 0  # 以 root 用户运行
        fsGroup: 0
      containers:
        - name: nfs-client-provisioner
          image: cr.registry.pcloud.citic.com/dci/nfs-client-provisioner
          resources:
            limits:
              cpu: "200m"
              memory: "256Mi"
          volumeMounts:
            - name: nfs-client-root
              mountPath: /mnt/dci 
          env:
            - name: PROVISIONER_NAME
              value: dci-nfs-storage  # 与StorageClass一致
            - name: NFS_SERVER
              value: 10.247.33.12    # 保持原NFS配置
            - name: NFS_PATH
              value: /mnt/dci         # 磁盘挂载路径
      imagePullSecrets:
        - name: dci-images-key
      volumes:
        - name: nfs-client-root
          nfs:
            server: 10.247.33.12
            path: /mnt/dci          # 磁盘挂载路径
