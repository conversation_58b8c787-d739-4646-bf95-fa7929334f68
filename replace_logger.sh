#!/bin/bash
# 替换所有zap.L()调用为common/logger调用

echo "开始替换zap.L()调用为common/logger调用..."

# 确保导入common/logger包
echo "添加common/logger导入..."
find ./dcimonitor/cmd ./dcimonitor/internal -name "*.go" -type f -exec grep -l "zap.L()" {} \; | while read file; do
  if grep -q "import (" "$file"; then
    # 如果文件中有import (，则在其后添加common/logger导入
    sed -i '' '/import (/a\'$'\n''\t"common/logger"' "$file"
  elif grep -q "^import " "$file"; then
    # 如果文件中有单行import，则将其转换为多行导入
    sed -i '' 's/^import "/import (\n\t"common\/logger"\n\t"/' "$file"
    sed -i '' 's/^import "/&/' "$file"
    sed -i '' '/^import "/s/$/")/' "$file"
  fi
done

echo "替换日志调用..."
# 替换zap.L().Info调用
find ./dcimonitor/cmd ./dcimonitor/internal -name "*.go" -type f -exec sed -i '' 's/zap\.L()\.Info/logger.Info/g' {} \;

# 替换zap.L().Debug调用
find ./dcimonitor/cmd ./dcimonitor/internal -name "*.go" -type f -exec sed -i '' 's/zap\.L()\.Debug/logger.Debug/g' {} \;

# 替换zap.L().Warn调用
find ./dcimonitor/cmd ./dcimonitor/internal -name "*.go" -type f -exec sed -i '' 's/zap\.L()\.Warn/logger.Warn/g' {} \;

# 替换zap.L().Error调用
find ./dcimonitor/cmd ./dcimonitor/internal -name "*.go" -type f -exec sed -i '' 's/zap\.L()\.Error/logger.Error/g' {} \;

# 替换zap.L().Fatal调用
find ./dcimonitor/cmd ./dcimonitor/internal -name "*.go" -type f -exec sed -i '' 's/zap\.L()\.Fatal/logger.Fatal/g' {} \;

# 替换zap.L().Panic调用
find ./dcimonitor/cmd ./dcimonitor/internal -name "*.go" -type f -exec sed -i '' 's/zap\.L()\.Panic/logger.Panic/g' {} \;

# 替换zap.L().DPanic调用
find ./dcimonitor/cmd ./dcimonitor/internal -name "*.go" -type f -exec sed -i '' 's/zap\.L()\.DPanic/logger.DPanic/g' {} \;

# 替换zap.L().Sync调用
find ./dcimonitor/cmd ./dcimonitor/internal -name "*.go" -type f -exec sed -i '' 's/zap\.L()\.Sync/logger.Sync/g' {} \;

echo "替换完成，请检查结果并修复可能的导入问题" 