---
title: |
  DCI项目自测工具说明

subtitle: |
  测试框架与测试用例使用指南
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-10 | 顾铠羟 | 初始版本           |
| V1.1 | 2025-05-21 | 顾铠羟 | 添加SNMP测试工具说明 |

# 1 引言

## 1.1 文档目的

本文档旨在介绍 DCI 项目的自测工具框架和使用方法，帮助开发人员和测试人员理解如何使用和扩展自测工具。自测工具是保证项目质量的重要手段，通过自动化测试可以及早发现问题并验证功能的正确性。

## 1.2 目录结构

DCI 项目自测工具目录结构如下：

```
dci_self_test/
├── README.md                      # 本文档
├── results/                       # 测试结果输出目录
├── scripts/                       # 测试脚本目录
│   ├── run_telegraf_tests.sh      # Telegraf 测试套件执行脚本
│   ├── self_test_telegraf_config_check_tool.sh   # 配置检查工具测试
│   ├── self_test_telegraf_log_management_tool.sh # 日志管理工具测试
│   └── self_test_telegraf_process_control.sh     # 进程控制功能测试
├── templates/                     # 测试模板和标准化文件
└── utils/                         # 测试辅助工具
```
