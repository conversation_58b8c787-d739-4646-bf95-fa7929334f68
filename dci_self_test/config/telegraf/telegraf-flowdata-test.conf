# Telegraf测试配置 - 实际SNMP采集
[agent]
  interval = "10s"
  round_interval = true
  metric_batch_size = 1000
  metric_buffer_limit = 10000
  collection_jitter = "0s"
  flush_interval = "2s"    # 更快的flush间隔
  flush_jitter = "0s"
  precision = ""
  debug = true
  quiet = false
  logfile = "/Users/<USER>/code/dci/dci-workspace/dci_self_test/results/telegraf-flowdata-test.log"
  hostname = "dci-agent"   # 设置采集代理主机名
  omit_hostname = false

# 全局标签配置 - 适用于所有输入插件
[global_tags]
  agent_id = "dci-agent"    # 采集代理ID
  agent_ip = "127.0.0.1"    # 采集代理IP
  
[[inputs.snmp]]
  # 设备列表 - 测试环境的交换机
  agents = [
    "udp://************:161"  # 测试交换机1
    # "udp://************:161"  # 测试交换机2
  ]
  
  # 基本配置
  timeout = "5s"        # 更短的超时
  retries = 1           # 减少重试次数
  version = 2
  community = "dcilab2025"
  agent_host_tag = "device_ip"  # SNMP设备IP标签
  
  # 使用全局标签，而不是SNMP输入插件特定标签
  
  # 高级配置
  max_repetitions = 10  # 根据记录数量限制调整
  name = "snmp"
  
  # 系统基本信息
  [[inputs.snmp.field]]
    name = "sysName"
    oid = ".*******.*******.0"
    is_tag = true
    
  # 接口表信息
  [[inputs.snmp.table]]
    name = "interface"
    inherit_tags = ["sysName"]
    oid = ".*******.*******"  # IF-MIB::ifTable
    
    # ifTable 字段
    [[inputs.snmp.table.field]]
      name = "ifIndex"
      oid = ".*******.*******.1.1"  # IF-MIB::ifIndex
      is_tag = true
      
    [[inputs.snmp.table.field]]
      name = "ifDescr"
      oid = ".*******.*******.1.2"  # IF-MIB::ifDescr
      is_tag = true
  
  # 接口扩展表信息 - 重点采集流量数据
  [[inputs.snmp.table]]
    name = "interfaceX"
    inherit_tags = ["sysName"]
    oid = ".*******.********.1"  # IF-MIB::ifXTable
    
    # ifXTable 字段
    [[inputs.snmp.table.field]]
      name = "ifName"
      oid = ".*******.********.1.1.1"  # IF-MIB::ifName
      is_tag = true
      
    [[inputs.snmp.table.field]]
      name = "ifHCInOctets"
      oid = ".*******.********.1.1.6"  # IF-MIB::ifHCInOctets
      
    [[inputs.snmp.table.field]]
      name = "ifHCOutOctets"
      oid = ".*******.********.1.1.10"  # IF-MIB::ifHCOutOctets
      
    [[inputs.snmp.table.field]]
      name = "ifInDiscards"
      oid = ".*******.*******.1.13"  # IF-MIB::ifInDiscards
      
    [[inputs.snmp.table.field]]
      name = "ifOutDiscards"
      oid = ".*******.*******.1.19"  # IF-MIB::ifOutDiscards
      
    [[inputs.snmp.table.field]]
      name = "ifInErrors"
      oid = ".*******.*******.1.14"  # IF-MIB::ifInErrors
      
    [[inputs.snmp.table.field]]
      name = "ifOutErrors"
      oid = ".*******.*******.1.20"  # IF-MIB::ifOutErrors
      
  # 添加标准化处理器配置
  
  # 处理器1: 确保将host/agent_host标签重命名为device_ip
  [[processors.rename]]
    # [[processors.rename.replace]]
    #   tag = "host"
    #   dest = "device_ip"

    # [[processors.rename.replace]]
    #   tag = "agent_host"
    #   dest = "device_ip"
  
  # 处理器2: 标准化字段名，移除可能的字段前缀
  [[processors.rename]]
    [[processors.rename.replace]]
      field = "fields_ifHCInOctets"
      dest = "ifHCInOctets"
      
    [[processors.rename.replace]]
      field = "fields_ifHCOutOctets"
      dest = "ifHCOutOctets"
      
    [[processors.rename.replace]]
      field = "fields_ifInDiscards"
      dest = "ifInDiscards"
      
    [[processors.rename.replace]]
      field = "fields_ifOutDiscards"
      dest = "ifOutDiscards"
      
    [[processors.rename.replace]]
      field = "fields_ifInErrors"
      dest = "ifInErrors"
      
    [[processors.rename.replace]]
      field = "fields_ifOutErrors"
      dest = "ifOutErrors"
      
  # 处理器3: 为ifIndex添加标准化处理  
  [[processors.rename]]
    [[processors.rename.replace]]
      field = "fields_ifIndex"
      dest = "ifIndex"
      
    [[processors.rename.replace]]
      tag = "ifindex"
      dest = "ifIndex"
# 添加处理器，确保agent_host标签转换为device_ip字段
[[processors.rename]]
  [[processors.rename.replace]]
    tag = "agent_host"
    dest = "device_ip"

# 添加Kafka输出插件 - 用于将数据注入Kafka
[[outputs.kafka]]
  # Kafka Broker连接信息
  brokers = ["dcikafka.intra.citic-x.com:30002"]
  
  # 主题配置 - 使用设计文档中指定的主题
  topic = "dci.monitor.v1.defaultchannel.flows.snmp"
  
  # 消息格式与压缩
  data_format = "json"
  compression_codec = 2  # 2 = snappy
  
  # 可靠性设置
  max_retry = 3
  max_message_bytes = 1000000
  
  # 确保包含必要的标签
  [outputs.kafka.tags]
    data_source_type = "snmp_flow"
# 文件输出插件 - 用于保存完整数据
[[outputs.file]]
  files = ["/Users/<USER>/code/dci/dci-workspace/dci_self_test/results/telegraf-flowdata-test-full.out"]
  data_format = "json"
  flush_interval = "2s"
  
# 标准输出插件 - 用于实时查看数据
[[outputs.file]]
  files = ["stdout"]
  data_format = "json"
