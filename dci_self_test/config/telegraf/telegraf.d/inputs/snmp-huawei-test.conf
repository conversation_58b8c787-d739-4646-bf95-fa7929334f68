[[inputs.snmp]]
  # 设备列表 - 测试环境的两台华为交换机
  agents = [
    "udp://10.36.46.188:161",  # 测试华为交换机1
    "udp://10.36.46.189:161"   # 测试华为交换机2
  ]
  
  # 基本配置
  timeout = "15s"
  retries = 3
  version = 2
  community = "dcilab2025"  # 测试环境中的SNMP社区字符串，生产环境应当使用环境变量或秘密管理
  
  # 高级配置
  max_repetitions = 10  # 用于SNMP GetBulk操作
  name = "snmp"  # 指标前缀
  
  # 系统基本信息
  [[inputs.snmp.field]]
    name = "sysName"
    oid = "SNMPv2-MIB::sysName.0"
    is_tag = true
    
  [[inputs.snmp.field]]
    name = "sysDescr"
    oid = "SNMPv2-MIB::sysDescr.0"
    
  [[inputs.snmp.field]]
    name = "sysUpTime"
    oid = "DISMAN-EVENT-MIB::sysUpTimeInstance"
    
  [[inputs.snmp.field]]
    name = "sysLocation"
    oid = "SNMPv2-MIB::sysLocation.0"

  # 华为设备CPU和内存使用率
  [[inputs.snmp.table]]
    name = "hw_entity"
    inherit_tags = ["sysName"]
    oid = "HUAWEI-ENTITY-EXTENT-MIB::hwEntityStateTable"
    
    [[inputs.snmp.table.field]]
      name = "hwEntityCpuUsage"
      oid = "HUAWEI-ENTITY-EXTENT-MIB::hwEntityCpuUsage"
      
    [[inputs.snmp.table.field]]
      name = "hwEntityMemUsage"
      oid = "HUAWEI-ENTITY-EXTENT-MIB::hwEntityMemUsage"
      
  # 接口基本信息表
  [[inputs.snmp.table]]
    name = "interfaces"
    inherit_tags = ["sysName"]
    oid = "IF-MIB::ifTable"
    
    [[inputs.snmp.table.field]]
      name = "ifIndex"
      oid = "IF-MIB::ifIndex"
      is_tag = true
      
    [[inputs.snmp.table.field]]
      name = "ifDescr"
      oid = "IF-MIB::ifDescr"
      is_tag = true
      
    [[inputs.snmp.table.field]]
      name = "ifType"
      oid = "IF-MIB::ifType"
      
    [[inputs.snmp.table.field]]
      name = "ifAdminStatus"
      oid = "IF-MIB::ifAdminStatus"
      
    [[inputs.snmp.table.field]]
      name = "ifOperStatus"
      oid = "IF-MIB::ifOperStatus"
    
  # 扩展接口信息表（高速接口计数器）
  [[inputs.snmp.table]]
    name = "ifXTable"
    inherit_tags = ["sysName"]
    oid = "IF-MIB::ifXTable"
    
    [[inputs.snmp.table.field]]
      name = "ifName"
      oid = "IF-MIB::ifName"
      is_tag = true
    
    [[inputs.snmp.table.field]]
      name = "ifHCInOctets"
      oid = "IF-MIB::ifHCInOctets"
    
    [[inputs.snmp.table.field]]
      name = "ifHCOutOctets"
      oid = "IF-MIB::ifHCOutOctets"
      
    [[inputs.snmp.table.field]]
      name = "ifHCInUcastPkts"
      oid = "IF-MIB::ifHCInUcastPkts"
      
    [[inputs.snmp.table.field]]
      name = "ifHCOutUcastPkts"
      oid = "IF-MIB::ifHCOutUcastPkts"
      
  # LLDP邻居信息表（用于拓扑发现）
  [[inputs.snmp.table]]
    name = "lldpRemTable"
    inherit_tags = ["sysName"]
    index_as_tag = true
    oid = "LLDP-MIB::lldpRemTable"
    
    [[inputs.snmp.table.field]]
      name = "lldpRemChassisIdSubtype"
      oid = "LLDP-MIB::lldpRemChassisIdSubtype"
      
    [[inputs.snmp.table.field]]
      name = "lldpRemChassisId"
      oid = "LLDP-MIB::lldpRemChassisId"
      
    [[inputs.snmp.table.field]]
      name = "lldpRemPortIdSubtype"
      oid = "LLDP-MIB::lldpRemPortIdSubtype"
      
    [[inputs.snmp.table.field]]
      name = "lldpRemPortId"
      oid = "LLDP-MIB::lldpRemPortId"
      
    [[inputs.snmp.table.field]]
      name = "lldpRemSysName"
      oid = "LLDP-MIB::lldpRemSysName" 