LLDP-EXT-DOT3-MIB DEFINITIONS ::= BEGIN
IMPORTS
MODULE-IDENTITY, OBJECT-TYPE, Integer32
FROM SNMPv2-SMI
TEXTUAL-CONVENTION, TruthValue
FROM SNMPv2-TC
MODULE-COMPLIANCE, OBJECT-GROUP
FROM SNMPv2-CONF
lldpExtensions, lldpLocPortNum,
lldpRemTimeMark, lldpRemLocalPortNum, lldpRemIndex,
lldpPortConfigEntry
FROM LLDP-MIB;
lldpXdot3MIB MODULE-IDENTITY
LAST-UPDATED "200505060000Z" -- May 06, 2005
ORGANIZATION "IEEE 802.1 Working Group"
CONTACT-INFO
" WG-URL: http://grouper.ieee.org/groups/802/1/index.html
WG-EMail: <EMAIL>
Contact: <PERSON> Congdon
Postal: Hewlett-Packard Company
8000 Foothills Blvd.
Roseville, CA 95747
USA
Tel: ******-785-5753
E-mail: <EMAIL>"
DESCRIPTION
"The LLDP Management Information Base extension module for
IEEE 802.3 organizationally defined discovery information.
In order to assure the uniqueness of the LLDP-MIB,
lldpXdot3MIB is branched from lldpExtensions using OUI value
as the node. An OUI/'company_id' is a 24 bit globally unique
assigned number referenced by various standards.
Table G-8-802.1 extension MIB object group conformance requirements
MIB group Rx mode Tx mode Tx/Rx
mode
lldpXdot3LocSystemsGroup M* - M
lldpXdot3RemSystemsGroup - M M
*Mandatory
22Copyright release for MIBs: Users of this standard may freely reproduce the MIB contained in this subclause so that it can be used for
its intended purpose.
23An ASCII version of this MIB module can be obtained by Web browser from the IEEE 802.1 Website at http://www.ieee802.org/1/
pages/MIBS.html.
IEEE
STATION AND MEDIA ACCESS CONTROL CONNECTIVITY DISCOVERY Std 802.1AB-2005
Copyright ? 2005 IEEE. All rights reserved. 141
Copyright (C) IEEE (2005). This version of this MIB module
is published as Annex G.6.1 of IEEE Std 802.1AB-2005;
see the standard itself for full legal notices."
REVISION "200505060000Z" -- May 06, 2005
DESCRIPTION
"Published as part of IEEE Std 802.1AB-2005 initial version."
-- OUI for IEEE 802.3 is 4623 (00-12-0F)
::= { lldpExtensions 4623 }
------------------------------------------------------------------------------
------------------------------------------------------------------------------
--
-- Organizationally Defined Information Extension - IEEE 802.3
--
------------------------------------------------------------------------------
------------------------------------------------------------------------------
lldpXdot3Objects OBJECT IDENTIFIER ::= { lldpXdot3MIB 1 }
-- LLDP IEEE 802.3 extension MIB groups
lldpXdot3Config OBJECT IDENTIFIER ::= { lldpXdot3Objects 1 }
lldpXdot3LocalData OBJECT IDENTIFIER ::= { lldpXdot3Objects 2 }
lldpXdot3RemoteData OBJECT IDENTIFIER ::= { lldpXdot3Objects 3 }
-- textual conventions
LldpPowerPortClass ::= TEXTUAL-CONVENTION
STATUS current
DESCRIPTION
"This TC describes the Power over Ethernet (PoE) port class."
SYNTAX INTEGER {
pClassPSE(1),
pClassPD(2)
}
LldpLinkAggStatusMap ::= TEXTUAL-CONVENTION
STATUS current
DESCRIPTION
"This TC describes the link aggregation status.
The bit 'aggCapable(0)' indicates the link is capable of being
aggregated.
The bit 'aggEnabled(1)' indicates the link is currently in
aggregation."
SYNTAX BITS {
aggCapable(0),
aggEnabled(1)
}
------------------------------------------------------------------------------
-- IEEE 802.3 - Configuration
------------------------------------------------------------------------------
lldpXdot3PortConfigTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot3PortConfigEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"A table that controls selection of LLDP TLVs to be transmitted
on individual ports."
::= { lldpXdot3Config 1 }
lldpXdot3PortConfigEntry OBJECT-TYPE
SYNTAX LldpXdot3PortConfigEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"LLDP configuration information that controls the
transmission of IEEE 802.3 organizationally defined TLVs on
LLDP transmission capable ports.
This configuration object augments the lldpPortConfigEntry of
the LLDP-MIB, therefore it is only present along with the port
configuration defined by the associated lldpPortConfigEntry
entry.
Each active lldpXdot3PortConfigEntry must be from non-volatile
storage (along with the corresponding lldpPortConfigEntry)
after a re-initialization of the management system."
AUGMENTS { lldpPortConfigEntry }
::= { lldpXdot3PortConfigTable 1 }
LldpXdot3PortConfigEntry ::= SEQUENCE {
lldpXdot3PortConfigTLVsTxEnable BITS
}
lldpXdot3PortConfigTLVsTxEnable OBJECT-TYPE
SYNTAX BITS {
macPhyConfigStatus(0),
powerViaMDI(1),
linkAggregation(2),
maxFrameSize(3)
}
MAX-ACCESS read-write
STATUS current
DESCRIPTION
"The lldpXdot3PortConfigTLVsTxEnable, defined as a bitmap,
includes the IEEE 802.3 organizationally defined set of LLDP
TLVs whose transmission is allowed on the local LLDP agent by
the network management. Each bit in the bitmap corresponds
to an IEEE 802.3 subtype associated with a specific IEEE
802.3 optional TLV. The bit 0 is not used since there is
no corresponding subtype.
The bit 'macPhyConfigStatus(0)' indicates that LLDP agent
should transmit 'MAC/PHY configuration/status TLV'.
The bit 'powerViaMDI(1)' indicates that LLDP agent should
transmit 'Power via MDI TLV'.
The bit 'linkAggregation(2)' indicates that LLDP agent should
transmit 'Link Aggregation TLV'.
The bit 'maxFrameSize(3)' indicates that LLDP agent should
transmit 'Maximum-frame-size TLV'.
IEEE
STATION AND MEDIA ACCESS CONTROL CONNECTIVITY DISCOVERY Std 802.1AB-2005
Copyright ? 2005 IEEE. All rights reserved. 143
The default value for lldpXdot3PortConfigTLVsTxEnable object
is an empty set, which means no enumerated values are set.
The value of this object must be restored from non-volatile
storage after a re-initialization of the management system."
REFERENCE
"IEEE Std 802.1AB-2005 ********"
DEFVAL { { } }
::= { lldpXdot3PortConfigEntry 1 }
------------------------------------------------------------------------------
-- IEEE 802.3 - Local Device Information
------------------------------------------------------------------------------
---
--- lldpXdot3LocPortTable: Ethernet Port AutoNeg/Speed/Duplex
--- Information Table
---
---
lldpXdot3LocPortTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot3LocPortEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This table contains one row per port of Ethernet port
information (as a part of the LLDP 802.3 organizational
extension) on the local system known to this agent."
::= { lldpXdot3LocalData 1 }
lldpXdot3LocPortEntry OBJECT-TYPE
SYNTAX LldpXdot3LocPortEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"Information about a particular port component."
INDEX { lldpLocPortNum }
::= { lldpXdot3LocPortTable 1 }
LldpXdot3LocPortEntry ::= SEQUENCE {
lldpXdot3LocPortAutoNegSupported TruthValue,
lldpXdot3LocPortAutoNegEnabled TruthValue,
lldpXdot3LocPortAutoNegAdvertisedCap OCTET STRING,
lldpXdot3LocPortOperMauType Integer32
}
lldpXdot3LocPortAutoNegSupported OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The truth value used to indicate whether the given port
(associated with the local system) supports Auto-negotiation."
REFERENCE
"IEEE Std 802.1AB-2005 G.2.1"
::= { lldpXdot3LocPortEntry 1 }
lldpXdot3LocPortAutoNegEnabled OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The truth value used to indicate whether port
Auto-negotiation is enabled on the given port associated
with the local system."
REFERENCE
"IEEE Std 802.1AB-2005 G.2.1"
::= { lldpXdot3LocPortEntry 2 }
lldpXdot3LocPortAutoNegAdvertisedCap OBJECT-TYPE
SYNTAX OCTET STRING(SIZE(2))
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"This object contains the value (bitmap) of the
ifMauAutoNegCapAdvertisedBits object (defined in IETF RFC
3636) which is associated with the given port on the
local system."
REFERENCE
"IEEE Std 802.1AB-2005 G.2.2"
::= { lldpXdot3LocPortEntry 3 }
lldpXdot3LocPortOperMauType OBJECT-TYPE
SYNTAX Integer32(0..2147483647)
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"An integer value that indicates the operational MAU type
of the given port on the local system.
This object contains the integer value derived from the
list position of the corresponding dot3MauType as listed
in IETF RFC 3636 (or subsequent revisions) and is equal
to the last number in the respective dot3MauType OID.
For example, if the ifMauType object is dot3MauType1000BaseTHD
which corresponds to {dot3MauType 29}, the numerical value of
this field will be 29. For MAU types not listed in RFC 3636
(or subsequent revisions), the value of this field shall be
set to zero."
REFERENCE
"IEEE Std 802.1AB-2005 G.2.3"
::= { lldpXdot3LocPortEntry 4 }
---
---
--- lldpXdot3LocPowerTable: Power Ethernet Information Table
---
---
lldpXdot3LocPowerTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot3LocPowerEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This table contains one row per port of power ethernet
information (as a part of the LLDP 802.3 organizational
extension) on the local system known to this agent."
::= { lldpXdot3LocalData 2 }
lldpXdot3LocPowerEntry OBJECT-TYPE
SYNTAX LldpXdot3LocPowerEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"Information about a particular port component."
INDEX { lldpLocPortNum }
::= { lldpXdot3LocPowerTable 1 }
LldpXdot3LocPowerEntry ::= SEQUENCE {
lldpXdot3LocPowerPortClass LldpPowerPortClass,
lldpXdot3LocPowerMDISupported TruthValue,
lldpXdot3LocPowerMDIEnabled TruthValue,
lldpXdot3LocPowerPairControlable TruthValue,
lldpXdot3LocPowerPairs Integer32,
lldpXdot3LocPowerClass Integer32
}
lldpXdot3LocPowerPortClass OBJECT-TYPE
SYNTAX LldpPowerPortClass
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The value that identifies the port Class of the given port
associated with the local system."
REFERENCE
"IEEE Std 802.1AB-2005 G.3.1"
::= { lldpXdot3LocPowerEntry 1 }
lldpXdot3LocPowerMDISupported OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The truth value used to indicate whether the MDI power is
supported on the given port associated with the local system."
REFERENCE
"IEEE Std 802.1AB-2005 G.3.1"
::= { lldpXdot3LocPowerEntry 2 }
lldpXdot3LocPowerMDIEnabled OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The truth value used to identify whether MDI power is
enabled on the given port associated with the local system."
REFERENCE
"IEEE Std 802.1AB-2005 G.3.1"
::= { lldpXdot3LocPowerEntry 3 }
lldpXdot3LocPowerPairControlable OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The truth value is derived from the value of
pethPsePortPowerPairsControlAbility object (defined in IETF
RFC 3621) and is used to indicate whether the pair selection
IEEE
Std 802.1AB-2005 LOCAL AND METROPOLITAN AREA NETWORKS
146 Copyright ? 2005 IEEE. All rights reserved.
can be controlled on the given port associated with the
local system."
REFERENCE
"IEEE Std 802.1AB-2005 G.3.1"
::= { lldpXdot3LocPowerEntry 4 }
lldpXdot3LocPowerPairs OBJECT-TYPE
SYNTAX Integer32(1|2)
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"This object contains the value of the pethPsePortPowerPairs
object (defined in IETF RFC 3621) which is associated with
the given port on the local system."
REFERENCE
"IEEE Std 802.1AB-2005 G.3.2"
::= { lldpXdot3LocPowerEntry 5 }
lldpXdot3LocPowerClass OBJECT-TYPE
SYNTAX Integer32(1|2|3|4|5)
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"This object contains the value of the
pethPsePortPowerClassifications object (defined in IETF
RFC 3621) which is associated with the given port on the
local system."
REFERENCE
"IEEE Std 802.1AB-2005 G.3.3"
::= { lldpXdot3LocPowerEntry 6 }
---
---
--- lldpXdot3LocLinkAggTable: Link Aggregation Information Table
---
---
lldpXdot3LocLinkAggTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot3LocLinkAggEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This table contains one row per port of link aggregation
information (as a part of the LLDP 802.3 organizational
extension) on the local system known to this agent."
::= { lldpXdot3LocalData 3 }
lldpXdot3LocLinkAggEntry OBJECT-TYPE
SYNTAX LldpXdot3LocLinkAggEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"Link Aggregation information about a particular port
component."
INDEX { lldpLocPortNum }
::= { lldpXdot3LocLinkAggTable 1 }
LldpXdot3LocLinkAggEntry ::= SEQUENCE {
lldpXdot3LocLinkAggStatus LldpLinkAggStatusMap,
lldpXdot3LocLinkAggPortId Integer32
}
lldpXdot3LocLinkAggStatus OBJECT-TYPE
SYNTAX LldpLinkAggStatusMap
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The bitmap value contains the link aggregation capabilities
and the current aggregation status of the link."
REFERENCE
"IEEE Std 802.1AB-2005 G.4.1"
::= { lldpXdot3LocLinkAggEntry 1 }
lldpXdot3LocLinkAggPortId OBJECT-TYPE
SYNTAX Integer32(0|1..2147483647)
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"This object contains the IEEE 802.3 aggregated port
identifier, aAggPortID (IEEE 802.3-2002, ********.1),
derived from the ifNumber of the ifIndex for the port
component in link aggregation.
If the port is not in link aggregation state and/or it
does not support link aggregation, this value should be set
to zero."
REFERENCE
"IEEE Std 802.1AB-2005 G.4.2"
::= { lldpXdot3LocLinkAggEntry 2 }
---
---
--- lldpXdot3LocMaxFrameSizeTable: Maximum Frame Size information
---
---
lldpXdot3LocMaxFrameSizeTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot3LocMaxFrameSizeEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This table contains one row per port of maximum frame
size information (as a part of the LLDP 802.3 organizational
extension) on the local system known to this agent."
::= { lldpXdot3LocalData 4 }
lldpXdot3LocMaxFrameSizeEntry OBJECT-TYPE
SYNTAX LldpXdot3LocMaxFrameSizeEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"Maximum Frame Size information about a particular port
component."
INDEX { lldpLocPortNum }
::= { lldpXdot3LocMaxFrameSizeTable 1 }
LldpXdot3LocMaxFrameSizeEntry ::= SEQUENCE {
lldpXdot3LocMaxFrameSize Integer32
}
lldpXdot3LocMaxFrameSize OBJECT-TYPE
SYNTAX Integer32(0..65535)
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"An integer value indicating the maximum supported frame
size in octets on the given port of the local system."
REFERENCE
"IEEE Std 802.1AB-2005 G.5.1"
::= { lldpXdot3LocMaxFrameSizeEntry 1 }
------------------------------------------------------------------------------
-- IEEE 802.3 - Remote Devices Information
------------------------------------------------------------------------------
---
---
--- lldpXdot3RemPortTable: Ethernet Information Table
---
---
lldpXdot3RemPortTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot3RemPortEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This table contains Ethernet port information (as a part
of the LLDP 802.3 organizational extension) of the remote
system."
::= { lldpXdot3RemoteData 1 }
lldpXdot3RemPortEntry OBJECT-TYPE
SYNTAX LldpXdot3RemPortEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"Information about a particular physical network connection."
INDEX { lldpRemTimeMark,
lldpRemLocalPortNum,
lldpRemIndex }
::= { lldpXdot3RemPortTable 1 }
LldpXdot3RemPortEntry ::= SEQUENCE {
lldpXdot3RemPortAutoNegSupported TruthValue,
lldpXdot3RemPortAutoNegEnabled TruthValue,
lldpXdot3RemPortAutoNegAdvertisedCap OCTET STRING,
lldpXdot3RemPortOperMauType Integer32
}
lldpXdot3RemPortAutoNegSupported OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The truth value used to indicate whether the given port
(associated with remote system) supports Auto-negotiation."
REFERENCE
"IEEE Std 802.1AB-2005 G.2.1"
::= { lldpXdot3RemPortEntry 1 }
lldpXdot3RemPortAutoNegEnabled OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The truth value used to indicate whether port
Auto-negotiation is enabled on the given port associated
with the remote system."
REFERENCE
"IEEE Std 802.1AB-2005 G.2.1"
::= { lldpXdot3RemPortEntry 2 }
lldpXdot3RemPortAutoNegAdvertisedCap OBJECT-TYPE
SYNTAX OCTET STRING(SIZE(2))
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"This object contains the value (bitmap) of the
ifMauAutoNegCapAdvertisedBits object (defined in IETF RFC
3636) which is associated with the given port on the
remote system."
REFERENCE
"IEEE Std 802.1AB-2005 G.2.2"
::= { lldpXdot3RemPortEntry 3 }
lldpXdot3RemPortOperMauType OBJECT-TYPE
SYNTAX Integer32(0..2147483647)
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"An integer value that indicates the operational MAU type
of the sending device.
This object contains the integer value derived from the
list position of the corresponding dot3MauType as listed in
in IETF RFC 3636 (or subsequent revisions) and is equal
to the last number in the respective dot3MauType OID.
For example, if the ifMauType object is dot3MauType1000BaseTHD
which corresponds to {dot3MauType 29}, the numerical value of
this field will be 29. For MAU types not listed in RFC 3636
(or subsequent revisions), the value of this field shall be
set to zero."
REFERENCE
"IEEE Std 802.1AB-2005 G.2.3"
::= { lldpXdot3RemPortEntry 4 }
---
---
--- lldpXdot3RemPowerTable: Power Ethernet Information Table
---
---
lldpXdot3RemPowerTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot3RemPowerEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This table contains Ethernet power information (as a part
of the LLDP 802.3 organizational extension) of the remote
IEEE
Std 802.1AB-2005 LOCAL AND METROPOLITAN AREA NETWORKS
150 Copyright ? 2005 IEEE. All rights reserved.
system."
::= { lldpXdot3RemoteData 2 }
lldpXdot3RemPowerEntry OBJECT-TYPE
SYNTAX LldpXdot3RemPowerEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"Information about a particular physical network connection."
INDEX { lldpRemTimeMark,
lldpRemLocalPortNum,
lldpRemIndex }
::= { lldpXdot3RemPowerTable 1 }
LldpXdot3RemPowerEntry ::= SEQUENCE {
lldpXdot3RemPowerPortClass LldpPowerPortClass,
lldpXdot3RemPowerMDISupported TruthValue,
lldpXdot3RemPowerMDIEnabled TruthValue,
lldpXdot3RemPowerPairControlable TruthValue,
lldpXdot3RemPowerPairs Integer32,
lldpXdot3RemPowerClass Integer32
}
lldpXdot3RemPowerPortClass OBJECT-TYPE
SYNTAX LldpPowerPortClass
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The value that identifies the port Class of the given port
associated with the remote system."
REFERENCE
"IEEE Std 802.1AB-2005 G.3.1"
::= { lldpXdot3RemPowerEntry 1 }
lldpXdot3RemPowerMDISupported OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The truth value used to indicate whether the MDI power
is supported on the given port associated with the remote
system."
REFERENCE
"IEEE Std 802.1AB-2005 G.3.1"
::= { lldpXdot3RemPowerEntry 2 }
lldpXdot3RemPowerMDIEnabled OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The truth value used to identify whether MDI power is
enabled on the given port associated with the remote system."
REFERENCE
"IEEE Std 802.1AB-2005 G.3.1"
::= { lldpXdot3RemPowerEntry 3 }
lldpXdot3RemPowerPairControlable OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The truth value is derived from the value of
pethPsePortPowerPairsControlAbility object (defined in IETF
RFC 3621) and is used to indicate whether the pair selection
can be controlled on the given port associated with the
remote system."
REFERENCE
"IEEE Std 802.1AB-2005 G.3.1"
::= { lldpXdot3RemPowerEntry 4 }
lldpXdot3RemPowerPairs OBJECT-TYPE
SYNTAX Integer32(1|2)
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"This object contains the value of the pethPsePortPowerPairs
object (defined in IETF RFC 3621) which is associated with
the given port on the remote system."
REFERENCE
"IEEE Std 802.1AB-2005 G.3.2"
::= { lldpXdot3RemPowerEntry 5 }
lldpXdot3RemPowerClass OBJECT-TYPE
SYNTAX Integer32(1|2|3|4|5)
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"This object contains the value of the
pethPsePortPowerClassifications object (defined in IETF
RFC 3621) which is associated with the given port on the
remote system."
REFERENCE
"IEEE Std 802.1AB-2005 G.3.3"
::= { lldpXdot3RemPowerEntry 6 }
---
---
--- lldpXdot3RemLinkAggTable: Link Aggregation Information Table
---
---
lldpXdot3RemLinkAggTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot3RemLinkAggEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This table contains port link aggregation information
(as a part of the LLDP IEEE 802.3 organizational extension)
of the remote system."
::= { lldpXdot3RemoteData 3 }
lldpXdot3RemLinkAggEntry OBJECT-TYPE
SYNTAX LldpXdot3RemLinkAggEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"Link Aggregation information about remote system's port
component."
INDEX { lldpRemTimeMark,
lldpRemLocalPortNum,
lldpRemIndex }
::= { lldpXdot3RemLinkAggTable 1 }
LldpXdot3RemLinkAggEntry ::= SEQUENCE {
lldpXdot3RemLinkAggStatus LldpLinkAggStatusMap,
lldpXdot3RemLinkAggPortId Integer32
}
lldpXdot3RemLinkAggStatus OBJECT-TYPE
SYNTAX LldpLinkAggStatusMap
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The bitmap value contains the link aggregation capabilities
and the current aggregation status of the link."
REFERENCE
"IEEE Std 802.1AB-2005 G.4.1"
::= { lldpXdot3RemLinkAggEntry 1 }
lldpXdot3RemLinkAggPortId OBJECT-TYPE
SYNTAX Integer32(0|1..2147483647)
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"This object contains the IEEE 802.3 aggregated port
identifier, aAggPortID (IEEE Std 802.3-2002, ********.1),
derived from the ifNumber of the ifIndex for the port
component associated with the remote system.
If the remote port is not in link aggregation state and/or
it does not support link aggregation, this value should be
zero."
REFERENCE
"IEEE Std 802.1AB-2005 G.4.2"
::= { lldpXdot3RemLinkAggEntry 2 }
---
---
--- lldpXdot3RemMaxFrameSizeTable: Maximum Frame Size information
---
---
lldpXdot3RemMaxFrameSizeTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot3RemMaxFrameSizeEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This table contains one row per port of maximum frame
size information (as a part of the LLDP IEEE 802.3 organizational
extension) of the remote system."
::= { lldpXdot3RemoteData 4 }
lldpXdot3RemMaxFrameSizeEntry OBJECT-TYPE
SYNTAX LldpXdot3RemMaxFrameSizeEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"Maximum Frame Size information about a particular port
component."
INDEX { lldpRemTimeMark,
lldpRemLocalPortNum,
lldpRemIndex }
::= { lldpXdot3RemMaxFrameSizeTable 1 }
LldpXdot3RemMaxFrameSizeEntry ::= SEQUENCE {
lldpXdot3RemMaxFrameSize Integer32
}
lldpXdot3RemMaxFrameSize OBJECT-TYPE
SYNTAX Integer32(0..65535)
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"An integer value indicating the maximum supported frame
size in octets on the port component associated with the
remote system."
REFERENCE
"IEEE Std 802.1AB-2005 G.5.1"
::= { lldpXdot3RemMaxFrameSizeEntry 1 }
------------------------------------------------------------------------------
-- Conformance Information
------------------------------------------------------------------------------
lldpXdot3Conformance OBJECT IDENTIFIER ::= { lldpXdot3MIB 2 }
lldpXdot3Compliances OBJECT IDENTIFIER ::= { lldpXdot3Conformance 1 }
lldpXdot3Groups OBJECT IDENTIFIER ::= { lldpXdot3Conformance 2 }
-- compliance statements
lldpXdot3Compliance MODULE-COMPLIANCE
STATUS current
DESCRIPTION
"The compliance statement for SNMP entities which implement
the LLDP 802.3 organizational extension MIB."
MODULE -- this module
MANDATORY-GROUPS { lldpXdot3ConfigGroup,
lldpXdot3LocSysGroup,
lldpXdot3RemSysGroup
}
::= { lldpXdot3Compliances 1 }
-- MIB groupings
lldpXdot3ConfigGroup OBJECT-GROUP
OBJECTS {
lldpXdot3PortConfigTLVsTxEnable
}
STATUS current
DESCRIPTION
"The collection of objects which are used to configure the
LLDP 802.3 organizational extension implementation behavior.
This group is mandatory for agents which implement the
LLDP 802.3 organizational extension."
::= { lldpXdot3Groups 1 }
lldpXdot3LocSysGroup OBJECT-GROUP
OBJECTS {
lldpXdot3LocPortAutoNegSupported,
lldpXdot3LocPortAutoNegEnabled,
lldpXdot3LocPortAutoNegAdvertisedCap,
lldpXdot3LocPortOperMauType,
lldpXdot3LocPowerPortClass,
lldpXdot3LocPowerMDISupported,
lldpXdot3LocPowerMDIEnabled,
lldpXdot3LocPowerPairControlable,
lldpXdot3LocPowerPairs,
lldpXdot3LocPowerClass,
lldpXdot3LocLinkAggStatus,
lldpXdot3LocLinkAggPortId,
lldpXdot3LocMaxFrameSize
}
STATUS current
DESCRIPTION
"The collection of objects which are used to represent LLDP
802.3 organizational extension Local Device Information.
This group is mandatory for agents which implement the
LLDP 802.3 organizational extension in the TX mode."
::= { lldpXdot3Groups 2 }
lldpXdot3RemSysGroup OBJECT-GROUP
OBJECTS {
lldpXdot3RemPortAutoNegSupported,
lldpXdot3RemPortAutoNegEnabled,
lldpXdot3RemPortAutoNegAdvertisedCap,
lldpXdot3RemPortOperMauType,
lldpXdot3RemPowerPortClass,
lldpXdot3RemPowerMDISupported,
lldpXdot3RemPowerMDIEnabled,
lldpXdot3RemPowerPairControlable,
lldpXdot3RemPowerPairs,
lldpXdot3RemPowerClass,
lldpXdot3RemLinkAggStatus,
lldpXdot3RemLinkAggPortId,
lldpXdot3RemMaxFrameSize
}
STATUS current
DESCRIPTION
"The collection of objects which are used to represent LLDP
802.3 organizational extension Local Device Information.
This group is mandatory for agents which implement the
LLDP 802.3 organizational extension in the RX mode."
::= { lldpXdot3Groups 3 }
END
