-- ===========================================================================
-- Description: Conformance Statements for SMI
-- Reference:   Module(SNMPv2-CONF) Extracted from RFC2580.TXT,from 7526 to 17269.Obsoletes: 1904
-- ===========================================================================

SNMPv2-CONF DEFINITIONS ::= BEGIN

IMPORTS ObjectName, NotificationName, ObjectSyntax
                                               FROM SNMPv2-SMI;

-- definitions for conformance groups

OBJECT-GROUP MACRO ::=
BEGIN
    TYPE NOTATION ::=
                  ObjectsPart
                  "STATUS" Status
                  "DESCRIPTION" Text
                  ReferPart

    VALUE NOTATION ::=
                  value(VALUE OBJECT IDENTIFIER)

    ObjectsPart ::=
                  "OBJECTS" "{" Objects "}"
    Objects ::=
                  Object
                | Objects "," Object
    Object ::=
                  value(ObjectName)

    Status ::=
                  "current"
                | "deprecated"
                | "obsolete"

    ReferPart ::=
                  "REFERENCE" Text
                | empty

    -- a character string as defined in [2]
    Text ::= value(IA5String)
END

-- more definitions for conformance groups

NOTIFICATION-GROUP MACRO ::=
BEGIN
    TYPE NOTATION ::=
                  NotificationsPart
                  "STATUS" Status
                  "DESCRIPTION" Text
                  ReferPart

    VALUE NOTATION ::=
                  value(VALUE OBJECT IDENTIFIER)

    NotificationsPart ::=
                  "NOTIFICATIONS" "{" Notifications "}"
    Notifications ::=
                  Notification
                | Notifications "," Notification
    Notification ::=
                  value(NotificationName)

    Status ::=
                  "current"
                | "deprecated"
                | "obsolete"

    ReferPart ::=
                  "REFERENCE" Text
                | empty

    -- a character string as defined in [2]
    Text ::= value(IA5String)
END
-- definitions for compliance statements

MODULE-COMPLIANCE MACRO ::=
BEGIN
    TYPE NOTATION ::=
                  "STATUS" Status
                  "DESCRIPTION" Text
                  ReferPart
                  ModulePart

    VALUE NOTATION ::=
                  value(VALUE OBJECT IDENTIFIER)

    Status ::=
                  "current"
                | "deprecated"
                | "obsolete"

    ReferPart ::=
                  "REFERENCE" Text
                | empty

    ModulePart ::=
                  Modules
    Modules ::=
                  Module
                | Modules Module
    Module ::=
                  -- name of module --
                  "MODULE" ModuleName
                  MandatoryPart
                  CompliancePart

    ModuleName ::=
                  -- identifier must start with uppercase letter
                  identifier ModuleIdentifier
                  -- must not be empty unless contained
                  -- in MIB Module
                | empty
    ModuleIdentifier ::=
                  value(OBJECT IDENTIFIER)
                | empty

    MandatoryPart ::=
                  "MANDATORY-GROUPS" "{" Groups "}"
                | empty

    Groups ::=
                  Group
                | Groups "," Group
    Group ::=
                  value(OBJECT IDENTIFIER)

    CompliancePart ::=
                  Compliances
                | empty

    Compliances ::=
                  Compliance
                | Compliances Compliance
    Compliance ::=
                  ComplianceGroup
                | Object

    ComplianceGroup ::=
                  "GROUP" value(OBJECT IDENTIFIER)
                  "DESCRIPTION" Text

    Object ::=
                  "OBJECT" value(ObjectName)
                  SyntaxPart
                  WriteSyntaxPart
                  AccessPart
                  "DESCRIPTION" Text

    -- must be a refinement for object's SYNTAX clause
    SyntaxPart ::= "SYNTAX" Syntax
                | empty

    -- must be a refinement for object's SYNTAX clause
    WriteSyntaxPart ::= "WRITE-SYNTAX" Syntax
                | empty

    Syntax ::=    -- Must be one of the following:
                       -- a base type (or its refinement),
                       -- a textual convention (or its refinement), or
                       -- a BITS pseudo-type
                  type
                | "BITS" "{" NamedBits "}"

    NamedBits ::= NamedBit
                | NamedBits "," NamedBit

    NamedBit ::= identifier "(" number ")" -- number is nonnegative

    AccessPart ::=
                  "MIN-ACCESS" Access
                | empty
    Access ::=
                  "not-accessible"
                | "accessible-for-notify"
                | "read-only"
                | "read-write"
                | "read-create"

    -- a character string as defined in [2]
    Text ::= value(IA5String)
END

-- definitions for capabilities statements

AGENT-CAPABILITIES MACRO ::=
BEGIN
    TYPE NOTATION ::=
                  "PRODUCT-RELEASE" Text
                  "STATUS" Status
                  "DESCRIPTION" Text
                  ReferPart
                  ModulePart

    VALUE NOTATION ::=
                  value(VALUE OBJECT IDENTIFIER)

    Status ::=
                  "current"
                | "obsolete"

    ReferPart ::=
                  "REFERENCE" Text
                | empty

    ModulePart ::=
                  Modules
                | empty
    Modules ::=
                  Module
                | Modules Module
    Module ::=
                  -- name of module --
                  "SUPPORTS" ModuleName
                  "INCLUDES" "{" Groups "}"
                  VariationPart

    ModuleName ::=
                  -- identifier must start with uppercase letter
                  identifier ModuleIdentifier
    ModuleIdentifier ::=
                  value(OBJECT IDENTIFIER)
                | empty

    Groups ::=
                  Group
                | Groups "," Group
    Group ::=
                  value(OBJECT IDENTIFIER)

    VariationPart ::=
                  Variations
                | empty
    Variations ::=
                  Variation
                | Variations Variation

    Variation ::=
                  ObjectVariation
                | NotificationVariation

    NotificationVariation ::=
                  "VARIATION" value(NotificationName)
                  AccessPart
                  "DESCRIPTION" Text

    ObjectVariation ::=
                  "VARIATION" value(ObjectName)
                  SyntaxPart
                  WriteSyntaxPart
                  AccessPart
                  CreationPart
                  DefValPart
                  "DESCRIPTION" Text

    -- must be a refinement for object's SYNTAX clause
    SyntaxPart ::= "SYNTAX" Syntax
                | empty

    WriteSyntaxPart ::= "WRITE-SYNTAX" Syntax
                | empty

    Syntax ::=    -- Must be one of the following:
                       -- a base type (or its refinement),
                       -- a textual convention (or its refinement), or
                       -- a BITS pseudo-type
                  type
                | "BITS" "{" NamedBits "}"

    NamedBits ::= NamedBit
                | NamedBits "," NamedBit

    NamedBit ::= identifier "(" number ")" -- number is nonnegative

    AccessPart ::=
                  "ACCESS" Access
                | empty

    Access ::=
                  "not-implemented"
                -- only "not-implemented" for notifications
                | "accessible-for-notify"
                | "read-only"
                | "read-write"
                | "read-create"
                -- following is for backward-compatibility only
                | "write-only"

    CreationPart ::=
                  "CREATION-REQUIRES" "{" Cells "}"
                | empty
    Cells ::=
                  Cell
                | Cells "," Cell
    Cell ::=
                  value(ObjectName)

    DefValPart ::= "DEFVAL" "{" Defvalue "}"
                | empty

    Defvalue ::=  -- must be valid for the object's syntax
                  -- in this macro's SYNTAX clause, if present,
                  -- or if not, in object's OBJECT-TYPE macro
                  value(ObjectSyntax)
                | "{" BitsValue "}"

    BitsValue ::= BitNames
                | empty

    BitNames ::=  BitName
                | BitNames "," BitName

    BitName ::= identifier
    -- a character string as defined in [2]
    Text ::= value(IA5String)
END

END
-- ===========================================================================
-- Full Copyright Statement
-- 
--    Copyright (C) The Internet Society (2004).  This document is subject
--    to the rights, licenses and restrictions contained in BCP 78, and
--    except as set forth therein, the authors retain all their rights.
-- 
--    This document and the information contained herein are provided on an
--    "AS IS" basis and THE CONTRIBUTOR, THE ORGANIZATION HE/SHE REPRESENTS
--    OR IS SPONSORED BY (IF ANY), THE INTERNET SOCIETY AND THE INTERNET
--    ENGINEERING TASK FORCE DISCLAIM ALL WARRANTIES, EXPRESS OR IMPLIED,
--    INCLUDING BUT NOT LIMITED TO ANY WARRANTY THAT THE USE OF THE
--    INFORMATION HEREIN WILL NOT INFRINGE ANY RIGHTS OR ANY IMPLIED
--    WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.
-- 
-- Intellectual Property
-- 
--    The IETF takes no position regarding the validity or scope of any
--    Intellectual Property Rights or other rights that might be claimed to
--    pertain to the implementation or use of the technology described in
--    this document or the extent to which any license under such rights
--    might or might not be available; nor does it represent that it has
--    made any independent effort to identify any such rights.  Information
--    on the procedures with respect to rights in RFC documents can be
--    found in BCP 78 and BCP 79.
-- 
--    Copies of IPR disclosures made to the IETF Secretariat and any
--    assurances of licenses to be made available, or the result of an
--    attempt made to obtain a general license or permission for the use of
--    such proprietary rights by implementers or users of this
--    specification can be obtained from the IETF on-line IPR repository at
--    http://www.ietf.org/ipr.
-- 
--    The IETF invites any interested party to bring to its attention any
--    copyrights, patents or patent applications, or other proprietary
--    rights that may cover technology that may be required to implement
--    this standard.  Please address the information to the IETF at ietf-
--    <EMAIL>.
-- 
-- Acknowledgement
-- 
--    Funding for the RFC Editor function is currently provided by the
--    Internet Society.
-- ===========================================================================
