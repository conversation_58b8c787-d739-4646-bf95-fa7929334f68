-- ============================================================================
-- Copyright (C) by  HUAWEI TECHNOLOGIES. All rights reserved.
-- Description: 
-- Reference: None
-- Version:   V2.58
-- ============================================================================
--
-- HUAWEI-ENTITY-EXTENT-MIB.mib
-- MIB generated by Visual MIB Builder Version 4.0 Build 341
-- Friday, July 15, 2011 at 16:41:48
--

    HUAWEI-ENTITY-EXTENT-MIB DEFINITIONS ::= BEGIN
 
        IMPORTS
            entPhysicalIndex, entPhysicalVendorType, entPhysicalName, PhysicalClass, PhysicalIndex            
                FROM ENTITY-MIB            
            hwDatacomm            
                FROM HUAWEI-MIB            
            InterfaceIndex            
                FROM IF-MIB       
            EnabledStatus            
                FROM P-BRIDGE-MIB            
            SnmpAdminString            
                FROM SNMP-FRAMEWORK-MIB            
            OBJECT-GROUP, MODULE-COMPLIANCE, NOTIFICATION-GROUP            
                FROM SNMPv2-CONF            
            Integer32, Unsigned32, Counter64, BITS, OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE            
                FROM SNMPv2-SMI            
            DateAndTime, DisplayString, RowStatus, TEXTUAL-CONVENTION, AutonomousType            
                FROM SNMPv2-TC;


        -- *******.4.1.2011.5.25.31
        hwEntityExtentMIB MODULE-IDENTITY 
            LAST-UPDATED "201409170000Z"
            ORGANIZATION 
                "Huawei Technologies Co.,Ltd."
            CONTACT-INFO 
                "Huawei Industrial Base
  Bantian, Longgang
   Shenzhen 518129
   People's Republic of China
   Website: http://www.huawei.com
   Email: <EMAIL>
 "
            DESCRIPTION 
                "The private mib file includes the general extent 
                information of the device."
           REVISION     "201409170000Z" 
           DESCRIPTION  "V2.57, Add the hwEntityFaultLightKeepTime" 

	    REVISION     "201408100000Z"
            DESCRIPTION  "V2.56, Add CE hwPwrStatusTable and hwEntityFanDesc"
	    
	    REVISION     "201406230000Z"
            DESCRIPTION  "V2.55, Add access network product's hwEntityPhysicalSpecTable"
	    
            REVISION    "201406090000Z"
            DESCRIPTION  "V2.54, Add the hwEntityOpticalVendorOUI, hwEntityOpticalVendorRev, hwEntityOpticalGponSN"

	    
            REVISION     "201406090000Z"
            DESCRIPTION  "V2.53, Add hwAdmPortTable, hwAdmPortEntry and hwAdmPortDescription."

            REVISION     "201404260000Z"                                                                                   
            DESCRIPTION  "V2.52, Updated the hwEntityOpticalType"

            REVISION     "201404180000Z"
            DESCRIPTION  "V2.51, Add hwGPSLocationInfo"

            REVISION     "201403280000Z"
            DESCRIPTION  "V2.50, Add hwBatteryState"
	    
            REVISION     "201401030000Z"
            DESCRIPTION  "V2.49, Add the  hwEntityManufacturerOUI"
	    
            REVISION     "201312280000Z"
            DESCRIPTION  "V2.48, Add hwBoardSplitPorts"
	                    
            REVISION     "201312231120Z"                                                                                          
            DESCRIPTION  "V2.47, Update the hwEntityOpticalType, hwEntityOpticalFiberType" 

            REVISION     "201311281400Z"                                                                                   
            DESCRIPTION  "V2.46, Add hwEntitySplitAttribute , hwSystemPowerReservedPower"
	    
            REVISION     "201311200000Z"                                                                                   
            DESCRIPTION  "V2.45, Add hwEntityStartMode"

            REVISION     "201308230000Z"                                                                                   
            DESCRIPTION  "V2.44, Add hwBatteryInfoTable  "

            REVISION     "201307250000Z"                                                                                   
            DESCRIPTION  "V2.43, Add hwEntityOpticalLaneBiasCurrent , hwEntityOpticalLaneRxPower  ,  hwEntityOpticalLaneTxPower  "				
				
            REVISION     "201307111307Z"                                                                                   
            DESCRIPTION  "V2.42, Updated the hwEntityOpticalTunableType  "

            REVISION     "201306270000Z"                                                                                   
            DESCRIPTION  "V2.41, Add hwDeviceServiceType  "

            REVISION     "201306070000Z"                                                                                   
            DESCRIPTION  "V2.40, Add hwOSPUnifyManageObjects ,hwEntityExtOSPTrapsPrefix ,hwEntityExtUnconnected,hwEntityExtUnconnectedResume  "

            REVISION     "201306060000Z"                                                                                   
            DESCRIPTION  "V2.39, Updated the hwEntityOpticalType, hwEntityOpticalFiberType"

            REVISION     "201304270000Z"                                                                                   
            DESCRIPTION  "V2.38, Add hwEntityOpticalWaveBand"
            
            REVISION     "201304250000Z"
            DESCRIPTION  "V2.37, Add hwEntityBoardName,hwEntityBoardDescription,hwEntity5MinCpuUsage to hwEntityStateEntry"

            REVISION     "201304230000Z"
            DESCRIPTION  "V2.36, Updated the hwEntityOpticalType enum name of value 5 and add a new hwSystemGlobalObjects  hwEntitySystemServiceType "

            REVISION     "201304080000Z"
            DESCRIPTION  "V2.35, Add hwBoardSoftwareVersionIncompatible "

            REVISION     "201304030000Z"
            DESCRIPTION  "V2.34, Add hwEntityOpticalTunableModuleChannel"

            REVISION     "201303200000Z"
            DESCRIPTION  "V2.33, Add hwAlarmPnPSequenceNo and hwAlarmPnPChangeNotification "

            REVISION     "201303130000Z"
            DESCRIPTION  "V2.32, Add hwEntityOpticalTunableType and hwEntityOpticalWaveLengthDecimal "

           REVISION     "201201250000Z"
            DESCRIPTION  "V2.20, Updated the hwSystemGlobalObjects  hwEntitySystemServiceType "

          REVISION  "201409170000Z"
         DESCRIPTION  "V2.58, Updated the hwEntityBoardClass"




           
            ::= { hwDatacomm 31 }



--
-- Textual conventions
--

        HwAdminState ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " Represents the various possible administrative states.
                A value of locked means the resource is administratively
                prohibited from use. A value of shuttingDown means that
                usage is administratively limited to current instances of
                use. A value of unlocked means the resource is not
                administratively prohibited from use. A value of up means
                that the port is not administratively shut down and a value 
                of down is the oppsite. A value of loopback here means the
                port is working in the loopback local mode. When a port is
                both shut down and looped back, we user the vale of down."
            REFERENCE 
                "ITU Recommendation X.731, 'Information Technology - Open
                Systems Interconnection - System Management: State
                Management Function', 1992"
            SYNTAX INTEGER
                {
                notSupported(1),
                locked(2),
                shuttingDown(3),
                unlocked(4),
                up(11),
                down(12),
                loopback(13)
                }

        HwOperState ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " Represents the possible values of operational states.
                A value of disabled means the resource is totally
                inoperable. A value of enabled means the resource
                is partially or fully operable. The values up(11),down(12) and
                connect(13) is used only for NE5000E BTB or it's extended system.
                A value of protocolUp means the port is available in the protocol
                lay or has already prepared for transmitting or receiving data.
                A value of linkUp means the port is only available in the physical
                lay. A value of linkDown means the port is not available physically."
            REFERENCE 
                "ITU Recommendation X.731, 'Information Technology - Open
                Systems Interconnection - System Management: State
                Management Function', 1992"
            SYNTAX INTEGER
                {
                notSupported(1),
                disabled(2),
                enabled(3),
                offline(4),
                up(11),
                down(12),
                connect(13),
                protocolUp(15),
                linkUp(16),
                linkDown(17)
                }

        HwStandbyStatus ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " Represents the possible values of standby status.
                
                A value of hotStandby means the resource is not providing
                service, but is will be immediately able to take over the
                role of the resource to be backed-up, without the need for
                initialization activity, and will contain the same
                information as the resource to be backed up. A value of
                coldStandy means that the resource is to back-up another
                resource, but will not be immediately able to take over
                the role of a resource to be backed up, and will require
                some initialization activity. A value of providingService
                means the resource is providing service."
            REFERENCE 
                "ITU Recommendation X.731, 'Information Technology - Open
                Systems Interconnection - System Management: State
                Management Function', 1992"
            SYNTAX INTEGER
                {
                notSupported(1),
                hotStandby(2),
                coldStandby(3),
                providingService(4)
                }

        HwAlarmStatus ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Represents the possible values of alarm status.
                When no bits of this attribute are set, then none of the
                status conditions described below are present. When the
                value of under repair is set, the resource is currently
                being repaired.
                
                When the value of critical is set, one or more critical
                alarms are active against the resource. When the value of
                major is set, one or more major alarms are active against
                the resource. When the value of minor is set, one or more
                minor alarms are active against the resource. When the
                value of warning is set, one or more warning alarms are
                active against the resource. When the value of
                indeterminate is set, one or more  alarms of indeterminate
                severity are active against the resource.
                
                When the value of alarm outstanding is set, one or more
                alarms is active against the resource. The fault may or may
                not be disabling. "
            REFERENCE 
                "ITU Recommendation X.731, 'Information Technology - Open
                Systems Interconnection - System Management: State
                Management Function', 1992"
            SYNTAX BITS
                {
                notSupported(0),
                underRepair(1),
                critical(2),
                major(3),
                minor(4),
                alarmOutstanding(5),
                warning(6),
                indeterminate(7)
                }

        HWLevelState ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "The normal state of input line ."
            SYNTAX INTEGER
                {
                lowLevel(1),
                highLevel(2)
                }


--
-- Node definitions
--

        -- *******.4.1.2011.*********
        hwEntityExtObjects OBJECT IDENTIFIER ::= { hwEntityExtentMIB 1 }


        -- *******.4.1.2011.*********.1
        hwEntityState OBJECT IDENTIFIER ::= { hwEntityExtObjects 1 }


        -- *******.4.1.2011.*********.1.1
        hwEntityStateTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwEntityStateEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table contains one row per physical entity, There is
                always at least one row for an 'overall' physical entity.
                The information in each row may be not include all the object
                in this table, because of the entity need not some of the 
                information here. "
            ::= { hwEntityState 1 }


        -- *******.4.1.2011.*********.1.1.1
        hwEntityStateEntry OBJECT-TYPE
            SYNTAX HwEntityStateEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Information about a particular physical entity.
                Each entry provides objects (entPhysicalDescr,
                entPhysicalVendorType, and entPhysicalClass) to help an NMS
                identify and characterize the entry, and objects
                (entPhysicalContainedIn and entPhysicalParentRelPos) to help
                an NMS relate the particular entry to other entries in this
                table."
            INDEX { entPhysicalIndex }
            ::= { hwEntityStateTable 1 }


        HwEntityStateEntry ::=
            SEQUENCE { 
                hwEntityAdminStatus
                    HwAdminState,
                hwEntityOperStatus
                    HwOperState,
                hwEntityStandbyStatus
                    HwStandbyStatus,
                hwEntityAlarmLight
                    HwAlarmStatus,
                hwEntityCpuUsage
                    Integer32,
                hwEntityCpuUsageThreshold
                    Integer32,
                hwEntityMemUsage
                    Integer32,
                hwEntityMemUsageThreshold
                    Integer32,
                hwEntityMemSize
                    Integer32,
                hwEntityUpTime
                    Integer32,
                hwEntityTemperature
                    Integer32,
                hwEntityTemperatureThreshold
                    Integer32,
                hwEntityVoltage
                    Integer32,
                hwEntityVoltageLowThreshold
                    Integer32,
                hwEntityVoltageHighThreshold
                    Integer32,
                hwEntityTemperatureLowThreshold
                    Integer32,
                hwEntityOpticalPower
                    Integer32,
                hwEntityCurrent
                    Integer32,
                hwEntityMemSizeMega
                    Integer32,
                hwEntityPortType
                    INTEGER,
                hwEntityDuplex
                    INTEGER,
                hwEntityOpticalPowerRx
                    Integer32,
                hwEntityCpuUsageLowThreshold
                    Integer32,
                hwEntityBoardPower
                    Integer32,
                hwEntityCpuFrequency
                    Integer32,
                hwEntitySupportFlexCard
                    INTEGER,
                hwEntityBoardClass
                    INTEGER,
                hwNseOpmStatus
                    Integer32,
                hwEntityCpuMaxUsage
                    Integer32,
                hwEntityCPUType
                    SnmpAdminString,
                hwEntityMemoryType
                    SnmpAdminString,
                hwEntityFlashSize
                    Integer32,
                hwEntityIfUpTimes
                    Integer32,
                hwEntityIfDownTimes
                    Integer32,
                hwEntityCPUAvgUsage
                    Integer32,
                hwEntityMemoryAvgUsage
                    Integer32,
                hwEntityMemUsed
                    Unsigned32,
                hwEntityTotalFanNum
                    Integer32,
                hwEntityNomalFanNum
                    Integer32,
                hwEntityTotalPwrNum
                    Integer32,
                hwEntityNomalPwrNum
                    Integer32,
                hwEntityFaultLight
                    INTEGER,
                hwEntityBoardName
                    OCTET STRING,
                hwEntityBoardDescription
                    OCTET STRING,
                hwEntity5MinCpuUsage
                     Integer32,
                hwEntityStartMode 
                    INTEGER,
                hwEntitySplitAttribute
                    OCTET STRING,
                hwEntityFaultLightKeepTime
                     Integer32	    
             }

        -- *******.4.1.2011.*********.*******
        hwEntityAdminStatus OBJECT-TYPE
            SYNTAX HwAdminState
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                " The administrative state for this object, and it is possible to set
                the state when needed. "
            ::= { hwEntityStateEntry 1 }


        -- *******.4.1.2011.*********.*******
        hwEntityOperStatus OBJECT-TYPE
            SYNTAX HwOperState
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The operational state for this object. "
            ::= { hwEntityStateEntry 2 }


        -- *******.4.1.2011.*********.*******
        hwEntityStandbyStatus OBJECT-TYPE
            SYNTAX HwStandbyStatus
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " Some entity in a device can support standby mode,
                This object is used for monitoring standby status. "
            ::= { hwEntityStateEntry 3 }


        -- *******.4.1.2011.*********.*******
        hwEntityAlarmLight OBJECT-TYPE
            SYNTAX HwAlarmStatus
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The alarm status for this entity. It does not include
                the severity of alarms raised on child components. On 
                the condition, there are a alarm light on the entity, 
                the object should have the same status with it."
            ::= { hwEntityStateEntry 4 }


        -- *******.4.1.2011.*********.*******
        hwEntityCpuUsage OBJECT-TYPE
            SYNTAX Integer32 (0..100)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The CPU usage for this entity. Generally, the CPU usage 
                will calculate the overall CPU usage on the entity, and it 
                is not sensible with the number of CPU on the entity. "
            ::= { hwEntityStateEntry 5 }


        -- *******.4.1.2011.*********.*******
        hwEntityCpuUsageThreshold OBJECT-TYPE
            SYNTAX Integer32 (0..100)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                " The threshold for the CPU usage. When the CPU usage exceed
                the threshold, a notification will be sent. "
            ::= { hwEntityStateEntry 6 }


        -- *******.4.1.2011.*********.*******
        hwEntityMemUsage OBJECT-TYPE
            SYNTAX Integer32 (0..100)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The memory usage for the entity. This object point out how
                many percent of memory has been used. "
            ::= { hwEntityStateEntry 7 }


        -- *******.4.1.2011.*********.*******
        hwEntityMemUsageThreshold OBJECT-TYPE
            SYNTAX Integer32 (0..100)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                " The threshold for the Memory usage, When the memory usage 
                exceed the threshold, a notification will be sent. "
            ::= { hwEntityStateEntry 8 }


        -- *******.4.1.2011.*********.*******
        hwEntityMemSize OBJECT-TYPE
            SYNTAX Integer32
            UNITS "bytes"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The size of memory for the entity. "
            ::= { hwEntityStateEntry 9 }


        -- *******.4.1.2011.*********.********
        hwEntityUpTime OBJECT-TYPE
            SYNTAX Integer32
            UNITS "seconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The up time for the entity. The mean of up time is
                when the entity is up, and the value of the object 
                will add one per seconds while the entity running. "
            ::= { hwEntityStateEntry 10 }


        -- *******.4.1.2011.*********.********
        hwEntityTemperature OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The temperature for the entity. The value 0X7FFFFFF indicates invalid temperature. "
            ::= { hwEntityStateEntry 11 }


        -- *******.4.1.2011.*********.********
        hwEntityTemperatureThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                " The threshold for the temperature. When the temperature
                 exceeds the threshold, a notification will be sent. "
            ::= { hwEntityStateEntry 12 }


        -- *******.4.1.2011.*********.********
        hwEntityVoltage OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The voltage for the entity. "
            ::= { hwEntityStateEntry 13 }


        -- *******.4.1.2011.*********.********
        hwEntityVoltageLowThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                " The low-threshold for the voltage. Generally, we have two 
                threshold for the voltage, one is low-threshold, and the other
                is high-threshold. the normal status of voltage should be
                greater than the low-threshold, and lower than the high-threshold.
                When the voltage low than the low-threshold, a notification
                will be sent. "
            ::= { hwEntityStateEntry 14 }


        -- *******.4.1.2011.*********.********
        hwEntityVoltageHighThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                " The high-threshold for the voltage. Generally, we have two 
                threshold for the voltage, one is low-threshold, and the other
                is high-threshold. the normal status of voltage should be
                greater than the low-threshold, and lower than the high-threshold.
                When the voltage greater than the high-threshold, a notification
                will be sent. "
            ::= { hwEntityStateEntry 15 }


        -- *******.4.1.2011.*********.********
        hwEntityTemperatureLowThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The low-threshold of temperature. If the device temperature is 
                lower than the threshold, an alarm is raised.
                "
            ::= { hwEntityStateEntry 16 }


        -- *******.4.1.2011.*********.********
        hwEntityOpticalPower OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The optical power of the optical module. Unit: uW"
            ::= { hwEntityStateEntry 17 }


        -- *******.4.1.2011.*********.********
        hwEntityCurrent OBJECT-TYPE
            SYNTAX Integer32
            UNITS "A"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The current for the entity. "
            ::= { hwEntityStateEntry 18 }


        -- *******.4.1.2011.*********.********
        hwEntityMemSizeMega OBJECT-TYPE
            SYNTAX Integer32
            UNITS "M bytes"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The size of SDRAM(Synchronous Dynamic Random Access Memory) memory for the entity. Unit: M bytes"
            ::= { hwEntityStateEntry 19 }


        -- *******.4.1.2011.*********.********
        hwEntityPortType OBJECT-TYPE
            SYNTAX INTEGER
                {
                notSupported(1),
                copper(2),
                fiber100(3),
                fiber1000(4),
                fiber10000(5),
                opticalnotExist(6),
                optical(7)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " Indicates the type of the Ethernet interface: an optical interface or an electrical interface. "
            ::= { hwEntityStateEntry 20 }


        -- *******.4.1.2011.*********.********
        hwEntityDuplex OBJECT-TYPE
            SYNTAX INTEGER
                {
                notSupported(1),
                full(2),
                half(3)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " Indicates the duplex mode of the Ethernet interface. An electrical Ethernet interface can 
                work in half-duplex mode or full-duplex mode. An optical Ethernet can work only in full-duplex mode. "
            ::= { hwEntityStateEntry 21 }


        -- *******.4.1.2011.*********.********
        hwEntityOpticalPowerRx OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The optical power(Rx) of the optical module. Unit: uW"
            ::= { hwEntityStateEntry 22 }


        -- *******.4.1.2011.*********.********
        hwEntityCpuUsageLowThreshold OBJECT-TYPE
            SYNTAX Integer32 (0..100)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                " The threshold for the CPU usage. When the CPU usage exceed
                the threshold, a notification will be sent. "
            ::= { hwEntityStateEntry 23 }


        -- *******.4.1.2011.*********.********
        hwEntityBoardPower OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The board power for the entity. Unit: W"
            ::= { hwEntityStateEntry 24 }


        -- *******.4.1.2011.*********.********
        hwEntityCpuFrequency OBJECT-TYPE
            SYNTAX Integer32
            UNITS "bytes"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The frequency of the entity. Unit: Hz"
            ::= { hwEntityStateEntry 25 }


        -- *******.4.1.2011.*********.********
        hwEntitySupportFlexCard OBJECT-TYPE
            SYNTAX INTEGER
                {
                notSupported(1),
                flexible(2),
                unflexible(3),
                dummy(4)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " It describes whether the whole board supports flexible cards. When 
                the board doesn't support flexible cards, the cards may be dummy ones
                whose ports will not be seen by the customer."
            ::= { hwEntityStateEntry 26 }


        -- *******.4.1.2011.*********.********
        hwEntityBoardClass OBJECT-TYPE
            SYNTAX INTEGER
                {
                notSupported(1),
                mpu(2),
                lpu(3),
                sfu(4),
                icu(5),
                ecu(6),
                fan(7),
                power(8),
                lcd(9),
                pmu(10),
                cmu(11)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " It describes the board type of the whole board."
            ::= { hwEntityStateEntry 27 }


        -- *******.4.1.2011.*********.********
        hwNseOpmStatus OBJECT-TYPE
            SYNTAX Integer32 (0..2)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "OPM status. 0: pass, 1: bypass, 2: invalid"
            ::= { hwEntityStateEntry 28 }


        -- *******.4.1.2011.*********.********
        hwEntityCpuMaxUsage OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The Max CPU usage for this entity. Generally, the Max CPU usage 
                will calculate the overall CPU usage on the entity, and it 
                is not sensible with the number of CPU on the entity. "
            ::= { hwEntityStateEntry 29 }


        -- *******.4.1.2011.*********.********
        hwEntityCPUType OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "CPU type."
            ::= { hwEntityStateEntry 30 }


        -- *******.4.1.2011.*********.********
        hwEntityMemoryType OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Memory type."
            ::= { hwEntityStateEntry 31 }


        -- *******.4.1.2011.*********.********
        hwEntityFlashSize OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Flash size, in KB."
            ::= { hwEntityStateEntry 32 }


        -- *******.4.1.2011.*********.********
        hwEntityIfUpTimes OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Times that the interface goes Up."
            ::= { hwEntityStateEntry 33 }


        -- *******.4.1.2011.*********.********
        hwEntityIfDownTimes OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Times that the interface goes Down."
            ::= { hwEntityStateEntry 34 }


        -- *******.4.1.2011.*********.********
        hwEntityCPUAvgUsage OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Average CPU usage within a specified statistical interval."
            ::= { hwEntityStateEntry 35 }


        -- *******.4.1.2011.*********.********
        hwEntityMemoryAvgUsage OBJECT-TYPE
            SYNTAX Integer32 (0..100)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Average memory usage within a specified statistical interval."
            ::= { hwEntityStateEntry 36 }

        -- *******.4.1.2011.*********.********
        hwEntityMemUsed OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The memory information for the entity. This object indicates how
                many bytes in the memory have been used. "
            ::= { hwEntityStateEntry 37 }

                    
        -- *******.4.1.2011.*********.********
        hwEntityTotalFanNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total numbers of the fans in the chassis ."
            ::= { hwEntityStateEntry 38 }
            
        -- *******.4.1.2011.*********.********
        hwEntityNomalFanNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The Nomal numbers of the fans in the chassis ."
            ::= { hwEntityStateEntry 39 }
            
        -- *******.4.1.2011.*********.********
        hwEntityTotalPwrNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total numbers of the powers in the chassis ."
            ::= { hwEntityStateEntry 40 }
            
        -- *******.4.1.2011.*********.********
        hwEntityNomalPwrNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The nomal numbers of the powers in the chassis ."
            ::= { hwEntityStateEntry 41 }

        -- *******.4.1.2011.*********.********
        hwEntityFaultLight OBJECT-TYPE
            SYNTAX INTEGER
            {
                notSupported(1),
                normal(2),
                underRepair(3)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The repair status for this entity.
                On the underrepair condition, there is a blue light on the entity."
            ::= { hwEntityStateEntry 42 }

        -- *******.4.1.2011.*********.********
        hwEntityBoardName OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The board name for this entity."
            ::= { hwEntityStateEntry 43 }

        -- *******.4.1.2011.*********.********
        hwEntityBoardDescription OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The board description for this entity."
            ::= { hwEntityStateEntry 44 }

        -- *******.4.1.2011.*********.********
        hwEntity5MinCpuUsage OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Average CPU usage of the last five minutes for this entity."
            ::= { hwEntityStateEntry 45 }

        -- *******.4.1.2011.*********.********
        hwEntityStartMode OBJECT-TYPE
            SYNTAX INTEGER
            {
                notSupported(1),
                cold(2),
                warm(3),
                unknown(4)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The start mode of board."
            ::= { hwEntityStateEntry 46 } 

        -- *******.4.1.2011.*********.********
        hwEntitySplitAttribute OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The attribute for the split port."
            ::= { hwEntityStateEntry 47 }

        -- *******.4.1.2011.*********.********
        hwEntityFaultLightKeepTime OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Fault light twinkle keep time,the default is 45 seconds."
            ::= { hwEntityStateEntry 48 }

                    
        -- *******.4.1.2011.*********.1.2
        hwRUModuleInfoTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwRUModuleInfoEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table contains one row per replaceable unit physical entity.
                The row with the field 'entPhysicalIsFRU' sets to 'true' in 
                table 'entPhysicalTable' will be mapped to one row in this 
                table."
            ::= { hwEntityState 2 }


        -- *******.4.1.2011.*********.1.2.1
        hwRUModuleInfoEntry OBJECT-TYPE
            SYNTAX HwRUModuleInfoEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Information about a replaceable unit physical entity.
                Each entry provides objects of a replaceable unit to help 
                an NMS identify and characterize the entry in this table.
                "
            INDEX { entPhysicalIndex }
            ::= { hwRUModuleInfoTable 1 }


        HwRUModuleInfoEntry ::=
            SEQUENCE { 
                hwEntityBomId
                    SnmpAdminString,
                hwEntityBomEnDesc
                    SnmpAdminString,
                hwEntityBomLocalDesc
                    SnmpAdminString,
                hwEntityManufacturedDate
                    DateAndTime,
                hwEntityManufactureCode
                    Integer32,
                hwEntityCLEICode
                    SnmpAdminString,
                hwEntityUpdateLog
                    SnmpAdminString,
                hwEntityArchivesInfoVersion
                    SnmpAdminString,
                hwEntityOpenBomId
                    SnmpAdminString,
                hwEntityIssueNum
                    SnmpAdminString,
                hwEntityBoardType
                    SnmpAdminString
             }

        -- *******.4.1.2011.*********.*******
        hwEntityBomId OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The identity of the Bill of Material about this replaceable 
                unit which coded by vendor. This node is mapping to 'ITEM' 
                in the information file.
                "
            ::= { hwRUModuleInfoEntry 1 }


        -- *******.4.1.2011.*********.*******
        hwEntityBomEnDesc OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The English description of the Bill of Material about this 
                replaceable unit.
                "
            ::= { hwRUModuleInfoEntry 2 }


        -- *******.4.1.2011.*********.*******
        hwEntityBomLocalDesc OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS obsolete
            DESCRIPTION
                "The local description of the Bill of Material about this 
                replaceable unit.
                "
            ::= { hwRUModuleInfoEntry 3 }


        -- *******.4.1.2011.*********.*******
        hwEntityManufacturedDate OBJECT-TYPE
            SYNTAX DateAndTime
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The date when this physical entity is Manufactured.
                "
            ::= { hwRUModuleInfoEntry 4 }


        -- *******.4.1.2011.*********.*******
        hwEntityManufactureCode OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS obsolete
            DESCRIPTION
                "The code of the place where this physical entity is Manufactured.
                "
            ::= { hwRUModuleInfoEntry 5 }


        -- *******.4.1.2011.*********.*******
        hwEntityCLEICode OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The string code of CLEI which was granted by America.
                "
            ::= { hwRUModuleInfoEntry 6 }


        -- *******.4.1.2011.*********.*******
        hwEntityUpdateLog OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS obsolete
            DESCRIPTION
                "The maintenance log of this replaceable unit.
                "
            ::= { hwRUModuleInfoEntry 7 }


        -- *******.4.1.2011.*********.*******
        hwEntityArchivesInfoVersion OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The Version of Archives Information.
                "
            ::= { hwRUModuleInfoEntry 8 }


        -- *******.4.1.2011.*********.*******
        hwEntityOpenBomId OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The BomId for distribution. It is different from hwEntityBomId."
            ::= { hwRUModuleInfoEntry 9 }


        -- *******.4.1.2011.*********.*******0
        hwEntityIssueNum OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The issue number for hardware change."
            ::= { hwRUModuleInfoEntry 10 }


        -- *******.4.1.2011.*********.*******1
        hwEntityBoardType OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The type code for entity."
            ::= { hwRUModuleInfoEntry 11 }


        -- *******.4.1.2011.*********.1.3
        hwOpticalModuleInfoTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwOpticalModuleInfoEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Table of optical module properties."
            ::= { hwEntityState 3 }


        -- *******.4.1.2011.*********.1.3.1
        hwOpticalModuleInfoEntry OBJECT-TYPE
            SYNTAX HwOpticalModuleInfoEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "An entry in the table of optical module properties for each optical module."
            INDEX { entPhysicalIndex }
            ::= { hwOpticalModuleInfoTable 1 }


        HwOpticalModuleInfoEntry ::=
            SEQUENCE { 
                hwEntityOpticalMode
                    INTEGER,
                hwEntityOpticalWaveLength
                    Integer32,
                hwEntityOpticalTransDistance
                    Integer32,
                hwEntityOpticalVendorSn
                    SnmpAdminString,
                hwEntityOpticalTemperature
                    Integer32,
                hwEntityOpticalVoltage
                    Integer32,
                hwEntityOpticalBiasCurrent
                    Integer32,
                hwEntityOpticalRxPower
                    Integer32,
                hwEntityOpticalTxPower
                    Integer32,
                hwEntityOpticalType
                    INTEGER,
                hwEntityOpticalTransBW
                    Integer32,
                hwEntityOpticalFiberType
                    INTEGER,
                hwEntityOpticalRxLowThreshold
                    Integer32,
                hwEntityOpticalRxHighThreshold
                    Integer32,
                hwEntityOpticalTxLowThreshold
                    Integer32,
                hwEntityOpticalTxHighThreshold
                    Integer32,
                hwEntityOpticalPlug
                    INTEGER,
                hwEntityOpticalDirectionType
                    INTEGER,
                hwEntityOpticalUserEeprom
            DisplayString,
        hwEntityOpticalRxLowWarnThreshold
            Integer32,
        hwEntityOpticalRxHighWarnThreshold
            Integer32,
        hwEntityOpticalTxLowWarnThreshold
            Integer32,
        hwEntityOpticalTxHighWarnThreshold
                    Integer32,
                hwEntityOpticalVenderName
                    DisplayString,
                hwEntityOpticalVenderPn
                    DisplayString,
                hwEntityOpticalAuthenticationStatus
                   INTEGER,
                hwEntityOpticalTunableType
                   INTEGER,
                hwEntityOpticalWaveLengthDecimal
                   Integer32,
               hwEntityOpticalTunableModuleChannel
                   Integer32,
               hwEntityOpticalWaveBand
                   INTEGER,
               hwEntityOpticalLaneBiasCurrent
                   SnmpAdminString,
               hwEntityOpticalLaneRxPower
                   SnmpAdminString,
               hwEntityOpticalLaneTxPower
                   SnmpAdminString,
               hwEntityOpticalVendorOUI
                    DisplayString,
               hwEntityOpticalVendorRev                    
                    DisplayString,    
               hwEntityOpticalGponSN
                    DisplayString  
     }

        -- *******.4.1.2011.*********.*******
        hwEntityOpticalMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                notSupported(1),
                singleMode(2),
                multiMode5(3),
                multiMode6(4),
                noValue(5)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The mode of the optical module."
            DEFVAL { notSupported }
            ::= { hwOpticalModuleInfoEntry 1 }


        -- *******.4.1.2011.*********.*******
        hwEntityOpticalWaveLength OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The wave length of the optical module. Unit: nm."
            ::= { hwOpticalModuleInfoEntry 2 }


        -- *******.4.1.2011.*********.*******
        hwEntityOpticalTransDistance OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The max transmission distance of the optical module. Unit: m."
            ::= { hwOpticalModuleInfoEntry 3 }


        -- *******.4.1.2011.*********.*******
        hwEntityOpticalVendorSn OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The vendor serial number (vendor SN) of the optical module. Length: 16 bytes."
            ::= { hwOpticalModuleInfoEntry 4 }


        -- *******.4.1.2011.*********.*******
        hwEntityOpticalTemperature OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The current temperature of the optical module. Unit: C"
            ::= { hwOpticalModuleInfoEntry 5 }


        -- *******.4.1.2011.*********.*******
        hwEntityOpticalVoltage OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The current voltage of the optical module. Unit: mV."
            ::= { hwOpticalModuleInfoEntry 6 }


        -- *******.4.1.2011.*********.*******
        hwEntityOpticalBiasCurrent OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The bias current of the optical module. Unit: mA."
            ::= { hwOpticalModuleInfoEntry 7 }


        -- *******.4.1.2011.*********.*******
        hwEntityOpticalRxPower OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The receive power of the optical module. Unit: dBm. the value is expanded 100 times,so the real value should be reduced 100 times "
            ::= { hwOpticalModuleInfoEntry 8 }


        -- *******.4.1.2011.*********.*******
        hwEntityOpticalTxPower OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The transmit power of the optical module. Unit: dBm. the value is expanded 100 times,so the real value should be reduced 100 times "
            ::= { hwOpticalModuleInfoEntry 9 }


        -- *******.4.1.2011.*********.*******0
        hwEntityOpticalType OBJECT-TYPE
            SYNTAX INTEGER
                {
                unknown(0),
                sc(1),
                gbic(2),
                sfp(3),
                esfp(4),
                rj45(5),
                xfp(6),
                xenpak(7),
                transponder(8),
                cfp(9),
                smb(10),
                sfpplus(11),
                cxp(12),
                qsfp(13),
                qsfpplus(14),
                cfp2(15),
                dwdmsfp(16)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The type of the optical module."
            ::= { hwOpticalModuleInfoEntry 10 }


        -- *******.4.1.2011.*********.*******1
        hwEntityOpticalTransBW OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The Transceiver max BW of the optical module.Unit:M"
            ::= { hwOpticalModuleInfoEntry 11 }


        -- *******.4.1.2011.*********.*******2
        hwEntityOpticalFiberType OBJECT-TYPE
            SYNTAX INTEGER
                {
                unknown(0),
                sc(1),
                style1CopperConnector(2),
                style2CopperConnector(3),
                bncTnc(4),
                coaxialHeaders(5),
                fiberJack(6),
                lc(7),
                mtRj(8),
                mu(9),
                sg(10),
                opticalPigtail(11),
                hssdcII(20),
                mpo(12),
                copperPigtail(21)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The Fiber type of the optical module."
            ::= { hwOpticalModuleInfoEntry 12 }


        -- *******.4.1.2011.*********.*******3
        hwEntityOpticalRxLowThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The receive power  low threshold of the optical module. Unit: dBm. the value is expanded 100 times,so the real value should be reduced 100 times "
            ::= { hwOpticalModuleInfoEntry 13 }


        -- *******.4.1.2011.*********.*******4
        hwEntityOpticalRxHighThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The receive power  high threshold of the optical module. Unit: dBm. the value is expanded 100 times,so the real value should be reduced 100 times "
            ::= { hwOpticalModuleInfoEntry 14 }


        -- *******.4.1.2011.*********.********
        hwEntityOpticalTxLowThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The transmit power  low threshold of the optical module. Unit: dBm. the value is expanded 100 times,so the real value should be reduced 100 times "
            ::= { hwOpticalModuleInfoEntry 15 }


        -- *******.4.1.2011.*********.********
        hwEntityOpticalTxHighThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The transmit power  high threshold of the optical module. Unit: dBm. the value is expanded 100 times,so the real value should be reduced 100 times "
            ::= { hwOpticalModuleInfoEntry 16 }


        -- *******.4.1.2011.*********.********
        hwEntityOpticalPlug OBJECT-TYPE
            SYNTAX INTEGER
                {
                notSupported(0),
                true(1),
                false(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The optical module is plug-supported or not."
            ::= { hwOpticalModuleInfoEntry 17 }


        -- *******.4.1.2011.*********.********
        hwEntityOpticalDirectionType OBJECT-TYPE
            SYNTAX INTEGER
                {
                notSupported(1),
                twoFiberBidirection(2),
                oneFiberBidirection(3),
                twoFiberTwoPortBidirection(4)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The direction type of the optical module."
            ::= { hwOpticalModuleInfoEntry 18 }

        -- *******.4.1.2011.*********.********
        hwEntityOpticalUserEeprom OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..120))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The user writable EEPROM of the optical module."
            ::= { hwOpticalModuleInfoEntry 19 }
        
        -- *******.4.1.2011.*********.********
        hwEntityOpticalRxLowWarnThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The receive power low warning threshold of the optical module. Unit: dBm. the value is expanded 100 times,so the real value should be reduced 100 times "
            ::= { hwOpticalModuleInfoEntry 20 }

        
        -- *******.4.1.2011.*********.********
        hwEntityOpticalRxHighWarnThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The receive power high warning threshold of the optical module. Unit: dBm. the value is expanded 100 times,so the real value should be reduced 100 times "
            ::= { hwOpticalModuleInfoEntry 21 }

        
        -- *******.4.1.2011.*********.********
        hwEntityOpticalTxLowWarnThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The transmit power low warning threshold of the optical module. Unit: dBm. the value is expanded 100 times,so the real value should be reduced 100 times "
            ::= { hwOpticalModuleInfoEntry 22 }

        
        -- *******.4.1.2011.*********.********
        hwEntityOpticalTxHighWarnThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The transmit power high warning threshold of the optical module. Unit: dBm. the value is expanded 100 times,so the real value should be reduced 100 times "
            ::= { hwOpticalModuleInfoEntry 23 }

        -- *******.4.1.2011.*********.********
        hwEntityOpticalVenderName OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..120))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The Vender Name of the optical module."
            ::= { hwOpticalModuleInfoEntry 24 }
            
        -- *******.4.1.2011.*********.********
        hwEntityOpticalVenderPn OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..120))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The Vender PN of the optical module."
            ::= { hwOpticalModuleInfoEntry 25 }

        -- *******.4.1.2011.*********.********
        hwEntityOpticalAuthenticationStatus OBJECT-TYPE
            SYNTAX INTEGER
                {
                unknown(0),
                authenticated(1),
                unauthenticated(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The authentication status of optical module."
            ::= { hwOpticalModuleInfoEntry 26 }


        -- *******.4.1.2011.*********.********
        hwEntityOpticalTunableType OBJECT-TYPE
            SYNTAX INTEGER
                {
                notSupported(1),
                notTunable(2), 
                tunable(3),
                supportTunableType(4)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The tunable type of the optical module."
            ::= { hwOpticalModuleInfoEntry 27 }

       
        -- *******.4.1.2011.*********.********
        hwEntityOpticalWaveLengthDecimal OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The Decimal wavelength of the optical module. Unit: nm. The value is expanded 1000 times,so the real value should be reduced 1000 times."
            ::= { hwOpticalModuleInfoEntry 28 }
    
       -- *******.4.1.2011.*********.********
        hwEntityOpticalTunableModuleChannel OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The current channel of tunable optical module."
            ::= { hwOpticalModuleInfoEntry 29 }
            
        -- *******.4.1.2011.*********.********
        hwEntityOpticalWaveBand OBJECT-TYPE
            SYNTAX INTEGER
                {
                unknown(0),
                clBand(1),
                cBand(2),
                lBand(3),
                c32Band(4),
                ramancBand(5),
                ramanlBand(6),
                cwdmBand(7),
                smcBand(8),
                c96bBand(9),
                c192bBand(10)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The wave band of the optical module."
            ::= { hwOpticalModuleInfoEntry 30 }


        -- *******.4.1.2011.*********.********
        hwEntityOpticalLaneBiasCurrent OBJECT-TYPE
            SYNTAX SnmpAdminString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The bias current of the QSFP or CFP optical module. Unit: mA.(e.g.,
 	37.88,39.15,39.84,37.82)"
            ::= { hwOpticalModuleInfoEntry 31 }
			
			
        -- *******.4.1.2011.*********.********
        hwEntityOpticalLaneRxPower OBJECT-TYPE
            SYNTAX SnmpAdminString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The receive power of the QSFP or CFP optical module. Unit: dBm. the value is expanded 100 times,so the real value should be reduced 100 times (e.g.,
 	114,32,-90,-228)"
            ::= { hwOpticalModuleInfoEntry 32 }
			
			
        -- *******.4.1.2011.*********.********
        hwEntityOpticalLaneTxPower OBJECT-TYPE
            SYNTAX SnmpAdminString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The transmit power of the QSFP or CFP optical module. Unit: dBm. the value is expanded 100 times,so the real value should be reduced 100 times (e.g.,
 	94,-39,33,-58)"
            ::= { hwOpticalModuleInfoEntry 33 }

        -- *******.4.1.2011.*********.********
        hwEntityOpticalVendorOUI OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..8))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Vendor ID of the optical module assigned by IEEE."
            ::= { hwOpticalModuleInfoEntry 34 } 
            
       -- *******.4.1.2011.*********.********
        hwEntityOpticalVendorRev OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..8))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The Vender Version of the optical module."
            ::= { hwOpticalModuleInfoEntry 35 } 
            
       -- *******.4.1.2011.*********.*******6
        hwEntityOpticalGponSN OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The SN of the optical module provided by the vendor."
            ::= { hwOpticalModuleInfoEntry 36 }

			
        -- *******.4.1.2011.*********.1.4
        hwMonitorInputTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMonitorInputEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table contains one row per replaceable unit physical entity.
                This table is used to set the status of monitor port."
            ::= { hwEntityState 4 }


        -- *******.4.1.2011.*********.1.4.1
        hwMonitorInputEntry OBJECT-TYPE
            SYNTAX HwMonitorInputEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This is a replaceable unit physical entity."
            INDEX { hwMonitorInputIndex }
            ::= { hwMonitorInputTable 1 }


        HwMonitorInputEntry ::=
            SEQUENCE { 
                hwMonitorInputIndex
                    Integer32,
                hwMonitorInputName
                    DisplayString,
                hwMonitorInputState
                    HWLevelState,
                hwMonitorInputStateEnable
                    EnabledStatus,
                hwMonitorInputRowStatus
                    RowStatus
             }

        -- *******.4.1.2011.*********.*******
        hwMonitorInputIndex OBJECT-TYPE
            SYNTAX Integer32 (1..4)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Monitor port index.
                This is a number of input line,ranging from 1 to 4.
                "
            ::= { hwMonitorInputEntry 1 }


        -- *******.4.1.2011.*********.*******
        hwMonitorInputName OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Monitor line name.
                "
            ::= { hwMonitorInputEntry 2 }


        -- *******.4.1.2011.*********.*******
        hwMonitorInputState OBJECT-TYPE
            SYNTAX HWLevelState
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Monitor line status.  
                The value is high or low.
                "
            ::= { hwMonitorInputEntry 3 }


        -- *******.4.1.2011.*********.*******
        hwMonitorInputStateEnable OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Enable monitor line. 
                "
            ::= { hwMonitorInputEntry 4 }


        -- *******.4.1.2011.*********.*******
        hwMonitorInputRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Status of monitor input row.
                "
            ::= { hwMonitorInputEntry 5 }


        -- *******.4.1.2011.*********.1.5
        hwMonitorOutputTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMonitorOutputEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table contains one row per replaceable unit physical entity.
                This table is used to create a output view."
            ::= { hwEntityState 5 }


        -- *******.4.1.2011.*********.1.5.1
        hwMonitorOutputEntry OBJECT-TYPE
            SYNTAX HwMonitorOutputEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This is a replaceable unit physical entity."
            INDEX { hwMonitorOutputIndex, hwMonitorOutputRuleIndex }
            ::= { hwMonitorOutputTable 1 }


        HwMonitorOutputEntry ::=
            SEQUENCE { 
                hwMonitorOutputIndex
                    Integer32,
                hwMonitorOutputRuleIndex
                    Integer32,
                hwMonitorOutputMask
                    DisplayString,
                hwMonitorOutputKey
                    DisplayString,
                hwMonitorOutputRowStatus
                    RowStatus
             }

        -- *******.4.1.2011.*********.*******
        hwMonitorOutputIndex OBJECT-TYPE
            SYNTAX Integer32 (1..3)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Monitor output view index. 
                Through this index ,you can turn into the output view.
                "
            ::= { hwMonitorOutputEntry 1 }


        -- *******.4.1.2011.*********.*******
        hwMonitorOutputRuleIndex OBJECT-TYPE
            SYNTAX Integer32 (1..16)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Monitor output rule index. 
                This is used to set the rules about all of the input line.
                "
            ::= { hwMonitorOutputEntry 2 }


        -- *******.4.1.2011.*********.*******
        hwMonitorOutputMask OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Monitor output mask . 
                This is used to matching the level of monitor input line.
                "
            ::= { hwMonitorOutputEntry 3 }


        -- *******.4.1.2011.*********.*******
        hwMonitorOutputKey OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Normal level value of this output line.
                "
            ::= { hwMonitorOutputEntry 4 }


        -- *******.4.1.2011.*********.*******
        hwMonitorOutputRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Status of monitor input row
                "
            ::= { hwMonitorOutputEntry 5 }


        -- *******.4.1.2011.*********.1.6
        hwEntPowerUsedInfoTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwEntPowerUsedInfoEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "A table of information that displays the power use of entity ."
            ::= { hwEntityState 6 }


        -- *******.4.1.2011.*********.1.6.1
        hwEntPowerUsedInfoEntry OBJECT-TYPE
            SYNTAX HwEntPowerUsedInfoEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Entry of the table of information that displays the power use of entity."
            INDEX { entPhysicalIndex }
            ::= { hwEntPowerUsedInfoTable 1 }


        HwEntPowerUsedInfoEntry ::=
            SEQUENCE { 
                hwEntPowerUsedInfoBoardName
                    OCTET STRING,
                hwEntPowerUsedInfoBoardType
                    OCTET STRING,
                hwEntPowerUsedInfoBoardSlot
                    Integer32,
                hwEntPowerUsedInfoPower
                    Integer32
             }

        -- *******.4.1.2011.*********.*******
        hwEntPowerUsedInfoBoardName OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Board name."
            ::= { hwEntPowerUsedInfoEntry 1 }


        -- *******.4.1.2011.*********.*******
        hwEntPowerUsedInfoBoardType OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Board type."
            ::= { hwEntPowerUsedInfoEntry 2 }


        -- *******.4.1.2011.*********.*******
        hwEntPowerUsedInfoBoardSlot OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Board's slot number."
            ::= { hwEntPowerUsedInfoEntry 3 }


        -- *******.4.1.2011.*********.*******
        hwEntPowerUsedInfoPower OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Used power of board."
            ::= { hwEntPowerUsedInfoEntry 4 }


        -- *******.4.1.2011.*********.1.7
        hwVirtualCableTestTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwVirtualCableTestEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table contains one row per replaceable unit physical entity.
                This table is used to display VCT result."
            ::= { hwEntityState 7 }


        -- *******.4.1.2011.*********.1.7.1
        hwVirtualCableTestEntry OBJECT-TYPE
            SYNTAX HwVirtualCableTestEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This is a replaceable unit physical entity."
            INDEX { hwVirtualCableTestIfIndex }
            ::= { hwVirtualCableTestTable 1 }


        HwVirtualCableTestEntry ::=
            SEQUENCE { 
                hwVirtualCableTestIfIndex
                    InterfaceIndex,
                hwVirtualCableTestPairStatus
                    INTEGER,
                hwVirtualCableTestPairLength
                    Integer32,
                hwVirtualCableTestOperation
                    INTEGER,
                hwVirtualCableTestLastTime
                    Integer32,
                hwVirtualCableTestPairAStatus
                    INTEGER,
                hwVirtualCableTestPairBStatus
                    INTEGER,
                hwVirtualCableTestPairCStatus
                    INTEGER,
                hwVirtualCableTestPairDStatus
                    INTEGER,
                hwVirtualCableTestPairALength
                    Integer32,
                hwVirtualCableTestPairBLength
                    Integer32,
                hwVirtualCableTestPairCLength
                    Integer32,
                hwVirtualCableTestPairDLength
                    Integer32
             }

        -- *******.4.1.2011.*********.*******
        hwVirtualCableTestIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndex
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "IfIndex of port that you want to test cable."
            ::= { hwVirtualCableTestEntry 1 }


        -- *******.4.1.2011.*********.*******
        hwVirtualCableTestPairStatus OBJECT-TYPE
            SYNTAX INTEGER
                {
                normal(1),
                abnormalOpen(2),
                abnormalShort(3),
                abnormalOpenShort(4),
                abnormalCrossTalk(5),
                unknown(6),
                notSupport(7)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Pair status. 
                This indicate the status of cable.
                "
            ::= { hwVirtualCableTestEntry 2 }


        -- *******.4.1.2011.*********.*******
        hwVirtualCableTestPairLength OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Pair Length.
                This indicate the length of cable.
                "
            ::= { hwVirtualCableTestEntry 3 }


        -- *******.4.1.2011.*********.*******
        hwVirtualCableTestOperation OBJECT-TYPE
            SYNTAX INTEGER
                {
                startTest(1),
                resetTestValue(2),
                readyStartTest(3)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "VirtualCableTestOperation. 
                This Operation of VirtualCableTest."
            ::= { hwVirtualCableTestEntry 4 }


        -- *******.4.1.2011.*********.*******
        hwVirtualCableTestLastTime OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The last test time for the entity. The mean of last time is 
                when the entity is tested, and the value of the object 
                will add one per seconds until now."
            ::= { hwVirtualCableTestEntry 5 }


        -- *******.4.1.2011.*********.*******
        hwVirtualCableTestPairAStatus OBJECT-TYPE
            SYNTAX INTEGER
                {
                normal(1),
                abnormalOpen(2),     
                abnormalShort(3),
                abnormalOpenShort(4),    
                abnormalCrossTalk(5),
                unknown(6),
                notSupport(7)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Pair A status. 
                 This indicate the status of cable.
                 "
            ::= { hwVirtualCableTestEntry 6 }



        -- *******.4.1.2011.*********.*******
        hwVirtualCableTestPairBStatus OBJECT-TYPE
            SYNTAX INTEGER
            {
                normal(1),
                abnormalOpen(2),
                abnormalShort(3),
                abnormalOpenShort(4),
                abnormalCrossTalk(5),
                unknown(6),
                notSupport(7)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Pair B status. 
                 This indicate the status of cable.
                 "
            ::= { hwVirtualCableTestEntry 7 }


        -- *******.4.1.2011.*********.*******
        hwVirtualCableTestPairCStatus OBJECT-TYPE
            SYNTAX INTEGER
                {
                normal(1),
                abnormalOpen(2),     
                abnormalShort(3),
                abnormalOpenShort(4),    
                abnormalCrossTalk(5),
                unknown(6),
                notSupport(7)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Pair C status. 
                 This indicate the status of cable.
                 "
            ::= { hwVirtualCableTestEntry 8 }


        -- *******.4.1.2011.*********.*******
        hwVirtualCableTestPairDStatus OBJECT-TYPE
            SYNTAX INTEGER
                {
                normal(1),
                abnormalOpen(2),     
                abnormalShort(3),
                abnormalOpenShort(4),    
                abnormalCrossTalk(5),
                unknown(6),
                notSupport(7)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Pair D status. 
                 This indicate the status of cable.
                 "
            ::= { hwVirtualCableTestEntry 9 }


        -- *******.4.1.2011.*********.*******0 
        hwVirtualCableTestPairALength OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Pair A Length.
                 This indicate the length of cable.
                "
            ::= { hwVirtualCableTestEntry 10 }


        -- *******.4.1.2011.*********.*******1
        hwVirtualCableTestPairBLength OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Pair B Length.
                 This indicate the length of cable.
                "
            ::= { hwVirtualCableTestEntry 11 }


        -- *******.4.1.2011.*********.*******2
        hwVirtualCableTestPairCLength OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Pair C Length.
                 This indicate the length of cable.
                "
            ::= { hwVirtualCableTestEntry 12 }


        -- *******.4.1.2011.*********.*******3
        hwVirtualCableTestPairDLength OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Pair D Length.
                 This indicate the length of cable.
                "
            ::= { hwVirtualCableTestEntry 13 }


        -- *******.4.1.2011.*********.1.8
        hwTemperatureThresholdTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwTemperatureThresholdEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table lists the alarm thresholds of temperature sensors. 
                You can read information about all the temperature sensors of the device. 
                You can set the alarm threshold of a specified temperature sensor. "
            ::= { hwEntityState 8 }


        -- *******.4.1.2011.*********.1.8.1
        hwTemperatureThresholdEntry OBJECT-TYPE
            SYNTAX HwTemperatureThresholdEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines an entry in the hwTemperatureThresholdTable."
            INDEX { hwEntityTempSlotId, hwEntityTempI2CId, hwEntityTempAddr, hwEntityTempChannel }
            ::= { hwTemperatureThresholdTable 1 }


        HwTemperatureThresholdEntry ::=
            SEQUENCE { 
                hwEntityTempSlotId
                    Integer32,
                hwEntityTempI2CId
                    Integer32,
                hwEntityTempAddr
                    Integer32,
                hwEntityTempChannel
                    Integer32,
                hwEntityTempStatus
                    INTEGER,
                hwEntityTempValue
                    Integer32,
                hwEntityTempMinorAlmThreshold
                    Integer32,
                hwEntityTempMajorAlmThreshold
                    Integer32,
                hwEntityTempFatalAlmThreshold
                    Integer32
             }

        -- *******.4.1.2011.*********.*******
        hwEntityTempSlotId OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the slot ID of a temperature sensor. "
            ::= { hwTemperatureThresholdEntry 1 }


        -- *******.4.1.2011.*********.*******
        hwEntityTempI2CId OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the I2CID of a temperature sensor. "
            ::= { hwTemperatureThresholdEntry 2 }


        -- *******.4.1.2011.*********.*******
        hwEntityTempAddr OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the address of a temperature sensor."
            ::= { hwTemperatureThresholdEntry 3 }


        -- *******.4.1.2011.*********.*******
        hwEntityTempChannel OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the channel of a temperature sensor. "
            ::= { hwTemperatureThresholdEntry 4 }


        -- *******.4.1.2011.*********.*******
        hwEntityTempStatus OBJECT-TYPE
            SYNTAX INTEGER
                {
                normal(1),
                minor(2),
                major(3),
                fatal(4)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the current status of a temperature sensor. "
            ::= { hwTemperatureThresholdEntry 5 }


        -- *******.4.1.2011.*********.*******
        hwEntityTempValue OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the current value in Celsius of a temperature sensor."
            ::= { hwTemperatureThresholdEntry 6 }


        -- *******.4.1.2011.*********.*******
        hwEntityTempMinorAlmThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the threshold for a minor alarm. The value can be set and ranges from 80% to 120% of the default value. The value is in Celsius. "
            ::= { hwTemperatureThresholdEntry 7 }


        -- *******.4.1.2011.*********.*******
        hwEntityTempMajorAlmThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the threshold for a major alarm. The value can be set and ranges from 80% to 120% of the default value. The value is in Celsius. "
            ::= { hwTemperatureThresholdEntry 8 }


        -- *******.4.1.2011.*********.*******
        hwEntityTempFatalAlmThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the threshold for a fatal alarm. The value can be set and ranges from 80% to 120% of the default value. The value is in Celsius. "
            ::= { hwTemperatureThresholdEntry 9 }


        -- *******.4.1.2011.*********.1.9
        hwVoltageInfoTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwVoltageInfoEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table lists information about voltage sensors. You can read information about all the voltage sensors of the device. You can set the alarm threshold of a specified voltage sensor. "
            ::= { hwEntityState 9 }


        -- *******.4.1.2011.*********.1.9.1
        hwVoltageInfoEntry OBJECT-TYPE
            SYNTAX HwVoltageInfoEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines an entry in the hwVoltageInfoTable."
            INDEX { hwEntityVolSlot, hwEntityVolI2CId, hwEntityVolAddr, hwEntityVolChannel }
            ::= { hwVoltageInfoTable 1 }


        HwVoltageInfoEntry ::=
            SEQUENCE { 
                hwEntityVolSlot
                    Integer32,
                hwEntityVolI2CId
                    Integer32,
                hwEntityVolAddr
                    Integer32,
                hwEntityVolChannel
                    Integer32,
                hwEntityVolStatus
                    INTEGER,
                hwEntityVolRequired
                    Integer32,
                hwEntityVolCurValue
                    Integer32,
                hwEntityVolRatio
                    Integer32,
                hwEntityVolLowAlmMajor
                    Integer32,
                hwEntityVolLowAlmFatal
                    Integer32,
                hwEntityVolHighAlmMajor
                    Integer32,
                hwEntityVolHighAlmFatal
                    Integer32
             }

        -- *******.4.1.2011.*********.*******
        hwEntityVolSlot OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the slot ID of a voltage sensor."
            ::= { hwVoltageInfoEntry 1 }


        -- *******.4.1.2011.*********.*******
        hwEntityVolI2CId OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the I2CID of a voltage sensor."
            ::= { hwVoltageInfoEntry 2 }


        -- *******.4.1.2011.*********.*******
        hwEntityVolAddr OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the address of a voltage sensor. "
            ::= { hwVoltageInfoEntry 3 }


        -- *******.4.1.2011.*********.*******
        hwEntityVolChannel OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the channel of a voltage sensor. "
            ::= { hwVoltageInfoEntry 4 }


        -- *******.4.1.2011.*********.*******
        hwEntityVolStatus OBJECT-TYPE
            SYNTAX INTEGER
                {
                normal(1),
                major(2),
                fatal(3)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the current status of a voltage sensor."
            ::= { hwVoltageInfoEntry 5 }


        -- *******.4.1.2011.*********.*******
        hwEntityVolRequired OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the nominal value in millivolt of a voltage sensor."
            ::= { hwVoltageInfoEntry 6 }


        -- *******.4.1.2011.*********.*******
        hwEntityVolCurValue OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the current value in millivolt of a voltage sensor."
            ::= { hwVoltageInfoEntry 7 }


        -- *******.4.1.2011.*********.*******
        hwEntityVolRatio OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the voltage ratio of a voltage sensor."
            ::= { hwVoltageInfoEntry 8 }


        -- *******.4.1.2011.*********.*******
        hwEntityVolLowAlmMajor OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the threshold for a major alarm of low voltage. The value can be set and ranges from 80% to 120% of the default value. The value is in millivolt. "
            ::= { hwVoltageInfoEntry 9 }


        -- *******.4.1.2011.*********.*******0
        hwEntityVolLowAlmFatal OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the threshold for a fatal alarm of low voltage. The value can be set and ranges from 80% to 120% of the default value. The value is in millivolt. "
            ::= { hwVoltageInfoEntry 10 }


        -- *******.4.1.2011.*********.*******1
        hwEntityVolHighAlmMajor OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the threshold for a major alarm of high voltage. The value can be set and ranges from 80% to 120% of the default value. The value is in millivolt. "
            ::= { hwVoltageInfoEntry 11 }


        -- *******.4.1.2011.*********.*******2
        hwEntityVolHighAlmFatal OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the threshold for a fatal alarm of high voltage. The value can be set and ranges from 80% to 120% of the default value. The value is in millivolt. "
            ::= { hwVoltageInfoEntry 12 }


        -- *******.4.1.2011.*********.1.10
        hwFanStatusTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwFanStatusEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table lists the status of fans. You can read the status of the fans on the device."
            ::= { hwEntityState 10 }


        -- *******.4.1.2011.*********.1.10.1
        hwFanStatusEntry OBJECT-TYPE
            SYNTAX HwFanStatusEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines an entry in the hwFanStatusTable."
            INDEX { hwEntityFanSlot, hwEntityFanSn }
            ::= { hwFanStatusTable 1 }


        HwFanStatusEntry ::=
            SEQUENCE { 
                hwEntityFanSlot
                    Integer32,
                hwEntityFanSn
                    Integer32,
                hwEntityFanReg
                    INTEGER,
                hwEntityFanSpdAdjMode
                    INTEGER,
                hwEntityFanSpeed
                    Integer32,
                hwEntityFanPresent
                    INTEGER,
                hwEntityFanState
                    INTEGER,
                hwEntityFanDesc
                    OCTET STRING
             }

        -- *******.4.1.2011.*********.********
        hwEntityFanSlot OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the slot ID of a fan."
            ::= { hwFanStatusEntry 1 }


        -- *******.4.1.2011.*********.********
        hwEntityFanSn OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the serial number of the fan in a certain slot."
            ::= { hwFanStatusEntry 2 }


        -- *******.4.1.2011.*********.********
        hwEntityFanReg OBJECT-TYPE
            SYNTAX INTEGER
                {
                yes(1),
                no(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates whether the fan is registered."
            ::= { hwFanStatusEntry 3 }


        -- *******.4.1.2011.*********.********
        hwEntityFanSpdAdjMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                auto(1),
                manual(2),
                unknown(3)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the speed adjustment mode of the fan."
            ::= { hwFanStatusEntry 4 }


        -- *******.4.1.2011.*********.********
        hwEntityFanSpeed OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the rotation speed (in percentage of the full speed) of the fan. "
            ::= { hwFanStatusEntry 5 }

        -- *******.4.1.2011.*********.********
        hwEntityFanPresent OBJECT-TYPE
            SYNTAX INTEGER 
                {
                present(1),
                absent(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the present information of the fan."
            ::= { hwFanStatusEntry 6 }  

        -- *******.4.1.2011.*********.********
        hwEntityFanState OBJECT-TYPE
            SYNTAX INTEGER 
            {
                normal(1),
                abnormal(2)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the state of the fan."
            ::= { hwFanStatusEntry 7 }

        -- *******.4.1.2011.*********.********
        hwEntityFanDesc OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the description of the fan."
            ::= { hwFanStatusEntry 8 }
			
        -- *******.4.1.2011.*********.1.11
        hwEntityGlobalPara OBJECT IDENTIFIER ::= { hwEntityState 11 }


        -- *******.4.1.2011.*********.1.11.1
        hwEntityServiceType OBJECT-TYPE
            SYNTAX INTEGER
                {
                sslvpn(1),
                firewall(2),
                loadBalance(3),
                ipsec(4),
                netstream(5),
                wlan(6)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " It describes the service-type of spu board ."
            ::= { hwEntityGlobalPara 1 }

        -- *******.4.1.2011.*********.1.11.2	
        hwDeviceServiceType OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " It describes the service-type of the device. bit0 is WLAN, bit1 is SSLVPN, bit2 is ipsec,other bits temporarily without easy back extension. Each bit 0 is disable, 1 is enable."
            ::= { hwEntityGlobalPara 2 }
            
        -- *******.4.1.2011.*********.1.11.3	
        hwEntityManufacturerOUI OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " It describes the Manufacturer of the device."
            ::= { hwEntityGlobalPara 3 }

        -- *******.4.1.2011.*********.1.12
        hwPortBip8StatisticsTable OBJECT-TYPE
            SYNTAX      SEQUENCE OF HwPortBip8StatisticsEntry
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "Indicates the BIP8 statistics.
                This table lists the various BIP8 statistics."
            ::= { hwEntityState 12 }

        -- *******.4.1.2011.*********.1.12.1
        hwPortBip8StatisticsEntry OBJECT-TYPE
            SYNTAX HwPortBip8StatisticsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the BIP8 statistics.
                This table lists the various BIP8 statistics."
            INDEX  { entPhysicalIndex }    
            ::= { hwPortBip8StatisticsTable 1 }


        HwPortBip8StatisticsEntry  ::= 
            SEQUENCE{
                hwPhysicalPortBip8StatisticsEB                    Counter64,
                hwPhysicalPortBip8StatisticsES                    Counter64,
                hwPhysicalPortBip8StatisticsSES                   Counter64,
                hwPhysicalPortBip8StatisticsUAS                   Counter64,
                hwPhysicalPortBip8StatisticsBBE                   Counter64,
                hwPhysicalPortSpeed                               Unsigned32
                }

        -- *******.4.1.2011.*********.********
        hwPhysicalPortBip8StatisticsEB OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
              "BIP8 Statistics: EB(Errored Block)."
            ::= { hwPortBip8StatisticsEntry 1 }

        -- *******.4.1.2011.*********.********
        hwPhysicalPortBip8StatisticsES OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
              "BIP8 Statistics: ES(Errored Second)."
            ::= { hwPortBip8StatisticsEntry 2 }

        -- *******.4.1.2011.*********.********
        hwPhysicalPortBip8StatisticsSES OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
              "BIP8 Statistics: SES(Severely Errored Second)."
            ::= { hwPortBip8StatisticsEntry 3 }

        -- *******.4.1.2011.*********.********
        hwPhysicalPortBip8StatisticsUAS OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
              "BIP8 Statistics: UAS(Unavailable Errored Second)."
            ::= { hwPortBip8StatisticsEntry 4 }

        -- *******.4.1.2011.*********.********
        hwPhysicalPortBip8StatisticsBBE OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
              "BIP8 Statistics: BBE(Background Block Error)."
            ::= { hwPortBip8StatisticsEntry 5 }

        -- *******.4.1.2011.*********.********
        hwPhysicalPortSpeed OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
              "An estimate of the interface's current bandwidth in
                          1000000 bits per second."
            ::= { hwPortBip8StatisticsEntry 6 }


        -- *******.4.1.2011.*********.1.13
        hwStorageEntTable OBJECT-TYPE
            SYNTAX      SEQUENCE OF HwStorageEntEntry
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "Indicates the Storage statistics"
            ::= { hwEntityState 13 }
            
        -- *******.4.1.2011.*********.1.13.1
        hwStorageEntEntry OBJECT-TYPE
            SYNTAX HwStorageEntEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the Storage statistics.
                This table lists the Entity Storage statistics."
            INDEX  { entPhysicalIndex }    
            ::= { hwStorageEntTable 1 }

	HwStorageEntEntry  ::= 
            SEQUENCE{
                hwStorageEntIndex                   Integer32,
                hwStorageEntType                    Integer32,
                hwStorageEntSpace                   Integer32,
                hwStorageEntSpaceFree               Integer32,
                hwStorageEntName                    SnmpAdminString,
                hwStorageEntDescr                   SnmpAdminString
                }

	-- *******.4.1.2011.*********.********
        hwStorageEntIndex OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
              "The Index of Storage statistics."
            ::= { hwStorageEntEntry 1 }

        -- *******.4.1.2011.*********.********
        hwStorageEntType OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
              "The Type of Storage."
            ::= { hwStorageEntEntry 2 }

        -- *******.4.1.2011.*********.********
        hwStorageEntSpace OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
              "The Size of Storage."
            ::= { hwStorageEntEntry 3 }

        -- *******.4.1.2011.*********.********
        hwStorageEntSpaceFree OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
              "The Free Size of Storage."
            ::= { hwStorageEntEntry 4 }

        -- *******.4.1.2011.*********.********
        hwStorageEntName OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
              "The Name of Storage."
            ::= { hwStorageEntEntry 5 }

        -- *******.4.1.2011.*********.********
        hwStorageEntDescr OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
              "The Description of Storage."
            ::= { hwStorageEntEntry 6 }

		
		-- *******.4.1.2011.*********.1.14
		hwSystemPowerTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwSystemPowerEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"System power infomation table."
			::= { hwEntityState 14 }

		
		-- *******.4.1.2011.*********.1.14.1
		hwSystemPowerEntry OBJECT-TYPE
			SYNTAX HwSystemPowerEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"System power infomation entry."
			INDEX { hwSystemPowerDeviceID }
			::= { hwSystemPowerTable 1 }

		
	HwSystemPowerEntry ::=
			SEQUENCE { 
				hwSystemPowerDeviceID
					Integer32,
				hwSystemPowerTotalPower
					Integer32,
				hwSystemPowerUsedPower
					Integer32,
				hwSystemPowerRemainPower
					Integer32,
                                hwSystemPowerReservedPower
                                        Integer32
			 }

		-- *******.4.1.2011.*********.********
		hwSystemPowerDeviceID OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"System power infomation device ID."
			::= { hwSystemPowerEntry 1 }

		
		-- *******.4.1.2011.*********.********
		hwSystemPowerTotalPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"System power infomation total power."
			::= { hwSystemPowerEntry 2 }

		
		-- *******.4.1.2011.*********.********
		hwSystemPowerUsedPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"System power infomation used power."
			::= { hwSystemPowerEntry 3 }

		
		-- *******.4.1.2011.*********.********
		hwSystemPowerRemainPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"System power infomation remain power."
			::= { hwSystemPowerEntry 4 }

		
		-- *******.4.1.2011.*********.********
		hwSystemPowerReservedPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"System power infomation reserved power."
			::= { hwSystemPowerEntry 5 }

		
--  *******.4.1.2011.*********.1.15
		-- *******.4.1.2011.*********.1.15
		hwBatteryInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwBatteryInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description.Battery information table"
			::= { hwEntityState 15 }

		
--  *******.4.1.2011.*********.1.15.1
		-- *******.4.1.2011.*********.1.15.1
		hwBatteryInfoEntry OBJECT-TYPE
			SYNTAX HwBatteryInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description.Battery information entry"
			INDEX { entPhysicalIndex }
			::= { hwBatteryInfoTable 1 }

		
		HwBatteryInfoEntry ::=
			SEQUENCE { 
				hwBatteryState
					INTEGER,
				hwBatteryTemperatureLow
					Integer32,
			    hwBatteryTemperatureHigh
					Integer32,
				hwBatteryRemainPercent
					Integer32,
			    hwBatteryRemainTime
					Integer32,
				hwBatteryElecTimes
					Integer32,
				hwBatteryLifeThreshold
					Integer32
			 }

		-- *******.4.1.2011.*********.********
		hwBatteryState OBJECT-TYPE
			SYNTAX INTEGER
			{
			charge(1),
			discharge(2),
			full(3),
			abnormal(4)
			}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.The state of lithium battery."
			::= { hwBatteryInfoEntry 1 }

		
		-- *******.4.1.2011.*********.********
		hwBatteryTemperatureLow OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.The lowest temperature of lithium battery."
			::= { hwBatteryInfoEntry 2 }    
			
		-- *******.4.1.2011.*********.********
		hwBatteryTemperatureHigh OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.The highest temperature of lithium battery."
			::= { hwBatteryInfoEntry 3 }


		
		-- *******.4.1.2011.*********.********
		hwBatteryRemainPercent OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description. The remained electricity quantity percent of lithium battery,unit in minute."
			::= { hwBatteryInfoEntry 4 }

		
		-- *******.4.1.2011.*********.********
		hwBatteryRemainTime OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description. The remained time of lithium battery."
			::= { hwBatteryInfoEntry 5 }


		-- *******.4.1.2011.*********.********
		hwBatteryElecTimes OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description.The charge and discharge times of lithium battery."
			::= { hwBatteryInfoEntry 6 }
         
         
                                            -- *******.4.1.2011.*********.********
		hwBatteryLifeThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description.The warning threshold of lithium battery's lifetime.(unit in minute,the default value is 20)"
			::= { hwBatteryInfoEntry 7 }

			
		-- *******.4.1.2011.*********.1.16
		hwGPSLocationInfo OBJECT IDENTIFIER ::= { hwEntityState 16 }

		
		-- *******.4.1.2011.*********.1.16.1
		hwGPSLongitude OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Description.The longitude of the device location."
			::= { hwGPSLocationInfo 1 }

		
		-- *******.4.1.2011.*********.1.16.2
		hwGPSLatitude OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Description.The latidude of the device location."
			::= { hwGPSLocationInfo 2 }

		
		-- *******.4.1.2011.*********.1.16.3
		hwGPSVelocity OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" Description.The velocity of the device."
			::= { hwGPSLocationInfo 3 }
        -- *******.4.1.2011.*********.1.17
        hwAdmPortTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwAdmPortEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The table of ADM(Add/Drop Multiplexer) port properties."
            ::= { hwEntityState 17 }


        -- *******.4.1.2011.*********.1.17.1
        hwAdmPortEntry OBJECT-TYPE
            SYNTAX HwAdmPortEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The entry of ADM(Add/Drop Multiplexer) port properties table."
            INDEX { entPhysicalIndex }
            ::= { hwAdmPortTable 1 }


        HwAdmPortEntry ::=
            SEQUENCE {
                hwAdmPortDescription
                    OCTET STRING
             }


        -- *******.4.1.2011.*********.********
        hwAdmPortDescription OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The description of ADM(Add/Drop Multiplexer) port."
            ::= { hwAdmPortEntry 1 }

        -- *******.4.1.2011.*********.1.18
        hwPwrStatusTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwPwrStatusEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table lists the status of powers. You can read the status of the powers on the device."
            ::= { hwEntityState 18 }


        -- *******.4.1.2011.*********.1.18.1
        hwPwrStatusEntry OBJECT-TYPE
            SYNTAX HwPwrStatusEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines an entry in the hwPwrStatusTable."
            INDEX { hwEntityPwrSlot, hwEntityPwrSn }
            ::= { hwPwrStatusTable 1 }


        HwPwrStatusEntry ::=
            SEQUENCE { 
                hwEntityPwrSlot
                    Integer32,
                hwEntityPwrSn
                    Integer32,
                hwEntityPwrReg
                    INTEGER,
                hwEntityPwrMode
                    INTEGER,
                hwEntityPwrPresent
                    INTEGER,
                hwEntityPwrState
                    INTEGER,
                hwEntityPwrCurrent
                    Integer32,
                hwEntityPwrVoltage
                    Integer32,
                hwEntityPwrDesc
                    OCTET STRING
             }

        -- *******.4.1.2011.*********.********
        hwEntityPwrSlot OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the slot ID of a power."
            ::= { hwPwrStatusEntry 1 }


        -- *******.4.1.2011.*********.********
        hwEntityPwrSn OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the serial number of the power in a certain slot."
            ::= { hwPwrStatusEntry 2 }


        -- *******.4.1.2011.*********.********
        hwEntityPwrReg OBJECT-TYPE
            SYNTAX INTEGER
                {
                yes(1),
                no(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates whether the power is registered."
            ::= { hwPwrStatusEntry 3 }


        -- *******.4.1.2011.*********.********
        hwEntityPwrMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                unknown(1),
                dc(2),
                ac(3)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the mode of the power."
            ::= { hwPwrStatusEntry 4 }
			

        -- *******.4.1.2011.*********.********
        hwEntityPwrPresent OBJECT-TYPE
            SYNTAX INTEGER 
                {
                present(1),
                absent(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the present information of the power."
            ::= { hwPwrStatusEntry 5 }

        -- *******.4.1.2011.*********.********
        hwEntityPwrState OBJECT-TYPE
            SYNTAX INTEGER 
            {
                supply(1),
                notSupply(2),
                sleep(3),
                unknown(4)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the state of the power."
            ::= { hwPwrStatusEntry 6 }	

        -- *******.4.1.2011.*********.********
        hwEntityPwrCurrent OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the current of the power. Unit: mA "
            ::= { hwPwrStatusEntry 7 }

        -- *******.4.1.2011.*********.********
        hwEntityPwrVoltage OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the voltage of the power. Unit: mV "
            ::= { hwPwrStatusEntry 8 }

        -- *******.4.1.2011.*********.********
        hwEntityPwrDesc OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the description of the power."
            ::= { hwPwrStatusEntry 9 }
			
		-- *******.4.1.2011.*********.2
		hwEntityPhysicalSpecTable OBJECT IDENTIFIER ::= { hwEntityExtObjects 2 }

		
		-- *******.4.1.2011.*********.2.1
		hwEntityPhysicalSpecRack OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The rack coefficient used for calculating the index of the physical entity. "
			::= { hwEntityPhysicalSpecTable 1 }

		
		-- *******.4.1.2011.*********.2.2
		hwEntityPhysicalSpecFrame OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The frame coefficient used for calculating the index of the physical entity. "
			::= { hwEntityPhysicalSpecTable 2 }

		
		-- *******.4.1.2011.*********.2.3
		hwEntityPhysicalSpecSlot OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The slot coefficient used for calculating the index of the physical entity. "
			::= { hwEntityPhysicalSpecTable 3 }

		
		-- *******.4.1.2011.*********.2.4
		hwEntityPhysicalSpecBoard OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The board coefficient used for calculating the index of the physical entity. "
			::= { hwEntityPhysicalSpecTable 4 }

		
		-- *******.4.1.2011.*********.2.5
		hwEntityPhysicalSpecSubSlot OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The sub slot coefficient used for calculating the index of the physical entity. "
			::= { hwEntityPhysicalSpecTable 5 }

		
		-- *******.4.1.2011.*********.2.6
		hwEntityPhysicalSpecSubBoard OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The sub board coefficient used for calculating the index of the physical entity. "
			::= { hwEntityPhysicalSpecTable 6 }

		
		-- *******.4.1.2011.*********.2.7
		hwEntityPhysicalSpecPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The port coefficient used for calculating the index of the physical entity.  "
			::= { hwEntityPhysicalSpecTable 7 }

		
		-- *******.4.1.2011.*********.2.8
		hwEntityPhysicalSpecEmu OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The EMU(Environment monitoring unit) coefficient used for calculating the index of the physical entity.  "
			::= { hwEntityPhysicalSpecTable 8 }

		
		-- *******.4.1.2011.*********.2.9
		hwEntityPhysicalSpecPowerframe OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The power frame coefficient used for calculating the index of the physical entity.  "
			::= { hwEntityPhysicalSpecTable 9 }

		
		-- *******.4.1.2011.*********.2.10
		hwEntityPhysicalSpecPowermodule OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The power module coefficient used for calculating the index of the physical entity.  "
			::= { hwEntityPhysicalSpecTable 10 }

		
		-- *******.4.1.2011.*********.2.11
		hwEntityPhysicalSpecBattery OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The battery coefficient used for calculating the index of the physical entity.  "
			::= { hwEntityPhysicalSpecTable 11 }

		-- *******.4.1.2011.*********
		hwEntityExtTraps OBJECT IDENTIFIER ::= { hwEntityExtentMIB 2 }


        -- *******.4.1.2011.*********.0
        hwEntityExtTrapsPrefix OBJECT IDENTIFIER ::= { hwEntityExtTraps 0 }


        -- *******.4.1.2011.*********.0.1
        hwEntityExtTemperatureThresholdNotification NOTIFICATION-TYPE
            OBJECTS { hwEntityTemperature, hwEntityTemperatureThreshold, hwEntityAdminStatus, hwEntityAlarmLight }
            STATUS current
            DESCRIPTION 
                " The hwEntityEnvTemperatureThresholdNotification
                indicates the temperature have been exceed the threshold.
                In the condition, user should check the hardware and the 
                environment of the entity, sometimes it happened because
                of the failture of air-condition. Anyway, the threshold
                should lower than the value that most service work fine. "
            ::= { hwEntityExtTrapsPrefix 1 }


        -- *******.4.1.2011.*********.0.2
        hwEntityExtVoltageLowThresholdNotification NOTIFICATION-TYPE
            OBJECTS { hwEntityVoltage, hwEntityVoltageLowThreshold, hwEntityAdminStatus, hwEntityAlarmLight }
            STATUS current
            DESCRIPTION 
                " The hwEntityExtVoltageLowThresholdNotification indicates
                the voltage is lower than the threshold. This may decrease
                the usability of the entity. If the voltage is lower too much 
                than the hardware needed, the entity will stop working. 
                Anyway, the threshold should higher than the value that 
                most service work fine. "
            ::= { hwEntityExtTrapsPrefix 2 }


        -- *******.4.1.2011.*********.0.3
        hwEntityExtVoltageHighThresholdNotification NOTIFICATION-TYPE
            OBJECTS { hwEntityVoltage, hwEntityVoltageHighThreshold, hwEntityAdminStatus, hwEntityAlarmLight }
            STATUS current
            DESCRIPTION 
                " The hwEntityExtVoltageHighThresholdNotification indicates
                the voltage is higher than the threshold. This may decrease
                the usability of the entity. If the voltage is higher too much 
                than the hardware needed, the entity may be destroyed by the high
                voltage. Anyway, the threshold should lower than the value 
                that most service work fine. "
            ::= { hwEntityExtTrapsPrefix 3 }


        -- *******.4.1.2011.*********.0.4
        hwEntityExtCpuUsageThresholdNotfication NOTIFICATION-TYPE
            OBJECTS { hwEntityCpuUsage, hwEntityCpuUsageThreshold, hwEntityTemperature, hwEntityTemperatureThreshold, hwEntityAdminStatus, 
                hwEntityAlarmLight }
            STATUS current
            DESCRIPTION 
                " The hwEntityExtCpuUsageThresholdNotfication indicates
                the entity is overload, and the service running in the entity
                will not get the performance it should because of CPU's 
                performance. Anyway, the threshold should lower than the 
                value that most service work fine. "
            ::= { hwEntityExtTrapsPrefix 4 }


        -- *******.4.1.2011.*********.0.5
        hwEntityExtMemUsageThresholdNotification NOTIFICATION-TYPE
            OBJECTS { hwEntityMemUsage, hwEntityMemUsageThreshold, hwEntityMemSize, hwEntityAdminStatus, hwEntityAlarmLight
                 }
            STATUS current
            DESCRIPTION 
                " The hwEntityExtMemUsageThresholdNotification indicates
                the entity is overload, and the service running in the entity
                will not get the performance it should because of lower 
                memory. Anyway, the threshold should lower than the 
                value that most service work fine. "
            ::= { hwEntityExtTrapsPrefix 5 }


        -- *******.4.1.2011.*********.0.6
        hwEntityExtOperEnabled NOTIFICATION-TYPE
            OBJECTS { hwEntityAdminStatus, hwEntityAlarmLight }
            STATUS current
            DESCRIPTION 
                "The entity is operational. The entity this
                notification refers can be identified by
                extracting the entPhysicalIndex from one of the
                variable bindings."
            ::= { hwEntityExtTrapsPrefix 6 }


        -- *******.4.1.2011.*********.0.7
        hwEntityExtOperDisabled NOTIFICATION-TYPE
            OBJECTS { hwEntityAdminStatus, hwEntityAlarmLight }
            STATUS current
            DESCRIPTION 
                "The entity is operational. The entity this
                notification refers can be identified by
                extracting the entPhysicalIndex from one of the
                variable bindings."
            ::= { hwEntityExtTrapsPrefix 7 }


        -- *******.4.1.2011.*********.0.8
        hwEntityExtMonitorBoardAbnormalNotification NOTIFICATION-TYPE
            STATUS current
            DESCRIPTION 
                "The monitor board turns normal to abnormal."
            ::= { hwEntityExtTrapsPrefix 8 }


        -- *******.4.1.2011.*********.0.9
        hwEntityExtMonitorBoardNormalNotification NOTIFICATION-TYPE
            STATUS current
            DESCRIPTION 
                "The monitor board turns abnormal to normal."
            ::= { hwEntityExtTrapsPrefix 9 }


        -- *******.4.1.2011.*********.0.10
        hwEntityExtMonitorPortAbnormalNotification NOTIFICATION-TYPE
            OBJECTS { hwMonitorInputState, hwMonitorInputName }
            STATUS current
            DESCRIPTION 
                "The monitor port turns normal to abnormal."
            ::= { hwEntityExtTrapsPrefix 10 }


        -- *******.4.1.2011.*********.0.11
        hwEntityExtMonitorPortNormalNotification NOTIFICATION-TYPE
            OBJECTS { hwMonitorInputState, hwMonitorInputName }
            STATUS current
            DESCRIPTION 
                "The monitor port turns abnormal to normal."
            ::= { hwEntityExtTrapsPrefix 11 }


        -- *******.4.1.2011.*********.0.12
        hwEntityExtCpuUsageLowThresholdNotfication NOTIFICATION-TYPE
            OBJECTS { hwEntityCpuUsage, hwEntityCpuUsageThreshold, hwEntityTemperature, hwEntityTemperatureThreshold, hwEntityAdminStatus, 
                hwEntityAlarmLight }
            STATUS current
            DESCRIPTION 
                " The hwEntityExtCpuUsageThresholdNotfication indicates
                the entity is overload, and the service running in the entity
                will not get the performance it should because of CPU's 
                performance. Anyway, the threshold should lower than the 
                value that most service work fine. "
            ::= { hwEntityExtTrapsPrefix 12 }

		-- *******.4.1.2011.*********.1
        hwEntityExtTrapObject OBJECT IDENTIFIER ::= { hwEntityExtTraps 1 }
		
		-- *******.4.1.2011.5.25.3*******
        hwEntityExtTrapBaseSoftwareVersion OBJECT-TYPE
			SYNTAX OCTET STRING
	MAX-ACCESS accessible-for-notify
	STATUS current
	DESCRIPTION
	        "System bassic software version."
	::= { hwEntityExtTrapObject 1 }
		
		-- *******.4.1.2011.5.25.3*******
		hwEntityExtTrapBoardSoftwareVersion OBJECT-TYPE
			SYNTAX OCTET STRING
	MAX-ACCESS accessible-for-notify
	STATUS current
	DESCRIPTION
	        "Board software version compare to basic software version."
	::= { hwEntityExtTrapObject 2 }
		
		-- *******.4.1.2011.5.25.3*******
		hwPhysicalName OBJECT-TYPE
			SYNTAX OCTET STRING
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
            "The textual name of the physical entity.  The value of this
            object should be the name of the component as assigned by
            the local device and should be suitable for use in commands
            entered at the device's `console'.  This might be a text
            name, such as `console' or a simple component number (e.g.,
            port or module number), such as `1', depending on the
            physical component naming syntax of the device.

            If there is no local name, or this object is otherwise not
            applicable, then this object contains a zero-length string.

            Note that the value of entPhysicalName for two physical
            entities will be the same in the event that the console
            interface does not distinguish between them, e.g., slot-1
            and the card in slot-1."
	::= { hwEntityExtTrapObject 3 }
	
		-- *******.4.1.2011.*********.2
        hwEntityExtTrap OBJECT IDENTIFIER ::= { hwEntityExtTraps 2 }		
				
		-- *******.4.1.2011.*********.2.1
        hwBoardSoftwareVersionIncompatible NOTIFICATION-TYPE
                   OBJECTS { entPhysicalName, hwEntityExtTrapBaseSoftwareVersion, hwEntityExtTrapBoardSoftwareVersion }
                   STATUS current
                   DESCRIPTION 
	        "Board software version incompatible with basic software version."
					::= { hwEntityExtTrap 1 }
		-- *******.4.1.2011.*********.2.2
	    hwBoardSplitPorts NOTIFICATION-TYPE
                   OBJECTS { hwPhysicalName }
                   STATUS current
                   DESCRIPTION 
	        "When the split ports exist on the board, send a SNMP trap to network manager."
					::= { hwEntityExtTrap 2 }

        -- *******.4.1.2011.*********
        hwDevicePowerInfoObjects OBJECT IDENTIFIER ::= { hwEntityExtentMIB 3 }


        -- *******.4.1.2011.*********.1
        hwDevicePowerInfoTotalPower OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Device total power."
            ::= { hwDevicePowerInfoObjects 1 }


        -- *******.4.1.2011.*********.2
        hwDevicePowerInfoUsedPower OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Device used power."
            ::= { hwDevicePowerInfoObjects 2 }


        -- *******.4.1.2011.*********
        hwEntityExtConformance OBJECT IDENTIFIER ::= { hwEntityExtentMIB 4 }


        -- *******.4.1.2011.*********.1
        hwEntityExtCompliances OBJECT IDENTIFIER ::= { hwEntityExtConformance 1 }


        -- *******.4.1.2011.5.25.3*******
        hwEntityExtCompliance MODULE-COMPLIANCE
            STATUS current
            DESCRIPTION 
                "The compliance statement for systems supporting
                the HUAWEI Entity extent MIB."
            MODULE -- this module
                MANDATORY-GROUPS { hwEntityExtGroup }
                OBJECT hwEntityAdminStatus
                    MIN-ACCESS read-only
                    DESCRIPTION 
                        "Write access is not required."
                OBJECT hwEntityCpuUsageThreshold
                    MIN-ACCESS read-only
                    DESCRIPTION 
                        "Write access is not required."
                OBJECT hwEntityMemUsageThreshold
                    MIN-ACCESS read-only
                    DESCRIPTION 
                        "Write access is not required."
                OBJECT hwEntityTemperatureThreshold
                    MIN-ACCESS read-only
                    DESCRIPTION 
                        "Write access is not required."
                OBJECT hwEntityVoltageLowThreshold
                    MIN-ACCESS read-only
                    DESCRIPTION 
                        "Write access is not required."
                OBJECT hwEntityVoltageHighThreshold
                    MIN-ACCESS read-only
                    DESCRIPTION 
                        "Write access is not required."
            ::= { hwEntityExtCompliances 1 }


        -- *******.4.1.2011.*********.2
        hwEntityExtGroups OBJECT IDENTIFIER ::= { hwEntityExtConformance 2 }


        -- *******.4.1.2011.*********.2.1
        hwEntityExtGroup OBJECT-GROUP
            OBJECTS { hwEntityAdminStatus, hwEntityOperStatus, hwEntityStandbyStatus, hwEntityAlarmLight, hwEntityCpuUsage, 
                hwEntityCpuUsageThreshold, hwEntityMemUsage, hwEntityMemUsageThreshold, hwEntityMemSize, hwEntityUpTime, 
                hwEntityTemperature, hwEntityTemperatureThreshold, hwEntityVoltage, hwEntityVoltageLowThreshold, hwEntityVoltageHighThreshold, 
                hwEntityTemperatureLowThreshold, hwEntityOpticalPower, hwEntityCurrent, hwEntityMemSizeMega, hwEntityPortType, 
                hwEntityDuplex, hwEntityOpticalPowerRx, hwEntityCpuUsageLowThreshold, hwEntityBoardPower, hwEntityCpuFrequency, 
                hwEntitySupportFlexCard, hwEntityBoardClass, hwNseOpmStatus, hwEntityCpuMaxUsage, hwEntityServiceType, hwDeviceServiceType, 
                hwEntityCPUType, hwEntityMemoryType, hwEntityFlashSize, hwEntityIfUpTimes, hwEntityIfDownTimes,
                hwEntity5MinCpuUsage,hwEntityBoardDescription,hwEntityBoardName,  hwEntityStartMode , hwEntitySplitAttribute,
                hwEntityCPUAvgUsage, hwEntityMemoryAvgUsage, hwEntityExtTrapBaseSoftwareVersion, hwEntityExtTrapBoardSoftwareVersion, hwPhysicalName,
                hwGPSLongitude, hwGPSLatitude, hwGPSVelocity}
            STATUS current
            DESCRIPTION 
                "Standard HUAWEI Entity Extent group."
            ::= { hwEntityExtGroups 1 }


        -- *******.4.1.2011.*********.2.2
        hwEntityExtNotificationGroup NOTIFICATION-GROUP
            NOTIFICATIONS { hwEntityExtTemperatureThresholdNotification, hwEntityExtVoltageLowThresholdNotification, hwEntityExtVoltageHighThresholdNotification, hwEntityExtCpuUsageThresholdNotfication, hwEntityExtMemUsageThresholdNotification, 
                hwEntityExtOperEnabled, hwEntityExtOperDisabled, hwEntityExtMonitorBoardAbnormalNotification, hwEntityExtMonitorBoardNormalNotification, hwEntityExtMonitorPortAbnormalNotification, 
                hwEntityExtMonitorPortNormalNotification, hwEntityExtCpuUsageLowThresholdNotfication,hwInsertDiffFromPreDisposed,hwPreDisposedChangeNotification,hwEntityHeartbeatTrap,hwHardwareCapaChangeNotification, hwAlarmPnPChangeNotification, hwBoardSoftwareVersionIncompatible, hwBoardSplitPorts }
            STATUS current
            DESCRIPTION 
                "Standard HUAWEI Entity Extent Notification group."
            ::= { hwEntityExtGroups 2 }


        -- *******.4.1.2011.*********.2.3
        hwRUModuleInfoGroup OBJECT-GROUP
            OBJECTS { hwEntityBomId, hwEntityBomEnDesc, hwEntityManufacturedDate, hwEntityCLEICode, hwEntityArchivesInfoVersion, 
                hwEntityOpenBomId, hwEntityIssueNum, hwEntityBoardType }
            STATUS current
            DESCRIPTION 
                "Standard HUAWEI Entity Extent group."
            ::= { hwEntityExtGroups 3 }


        -- *******.4.1.2011.*********.2.4
        hwEntityExtOldObjectsGroup OBJECT-GROUP
            OBJECTS { hwEntityBomLocalDesc, hwEntityManufactureCode, hwEntityUpdateLog }
            STATUS obsolete
            DESCRIPTION 
                "Group for old objects that may be obsoleted."
            ::= { hwEntityExtGroups 4 }


        -- *******.4.1.2011.*********.2.5
        hwOpticalModuleInfoGroup OBJECT-GROUP
            OBJECTS { hwEntityOpticalMode, hwEntityOpticalWaveLength, hwEntityOpticalTransDistance, hwEntityOpticalVendorSn, hwEntityOpticalTemperature, 
                hwEntityOpticalVoltage, hwEntityOpticalBiasCurrent, hwEntityOpticalRxPower, hwEntityOpticalTxPower, hwEntityOpticalType, 
                hwEntityOpticalTransBW, hwEntityOpticalFiberType, hwEntityOpticalRxLowThreshold, hwEntityOpticalRxHighThreshold, hwEntityOpticalTxLowThreshold, 
                hwEntityOpticalTxHighThreshold, hwEntityOpticalPlug, hwEntityOpticalDirectionType, hwEntityOpticalUserEeprom, hwEntityOpticalLaneBiasCurrent, 
				hwEntityOpticalLaneRxPower, hwEntityOpticalLaneTxPower, hwEntityOpticalVendorOUI, hwEntityOpticalVendorRev, hwEntityOpticalGponSN}
            STATUS current
            DESCRIPTION 
                "Collection of objects that display the fiber module working information."
            ::= { hwEntityExtGroups 5 }


        -- *******.4.1.2011.*********.2.6
        hwMonitorInputGroup OBJECT-GROUP
            OBJECTS { hwMonitorInputName, hwMonitorInputState, hwMonitorInputStateEnable, hwMonitorInputRowStatus }
            STATUS current
            DESCRIPTION 
                "Standard HUAWEI Entity Extent group."
            ::= { hwEntityExtGroups 6 }


        -- *******.4.1.2011.*********.2.7
        hwMonitorOutputGroup OBJECT-GROUP
            OBJECTS { hwMonitorOutputMask, hwMonitorOutputKey, hwMonitorOutputRowStatus }
            STATUS current
            DESCRIPTION 
                "Standard HUAWEI Entity Extent group."
            ::= { hwEntityExtGroups 7 }


        -- *******.4.1.2011.*********.2.8
        hwEntPowerUsedInfoGroup OBJECT-GROUP
            OBJECTS { hwEntPowerUsedInfoBoardName, hwEntPowerUsedInfoBoardType, hwEntPowerUsedInfoBoardSlot, hwEntPowerUsedInfoPower }
            STATUS current
            DESCRIPTION 
                "Collection of objects that display the power informations of entity."
            ::= { hwEntityExtGroups 8 }


        -- *******.4.1.2011.*********.2.9
        hwDevicePowerInfoGroup OBJECT-GROUP
            OBJECTS { hwDevicePowerInfoTotalPower, hwDevicePowerInfoUsedPower }
            STATUS current
            DESCRIPTION 
                "Collection of objects that display the power informations of device."
            ::= { hwEntityExtGroups 9 }


        -- *******.4.1.2011.*********.2.10
        hwVirtualCableTestGroup OBJECT-GROUP
            OBJECTS { hwVirtualCableTestPairStatus, hwVirtualCableTestPairLength, hwVirtualCableTestOperation, hwVirtualCableTestLastTime,
                          hwVirtualCableTestPairAStatus,hwVirtualCableTestPairBStatus,hwVirtualCableTestPairCStatus,hwVirtualCableTestPairDStatus,
                          hwVirtualCableTestPairALength,hwVirtualCableTestPairBLength,hwVirtualCableTestPairCLength,hwVirtualCableTestPairDLength}
            STATUS current
            DESCRIPTION 
                "Collection of objects that display the status of cable."
            ::= { hwEntityExtGroups 10 }


        -- *******.4.1.2011.*********.2.11
        hwTemperatureThresholdGroup OBJECT-GROUP
            OBJECTS { hwEntityTempSlotId, hwEntityTempI2CId, hwEntityTempAddr, hwEntityTempChannel, hwEntityTempStatus, 
                hwEntityTempValue, hwEntityTempMinorAlmThreshold, hwEntityTempMajorAlmThreshold, hwEntityTempFatalAlmThreshold }
            STATUS current
            DESCRIPTION 
                "Temperature threshold group."
            ::= { hwEntityExtGroups 11 }


        -- *******.4.1.2011.*********.2.12
        hwVoltageInfoGroup OBJECT-GROUP
            OBJECTS { hwEntityVolSlot, hwEntityVolI2CId, hwEntityVolAddr, hwEntityVolChannel, hwEntityVolStatus, 
                hwEntityVolRequired, hwEntityVolLowAlmMajor, hwEntityVolLowAlmFatal, hwEntityVolHighAlmMajor, hwEntityVolHighAlmFatal, 
                hwEntityVolCurValue, hwEntityVolRatio }
            STATUS current
            DESCRIPTION 
                "Voltage info group."
            ::= { hwEntityExtGroups 12 }


        -- *******.4.1.2011.*********.2.13
        hwFanStatusGroup OBJECT-GROUP
            OBJECTS { hwEntityFanSlot, hwEntityFanSn, hwEntityFanReg, hwEntityFanSpdAdjMode, hwEntityFanSpeed,hwEntityFanPresent,hwEntityFanState,hwEntityFanDesc
                 }
            STATUS current
            DESCRIPTION 
                "fan state group."
            ::= { hwEntityExtGroups 13 }


        -- *******.4.1.2011.*********.2.14
        hwPnpGroup OBJECT-GROUP
            OBJECTS { hwAlarmPnPSequenceNo, hwHardwareCapaSequenceNo, hwFileGeneIndex, hwFileGeneResourceType, hwFileGeneResourceID, hwFileGeneDestinationFile, 
                hwFileGeneOperState, hwFileGeneRowStatus, hwHardwareCapaChangeNotification, hwAlarmPnPChangeNotification }
            STATUS current
            DESCRIPTION 
                "Pnp group."
            ::= { hwEntityExtGroups 14 }


        -- *******.4.1.2011.*********.2.15
        hwSystemGlobalGroup OBJECT-GROUP
            OBJECTS { hwEntitySystemNetID, hwEntitySoftwareName, hwEntitySoftwareVersion, hwEntitySoftwareVendor, hwEntitySystemModel, 
                hwEntitySystemTime, hwEntitySystemMacAddress, hwEntitySystemReset, hwEntitySystemHealthInterval, hwEntitySystemNEId, hwEntitySystemServiceType }
            STATUS current
            DESCRIPTION 
                "System global group."
            ::= { hwEntityExtGroups 15 }


        -- *******.4.1.2011.*********.2.16
        hwHeartbeatGroup OBJECT-GROUP
            OBJECTS { hwEntityHeartbeatOnOff, hwEntityHeartbeatPeriod, hwEntityHeartbeatTrap }
            STATUS current
            DESCRIPTION 
                "Heart beat group."
            ::= { hwEntityExtGroups 16 }

        -- *******.4.1.2011.*********.2.17
        hwPortBip8StatisticsObjectGroup OBJECT-GROUP
            OBJECTS{ hwPhysicalPortBip8StatisticsEB, hwPhysicalPortBip8StatisticsES, hwPhysicalPortBip8StatisticsSES,
                     hwPhysicalPortBip8StatisticsUAS, hwPhysicalPortBip8StatisticsBBE }
            STATUS current
            DESCRIPTION 
                "The BIP8 statistics group."
            ::= { hwEntityExtGroups 17 }

        -- *******.4.1.2011.*********.2.18
        hwPredisposeGroup OBJECT-GROUP
            OBJECTS { hwPreDisposeSequenceNo, hwDisposeSlot, hwDisposeCardId, hwDisposeSbom,  
                hwDisposeRowStatus, hwDisposeOperState, hwDisposeEntPhysicalIndex, hwDisposeEntPhysicalDescr, hwDisposeEntPhysicalVendorType, 
                hwDisposeEntPhysicalContainedIn, hwDisposeEntPhysicalClass, hwDisposeEntPhysicalParentRelPos, hwDisposeEntPhysicalName, hwInsertDiffFromPreDisposed,
                hwPreDisposedChangeNotification }
            STATUS current
            DESCRIPTION 
                "Pnp group."
            ::= { hwEntityExtGroups 18 }

-- *******.4.1.2011.*********.2.19
      hwSystemPowerGroup OBJECT-GROUP
           OBJECTS { hwSystemPowerDeviceID, hwSystemPowerTotalPower, hwSystemPowerUsedPower, hwSystemPowerRemainPower, hwSystemPowerReservedPower }
           STATUS current
          DESCRIPTION 
               "Description."
          ::= { hwEntityExtGroups 19 }

-- *******.4.1.2011.*********.2.20
        hwX86BoardGroup NOTIFICATION-GROUP
            NOTIFICATIONS {hwEntityExtUnconnected , hwEntityExtUnconnectedResume  }
            STATUS current
            DESCRIPTION 
                "X86 group."
            ::= { hwEntityExtGroups 20 }

        -- *******.4.1.2011.*********.2.21
        hwPwrStatusGroup OBJECT-GROUP
            OBJECTS { hwEntityPwrSlot, hwEntityPwrSn, hwEntityPwrReg, hwEntityPwrMode, hwEntityPwrPresent,hwEntityPwrState,hwEntityPwrCurrent,hwEntityPwrVoltage,hwEntityPwrDesc
                 }
            STATUS current
            DESCRIPTION 
                "pwr state group."
            ::= { hwEntityExtGroups 21 }
	    
        -- *******.4.1.2011.*********
        hwPnpObjects OBJECT IDENTIFIER ::= { hwEntityExtentMIB 5 }


        -- *******.4.1.2011.*********.1
        hwPnpInfo OBJECT IDENTIFIER ::= { hwPnpObjects 1 }


        -- *******.4.1.2011.5.25.3*******
        hwHardwareCapaSequenceNo OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sequence number of the 'hardware capability file'.The value consists of the file version number and CRC code."
            ::= { hwPnpInfo 1 }
			
		-- *******.4.1.2011.5.25.3*******
        hwAlarmPnPSequenceNo OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sequence number of the 'alarm capability file'.The value consists of the file version number and CRC code."
            ::= { hwPnpInfo 2 }	


        -- *******.4.1.2011.*********.2
        hwPnpTraps OBJECT IDENTIFIER ::= { hwPnpObjects 2 }


        -- *******.4.1.2011.*********.2.1
        hwHardwareCapaChangeNotification NOTIFICATION-TYPE
            OBJECTS { hwHardwareCapaSequenceNo }
            STATUS current
            DESCRIPTION 
                "This object indicates the change of the 'hardware capability file'. An alarm is generated if the sequence number of the 'hardware capability file' in the current startup file is different from that in the last startup file."
            ::= { hwPnpTraps 1 }
		
		-- *******.4.1.2011.*********.2.2
        hwAlarmPnPChangeNotification NOTIFICATION-TYPE
            OBJECTS { hwAlarmPnPSequenceNo }
            STATUS current
            DESCRIPTION 
                "This object indicates the change of the 'alarm capability file'. An alarm is generated if the sequence number of the 'alarm capability file' in the current startup file is different from that in the last startup file."
            ::= { hwPnpTraps 2 }
		

        -- *******.4.1.2011.*********.3
        hwPnpOperateTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwPnpOperateEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table describes the hardware functions. Using this table, you can obtain the specified 'hardware description file' or 'hardware capability file'."
            ::= { hwPnpObjects 3 }


        -- *******.4.1.2011.*********.3.1
        hwPnpOperateEntry OBJECT-TYPE
            SYNTAX HwPnpOperateEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION            " "
            INDEX { hwFileGeneIndex }
            ::= { hwPnpOperateTable 1 }


        HwPnpOperateEntry ::=
            SEQUENCE { 
                hwFileGeneIndex
                    Integer32,
                hwFileGeneOperState
                    INTEGER,
                hwFileGeneResourceType
                    INTEGER,
                hwFileGeneResourceID
                    SnmpAdminString,
                hwFileGeneDestinationFile
                    SnmpAdminString,
                hwFileGeneRowStatus
                    RowStatus
             }

        -- *******.4.1.2011.*********.3.1.1
        hwFileGeneIndex OBJECT-TYPE
            SYNTAX Integer32 (1..2147483647)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The value of this object identifies the operation index, which can be specified by the user. 
                The value ranges from 1 to 2147483647."
            ::= { hwPnpOperateEntry 1 }


        -- *******.4.1.2011.*********.3.1.2
        hwFileGeneOperState OBJECT-TYPE
            SYNTAX INTEGER
                {
                opInProgress(1),
                opSuccess(2),
                opGetFileError(3),
                opInvalidDestName(4),
                opNoFlashSpace(5),
                opWriteFileError(6),
                opDestoryError(7)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The value of this object identifies the operation status.
                opInProgress(1): The operation is ongoing. 
                opSuccess(2): The operation succeeds. 
                opGetFileError(3): Obtaining the file fails. 
                opInvalidDestName(4): The specified storage path is invalid. 
                opNoFlashSpace(5): The space in the flash memory is insufficient. 
                opWriteFileError(6): Writing the file fails. 
                opDestoryError(7): Deleting the file fails."
            ::= { hwPnpOperateEntry 2 }


        -- *******.4.1.2011.*********.3.1.3
        hwFileGeneResourceType OBJECT-TYPE
            SYNTAX INTEGER
                {
                pnpcard(1),
                pnpsubcard(2),
                pnphardcapability(3),
                pnpPreDisposeCapability(4),
                pnpframe(5),
                pnpdevtype(6),
                pnpalarm(7) 
               }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the type of the file to be obtained.
                pnpcard(1): board description file
                pnpcard(2): subcard description file
                pnphardcapability(3): hardware capability file
                pnpPreDisposeCapability(4): predispose capability file
                pnpframe(5):frame description file
                pnpdevtype(6):device description file
                pnpalarm(7):alarm description file"
            ::= { hwPnpOperateEntry 3 }


        -- *******.4.1.2011.*********.3.1.4
        hwFileGeneResourceID OBJECT-TYPE
            SYNTAX SnmpAdminString (SIZE (0..255))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the ID of the 'hardware capability file' to be obtained."
            ::= { hwPnpOperateEntry 4 }


        -- *******.4.1.2011.*********.3.1.5
        hwFileGeneDestinationFile OBJECT-TYPE
            SYNTAX SnmpAdminString (SIZE (0..255))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the target path storing the file, which does not need to be specified."
            ::= { hwPnpOperateEntry 5 }


        -- *******.4.1.2011.*********.3.1.6
        hwFileGeneRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "This object indicates the row status.
                active(1) 
                notInService(2) 
                notReady(3) 
                createAndGo(4) 
                createAndWait(5) 
                destroy(6)"
            ::= { hwPnpOperateEntry 6 }


        -- *******.4.1.2011.*********
        hwSystemGlobalObjects OBJECT IDENTIFIER ::= { hwEntityExtentMIB 6 }


        -- *******.4.1.2011.*********.1
        hwEntitySystemNetID OBJECT-TYPE
            SYNTAX OCTET STRING 
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Network element ID."
            ::= { hwSystemGlobalObjects 1 }


        -- *******.4.1.2011.*********.2
        hwEntitySoftwareName OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "System software name."
            ::= { hwSystemGlobalObjects 2 }


        -- *******.4.1.2011.*********.3
        hwEntitySoftwareVersion OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Software version number. "
            ::= { hwSystemGlobalObjects 3 }


        -- *******.4.1.2011.*********.4
        hwEntitySoftwareVendor OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Software vendor."
            ::= { hwSystemGlobalObjects 4 }


        -- *******.4.1.2011.*********.5
        hwEntitySystemModel OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Device model."
            ::= { hwSystemGlobalObjects 5 }


        -- *******.4.1.2011.*********.6
        hwEntitySystemTime OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "System time displayed on the device."
            ::= { hwSystemGlobalObjects 6 }


        -- *******.4.1.2011.*********.7
        hwEntitySystemMacAddress OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "MAC address of the device."
            ::= { hwSystemGlobalObjects 7 }


        -- *******.4.1.2011.*********.8
        hwEntitySystemReset OBJECT-TYPE
            SYNTAX INTEGER
                {
                normal(1),
                restart(2)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "System reset."
            ::= { hwSystemGlobalObjects 8 }


        -- *******.4.1.2011.*********.9
        hwEntitySystemHealthInterval OBJECT-TYPE
            SYNTAX Integer32 (5..900)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Statistical interval for average CPU and memory usage, in seconds (By default, the value is 300) ."
            ::= { hwSystemGlobalObjects 9 }

        -- *******.4.1.2011.*********.10
        hwEntitySystemNEId OBJECT-TYPE
            SYNTAX Integer32 (0..16777215)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Systme NEId."
            ::= { hwSystemGlobalObjects 10 }

        -- *******.4.1.2011.*********.11
        hwEntitySystemServiceType OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "System service type."
            ::= { hwSystemGlobalObjects 11 }



        -- *******.4.1.2011.*********
        hwHeartbeatObjects OBJECT IDENTIFIER ::= { hwEntityExtentMIB 7 }


        -- *******.4.1.2011.*********.1
        hwHeartbeatConfig OBJECT IDENTIFIER ::= { hwHeartbeatObjects 1 }


        -- *******.4.1.2011.5.25.3*******
        hwEntityHeartbeatOnOff OBJECT-TYPE
            SYNTAX INTEGER
                {
                on(1),
                off(2)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Whether Heartbeat sending periodically enabled."
            ::= { hwHeartbeatConfig 1 }


        -- *******.4.1.2011.5.25.3*******
        hwEntityHeartbeatPeriod OBJECT-TYPE
            SYNTAX Integer32 (60..86400)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Heartbeat sending period detected in the trap reporting channel, with the minimum value of 60 seconds."
            ::= { hwHeartbeatConfig 2 }


        -- *******.4.1.2011.*********.2
        hwHeartbeatTrapPrefix OBJECT IDENTIFIER ::= { hwHeartbeatObjects 2 }


        -- *******.4.1.2011.*********.2.1
        hwEntityHeartbeatTrap NOTIFICATION-TYPE
            STATUS current
            DESCRIPTION 
                "Heartbeat trap."
            ::= { hwHeartbeatTrapPrefix 1 }
         
        -- *******.4.1.2011.*********
        hwPreDisposeObjects OBJECT IDENTIFIER ::= { hwEntityExtentMIB 8 }

        -- *******.4.1.2011.*********.1
        hwPreDisposeInfo OBJECT IDENTIFIER ::= { hwPreDisposeObjects 1 }
        
        -- *******.4.1.2011.*********.1.1
        hwPreDisposeSequenceNo OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sequence number of the 'predispose capability file'.The value consists of the file version number and CRC code."
            ::= { hwPreDisposeInfo 1 }
        
        -- *******.4.1.2011.*********.2
        hwPreDisposedTraps OBJECT IDENTIFIER ::= { hwPreDisposeObjects 2 }
        
        -- *******.4.1.2011.*********.2.1
        hwInsertDiffFromPreDisposed NOTIFICATION-TYPE
            OBJECTS { hwDisposeEntPhysicalIndex, hwDisposeEntPhysicalVendorType, entPhysicalVendorType }
            STATUS current
            DESCRIPTION 
                "This object indicates the type of the inserted entity is different from that of pre-disposed entity on the slot."
            ::= { hwPreDisposedTraps 1 } 
            
        -- *******.4.1.2011.*********.2.2
        hwPreDisposedChangeNotification NOTIFICATION-TYPE
            OBJECTS { hwPreDisposeSequenceNo }
            STATUS current
            DESCRIPTION 
                "This object indicates the change of the 'predispose capability file'. An alarm is generated if the sequence number of the 'predispose capability file' in the current startup file is different from that in the last startup file."
            ::= { hwPreDisposedTraps 2 }     

    -- *******.4.1.2011.*********.3
        hwPreDisposeConfigTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwPreDisposeConfigEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Table of predisposing boards or flexible subcards."
            ::= { hwPreDisposeObjects 3 }
            
        -- *******.4.1.2011.*********.3.1
        hwPreDisposeConfigEntry OBJECT-TYPE
            SYNTAX HwPreDisposeConfigEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Entry of the table to predispose boards or flexible subcards."                
            INDEX { hwDisposeSlot, hwDisposeCardId }
            ::= { hwPreDisposeConfigTable 1 } 
            
    HwPreDisposeConfigEntry ::=
            SEQUENCE { 
                hwDisposeSlot
                    Integer32,
                hwDisposeCardId
                    Integer32,
                hwDisposeSbom
                    OCTET STRING,
                hwDisposeRowStatus
                    RowStatus,
                hwDisposeOperState
                    INTEGER                                    
             }                  
        
        -- *******.4.1.2011.*********.3.1.1
        hwDisposeSlot OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the slot ID of predisposed board. "
            ::= { hwPreDisposeConfigEntry 1 }           
            
        -- *******.4.1.2011.*********.3.1.2
        hwDisposeCardId OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the card ID of predisposed subcard. "
            ::= { hwPreDisposeConfigEntry 2 }
            
        -- *******.4.1.2011.*********.3.1.3
        hwDisposeSbom OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the sbom information of predisposed board or subcard. "
            ::= { hwPreDisposeConfigEntry 3 }
            
        -- *******.4.1.2011.*********.3.1.4
        hwDisposeRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "This object indicates the row status when config predispose.
                active(1) 
                notInService(2) 
                notReady(3) 
                createAndGo(4) 
                createAndWait(5) 
                destroy(6)"
            ::= { hwPreDisposeConfigEntry 4 }  
            
        -- *******.4.1.2011.*********.3.1.5
        hwDisposeOperState OBJECT-TYPE
            SYNTAX INTEGER
                {
                opSuccess(1),
                opInProgress(2),
                opDevNotSupportPredispose(3),
                opCardNotSupportPredispose(4),
                opAlreadyPredispose(5),
                opCardConflict(6),
                opDevOperationError(7)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The value of this object identifies the operation status.
                opSuccess(1): The operation succeeds.
                opInProgress(2): The operation is ongoing.  
                opDevNotSupportPredispose(3): The device does not support predispose. 
                opCardNotSupportPredispose(4): The type of card or subcard is not supported to be predisposed on this device. 
                opAlreadyPredispose(5): The slot or subslot has already been predisposed. 
                opCardConflict(6): The slot or subslot to be predisposed conflict with others. 
                opDevOperationError(7): Other error occurs on the device."
            ::= { hwPreDisposeConfigEntry 5 }     
            
        -- *******.4.1.2011.*********.4
        hwPreDisposeEntInfoTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwPreDisposeEntInfoEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table is used to get the information of predisposed entitys."
            ::= { hwPreDisposeObjects 4 }
            
        -- *******.4.1.2011.*********.4.1
        hwPreDisposeEntInfoEntry OBJECT-TYPE
            SYNTAX HwPreDisposeEntInfoEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Entry of the table to get the information of predisposed entitys."                
            INDEX { hwDisposeEntPhysicalIndex }
            ::= { hwPreDisposeEntInfoTable 1 } 
            
    HwPreDisposeEntInfoEntry ::=
            SEQUENCE { 
                hwDisposeEntPhysicalIndex            PhysicalIndex,
                hwDisposeEntPhysicalDescr            SnmpAdminString,
                hwDisposeEntPhysicalVendorType       AutonomousType,
                hwDisposeEntPhysicalContainedIn      Integer32,
                hwDisposeEntPhysicalClass            PhysicalClass,
                hwDisposeEntPhysicalParentRelPos     Integer32,
                hwDisposeEntPhysicalName             SnmpAdminString                
             }
        
        -- *******.4.1.2011.*********.4.1.1     
        hwDisposeEntPhysicalIndex    OBJECT-TYPE
           SYNTAX      PhysicalIndex
           MAX-ACCESS  accessible-for-notify
           STATUS      current
           DESCRIPTION
               "The index for this entry."
           ::= { hwPreDisposeEntInfoEntry 1 }
        
        -- *******.4.1.2011.*********.4.1.2
        hwDisposeEntPhysicalDescr OBJECT-TYPE
           SYNTAX      SnmpAdminString
           MAX-ACCESS  read-only
           STATUS      current
           DESCRIPTION
               "A textual description of predisposed physical entity.This object
               should contain a string which identifies the manufacturer's
               name for the physical entity. "
           ::= { hwPreDisposeEntInfoEntry 2 }
        
        -- *******.4.1.2011.*********.4.1.3
        hwDisposeEntPhysicalVendorType OBJECT-TYPE
           SYNTAX      AutonomousType
           MAX-ACCESS  read-only
           STATUS      current
           DESCRIPTION
               "An indication of the vendor-specific hardware type of the
               predisposed physical entity.       
               "
           ::= { hwPreDisposeEntInfoEntry 3 }
        
        -- *******.4.1.2011.*********.4.1.4
        hwDisposeEntPhysicalContainedIn OBJECT-TYPE
           SYNTAX      Integer32 (0..2147483647)
           MAX-ACCESS  read-only
           STATUS      current
           DESCRIPTION
               "The value of 'entPhysicalContainedIn' for the predisposed physical entity."
           ::= { hwPreDisposeEntInfoEntry 4 }
        
        -- *******.4.1.2011.*********.4.1.5
        hwDisposeEntPhysicalClass OBJECT-TYPE
           SYNTAX      PhysicalClass
           MAX-ACCESS  read-only
           STATUS      current
           DESCRIPTION
               "An indication of the general hardware type of the predisposed physical entity."
           ::= { hwPreDisposeEntInfoEntry 5 }
        
        -- *******.4.1.2011.*********.4.1.6
        hwDisposeEntPhysicalParentRelPos OBJECT-TYPE
           SYNTAX      Integer32 (-1..2147483647)
           MAX-ACCESS  read-only
           STATUS      current
           DESCRIPTION
               "An indication of the relative position of this 'child' component among all its 'sibling' components."
           ::= { hwPreDisposeEntInfoEntry 6 }
        
        -- *******.4.1.2011.*********.4.1.7
        hwDisposeEntPhysicalName OBJECT-TYPE
           SYNTAX      SnmpAdminString
           MAX-ACCESS  read-only
           STATUS      current
           DESCRIPTION
               "The textual name of the predisposed physical entity."
           ::= { hwPreDisposeEntInfoEntry 7 }

        -- *******.4.1.2011.*********
        hwOSPUnifyManageObjects OBJECT IDENTIFIER ::= { hwEntityExtentMIB 9 }

        -- *******.4.1.2011.*********.1
        hwEntityExtOSPTrapsPrefix OBJECT IDENTIFIER ::= { hwOSPUnifyManageObjects 1 }

        -- *******.4.1.2011.*********.1.1
        hwEntityExtUnconnected NOTIFICATION-TYPE
        	OBJECTS { entPhysicalIndex, entPhysicalName }
            	STATUS current
            	DESCRIPTION
                "Board become unconnected for some reason."
            ::= { hwEntityExtOSPTrapsPrefix 1 }
            
        -- *******.4.1.2011.*********.1.2
        hwEntityExtUnconnectedResume NOTIFICATION-TYPE
        	OBJECTS { entPhysicalIndex, entPhysicalName }
            	STATUS current
            	DESCRIPTION
                "Board resume from unconnected"
            ::= { hwEntityExtOSPTrapsPrefix 2 }

     END

--
-- HUAWEI-ENTITY-EXTENT-MIB.mib
--
