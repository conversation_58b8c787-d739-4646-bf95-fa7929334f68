LLDP-EXT-DOT1-MIB DEFINITIONS ::= BEGIN
IMPORTS
MODULE-IDENTITY, OBJECT-TYPE, Integer32
FROM SNMPv2-SMI
TruthValue
FROM SNMPv2-TC
SnmpAdminString
FROM SNMP-FRAMEWORK-MIB
MODULE-COMPLIANCE, OBJECT-GROUP
FROM SNMPv2-CONF
lldpExtensions, lldpLocPortNum,
lldpRemTimeMark, lldpRemLocalPortNum, lldpRemIndex,
lldpPortConfigEntry
FROM LLDP-MIB;

VlanId ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "d"
    STATUS      current
    DESCRIPTION
        "The VLAN-ID that uniquely identifies a VLAN.  This
        is the 12-bit VLAN-ID used in the VLAN Tag header.
        The range is defined by the REFERENCEd specification."
    REFERENCE
        "IEEE Std 802.1Q 2003 Edition, Virtual Bridged
        Local Area Networks."
    SYNTAX      Integer32 (1..4094)

lldpXdot1MIB MODULE-IDENTITY
LAST-UPDATED "200505060000Z" -- May 06, 2005
ORGANIZATION "IEEE 802.1 Working Group"
CONTACT-INFO
" WG-URL: http://grouper.ieee.org/groups/802/1/index.html
WG-EMail: <EMAIL>
Contact: Paul Congdon
Postal: Hewlett-Packard Company
8000 Foothills Blvd.
Roseville, CA 95747
USA
Tel: ******-785-5753
E-mail: <EMAIL>"
DESCRIPTION
"The LLDP Management Information Base extension module for
IEEE 802.1 organizationally defined discovery information.
In order to assure the uniqueness of the LLDP-MIB,
lldpXdot1MIB is branched from lldpExtensions using OUI value
as the node. An OUI/'company_id' is a 24 bit globally unique
assigned number referenced by various standards.
Copyright (C) IEEE (2005). This version of this MIB module
is published as Annex F.7.1 of IEEE Std 802.1AB-2005;
see the standard itself for full legal notices."
REVISION "200505060000Z" -- May 06, 2005
DESCRIPTION
"Published as part of IEEE Std 802.1AB-2005 initial version."
-- OUI for IEEE 802.1 is 32962 (00-80-C2)
::= { lldpExtensions 32962 }
------------------------------------------------------------------------------
------------------------------------------------------------------------------
--
-- Organizationally Defined Information Extension - IEEE 802.1
--
------------------------------------------------------------------------------
------------------------------------------------------------------------------
lldpXdot1Objects OBJECT IDENTIFIER ::= { lldpXdot1MIB 1 }
-- LLDP IEEE 802.1 extension MIB groups
lldpXdot1Config OBJECT IDENTIFIER ::= { lldpXdot1Objects 1 }
lldpXdot1LocalData OBJECT IDENTIFIER ::= { lldpXdot1Objects 2 }
lldpXdot1RemoteData OBJECT IDENTIFIER ::= { lldpXdot1Objects 3 }
------------------------------------------------------------------------------
-- IEEE 802.1 - Configuration
------------------------------------------------------------------------------
--
-- lldpXdot1ConfigPortVlanTable : configure the transmission of the
-- Port VLAN-ID TLVs on set of ports.
--
lldpXdot1ConfigPortVlanTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot1ConfigPortVlanEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"A table that controls selection of LLDP Port VLAN-ID TLVs
to be transmitted on individual ports."
::= { lldpXdot1Config 1 }
lldpXdot1ConfigPortVlanEntry OBJECT-TYPE
SYNTAX LldpXdot1ConfigPortVlanEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"LLDP configuration information that controls the
transmission of IEEE 802.1 organizationally defined Port
VLAN-ID TLV on LLDP transmission capable ports.
This configuration object augments the lldpPortConfigEntry of
the LLDP-MIB, therefore it is only present along with the port
configuration defined by the associated lldpPortConfigEntry
entry.
Each active lldpConfigEntry must be restored from non-volatile
storage (along with the corresponding lldpPortConfigEntry)
after a re-initialization of the management system."
AUGMENTS { lldpPortConfigEntry }
::= { lldpXdot1ConfigPortVlanTable 1 }
LldpXdot1ConfigPortVlanEntry ::= SEQUENCE {
lldpXdot1ConfigPortVlanTxEnable TruthValue
}
lldpXdot1ConfigPortVlanTxEnable OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-write
STATUS current
DESCRIPTION
"The lldpXdot1ConfigPortVlanTxEnable, which is defined as
a truth value and configured by the network management,
determines whether the IEEE 802.1 organizationally defined
port VLAN TLV transmission is allowed on a given LLDP
transmission capable port.
The value of this object must be restored from non-volatile
storage after a re-initialization of the management system."
REFERENCE
"IEEE Std 802.1AB-2005 ********"
DEFVAL { false }
::= { lldpXdot1ConfigPortVlanEntry 1 }
--
-- lldpXdot1ConfigVlanNameTable : configure the transmission of the
-- VLAN name instances on set of ports.
--
lldpXdot1ConfigVlanNameTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot1ConfigVlanNameEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"The table that controls selection of LLDP VLAN name TLV
instances to be transmitted on individual ports."
::= { lldpXdot1Config 2 }
lldpXdot1ConfigVlanNameEntry OBJECT-TYPE
SYNTAX LldpXdot1ConfigVlanNameEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"LLDP configuration information that specifies the set of
ports (represented as a PortList) on which the Local System
VLAN name instance will be transmitted.
This configuration object augments the lldpLocVlanEntry,
therefore it is only present along with the VLAN Name instance
contained in the associated lldpLocVlanNameEntry entry.
Each active lldpXdot1ConfigVlanNameEntry must be restored
from non-volatile storage (along with the corresponding
lldpXdot1LocVlanNameEntry) after a re-initialization of the
management system."
AUGMENTS { lldpXdot1LocVlanNameEntry }
::= { lldpXdot1ConfigVlanNameTable 1 }
LldpXdot1ConfigVlanNameEntry ::= SEQUENCE {
lldpXdot1ConfigVlanNameTxEnable TruthValue
}
lldpXdot1ConfigVlanNameTxEnable OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-write
STATUS current
DESCRIPTION
"The boolean value that indicates whether the corresponding
Local System VLAN name instance will be transmitted on the
port defined by the given lldpXdot1LocVlanNameEntry.
The value of this object must be restored from non-volatile
storage after a re-initialization of the management system."
REFERENCE
"IEEE Std 802.1AB-2005 ********"
DEFVAL { false }
::= { lldpXdot1ConfigVlanNameEntry 1 }
--
-- lldpXdot1ConfigProtoVlanTable : configure the transmission of the
-- protocol VLAN instances on set
-- of ports.
--
lldpXdot1ConfigProtoVlanTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot1ConfigProtoVlanEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"The table that controls selection of LLDP Port and Protocol
VLAN ID TLV instances to be transmitted on individual ports."
::= { lldpXdot1Config 3 }
lldpXdot1ConfigProtoVlanEntry OBJECT-TYPE
SYNTAX LldpXdot1ConfigProtoVlanEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"LLDP configuration information that specifies the set of
ports (represented as a PortList) on which the Local System
Protocol VLAN instance will be transmitted.
This configuration object augments the lldpXdot1LocVlanEntry,
therefore it is only present along with the Port and
Protocol VLAN ID instance contained in the associated
lldpXdot1LocVlanEntry entry.
Each active lldpXdot1ConfigProtoVlanEntry must be restored
from non-volatile storage (along with the corresponding
lldpXdot1LocProtoVlanEntry) after a re-initialization of
the management system."
AUGMENTS { lldpXdot1LocProtoVlanEntry }
::= { lldpXdot1ConfigProtoVlanTable 1 }
LldpXdot1ConfigProtoVlanEntry ::= SEQUENCE {
lldpXdot1ConfigProtoVlanTxEnable TruthValue
}
lldpXdot1ConfigProtoVlanTxEnable OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-write
STATUS current
DESCRIPTION
"The boolean value that indicates whether the corresponding
IEEE
Std 802.1AB-2005 LOCAL AND METROPOLITAN AREA NETWORKS
118 Copyright . 2005 IEEE. All rights reserved.
Local System Port and Protocol VLAN instance will
be transmitted on the port defined by the given
lldpXdot1LocProtoVlanEntry.
The value of this object must be restored from non-volatile
storage after a re-initialization of the management system."
REFERENCE
"IEEE Std 802.1AB-2005 ********"
DEFVAL { false }
::= { lldpXdot1ConfigProtoVlanEntry 1 }
--
-- lldpXdot1ConfigProtocolTable : configure the transmission of the
-- protocol instances on set
-- of ports.
--
lldpXdot1ConfigProtocolTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot1ConfigProtocolEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"The table that controls selection of LLDP Protocol
TLV instances to be transmitted on individual ports."
::= { lldpXdot1Config 4 }
lldpXdot1ConfigProtocolEntry OBJECT-TYPE
SYNTAX LldpXdot1ConfigProtocolEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"LLDP configuration information that specifies the set of
ports (represented as a PortList) on which the Local System
Protocol instance will be transmitted.
This configuration object augments the lldpXdot1LocProtoEntry,
therefore it is only present along with the Protocol instance
contained in the associated lldpXdot1LocProtoEntry entry.
Each active lldpXdot1ConfigProtocolEntry must be restored
from non-volatile storage (along with the corresponding
lldpXdot1LocProtocolEntry) after a re-initialization of the
management system."
AUGMENTS { lldpXdot1LocProtocolEntry }
::= { lldpXdot1ConfigProtocolTable 1 }
LldpXdot1ConfigProtocolEntry ::= SEQUENCE {
lldpXdot1ConfigProtocolTxEnable TruthValue
}
lldpXdot1ConfigProtocolTxEnable OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-write
STATUS current
DESCRIPTION
"The boolean value that indicates whether the corresponding
Local System Protocol Identity instance will be transmitted
on the port defined by the given lldpXdot1LocProtocolEntry.
IEEE
STATION AND MEDIA ACCESS CONTROL CONNECTIVITY DISCOVERY Std 802.1AB-2005
Copyright . 2005 IEEE. All rights reserved. 119
The value of this object must be restored from non-volatile
storage after a re-initialization of the management system."
REFERENCE
"IEEE Std 802.1AB-2005 ********"
DEFVAL { false }
::= { lldpXdot1ConfigProtocolEntry 1 }
------------------------------------------------------------------------------
-- IEEE 802.1 - Local System Information
------------------------------------------------------------------------------
lldpXdot1LocTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot1LocEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This table contains one row per port for IEEE 802.1
organizationally defined LLDP extension on the local system
known to this agent."
::= { lldpXdot1LocalData 1 }
lldpXdot1LocEntry OBJECT-TYPE
SYNTAX LldpXdot1LocEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"Information about IEEE 802.1 organizationally defined
LLDP extension."
INDEX { lldpLocPortNum }
::= { lldpXdot1LocTable 1 }
LldpXdot1LocEntry ::= SEQUENCE {
lldpXdot1LocPortVlanId Integer32
}
lldpXdot1LocPortVlanId OBJECT-TYPE
SYNTAX Integer32(0|1..4094)
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The integer value used to identify the port's VLAN identifier
associated with the local system. A value of zero shall
be used if the system either does not know the PVID or does
not support port-based VLAN operation."
REFERENCE
"IEEE Std 802.1AB-2005 F.2.1"
::= { lldpXdot1LocEntry 1 }
--
-- lldpXdot1LocProtoVlanTable: Port and Protocol VLAN information
--
lldpXdot1LocProtoVlanTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot1LocProtoVlanEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This table contains one or more rows per Port and Protocol
VLAN information about the local system."
::= { lldpXdot1LocalData 2 }

lldpXdot1LocProtoVlanEntry OBJECT-TYPE
SYNTAX LldpXdot1LocProtoVlanEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"Port and protocol VLAN ID Information about a particular
port component. There may be multiple port and protocol VLANs,
identified by a particular lldpXdot1LocProtoVlanId, configured
on the given port."
INDEX { lldpLocPortNum,
lldpXdot1LocProtoVlanId }
::= { lldpXdot1LocProtoVlanTable 1 }
LldpXdot1LocProtoVlanEntry ::= SEQUENCE {
lldpXdot1LocProtoVlanId Integer32,
lldpXdot1LocProtoVlanSupported TruthValue,
lldpXdot1LocProtoVlanEnabled TruthValue
}
lldpXdot1LocProtoVlanId OBJECT-TYPE
SYNTAX Integer32(0|1..4094)
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"The integer value used to identify the port and protocol
VLANs associated with the given port associated with the
local system. A value of zero shall be used if the system
either does not know the protocol VLAN ID (PPVID) or does
not support port and protocol VLAN operation."
REFERENCE
"IEEE Std 802.1AB-2005 F.3.2"
::= { lldpXdot1LocProtoVlanEntry 1 }
lldpXdot1LocProtoVlanSupported OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The truth value used to indicate whether the given port
(associated with the local system) supports port and protocol
VLANs."
REFERENCE
"IEEE Std 802.1AB-2005 F.3.1"
::= { lldpXdot1LocProtoVlanEntry 2 }
lldpXdot1LocProtoVlanEnabled OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The truth value used to indicate whether the port and
protocol VLANs are enabled on the given port associated with
the local system."
REFERENCE
"IEEE Std 802.1AB-2005 F.3.1"
::= { lldpXdot1LocProtoVlanEntry 3 }
--
-- lldpXdot1LocVlanNameTable : VLAN name information about the local system
--
lldpXdot1LocVlanNameTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot1LocVlanNameEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This table contains one or more rows per IEEE 802.1Q VLAN
name information on the local system known to this agent."
::= { lldpXdot1LocalData 3 }
lldpXdot1LocVlanNameEntry OBJECT-TYPE
SYNTAX LldpXdot1LocVlanNameEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"VLAN name Information about a particular port component.
There may be multiple VLANs, identified by a particular
lldpXdot1LocVlanId, configured on the given port."
INDEX { lldpLocPortNum,
lldpXdot1LocVlanId }
::= { lldpXdot1LocVlanNameTable 1 }
LldpXdot1LocVlanNameEntry ::= SEQUENCE {
lldpXdot1LocVlanId VlanId,
lldpXdot1LocVlanName SnmpAdminString
}
lldpXdot1LocVlanId OBJECT-TYPE
SYNTAX VlanId
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"The integer value used to identify the IEEE 802.1Q
VLAN IDs with which the given port is compatible."
REFERENCE
"IEEE Std 802.1AB-2005 F.4.2"
::= { lldpXdot1LocVlanNameEntry 1 }
lldpXdot1LocVlanName OBJECT-TYPE
SYNTAX SnmpAdminString (SIZE(1..32))
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The string value used to identify VLAN name identified by the
Vlan Id associated with the given port on the local system.
This object should contain the value of the dot1QVLANStaticName
object (defined in IETF RFC 2674) identified with the given
lldpXdot1LocVlanId."
REFERENCE
"IEEE Std 802.1AB-2005 F.4.4"
::= { lldpXdot1LocVlanNameEntry 2 }
--
-- lldpXdot1LocProtocolTable : Protocol Identity information
--
lldpXdot1LocProtocolTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot1LocProtocolEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This table contains one or more rows per protocol identity
information on the local system known to this agent."
REFERENCE
"IEEE Std 802.1AB-2005 F.5"
::= { lldpXdot1LocalData 4 }
lldpXdot1LocProtocolEntry OBJECT-TYPE
SYNTAX LldpXdot1LocProtocolEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"Information about particular protocols that are accessible
through the given port component.
There may be multiple protocols, identified by particular
lldpXdot1ProtocolIndex, and lldpLocPortNum."
REFERENCE
"IEEE Std 802.1AB-2005 F.5"
INDEX { lldpLocPortNum,
lldpXdot1LocProtocolIndex }
::= { lldpXdot1LocProtocolTable 1 }
LldpXdot1LocProtocolEntry ::= SEQUENCE {
lldpXdot1LocProtocolIndex Integer32,
lldpXdot1LocProtocolId OCTET STRING
}
lldpXdot1LocProtocolIndex OBJECT-TYPE
SYNTAX Integer32(1..2147483647)
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This object represents an arbitrary local integer value used
by this agent to identify a particular protocol identity."
::= { lldpXdot1LocProtocolEntry 1 }
lldpXdot1LocProtocolId OBJECT-TYPE
SYNTAX OCTET STRING (SIZE (1..255))
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The octet string value used to identify the protocols
associated with the given port of the local system."
REFERENCE
"IEEE Std 802.1AB-2005 F.5.3"
::= { lldpXdot1LocProtocolEntry 2 }
------------------------------------------------------------------------------
-- IEEE 802.1 - Remote System Information
------------------------------------------------------------------------------
lldpXdot1RemTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot1RemEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This table contains one or more rows per physical network
connection known to this agent. The agent may wish to
ensure that only one lldpXdot1RemEntry is present for
each local port, or it may choose to maintain multiple
lldpXdot1RemEntries for the same local port."
::= { lldpXdot1RemoteData 1 }
lldpXdot1RemEntry OBJECT-TYPE
SYNTAX LldpXdot1RemEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"Information about a particular port component."
INDEX { lldpRemTimeMark,
lldpRemLocalPortNum,
lldpRemIndex }
::= { lldpXdot1RemTable 1 }
LldpXdot1RemEntry ::= SEQUENCE {
lldpXdot1RemPortVlanId Integer32
}
lldpXdot1RemPortVlanId OBJECT-TYPE
SYNTAX Integer32(0|1..4094)
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The integer value used to identify the port's VLAN identifier
associated with the remote system. if the remote system
either does not know the PVID or does not support port-based
VLAN operation, the value of lldpXdot1RemPortVlanId should
be zero."
REFERENCE
"IEEE Std 802.1AB-2005 F.2.1"
::= { lldpXdot1RemEntry 1 }
lldpXdot1RemProtoVlanTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot1RemProtoVlanEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This table contains one or more rows per Port and Protocol
VLAN information about the remote system, received on the
given port."
::= { lldpXdot1RemoteData 2 }
lldpXdot1RemProtoVlanEntry OBJECT-TYPE
SYNTAX LldpXdot1RemProtoVlanEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"Port and protocol VLAN name Information about a particular
port component. There may be multiple protocol VLANs,
identified by a particular lldpXdot1RemProtoVlanId, configured
on the remote system."
INDEX { lldpRemTimeMark,
lldpRemLocalPortNum,
lldpRemIndex,
lldpXdot1RemProtoVlanId }
::= { lldpXdot1RemProtoVlanTable 1 }
LldpXdot1RemProtoVlanEntry ::= SEQUENCE {
lldpXdot1RemProtoVlanId Integer32,
lldpXdot1RemProtoVlanSupported TruthValue,
lldpXdot1RemProtoVlanEnabled TruthValue
}
lldpXdot1RemProtoVlanId OBJECT-TYPE
SYNTAX Integer32(0|1..4094)
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"The integer value used to identify the port and protocol
VLANs associated with the given port associated with the
remote system.
If port and protocol VLANs are not supported on the given
port associated with the remote system, or if the port is
not enabled with any port and protocol VLAN, the value of
lldpXdot1RemProtoVlanId should be zero."
REFERENCE
"IEEE Std 802.1AB-2005 F.3.2"
::= { lldpXdot1RemProtoVlanEntry 1 }
lldpXdot1RemProtoVlanSupported OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The truth value used to indicate whether the given port
(associated with the remote system) is capable of supporting
port and protocol VLANs."
REFERENCE
"IEEE Std 802.1AB-2005 F.3.1"
::= { lldpXdot1RemProtoVlanEntry 2 }
lldpXdot1RemProtoVlanEnabled OBJECT-TYPE
SYNTAX TruthValue
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The truth value used to indicate whether the port and
protocol VLANs are enabled on the given port associated with
the remote system."
REFERENCE
"IEEE Std 802.1AB-2005 F.3.1"
::= { lldpXdot1RemProtoVlanEntry 3 }
--
-- lldpXdot1RemVlanNameTable : VLAN name information of the remote
-- systems
--
lldpXdot1RemVlanNameTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot1RemVlanNameEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This table contains one or more rows per IEEE 802.1Q VLAN
name information about the remote system, received on the
given port."
REFERENCE
"IEEE Std 802.1AB-2005 F.4"
::= { lldpXdot1RemoteData 3 }
lldpXdot1RemVlanNameEntry OBJECT-TYPE
SYNTAX LldpXdot1RemVlanNameEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"VLAN name Information about a particular port component.
There may be multiple VLANs, identified by a particular
lldpXdot1RemVlanId, received on the given port."
INDEX { lldpRemTimeMark,
lldpRemLocalPortNum,
lldpRemIndex,
lldpXdot1RemVlanId }
::= { lldpXdot1RemVlanNameTable 1 }
LldpXdot1RemVlanNameEntry ::= SEQUENCE {
lldpXdot1RemVlanId VlanId,
lldpXdot1RemVlanName SnmpAdminString
}
lldpXdot1RemVlanId OBJECT-TYPE
SYNTAX VlanId
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"The integer value used to identify the IEEE 802.1Q
VLAN IDs with which the given port of the remote system
is compatible."
REFERENCE
"IEEE Std 802.1AB-2005 F.4.2"
::= { lldpXdot1RemVlanNameEntry 1 }
lldpXdot1RemVlanName OBJECT-TYPE
SYNTAX SnmpAdminString (SIZE(1..32))
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The string value used to identify VLAN name identified by the
VLAN Id associated with the remote system."
REFERENCE
"IEEE Std 802.1AB-2005 F.4.4"
::= { lldpXdot1RemVlanNameEntry 2 }
--
-- lldpXdot1RemProtocolTable : Protocol information of the remote systems
--
lldpXdot1RemProtocolTable OBJECT-TYPE
SYNTAX SEQUENCE OF LldpXdot1RemProtocolEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This table contains one or more rows per protocol information
about the remote system, received on the given port."
::= { lldpXdot1RemoteData 4 }
lldpXdot1RemProtocolEntry OBJECT-TYPE
SYNTAX LldpXdot1RemProtocolEntry
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"Protocol information about a particular port component.
There may be multiple protocols, identified by a particular
lldpXdot1ProtocolIndex, received on the given port."
INDEX { lldpRemTimeMark,
lldpRemLocalPortNum,
lldpRemIndex,
lldpXdot1RemProtocolIndex }
::= { lldpXdot1RemProtocolTable 1 }
LldpXdot1RemProtocolEntry ::= SEQUENCE {
lldpXdot1RemProtocolIndex Integer32,
lldpXdot1RemProtocolId OCTET STRING
}
lldpXdot1RemProtocolIndex OBJECT-TYPE
SYNTAX Integer32(1..2147483647)
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"This object represents an arbitrary local integer value used
by this agent to identify a particular protocol identity."
::= { lldpXdot1RemProtocolEntry 1 }
lldpXdot1RemProtocolId OBJECT-TYPE
SYNTAX OCTET STRING (SIZE (1..255))
MAX-ACCESS read-only
STATUS current
DESCRIPTION
"The octet string value used to identify the protocols
associated with the given port of remote system."
REFERENCE
"IEEE Std 802.1AB-2005 F.5.3"
::= { lldpXdot1RemProtocolEntry 2 }
------------------------------------------------------------------------------
-- Conformance Information
------------------------------------------------------------------------------
lldpXdot1Conformance OBJECT IDENTIFIER ::= { lldpXdot1MIB 2 }
lldpXdot1Compliances OBJECT IDENTIFIER ::= { lldpXdot1Conformance 1 }
lldpXdot1Groups OBJECT IDENTIFIER ::= { lldpXdot1Conformance 2 }
-- compliance statements
lldpXdot1Compliance MODULE-COMPLIANCE
STATUS current
DESCRIPTION
"The compliance statement for SNMP entities which implement
the IEEE 802.1 organizationally defined LLDP extension MIB."
MODULE -- this module
MANDATORY-GROUPS { lldpXdot1ConfigGroup,
lldpXdot1LocSysGroup,
lldpXdot1RemSysGroup
}
::= { lldpXdot1Compliances 1 }
-- MIB groupings
lldpXdot1ConfigGroup OBJECT-GROUP
OBJECTS {
lldpXdot1ConfigPortVlanTxEnable,
lldpXdot1ConfigVlanNameTxEnable,
lldpXdot1ConfigProtoVlanTxEnable,
lldpXdot1ConfigProtocolTxEnable
}
STATUS current
DESCRIPTION
"The collection of objects which are used to configure the
IEEE 802.1 organizationally defined LLDP extension
implementation behavior.
This group is mandatory for agents which implement the
IEEE 802.1 organizationally defined LLDP extension."
::= { lldpXdot1Groups 1 }
lldpXdot1LocSysGroup OBJECT-GROUP
OBJECTS {
lldpXdot1LocPortVlanId,
lldpXdot1LocProtoVlanSupported,
lldpXdot1LocProtoVlanEnabled,
lldpXdot1LocVlanName,
lldpXdot1LocProtocolId
}
STATUS current
DESCRIPTION
"The collection of objects which are used to represent
IEEE 802.1 organizationally defined LLDP extension associated
with the Local Device Information.
This group is mandatory for agents which implement the
IEEE 802.1 organizationally defined LLDP extension in the
TX mode."
::= { lldpXdot1Groups 2 }
lldpXdot1RemSysGroup OBJECT-GROUP
OBJECTS {
lldpXdot1RemPortVlanId,
lldpXdot1RemProtoVlanSupported,
lldpXdot1RemProtoVlanEnabled,
lldpXdot1RemVlanName,
lldpXdot1RemProtocolId
}
STATUS current
DESCRIPTION
"The collection of objects which are used to represent LLDP
802.1 organizational extension Local Device Information.
IEEE
Std 802.1AB-2005 LOCAL AND METROPOLITAN AREA NETWORKS
128 Copyright . 2005 IEEE. All rights reserved.
This group is mandatory for agents which implement the
LLDP 802.1 organizational extension in the RX mode."
::= { lldpXdot1Groups 3 }
END
