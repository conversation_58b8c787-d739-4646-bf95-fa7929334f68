-- ===========================================================================
-- Description: Internet Network Address Conventions
-- Reference:   Module(INET-ADDRESS-MIB) Extracted from RFC4001.TXT,from 9920 to 27921.Obsoletes: 3291
-- ===========================================================================

INET-ADDRESS-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, mib-2, Unsigned32 FROM SNMPv2-SMI
    TEXTUAL-CONVENTION                 FROM SNMPv2-TC;

inetAddressMIB MODULE-IDENTITY
    LAST-UPDATED "200502040000Z"
    ORGANIZATION
        "IETF Operations and Management Area"
    CONTACT-INFO
        "<PERSON><PERSON><PERSON> (Editor)
         International University Bremen
         P.O. Box 750 561
         28725 Bremen, Germany

         Phone: +49 ************
         EMail: <EMAIL>

         Send comments to <<EMAIL>>."
    DESCRIPTION
        "This MIB module defines textual conventions for
         representing Internet addresses.  An Internet
         address can be an IPv4 address, an IPv6 address,
         or a DNS domain name.  This module also defines
         textual conventions for Internet port numbers,
         autonomous system numbers, and the length of an
         Internet address prefix.

         Copyright (C) The Internet Society (2005).  This version
         of this MIB module is part of RFC 4001, see the RFC
         itself for full legal notices."
    REVISION     "200502040000Z"
    DESCRIPTION
        "Third version, published as RFC 4001.  This revision
         introduces the InetZoneIndex, InetScopeType, and
         InetVersion textual conventions."
    REVISION     "200205090000Z"
    DESCRIPTION
        "Second version, published as RFC 3291.  This
         revision contains several clarifications and
         introduces several new textual conventions:
         InetAddressPrefixLength, InetPortNumber,
         InetAutonomousSystemNumber, InetAddressIPv4z,
         and InetAddressIPv6z."
    REVISION     "200006080000Z"
    DESCRIPTION
        "Initial version, published as RFC 2851."
    ::= { mib-2 76 }

InetAddressType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "A value that represents a type of Internet address.

         unknown(0)  An unknown address type.  This value MUST
                     be used if the value of the corresponding
                     InetAddress object is a zero-length string.
                     It may also be used to indicate an IP address
                     that is not in one of the formats defined
                     below.

         ipv4(1)     An IPv4 address as defined by the
                     InetAddressIPv4 textual convention.

         ipv6(2)     An IPv6 address as defined by the
                     InetAddressIPv6 textual convention.

         ipv4z(3)    A non-global IPv4 address including a zone
                     index as defined by the InetAddressIPv4z
                     textual convention.

         ipv6z(4)    A non-global IPv6 address including a zone
                     index as defined by the InetAddressIPv6z
                     textual convention.

         dns(16)     A DNS domain name as defined by the
                     InetAddressDNS textual convention.

         Each definition of a concrete InetAddressType value must be
         accompanied by a definition of a textual convention for use
         with that InetAddressType.

         To support future extensions, the InetAddressType textual
         convention SHOULD NOT be sub-typed in object type definitions.
         It MAY be sub-typed in compliance statements in order to
         require only a subset of these address types for a compliant
         implementation.

         Implementations must ensure that InetAddressType objects
         and any dependent objects (e.g., InetAddress objects) are
         consistent.  An inconsistentValue error must be generated
         if an attempt to change an InetAddressType object would,
         for example, lead to an undefined InetAddress value.  In
         particular, InetAddressType/InetAddress pairs must be
         changed together if the address type changes (e.g., from
         ipv6(2) to ipv4(1))."
    SYNTAX       INTEGER {
                     unknown(0),
                     ipv4(1),
                     ipv6(2),
                     ipv4z(3),
                     ipv6z(4),
                     dns(16)
                 }

InetAddress ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Denotes a generic Internet address.

         An InetAddress value is always interpreted within the context
         of an InetAddressType value.  Every usage of the InetAddress
         textual convention is required to specify the InetAddressType
         object that provides the context.  It is suggested that the
         InetAddressType object be logically registered before the
         object(s) that use the InetAddress textual convention, if
         they appear in the same logical row.

         The value of an InetAddress object must always be
         consistent with the value of the associated InetAddressType
         object.  Attempts to set an InetAddress object to a value
         inconsistent with the associated InetAddressType
         must fail with an inconsistentValue error.

         When this textual convention is used as the syntax of an
         index object, there may be issues with the limit of 128
         sub-identifiers specified in SMIv2, STD 58.  In this case,
         the object definition MUST include a 'SIZE' clause to
         limit the number of potential instance sub-identifiers;
         otherwise the applicable constraints MUST be stated in
         the appropriate conceptual row DESCRIPTION clauses, or
         in the surrounding documentation if there is no single
         DESCRIPTION clause that is appropriate."
    SYNTAX       OCTET STRING (SIZE (0..255))

InetAddressIPv4 ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "1d.1d.1d.1d"
    STATUS       current
    DESCRIPTION
        "Represents an IPv4 network address:
           Octets   Contents         Encoding
            1-4     IPv4 address     network-byte order

         The corresponding InetAddressType value is ipv4(1).

         This textual convention SHOULD NOT be used directly in object
         definitions, as it restricts addresses to a specific format.
         However, if it is used, it MAY be used either on its own or in
         conjunction with InetAddressType, as a pair."
    SYNTAX       OCTET STRING (SIZE (4))

InetAddressIPv6 ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "2x:2x:2x:2x:2x:2x:2x:2x"
    STATUS       current
    DESCRIPTION
        "Represents an IPv6 network address:

           Octets   Contents         Encoding
            1-16    IPv6 address     network-byte order

         The corresponding InetAddressType value is ipv6(2).

         This textual convention SHOULD NOT be used directly in object
         definitions, as it restricts addresses to a specific format.
         However, if it is used, it MAY be used either on its own or in
         conjunction with InetAddressType, as a pair."
    SYNTAX       OCTET STRING (SIZE (16))

InetAddressIPv4z ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "1d.1d.1d.1d%4d"
    STATUS       current
    DESCRIPTION
        "Represents a non-global IPv4 network address, together
         with its zone index:

           Octets   Contents         Encoding
            1-4     IPv4 address     network-byte order
            5-8     zone index       network-byte order

         The corresponding InetAddressType value is ipv4z(3).

         The zone index (bytes 5-8) is used to disambiguate identical
         address values on nodes that have interfaces attached to
         different zones of the same scope.  The zone index may contain
         the special value 0, which refers to the default zone for each
         scope.

         This textual convention SHOULD NOT be used directly in object
         definitions, as it restricts addresses to a specific format.
         However, if it is used, it MAY be used either on its own or in
         conjunction with InetAddressType, as a pair."
    SYNTAX       OCTET STRING (SIZE (8))

InetAddressIPv6z ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "2x:2x:2x:2x:2x:2x:2x:2x%4d"
    STATUS       current
    DESCRIPTION
        "Represents a non-global IPv6 network address, together
         with its zone index:

           Octets   Contents         Encoding
            1-16    IPv6 address     network-byte order
           17-20    zone index       network-byte order

         The corresponding InetAddressType value is ipv6z(4).

         The zone index (bytes 17-20) is used to disambiguate
         identical address values on nodes that have interfaces
         attached to different zones of the same scope.  The zone index
         may contain the special value 0, which refers to the default
         zone for each scope.

         This textual convention SHOULD NOT be used directly in object
         definitions, as it restricts addresses to a specific format.
         However, if it is used, it MAY be used either on its own or in
         conjunction with InetAddressType, as a pair."
    SYNTAX       OCTET STRING (SIZE (20))

InetAddressDNS ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS       current
    DESCRIPTION
        "Represents a DNS domain name.  The name SHOULD be fully
         qualified whenever possible.

         The corresponding InetAddressType is dns(16).

         The DESCRIPTION clause of InetAddress objects that may have
         InetAddressDNS values MUST fully describe how (and when)
         these names are to be resolved to IP addresses.

         The resolution of an InetAddressDNS value may require to
         query multiple DNS records (e.g., A for IPv4 and AAAA for
         IPv6).  The order of the resolution process and which DNS
         record takes precedence depends on the configuration of the
         resolver.
         This textual convention SHOULD NOT be used directly in object
         definitions, as it restricts addresses to a specific format.
         However, if it is used, it MAY be used either on its own or in
         conjunction with InetAddressType, as a pair."
    SYNTAX       OCTET STRING (SIZE (1..255))

InetAddressPrefixLength ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "d"
    STATUS       current
    DESCRIPTION
        "Denotes the length of a generic Internet network address
         prefix.  A value of n corresponds to an IP address mask
         that has n contiguous 1-bits from the most significant
         bit (MSB), with all other bits set to 0.

         An InetAddressPrefixLength value is always interpreted within
         the context of an InetAddressType value.  Every usage of the
         InetAddressPrefixLength textual convention is required to
         specify the InetAddressType object that provides the
         context.  It is suggested that the InetAddressType object be
         logically registered before the object(s) that use the
         InetAddressPrefixLength textual convention, if they appear
         in the same logical row.

         InetAddressPrefixLength values larger than
         the maximum length of an IP address for a specific
         InetAddressType are treated as the maximum significant
         value applicable for the InetAddressType.  The maximum
         significant value is 32 for the InetAddressType
         'ipv4(1)' and 'ipv4z(3)' and 128 for the InetAddressType
         'ipv6(2)' and 'ipv6z(4)'.  The maximum significant value
         for the InetAddressType 'dns(16)' is 0.

         The value zero is object-specific and must be defined as
         part of the description of any object that uses this
         syntax.  Examples of the usage of zero might include
         situations where the Internet network address prefix
         is unknown or does not apply.

         The upper bound of the prefix length has been chosen to
         be consistent with the maximum size of an InetAddress."
    SYNTAX       Unsigned32 (0..2040)

InetPortNumber ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "d"
    STATUS       current
    DESCRIPTION
        "Represents a 16 bit port number of an Internet transport
         layer protocol.  Port numbers are assigned by IANA.  A
         current list of all assignments is available from
         <http://www.iana.org/>.

         The value zero is object-specific and must be defined as
         part of the description of any object that uses this
         syntax.  Examples of the usage of zero might include
         situations where a port number is unknown, or when the
         value zero is used as a wildcard in a filter."
    REFERENCE   "STD 6 (RFC 768), STD 7 (RFC 793) and RFC 2960"
    SYNTAX       Unsigned32 (0..65535)

InetAutonomousSystemNumber ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "d"
    STATUS       current
    DESCRIPTION
        "Represents an autonomous system number that identifies an
         Autonomous System (AS).  An AS is a set of routers under a
         single technical administration, using an interior gateway
         protocol and common metrics to route packets within the AS,
         and using an exterior gateway protocol to route packets to
         other ASes'.  IANA maintains the AS number space and has
         delegated large parts to the regional registries.

         Autonomous system numbers are currently limited to 16 bits
         (0..65535).  There is, however, work in progress to enlarge the
         autonomous system number space to 32 bits.  Therefore, this
         textual convention uses an Unsigned32 value without a
         range restriction in order to support a larger autonomous
         system number space."
    REFERENCE   "RFC 1771, RFC 1930"
    SYNTAX       Unsigned32

InetScopeType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "Represents a scope type.  This textual convention can be used
         in cases where a MIB has to represent different scope types
         and there is no context information, such as an InetAddress
         object, that implicitly defines the scope type.

         Note that not all possible values have been assigned yet, but
         they may be assigned in future revisions of this specification.
         Applications should therefore be able to deal with values
         not yet assigned."
    REFERENCE   "RFC 3513"
    SYNTAX       INTEGER {
                     -- reserved(0),
                     interfaceLocal(1),
                     linkLocal(2),
                     subnetLocal(3),
                     adminLocal(4),
                     siteLocal(5), -- site-local unicast addresses
                                   -- have been deprecated by RFC 3879
                     -- unassigned(6),
                     -- unassigned(7),
                     organizationLocal(8),
                     -- unassigned(9),
                     -- unassigned(10),
                     -- unassigned(11),
                     -- unassigned(12),
                     -- unassigned(13),
                     global(14)
                     -- reserved(15)
                 }

InetZoneIndex ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "d"
    STATUS       current
    DESCRIPTION
        "A zone index identifies an instance of a zone of a
         specific scope.

         The zone index MUST disambiguate identical address
         values.  For link-local addresses, the zone index will
         typically be the interface index (ifIndex as defined in the
         IF-MIB) of the interface on which the address is configured.

         The zone index may contain the special value 0, which refers
         to the default zone.  The default zone may be used in cases
         where the valid zone index is not known (e.g., when a
         management application has to write a link-local IPv6
         address without knowing the interface index value).  The
         default zone SHOULD NOT be used as an easy way out in
         cases where the zone index for a non-global IPv6 address
         is known."
    REFERENCE   "RFC4007"
    SYNTAX       Unsigned32

InetVersion ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "A value representing a version of the IP protocol.

         unknown(0)  An unknown or unspecified version of the IP
                     protocol.
         ipv4(1)     The IPv4 protocol as defined in RFC 791 (STD 5).

         ipv6(2)     The IPv6 protocol as defined in RFC 2460.

         Note that this textual convention SHOULD NOT be used to
         distinguish different address types associated with IP
         protocols.  The InetAddressType has been designed for this
         purpose."
    REFERENCE   "RFC 791, RFC 2460"
    SYNTAX       INTEGER {
                     unknown(0),
                     ipv4(1),
                     ipv6(2)
                 }
END
-- ===========================================================================
-- Full Copyright Statement
-- 
--    Copyright (C) The Internet Society (2004).  This document is subject
--    to the rights, licenses and restrictions contained in BCP 78, and
--    except as set forth therein, the authors retain all their rights.
-- 
--    This document and the information contained herein are provided on an
--    "AS IS" basis and THE CONTRIBUTOR, THE ORGANIZATION HE/SHE REPRESENTS
--    OR IS SPONSORED BY (IF ANY), THE INTERNET SOCIETY AND THE INTERNET
--    ENGINEERING TASK FORCE DISCLAIM ALL WARRANTIES, EXPRESS OR IMPLIED,
--    INCLUDING BUT NOT LIMITED TO ANY WARRANTY THAT THE USE OF THE
--    INFORMATION HEREIN WILL NOT INFRINGE ANY RIGHTS OR ANY IMPLIED
--    WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.
-- 
-- Intellectual Property
-- 
--    The IETF takes no position regarding the validity or scope of any
--    Intellectual Property Rights or other rights that might be claimed to
--    pertain to the implementation or use of the technology described in
--    this document or the extent to which any license under such rights
--    might or might not be available; nor does it represent that it has
--    made any independent effort to identify any such rights.  Information
--    on the procedures with respect to rights in RFC documents can be
--    found in BCP 78 and BCP 79.
-- 
--    Copies of IPR disclosures made to the IETF Secretariat and any
--    assurances of licenses to be made available, or the result of an
--    attempt made to obtain a general license or permission for the use of
--    such proprietary rights by implementers or users of this
--    specification can be obtained from the IETF on-line IPR repository at
--    http://www.ietf.org/ipr.
-- 
--    The IETF invites any interested party to bring to its attention any
--    copyrights, patents or patent applications, or other proprietary
--    rights that may cover technology that may be required to implement
--    this standard.  Please address the information to the IETF at ietf-
--    <EMAIL>.
-- 
-- Acknowledgement
-- 
--    Funding for the RFC Editor function is currently provided by the
--    Internet Society.
-- ===========================================================================
