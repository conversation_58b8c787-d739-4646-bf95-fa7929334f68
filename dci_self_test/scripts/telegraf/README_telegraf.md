# DCI监控系统 - Telegraf测试工具集使用说明

## 概述

Telegraf测试工具集是DCI监控系统自测试框架的一部分，用于验证Telegraf组件的各项功能。这些工具可以帮助用户测试Telegraf的进程控制、配置检查、日志管理等功能，确保Telegraf能够正常工作并与监控系统其他组件正确交互。

本文档提供了Telegraf测试工具集中各个脚本的详细说明和使用方法。

## 目录结构

```
dci_self_test/scripts/telegraf/
├── run_telegraf_tests.sh                    # Telegraf测试套件运行脚本
├── self_test_telegraf_process_control.sh    # Telegraf进程控制测试脚本
├── self_test_telegraf_log_management_tool.sh # Telegraf日志管理工具测试脚本
├── self_test_telegraf_config_check_tool.sh  # Telegraf配置检查工具测试脚本
└── README_telegraf.md                       # 本文档
```

## 脚本说明

### 1. run_telegraf_tests.sh - Telegraf测试套件运行脚本

#### 功能介绍

`run_telegraf_tests.sh`是一个统一的入口点，用于运行所有Telegraf相关的测试。该脚本会按照预定义的顺序执行各个测试脚本，并汇总测试结果。

#### 使用方法

```bash
./run_telegraf_tests.sh [选项]
```

#### 选项

- `-s, --single <test_name>`：仅运行指定的测试
- `-p, --parallel`：并行运行测试（默认为串行）
- `-r, --report`：生成HTML格式的测试报告

#### 示例

```bash
# 运行所有Telegraf测试
./run_telegraf_tests.sh

# 仅运行进程控制测试
./run_telegraf_tests.sh -s process_control

# 并行运行所有测试并生成报告
./run_telegraf_tests.sh -p -r
```

#### 输出

- 测试结果摘要：控制台输出
- 详细日志：`dci_self_test/results/telegraf_tests.log`
- 测试报告（如果启用）：`dci_self_test/results/telegraf_tests_report.html`

### 2. self_test_telegraf_process_control.sh - Telegraf进程控制测试脚本

#### 功能介绍

`self_test_telegraf_process_control.sh`用于测试DCI监控系统对Telegraf进程的控制功能。该脚本会验证系统是否能够正确地启动、停止、重启和查询Telegraf进程状态。测试内容包括：

- Telegraf进程启动
- Telegraf进程停止
- Telegraf进程重启
- 进程状态查询
- 优雅关闭机制
- 异常情况处理

#### 使用方法

```bash
./self_test_telegraf_process_control.sh [选项]
```

#### 选项

- `-t, --timeout <seconds>`：设置操作超时时间（默认30秒）
- `-v, --verbose`：显示详细的执行过程
- `-c, --config <file>`：指定要使用的Telegraf配置文件

#### 示例

```bash
# 默认测试Telegraf进程控制
./self_test_telegraf_process_control.sh

# 设置超时时间为60秒
./self_test_telegraf_process_control.sh -t 60

# 使用自定义配置文件
./self_test_telegraf_process_control.sh -c /path/to/custom/telegraf.conf
```

#### 输出

- 测试报告：`dci_self_test/results/telegraf_process_control_test.md`
- 日志文件：`dci_self_test/results/telegraf_process_control.log`
- 进程状态记录：`dci_self_test/results/telegraf_process_status.json`

### 3. self_test_telegraf_log_management_tool.sh - Telegraf日志管理工具测试脚本

#### 功能介绍

`self_test_telegraf_log_management_tool.sh`用于测试DCI监控系统的Telegraf日志管理功能。该脚本会验证系统是否能够正确地收集、轮转和分析Telegraf日志。测试内容包括：

- 日志文件创建
- 日志级别控制
- 日志轮转机制
- 日志内容验证
- 日志分析功能
- 异常日志处理

#### 使用方法

```bash
./self_test_telegraf_log_management_tool.sh [选项]
```

#### 选项

- `-d, --duration <seconds>`：设置测试持续时间（默认300秒）
- `-l, --level <level>`：设置日志级别（debug, info, warn, error）
- `-s, --size <size>`：设置日志文件大小限制（例如：10MB）
- `-r, --rotate <count>`：设置日志文件轮转数量

#### 示例

```bash
# 默认测试日志管理
./self_test_telegraf_log_management_tool.sh

# 设置测试持续时间为10分钟，日志级别为debug
./self_test_telegraf_log_management_tool.sh -d 600 -l debug

# 设置日志文件大小限制为5MB，轮转数量为3
./self_test_telegraf_log_management_tool.sh -s 5MB -r 3
```

#### 输出

- 测试报告：`dci_self_test/results/telegraf_log_management_test.md`
- 日志文件：`dci_self_test/results/telegraf.log*`
- 日志分析报告：`dci_self_test/results/log_analysis_report.json`

### 4. self_test_telegraf_config_check_tool.sh - Telegraf配置检查工具测试脚本

#### 功能介绍

`self_test_telegraf_config_check_tool.sh`用于测试DCI监控系统的Telegraf配置检查功能。该脚本会验证系统是否能够正确地验证Telegraf配置文件的有效性，并检测潜在的配置问题。测试内容包括：

- 配置文件语法检查
- 插件配置验证
- 配置冲突检测
- 性能影响评估
- 配置修复建议
- 配置历史管理

#### 使用方法

```bash
./self_test_telegraf_config_check_tool.sh [选项]
```

#### 选项

- `-c, --config <directory>`：指定配置目录（默认为`dci_self_test/config/telegraf`）
- `-t, --test-cases <file>`：指定测试用例文件
- `-f, --fix`：尝试自动修复发现的配置问题

#### 示例

```bash
# 默认测试配置检查工具
./self_test_telegraf_config_check_tool.sh

# 检查自定义配置目录
./self_test_telegraf_config_check_tool.sh -c /path/to/custom/config

# 使用特定测试用例并尝试自动修复问题
./self_test_telegraf_config_check_tool.sh -t /path/to/test_cases.json -f
```

#### 输出

- 测试报告：`dci_self_test/results/telegraf_config_check_test.md`
- 配置检查结果：`dci_self_test/results/config_check_results.json`
- 修复日志（如果启用）：`dci_self_test/results/config_fix.log`

## 典型使用场景

### 场景一：全面测试Telegraf功能

当需要全面验证Telegraf的所有功能时，可以使用`run_telegraf_tests.sh`脚本：

```bash
# 运行所有Telegraf测试并生成报告
./run_telegraf_tests.sh -r
```

### 场景二：验证进程控制功能

当需要验证Telegraf进程控制功能时，可以使用`self_test_telegraf_process_control.sh`脚本：

```bash
# 测试Telegraf进程控制
./self_test_telegraf_process_control.sh
```

### 场景三：验证日志管理功能

当需要验证Telegraf日志管理功能时，可以使用`self_test_telegraf_log_management_tool.sh`脚本：

```bash
# 测试Telegraf日志管理，设置debug级别
./self_test_telegraf_log_management_tool.sh -l debug
```

### 场景四：验证配置检查功能

当需要验证Telegraf配置检查功能时，可以使用`self_test_telegraf_config_check_tool.sh`脚本：

```bash
# 测试Telegraf配置检查，并尝试修复问题
./self_test_telegraf_config_check_tool.sh -f
```

## 常见问题排查

### 1. Telegraf进程控制问题

如果Telegraf进程控制测试失败，请检查以下几点：

- Telegraf二进制文件是否存在且可执行
- 系统权限是否足够（可能需要sudo权限）
- 配置文件是否有效
- 系统资源是否足够（内存、CPU等）

### 2. 日志管理问题

如果日志管理测试失败，请检查以下几点：

- 日志目录是否有写入权限
- 磁盘空间是否足够
- 日志轮转配置是否正确
- 系统日志服务是否正常运行

### 3. 配置检查问题

如果配置检查测试失败，请检查以下几点：

- 配置文件语法是否正确
- 引用的插件是否存在
- 配置参数是否在有效范围内
- 配置文件路径是否正确

## 高级配置

### 自定义测试环境

如果需要自定义测试环境，可以编辑`run_telegraf_tests.sh`脚本中的配置部分：

```bash
# 自定义Telegraf二进制路径
TELEGRAF_BIN="/custom/path/to/telegraf"

# 自定义配置目录
CONFIG_DIR="/custom/path/to/config"

# 自定义结果目录
RESULTS_DIR="/custom/path/to/results"
```

### 自定义测试用例

如果需要添加自定义测试用例，可以创建一个JSON格式的测试用例文件：

```json
{
  "test_cases": [
    {
      "name": "无效插件配置测试",
      "config_file": "invalid_plugin.conf",
      "expected_result": "failure",
      "error_message": "plugin not found"
    },
    {
      "name": "有效配置测试",
      "config_file": "valid_config.conf",
      "expected_result": "success"
    }
  ]
}
```

然后在运行配置检查测试时指定此文件：

```bash
./self_test_telegraf_config_check_tool.sh -t /path/to/custom_test_cases.json
```

## 结论

Telegraf测试工具集提供了一套全面的工具，用于测试和验证DCI监控系统的Telegraf组件功能。通过这些工具，用户可以轻松验证Telegraf的各个功能是否正常工作，并快速排查潜在问题。 