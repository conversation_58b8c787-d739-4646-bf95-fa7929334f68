#!/bin/bash
#
# 自测脚本 - Telegraf配置检查工具测试
# 测试ID: telegraf-config-check-01
# 文件名: self_test_telegraf_config_check_tool.sh
#

# 设置颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # 无颜色

# 测试结果统计
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# 测试时使用的临时目录
TEST_HOME="/tmp/dci-test-telegraf"
TEST_CONFIG_DIR="${TEST_HOME}/conf"
TEST_CONFIG="${TEST_CONFIG_DIR}/telegraf.conf"
TEST_CONFIG_D="${TEST_CONFIG_DIR}/telegraf.d"
TEST_LOG_DIR="${TEST_HOME}/logs"
TEST_BIN_DIR="${TEST_HOME}/bin"

# 指定Telegraf二进制文件路径
TELEGRAF_BIN="../bin/telegraf"

# 确保 Telegraf 二进制文件存在
if [ ! -f "$TELEGRAF_BIN" ]; then
    echo -e "${RED}错误: 未找到 Telegraf 二进制文件: $TELEGRAF_BIN${NC}"
    exit 1
fi

# 确保dcicollector命令可用
DCICOLLECTOR_CMD="$(which dciagent 2>/dev/null)"
if [ -z "$DCICOLLECTOR_CMD" ]; then
    echo -e "${YELLOW}警告: 未找到dcicollector命令。将使用模拟命令进行测试。${NC}"
    DCICOLLECTOR_CMD="echo dciagent"
fi

echo "==========================================="
echo "    Telegraf配置检查工具自测脚本"
echo "==========================================="
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo "测试主目录: ${TEST_HOME}"
echo "Telegraf路径: ${TELEGRAF_BIN}"
echo ""

# 辅助函数：运行测试并记录结果
run_test() {
    local test_name="$1"
    local test_cmd="$2"
    local expected_result="$3"
    local test_description="$4"
    
    echo -e "${YELLOW}[测试 $((TESTS_TOTAL+1))]${NC} $test_name"
    echo "描述: $test_description"
    echo "命令: $test_cmd"
    
    TESTS_TOTAL=$((TESTS_TOTAL+1))
    
    # 执行测试命令并捕获输出和退出状态
    output=$(eval "$test_cmd" 2>&1)
    exit_status=$?
    
    # 检查结果
    if [ "$expected_result" = "success" ] && [ $exit_status -eq 0 ]; then
        echo -e "${GREEN}✅ 通过${NC}"
        TESTS_PASSED=$((TESTS_PASSED+1))
    elif [ "$expected_result" = "failure" ] && [ $exit_status -ne 0 ]; then
        echo -e "${GREEN}✅ 通过${NC} (预期失败)"
        TESTS_PASSED=$((TESTS_PASSED+1))
    else
        echo -e "${RED}❌ 失败${NC} (退出状态: $exit_status)"
        TESTS_FAILED=$((TESTS_FAILED+1))
    fi
    
    echo "输出:"
    echo "$output" | sed 's/^/  /'
    echo "-----------------------------------"
}

# 清理之前的测试环境
cleanup() {
    rm -rf "${TEST_HOME}"
}

# 创建测试环境
setup() {
    echo "创建测试环境..."
    cleanup
    mkdir -p "${TEST_CONFIG_DIR}" "${TEST_CONFIG_D}" "${TEST_LOG_DIR}" "${TEST_BIN_DIR}"
    
    # 创建指向真实 Telegraf 二进制文件的符号链接
    ln -sf "${TELEGRAF_BIN}" "${TEST_BIN_DIR}/telegraf"
    
    # 创建有效的Telegraf配置
    cat > "${TEST_CONFIG}" <<EOF
# 有效的Telegraf配置文件
[agent]
  interval = "60s"
  round_interval = true
  metric_batch_size = 1000
  metric_buffer_limit = 10000
  collection_jitter = "5s"
  flush_interval = "30s"
  flush_jitter = "5s"
  precision = "s"
  logfile = "${TEST_LOG_DIR}/telegraf.log"
  hostname = ""
  omit_hostname = false

[[outputs.kafka]]
  brokers = ["kafka1:9092", "kafka2:9092"]
  topic = "telegraf"
EOF

    # 创建无效的配置
    mkdir -p "${TEST_CONFIG_D}/invalid"
    cat > "${TEST_CONFIG_D}/invalid/bad_config.conf" <<EOF
# 无效的Telegraf配置
[[inputs.invalid
  bad_syntax = 
EOF

    # 创建测试配置文件
    cat > "${TEST_HOME}/test_config.yaml" <<EOF
global:
  log_level: debug

telegraf:
  home: "${TEST_HOME}"
  config: "${TEST_CONFIG}"
  config_dir: "${TEST_CONFIG_D}"
  log_dir: "${TEST_LOG_DIR}"
EOF

    echo "测试环境创建完成"
}

# 开始测试
setup

# 测试1: 基本配置检查 - 有效配置（模拟）
run_test "基本配置检查 - 有效配置" \
    "${TEST_BIN_DIR}/telegraf --config ${TEST_CONFIG} --test" \
    "success" \
    "检查有效的Telegraf配置文件，应成功执行"

# 测试2: 基本配置检查 - 无法找到配置文件
run_test "配置检查 - 配置文件不存在" \
    "${TEST_BIN_DIR}/telegraf --config /path/not/exist.conf --test" \
    "failure" \
    "检查不存在的配置文件，应该失败"

# 测试3: 检查配置 - 多输入插件
cat >> "${TEST_CONFIG}" <<EOF

[[inputs.cpu]]
  percpu = true
  totalcpu = true
  collect_cpu_time = false
  report_active = false
EOF

run_test "配置检查 - 添加CPU插件" \
    "${TEST_BIN_DIR}/telegraf --config ${TEST_CONFIG} --test" \
    "success" \
    "在配置中添加CPU插件后进行检查，应该成功"

# 测试4: 检查配置 - 添加无效配置
cp "${TEST_CONFIG}" "${TEST_CONFIG}.bak"
echo "[[inputs.invalid" >> "${TEST_CONFIG}"
run_test "配置检查 - 无效配置" \
    "${TEST_BIN_DIR}/telegraf --config ${TEST_CONFIG} --test" \
    "failure" \
    "检查包含语法错误的配置，应该失败"
mv "${TEST_CONFIG}.bak" "${TEST_CONFIG}"

# 测试5: 检查配置 - 配置目录包含多个文件
cat > "${TEST_CONFIG_D}/inputs.conf" <<EOF
[[inputs.diskio]]
  devices = ["sda", "sdb"]
  skip_serial_number = true
EOF

run_test "配置检查 - 配置目录" \
    "${TEST_BIN_DIR}/telegraf --config ${TEST_CONFIG} --config-directory ${TEST_CONFIG_D} --test" \
    "success" \
    "检查主配置文件及配置目录，应该成功"

# 输出测试结果摘要
echo ""
echo "==========================================="
echo "测试结果摘要"
echo "==========================================="
echo "总测试数: ${TESTS_TOTAL}"
echo -e "通过测试: ${GREEN}${TESTS_PASSED}${NC}"
echo -e "失败测试: ${RED}${TESTS_FAILED}${NC}"
echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S')"

# 清理测试环境
if [ $TESTS_FAILED -eq 0 ]; then
    echo "所有测试通过，正在清理测试环境..."
    cleanup
else
    echo "有测试失败，保留测试环境供调试 (${TEST_HOME})"
fi

# 返回状态码
if [ $TESTS_FAILED -eq 0 ]; then
    exit 0
else
    exit 1
fi 