#!/bin/bash
#
# 自测运行脚本 - 运行所有Telegraf功能测试
# 文件名: run_telegraf_tests.sh
#

# 设置颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 测试结果统计
TOTAL_SCRIPTS=0
PASSED_SCRIPTS=0
FAILED_SCRIPTS=0

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# 存储测试结果的目录
RESULTS_DIR="${PROJECT_ROOT}/results"
mkdir -p "${RESULTS_DIR}"

# 测试结果日志
LOG_FILE="${RESULTS_DIR}/telegraf_tests_$(date '+%Y%m%d_%H%M%S').log"

echo "==========================================="
echo "    Telegraf功能测试套件执行脚本"
echo "==========================================="
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo "测试脚本目录: ${SCRIPT_DIR}"
echo "结果保存至: ${LOG_FILE}"
echo ""

# 记录环境信息
{
    echo "============ 测试环境信息 ============"
    echo "操作系统: $(uname -a)"
    echo "测试时间: $(date '+%Y-%m-%d %H:%M:%S')"
    
    # 检查Telegraf
    TELEGRAF_BIN="/Users/<USER>/code/dci/dci-workspace/telegraf/telegraf"
    if [ -f "$TELEGRAF_BIN" ]; then
        echo "Telegraf版本: $($TELEGRAF_BIN --version 2>&1 | head -n 1)"
    else
        echo "警告: Telegraf未找到于 $TELEGRAF_BIN"
    fi
    
    # 检查dcicollector
    if command -v dciagent &> /dev/null; then
        echo "dcicollector版本: $(dciagent version 2>&1)"
    else
        echo "警告: dcicollector命令未找到"
    fi
    echo "=======================================\n"
} | tee -a "${LOG_FILE}"

# 要执行的测试脚本列表
TEST_SCRIPTS=(
    "self_test_telegraf_config_check_tool.sh" 
    "self_test_telegraf_log_management_tool.sh"
    "self_test_telegraf_process_control.sh"
)

# 运行测试脚本
run_test_script() {
    local script="$1"
    local script_path="${SCRIPT_DIR}/${script}"
    
    if [ ! -f "${script_path}" ]; then
        echo -e "${RED}错误: 测试脚本不存在: ${script_path}${NC}" | tee -a "${LOG_FILE}"
        return 1
    fi
    
    echo -e "\n${BLUE}============================================${NC}" | tee -a "${LOG_FILE}"
    echo -e "${BLUE}开始执行测试脚本: ${script}${NC}" | tee -a "${LOG_FILE}"
    echo -e "${BLUE}============================================${NC}" | tee -a "${LOG_FILE}"
    
    # 确保脚本有执行权限
    chmod +x "${script_path}"
    
    # 执行测试脚本并记录输出
    "${script_path}" | tee -a "${LOG_FILE}"
    local exit_status=${PIPESTATUS[0]}
    
    echo -e "${BLUE}--------------------------------------------${NC}" | tee -a "${LOG_FILE}"
    if [ ${exit_status} -eq 0 ]; then
        echo -e "${GREEN}测试脚本执行成功: ${script}${NC}" | tee -a "${LOG_FILE}"
        return 0
    else
        echo -e "${RED}测试脚本执行失败: ${script} (退出状态: ${exit_status})${NC}" | tee -a "${LOG_FILE}"
        return 1
    fi
}

# 主测试循环
for script in "${TEST_SCRIPTS[@]}"; do
    TOTAL_SCRIPTS=$((TOTAL_SCRIPTS+1))
    
    if run_test_script "${script}"; then
        PASSED_SCRIPTS=$((PASSED_SCRIPTS+1))
    else
        FAILED_SCRIPTS=$((FAILED_SCRIPTS+1))
    fi
done

# 输出测试结果摘要
{
    echo ""
    echo "==========================================="
    echo "            测试套件执行结果"
    echo "==========================================="
    echo "总测试脚本数: ${TOTAL_SCRIPTS}"
    echo -e "通过脚本数: ${GREEN}${PASSED_SCRIPTS}${NC}"
    echo -e "失败脚本数: ${RED}${FAILED_SCRIPTS}${NC}"
    echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "完整日志: ${LOG_FILE}"
} | tee -a "${LOG_FILE}"

# 返回状态码
if [ $FAILED_SCRIPTS -eq 0 ]; then
    echo -e "\n${GREEN}测试套件全部通过!${NC}"
    exit 0
else
    echo -e "\n${RED}测试套件执行失败!${NC}"
    exit 1
fi 