#!/bin/bash
#
# 自测脚本 - Telegraf日志管理工具测试
# 测试ID: telegraf-log-management-01
# 文件名: self_test_telegraf_log_management_tool.sh
#

# 设置颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # 无颜色

# 测试结果统计
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# 测试时使用的临时目录
TEST_HOME="/tmp/dci-test-telegraf"
TEST_CONFIG_DIR="${TEST_HOME}/conf"
TEST_CONFIG="${TEST_CONFIG_DIR}/telegraf.conf"
TEST_CONFIG_D="${TEST_CONFIG_DIR}/telegraf.d"
TEST_LOG_DIR="${TEST_HOME}/logs"
TEST_BIN_DIR="${TEST_HOME}/bin"

# 指定Telegraf二进制文件路径
TELEGRAF_BIN="../bin/telegraf"

# 确保 Telegraf 二进制文件存在
if [ ! -f "$TELEGRAF_BIN" ]; then
    echo -e "${RED}错误: 未找到 Telegraf 二进制文件: $TELEGRAF_BIN${NC}"
    exit 1
fi

# 确保dcicollector命令可用
DCICOLLECTOR_CMD="$(which dciagent 2>/dev/null)"
if [ -z "$DCICOLLECTOR_CMD" ]; then
    echo -e "${YELLOW}警告: 未找到dcicollector命令。将使用模拟命令进行测试。${NC}"
    DCICOLLECTOR_CMD="echo dciagent"
fi

echo "==========================================="
echo "    Telegraf日志管理工具自测脚本"
echo "==========================================="
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo "测试主目录: ${TEST_HOME}"
echo "Telegraf路径: ${TELEGRAF_BIN}"
echo ""

# 辅助函数：运行测试并记录结果
run_test() {
    local test_name="$1"
    local test_cmd="$2"
    local expected_result="$3"
    local test_description="$4"
    
    echo -e "${YELLOW}[测试 $((TESTS_TOTAL+1))]${NC} $test_name"
    echo "描述: $test_description"
    echo "命令: $test_cmd"
    
    TESTS_TOTAL=$((TESTS_TOTAL+1))
    
    # 执行测试命令并捕获输出和退出状态
    output=$(eval "$test_cmd" 2>&1)
    exit_status=$?
    
    # 检查结果
    if [ "$expected_result" = "success" ] && [ $exit_status -eq 0 ]; then
        echo -e "${GREEN}✅ 通过${NC}"
        TESTS_PASSED=$((TESTS_PASSED+1))
    elif [ "$expected_result" = "failure" ] && [ $exit_status -ne 0 ]; then
        echo -e "${GREEN}✅ 通过${NC} (预期失败)"
        TESTS_PASSED=$((TESTS_PASSED+1))
    else
        echo -e "${RED}❌ 失败${NC} (退出状态: $exit_status)"
        TESTS_FAILED=$((TESTS_FAILED+1))
    fi
    
    echo "输出:"
    echo "$output" | sed 's/^/  /'
    echo "-----------------------------------"
}

# 验证文件是否存在
verify_file_exists() {
    local file="$1"
    if [ -f "$file" ]; then
        return 0
    else
        return 1
    fi
}

# 验证文件内容是否包含特定字符串
verify_file_contains() {
    local file="$1"
    local content="$2"
    if grep -q "$content" "$file" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 清理之前的测试环境
cleanup() {
    rm -rf "${TEST_HOME}"
}

# 创建测试环境
setup() {
    echo "创建测试环境..."
    cleanup
    mkdir -p "${TEST_CONFIG_DIR}" "${TEST_CONFIG_D}" "${TEST_LOG_DIR}/archive" "${TEST_BIN_DIR}"
    
    # 创建指向真实 Telegraf 二进制文件的符号链接
    ln -sf "${TELEGRAF_BIN}" "${TEST_BIN_DIR}/telegraf"
    
    # 创建模拟的Telegraf日志文件
    for i in {1..5}; do
        echo "日志内容 $i - 时间戳: $(date)" > "${TEST_LOG_DIR}/telegraf.log.$i"
    done
    
    # 创建当前日志文件
    cat > "${TEST_LOG_DIR}/telegraf.log" <<EOF
[2025-04-30 00:00:00] 调试信息: Telegraf启动中
[2025-04-30 00:00:01] 信息: 加载配置文件
[2025-04-30 00:00:02] 信息: 连接到Kafka服务器
[2025-04-30 00:00:03] 警告: 服务器响应缓慢
[2025-04-30 00:00:04] 错误: 无法连接到TDengine数据库
[2025-04-30 00:00:05] 信息: 使用备用存储
[2025-04-30 00:00:06] 调试信息: 执行输入插件: cpu
[2025-04-30 00:00:07] 调试信息: 执行输出插件: kafka
EOF
    
    # 创建模拟的历史日志
    mkdir -p "${TEST_LOG_DIR}/archive/2025-04-29"
    echo "历史日志内容" > "${TEST_LOG_DIR}/archive/2025-04-29/telegraf.log"
    
    # 创建Telegraf配置文件（用于日志相关配置）
    cat > "${TEST_CONFIG}" <<EOF
# Telegraf配置文件
[agent]
  interval = "60s"
  round_interval = true
  metric_batch_size = 1000
  metric_buffer_limit = 10000
  collection_jitter = "5s"
  flush_interval = "30s"
  flush_jitter = "5s"
  precision = "s"
  logfile = "${TEST_LOG_DIR}/telegraf.log"
  debug = false
  quiet = false
EOF
    
    # 创建测试配置文件
    cat > "${TEST_HOME}/test_config.yaml" <<EOF
global:
  log_level: debug

telegraf:
  home: "${TEST_HOME}"
  config: "${TEST_CONFIG}"
  config_dir: "${TEST_CONFIG_D}"
  log_dir: "${TEST_LOG_DIR}"
EOF

    echo "测试环境创建完成"
}

# 开始测试
setup

# 模拟日志管理功能的实现
# 注意：以下是为了测试目的模拟的命令，实际应使用dcicollector

# 日志查看函数
view_log() {
    local level=$1
    local tail=$2
    
    if [ -n "$level" ]; then
        grep "$level" "${TEST_LOG_DIR}/telegraf.log"
    elif [ -n "$tail" ] && [ "$tail" -gt 0 ]; then
        tail -n "$tail" "${TEST_LOG_DIR}/telegraf.log"
    else
        cat "${TEST_LOG_DIR}/telegraf.log"
    fi
    return 0
}

# 日志轮转函数
rotate_log() {
    local timestamp=$(date '+%Y%m%d%H%M%S')
    cp "${TEST_LOG_DIR}/telegraf.log" "${TEST_LOG_DIR}/telegraf.log.$timestamp"
    cat /dev/null > "${TEST_LOG_DIR}/telegraf.log"
    return 0
}

# 日志清理函数
clean_log() {
    # 模拟清理7天前的日志
    find "${TEST_LOG_DIR}/archive" -type f -mtime +7 -delete 2>/dev/null
    return 0
}

# 日志统计函数
log_stats() {
    local total_size=$(du -sh "${TEST_LOG_DIR}" | cut -f1)
    echo "总日志大小: $total_size"
    echo "日志文件数: $(find "${TEST_LOG_DIR}" -type f | wc -l)"
    return 0
}

# 测试1: 查看日志 - 基本功能
run_test "查看日志 - 基本功能" \
    "view_log" \
    "success" \
    "查看当前Telegraf日志，验证基本日志查看功能"

# 测试2: 查看日志 - 显示最后几行
run_test "查看日志 - 显示最后几行" \
    "view_log '' 3" \
    "success" \
    "仅显示日志文件的最后3行"

# 测试3: 查看日志 - 过滤特定级别
run_test "查看日志 - 过滤特定级别" \
    "view_log 错误" \
    "success" \
    "仅显示错误级别的日志"

# 测试4: 轮转日志
run_test "轮转日志" \
    "rotate_log" \
    "success" \
    "执行日志轮转，当前日志文件应备份并创建新文件"

# 验证日志轮转是否成功
new_log_created=0
if verify_file_exists "${TEST_LOG_DIR}/telegraf.log"; then
    if [ ! -s "${TEST_LOG_DIR}/telegraf.log" ]; then
        # 新日志文件应该是空的
        new_log_created=1
    fi
fi

rotated_log_found=0
for file in "${TEST_LOG_DIR}"/telegraf.log.*; do
    if verify_file_contains "$file" "无法连接到TDengine数据库"; then
        # 旧日志内容应在轮转的文件中
        rotated_log_found=1
        break
    fi
done

if [ $new_log_created -eq 1 ] && [ $rotated_log_found -eq 1 ]; then
    echo -e "${GREEN}✅ 验证通过: 日志轮转成功${NC}"
    TESTS_PASSED=$((TESTS_PASSED+1))
else
    echo -e "${RED}❌ 验证失败: 日志轮转问题${NC}"
    TESTS_FAILED=$((TESTS_FAILED+1))
fi
TESTS_TOTAL=$((TESTS_TOTAL+1))

# 测试5: 清理旧日志
run_test "清理旧日志" \
    "clean_log" \
    "success" \
    "清理7天前的旧日志文件"

# 测试6: 查看日志统计
run_test "查看日志统计" \
    "log_stats" \
    "success" \
    "显示日志文件占用空间和统计信息"

# 测试7: 导出日志
export_file="${TEST_HOME}/export_logs.txt"
run_test "导出日志" \
    "cat ${TEST_LOG_DIR}/telegraf.log > ${export_file}" \
    "success" \
    "将日志导出到指定文件"

# 验证日志导出是否成功
if verify_file_exists "$export_file"; then
    if [ -s "$export_file" ]; then
        echo -e "${GREEN}✅ 验证通过: 日志导出成功${NC}"
        TESTS_PASSED=$((TESTS_PASSED+1))
    else
        echo -e "${RED}❌ 验证失败: 导出文件为空${NC}"
        TESTS_FAILED=$((TESTS_FAILED+1))
    fi
else
    echo -e "${RED}❌ 验证失败: 导出文件不存在${NC}"
    TESTS_FAILED=$((TESTS_FAILED+1))
fi
TESTS_TOTAL=$((TESTS_TOTAL+1))

# 测试8: 修改日志级别
run_test "设置日志级别" \
    "sed -i.bak 's/debug = false/debug = true/' ${TEST_CONFIG}" \
    "success" \
    "将Telegraf日志级别设置为debug模式"

# 输出测试结果摘要
echo ""
echo "==========================================="
echo "测试结果摘要"
echo "==========================================="
echo "总测试数: ${TESTS_TOTAL}"
echo -e "通过测试: ${GREEN}${TESTS_PASSED}${NC}"
echo -e "失败测试: ${RED}${TESTS_FAILED}${NC}"
echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S')"

# 清理测试环境
if [ $TESTS_FAILED -eq 0 ]; then
    echo "所有测试通过，正在清理测试环境..."
    cleanup
else
    echo "有测试失败，保留测试环境供调试 (${TEST_HOME})"
fi

# 返回状态码
if [ $TESTS_FAILED -eq 0 ]; then
    exit 0
else
    exit 1
fi 