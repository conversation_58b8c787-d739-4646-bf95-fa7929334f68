#!/bin/bash
#
# 自测脚本 - Telegraf进程控制功能测试
# 测试ID: telegraf-process-control-01
# 文件名: self_test_telegraf_process_control.sh
#

# 设置颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # 无颜色

# 测试结果统计
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# 测试时使用的临时目录
TEST_HOME="/tmp/dci-test-telegraf"
TEST_CONFIG_DIR="${TEST_HOME}/conf"
TEST_CONFIG="${TEST_CONFIG_DIR}/telegraf.conf"
TEST_CONFIG_D="${TEST_CONFIG_DIR}/telegraf.d"
TEST_LOG_DIR="${TEST_HOME}/logs"
TEST_BIN_DIR="${TEST_HOME}/bin"
TEST_RUN_DIR="${TEST_HOME}/run"

# 指定Telegraf二进制文件路径
TELEGRAF_BIN="../bin/telegraf"

# 确保 Telegraf 二进制文件存在
if [ ! -f "$TELEGRAF_BIN" ]; then
    echo -e "${RED}错误: 未找到 Telegraf 二进制文件: $TELEGRAF_BIN${NC}"
    exit 1
fi

# 确保dcicollector命令可用
DCICOLLECTOR_CMD="$(which dciagent 2>/dev/null)"
if [ -z "$DCICOLLECTOR_CMD" ]; then
    echo -e "${YELLOW}警告: 未找到dcicollector命令。将使用模拟命令进行测试。${NC}"
    DCICOLLECTOR_CMD="echo dciagent"
fi

echo "==========================================="
echo "    Telegraf进程控制功能自测脚本"
echo "==========================================="
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo "测试主目录: ${TEST_HOME}"
echo "Telegraf路径: ${TELEGRAF_BIN}"
echo ""

# 辅助函数：运行测试并记录结果
run_test() {
    local test_name="$1"
    local test_cmd="$2"
    local expected_result="$3"
    local test_description="$4"
    
    echo -e "${YELLOW}[测试 $((TESTS_TOTAL+1))]${NC} $test_name"
    echo "描述: $test_description"
    echo "命令: $test_cmd"
    
    TESTS_TOTAL=$((TESTS_TOTAL+1))
    
    # 执行测试命令并捕获输出和退出状态
    output=$(eval "$test_cmd" 2>&1)
    exit_status=$?
    
    # 检查结果
    if [ "$expected_result" = "success" ] && [ $exit_status -eq 0 ]; then
        echo -e "${GREEN}✅ 通过${NC}"
        TESTS_PASSED=$((TESTS_PASSED+1))
    elif [ "$expected_result" = "failure" ] && [ $exit_status -ne 0 ]; then
        echo -e "${GREEN}✅ 通过${NC} (预期失败)"
        TESTS_PASSED=$((TESTS_PASSED+1))
    else
        echo -e "${RED}❌ 失败${NC} (退出状态: $exit_status)"
        TESTS_FAILED=$((TESTS_FAILED+1))
    fi
    
    echo "输出:"
    echo "$output" | sed 's/^/  /'
    echo "-----------------------------------"
}

# 检查模拟telegraf进程是否正在运行
is_telegraf_running() {
    if [ -f "${TEST_RUN_DIR}/telegraf.pid" ]; then
        pid=$(cat "${TEST_RUN_DIR}/telegraf.pid")
        if [ -n "$pid" ] && [ -f "${TEST_RUN_DIR}/telegraf.running" ]; then
            # 对于macOS，使用ps命令检查进程是否存在（仅模拟）
            return 0 # 假设进程在运行
        fi
    fi
    return 1 # 未运行
}

# 清理之前的测试环境
cleanup() {
    # 确保模拟进程被终止
    if [ -f "${TEST_RUN_DIR}/telegraf.pid" ]; then
        kill_mock_process
    fi
    rm -rf "${TEST_HOME}"
}

# 创建测试环境
setup() {
    echo "创建测试环境..."
    cleanup
    mkdir -p "${TEST_CONFIG_DIR}" "${TEST_CONFIG_D}" "${TEST_LOG_DIR}" "${TEST_BIN_DIR}" "${TEST_RUN_DIR}"
    
    # 创建指向真实 Telegraf 二进制文件的符号链接
    ln -sf "${TELEGRAF_BIN}" "${TEST_BIN_DIR}/telegraf"
    
    # 创建用于模拟进程控制的脚本
    cat > "${TEST_BIN_DIR}/telegraf_control.sh" <<EOF
#!/bin/bash
# 模拟的Telegraf进程控制脚本

function start() {
    if [ -f "${TEST_RUN_DIR}/telegraf.running" ]; then
        echo "Telegraf已在运行"
        return 1
    fi
    
    echo "启动Telegraf..."
    echo "\$\$" > "${TEST_RUN_DIR}/telegraf.pid"
    touch "${TEST_RUN_DIR}/telegraf.running"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Telegraf启动成功" >> "${TEST_LOG_DIR}/telegraf.log"
    return 0
}

function stop() {
    if [ ! -f "${TEST_RUN_DIR}/telegraf.running" ]; then
        echo "Telegraf未在运行"
        return 1
    fi
    
    echo "停止Telegraf..."
    rm -f "${TEST_RUN_DIR}/telegraf.running"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Telegraf已停止" >> "${TEST_LOG_DIR}/telegraf.log"
    return 0
}

function status() {
    if [ -f "${TEST_RUN_DIR}/telegraf.running" ]; then
        echo "Telegraf正在运行"
        if [ "\$1" == "--verbose" ]; then
            echo "PID: \$(cat ${TEST_RUN_DIR}/telegraf.pid)"
            echo "内存使用: 约 24MB"
            echo "CPU使用: 约 0.5%"
            echo "运行时间: $(date -r ${TEST_RUN_DIR}/telegraf.running '+%H小时%M分钟')"
        fi
        return 0
    else
        echo "Telegraf未在运行"
        return 1
    fi
}

function restart() {
    stop
    sleep 1
    start
    return 0
}

# 处理命令行参数
case "\$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status "\$2"
        ;;
    *)
        echo "用法: \$0 {start|stop|restart|status}"
        exit 1
        ;;
esac
exit \$?
EOF
    chmod +x "${TEST_BIN_DIR}/telegraf_control.sh"
    
    # 创建有效的Telegraf配置
    cat > "${TEST_CONFIG}" <<EOF
# 有效的Telegraf配置文件
[agent]
  interval = "60s"
  round_interval = true
  metric_batch_size = 1000
  metric_buffer_limit = 10000
  collection_jitter = "5s"
  flush_interval = "30s"
  flush_jitter = "5s"
  precision = "s"
  logfile = "${TEST_LOG_DIR}/telegraf.log"
  hostname = ""
  omit_hostname = false

[[outputs.kafka]]
  brokers = ["kafka1:9092", "kafka2:9092"]
  topic = "telegraf"

[[inputs.cpu]]
  percpu = true
  totalcpu = true
EOF
    
    # 创建测试配置文件
    cat > "${TEST_HOME}/test_config.yaml" <<EOF
global:
  log_level: debug

telegraf:
  home: "${TEST_HOME}"
  config: "${TEST_CONFIG}"
  config_dir: "${TEST_CONFIG_D}"
  log_dir: "${TEST_LOG_DIR}"
EOF

    echo "测试环境创建完成"
}

# 控制模拟Telegraf进程的函数
start_mock_process() {
    "${TEST_BIN_DIR}/telegraf_control.sh" start
    return $?
}

stop_mock_process() {
    "${TEST_BIN_DIR}/telegraf_control.sh" stop
    return $?
}

restart_mock_process() {
    "${TEST_BIN_DIR}/telegraf_control.sh" restart
    return $?
}

check_mock_process_status() {
    "${TEST_BIN_DIR}/telegraf_control.sh" status $1
    return $?
}

kill_mock_process() {
    # 清理模拟进程状态
    rm -f "${TEST_RUN_DIR}/telegraf.pid" "${TEST_RUN_DIR}/telegraf.running"
}

# 开始测试
setup

# 测试1: 初始状态检查 - 应该未运行
run_test "初始状态检查 - 进程未运行" \
    "check_mock_process_status" \
    "failure" \
    "检查Telegraf初始状态，此时应未运行"

# 测试2: 启动Telegraf
run_test "启动Telegraf" \
    "start_mock_process" \
    "success" \
    "启动Telegraf进程"

# 测试3: 验证运行状态
run_test "验证运行状态" \
    "check_mock_process_status" \
    "success" \
    "验证Telegraf已成功运行"

# 测试4: 重复启动 - 应该报错
run_test "重复启动检查" \
    "start_mock_process" \
    "failure" \
    "再次启动已运行的Telegraf，应该返回错误"

# 测试5: 停止Telegraf
run_test "停止Telegraf" \
    "stop_mock_process" \
    "success" \
    "停止Telegraf进程"

# 测试6: 验证停止状态
run_test "验证停止状态" \
    "check_mock_process_status" \
    "failure" \
    "验证Telegraf已成功停止"

# 测试7: 再次停止 - 应该报错
run_test "重复停止检查" \
    "stop_mock_process" \
    "failure" \
    "停止未运行的Telegraf，应该返回错误"

# 测试8: 重启Telegraf
run_test "重启Telegraf" \
    "restart_mock_process" \
    "success" \
    "重启Telegraf进程"

# 测试9: 验证重启后状态
run_test "验证重启后状态" \
    "check_mock_process_status" \
    "success" \
    "验证Telegraf重启后处于运行状态"

# 测试10: 查看详细状态
run_test "查看详细状态" \
    "check_mock_process_status --verbose" \
    "success" \
    "以详细模式查看Telegraf状态，包括内存和CPU使用情况"

# 输出测试结果摘要
echo ""
echo "==========================================="
echo "测试结果摘要"
echo "==========================================="
echo "总测试数: ${TESTS_TOTAL}"
echo -e "通过测试: ${GREEN}${TESTS_PASSED}${NC}"
echo -e "失败测试: ${RED}${TESTS_FAILED}${NC}"
echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S')"

# 清理测试环境
if [ $TESTS_FAILED -eq 0 ]; then
    echo "所有测试通过，正在清理测试环境..."
    cleanup
else
    echo "有测试失败，保留测试环境供调试 (${TEST_HOME})"
fi

# 返回状态码
if [ $TESTS_FAILED -eq 0 ]; then
    exit 0
else
    exit 1
fi 