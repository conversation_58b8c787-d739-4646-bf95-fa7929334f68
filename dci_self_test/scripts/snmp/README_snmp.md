
# DCI监控系统 - SNMP测试工具集使用说明更新

## 概述

SNMP测试工具集是DCI监控系统自测试框架的一部分，用于验证系统对SNMP协议的支持能力。这些工具可以帮助用户测试Telegraf对网络设备的SNMP数据采集功能，以及数据从采集到Kafka的完整流程。

本文档提供了SNMP测试工具集中各个脚本的详细说明和使用方法。

## 目录结构

```
dci_self_test/scripts/snmp/
├── snmp_test.sh                  # SNMP采集功能验证脚本
├── snmpwalk_test.sh              # SNMP OID测试脚本
├── telegraf_kafka_test.sh        # Telegraf与Kafka集成测试脚本
├── verify_kafka_data.sh          # Kafka数据验证工具
├── analyze_snmp_results.sh       # SNMP采集结果分析和中文注释工具
└── README_snmp.md                # 本文档
```

## 脚本说明

### 1. snmp_test.sh - SNMP采集功能验证脚本

#### 功能介绍

`snmp_test.sh`是一个全面的SNMP采集测试工具，用于验证Telegraf能否正确采集网络设备的SNMP数据。该脚本支持两种运行模式：

- **模拟模式**：使用预定义的模拟数据，无需实际连接SNMP设备
- **实际SNMP采集模式**：连接真实的网络设备进行SNMP数据采集

脚本会自动检查网络连通性，验证配置有效性，并进行SNMP数据采集测试。

#### 使用方法

```bash
./snmp_test.sh [选项]
```

#### 选项

- `-k, --kafka`：启用Kafka输出，将采集的数据发送到Kafka
- `-r, --real`：使用实际SNMP采集模式（默认为模拟模式）

#### 示例

```bash
# 使用模拟模式测试
./snmp_test.sh

# 使用实际SNMP采集模式测试
./snmp_test.sh -r

# 使用模拟模式并将数据发送到Kafka
./snmp_test.sh -k

# 使用实际SNMP采集模式并将数据发送到Kafka
./snmp_test.sh -r -k
```

#### 输出文件

- 测试报告：`dci_self_test/results/snmp_test_results.md`
- 采集数据：`dci_self_test/results/telegraf-snmp-test.out`
- 日志文件：`dci_self_test/results/telegraf-snmp-test.log`

### 2. snmpwalk_test.sh - SNMP OID测试脚本

#### 功能介绍

`snmpwalk_test.sh`是用于在配置Telegraf前验证OID的可访问性和返回值的工具。该脚本可以帮助用户检查特定的SNMP OID是否可访问，并显示返回的数据。它会测试系统基本信息、接口表信息、接口扩展表信息、实体物理信息、华为特有实体MIB以及LLDP信息等各类OID。

脚本会自动检查必要命令(如snmpget和snmpwalk)是否存在，并显示命令执行时间，便于性能评估。

#### 使用方法

```bash
./snmpwalk_test.sh [选项]
```

#### 选项

- `-f, --full`：显示完整SNMP结果，而不是只显示前几行
- `-a, --all-hosts`：测试所有配置的主机，而不仅仅是第一个
- `-h, --help`：显示帮助信息

#### 示例

```bash
# 默认模式（简略输出，只测试第一个主机）
./snmpwalk_test.sh

# 显示完整SNMP结果
./snmpwalk_test.sh -f

# 测试所有配置的主机
./snmpwalk_test.sh -a

# 显示完整结果并测试所有主机
./snmpwalk_test.sh -f -a
```

#### 测试内容

脚本会测试以下OID类别：

1. **系统基本信息**：sysName、sysDescr、sysUpTime、sysLocation
2. **接口表信息**：ifIndex、ifDescr、ifType、ifAdminStatus、ifOperStatus
3. **接口扩展表信息**：ifName、ifHCInOctets、ifHCOutOctets等
4. **实体物理信息**：entPhysicalClass、entPhysicalName
5. **华为特有实体MIB**：hwEntityCpuUsage、hwEntityMemUsage、hwEntityTemperature
6. **LLDP信息**：lldpRemSysName、lldpRemPortId、lldpRemSysDesc、lldpRemPortDesc

### 3. telegraf_kafka_test.sh - Telegraf与Kafka集成测试脚本

#### 功能介绍

`telegraf_kafka_test.sh`用于测试Telegraf采集的SNMP数据能否正确发送到Kafka，并验证整个数据流的完整性。该脚本会执行以下步骤：

1. 检查环境（Telegraf、Kafka客户端工具、网络连接）
2. 运行SNMP测试脚本采集数据并发送到Kafka
3. 验证数据是否成功发送到Kafka
4. 生成测试报告

#### 使用方法

```bash
./telegraf_kafka_test.sh
```

#### 输出文件

- 测试报告：`dci_self_test/results/telegraf_kafka_test_report.md`

### 4. verify_kafka_data.sh - Kafka数据验证工具

#### 功能介绍

`verify_kafka_data.sh`用于验证SNMP数据是否成功写入Kafka，并提供详细的数据统计和分析。该脚本会执行以下操作：

1. 检查Kafka服务器连接状态
2. 检查Kafka主题是否存在
3. 从Kafka读取数据并进行分析
4. 生成统计信息和数据样例

#### 使用方法

```bash
./verify_kafka_data.sh
```

#### 输出文件

- Kafka消费日志：`dci_self_test/results/kafka-consumer-test-full.log`

### 5. analyze_snmp_results.sh - SNMP采集结果分析工具

#### 功能介绍

`analyze_snmp_results.sh`是一个强大的分析工具，用于解析SNMP采集结果并生成带有中文注释的详细报告。该工具支持以下功能：

1. 解析JSON格式或Influx Line Protocol格式的Telegraf输出
2. 为SNMP指标添加中文说明
3. 格式化数据值，使其更易于理解
4. 生成结构化的分析报告，包括：
   - 设备基本信息
   - 接口流量统计
   - LLDP邻居发现信息
   - 数据流量汇总

#### 使用方法

```bash
./analyze_snmp_results.sh
```

#### 输出文件

- 分析报告：`dci_self_test/results/snmp_results_annotated.txt`

## 典型使用场景

### 场景一：验证特定OID的可访问性

在配置Telegraf采集之前，可以使用`snmpwalk_test.sh`脚本测试特定OID是否可访问：

```bash
# 测试单个主机的OID可访问性
./snmpwalk_test.sh

# 查看完整结果
./snmpwalk_test.sh -f
```

### 场景二：验证SNMP采集功能

当需要验证系统是否能正确采集SNMP数据时，可以使用`snmp_test.sh`脚本：

```bash
# 使用模拟模式进行快速测试
./snmp_test.sh

# 使用实际SNMP采集模式进行真实环境测试
./snmp_test.sh -r
```

### 场景三：验证完整数据流

当需要验证从SNMP采集到Kafka的完整数据流时，可以使用`telegraf_kafka_test.sh`脚本：

```bash
./telegraf_kafka_test.sh
```

### 场景四：分析SNMP数据

当需要对采集的SNMP数据进行详细分析时，可以使用`analyze_snmp_results.sh`脚本：

```bash
./analyze_snmp_results.sh
```

## 常见问题排查

### 1. SNMP采集失败

如果SNMP采集失败，请检查以下几点：

- 网络连接是否正常
- SNMP社区字符串是否正确
- 设备是否支持所查询的OID
- MIB文件是否正确加载

### 2. OID访问问题

如果`snmpwalk_test.sh`显示OID无法访问或返回"No Such Object"错误：

- 确认设备是否支持该OID
- 检查OID语法是否正确
- 验证SNMP版本和社区字符串是否匹配
- 使用`-f`参数查看完整错误信息

### 3. Kafka连接问题

如果无法连接到Kafka，请检查以下几点：

- Kafka服务器是否正常运行
- 网络连接是否正常
- Kafka主题是否存在
- 防火墙设置是否阻止了连接

### 4. 模拟模式下的数据问题

如果在模拟模式下遇到数据问题，请检查以下几点：

- 模拟数据文件是否正确生成
- Telegraf配置是否正确
- 输出插件是否正确配置

### 5. LLDP和华为特有MIB采集问题

本测试工具支持两种SNMP采集模式：

1. **OID模式**：直接使用数字OID（如`.1.3.6.1.2.1.1.5.0`）进行采集
2. **MIB模式**：使用MIB名称（如`SNMPv2-MIB::sysName.0`）进行采集

在DCI监控系统中，所有必要的MIB文件都存在于`/Users/<USER>/code/dci/dci-workspace/dci_self_test/config/mibs`目录中，因此测试脚本默认使用更易读的MIB模式。

使用MIB模式的优势：
- 更好的可读性和维护性
- 自动处理数据类型转换
- 提供更详细的错误信息和调试信息

若需使用OID模式（例如在MIB文件不可用的环境中），可以修改脚本中的配置部分：

```bash
# MIB模式示例
[[inputs.snmp.field]]
  name = "sysName"
  oid = "SNMPv2-MIB::sysName.0"
  is_tag = true

# OID模式示例
[[inputs.snmp.field]]
  name = "sysName"
  oid = ".1.3.6.1.2.1.1.5.0"
  is_tag = true
```

LLDP和华为特有MIB采集时的注意事项：
- 确保相应的MIB文件存在于MIB目录中
- LLDP采集需要`LLDP-MIB`
- 华为设备特有功能需要`HUAWEI-ENTITY-EXTENT-MIB`等华为专有MIB
- 在运行测试前确认设备支持相应的OID/MIB

## 高级配置

### 自定义MIB目录

如果需要使用不同的MIB目录，可以修改`snmp_test.sh`脚本中的MIB_PATH变量：

```bash
# 修改MIB目录
MIB_PATH="/path/to/your/mibs"
```

您也可以设置环境变量来指定MIB目录：

```bash
export MIBDIRS="/path/to/your/mibs"
./snmp_test.sh
```

### 自定义SNMP OID

如果需要采集特定的SNMP OID，可以修改`snmp_test.sh`脚本中的Telegraf配置部分。例如，添加新的OID采集：

```bash
[[inputs.snmp.field]]
  name = "customMetric"
  oid = ".1.3.6.1.4.1.X.X.X.X"  # 替换为您需要的OID
```

### 自定义Kafka配置

如果需要修改Kafka配置，可以编辑`snmp_test.sh`脚本中的Kafka输出插件部分：

```bash
[[outputs.kafka]]
  brokers = ["your-kafka-broker:9092"]  # 替换为您的Kafka服务器地址
  topic = "your-topic"                  # 替换为您的主题名称
```

## 结论

SNMP测试工具集提供了一套全面的工具，用于测试和验证DCI监控系统的SNMP数据采集功能。通过这些工具，用户可以轻松验证系统的各个组件是否正常工作，并快速排查潜在问题。
