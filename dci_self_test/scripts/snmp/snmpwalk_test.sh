#!/bin/bash
# SNMP OID测试脚本
# 用于在配置Telegraf前验证OID的可访问性和返回值

# 检查必要的命令是否存在
check_dependencies() {
  local missing_deps=()
  
  # 检查snmpget和snmpwalk命令是否存在
  if ! command -v snmpget &> /dev/null; then
    missing_deps+=("snmpget")
  fi
  
  if ! command -v snmpwalk &> /dev/null; then
    missing_deps+=("snmpwalk")
  fi
  
  # 如果有缺失的依赖，提示安装
  if [ ${#missing_deps[@]} -gt 0 ]; then
    echo "错误: 以下必要的命令未找到:"
    for dep in "${missing_deps[@]}"; do
      echo "  - $dep"
    done
    echo "请安装缺失的依赖后再运行此脚本。"
    echo "在大多数Linux系统上，可以使用以下命令安装:"
    echo "  - snmp工具: apt-get install snmp 或 yum install net-snmp-utils"
    exit 1
  fi
}

# 运行依赖检查
check_dependencies

# 输出颜色设置
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 设置必要的变量
# 定义测试主机列表，可以根据需要添加更多主机
declare -a HOST_LIST=(
  "************"  # 第一个为主要测试设备，默认只测第一个，有 -a 则全测
  "************"  
  # 可以在此添加更多主机
)

COMMUNITY="dcilab2025"
TIMEOUT="-t 2"  # 超时2秒
FULL_OUTPUT=false  # 默认不显示完整输出
TEST_ALL_HOSTS=false  # 默认只测试第一个主机

# 处理命令行参数
usage() {
  echo "用法: $0 [选项]"
  echo "选项:"
  echo "  -f, --full       显示完整SNMP结果，而不是只显示前几行"
  echo "  -a, --all-hosts  测试所有配置的主机，而不仅仅是第一个"
  echo "  -h, --help       显示此帮助信息"
  exit 1
}

while [[ "$#" -gt 0 ]]; do
  case $1 in
    -f|--full) FULL_OUTPUT=true; shift ;;
    -a|--all-hosts) TEST_ALL_HOSTS=true; shift ;;
    -h|--help) usage ;;
    *) echo "未知参数: $1"; usage ;;
  esac
done

echo -e "${YELLOW}=====================================${NC}"
echo -e "${GREEN}SNMP OID测试脚本 - 获取验证OID${NC}"
echo -e "${YELLOW}=====================================${NC}\n"

# 显示测试配置信息
echo -e "${GREEN}SNMP团体名: ${COMMUNITY}${NC}"
if $FULL_OUTPUT; then
  echo -e "${GREEN}输出模式: 完整输出${NC}"
else
  echo -e "${GREEN}输出模式: 简略输出（仅显示前几行）${NC}"
fi

if $TEST_ALL_HOSTS; then
  echo -e "${GREEN}测试模式: 测试所有主机${NC}"
  echo -e "${GREEN}主机列表: ${HOST_LIST[*]}${NC}\n"
else
  echo -e "${GREEN}测试模式: 仅测试第一个主机${NC}"
  echo -e "${GREEN}测试主机: ${HOST_LIST[0]}${NC}\n"
fi

# 辅助函数 - 运行SNMP命令并检查结果
run_snmp_cmd() {
    local cmd=$1
    local desc=$2
    local oid=$3
    
    # 如果是snmpwalk命令且需要完整输出，则移除head命令限制
    if $FULL_OUTPUT && [[ $cmd == *"snmpwalk"* && $cmd == *"head -n"* ]]; then
        cmd=$(echo $cmd | sed 's/| head -n [0-9]*//')
    fi
    
    echo -e "${YELLOW}测试: ${desc} (${oid})${NC}"
    echo -e "${YELLOW}命令: ${cmd}${NC}"
    
    # 检查系统是否支持纳秒精度的时间戳
    if date +%N | grep -q "N"; then
        # 系统不支持%N参数，使用time命令代替
        local time_output=$(TIMEFORMAT='%3R'; { time eval "$cmd" > /tmp/cmd_output 2>&1; } 2>&1)
        local cmd_status=$?
        result=$(cat /tmp/cmd_output)
        rm -f /tmp/cmd_output
        
        # 将time命令的输出转换为毫秒
        local elapsed=$(echo "$time_output" | awk '{printf "%.0f", $1 * 1000}')
    else
        # 系统支持%N参数，使用纳秒精度
        # 记录开始时间 - 使用更可靠的方法获取毫秒级精度
        local start_time=$(($(date +%s%N)/1000000))
        
        # 执行命令
        result=$(eval "$cmd" 2>&1)
        local cmd_status=$?
        
        # 计算执行时间（毫秒）
        local end_time=$(($(date +%s%N)/1000000))
        local elapsed=$((end_time - start_time))
    fi
    
    # 输出结果
    echo "$result"
    
    if [ $cmd_status -eq 0 ]; then
        # 检查结果中是否包含错误信息
        if echo "$result" | grep -q "No Such Object\|No Such Instance"; then
            echo -e "${RED}OID不存在或无法访问!${NC}"
            echo -e "${YELLOW}执行耗时: ${elapsed} 毫秒${NC}\n"
            return 1
        else
            echo -e "${GREEN}成功获取数据!${NC}"
            echo -e "${YELLOW}执行耗时: ${elapsed} 毫秒${NC}\n"
            return 0
        fi
    else
        echo -e "${RED}命令执行失败!${NC}"
        echo -e "${YELLOW}执行耗时: ${elapsed} 毫秒${NC}\n"
        return 1
    fi
}

# 获取LLDP索引函数
get_lldp_index() {
    local host=$1
    local oid=$2
    local cmd="snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} ${oid}"
    
    echo -e "${YELLOW}尝试获取LLDP索引...${NC}" >&2
    
    # 执行SNMP命令获取索引
    local output=$(eval "$cmd" 2>&1)
    local cmd_status=$?
    
    if [ $cmd_status -ne 0 ]; then
        echo -e "${RED}获取LLDP索引失败!${NC}" >&2
        return 1
    fi
    
    # 从输出中提取第一个有效的LLDP索引
    local index=$(echo "$output" | grep -v "No Such" | head -n 1 | awk -F "." '{print $(NF-2)"."$(NF-1)"."$NF}' | awk '{print $1}')
    
    if [ -z "$index" ]; then
        echo -e "${RED}未找到有效的LLDP索引!${NC}" >&2
        return 1
    fi
    
    echo -e "${GREEN}找到LLDP索引: ${index}${NC}" >&2
    # 只输出索引值，不包含任何调试信息
    echo "$index"
    return 0
}

# 对单个主机执行完整SNMP测试
test_host() {
    local host=$1
    echo -e "${GREEN}======================================${NC}"
    echo -e "${GREEN}开始测试主机: ${host}${NC}"
    echo -e "${GREEN}======================================${NC}\n"
    
    # 检查设备连通性
    echo -e "${YELLOW}检查设备连通性...${NC}"
    ping -c 1 -W 1 ${host} > /dev/null
    if [ $? -ne 0 ]; then
        echo -e "${RED}设备 ${host} 不可访问，跳过测试${NC}\n"
        return 1
    fi
    
    echo -e "${GREEN}设备 ${host} 可访问${NC}\n"
    
    ## 1. 系统基本信息测试
    echo -e "${GREEN}=== 系统基本信息测试 ===${NC}\n"
    
    run_snmp_cmd "snmpget ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.*******.0" "系统名称(sysName)" ".*******.*******.0"
    run_snmp_cmd "snmpget ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.*******.0" "系统描述(sysDescr)" ".*******.*******.0"
    run_snmp_cmd "snmpget ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.*******.0" "系统运行时间(sysUpTime)" ".*******.*******.0"
    run_snmp_cmd "snmpget ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.*******.0" "系统位置(sysLocation)" ".*******.*******.0"
    
    ## 2. 接口表信息测试
    echo -e "${GREEN}=== 接口表信息测试 ===${NC}\n"
    
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.******* | head -n 5" "接口表(ifTable)" ".*******.*******"
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.*******.1.1 | head -n 5" "接口索引(ifIndex)" ".*******.*******.1.1"
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.*******.1.2 | head -n 5" "接口描述(ifDescr)" ".*******.*******.1.2"
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.*******.1.3 | head -n 5" "接口类型(ifType)" ".*******.*******.1.3"
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.*******.1.7 | head -n 5" "接口管理状态(ifAdminStatus)" ".*******.*******.1.7"
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.*******.1.8 | head -n 5" "接口运行状态(ifOperStatus)" ".*******.*******.1.8"
    
    ## 3. 接口扩展表信息测试
    echo -e "${GREEN}=== 接口扩展表信息测试 ===${NC}\n"
    
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.********.1 | head -n 5" "接口扩展表(ifXTable)" ".*******.********.1"
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.********.1.1.1 | head -n 5" "接口名称(ifName)" ".*******.********.1.1.1"
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.********.1.1.6 | head -n 5" "接口入向流量(ifHCInOctets)" ".*******.********.1.1.6"
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.********.1.1.10 | head -n 5" "接口出向流量(ifHCOutOctets)" ".*******.********.1.1.10"
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.2.1.31.******* | head -n 5" "接口入向单播包数(ifHCInUcastPkts)" ".*******.2.1.31.*******"
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.2.1.31.******** | head -n 5" "接口出向单播包数(ifHCOutUcastPkts)" ".*******.2.1.31.********"
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.********.1.1.15 | head -n 5" "接口高速度(ifHighSpeed)" ".*******.********.1.1.15"
    
    ## 4. 实体物理信息测试
    echo -e "${GREEN}=== 实体物理信息测试 ===${NC}\n"
    
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.********.******* | head -n 10" "实体类别表(entPhysicalClass)" ".*******.********.*******"
    run_snmp_cmd "snmpget ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.********.*******.16842753" "实体类别-特定索引(entPhysicalClass.16842753)" ".*******.********.*******.16842753"
    run_snmp_cmd "snmpget ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.********.*******.16842753" "实体名称-特定索引(entPhysicalName.16842753)" ".*******.********.*******.16842753"
    
    ## 5. 华为特有实体MIB测试
    echo -e "${GREEN}=== 华为特有实体MIB测试 ===${NC}\n"
    
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.4.1.2011.*********.******* | head -n 10" "CPU使用率表(hwEntityCpuUsage)" ".*******.4.1.2011.*********.*******"
    run_snmp_cmd "snmpget ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.4.1.2011.*********.*******.16842753" "CPU使用率-特定索引(hwEntityCpuUsage.16842753)" ".*******.4.1.2011.*********.*******.16842753"
    run_snmp_cmd "snmpget ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.4.1.2011.*********.*******.16842753" "内存使用率-特定索引(hwEntityMemUsage.16842753)" ".*******.4.1.2011.*********.*******.16842753"
    run_snmp_cmd "snmpget ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .*******.4.1.2011.*********.********.16842753" "温度-特定索引(hwEntityTemperature.16842753)" ".*******.4.1.2011.*********.********.16842753"
    
    ## 6. LLDP信息测试
    echo -e "${GREEN}=== LLDP信息测试 ===${NC}\n"
    
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .1.0.8802.*******.******* | head -n 10" "LLDP远端设备名称表(lldpRemSysName)" ".1.0.8802.*******.*******"
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .1.0.8802.*******.******* | head -n 10" "LLDP远端端口ID表(lldpRemPortId)" ".1.0.8802.*******.*******"
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .1.0.8802.*******.******** | head -n 10" "LLDP远端系统描述表(lldpRemSysDesc)" ".1.0.8802.*******.********"
    run_snmp_cmd "snmpwalk ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .1.0.8802.*******.******* | head -n 10" "LLDP远端端口描述表(lldpRemPortDesc)" ".1.0.8802.*******.*******"
    
    # 动态获取LLDP索引并测试特定索引的LLDP数据
    echo -e "${GREEN}=== 尝试动态获取并测试LLDP索引 ===${NC}\n"
    local lldp_index=$(get_lldp_index "${host}" ".1.0.8802.*******.*******")
    if [ $? -eq 0 ] && [ ! -z "$lldp_index" ]; then
        echo -e "${GREEN}使用动态获取的LLDP索引: ${lldp_index}${NC}\n"
        run_snmp_cmd "snmpget ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .1.0.8802.*******.*******.${lldp_index}" "LLDP远端设备名称-动态索引(lldpRemSysName.${lldp_index})" ".1.0.8802.*******.*******.${lldp_index}"
        run_snmp_cmd "snmpget ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .1.0.8802.*******.*******.${lldp_index}" "LLDP远端端口ID-动态索引(lldpRemPortId.${lldp_index})" ".1.0.8802.*******.*******.${lldp_index}"
    else
        echo -e "${YELLOW}无法获取有效的LLDP索引，尝试使用默认索引测试${NC}\n"
        # 作为备选方案，仍尝试使用默认索引，但提示这是备选方案
        echo -e "${YELLOW}以下测试使用默认索引，可能在某些设备上不适用${NC}\n"
        run_snmp_cmd "snmpget ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .1.0.8802.*******.*******.123851676.8.1" "LLDP远端设备名称-默认索引(lldpRemSysName.123851676.8.1)" ".1.0.8802.*******.*******.123851676.8.1"
        run_snmp_cmd "snmpget ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .1.0.8802.*******.*******.123851676.8.1" "LLDP远端端口ID-默认索引(lldpRemPortId.123851676.8.1)" ".1.0.8802.*******.*******.123851676.8.1"
    fi
    
    # 检查LLDP通知间隔
    run_snmp_cmd "snmpget ${TIMEOUT} -v 2c -c ${COMMUNITY} ${host} .1.0.8802.*******.1.5.0" "LLDP通知间隔(lldpNotificationInterval.0)" ".1.0.8802.*******.1.5.0"
    
    echo -e "${GREEN}=== 主机 ${host} 测试完成 ===${NC}\n"
}

# 执行测试
if $TEST_ALL_HOSTS; then
    # 测试所有主机
    for host in "${HOST_LIST[@]}"; do
        test_host "$host"
    done
else
    # 只测试第一个主机
    test_host "${HOST_LIST[0]}"
fi

echo -e "${GREEN}=== SNMP OID测试完成 ===${NC}"
echo -e "${YELLOW}查看输出结果，确认所有OID是否都能正确返回数据${NC}"
echo -e "${YELLOW}输出中的'No Such Object'或'No Such Instance'表示OID不存在或无法访问，应做检查${NC}"
if $FULL_OUTPUT; then
    echo -e "${YELLOW}注意: 已启用完整输出模式，显示了所有SNMP数据${NC}"
else
    echo -e "${YELLOW}提示: 使用 -f 或 --full 参数可显示完整SNMP数据${NC}"
fi
echo -e "${YELLOW}提示: 使用 -a 或 --all-hosts 参数可测试所有配置的主机${NC}"
