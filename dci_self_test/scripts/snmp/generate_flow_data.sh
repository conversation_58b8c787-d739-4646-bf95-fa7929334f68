#!/bin/bash

# 本脚本生成模拟SNMP流量数据（JSON格式），以匹配真实采集产生的多指标输出。
# 它使用一个计数器文件来维持两次运行之间的状态。

# 定义工作目录和状态文件的绝对路径，确保在任何调用环境下都能正确找到
DCI_ROOT="/Users/<USER>/code/dci/dci-workspace"
STATE_DATA_DIR="${DCI_ROOT}/dci_self_test/data/snmp"
COUNTER_FILE="${STATE_DATA_DIR}/flow_counters.dat"

# 确保状态目录存在
mkdir -p ${STATE_DATA_DIR}

# --- 1. 初始化计数器 ---
IFHCINOCTETS=0
IFHCOUTOCTETS=0

# 如果状态文件存在，则从中读取上一次的计数值
if [ -f "${COUNTER_FILE}" ]; then
    source "${COUNTER_FILE}"
fi

# 如果计数器为0（通常是首次运行），则用一个随机基数进行初始化
if [ ${IFHCINOCTETS} -eq 0 ]; then
    IFHCINOCTETS=$((1000000000 + RANDOM * 100000))
fi
if [ ${IFHCOUTOCTETS} -eq 0 ]; then
    IFHCOUTOCTETS=$((800000000 + RANDOM * 80000))
fi

# --- 2. 生成新数值 ---
# 模拟本次采集间隔内的随机流量增量
IN_INCREMENT=$((RANDOM % 5000000 + 1000000)) # 随机增量 1MB - 5MB
OUT_INCREMENT=$((RANDOM % 3000000 + 800000)) # 随机增量 0.8MB - 3MB

CURRENT_IN_OCTETS=$((IFHCINOCTETS + IN_INCREMENT))
CURRENT_OUT_OCTETS=$((IFHCOUTOCTETS + OUT_INCREMENT))
CURRENT_TIMESTAMP=$(date +%s%N) # 使用纳秒级时间戳

# --- 3. 输出两组独立的、扁平化的JSON到标准输出 ---

# 第一组: 模拟 snmp_interface 指标
# 包含描述、索引、错误和丢弃等信息
cat <<JSON1
{
  "name": "snmp_interface",
  "ifInDiscards": 0,
  "ifOutDiscards": 0,
  "ifInErrors": 0,
  "ifOutErrors": 0,
  "agent_id": "dci-agent",
  "agent_ip": "127.0.0.1",
  "device_ip": "************",
  "host": "dci-agent",
  "ifDescr": "10GE1/0/1.6005002",
  "ifIndex": "70",
  "ifPhysAddress": "44:9b:c1:07:8f:52",
  "sysName": "SW1",
  "timestamp": ${CURRENT_TIMESTAMP}
}
JSON1

# 第二组: 模拟 snmp_interfaceX 指标
# 包含接口名称和高精度流量计数器
cat <<JSON2
{
  "name": "snmp_interfaceX",
  "ifHCInOctets": ${CURRENT_IN_OCTETS},
  "ifHCOutOctets": ${CURRENT_OUT_OCTETS},
  "agent_id": "dci-agent",
  "agent_ip": "127.0.0.1",
  "device_ip": "************",
  "host": "dci-agent",
  "ifIndex": "70",
  "ifName": "10GE1/0/1",
  "sysName": "SW1",
  "timestamp": ${CURRENT_TIMESTAMP}
}
JSON2

# --- 4. 保存状态供下次运行使用 ---
# 将本次计算出的最新值写回状态文件
echo "IFHCINOCTETS=${CURRENT_IN_OCTETS}" > "${COUNTER_FILE}"
echo "IFHCOUTOCTETS=${CURRENT_OUT_OCTETS}" >> "${COUNTER_FILE}" 