#!/bin/bash
# Telegraf - Kafka 集成测试脚本

# 输出颜色设置
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 设置工作目录
DCI_ROOT="/Users/<USER>/code/dci/dci-workspace"
SCRIPTS_DIR="${DCI_ROOT}/dci_self_test/scripts/snmp"
RESULTS_DIR="${DCI_ROOT}/dci_self_test/results"
TEST_REPORT="${RESULTS_DIR}/telegraf_kafka_test_report.md"

# 确保目录存在
mkdir -p ${RESULTS_DIR}

# 打印测试信息
echo -e "${GREEN}======================================${NC}"
echo -e "${GREEN}=== Telegraf与Kafka集成测试 ===${NC}"
echo -e "${GREEN}======================================${NC}"
echo

# 步骤1: 检查环境
echo -e "${YELLOW}步骤1: 检查环境${NC}"

# 检查Telegraf
if [ ! -f "${DCI_ROOT}/dci_self_test/bin/telegraf" ]; then
    echo -e "${RED}Error: Telegraf未找到${NC}"
    exit 1
else
    echo -e "${GREEN}✓ Telegraf已找到${NC}"
fi

# 检查Kafka客户端工具
if ! command -v kafka-console-consumer &> /dev/null; then
    echo -e "${YELLOW}警告: kafka-console-consumer命令不可用${NC}"
    echo -e "${YELLOW}某些测试步骤可能会失败${NC}"
else
    echo -e "${GREEN}✓ Kafka客户端工具已找到${NC}"
fi

# 检查网络连接
echo "检查Kafka服务器连接..."
if ping -c 1 -W 2 dcikafka.intra.citic-x.com &> /dev/null; then
    echo -e "${GREEN}✓ Kafka服务器可达${NC}"
else
    echo -e "${YELLOW}警告: 无法ping通Kafka服务器，这可能是正常的，如果服务器禁止了ICMP${NC}"
    # 尝试通过TCP连接测试
    if nc -z -w2 dcikafka.intra.citic-x.com 30002 &> /dev/null; then
        echo -e "${GREEN}✓ Kafka端口30002可连接${NC}"
    else
        echo -e "${RED}× Kafka端口30002无法连接${NC}"
        echo -e "${YELLOW}测试可能会失败，请检查网络连接和Kafka服务状态${NC}"
    fi
fi

echo

# 步骤2: 运行SNMP测试脚本采集数据并发送到Kafka
echo -e "${YELLOW}步骤2: 运行SNMP测试脚本采集数据并发送到Kafka${NC}"
echo "运行snmp_test.sh -r -k"
${SCRIPTS_DIR}/snmp_test.sh -r -k

# 检查snmp_test.sh的执行结果
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: SNMP测试脚本执行失败${NC}"
    exit 1
fi

echo
echo -e "${GREEN}SNMP数据采集完成${NC}"
echo

# 步骤3: 验证数据已成功发送到Kafka
echo -e "${YELLOW}步骤3: 验证数据已成功发送到Kafka${NC}"
echo "运行verify_kafka_data.sh..."
${SCRIPTS_DIR}/verify_kafka_data.sh

# 检查verify_kafka_data.sh的执行结果
VERIFY_RESULT=$?
if [ $VERIFY_RESULT -ne 0 ]; then
    echo -e "${RED}Error: Kafka数据验证失败${NC}"
    TEST_SUCCESS=false
else
    echo -e "${GREEN}Kafka数据验证成功${NC}"
    TEST_SUCCESS=true
fi

echo

# 步骤4: 生成测试报告
echo -e "${YELLOW}步骤4: 生成测试报告${NC}"

cat > ${TEST_REPORT} << EOF
# Telegraf与Kafka集成测试报告

## 测试环境

- 测试时间: $(date)
- Telegraf版本: $(${DCI_ROOT}/dci_self_test/bin/telegraf --version | head -n 1)
- Kafka服务器: dcikafka.intra.citic-x.com:30002
- Kafka主题: dci.monitor.v1.defaultchannel.metrics.telegraf

## 测试结果

**总体结果: $(if $TEST_SUCCESS; then echo "成功 ✅"; else echo "失败 ❌"; fi)**

### 测试步骤

1. **环境检查**
   - Telegraf: ✅ 可用
   - Kafka客户端工具: $(if command -v kafka-console-consumer &> /dev/null; then echo "✅ 可用"; else echo "❌ 不可用"; fi)
   - Kafka服务器连接: $(if nc -z -w2 dcikafka.intra.citic-x.com 30002 &> /dev/null; then echo "✅ 可达"; else echo "❌ 不可达"; fi)

2. **SNMP数据采集**
   - 采集目标: 10.36.46.188, 10.36.46.189 (华为交换机)
   - 采集配置: \`/tmp/telegraf-test-temp.conf\`
   - 采集结果: $(if [ -f "${RESULTS_DIR}/telegraf-snmp-test.out" ] && [ -s "${RESULTS_DIR}/telegraf-snmp-test.out" ]; then echo "✅ 成功"; else echo "❌ 失败"; fi)

3. **Kafka数据验证**
   - 验证脚本: \`verify_kafka_data.sh\`
   - 验证结果: $(if [ $VERIFY_RESULT -eq 0 ]; then echo "✅ 成功"; else echo "❌ 失败"; fi)
   $(if [ -f "${RESULTS_DIR}/kafka-consumer-test.log" ]; then
     if [ -s "${RESULTS_DIR}/kafka-consumer-test.log" ]; then
       echo "   - 成功接收到Kafka消息"
       MSG_COUNT=$(grep -c "}" "${RESULTS_DIR}/kafka-consumer-test.log")
       echo "   - 消息数量: $MSG_COUNT"
     else
       echo "   - 未接收到Kafka消息"
     fi
   else
     echo "   - 未生成验证日志"
   fi)

## 数据分析

$(if [ -f "${RESULTS_DIR}/kafka-consumer-test.log" ] && [ -s "${RESULTS_DIR}/kafka-consumer-test.log" ]; then
  echo "### 消息内容分析"
  
  # 检查是否包含SNMP数据
  if grep -q "\"name\":\"snmp\"" "${RESULTS_DIR}/kafka-consumer-test.log"; then
    echo "- ✅ 包含SNMP数据"
  else
    echo "- ❌ 未发现SNMP数据"
  fi
  
  # 检查设备信息
  if grep -q "\"sysName\"" "${RESULTS_DIR}/kafka-consumer-test.log"; then
    echo "- ✅ 包含设备名称"
    echo "- 设备列表:"
    grep -o "\"sysName\":\"[^\"]*\"" "${RESULTS_DIR}/kafka-consumer-test.log" | sort | uniq | cut -d: -f2 | while read line; do echo "  - $line"; done
  else
    echo "- ❌ 未发现设备名称"
  fi
  
  # 检查关键指标
  echo -e "\n### 关键指标覆盖"
  METRICS=("\"hwEntityCpuUsage\"" "\"hwEntityMemUsage\"" "\"ifHCInOctets\"" "\"ifHCOutOctets\"" "\"lldpRemSysName\"")
  
  for metric in "\${METRICS[@]}"; do
    if grep -q "$metric" "${RESULTS_DIR}/kafka-consumer-test.log"; then
      echo "- ✅ $metric"
    else
      echo "- ❌ $metric (未发现)"
    fi
  done
else
  echo "无法进行数据分析，未收到Kafka消息或验证日志不存在。"
fi)

## 结论

$(if $TEST_SUCCESS; then
  echo "测试成功完成。Telegraf能够正确采集SNMP数据并成功发送到Kafka。系统满足数据流设计要求。"
else
  echo "测试失败。请检查以下可能的问题:"
  echo "1. Telegraf配置是否正确，特别是Kafka输出插件部分"
  echo "2. Kafka服务是否正常运行并可访问"
  echo "3. Kafka主题是否存在"
  echo "4. 网络连接是否畅通"
fi)

## 建议

1. 确保在生产环境中正确配置Kafka安全设置，包括认证和加密
2. 实施Kafka消息监控，确保数据流的连续性
3. 为Telegraf添加重试机制，提高系统稳定性
EOF

echo -e "${GREEN}测试报告已生成: ${TEST_REPORT}${NC}"

# 打印测试结果摘要
echo
echo -e "${GREEN}========= 测试结果摘要 ==========${NC}"
if $TEST_SUCCESS; then
    echo -e "${GREEN}✅ 测试成功: Telegraf能够正确采集SNMP数据并成功发送到Kafka${NC}"
else
    echo -e "${RED}❌ 测试失败: 请查看测试报告了解详情${NC}"
fi
echo -e "${GREEN}==================================${NC}"

# 返回测试结果
if $TEST_SUCCESS; then
    exit 0
else
    exit 1
fi 