# DCI流量数据采集与处理测试

本目录包含用于测试DCI监控系统中流量数据采集和处理功能的测试脚本。该脚本基于《19-DCI-流量类数据接收及存储技术设计.md》的设计要求实现，验证从Telegraf采集的SNMP流量数据通过Kafka传输，并由FlowData模块处理存储至Prometheus的完整数据链路。

## 测试脚本功能

`telegraf_flowdata_test.sh`脚本提供以下功能：

1.  使用Telegraf采集网络设备的流量数据（支持模拟模式和实际SNMP模式）。
2.  通过命令行参数选择将数据发送到生产Kafka主题或测试Kafka主题。
3.  检查FlowData服务是否正在运行。
4.  验证数据采集、传输和处理的每个步骤。
5.  生成详细的测试报告。

## 测试脚本参数

脚本支持以下命令行参数：

- `-r, --real`: 使用实际SNMP设备进行采集。如果未指定，则默认使用脚本生成的模拟数据。
- `-t, --kafka-test`: 将数据发送到测试Kafka主题 (`dci.monitor.vtest...`)。如果未指定，则默认发送到真实的生产主题 (`dci.monitor.v1...`)。
- `-h, --help`: 显示帮助信息。

## 使用示例

### 测试场景组合

通过组合使用 `-r` 和 `-t` 参数，可以覆盖四种核心测试场景：

1.  **模拟数据 -> 生产主题** (默认)
    ```bash
    # 使用 telegraf_flowdata_mock.conf
    ./telegraf_flowdata_test.sh
    ```

2.  **模拟数据 -> 测试主题**
    ```bash
    # 使用 telegraf_flowdata_mock_kafka-test.conf
    ./telegraf_flowdata_test.sh -t
    ```

3.  **真实数据 -> 生产主题**
    ```bash
    # 使用 telegraf_flowdata_real.conf
    ./telegraf_flowdata_test.sh -r
    ```

4.  **真实数据 -> 测试主题**
    ```bash
    # 使用 telegraf_flowdata_real_kafka-test.conf
    ./telegraf_flowdata_test.sh -r -t
    ```

## 数据量控制说明

脚本默认运行 **30 秒**。数据采集量由Telegraf配置文件中的`interval`和脚本中的`RUN_TIME`变量控制。目前不支持通过命令行参数动态调整数据量。

## 测试输出

测试完成后，脚本将生成以下输出：

- 测试日志：`dci_self_test/results/telegraf-flowdata-test.log`
- 采集数据：`dci_self_test/results/telegraf-flowdata-test.out`
- 测试报告：`dci_self_test/results/flowdata_test_results.md`

## 验证项

该测试脚本验证以下关键项目：

1. Telegraf正确采集流量数据（入流量、出流量、错误数和丢弃数）
2. 数据正确格式化为JSON并发送至Kafka
3. FlowData服务正确接收和处理数据
4. 数据正确转换并存储到Prometheus
5. 生成可用于设备端口流量查询的指标

## 依赖组件

- Telegraf（用于数据采集）
- Kafka集群（用于消息传输）
- FlowData服务（用于数据处理）
- MySQL数据库（用于设备端口映射）
- Prometheus（用于指标存储）

## 注意事项

1. 使用实际SNMP模式前，请确保测试环境中有可访问的网络设备
2. 使用Kafka模式前，请确保Kafka集群可用且配置正确
3. 请确保FlowData服务已运行，脚本会在执行前检查服务状态
4. 该测试脚本默认使用模拟数据，因此即使没有实际设备也能测试大部分功能 