# Telegraf测试配置 - 综合SNMP采集(流量+状态)
[agent]
  interval = "50s" # 默认的数据采集间隔
  round_interval = true # 将采集间隔四舍五入到最接近的'interval'，以确保采集时间点的对齐
  metric_batch_size = 1000 # Telegraf向输出插件发送指标的批次大小
  metric_buffer_limit = 10000 # 内存中可缓存的最大指标数，防止内存溢出
  collection_jitter = "0s" # 采集抖动，避免所有agent在同一时间点采集，分散负载
  flush_interval = "2s"    # 将缓冲区的指标推送到输出插件的频率
  flush_jitter = "0s" # 刷新抖动，避免所有agent在同一时间点推送数据
  precision = "" # 收集指标的时间戳精度 (例如: "s", "ms", "us")
  debug = true # 启用调试模式，会输出更详细的日志
  quiet = false # 关闭静默模式，会在标准输出打印成功写入的指标
  logfile = "/Users/<USER>/code/dci/dci-workspace/dci_self_test/results/telegraf-flowdata-test.log"
  hostname = "dci-agent"   # 设置采集代理主机名，在结果中显示为host标签，用于区分不同采集代理
  omit_hostname = false # 是否忽略hostname标签，当 omit_hostname 设置为 false (这是默认值)时，添加 host 标签。

# 全局标签配置 - 适用于所有输入插件
[global_tags]
  agent_id = "dci-agent"    # 采集代理ID
  agent_ip = "127.0.0.1"    # 采集代理IP

[[inputs.snmp]]
  # 设备列表 - 测试环境的交换机
  agents = [
    "udp://************:161",  # 测试交换机1
    "udp://************:161"  # 测试交换机2
  ]
  
  # 基本配置
  timeout = "5s"        # 更短的超时
  retries = 1           # 减少重试次数
  version = 2
  community = "dcilab2025"
  # SNMP设备IP标签
  agent_host_tag = "device_ip"  
  
  # 高级配置
  max_repetitions = 10 # 固定值
  name = "snmp"
  
  # 系统基本信息
  [[inputs.snmp.field]]
    name = "sysName"
    oid = ".*******.*******.0"
    is_tag = true
    
  # === 流量监控部分 ===
  # 接口扩展表信息 - 采集64位高精度计数器
  [[inputs.snmp.table]]
    name = "interfaceX"
    inherit_tags = ["sysName"]
    oid = ".*******.********.1"  # IF-MIB::ifXTable
    
    # 关键：为 interfaceX 添加 ifIndex 标签，确保标签对齐
    [[inputs.snmp.table.field]]
      name = "ifIndex"
      oid = ".*******.*******.1.1" # OID 仍然是 ifTable 的 ifIndex
      is_tag = true

    # 将 ifName 作为普通字段
    [[inputs.snmp.table.field]]
      name = "ifName"
      oid = ".*******.********.1.1.1"
    [[inputs.snmp.table.field]]
      name = "ifHCInOctets"
      oid = ".*******.********.1.1.6"
    [[inputs.snmp.table.field]]
      name = "ifHCOutOctets"
      oid = ".*******.********.1.1.10"

  # === 状态监控部分 ===
  # Huawei Entity Status (CPU and Memory) via HUAWEI-ENTITY-EXT-MIB
  [[inputs.snmp.table]]
    name = "huawei_entity"
    inherit_tags = ["sysName"]
    oid = "*******.2.1.47.1.1.1" # ENTITY-MIB::entPhysicalEntry

    # 实体索引 - 这是必须的，用于关联CPU和内存数据
    [[inputs.snmp.table.field]]
      name = "entPhysicalIndex"
      oid = "*******.2.1.47.1.1.1.1.1"
      is_tag = true

    [[inputs.snmp.table.field]]
      name = "entPhysicalName"
      oid = "*******.2.1.47.1.1.1.1.7"
      is_tag = true

    [[inputs.snmp.table.field]]
      name = "entPhysicalClass"
      oid = "*******.2.1.47.1.1.1.1.5"
      is_tag = true
    
    [[inputs.snmp.table.field]]
      name = "hwEntityCpuUsage"
      oid = "*******.4.1.2011.5.25.31.1.1.1.1.5"

    [[inputs.snmp.table.field]]
      name = "hwEntityMemUsage"
      oid = "*******.4.1.2011.5.25.31.1.1.1.1.7"

  # Interface Status (Standard IF-MIB)
  [[inputs.snmp.table]]
    name = "interface_status"
    inherit_tags = ["sysName"]
    oid = "*******.*******"  # IF-MIB::ifTable

    [[inputs.snmp.table.field]]
      name = "ifName"
      oid = "*******.********.1.1.1"
      is_tag = true

    [[inputs.snmp.table.field]]
      name = "ifAdminStatus"
      oid = "*******.*******.1.7"

    [[inputs.snmp.table.field]]
      name = "ifOperStatus"
      oid = "*******.*******.1.8"

# === 处理器配置 ===
# 处理器1: 重命名流量指标
[[processors.rename]]
  [[processors.rename.replace]]
    measurement = "interfaceX"
    dest = "snmp_HCinterface"

# 处理器2: 确保设备IP标签的兼容性
[[processors.rename]]
  [[processors.rename.replace]]
    tag = "agent_host"
    dest = "device_ip"

# 处理器3: 根据速率(ifHighSpeed)添加速率等级标签
[[processors.enum]]
  [[processors.enum.mapping]]
    field = "ifHighSpeed"
    dest = "interface_speed"
    default = "other"
    [processors.enum.mapping.value_mappings]
      1000 = "1G"
      10000 = "10G"
      25000 = "25G"
      40000 = "40G"
      100000 = "100G"

# 处理器4: 为流量指标添加单位标签
[[processors.override]]
  namepass = ["snmp_HCinterface"]
  [processors.override.tags]
    traffic_unit = "bytes"

# 处理器5: 重命名交换机状态指标
[[processors.rename]]
  [[processors.rename.replace]]
    measurement = "huawei_entity"
    field = "hwEntityCpuUsage"
    dest = "dci_switch_cpu_usage_percent"
  [[processors.rename.replace]]
    measurement = "huawei_entity"
    field = "hwEntityMemUsage"
    dest = "dci_switch_memory_usage_percent"
  [[processors.rename.replace]]
    measurement = "interface_status"
    field = "ifAdminStatus"
    dest = "dci_switch_interface_admin_status"
  [[processors.rename.replace]]
    measurement = "interface_status"
    field = "ifOperStatus"
    dest = "dci_switch_interface_oper_status"

# === 输出配置 ===
# 添加Kafka输出插件 - 用于将数据注入Kafka
[[outputs.kafka]]
  # Kafka Broker连接信息
  brokers = ["dcikafka.citic-x.com:30010", "dcikafka.citic-x.com:30011", "dcikafka.citic-x.com:30012"]
  
  # 主题配置 - 使用正式生产主题
  topic = "dci.monitor.v1.defaultchannel.flows.snmp"
  
  # 消息格式与压缩
  data_format = "json"
  compression_codec = 2  # 2 = snappy
  
  # --- 安全配置 ---
  # 启用 TLS 和 SASL/PLAIN
  sasl_mechanism = "PLAIN"
  sasl_username = "dci-telegraf"
  # 密码与 kafka_server_jaas.conf 中 'user_dci-telegraf' 的值对应
  sasl_password = "PsmeXYWPELMpbrC12D+18LJ83qv9QS+/"
  
  # TLS 配置
  # 使用由generate_certs.sh生成的中间CA证书来进行服务端证书验证
  tls_ca = "/Users/<USER>/code/dci/dci-workspace/prod_env/kafka/scripts/dci-kafka-certs/ca-chain.crt"
  # 客户端也需要提供证书进行双向认证(mTLS)
  tls_cert = "/Users/<USER>/code/dci/dci-workspace/prod_env/kafka/scripts/dci-kafka-certs/dci-telegraf.client.crt"
  tls_key = "/Users/<USER>/code/dci/dci-workspace/prod_env/kafka/scripts/dci-kafka-certs/dci-telegraf.client.key"
  
  # 可靠性设置
  max_retry = 3
  max_message_bytes = 1000000
  
  # 确保包含必要的标签
  [outputs.kafka.tags]
    data_source_type = "snmp_flow"

# 文件输出插件 - 用于保存完整数据
[[outputs.file]]
  files = ["/Users/<USER>/code/dci/dci-workspace/dci_self_test/results/telegraf-flowdata-test-full.out"]
  data_format = "json"
  flush_interval = "2s"
  
# 标准输出插件 - 用于实时查看数据
[[outputs.file]]
  files = ["stdout"]
  data_format = "json" 