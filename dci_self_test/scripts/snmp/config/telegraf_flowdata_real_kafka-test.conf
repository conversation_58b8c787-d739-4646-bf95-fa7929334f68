# Telegraf测试配置 - 实际SNMP采集
[agent]
  interval = "5s" # 默认的数据采集间隔
  round_interval = true # 将采集间隔四舍五入到最接近的'interval'，以确保采集时间点的对齐
  metric_batch_size = 1000 # Telegraf向输出插件发送指标的批次大小
  metric_buffer_limit = 10000 # 内存中可缓存的最大指标数，防止内存溢出
  collection_jitter = "0s" # 采集抖动，避免所有agent在同一时间点采集，分散负载
  flush_interval = "2s"    # 将缓冲区的指标推送到输出插件的频率
  flush_jitter = "0s" # 刷新抖动，避免所有agent在同一时间点推送数据
  precision = "" # 收集指标的时间戳精度 (例如: "s", "ms", "us")
  debug = true # 启用调试模式，会输出更详细的日志
  quiet = false # 关闭静默模式，会在标准输出打印成功写入的指标
  logfile = "/Users/<USER>/code/dci/dci-workspace/dci_self_test/results/telegraf-flowdata-test.log"
  hostname = "dci-agent"   # 设置采集代理主机名，在结果中显示为host标签，用于区分不同采集代理
  omit_hostname = false # 是否忽略hostname标签，当 omit_hostname 设置为 false (这是默认值)时，添加 host 标签。

# 全局标签配置 - 适用于所有输入插件
# 这是一个独立的配置块，在这里定义的标签会附加到所有由这个 Telegraf 实例采集的指标上。
# 这是设置全局上下文（如数据中心位置、环境名）的最佳位置。
[global_tags]
  agent_id = "dci-agent"    # 采集代理ID
  agent_ip = "127.0.0.1"    # 采集代理IP

# 整个流程是：
# Telegraf 根据 [agent] 和 [global_tags] 设置好基础信息。
# inputs.snmp 插件连接到 ************。
# 它首先获取了设备的 sysName 并将其设为后续所有指标的标签。
# 然后，它开始遍历（WALK）ifTable（OID: .*******.*******）。
# 当遍历到 ifIndex 为 70 的这一行时，它创建了一个 name 为 interface 的指标。
# 它将 ifIndex 和 ifDescr 的值作为该指标的特定标签。
# 它采集了这一行所有的列（ifAdminStatus, ifMtu, ifInOctets...）作为该指标的字段。
# 最后，将所有标签、字段和时间戳组合成一个 JSON 对象输出。
[[inputs.snmp]]
  # 设备列表 - 测试环境的交换机
  agents = [
    "udp://************:161"  # 测试交换机1
    # "udp://************:161"  # 测试交换机2
  ]
  
  # 基本配置
  timeout = "5s"        # 更短的超时
  retries = 1           # 减少重试次数
  version = 2
  community = "dcilab2025"
  # SNMP设备IP标签，elegraf 从 agents 列表中获取设备 IP，并根据 agent_host_tag 的设置将其命名为 device_ip
  agent_host_tag = "device_ip"  
  
  # 使用全局标签，而不是SNMP输入插件特定标签
  
  # 高级配置
  max_repetitions = 10 # 固定值
  name = "snmp"
  
  # 系统基本信息
  [[inputs.snmp.field]]
    name = "sysName"
    oid = ".*******.*******.0"
    is_tag = true
    
  # 接口表信息 - 暂时屏蔽，未来需要时再启用
   # ifTable 字段
    # fields 对象其实是 Telegraf 对设备上 ifIndex 为 70 的这个接口，
    # 在 IF-MIB::ifTable 中能采集到的所有列的值的集合。
    # 我们将 ifIndex 作为唯一的接口标识标签
    # 这是为了与 interfaceX 表进行精确匹配
  # [[inputs.snmp.table]]
  #   name = "interface"
  #   inherit_tags = ["sysName"]
  #   oid = ".*******.*******"  # IF-MIB::ifTable
  #   
  #   # 使用 ifIndex 作为唯一的接口标识标签
  #   [[inputs.snmp.table.field]]
  #     name = "ifIndex"
  #     oid = ".*******.*******.1.1"
  #     is_tag = true
  #     
  #   # 采集需要的字段
  #   [[inputs.snmp.table.field]]
  #     name = "ifDescr"
  #     oid = ".*******.*******.1.2"
  #   [[inputs.snmp.table.field]]
  #     name = "ifInDiscards"
  #     oid = ".*******.*******.1.13"
  #   [[inputs.snmp.table.field]]
  #     name = "ifOutDiscards"
  #     oid = ".*******.*******.1.19"
  #   [[inputs.snmp.table.field]]
  #     name = "ifInErrors"
  #     oid = ".*******.*******.1.14"
  #   [[inputs.snmp.table.field]]
  #     name = "ifOutErrors"
  #     oid = ".*******.*******.1.20"
  
  # 接口扩展表信息 - 采集64位高精度计数器
  [[inputs.snmp.table]]
    name = "interfaceX"
    inherit_tags = ["sysName"]
    oid = ".*******.********.1"  # IF-MIB::ifXTable
    
    # 关键：为 interfaceX 也添加 ifIndex 标签，确保与 interface 表的标签完全对齐
    [[inputs.snmp.table.field]]
      name = "ifIndex"
      oid = ".*******.*******.1.1" # OID 仍然是 ifTable 的 ifIndex
      is_tag = true

    # 将 ifName 作为普通字段
    [[inputs.snmp.table.field]]
      name = "ifName"
      oid = ".*******.********.1.1.1"
    [[inputs.snmp.table.field]]
      name = "ifHCInOctets"
      oid = ".*******.********.1.1.6"
    [[inputs.snmp.table.field]]
      name = "ifHCOutOctets"
      oid = ".*******.********.1.1.10"

# # --- 聚合器与处理器链 ---
# # 聚合器1: 合并功能已屏蔽
# [[aggregators.merge]]
#   period = "5s"
#   drop_original = true

# 处理器1: 重命名指标
# 将采集的 "interfaceX" measurement 重命名为 "snmp_HCinterface"
[[processors.rename]]
  [[processors.rename.replace]]
    measurement = "interfaceX"
    dest = "snmp_HCinterface"

# 处理器2: 确保设备IP标签的兼容性
[[processors.rename]]
  [[processors.rename.replace]]
    tag = "agent_host"
    dest = "device_ip"

# 处理器3: 根据速率(ifHighSpeed)添加速率等级标签 (interface_speed)
# 该处理器会读取 ifHighSpeed 字段的值，并根据下面的映射关系，创建一个名为 interface_speed 的新标签。
[[processors.enum]]
  [[processors.enum.mapping]]
    # 源字段：ifHighSpeed 的值是速率，单位 Mbps
    field = "ifHighSpeed"
    # 目标标签：新创建的标签名
    dest = "interface_speed"
    # 默认值，如果速率不匹配任何已知值
    default = "other"
    # 值映射关系
    [processors.enum.mapping.value_mappings]
      1000 = "1G"
      10000 = "10G"
      25000 = "25G"
      40000 = "40G"
      100000 = "100G"

# 处理器4: 为流量指标添加单位标签
# 为所有名为 "snmp_HCinterface" 的指标添加一个固定的标签 `traffic_unit: "bytes"`
[[processors.override]]
  namepass = ["snmp_HCinterface"]
  [processors.override.tags]
    traffic_unit = "bytes"

# 添加Kafka输出插件 - 用于将数据注入Kafka
[[outputs.kafka]]
  # Kafka Broker连接信息
  brokers = ["dcikafka.intra.citic-x.com:30010", "dcikafka.intra.citic-x.com:30011", "dcikafka.intra.citic-x.com:30012"]
  
  # 主题配置 - 使用设计文档中指定的主题
  topic = "dci.monitor.vtest.defaultchannel.flows.snmp"
  
  # 消息格式与压缩
  data_format = "json"
  compression_codec = 2  # 2 = snappy
  
  # --- 安全配置 ---
  # 启用 TLS 和 SASL/PLAIN
  sasl_mechanism = "PLAIN"
  sasl_username = "dci-telegraf"
  # 密码与 kafka_server_jaas.conf 中 'user_dci-telegraf' 的值对应
  sasl_password = "telegraf-secret"
  
  # TLS 配置
  # 使用由generate_certs.sh生成的中间CA证书来进行服务端证书验证
  tls_ca = "/Users/<USER>/code/dci/dci-workspace/dci-monitor/kafka/scripts/dci-kafka-certs/intermediate-ca.crt"
  # 客户端也需要提供证书进行双向认证(mTLS)
  tls_cert = "/Users/<USER>/code/dci/dci-workspace/dci-monitor/kafka/scripts/dci-kafka-certs/dci-telegraf.client.crt"
  tls_key = "/Users/<USER>/code/dci/dci-workspace/dci-monitor/kafka/scripts/dci-kafka-certs/dci-telegraf.client.key"
  
  # 可靠性设置
  max_retry = 3
  max_message_bytes = 1000000
  
  # 确保包含必要的标签
  [outputs.kafka.tags]
    data_source_type = "snmp_flow"
# 文件输出插件 - 用于保存完整数据
[[outputs.file]]
  files = ["/Users/<USER>/code/dci/dci-workspace/dci_self_test/results/telegraf-flowdata-test-full.out"]
  data_format = "json"
  flush_interval = "2s"
  
# 标准输出插件 - 用于实时查看数据
[[outputs.file]]
  files = ["stdout"]
  data_format = "json"