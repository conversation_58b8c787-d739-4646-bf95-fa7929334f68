# Telegraf测试配置 - 模拟模式
[agent]
  interval = "10s"
  round_interval = true
  metric_batch_size = 1000
  metric_buffer_limit = 10000
  collection_jitter = "0s"
  flush_interval = "2s"
  flush_jitter = "0s"
  precision = ""
  debug = true
  quiet = false
  logfile = "/Users/<USER>/code/dci/dci-workspace/dci_self_test/results/telegraf-flowdata-test.log"
  hostname = "dci-agent"
  omit_hostname = false

# 全局标签配置
[global_tags]
  agent_id = "dci-agent"
  agent_ip = "127.0.0.1"
  
# 调用外部脚本生成模拟数据
[[inputs.exec]]
  commands = ["/bin/bash /Users/<USER>/code/dci/dci-workspace/dci_self_test/scripts/snmp/generate_flow_data.sh"]
  timeout = "5s"
  data_format = "json"
  # 明确告知JSON解析器哪些字段是标签
  tag_keys = [
    "agent_id", "agent_ip", "device_ip", "host", "ifName", "sysName","ifIndex", "ifDescr", "ifPhysAddress"
  ]
  # 使用 "name" 字段来动态设置指标名称 (e.g., "snmp_interface", "snmp_interfaceX")
  json_name_key = "name"

# 处理器: 标准化字段名和标签
# 注意：此处理器对所有通过此inputs插件的数据生效
[[processors.rename]]
  [[processors.rename.replace]]
    field = "fields_ifHCInOctets"
    dest = "ifHCInOctets"
  [[processors.rename.replace]]
    field = "fields_ifHCOutOctets"
    dest = "ifHCOutOctets"
  [[processors.rename.replace]]
    field = "fields_ifInDiscards"
    dest = "ifInDiscards"
  [[processors.rename.replace]]
    field = "fields_ifOutDiscards"
    dest = "ifOutDiscards"
  [[processors.rename.replace]]
    field = "fields_ifInErrors"
    dest = "ifInErrors"
  [[processors.rename.replace]]
    field = "fields_ifOutErrors"
    dest = "ifOutErrors"
  # 确保任何名为 'agent_host' 的标签都被标准化为 'device_ip'
  # 这提供了对不同输入插件行为的兼容性
  [[processors.rename.replace]]
    tag = "agent_host"
    dest = "device_ip"

# 添加Kafka输出插件 - 用于将数据注入Kafka
[[outputs.kafka]]
  # Kafka Broker连接信息 (use the load-balanced addresses)
  brokers = ["dcikafka.intra.citic-x.com:30010", "dcikafka.intra.citic-x.com:30011", "dcikafka.intra.citic-x.com:30012"]
  
  # 主题配置 - 使用设计文档中指定的主题
  topic = "dci.monitor.v1.defaultchannel.flows.snmp"
  
  # 消息格式与压缩
  data_format = "json"
  compression_codec = 2  # 2 = snappy

  # --- 安全配置 ---
  # 启用 TLS 和 SASL/PLAIN
  sasl_mechanism = "PLAIN"
  sasl_username = "dci-telegraf"
  # 密码与 kafka_server_jaas.conf 中 'user_dci-telegraf' 的值对应
  sasl_password = "telegraf-secret" 
  
  # TLS 配置
  # 使用由generate_certs.sh生成的中间CA证书来进行服务端证书验证
  tls_ca = "/Users/<USER>/code/dci/dci-workspace/dci-monitor/kafka/scripts/dci-kafka-certs/intermediate-ca.crt"
  # 客户端也需要提供证书进行双向认证(mTLS)
  tls_cert = "/Users/<USER>/code/dci/dci-workspace/dci-monitor/kafka/scripts/dci-kafka-certs/dci-telegraf.client.crt"
  tls_key = "/Users/<USER>/code/dci/dci-workspace/dci-monitor/kafka/scripts/dci-kafka-certs/dci-telegraf.client.key"

  # Reliability settings
  max_retry = 3
  max_message_bytes = 1000000
  
  # 确保包含必要的标签
  tags = { data_source_type = "snmp_flow" }

# 文件输出插件 - 用于保存完整数据
[[outputs.file]]
  files = ["/Users/<USER>/code/dci/dci-workspace/dci_self_test/results/telegraf-flowdata-test-full.out"]
  data_format = "json"
  flush_interval = "2s"
  
# 标准输出插件 - 用于实时查看数据
[[outputs.file]]
  files = ["stdout"]
  data_format = "json"