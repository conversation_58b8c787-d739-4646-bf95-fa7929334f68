#!/bin/bash
# Kafka数据验证工具 - 用于验证Telegraf SNMP数据是否成功写入Kafka

# 输出颜色设置
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 设置工作目录
DCI_ROOT="/Users/<USER>/code/dci/dci-workspace"
RESULT_DIR="${DCI_ROOT}/dci_self_test/results"
KAFKA_LOG="${RESULT_DIR}/01-snmp-kafka-consumer-test-full.log"

# 确保结果目录存在
mkdir -p ${RESULT_DIR}

# 清理之前的日志
rm -f ${KAFKA_LOG}

# 打印测试信息
echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}=== Telegraf到Kafka数据流验证测试(完整版) ====${NC}"
echo -e "${GREEN}========================================${NC}"
echo

# Kafka配置
KAFKA_BROKER="dcikafka.intra.citic-x.com:30002"
KAFKA_TOPIC="dci.monitor.v1.defaultchannel.metrics.telegraf"
CONSUMER_GROUP="dci-test-consumer-$(date +%s)"

# 检查Kafka服务器连接
echo "检查Kafka服务器连接状态..."
if nc -z -w 5 dcikafka.intra.citic-x.com 30002 2>/dev/null; then
    echo -e "${GREEN}✓ Kafka服务器可达${NC}"
else
    echo -e "${RED}× Kafka服务器不可达 (dcikafka.intra.citic-x.com:30002)${NC}"
    echo -e "${YELLOW}提示: 请检查Kafka服务器是否运行，网络连接是否正常${NC}"
    echo "测试失败，无法连接到Kafka服务器"
    exit 1
fi

# 检查端口连通性
echo "检查Kafka端口连通性..."
if curl -s telnet://dcikafka.intra.citic-x.com:30002 -m 5 >/dev/null 2>&1; then
    echo -e "${GREEN}✓ Kafka服务端口正常${NC}"
else
    echo -e "${YELLOW}警告: 无法使用curl telnet进行Kafka端口测试${NC}"
    echo "继续检查主题信息..."
fi

# 如果有kafkacat，使用它来检查Kafka主题
echo "检查Kafka主题 ${KAFKA_TOPIC} 是否存在..."
if command -v kafkacat &> /dev/null; then
    TOPICS=$(kafkacat -b ${KAFKA_BROKER} -L 2>/dev/null | grep "${KAFKA_TOPIC}")
    if [ -n "$TOPICS" ]; then
        echo -e "${GREEN}✓ Kafka主题 ${KAFKA_TOPIC} 存在${NC}"
    else
        echo -e "${YELLOW}警告: 未找到主题 ${KAFKA_TOPIC}${NC}"
    fi
elif command -v kcat &> /dev/null; then
    TOPICS=$(kcat -b ${KAFKA_BROKER} -L 2>/dev/null | grep "${KAFKA_TOPIC}")
    if [ -n "$TOPICS" ]; then
        echo -e "${GREEN}✓ Kafka主题 ${KAFKA_TOPIC} 存在${NC}"
    else
        echo -e "${YELLOW}警告: 未找到主题 ${KAFKA_TOPIC}${NC}"
    fi
else
    echo -e "${YELLOW}警告: 未找到kafkacat/kcat工具，无法检查主题信息${NC}"
fi

# 检查Kafka数据
echo "尝试从Kafka主题 ${KAFKA_TOPIC} 读取数据(最多1000条消息)..."

# 使用kafka-console-consumer尝试读取数据
if command -v kafka-console-consumer &> /dev/null; then
    echo "使用kafka-console-consumer监听Kafka消息，将运行30秒..."
    timeout 30 kafka-console-consumer --bootstrap-server ${KAFKA_BROKER} \
      --topic ${KAFKA_TOPIC} \
      --from-beginning \
      --max-messages 1000 > ${KAFKA_LOG} 2>&1
    
    if [ $? -eq 124 ]; then
        # Timeout正常退出
        if [ -s "${KAFKA_LOG}" ]; then
            MSGS_COUNT=$(grep -c "fields" ${KAFKA_LOG})
            echo -e "${GREEN}✓ 成功从Kafka接收数据：共${MSGS_COUNT}条消息${NC}"
            echo "消息统计信息:"
            
            # 统计不同类型的消息
            SNMP_MSGS=$(grep -c "\"name\":\"snmp\"" ${KAFKA_LOG})
            INTERFACE_MSGS=$(grep -c "\"name\":\"interface\"" ${KAFKA_LOG})
            INTERFACEX_MSGS=$(grep -c "\"name\":\"interfaceX\"" ${KAFKA_LOG})
            
            echo "- SNMP基本信息消息: ${SNMP_MSGS}条"
            echo "- 接口基本信息消息: ${INTERFACE_MSGS}条"
            echo "- 接口扩展信息消息: ${INTERFACEX_MSGS}条"
            
            # 统计设备信息
            echo -e "\n设备统计:"
            grep -o "\"sysName\":\"[^\"]*\"" ${KAFKA_LOG} 2>/dev/null | sort | uniq -c | while read count name; do
                device=$(echo $name | cut -d '"' -f 4)
                echo "- $device: $count 条消息"
            done
        else
            echo -e "${YELLOW}警告: 30秒内未收到Kafka数据${NC}"
            echo "这可能是因为:"
            echo "1. Telegraf未将数据发送到Kafka"
            echo "2. Kafka主题名称不正确"
            echo "3. Kafka消费者配置不正确"
        fi
    else
        echo -e "${RED}× Kafka消费者执行失败${NC}"
        cat ${KAFKA_LOG}
    fi
elif command -v kafkacat &> /dev/null; then
    echo "使用kafkacat监听Kafka消息，将运行30秒..."
    timeout 30 kafkacat -b ${KAFKA_BROKER} -t ${KAFKA_TOPIC} -C -o beginning -c 1000 > ${KAFKA_LOG} 2>&1
    
    if [ -s "${KAFKA_LOG}" ]; then
        MSGS_COUNT=$(grep -c "fields" ${KAFKA_LOG})
        echo -e "${GREEN}✓ 成功从Kafka接收数据：共${MSGS_COUNT}条消息${NC}"
        echo "消息统计信息:"
        
        # 统计不同类型的消息
        SNMP_MSGS=$(grep -c "\"name\":\"snmp\"" ${KAFKA_LOG})
        INTERFACE_MSGS=$(grep -c "\"name\":\"interface\"" ${KAFKA_LOG})
        INTERFACEX_MSGS=$(grep -c "\"name\":\"interfaceX\"" ${KAFKA_LOG})
        
        echo "- SNMP基本信息消息: ${SNMP_MSGS}条"
        echo "- 接口基本信息消息: ${INTERFACE_MSGS}条"
        echo "- 接口扩展信息消息: ${INTERFACEX_MSGS}条"
        
        # 统计设备信息
        echo -e "\n设备统计:"
        grep -o "\"sysName\":\"[^\"]*\"" ${KAFKA_LOG} 2>/dev/null | sort | uniq -c | while read count name; do
            device=$(echo $name | cut -d '"' -f 4)
            echo "- $device: $count 条消息"
        done
    else
        echo -e "${YELLOW}警告: 未从Kafka接收到数据${NC}"
    fi
elif command -v kcat &> /dev/null; then
    echo "使用kcat监听Kafka消息，将运行30秒..."
    timeout 30 kcat -b ${KAFKA_BROKER} -t ${KAFKA_TOPIC} -C -o beginning -c 1000 > ${KAFKA_LOG} 2>&1
    
    if [ -s "${KAFKA_LOG}" ]; then
        MSGS_COUNT=$(grep -c "fields" ${KAFKA_LOG})
        echo -e "${GREEN}✓ 成功从Kafka接收数据：共${MSGS_COUNT}条消息${NC}"
        echo "消息统计信息:"
        
        # 统计不同类型的消息
        SNMP_MSGS=$(grep -c "\"name\":\"snmp\"" ${KAFKA_LOG})
        INTERFACE_MSGS=$(grep -c "\"name\":\"interface\"" ${KAFKA_LOG})
        INTERFACEX_MSGS=$(grep -c "\"name\":\"interfaceX\"" ${KAFKA_LOG})
        
        echo "- SNMP基本信息消息: ${SNMP_MSGS}条"
        echo "- 接口基本信息消息: ${INTERFACE_MSGS}条"
        echo "- 接口扩展信息消息: ${INTERFACEX_MSGS}条"
        
        # 统计设备信息
        echo -e "\n设备统计:"
        grep -o "\"sysName\":\"[^\"]*\"" ${KAFKA_LOG} 2>/dev/null | sort | uniq -c | while read count name; do
            device=$(echo $name | cut -d '"' -f 4)
            echo "- $device: $count 条消息"
        done
    else
        echo -e "${YELLOW}警告: 未从Kafka接收到数据${NC}"
    fi
else
    echo -e "${YELLOW}警告: 未安装Kafka客户端工具，无法验证数据流${NC}"
    echo "请使用以下命令安装Kafka客户端工具之一:"
    echo "  - brew install kafka (kafka-console-consumer)"
    echo "  - brew install kafkacat (kafkacat/kcat)"
    
    # 尝试用nc或telnet命令测试连接
    echo "尝试使用netcat检测Kafka端口连通性..."
    if nc -z -w 5 dcikafka.intra.citic-x.com 30002 2>/dev/null; then
        echo -e "${GREEN}✓ Kafka服务器端口开放${NC}"
    else
        echo -e "${RED}× Kafka服务器端口不可达${NC}"
    fi
fi

# 显示部分消息示例
if [ -s "${KAFKA_LOG}" ]; then
    echo -e "\n前5条消息示例:"
    head -n 5 ${KAFKA_LOG}
    
    echo -e "\n总消息行数: $(wc -l < ${KAFKA_LOG})"
fi

echo
echo "完整测试报告保存在: ${KAFKA_LOG}"
echo -e "${GREEN}========================================${NC}"
echo "数据验证测试完成" 