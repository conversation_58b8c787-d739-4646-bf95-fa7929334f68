#!/bin/bash
# SNMP采集结果分析和中文注释工具
# 支持JSON格式的Telegraf输出文件解析

# 输出颜色设置
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 设置工作目录
DCI_ROOT="/Users/<USER>/code/dci/dci-workspace"
INPUT_FILE="${DCI_ROOT}/dci_self_test/results/telegraf-snmp-test.out"
OUTPUT_FILE="${DCI_ROOT}/dci_self_test/results/snmp_results_annotated.txt"

# 检查原始结果文件是否存在
if [ ! -f "${INPUT_FILE}" ]; then
    echo -e "${RED}错误: SNMP采集结果文件未找到: ${INPUT_FILE}${NC}"
    exit 1
fi

# 创建结果目录（如果不存在）
mkdir -p "${DCI_ROOT}/dci_self_test/results"

# 定义指标中文映射函数
get_metric_desc() {
    case "$1" in
        "sysName") echo "设备名称" ;;
        "sysDescr") echo "系统描述" ;;
        "sysUpTime") echo "系统运行时间" ;;
        "sysLocation") echo "设备位置" ;;
        "hwEntityCpuUsage") echo "CPU使用率(%)" ;;
        "hwEntityMemUsage") echo "内存使用率(%)" ;;
        "hwEntityTemperature") echo "温度(℃)" ;;
        "ifIndex") echo "接口索引" ;;
        "ifDescr") echo "接口描述" ;;
        "ifType") echo "接口类型" ;;
        "ifAdminStatus") echo "管理状态(1=开启,2=关闭)" ;;
        "ifOperStatus") echo "运行状态(1=开启,2=关闭)" ;;
        "ifHCInOctets") echo "入方向字节数" ;;
        "ifHCOutOctets") echo "出方向字节数" ;;
        "ifHCInUcastPkts") echo "入方向单播包数" ;;
        "ifHCOutUcastPkts") echo "出方向单播包数" ;;
        "ifHCInMulticastPkts") echo "入方向组播包数" ;;
        "ifHCOutMulticastPkts") echo "出方向组播包数" ;;
        "ifHCInBroadcastPkts") echo "入方向广播包数" ;;
        "ifHCOutBroadcastPkts") echo "出方向广播包数" ;;
        "ifHighSpeed") echo "接口速率(Mbps)" ;;
        "ifPromiscuousMode") echo "混杂模式(1=开启,2=关闭)" ;;
        "ifConnectorPresent") echo "接口连接状态(1=有,2=无)" ;;
        "ifAlias") echo "接口别名" ;;
        "lldpRemIndex") echo "LLDP远端索引" ;;
        "lldpRemSysName") echo "远端设备名称" ;;
        "lldpRemPortId") echo "远端端口ID" ;;
        "lldpRemChassisIdSubtype") echo "远端机箱ID类型" ;;
        "lldpRemChassisId") echo "远端机箱ID" ;;
        "lldpRemPortIdSubtype") echo "远端端口ID类型" ;;
        "lldpRemPortDesc") echo "远端端口描述" ;;
        "lldpRemSysDesc") echo "远端系统描述" ;;
        "lldpLocPortId") echo "本地端口ID" ;;
        "lldpLocPortDesc") echo "本地端口描述" ;;
        *) echo "$1" ;;
    esac
}

# 定义度量值处理函数
format_value() {
    local metric_name="$1"
    local value="$2"
    
    # 去除末尾的i（整数标记）
    value=${value%i}
    
    case "$metric_name" in
        "ifAdminStatus"|"ifOperStatus")
            case "$value" in
                "1") echo "开启(1)" ;;
                "2") echo "关闭(2)" ;;
                "3") echo "测试(3)" ;;
                "4") echo "未知(4)" ;;
                "5") echo "休眠(5)" ;;
                "6") echo "未激活(6)" ;;
                "7") echo "下层关闭(7)" ;;
                *) echo "$value" ;;
            esac
            ;;
        "ifPromiscuousMode")
            case "$value" in
                "1") echo "开启(1)" ;;
                "2") echo "关闭(2)" ;;
                *) echo "$value" ;;
            esac
            ;;
        "ifConnectorPresent")
            case "$value" in
                "1") echo "有物理连接(1)" ;;
                "2") echo "无物理连接(2)" ;;
                *) echo "$value" ;;
            esac
            ;;
        "ifType")
            case "$value" in
                "1") echo "其他(1)" ;;
                "6") echo "以太网(6)" ;;
                "24") echo "软件环回(24)" ;;
                "131") echo "隧道(131)" ;;
                *) echo "$value" ;;
            esac
            ;;
        "lldpRemChassisIdSubtype")
            case "$value" in
                "1") echo "机箱组件(1)" ;;
                "2") echo "接口别名(2)" ;;
                "3") echo "端口组件(3)" ;;
                "4") echo "MAC地址(4)" ;;
                "5") echo "网络地址(5)" ;;
                "6") echo "接口名称(6)" ;;
                "7") echo "本地分配(7)" ;;
                *) echo "$value" ;;
            esac
            ;;
        "lldpRemPortIdSubtype")
            case "$value" in
                "1") echo "接口别名(1)" ;;
                "2") echo "端口组件(2)" ;;
                "3") echo "MAC地址(3)" ;;
                "4") echo "网络地址(4)" ;;
                "5") echo "接口名称(5)" ;;
                "6") echo "代理电路ID(6)" ;;
                "7") echo "本地分配(7)" ;;
                *) echo "$value" ;;
            esac
            ;;
        "ifHighSpeed")
            echo "${value}Mbps"
            ;;
        "ifHCInOctets"|"ifHCOutOctets")
            if [ "$value" -gt 1048576 ]; then
                echo "$(echo "scale=2; $value/1048576" | bc)MB"
            elif [ "$value" -gt 1024 ]; then
                echo "$(echo "scale=2; $value/1024" | bc)KB"
            else
                echo "${value}B"
            fi
            ;;
        *)
            echo "$value"
            ;;
    esac
}

# 开始处理
echo "开始处理SNMP采集结果..."
echo "原始文件: ${INPUT_FILE}"
echo "注释输出: ${OUTPUT_FILE}"

# 检查文件格式
if head -n 1 "${INPUT_FILE}" | grep -q '{"fields"'; then
    echo "检测到JSON格式的Telegraf输出文件，将进行相应解析"
    FORMAT="json"
else
    echo "检测到Influx Line Protocol格式的Telegraf输出文件，将进行相应解析"
    FORMAT="line"
fi

# 写入标题
cat > ${OUTPUT_FILE} << EOF
# SNMP采集结果（带中文注释）

以下是SNMP采集结果的中文注释版本，方便理解各个指标的含义。

## 采集数据分析
EOF

# 如果是JSON格式，则进行JSON格式处理
if [ "$FORMAT" = "json" ]; then
    echo -e "\n### SNMP设备信息\n" >> ${OUTPUT_FILE}
    echo "| 设备IP | 设备名称 | CPU使用率 | 内存使用率 | 温度 |" >> ${OUTPUT_FILE}
    echo "|--------|----------|----------|------------|------|" >> ${OUTPUT_FILE}
    
    # 提取所有包含SNMP相关字段的JSON记录
    grep -e '"name":"snmp"' "${INPUT_FILE}" | while read -r line; do
        agent_host=$(echo "$line" | grep -o '"agent_host":"[^"]*"' | cut -d: -f2 | tr -d '"')
        sys_name=$(echo "$line" | grep -o '"sysName":"[^"]*"' | cut -d: -f2 | tr -d '"')
        cpu_usage=$(echo "$line" | grep -o '"hwEntityCpuUsage":[0-9.]*' | cut -d: -f2)
        mem_usage=$(echo "$line" | grep -o '"hwEntityMemUsage":[0-9.]*' | cut -d: -f2)
        temperature=$(echo "$line" | grep -o '"hwEntityTemperature":[0-9.]*' | cut -d: -f2)
        
        if [ -n "$agent_host" ]; then
            echo "| $agent_host | ${sys_name:-未获取} | ${cpu_usage:-N/A}% | ${mem_usage:-N/A}% | ${temperature:-N/A}℃ |" >> ${OUTPUT_FILE}
        fi
    done
    
    # 提取接口信息
    echo -e "\n### 接口流量统计\n" >> ${OUTPUT_FILE}
    echo "| 设备IP | 设备名称 | 接口名称 | 接口状态 | 入向流量 | 出向流量 | 接口速率 |" >> ${OUTPUT_FILE}
    echo "|--------|----------|----------|----------|----------|----------|----------|" >> ${OUTPUT_FILE}
    
    grep -e '"name":"interface"' -e '"name":"interfaceX"' "${INPUT_FILE}" | while read -r line; do
        agent_host=$(echo "$line" | grep -o '"agent_host":"[^"]*"' | cut -d: -f2 | tr -d '"')
        sys_name=$(echo "$line" | grep -o '"sysName":"[^"]*"' | cut -d: -f2 | tr -d '"')
        if_name=$(echo "$line" | grep -o '"ifName":"[^"]*"' | cut -d: -f2 | tr -d '"')
        if_descr=$(echo "$line" | grep -o '"ifDescr":"[^"]*"' | cut -d: -f2 | tr -d '"')
        if_status=$(echo "$line" | grep -o '"ifOperStatus":[0-9.]*' | cut -d: -f2)
        in_octets=$(echo "$line" | grep -o '"ifHCInOctets":[0-9.]*' | cut -d: -f2)
        out_octets=$(echo "$line" | grep -o '"ifHCOutOctets":[0-9.]*' | cut -d: -f2)
        if_speed=$(echo "$line" | grep -o '"ifHighSpeed":[0-9.]*' | cut -d: -f2)
        
        # 格式化接口状态
        case "$if_status" in
            "1") if_status_str="开启(1)" ;;
            "2") if_status_str="关闭(2)" ;;
            *) if_status_str="${if_status:-未知}" ;;
        esac
        
        # 格式化流量
        if [ -n "$in_octets" ]; then
            if [ "$in_octets" -gt 1048576 ]; then
                in_octets_fmt="$(echo "scale=2; $in_octets/1048576" | bc)MB"
            elif [ "$in_octets" -gt 1024 ]; then
                in_octets_fmt="$(echo "scale=2; $in_octets/1024" | bc)KB"
            else
                in_octets_fmt="${in_octets}B"
            fi
        else
            in_octets_fmt="0B"
        fi
        
        if [ -n "$out_octets" ]; then
            if [ "$out_octets" -gt 1048576 ]; then
                out_octets_fmt="$(echo "scale=2; $out_octets/1048576" | bc)MB"
            elif [ "$out_octets" -gt 1024 ]; then
                out_octets_fmt="$(echo "scale=2; $out_octets/1024" | bc)KB"
            else
                out_octets_fmt="${out_octets}B"
            fi
        else
            out_octets_fmt="0B"
        fi
        
        # 使用ifName，如果不存在则使用ifDescr
        display_name="${if_name:-${if_descr:-未命名}}"
        
        if [ -n "$agent_host" ] && [ -n "$display_name" ]; then
            echo "| $agent_host | ${sys_name:-未获取} | $display_name | $if_status_str | $in_octets_fmt | $out_octets_fmt | ${if_speed:-N/A}Mbps |" >> ${OUTPUT_FILE}
        fi
    done
    
    # 增加: 提取LLDP邻居信息（JSON格式）
    echo -e "\n### LLDP邻居发现信息\n" >> ${OUTPUT_FILE}
    echo "| 设备IP | 本地设备名称 | 本地接口 | 远端设备名称 | 远端接口 | 远端描述 |" >> ${OUTPUT_FILE}
    echo "|--------|------------|----------|-------------|----------|----------|" >> ${OUTPUT_FILE}
    
    lldp_found=false
    
    # 检查标准LLDP表数据
    if grep -q '"name":"lldp"' "${INPUT_FILE}"; then
        grep -e '"name":"lldp"' "${INPUT_FILE}" | while read -r line; do
            agent_host=$(echo "$line" | grep -o '"agent_host":"[^"]*"' | cut -d: -f2 | tr -d '"')
            sys_name=$(echo "$line" | grep -o '"sysName":"[^"]*"' | cut -d: -f2 | tr -d '"')
            
            # LLDP特有字段
            loc_port_id=$(echo "$line" | grep -o '"lldpLocPortId":"[^"]*"' | cut -d: -f2 | tr -d '"')
            loc_port_desc=$(echo "$line" | grep -o '"lldpLocPortDesc":"[^"]*"' | cut -d: -f2 | tr -d '"')
            rem_sys_name=$(echo "$line" | grep -o '"lldpRemSysName":"[^"]*"' | cut -d: -f2 | tr -d '"')
            rem_port_id=$(echo "$line" | grep -o '"lldpRemPortId":"[^"]*"' | cut -d: -f2 | tr -d '"')
            rem_port_desc=$(echo "$line" | grep -o '"lldpRemPortDesc":"[^"]*"' | cut -d: -f2 | tr -d '"')
            
            # 使用适当的展示名称
            local_port="${loc_port_desc:-${loc_port_id:-未知}}"
            remote_port="${rem_port_id:-未知}"
            remote_desc="${rem_port_desc:-无描述}"
            
            if [ -n "$agent_host" ] && [ -n "$rem_sys_name" ]; then
                echo "| $agent_host | ${sys_name:-未获取} | $local_port | $rem_sys_name | $remote_port | $remote_desc |" >> ${OUTPUT_FILE}
                lldp_found=true
            fi
        done
    fi
    
    # 检查作为字段采集的LLDP数据
    if grep -q '"lldpRemSysName":' "${INPUT_FILE}"; then
        # 使用全局变量标记LLDP查找状态，解决子shell变量作用域问题
        # shellcheck disable=SC2034
        LLDP_GLOBAL_FOUND=true
        
        grep -e '"lldpRemSysName":' "${INPUT_FILE}" | while read -r line; do
            agent_host=$(echo "$line" | grep -o '"agent_host":"[^"]*"' | cut -d: -f2 | tr -d '"')
            sys_name=$(echo "$line" | grep -o '"sysName":"[^"]*"' | cut -d: -f2 | tr -d '"')
            
            # 如果找到了lldpRemSysName字段，显示LLDP信息
            if [[ $line =~ \"lldpRemSysName\":\"([^\"]+)\" ]]; then
                lldp_found=true
                rem_sys_name="${BASH_REMATCH[1]}"
                
                # 尝试获取远程接口ID
                if [[ $line =~ \"lldpRemPortId\":\"([^\"]+)\" ]]; then
                    rem_port_id="${BASH_REMATCH[1]}"
                else
                    rem_port_id="未知"
                fi
                
                # 尝试获取本地接口名称
                # 注意：当前LLDP信息是作为snmp指标字段采集的，而接口名称在interfaceX指标中
                # 由于不在同一记录中，无法直接关联。要解决此问题，需要修改采集配置，
                # 在LLDP表配置中添加lldpLocPortId的OID采集，或使用表格形式采集LLDP信息
                if [[ $line =~ \"ifName\":\"([^\"]+)\" ]]; then
                    loc_port_id="${BASH_REMATCH[1]}"
                else
                    loc_port_id="未知"
                fi
                
                # 获取远程设备描述（可能不存在）
                if [[ $line =~ \"lldpRemSysDesc\":\"([^\"]+)\" ]]; then
                    rem_desc="${BASH_REMATCH[1]}"
                    # 截断过长的描述
                    if [ ${#rem_desc} -gt 30 ]; then
                        rem_desc="${rem_desc:0:27}..."
                    fi
                else
                    rem_desc="无描述"
                fi
                
                # 输出LLDP信息
                echo "| $agent_host | ${sys_name:-未知} | $loc_port_id | $rem_sys_name | $rem_port_id | $rem_desc |" >> ${OUTPUT_FILE}
            fi
        done
    fi
    
    # 检查全局变量判断LLDP是否找到
    if grep -q '"lldpRemSysName":' "${INPUT_FILE}"; then
        lldp_found=true
    fi
    
    # 如果没有找到LLDP数据，显示未检测到LLDP信息
    if [ "$lldp_found" = false ]; then
        echo "未检测到LLDP信息，可能原因：" >> ${OUTPUT_FILE}
        echo "1. 设备未启用LLDP功能" >> ${OUTPUT_FILE}
        echo "2. Telegraf配置中未包含LLDP的OID" >> ${OUTPUT_FILE}
        echo "3. 网络设备没有LLDP邻居" >> ${OUTPUT_FILE}
    fi
    
    # 完成JSON格式处理，直接退出
    echo -e "${GREEN}JSON格式处理完成！${NC}"
    exit 0
fi

# 下面的代码将仅在非JSON格式时执行

# 处理系统信息数据 - 修正匹配模式
if grep -q "snmp," "${INPUT_FILE}"; then
    echo -e "\n### 系统基本信息\n" >> ${OUTPUT_FILE}
    echo "| 设备IP | 指标名称 | 中文含义 | 值 |" >> ${OUTPUT_FILE}
    echo "|--------|----------|----------|-----|" >> ${OUTPUT_FILE}
    
    grep "snmp," "${INPUT_FILE}" | while read -r line; do
        # 提取设备IP和主机名
        agent_host=$(echo "$line" | grep -o 'agent_host=[^,]*' | cut -d= -f2)
        
        # 提取各个字段
        fields=$(echo "$line" | cut -d' ' -f2 | tr ',' '\n')
        
        for field in $fields; do
            name=$(echo "$field" | cut -d= -f1)
            value=$(echo "$field" | cut -d= -f2)
            
            # 跳过时间戳字段和空值字段
            if [[ "$name" == *Time* && "$value" == *[0-9] ]] || [ -z "$name" ] || [ -z "$value" ]; then
                continue
            fi
            
            desc=$(get_metric_desc "$name")
            formatted_value=$(format_value "$name" "$value")
            
            echo "| $agent_host | $name | $desc | $formatted_value |" >> ${OUTPUT_FILE}
        done
    done
fi

# 处理接口信息数据 - 修正匹配模式
if grep -q "interface," "${INPUT_FILE}" && ! grep -q "interfaceX," "${INPUT_FILE}"; then
    echo -e "\n### 接口基本信息\n" >> ${OUTPUT_FILE}
    echo "| 设备IP | 接口名称 | 指标名称 | 中文含义 | 值 |" >> ${OUTPUT_FILE}
    echo "|--------|----------|----------|----------|-----|" >> ${OUTPUT_FILE}
    
    grep "interface," "${INPUT_FILE}" | grep -v "interfaceX," | while read -r line; do
        # 提取设备IP和接口名
        agent_host=$(echo "$line" | grep -o 'agent_host=[^,]*' | cut -d= -f2)
        if_index=$(echo "$line" | grep -o 'ifIndex=[^,]*' | cut -d= -f2)
        if_descr=$(echo "$line" | grep -o 'ifDescr="[^"]*"' | cut -d= -f2 | tr -d '"')
        
        if [ -z "$if_descr" ]; then
            if_descr="接口$if_index"
        fi
        
        # 提取各个字段
        fields=$(echo "$line" | cut -d' ' -f2 | tr ',' '\n')
        
        for field in $fields; do
            name=$(echo "$field" | cut -d= -f1)
            value=$(echo "$field" | cut -d= -f2)
            
            # 跳过时间戳字段、空值字段和已处理的字段
            if [[ "$name" == *Time* && "$value" == *[0-9] ]] || [ -z "$name" ] || [ -z "$value" ] || 
               [[ "$name" == "ifIndex" ]] || [[ "$name" == "ifDescr" ]]; then
                continue
            fi
            
            desc=$(get_metric_desc "$name")
            formatted_value=$(format_value "$name" "$value")
            
            echo "| $agent_host | $if_descr | $name | $desc | $formatted_value |" >> ${OUTPUT_FILE}
        done
    done
fi

# 处理接口扩展信息数据 - 修正匹配模式
if grep -q "interfaceX," "${INPUT_FILE}"; then
    echo -e "\n### 接口扩展信息\n" >> ${OUTPUT_FILE}
    echo "| 设备IP | 接口名称 | 指标名称 | 中文含义 | 值 |" >> ${OUTPUT_FILE}
    echo "|--------|----------|----------|----------|-----|" >> ${OUTPUT_FILE}
    
    grep "interfaceX," "${INPUT_FILE}" | while read -r line; do
        # 提取设备IP和接口名
        agent_host=$(echo "$line" | grep -o 'agent_host=[^,]*' | cut -d= -f2)
        if_name=$(echo "$line" | grep -o 'ifName=[^,]*' | cut -d= -f2 | tr -d '"')
        
        # 提取各个字段
        fields=$(echo "$line" | cut -d' ' -f2 | tr ',' '\n')
        
        for field in $fields; do
            name=$(echo "$field" | cut -d= -f1)
            value=$(echo "$field" | cut -d= -f2)
            
            # 跳过时间戳字段、空值字段和已处理的字段
            if [[ "$name" == *Time* && "$value" == *[0-9] ]] || [ -z "$name" ] || [ -z "$value" ] || 
               [[ "$name" == "ifName" ]]; then
                continue
            fi
            
            desc=$(get_metric_desc "$name")
            formatted_value=$(format_value "$name" "$value")
            
            echo "| $agent_host | $if_name | $name | $desc | $formatted_value |" >> ${OUTPUT_FILE}
        done
    done
fi

# 新增: 处理LLDP信息数据 - Influx Line Protocol格式
if grep -q "lldp," "${INPUT_FILE}"; then
    echo -e "\n### LLDP邻居发现信息\n" >> ${OUTPUT_FILE}
    echo "| 设备IP | 本地接口 | 远端设备名称 | 远端接口 | 远端描述 |" >> ${OUTPUT_FILE}
    echo "|--------|----------|-------------|----------|----------|" >> ${OUTPUT_FILE}
    
    grep "lldp," "${INPUT_FILE}" | while read -r line; do
        # 提取设备IP
        agent_host=$(echo "$line" | grep -o 'agent_host=[^,]*' | cut -d= -f2)
        
        # 提取LLDP字段
        fields=$(echo "$line" | cut -d' ' -f2 | tr ',' '\n')
        loc_port_id=""
        loc_port_desc=""
        rem_sys_name=""
        rem_port_id=""
        rem_port_desc=""
        
        for field in $fields; do
            name=$(echo "$field" | cut -d= -f1)
            value=$(echo "$field" | cut -d= -f2 | tr -d '"')
            
            case "$name" in
                "lldpLocPortId") loc_port_id="$value" ;;
                "lldpLocPortDesc") loc_port_desc="$value" ;;
                "lldpRemSysName") rem_sys_name="$value" ;;
                "lldpRemPortId") rem_port_id="$value" ;;
                "lldpRemPortDesc") rem_port_desc="$value" ;;
            esac
        done
        
        # 使用适当的展示名称
        local_port="${loc_port_desc:-${loc_port_id:-未知}}"
        remote_port="${rem_port_id:-未知}"
        remote_desc="${rem_port_desc:-无描述}"
        
        if [ -n "$agent_host" ] && [ -n "$rem_sys_name" ]; then
            echo "| $agent_host | $local_port | $rem_sys_name | $remote_port | $remote_desc |" >> ${OUTPUT_FILE}
        fi
    done
else
    echo -e "\n### LLDP邻居发现信息\n" >> ${OUTPUT_FILE}
    echo "未检测到LLDP信息，可能原因：" >> ${OUTPUT_FILE}
    echo "1. 设备未启用LLDP功能" >> ${OUTPUT_FILE}
    echo "2. Telegraf配置中未包含LLDP的OID" >> ${OUTPUT_FILE}
    echo "3. 网络设备没有LLDP邻居" >> ${OUTPUT_FILE}
fi

# 输出数据流量汇总信息
echo -e "\n### 数据流量汇总\n" >> ${OUTPUT_FILE}
echo "| 设备IP | 接口名称 | 入向流量 | 出向流量 | 入向包数 | 出向包数 |" >> ${OUTPUT_FILE}
echo "|--------|----------|----------|----------|----------|----------|" >> ${OUTPUT_FILE}

grep "interfaceX," "${INPUT_FILE}" | while read -r line; do
    # 提取设备IP和接口名
    agent_host=$(echo "$line" | grep -o 'agent_host=[^,]*' | cut -d= -f2)
    if_name=$(echo "$line" | grep -o 'ifName=[^,]*' | cut -d= -f2 | tr -d '"')
    
    # 提取流量数据 - 改进提取方法
    fields=$(echo "$line" | cut -d' ' -f2 | tr ',' '\n')
    in_octets=""
    out_octets=""
    in_packets=""
    out_packets=""
    
    for field in $fields; do
        name=$(echo "$field" | cut -d= -f1)
        value=$(echo "$field" | cut -d= -f2)
        
        case "$name" in 
            "ifHCInOctets") in_octets="$value" ;;
            "ifHCOutOctets") out_octets="$value" ;;
            "ifHCInUcastPkts") in_packets="$value" ;;
            "ifHCOutUcastPkts") out_packets="$value" ;;
        esac
    done
    
    # 格式化数据
    in_octets=${in_octets%i}
    out_octets=${out_octets%i}
    in_packets=${in_packets%i}
    out_packets=${out_packets%i}
    
    # 转换单位
    if [ -n "$in_octets" ]; then
        if [ "$in_octets" -gt 1048576 ]; then
            in_octets_fmt="$(echo "scale=2; $in_octets/1048576" | bc)MB"
        elif [ "$in_octets" -gt 1024 ]; then
            in_octets_fmt="$(echo "scale=2; $in_octets/1024" | bc)KB"
        else
            in_octets_fmt="${in_octets}B"
        fi
    else
        in_octets_fmt="0B"
    fi
    
    if [ -n "$out_octets" ]; then
        if [ "$out_octets" -gt 1048576 ]; then
            out_octets_fmt="$(echo "scale=2; $out_octets/1048576" | bc)MB"
        elif [ "$out_octets" -gt 1024 ]; then
            out_octets_fmt="$(echo "scale=2; $out_octets/1024" | bc)KB"
        else
            out_octets_fmt="${out_octets}B"
        fi
    else
        out_octets_fmt="0B"
    fi
    
    echo "| $agent_host | $if_name | $in_octets_fmt | $out_octets_fmt | ${in_packets:-0} | ${out_packets:-0} |" >> ${OUTPUT_FILE}
done

echo -e "${GREEN}SNMP采集结果处理完成！${NC}"
echo -e "生成的中文注释报告: ${OUTPUT_FILE}"

exit 0 