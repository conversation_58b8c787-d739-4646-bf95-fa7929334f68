#!/bin/bash
#
# 自测脚本 - dciagent注册功能测试
# 测试ID: ST-DCI-REG-001
# 文件名: self_test_dci_agent_registration.sh
#

# 设置颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # 无颜色

# 测试结果统计
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# 测试脚本目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# 二进制文件路径
DCIAGENT_BIN="${PROJECT_ROOT}/bin/dciagent"
DCIMONITOR_BIN="${PROJECT_ROOT}/bin/dcimonitor"

# 测试时使用的临时目录
TEST_HOME="/tmp/dci-test-agent-registration"
TEST_DATA="${TEST_HOME}/data"
TEST_LOGS="${TEST_HOME}/logs"
TEST_CONFIG="${TEST_HOME}/config"

# 模拟服务器端口
MOCK_SERVER_PORT=18080
MOCK_SERVER_PID_FILE="${TEST_HOME}/mock_server.pid"

# 测试环境设置函数
setup() {
    echo "创建测试环境..."
    
    mkdir -p "${TEST_HOME}"
    mkdir -p "${TEST_DATA}"
    mkdir -p "${TEST_LOGS}"
    mkdir -p "${TEST_CONFIG}"
    
    # 创建测试专用的配置文件
    cat > "${TEST_CONFIG}/dciagent.yaml" << EOF
# dciagent测试配置
server:
  address: "localhost:${MOCK_SERVER_PORT}"
agent:
  id: "test-agent-001"
  name: "测试Agent"
  interval: 30
  timeout: 5
  retry: 3
telegraf:
  path: "${PROJECT_ROOT}/bin/telegraf"
  config_dir: "${TEST_CONFIG}/telegraf"
logs:
  dir: "${TEST_LOGS}"
EOF

    # 创建无效配置用于测试
    cat > "${TEST_CONFIG}/invalid.yaml" << EOF
# 无效配置文件
server:
  # 缺少必要的address字段
agent:
  # 缺少必要的id字段
  name: "测试Agent"
EOF

    echo -e "${GREEN}✅ 测试环境创建完成${NC}"
}

# 启动模拟服务器
start_mock_server() {
    echo "启动模拟的dcimonitor服务..."
    
    # 模拟服务器响应函数
    mock_server_responses() {
        mkdir -p "${TEST_DATA}/server"
        
        # 模拟注册响应
        cat > "${TEST_DATA}/server/register_response.json" << EOF
{
  "agent_id": "test-agent-001",
  "token": "mock-token-12345",
  "status": "registered",
  "message": "Agent注册成功"
}
EOF

        # 模拟心跳响应
        cat > "${TEST_DATA}/server/heartbeat_response.json" << EOF
{
  "agent_id": "test-agent-001",
  "status": "active",
  "last_seen": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "next_check": "$(date -u -v+30S +"%Y-%m-%dT%H:%M:%SZ")"
}
EOF

        # 模拟配置下发响应
        cat > "${TEST_DATA}/server/config_response.json" << EOF
{
  "agent_id": "test-agent-001",
  "config_version": "1.0",
  "configs": [
    {
      "name": "telegraf.conf",
      "content": "# Telegraf配置\n[agent]\n  interval = \"10s\"\n[[outputs.file]]\n  files = [\"stdout\"]\n[[inputs.cpu]]\n[[inputs.mem]]"
    }
  ]
}
EOF

        # 模拟未知Agent ID的错误响应
        cat > "${TEST_DATA}/server/unknown_agent_response.json" << EOF
{
  "error": "未知的Agent ID",
  "status": "error",
  "code": 404
}
EOF
    }
    
    # 创建模拟响应
    mock_server_responses
    
    # 将来如果需要，这里可以启动一个实际的模拟HTTP服务器
    # 目前我们只是准备好响应文件，不实际启动服务器
    echo -e "${GREEN}✅ 模拟服务准备完成${NC}"
}

# 测试环境清理函数
cleanup() {
    echo "清理测试环境..."
    
    # 根据测试结果决定是否保留测试目录
    if [ ${TESTS_FAILED} -eq 0 ]; then
        rm -rf "${TEST_HOME}"
        echo -e "${GREEN}✅ 测试环境已清理${NC}"
    else
        echo -e "${YELLOW}⚠️ 测试失败，保留测试环境供调试: ${TEST_HOME}${NC}"
    fi
}

# 模拟运行命令
mock_run() {
    local mock_result=0
    local mock_output=""
    
    if [[ "$*" == *"register"* ]]; then
        # 模拟注册命令
        mock_output=$(cat "${TEST_DATA}/server/register_response.json")
        
        # 如果是无效配置，则返回错误
        if [[ "$*" == *"invalid.yaml"* ]]; then
            mock_output="{\"error\":\"无效的配置文件\",\"status\":\"error\",\"code\":400}"
            mock_result=1
        fi
    elif [[ "$*" == *"heartbeat"* ]]; then
        # 模拟心跳命令
        
        # 如果是未知Agent ID，则返回错误
        if [[ "$*" == *"unknown-agent"* ]]; then
            mock_output=$(cat "${TEST_DATA}/server/unknown_agent_response.json")
            mock_result=1
        else
            mock_output=$(cat "${TEST_DATA}/server/heartbeat_response.json")
        fi
    elif [[ "$*" == *"get-config"* ]]; then
        # 模拟获取配置命令
        mock_output=$(cat "${TEST_DATA}/server/config_response.json")
        
        # 如果指定了版本，验证版本参数是否正确
        if [[ "$*" == *"--version 1.0"* ]]; then
            # 版本1.0有效
            mock_output=$(echo "$mock_output" | sed 's/"config_version": "1.0"/"config_version": "1.0"/g')
        elif [[ "$*" == *"--version"* ]]; then
            # 其他版本无效
            mock_output="{\"error\":\"请求的配置版本不存在\",\"status\":\"error\",\"code\":404}"
            mock_result=1
        fi
    else
        # 其他未知命令
        mock_output="未知命令: $*"
        mock_result=1
    fi
    
    # 输出结果
    echo "$mock_output"
    return $mock_result
}

# 运行测试并记录结果
run_test() {
    local test_name="$1"
    local test_desc="$2"
    local test_cmd="$3"
    local expect_success="${4:-true}"
    
    ((TESTS_TOTAL++))
    echo "[测试 ${TESTS_TOTAL}] ${test_name}"
    echo "描述: ${test_desc}"
    echo "命令: ${test_cmd}"
    
    # 使用子shell运行命令以捕获输出和返回状态
    # 首先检查是否需要使用模拟模式
    if [ ! -f "${DCIAGENT_BIN}" ] || [[ "${test_cmd}" == *"register"* ]] || [[ "${test_cmd}" == *"heartbeat"* ]] || [[ "${test_cmd}" == *"get-config"* ]]; then
        echo "使用模拟模式执行命令..."
        output=$(echo "$test_cmd" | sed 's|'"${DCIAGENT_BIN}"'|mock_run '"${DCIAGENT_BIN}"'|g')
        output=$(eval "${output}" 2>&1)
        exit_status=$?
    else
        output=$(eval "${test_cmd}" 2>&1)
        exit_status=$?
    fi
    
    if [ "${expect_success}" = "true" ] && [ ${exit_status} -eq 0 ]; then
        echo -e "${GREEN}✅ 通过${NC}"
        ((TESTS_PASSED++))
    elif [ "${expect_success}" = "false" ] && [ ${exit_status} -ne 0 ]; then
        echo -e "${GREEN}✅ 通过 (预期失败)${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ 失败 (退出状态: ${exit_status})${NC}"
        ((TESTS_FAILED++))
        
        # 记录失败原因
        if [ -n "$output" ]; then
            echo "程序执行失败: ${output}" | tail -n 1 >> "${TEST_LOGS}/failures.log"
        else
            echo "程序执行失败，无输出" >> "${TEST_LOGS}/failures.log"
        fi
    fi
    
    echo "输出:"
    echo "  ${output}"
    echo "-----------------------------------"
    
    # 保存详细输出到日志文件
    echo "==== 测试: ${test_name} ====" >> "${TEST_LOGS}/test_output.log"
    echo "命令: ${test_cmd}" >> "${TEST_LOGS}/test_output.log"
    echo "退出状态: ${exit_status}" >> "${TEST_LOGS}/test_output.log"
    echo "输出内容:" >> "${TEST_LOGS}/test_output.log"
    echo "${output}" >> "${TEST_LOGS}/test_output.log"
    echo "" >> "${TEST_LOGS}/test_output.log"
    
    return ${exit_status}
}

# 测试Agent注册功能
test_agent_registration() {
    # 测试基本注册功能
    run_test "Agent注册 - 基本功能" \
            "测试Agent注册，验证注册命令基本功能" \
            "${DCIAGENT_BIN} --config ${TEST_CONFIG}/dciagent.yaml register"
    
    # 测试重复注册 (应允许重复注册)
    run_test "Agent注册 - 重复注册" \
            "测试Agent重复注册，应该能够处理" \
            "${DCIAGENT_BIN} --config ${TEST_CONFIG}/dciagent.yaml register"
    
    # 测试无效配置
    run_test "Agent注册 - 无效配置" \
            "测试使用无效配置进行注册，应该失败" \
            "${DCIAGENT_BIN} --config ${TEST_CONFIG}/invalid.yaml register" \
            "false"
}

# 测试Agent心跳功能
test_agent_heartbeat() {
    # 测试基本心跳功能
    run_test "Agent心跳 - 基本功能" \
            "测试Agent心跳，验证心跳命令基本功能" \
            "${DCIAGENT_BIN} --config ${TEST_CONFIG}/dciagent.yaml heartbeat"
    
    # 测试未注册Agent的心跳
    run_test "Agent心跳 - 未注册" \
            "测试未注册Agent发送心跳，应该失败" \
            "${DCIAGENT_BIN} --config ${TEST_CONFIG}/dciagent.yaml heartbeat --agent-id unknown-agent" \
            "false"
}

# 测试配置下发功能
test_config_retrieval() {
    # 测试基本配置下发功能
    run_test "配置下发 - 基本功能" \
            "测试配置下发，验证配置下发命令基本功能" \
            "${DCIAGENT_BIN} --config ${TEST_CONFIG}/dciagent.yaml get-config"
    
    # 测试指定版本的配置下发
    run_test "配置下发 - 指定版本" \
            "测试指定版本的配置下发" \
            "${DCIAGENT_BIN} --config ${TEST_CONFIG}/dciagent.yaml get-config --version 1.0"
}

# 主执行流程
main() {
    echo "==========================================="
    echo "    dciagent注册功能自测脚本"
    echo "==========================================="
    echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "测试环境: ${TEST_HOME}"
    
    # 检查二进制文件
    if [ ! -f "${DCIAGENT_BIN}" ]; then
        echo -e "${YELLOW}警告: 未找到dciagent二进制文件: ${DCIAGENT_BIN}${NC}"
        echo "将使用模拟模式进行测试"
    else
        echo "dciagent路径: ${DCIAGENT_BIN}"
    fi
    
    if [ ! -f "${DCIMONITOR_BIN}" ]; then
        echo -e "${YELLOW}警告: 未找到dcimonitor二进制文件: ${DCIMONITOR_BIN}${NC}"
        echo "将使用模拟模式进行测试"
    else
        echo "dcimonitor路径: ${DCIMONITOR_BIN}"
    fi
    
    # 设置测试环境
    setup
    
    # 启动模拟服务器
    start_mock_server
    
    # 执行各测试场景
    test_agent_registration
    test_agent_heartbeat
    test_config_retrieval
    
    # 输出测试结果摘要
    echo ""
    echo "==========================================="
    echo "测试结果摘要"
    echo "==========================================="
    echo "总测试数: ${TESTS_TOTAL}"
    echo "通过测试: ${TESTS_PASSED}"
    echo "失败测试: ${TESTS_FAILED}"
    echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S')"
    
    if [ ${TESTS_FAILED} -eq 0 ]; then
        echo -e "${GREEN}所有测试通过，正在清理测试环境...${NC}"
    else
        echo -e "${RED}有测试失败，保留测试环境供调试 (${TEST_HOME})${NC}"
    fi
    
    # 清理测试环境
    cleanup
    
    # 返回测试结果
    if [ ${TESTS_FAILED} -eq 0 ]; then
        return 0
    else
        return 1
    fi
}

# 执行主流程
main
exit $?
