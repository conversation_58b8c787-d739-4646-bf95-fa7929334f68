#!/bin/bash
#
# 执行脚本 - 运行DCI Agent和Monitor测试套件
# 文件名: run_agent_tests.sh
#

# 设置颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # 无颜色

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
RESULTS_DIR="${PROJECT_ROOT}/results"

# 创建结果目录
mkdir -p "${RESULTS_DIR}"

# 生成结果文件名
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
RESULTS_FILE="${RESULTS_DIR}/agent_tests_${TIMESTAMP}.log"

# 测试结果统计
SCRIPTS_TOTAL=0
SCRIPTS_PASSED=0
SCRIPTS_FAILED=0

# 记录到日志文件和标准输出
log() {
    echo "$@" | tee -a "${RESULTS_FILE}"
}

# 保存命令输出到日志文件
run_and_log() {
    local cmd="$1"
    local desc="$2"
    
    log "============================================"
    log "开始执行测试脚本: $(basename "${cmd}")"
    log "============================================"
    
    if [[ ! -x "${cmd}" ]]; then
        chmod +x "${cmd}"
        log "已为脚本添加执行权限: $(basename "${cmd}")"
    fi
    
    # 执行命令并记录输出
    "${cmd}" | tee -a "${RESULTS_FILE}"
    exit_status=${PIPESTATUS[0]}
    
    if [ ${exit_status} -eq 0 ]; then
        log "--------------------------------------------"
        log "测试脚本执行成功: $(basename "${cmd}")"
        return 0
    else
        log "--------------------------------------------"
        log "测试脚本执行失败: $(basename "${cmd}") (退出状态: ${exit_status})"
        return ${exit_status}
    fi
}

# 主执行流程
main() {
    log "==========================================="
    log "    DCI Agent和Monitor测试套件执行脚本"
    log "==========================================="
    log "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
    log "测试脚本目录: ${SCRIPT_DIR}"
    log "结果保存至: ${RESULTS_FILE}"
    log ""
    
    # 记录测试环境信息
    log "============ 测试环境信息 ============"
    log "操作系统: $(uname -a)"
    log "测试时间: $(date '+%Y-%m-%d %H:%M:%S')"
    
    # 检查dciagent和dcimonitor是否可用
    if [ -f "${PROJECT_ROOT}/bin/dciagent" ] && [ -x "${PROJECT_ROOT}/bin/dciagent" ]; then
        log "dciagent版本: $(${PROJECT_ROOT}/bin/dciagent version 2>/dev/null || echo "无法获取版本信息")"
    else
        log "警告: dciagent命令未找到"
    fi
    
    if [ -f "${PROJECT_ROOT}/bin/dcimonitor" ] && [ -x "${PROJECT_ROOT}/bin/dcimonitor" ]; then
        log "dcimonitor版本: $(${PROJECT_ROOT}/bin/dcimonitor version 2>/dev/null || echo "无法获取版本信息")"
    else
        log "警告: dcimonitor命令未找到"
    fi
    log "=======================================\n"
    
    # 运行所有Agent相关的测试脚本
    for script in "${SCRIPT_DIR}"/self_test_dci_*.sh; do
        if [ -f "${script}" ]; then
            ((SCRIPTS_TOTAL++))
            
            if run_and_log "${script}" "Agent测试脚本"; then
                ((SCRIPTS_PASSED++))
            else
                ((SCRIPTS_FAILED++))
            fi
        fi
    done
    
    # 输出测试结果摘要
    log ""
    log "==========================================="
    log "            测试套件执行结果"
    log "==========================================="
    log "总测试脚本数: ${SCRIPTS_TOTAL}"
    log "通过脚本数: ${SCRIPTS_PASSED}"
    log "失败脚本数: ${SCRIPTS_FAILED}"
    log "结束时间: $(date '+%Y-%m-%d %H:%M:%S')"
    log "完整日志: ${RESULTS_FILE}"
    log ""
    
    if [ ${SCRIPTS_FAILED} -eq 0 ]; then
        log "测试套件执行成功!"
        return 0
    else
        log "测试套件执行失败!"
        return 1
    fi
}

# 执行主流程
main
exit $? 