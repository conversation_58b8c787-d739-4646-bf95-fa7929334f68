# DCI监控系统 - Agent测试工具集使用说明

## 概述

Agent测试工具集是DCI监控系统自测试框架的一部分，用于验证DCI Agent的核心功能。这些工具可以帮助用户测试Agent的注册、数据采集、环境配置等功能，确保Agent能够正常工作并与监控系统其他组件正确交互。

本文档提供了Agent测试工具集中各个脚本的详细说明和使用方法。

## 目录结构

```
dci_self_test/scripts/agent/
├── setup_test_env.sh                  # 测试环境设置脚本
├── run_agent_tests.sh                 # Agent测试套件运行脚本
├── self_test_dci_agent_registration.sh # Agent注册测试脚本
├── self_test_dci_data_collection.sh   # 数据采集测试脚本
└── README_agent.md                    # 本文档
```

## 脚本说明

### 1. setup_test_env.sh - 测试环境设置脚本

#### 功能介绍

`setup_test_env.sh`用于准备DCI Agent测试所需的环境，包括创建必要的目录结构、配置文件、测试数据等。该脚本执行以下操作：

- 创建测试所需的目录结构
- 生成测试配置文件
- 设置环境变量
- 检查系统依赖
- 准备测试数据

#### 使用方法

```bash
./setup_test_env.sh [选项]
```

#### 选项

- `-c, --clean`：清理之前的测试环境并重新创建
- `-v, --verbose`：显示详细的执行过程

#### 示例

```bash
# 默认设置测试环境
./setup_test_env.sh

# 清理并重新创建测试环境
./setup_test_env.sh -c

# 显示详细执行过程
./setup_test_env.sh -v
```

#### 输出

- 测试环境目录：`dci_self_test/data/dciagent`
- 配置文件：`dci_self_test/config/dciagent`
- 日志文件：`dci_self_test/results/setup_env.log`

### 2. run_agent_tests.sh - Agent测试套件运行脚本

#### 功能介绍

`run_agent_tests.sh`是一个统一的入口点，用于运行所有Agent相关的测试。该脚本会按照预定义的顺序执行各个测试脚本，并汇总测试结果。

#### 使用方法

```bash
./run_agent_tests.sh [选项]
```

#### 选项

- `-s, --single <test_name>`：仅运行指定的测试
- `-p, --parallel`：并行运行测试（默认为串行）
- `-r, --report`：生成HTML格式的测试报告

#### 示例

```bash
# 运行所有Agent测试
./run_agent_tests.sh

# 仅运行Agent注册测试
./run_agent_tests.sh -s registration

# 并行运行所有测试并生成报告
./run_agent_tests.sh -p -r
```

#### 输出

- 测试结果摘要：控制台输出
- 详细日志：`dci_self_test/results/agent_tests.log`
- 测试报告（如果启用）：`dci_self_test/results/agent_tests_report.html`

### 3. self_test_dci_agent_registration.sh - Agent注册测试脚本

#### 功能介绍

`self_test_dci_agent_registration.sh`用于测试DCI Agent的注册功能。该脚本会验证Agent是否能够成功向监控系统注册，并检查注册过程中的各个环节是否正常工作。测试内容包括：

- Agent ID生成
- 证书生成和验证
- 与服务器的通信
- 注册信息的正确性
- 错误处理和重试机制

#### 使用方法

```bash
./self_test_dci_agent_registration.sh [选项]
```

#### 选项

- `-m, --mock`：使用模拟服务器进行测试，无需真实服务器环境
- `-f, --force`：强制重新注册，即使已存在注册信息
- `-t, --timeout <seconds>`：设置操作超时时间（默认60秒）

#### 示例

```bash
# 默认测试Agent注册
./self_test_dci_agent_registration.sh

# 使用模拟服务器测试
./self_test_dci_agent_registration.sh -m

# 强制重新注册并设置超时时间
./self_test_dci_agent_registration.sh -f -t 120
```

#### 输出

- 测试报告：`dci_self_test/results/agent_registration_test.md`
- 日志文件：`dci_self_test/results/agent_registration.log`
- 注册信息：`dci_self_test/data/dciagent/registration.json`

### 4. self_test_dci_data_collection.sh - 数据采集测试脚本

#### 功能介绍

`self_test_dci_data_collection.sh`用于测试DCI Agent的数据采集功能。该脚本会验证Agent是否能够正确采集系统数据并发送到后端服务。测试内容包括：

- 基本系统指标采集（CPU、内存、磁盘等）
- 自定义指标采集
- 数据格式和完整性
- 数据传输和确认机制
- 错误处理和恢复机制

#### 使用方法

```bash
./self_test_dci_data_collection.sh [选项]
```

#### 选项

- `-d, --duration <seconds>`：设置测试持续时间（默认300秒）
- `-i, --interval <seconds>`：设置数据采集间隔（默认10秒）
- `-m, --metrics <list>`：指定要测试的指标列表，逗号分隔
- `-o, --output <file>`：将采集到的数据保存到指定文件

#### 示例

```bash
# 默认测试数据采集
./self_test_dci_data_collection.sh

# 设置测试持续60秒，采集间隔5秒
./self_test_dci_data_collection.sh -d 60 -i 5

# 仅测试CPU和内存指标
./self_test_dci_data_collection.sh -m cpu,memory

# 将采集数据保存到文件
./self_test_dci_data_collection.sh -o /tmp/collected_data.json
```

#### 输出

- 测试报告：`dci_self_test/results/data_collection_test.md`
- 日志文件：`dci_self_test/results/data_collection.log`
- 采集数据样本：`dci_self_test/results/sample_data.json`

## 典型使用场景

### 场景一：全面测试Agent功能

当需要全面验证Agent的所有功能时，可以使用`run_agent_tests.sh`脚本：

```bash
# 设置测试环境
./setup_test_env.sh

# 运行所有Agent测试并生成报告
./run_agent_tests.sh -r
```

### 场景二：验证Agent注册

当需要单独验证Agent注册功能时，可以使用`self_test_dci_agent_registration.sh`脚本：

```bash
# 使用模拟服务器测试Agent注册
./self_test_dci_agent_registration.sh -m
```

### 场景三：验证数据采集

当需要验证Agent数据采集功能时，可以使用`self_test_dci_data_collection.sh`脚本：

```bash
# 测试数据采集，持续时间60秒
./self_test_dci_data_collection.sh -d 60
```

## 常见问题排查

### 1. Agent注册失败

如果Agent注册失败，请检查以下几点：

- 网络连接是否正常
- 服务器地址和端口是否正确
- 证书文件是否存在且有效
- 是否有足够的权限创建和访问注册文件

### 2. 数据采集问题

如果数据采集出现问题，请检查以下几点：

- Agent进程是否正常运行
- 配置文件是否正确
- 系统资源是否足够
- 是否有权限访问系统指标

### 3. 测试环境问题

如果测试环境设置出现问题，请检查以下几点：

- 磁盘空间是否足够
- 是否有目录创建权限
- 系统依赖是否满足
- 环境变量是否正确设置

## 高级配置

### 自定义测试环境

如果需要自定义测试环境，可以编辑`setup_test_env.sh`脚本中的配置部分：

```bash
# 自定义配置目录
CONFIG_DIR="/custom/path/to/config"

# 自定义数据目录
DATA_DIR="/custom/path/to/data"

# 自定义结果目录
RESULTS_DIR="/custom/path/to/results"
```

### 自定义测试参数

如果需要调整测试参数，可以创建一个配置文件`agent_test_config.sh`：

```bash
#!/bin/bash
# 自定义测试参数

# 注册服务器地址
REGISTRATION_SERVER="https://custom-server:8443"

# 数据采集间隔
COLLECTION_INTERVAL=30

# 测试持续时间
TEST_DURATION=600

# 要测试的指标列表
TEST_METRICS="cpu,memory,disk,network"
```

然后在运行测试前导入此配置：

```bash
source agent_test_config.sh
./run_agent_tests.sh
```

## 结论

Agent测试工具集提供了一套全面的工具，用于测试和验证DCI监控系统的Agent功能。通过这些工具，用户可以轻松验证Agent的各个组件是否正常工作，并快速排查潜在问题。 