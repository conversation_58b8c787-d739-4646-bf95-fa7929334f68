#!/bin/bash
#
# 测试环境准备脚本 - 设置测试环境必要的链接和配置
# 文件名: setup_test_env.sh
#

# 设置颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # 无颜色

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
BIN_DIR="${PROJECT_ROOT}/bin"
CONFIG_DIR="${PROJECT_ROOT}/config"
DATA_DIR="${PROJECT_ROOT}/data"

# 二进制文件来源和目标路径
TELEGRAF_SOURCE="/Users/<USER>/code/dci/dci-workspace/telegraf/telegraf"
TELEGRAF_TARGET="${BIN_DIR}/telegraf"
DCIAGENT_SOURCE="/Users/<USER>/code/dci/dci-workspace/dci-monitor/src/dciagent/bin/dciagent"
DCIAGENT_TARGET="${BIN_DIR}/dciagent"
DCIMONITOR_SOURCE="/Users/<USER>/code/dci/dci-workspace/dci-monitor/src/dcimonitor/bin/dcimonitor"
DCIMONITOR_TARGET="${BIN_DIR}/dcimonitor"

echo "==========================================="
echo "    DCI测试环境准备脚本"
echo "==========================================="
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo "项目根目录: ${PROJECT_ROOT}"
echo ""

# 创建目录
create_directories() {
    echo "创建必要的目录..."
    
    # 创建bin目录
    mkdir -p "${BIN_DIR}"
    
    # 创建结果目录
    mkdir -p "${PROJECT_ROOT}/results"
    
    # 创建配置目录
    mkdir -p "${CONFIG_DIR}/dciagent"
    mkdir -p "${CONFIG_DIR}/dcimonitor"
    mkdir -p "${CONFIG_DIR}/telegraf"
    
    # 创建数据目录
    mkdir -p "${DATA_DIR}/dciagent"
    mkdir -p "${DATA_DIR}/dcimonitor"
    mkdir -p "${DATA_DIR}/telegraf"
    
    # 创建临时测试目录
    mkdir -p "/tmp/dci-test-telegraf"
    mkdir -p "/tmp/dci-test-dciagent"
    mkdir -p "/tmp/dci-test-dcimonitor"
    
    echo -e "${GREEN}✅ 目录创建完成${NC}"
}

# 创建软链接
create_symlinks() {
    echo "创建软链接..."
    
    # 检查源文件是否存在
    if [ ! -f "${TELEGRAF_SOURCE}" ]; then
        echo -e "${YELLOW}警告: 未找到Telegraf二进制文件: ${TELEGRAF_SOURCE}${NC}"
        echo "Telegraf测试可能无法正常工作"
    else
        # 创建Telegraf软链接
        ln -sf "${TELEGRAF_SOURCE}" "${TELEGRAF_TARGET}"
        echo -e "${GREEN}✅ Telegraf链接创建完成${NC}"
        echo "Telegraf链接: ${TELEGRAF_TARGET} -> ${TELEGRAF_SOURCE}"
    fi
    
    # 检查dciagent源文件是否存在
    if [ ! -f "${DCIAGENT_SOURCE}" ]; then
        echo -e "${YELLOW}警告: 未找到dciagent二进制文件: ${DCIAGENT_SOURCE}${NC}"
        echo "dciagent测试可能无法正常工作"
    else
        # 创建dciagent软链接
        ln -sf "${DCIAGENT_SOURCE}" "${DCIAGENT_TARGET}"
        echo -e "${GREEN}✅ dciagent链接创建完成${NC}"
        echo "dciagent链接: ${DCIAGENT_TARGET} -> ${DCIAGENT_SOURCE}"
    fi
    
    # 检查dcimonitor源文件是否存在
    if [ ! -f "${DCIMONITOR_SOURCE}" ]; then
        echo -e "${YELLOW}警告: 未找到dcimonitor二进制文件: ${DCIMONITOR_SOURCE}${NC}"
        echo "dcimonitor测试可能无法正常工作"
    else
        # 创建dcimonitor软链接
        ln -sf "${DCIMONITOR_SOURCE}" "${DCIMONITOR_TARGET}"
        echo -e "${GREEN}✅ dcimonitor链接创建完成${NC}"
        echo "dcimonitor链接: ${DCIMONITOR_TARGET} -> ${DCIMONITOR_SOURCE}"
    fi
}

# 清理临时目录
clean_temp_dirs() {
    echo "清理临时测试目录..."
    if [ -d "/tmp/dci-test-telegraf" ]; then
        rm -rf "/tmp/dci-test-telegraf"
        echo -e "${GREEN}✅ Telegraf临时目录已清理${NC}"
    else
        echo -e "${YELLOW}⚠️ Telegraf临时目录不存在，无需清理${NC}"
    fi
    
    if [ -d "/tmp/dci-test-dciagent" ]; then
        rm -rf "/tmp/dci-test-dciagent"
        echo -e "${GREEN}✅ dciagent临时目录已清理${NC}"
    else
        echo -e "${YELLOW}⚠️ dciagent临时目录不存在，无需清理${NC}"
    fi
    
    if [ -d "/tmp/dci-test-dcimonitor" ]; then
        rm -rf "/tmp/dci-test-dcimonitor"
        echo -e "${GREEN}✅ dcimonitor临时目录已清理${NC}"
    else
        echo -e "${YELLOW}⚠️ dcimonitor临时目录不存在，无需清理${NC}"
    fi
}

# 设置脚本执行权限
set_permissions() {
    echo "设置脚本执行权限..."
    
    # 设置所有测试脚本的执行权限
    chmod +x "${SCRIPT_DIR}"/*.sh
    
    echo -e "${GREEN}✅ 权限设置完成${NC}"
}

# 创建基本配置文件
create_config_files() {
    echo "创建基本配置文件..."
    
    # 创建dciagent配置
    if [ ! -f "${CONFIG_DIR}/dciagent/config.yaml" ]; then
        cat > "${CONFIG_DIR}/dciagent/config.yaml" << EOF
# dciagent测试配置
server:
  address: "localhost:8080"
agent:
  id: "test-agent-001"
  name: "测试Agent"
  interval: 30
  timeout: 5
  retry: 3
telegraf:
  path: "../bin/telegraf"
  config_dir: "../config/telegraf"
EOF
        echo -e "${GREEN}✅ dciagent配置文件创建完成${NC}"
    else
        echo -e "${YELLOW}⚠️ dciagent配置文件已存在，跳过创建${NC}"
    fi
    
    # 创建dcimonitor配置
    if [ ! -f "${CONFIG_DIR}/dcimonitor/config.yaml" ]; then
        cat > "${CONFIG_DIR}/dcimonitor/config.yaml" << EOF
# dcimonitor测试配置
server:
  port: 8080
  host: "localhost"
database:
  type: "mock" # 使用模拟数据库
  dsn: ""
kafka:
  bootstrap_servers: "localhost:9092"
  topics:
    metrics: "dci.monitor.v1.defaultchannel.metrics"
    logs: "dci.monitor.v1.defaultchannel.logs"
EOF
        echo -e "${GREEN}✅ dcimonitor配置文件创建完成${NC}"
    else
        echo -e "${YELLOW}⚠️ dcimonitor配置文件已存在，跳过创建${NC}"
    fi
    
    # 创建Telegraf配置
    if [ ! -f "${CONFIG_DIR}/telegraf/telegraf.conf" ]; then
        cat > "${CONFIG_DIR}/telegraf/telegraf.conf" << EOF
# Telegraf测试配置
[agent]
  interval = "10s"
  round_interval = true
  metric_batch_size = 1000
  metric_buffer_limit = 10000
  collection_jitter = "0s"
  flush_interval = "10s"
  flush_jitter = "0s"
  precision = ""
  debug = false
  quiet = false
  logfile = ""
  hostname = ""
  omit_hostname = false

[[outputs.kafka]]
  brokers = ["localhost:9092"]
  topic = "dci.monitor.v1.defaultchannel.metrics"
  
[[inputs.cpu]]
  percpu = true
  totalcpu = true
  collect_cpu_time = false
  report_active = false

[[inputs.mem]]
EOF
        echo -e "${GREEN}✅ Telegraf配置文件创建完成${NC}"
    else
        echo -e "${YELLOW}⚠️ Telegraf配置文件已存在，跳过创建${NC}"
    fi
}

# 验证环境
verify_environment() {
    echo "验证测试环境..."
    
    local test_failed=0
    
    # 验证Telegraf是否可用
    if [ -f "${TELEGRAF_TARGET}" ] && [ -x "${TELEGRAF_TARGET}" ]; then
        echo -e "${GREEN}✅ Telegraf可用: ${TELEGRAF_TARGET}${NC}"
        echo "版本信息:"
        "${TELEGRAF_TARGET}" --version | head -n 1
    else
        echo -e "${YELLOW}⚠️ Telegraf不可用: ${TELEGRAF_TARGET}${NC}"
        test_failed=1
    fi
    
    # 验证dciagent是否可用
    if [ -f "${DCIAGENT_TARGET}" ] && [ -x "${DCIAGENT_TARGET}" ]; then
        echo -e "${GREEN}✅ dciagent可用: ${DCIAGENT_TARGET}${NC}"
        echo "版本信息:"
        "${DCIAGENT_TARGET}" version 2>/dev/null || echo "无法获取版本信息"
    else
        echo -e "${YELLOW}⚠️ dciagent不可用: ${DCIAGENT_TARGET}${NC}"
        test_failed=1
    fi
    
    # 验证dcimonitor是否可用
    if [ -f "${DCIMONITOR_TARGET}" ] && [ -x "${DCIMONITOR_TARGET}" ]; then
        echo -e "${GREEN}✅ dcimonitor可用: ${DCIMONITOR_TARGET}${NC}"
        echo "版本信息:"
        "${DCIMONITOR_TARGET}" version 2>/dev/null || echo "无法获取版本信息"
    else
        echo -e "${YELLOW}⚠️ dcimonitor不可用: ${DCIMONITOR_TARGET}${NC}"
        test_failed=1
    fi
    
    # 验证测试脚本
    for script in "${SCRIPT_DIR}"/self_test_*.sh; do
        if [ -f "$script" ] && [ -x "$script" ]; then
            echo -e "${GREEN}✅ 测试脚本可用: $(basename "$script")${NC}"
        else
            echo -e "${RED}❌ 测试脚本不可用或没有执行权限: $(basename "$script")${NC}"
            test_failed=1
        fi
    done
    
    # 验证运行脚本
    if [ -f "${SCRIPT_DIR}/run_telegraf_tests.sh" ] && [ -x "${SCRIPT_DIR}/run_telegraf_tests.sh" ]; then
        echo -e "${GREEN}✅ 运行脚本可用: run_telegraf_tests.sh${NC}"
    else
        echo -e "${RED}❌ 运行脚本不可用或没有执行权限: run_telegraf_tests.sh${NC}"
        test_failed=1
    fi
    
    # 验证配置文件
    if [ -f "${CONFIG_DIR}/dciagent/config.yaml" ]; then
        echo -e "${GREEN}✅ dciagent配置文件存在${NC}"
    else
        echo -e "${RED}❌ dciagent配置文件不存在${NC}"
        test_failed=1
    fi
    
    if [ -f "${CONFIG_DIR}/dcimonitor/config.yaml" ]; then
        echo -e "${GREEN}✅ dcimonitor配置文件存在${NC}"
    else
        echo -e "${RED}❌ dcimonitor配置文件不存在${NC}"
        test_failed=1
    fi
    
    if [ -f "${CONFIG_DIR}/telegraf/telegraf.conf" ]; then
        echo -e "${GREEN}✅ Telegraf配置文件存在${NC}"
    else
        echo -e "${RED}❌ Telegraf配置文件不存在${NC}"
        test_failed=1
    fi
    
    return $test_failed
}

# 更新测试脚本中的路径
update_script_paths() {
    echo "更新测试脚本中的路径..."
    
    # 获取相对路径的Telegraf位置
    REL_TELEGRAF_PATH="../bin/telegraf"
    
    # 更新所有测试脚本中的Telegraf路径
    for script in "${SCRIPT_DIR}"/self_test_*.sh; do
        # 检查脚本中是否包含Telegraf路径
        if grep -q "TELEGRAF_BIN=" "$script"; then
            # 备份原始文件
            cp "$script" "${script}.bak"
            
            # 替换路径
            sed -i.bak "s|TELEGRAF_BIN=\".*\"|TELEGRAF_BIN=\"${REL_TELEGRAF_PATH}\"|g" "$script"
            rm "${script}.bak"  # 删除macOS生成的备份文件
            
            echo -e "${GREEN}✅ 已更新: $(basename "$script")${NC}"
        else
            echo -e "${YELLOW}⚠️ 脚本未包含Telegraf路径设置: $(basename "$script")${NC}"
        fi
    done
    
    echo -e "${GREEN}✅ 路径更新完成${NC}"
}

# 主执行流程
main() {
    # 创建必要目录
    create_directories
    
    # 清理临时目录
    clean_temp_dirs
    
    # 创建软链接
    create_symlinks
    
    # 设置权限
    set_permissions
    
    # 创建配置文件
    create_config_files
    
    # 更新脚本路径
    update_script_paths
    
    # 验证环境
    if verify_environment; then
        echo -e "\n${GREEN}✅ 测试环境设置完成！${NC}"
        echo "您现在可以运行测试脚本:"
        echo "  cd ${SCRIPT_DIR}"
        echo "  ./run_agent_tests.sh  # 测试dciagent和dcimonitor通信"
        echo "  ./run_telegraf_tests.sh  # 测试Telegraf功能"
        return 0
    else
        echo -e "\n${YELLOW}⚠️ 测试环境设置存在一些问题，但仍可继续。${NC}"
        echo "某些测试可能会失败，请检查上述警告。"
        return 0
    fi
}

# 执行主流程
main
exit $? 