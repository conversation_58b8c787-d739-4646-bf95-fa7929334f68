# DCI监控系统测试脚本目录

本目录包含DCI监控系统的各类测试脚本，按功能模块进行了分组。

## 目录结构

```
scripts/
├── README.md                  # 本文档
├── snmp/                      # SNMP相关测试脚本
│   ├── snmp_test.sh           # SNMP采集功能验证脚本
│   ├── telegraf_kafka_test.sh # Telegraf与Kafka集成测试脚本
│   ├── verify_kafka_data.sh   # Kafka数据验证工具
│   ├── analyze_snmp_results.sh # SNMP采集结果分析工具
│   └── README_snmp.md         # SNMP测试工具集使用说明
├── telegraf/                  # Telegraf相关测试脚本
│   ├── self_test_telegraf_process_control.sh    # Telegraf进程控制测试
│   ├── self_test_telegraf_log_management_tool.sh # Telegraf日志管理工具测试
│   ├── self_test_telegraf_config_check_tool.sh  # Telegraf配置检查工具测试
│   └── run_telegraf_tests.sh                    # Telegraf测试套件运行脚本
├── agent/                     # DCI Agent相关测试脚本
│   ├── setup_test_env.sh                        # 测试环境设置脚本
│   ├── run_agent_tests.sh                       # Agent测试套件运行脚本
│   ├── self_test_dci_agent_registration.sh      # Agent注册测试脚本
│   └── self_test_dci_data_collection.sh         # 数据采集测试脚本
└── common/                    # 通用工具脚本
```

## 模块说明

### SNMP测试模块

SNMP测试模块包含了用于测试和验证DCI监控系统SNMP数据采集功能的脚本。详细说明请参见 [SNMP测试工具集使用说明](./snmp/README_snmp.md)。

### Telegraf测试模块

Telegraf测试模块包含了用于测试Telegraf组件各项功能的脚本，包括进程控制、日志管理和配置检查等。

### Agent测试模块

Agent测试模块包含了用于测试DCI Agent功能的脚本，包括Agent注册、数据采集等核心功能。

### 通用工具模块

通用工具模块包含了可在多个测试场景中复用的工具脚本。

## 使用方法

### 运行所有测试

要运行所有测试，请执行：

```bash
cd dci_self_test/scripts
./run_agent_tests.sh
./run_telegraf_tests.sh
```

### 运行特定模块测试

要运行特定模块的测试，请进入相应目录并执行对应的脚本：

```bash
# 运行SNMP测试
cd dci_self_test/scripts/snmp
./snmp_test.sh

# 运行Telegraf测试
cd dci_self_test/scripts/telegraf
./run_telegraf_tests.sh

# 运行Agent测试
cd dci_self_test/scripts/agent
./run_agent_tests.sh
```

## 测试结果

所有测试的结果将保存在 `dci_self_test/results` 目录中，包括日志文件、测试报告和采集数据等。

## 注意事项

1. 在运行测试前，请确保已正确设置环境变量和配置文件
2. 某些测试可能需要特定的网络环境或设备支持
3. 测试脚本中的默认配置可能需要根据实际环境进行调整 