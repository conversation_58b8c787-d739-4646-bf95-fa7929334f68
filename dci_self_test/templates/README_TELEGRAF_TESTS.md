# Telegraf功能自测脚本说明

本文档介绍DCI监测系统中Telegraf相关功能的自测脚本用法和实现方式。这些脚本用于验证Telegraf配置检查、日志管理和进程控制等功能的正确性。

## 测试脚本概述

本测试套件包含以下自测脚本：

1. **配置检查工具测试** (`self_test_telegraf_config_check_tool.sh`)
   - 测试配置文件语法检查功能
   - 验证不同级别的配置验证
   - 测试多种输出格式支持

2. **日志管理工具测试** (`self_test_telegraf_log_management_tool.sh`)
   - 测试日志查看、过滤和搜索功能
   - 验证日志轮转和归档功能
   - 测试日志统计和清理功能

3. **进程控制功能测试** (`self_test_telegraf_process_control.sh`)
   - 测试进程启动、停止和重启功能
   - 验证状态查询和监控功能
   - 测试异常情况处理能力

4. **测试套件执行脚本** (`run_telegraf_tests.sh`)
   - 一键执行所有测试并收集结果
   - 生成详细测试报告和日志
   - 提供环境检查和问题诊断

## 使用方法

### 前提条件

运行测试前，需要确保以下条件满足：

1. Telegraf二进制文件已准备好：`/Users/<USER>/code/dci/dci-workspace/telegraf/telegraf`
2. 执行测试的用户具有创建临时目录的权限
3. 脚本具有执行权限(`chmod +x *.sh`)

### 运行单个测试

可以单独运行某个测试脚本，例如：

```bash
cd /Users/<USER>/code/dci/dci-workspace/dci_self_test/scripts
./self_test_telegraf_config_check_tool.sh
```

### 运行所有测试

使用测试套件执行脚本可以一次运行所有测试：

```bash
cd /Users/<USER>/code/dci/dci-workspace/dci_self_test/scripts
./run_telegraf_tests.sh
```

测试结果将显示在控制台，并保存在`../results`目录下的日志文件中。

## 测试环境

测试脚本会在以下位置创建临时测试环境：

- 测试目录：`/tmp/dci-test-telegraf/`
- 配置目录：`/tmp/dci-test-telegraf/conf/`
- 日志目录：`/tmp/dci-test-telegraf/logs/`
- 运行目录：`/tmp/dci-test-telegraf/run/`

如果测试全部通过，临时目录会被自动清理；如果测试失败，目录将保留以便调试。

## 测试原理

### 配置检查测试

配置检查测试通过以下步骤验证Telegraf配置检查功能：

1. 创建有效的示例配置文件
2. 验证配置检查命令能正确识别有效配置
3. 注入语法错误，验证错误检测功能
4. 测试不同检查级别和参数组合

### 日志管理测试

日志管理测试通过以下步骤验证Telegraf日志管理功能：

1. 创建模拟的日志文件和历史日志
2. 测试日志查看和过滤功能
3. 执行日志轮转并验证结果
4. 测试日志清理和统计功能

### 进程控制测试

进程控制测试通过以下步骤验证Telegraf进程管理功能：

1. 模拟进程状态和控制机制
2. 测试启动、停止和重启命令
3. 验证状态检查和详细信息显示
4. 测试异常情况处理和错误报告

## macOS环境适配

由于测试环境为macOS，测试脚本已针对macOS特性进行了适配：

1. 使用`sed -i.bak`替代Linux的`sed -i`
2. 使用macOS兼容的进程状态查询方式
3. 调整文件权限和目录结构适配macOS

## 测试扩展

可以通过以下方式扩展测试套件：

1. 添加新的测试用例到现有脚本
2. 创建新的测试脚本并添加到`run_telegraf_tests.sh`
3. 扩展测试环境模拟更复杂的使用场景

## 常见问题解决

1. **问题**：测试脚本无法找到Telegraf二进制文件
   **解决方法**：检查`TELEGRAF_BIN`变量指向的路径是否正确

2. **问题**：测试环境创建失败
   **解决方法**：检查临时目录权限和磁盘空间

3. **问题**：测试结果不一致
   **解决方法**：清理旧的测试环境(`rm -rf /tmp/dci-test-telegraf`)后重新运行测试 