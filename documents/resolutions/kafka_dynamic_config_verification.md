# Kafka动态配置方案验证步骤

本文档提供了验证新实现的Kafka动态配置方案的步骤和命令。通过这些验证步骤，您可以确认advertised.listeners配置在Pod重新调度的情况下是否仍然有效。

## 先决条件

- 已部署更新后的kafka.yaml配置
- 具有kubectl访问权限
- 已安装kafka_connection_checker.sh工具

## 验证步骤

### 1. 检查Kafka Broker的当前配置

首先，检查每个Kafka Broker实际使用的配置：

```bash
# 查看kafka-0的配置
kubectl -n dci exec kafka-0 -- cat /opt/bitnami/kafka/config/server-0.properties | grep advertised

# 查看kafka-1的配置
kubectl -n dci exec kafka-1 -- cat /opt/bitnami/kafka/config/server-1.properties | grep advertised

# 查看kafka-2的配置
kubectl -n dci exec kafka-2 -- cat /opt/bitnami/kafka/config/server-2.properties | grep advertised
```

确认advertised.listeners配置中的地址是否正确（应为节点的实际可访问地址）。

### 2. 验证动态地址发现逻辑

检查Kafka Pod的日志，确认动态地址发现逻辑正常工作：

```bash
# 查看地址发现日志
kubectl -n dci logs kafka-0 | grep -E "Trying to get|Found|advertised"
```

您应该能看到类似以下的输出，显示地址发现过程：

```
Trying to get advertised address from node label...
Trying to get node ExternalIP...
Found node ExternalIP: 10.247.33.xx
Using dynamic advertised address: 10.247.33.xx:30002
```

### 3. 使用网络检查工具验证连接

使用kafka_connection_checker.sh工具验证客户端连接：

```bash
# 运行完整连接测试
./tools/network_checker/kafka_connection_checker.sh -v

# 特别检查元数据获取
./tools/network_checker/kafka_connection_checker.sh --mode=metadata -v
```

确认以下测试通过：
- 基础连接测试
- 元数据获取
- 消息生产和消费

### 4. 测试Pod重新调度场景

为验证动态配置在Pod重新调度时的有效性，可以模拟这一场景：

```bash
# 删除其中一个Kafka Pod，让它重新调度
kubectl -n dci delete pod kafka-1

# 等待Pod重新创建
kubectl -n dci get pods -w | grep kafka-1
```

Pod重新创建后，重复步骤1-3，确认：
- 新Pod能正确获取其所在节点的地址
- advertised.listeners配置已正确更新
- 客户端仍能成功连接并获取元数据

### 5. 验证节点标签配置（可选）

如果您希望使用节点标签来明确指定advertised.listeners地址，可以：

```bash
# 获取节点名称
NODE_NAME=$(kubectl -n dci get pod kafka-0 -o jsonpath='{.spec.nodeName}')

# 为该节点添加标签
kubectl label node $NODE_NAME kafka.dci.io/advertised-host=10.247.33.xx

# 删除并重建Pod
kubectl -n dci delete pod kafka-0
```

Pod重建后，检查其配置和日志，验证是否使用了标签中指定的地址：

```bash
kubectl -n dci logs kafka-0 | grep -E "node label|Found advertised IP"
```

应看到类似输出：
```
Trying to get advertised address from node label...
Found advertised IP from node label: 10.247.33.xx
```

### 6. 验证集群功能

最后，确认Kafka集群的基本功能正常工作：

```bash
# 列出所有主题
kubectl -n dci exec kafka-0 -- kafka-topics.sh --bootstrap-server localhost:9092 --list

# 发送测试消息
kubectl -n dci exec kafka-0 -- kafka-console-producer.sh --bootstrap-server localhost:9092 --topic test <<< "测试消息 $(date)"

# 查看消息
kubectl -n dci exec kafka-0 -- kafka-console-consumer.sh --bootstrap-server localhost:9092 --topic test --from-beginning --max-messages 1
```

## 故障排除

如果验证过程中遇到问题，可以检查：

1. **Pod日志**：查看详细的启动过程和配置生成日志
   ```bash
   kubectl -n dci logs kafka-0
   ```

2. **节点信息**：确认节点的IP地址信息是否正确
   ```bash
   kubectl get node -o wide
   kubectl describe node <节点名称>
   ```

3. **网络连接**：验证节点间以及客户端到节点的网络连接
   ```bash
   # 从客户端机器运行
   ./tools/network_checker/kafka_connection_checker.sh --mode=network -v
   ```

4. **回退到手动配置**：如果自动配置失败，可以使用节点标签方式手动指定地址
   ```bash
   kubectl label node <节点名称> kafka.dci.io/advertised-host=<IP地址> --overwrite
   ```

## 预期结果

成功部署和验证后，您应该观察到：

1. Kafka Pod能够自动检测并配置正确的advertised.listeners地址
2. Pod重新调度后，新的配置自动适应新节点的网络环境
3. 客户端（包括应用程序和管理工具）能够始终与Kafka集群建立连接
4. 消息生产和消费正常工作

这表明动态配置方案运行正常，解决了静态IP绑定问题。 