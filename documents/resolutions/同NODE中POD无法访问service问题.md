# Kubernetes 同一 Node 节点内的 Pod 无法通过 Service IP 互访问题解决指引

## 问题描述

在 中信阿里云 Kubernetes 集群中，遇到一个现象：当两个 Pod (例如 Pod A 和 Pod B) 位于同一个 Node 节点上时，Pod A 无法通过 Service 的 ClusterIP 或 NodePort 访问 Pod B 提供的服务，而位于不同节点上的 Pod 则可以正常访问。访问失败时通常表现为连接超时或连接被拒绝。

## 问题原因

此问题的根本原因在于 `kube-proxy` 组件处理网络流量的方式，尤其是在其默认配置下：

1.  **DNAT (目标地址转换)**: 当 Pod A 访问 Service IP 时，`kube-proxy` 会将请求的目标 IP 从 Service IP 转换为后端 Pod B 的实际 IP 地址。这是 Service 负载均衡的基础。
2.  **缺少 SNAT (源地址转换)**: 在默认配置（特别是 `masqueradeAll: false`）下，`kube-proxy` **不会**修改请求的源 IP 地址（即 Pod A 的 IP）。
3.  **同节点通信路径**: Pod B 收到请求后，看到源 IP 是同一节点上的 Pod A 的 IP。由于它们在同一节点，网络路径通常不经过节点网关。Pod B 会直接将响应包发送回 Pod A 的 IP 地址。
4.  **连接跟踪失败**: Pod A 发送请求的目标是 Service IP，但收到的响应包的源 IP 是 Pod B 的 IP。由于请求的目标 IP 和响应的源 IP 不匹配，Pod A 的网络栈会认为这个响应包与其发出的连接无关，并将其丢弃，导致连接建立失败。

可以参考这篇博客文章中的详细抓包分析：[Kubernetes 同一个 Node 节点内的 Pod 不能通过 Service 互访](https://www.cnblogs.com/longgor/p/13588191.html)

## 解决方案

解决方案是修改 `kube-proxy` 的配置，强制其对所有通过 Service IP 访问的流量进行 SNAT（源地址伪装），通常是将源 IP 地址伪装成节点自身的 IP 地址。这样，后端 Pod B 收到的请求源 IP 就是节点 IP，响应会先发送给节点，`kube-proxy` 再正确地将响应转发给 Pod A，从而解决 IP 不匹配的问题。

具体操作步骤如下：

1.  **定位 `kube-proxy` 配置**:
    *   在我们的场景中，`kube-proxy` 是通过 DaemonSet `kube-proxy-master` (位于 `kube-system` 命名空间) 部署的。
    *   其配置是通过容器的 `command` 命令行参数传递的。

2.  **修改 `kube-proxy` DaemonSet**:
    *   编辑 `kube-proxy-master` DaemonSet：
        ```bash
        kubectl edit ds kube-proxy-master -n kube-system
        ```
    *   在 `spec.template.spec.containers` 下找到名为 `kube-proxy-master` 的容器，并在其 `command` 数组中添加以下两个参数：
        *   `--proxy-mode=iptables`：虽然这在 v1.12 中是默认值，但显式指定可以增加配置的明确性。
        *   `--masquerade-all=true`：**这是解决问题的关键参数**，强制进行 SNAT。

3.  **应用更改并重启 Pod**:
    *   保存编辑后的 DaemonSet YAML 文件。Kubernetes 会自动进行滚动更新，用新的配置替换旧的 `kube-proxy` Pod。
    *   可以通过以下命令观察 Pod 的更新状态：
        ```bash
        kubectl get pods -n kube-system -l k8s-app=kube-proxy-master -w
        ```

**配置修改前后对比：**

### 修改前 (部分 YAML):

```yaml
# ... (其他 DaemonSet 配置) ...
    spec:
      containers:
        - command:
            - /usr/local/bin/kube-proxy
            - '--kubeconfig=/var/lib/kube-proxy/kubeconfig.conf'
            - '--cluster-cidr=**********/16'
            - '--hostname-override=$(NODE_NAME)'
          # ... (其他容器配置) ...
# ... (其他 DaemonSet 配置) ...
```

### 修改后 (部分 YAML):

```yaml
# ... (其他 DaemonSet 配置) ...
    spec:
      containers:
        - command:
            - /usr/local/bin/kube-proxy
            - '--kubeconfig=/var/lib/kube-proxy/kubeconfig.conf'
            - '--cluster-cidr=**********/16'
            - '--hostname-override=$(NODE_NAME)'
            - '--proxy-mode=iptables'   # <--- 显式指定模式
            - '--masquerade-all=true'   # <--- 添加此行解决问题
          # ... (其他容器配置) ...
# ... (其他 DaemonSet 配置) ...
```

## 验证方法

在所有节点的 `kube-proxy` Pod 都更新并成功运行后，重新尝试之前失败的操作：在同一个 Node 上的 Pod A 中，通过 Service IP 或 NodePort 访问 Pod B 提供的服务。此时连接应该能够成功建立。

例如：
```bash
# 假设 Pod A 和 Pod B 在同一节点，nginx-service 是 Pod B 的 Service
kubectl exec <pod-a-name> -- curl http://nginx-service:<service-port>
```
如果能够正常收到来自 Pod B 的响应，则说明问题已解决。