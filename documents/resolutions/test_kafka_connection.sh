#!/bin/bash

# Kafka连接测试脚本
# 用于测试Kafka配置修复后的客户端连接

set -e

echo "===== Kafka连接测试脚本 ====="
echo "测试Kafka配置修复后的客户端连接"
echo ""

# 切换到项目目录
cd "$(dirname "$0")/../../dci-monitor/bin"

# 定义Kafka引导服务器地址
BOOTSTRAP_SERVER="10.247.33.12:30002"

echo "1. 运行基本连接和DNS检查测试..."
./dci-monitor kafka-client --bootstrap=${BOOTSTRAP_SERVER} --dnscheck --netcheck
echo ""

echo "2. 运行元数据获取测试..."
./dci-monitor kafka-client --bootstrap=${BOOTSTRAP_SERVER} --metadata --verbose
echo ""

echo "3. 测试发送消息到test主题..."
./dci-monitor kafka-client --bootstrap=${BOOTSTRAP_SERVER} --producer --topic=test --message="测试消息-$(date "+%Y%m%d%H%M%S")"
echo ""

echo "4. 测试从test主题消费消息(5秒)..."
timeout 5s ./dci-monitor kafka-client --bootstrap=${BOOTSTRAP_SERVER} --consumer --topic=test || true
echo ""

echo "===== 测试完成 ====="
echo "如果上述测试都成功执行，说明Kafka连接问题已修复。"
echo "如果仍然出现连接问题，请检查advertised.listeners配置是否正确生效。" 