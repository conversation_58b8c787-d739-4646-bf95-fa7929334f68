# Kafka KRaft 模式部署问题与解决方案总结

2025年04月23日

## 问题回顾

本次在 Kubernetes 上部署 KRaft 模式的 Kafka 集群过程中，遇到问题总结：

1.  **KRaft 配置复杂性:**
    *   相较于 Zookeeper 模式，KRaft 需要更精确地配置 `node.id`, `process.roles`, `controller.quorum.voters`, 监听器 (`listeners`, `advertised.listeners`, `controller.listener.names`) 等参数。确保这些参数根据 Pod 的序号和集群网络环境动态且正确地生成是第一个挑战。

2.  **环境变量注入失败:**
    *   最初尝试通过 `KAFKA_CFG_*` 环境变量（特别是 `KAFKA_CFG_NODE_ID`）将配置注入容器，依赖 Bitnami Kafka 镜像的入口点脚本进行处理。
    *   实践发现，在使用自定义 `command` 或特定场景下，入口点脚本并未按预期读取或应用这些环境变量，导致 Kafka 启动时因缺少 `node.id` 而失败。

3.  **卷权限问题:**
    *   在使用 NFS 作为持久化存储时，尝试在 Init Container 或 Pod 启动脚本中使用 `chown` 命令修改挂载卷的所有权以匹配 Kafka 进程用户 (UID 1001)，遇到了 `Operation not permitted` 错误。
    *   最终解决方案是移除脚本中的 `chown`/`chmod` 操作，改为在 StatefulSet 的 `spec.template.spec.securityContext` 中设置 `fsGroup: 1001`，利用 Kubernetes 的机制来确保 Pod 对挂载卷具有正确的组写入权限。

4.  **DNS 解析失败与启动死锁 (核心问题):**
    *   在 Pod 基本能够启动后，观察到 Pod 之间无法通过 Headless Service (`kafka-headless`) 提供的 FQDN (`kafka-*.kafka-headless.dci.svc.cluster.local`) 进行相互发现。Kafka 日志中反复出现 `java.net.UnknownHostException`。
    *   **根本原因分析:** 这是一个典型的 StatefulSet 启动依赖死锁：
        *   Kafka Pod (KRaft Controller/Broker) 启动时需要通过 DNS 解析其他节点的地址来建立 KRaft Quorum（法定人数）。
        *   在 Quorum 建立之前，Kafka 服务未完全就绪，无法通过 Readiness Probe（就绪探针）。
        *   由于 Pod 未 Ready，Kubernetes 将其 IP 地址放入关联的 Headless Service Endpoints 对象的 `notReadyAddresses` 列表。
        *   Kubernetes DNS 服务（如 CoreDNS）默认**不会**为 `notReadyAddresses` 列表中的 IP 创建 DNS A 记录。
        *   因此，Kafka Pod 无法通过 DNS 解析到其他节点地址，无法形成 Quorum，永远无法 Ready，形成死循环。
    *   **进一步诊断:** 通过 `kubectl get endpoints kafka-headless -n dci -o yaml` 确认了所有 Pod IP 确实只存在于 `notReadyAddresses` 字段，验证了死锁的存在。

## 解决方案

针对上述问题，采取了以下解决方案：

1.  **直接配置生成与启动脚本:**
    *   放弃依赖环境变量注入关键配置的方式。
    *   在 `kafka.yaml` StatefulSet 的 `spec.template.spec.containers[0].args` 中编写了一个明确的启动脚本。
    *   该脚本负责：
        *   从 Pod 主机名 (`${HOSTNAME}`) 动态提取序号 (`ORDINAL`)。
        *   基于 ConfigMap 中的 `server-template.properties` 模板文件，为每个 Pod 动态生成独立的、包含正确 `node.id`, `listeners`, `advertised.listeners` (使用 Pod FQDN), 和 `controller.quorum.voters` (包含所有节点 FQDN) 的配置文件 (`/opt/bitnami/kafka/config/server-${ORDINAL}.properties`)。
        *   检查持久卷中是否已存在 `meta.properties` 文件，判断是否需要执行 KRaft 存储格式化 (`kafka-storage.sh format -t <cluster_id> -c <config_file>`)，确保只在首次启动时格式化。
        *   使用生成的特定配置文件显式启动 Kafka 服务 (`kafka-server-start.sh /opt/bitnami/kafka/config/server-${ORDINAL}.properties`)。

2.  **打破 DNS 启动死锁 (关键修复):**
    *   修改 `kafka-headless` Service 的定义文件 (`kafka/kafka-service.yaml`)。
    *   在 Service `spec` 下添加关键配置项：**`publishNotReadyAddresses: true`**。
    *   **作用解释:** 该配置指示 Kubernetes Endpoint Controller 将**所有**匹配 Service Selector 的 Pod（无论其 Ready 状态如何）的 IP 地址都发布到 Endpoints 对象的 `addresses` 字段。这使得 Kubernetes DNS 服务能够为所有 Pod（包括启动中、尚未 Ready 的 Pod）创建 DNS A 记录。
    *   **效果:** Kafka Pod 在启动初期即使尚未 Ready，也能通过 DNS 成功解析到其他 Pod 的地址，从而能够建立 KRaft Quorum，完成初始化，最终通过 Readiness Probe，打破了启动死循环。

## 最终结论

成功部署 KRaft Kafka 集群的关键在于理解并解决 StatefulSet 应用在启动过程中对 Pod 间服务发现的依赖性问题。利用 Kubernetes Service 的 `publishNotReadyAddresses: true` 特性来确保 DNS 记录在 Pod 就绪前即可用。 