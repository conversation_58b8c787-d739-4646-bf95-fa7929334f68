#!/bin/bash

# Kafka客户端编译测试脚本
# 用于编译和测试修改后的Kafka客户端工具

set -e

echo "=== 编译和测试Kafka客户端工具 ==="

# 切换到项目目录
cd "$(dirname "$0")/../../dci-monitor/src/dcimonitor"

echo "当前目录: $(pwd)"
echo "检查代码格式..."
go fmt ./cmd/kafka_client.go > /dev/null

echo "编译代码..."
go build -o ../../bin/dci-monitor main.go

echo "编译成功！"

echo "============================================="
echo "Kafka客户端工具已编译，可使用以下命令进行测试:"
echo " ../../bin/dci-monitor kafka-client --bootstrap=10.247.33.12:30002 --dnscheck --netcheck"
echo " ../../bin/dci-monitor kafka-client --bootstrap=10.247.33.12:30002 --metadata --verbose"
echo "============================================="

# 确认是否要运行测试
read -p "是否要立即运行网络检查测试? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]
then
    echo "执行网络检查测试..."
    ../../bin/dci-monitor kafka-client --bootstrap=10.247.33.12:30002 --dnscheck --netcheck
fi

read -p "是否要运行元数据获取测试? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]
then
    echo "执行元数据获取测试..."
    ../../bin/dci-monitor kafka-client --bootstrap=10.247.33.12:30002 --metadata --verbose
fi

echo "测试完成！" 