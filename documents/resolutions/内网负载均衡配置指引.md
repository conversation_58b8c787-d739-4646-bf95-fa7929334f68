
## 中信阿里云 Kubernetes 服务内网负载均衡新增 TCP 端口配置

以新增暴露 Kibana 服务端口 `5601` 为例：

### 0. (可选) 配置 Ingress 路由规则 (针对 HTTP/HTTPS 服务)

如果需要通过域名访问内部服务（通常是 HTTP/HTTPS 服务），需要配置 Ingress 规则。对于纯 TCP 端口转发（如本例的 `5601`），此步骤不是直接必须的，但如果 Kibana 也需要通过域名访问其 Web 界面，则需要配置。

*   导航至 **容器服务 Kubernetes 版** -> **路由与负载均衡** -> **路由**。
*   选择对应的集群。
*   点击 **创建**。
*   配置规则：
    *   **名称**: 为 Ingress 资源命名（例如 `dci-kibana`）。
    *   **规则**:
        *   **域名**: 输入用于访问服务的域名 (例如 `dcikibanatest.intra.citic-x.com`)。
        *   **路径**: 配置访问路径 (例如 `/`)。
        *   **服务**: 选择目标服务 (例如 `kibana-service`) 和服务端口 (例如 `5601` 或 Kibana 的 HTTP 端口)。
    *   **注解 (Annotations)**: 可能需要添加特定的注解来配置 Nginx Ingress Controller 的行为。
    *   选择 开启权重。
    *   **(可选) 开启 TLS**: 如果需要 HTTPS 访问，可以配置 TLS 证书。
*   点击 **创建**。

**注意**: 此步骤主要用于 Layer 7 (HTTP/HTTPS) 路由。对于 Layer 4 (TCP/UDP) 端口暴露，核心是下面的 LoadBalancer Service 和 `tcp-services` ConfigMap 配置。

### 1. 编辑 LoadBalancer Service

*   导航至 **容器服务 Kubernetes 版** -> **路由与负载均衡** -> **服务**。
*   选择对应的集群和 `kube-system` 命名空间。
*   找到类型为 `LoadBalancer` 的服务（通常名为 `nginx-ingress-lb`）。
*   点击 **查看YAML** 或 **编辑**。
*   在 `spec.ports` 列表中添加新的端口配置，类似如下：

    ```yaml
    # ... (其他端口) ...
    - name: kibana-port  # 自定义名称
    nodePort: 32319  # 选择一个未使用的 NodePort
      # port: 5601       # 自动生成对外暴露的端口，不写
      protocol: TCP
      targetPort: 5601   # 目标端口，通常与 port 相同
    ```
*   保存更改。阿里云会自动更新关联的 SLB 实例，添加对 `5601` 端口的监听

### 2. 配置 Nginx TCP 服务转发

*   导航至 **容器服务 Kubernetes 版** -> **应用配置** -> **配置项**。
*   选择对应的集群和 `kube-system` 命名空间。
*   找到名为 `tcp-services` 的 ConfigMap（如果不存在，需根据 Nginx Ingress 的部署方式确认或创建）。
*   点击 **查看YAML** 或 **编辑**。
*   在 `data` 字段中添加新的端口映射，格式为 `<external-port>: "<namespace>/<service-name>:<service-port>"`：

    ```yaml
    data:
      # key 是对外暴露的端口 (Service 中定义的 port)
      # value 是 "目标命名空间/目标服务名:目标服务端口"
      "5601": "dci/kibana-service:5601" # 将外部 5601 端口流量转发给 dci 命名空间下的 kibana-service 服务的 5601 端口
      # ... (其他已有的 TCP 映射) ...
    ```
    注意使用 POD 的端口。
*   保存更改。Nginx Ingress Controller 会加载此配置，并将通过 SLB (`*************:5601`) 进来的 TCP 流量转发到 `dci/kibana-service:5601`。

### 3. 验证

*   确认 `nginx-ingress-lb` Service 的外部 IP (`status.loadBalancer.ingress.ip`)。
*   在阿里云 SLB 控制台确认监听器已成功添加。
*   从集群内网可达的机器尝试访问 `*************:5601`，应能连接到 Kibana 服务。

## 参考

参考 https://www.jianshu.com/p/46d9c327a7c7
