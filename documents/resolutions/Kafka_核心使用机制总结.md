# Kafka 核心使用机制总结


## 1. 分区 (Partitions) - 实现伸缩性与并行处理

*   **概念:** 一个 Kafka 主题 (Topic) 可以被划分为一个或多个分区。每个分区是一个独立的、有序的日志流。
*   **目的:** 主要为了提高 Kafka 的**伸缩性 (Scalability)** 和**吞吐量 (Throughput)**。
    *   **并行写入:** 生产者可以同时向不同分区发送消息。
    *   **并行消费:** 一个消费者组内的多个消费者实例可以同时从不同的分区读取消息。
*   **我们设置了 3 个分区:** 对于 `test-topic`（下文测试示例），设置 3 个分区允许最多 3 个属于**同一个消费者组**的消费者实例**并行**工作，每个实例负责一个分区。
*   **顺序保证:** Kafka 只保证在一个**分区内部**的消息是严格有序的。不同分区之间的消息顺序不保证。
*   **注意:** 一条消息只会属于**一个**分区。

## 2. 副本 (Replicas) / 复制因子 (Replication Factor) - 实现高可用与容错

*   **概念:** 为了数据冗余和故障恢复，每个分区可以有多个副本，这些副本必须分布在不同的 Broker 节点上。
*   **复制因子:** 指每个分区总共的副本数量（1 个 Leader + N 个 Follower）。
*   **Leader 与 Follower:** 每个分区有且仅有一个 Leader 副本负责处理所有读写请求，其他 Follower 副本从 Leader 同步数据。
*   **高可用性:** 如果持有 Leader 副本的 Broker 宕机，Kafka 会自动从同步的 Follower 中选举出新的 Leader，确保服务几乎不中断且数据不丢失。
*   **我们设置了 3 个副本 (复制因子=3):** 在 3 节点的 Kafka 集群中，设置复制因子为 3 是最高容错配置。`test-topic` 的每个分区的 3 个副本会分布在 3 个不同的 Broker 上。即使任意一个 Broker 宕机，该主题仍然可用且数据完整。

## 3. 消费者组 (Consumer Groups) - 控制消息分发与消费进度

*   **概念:** 一个或多个消费者实例可以组成一个消费者组，由一个唯一的组 ID (Group ID) 标识。
*   **核心原则:** 一个主题的**一个分区**在任意时刻最多只能被**同一个消费者组内的一个消费者实例**所消费。
*   **目的:**
    *   **实现并行消费:** 如上所述，组内的多个消费者可以并行处理不同分区。
    *   **负载均衡:** Kafka 自动在组内消费者之间分配分区。
    *   **点对点/队列模式:** 确保组内消息只被处理一次，不会重复消费。
*   **澄清:** "3 个分区允许最多 3 个消费者同时消费" 是指**同一个消费者组内**的 3 个消费者会分别处理 3 个不同分区的消息，**不会收到重复消息**。
*   **Offset 跟踪:** Kafka 为每个消费者组记录其在每个分区上的消费进度（Offset）。
*   **`--from-beginning` vs. 固定 Group ID:**
    *   `kafka-console-consumer --from-beginning`: 每次启动都使用随机 Group ID，并强制从头读取，导致重复看到所有消息。
    *   `kafka-console-consumer --group <fixed_group_id>`: 使用固定的 Group ID，Kafka 会记录该组的 Offset。移除 `--from-beginning` 后，再次启动会从上次记录的 Offset 继续消费，只读取新消息，实现"消费掉"（不重复读旧消息）的效果。

## 4. 数据保留策略 (Retention Policies) - 管理存储空间

*   **持久化存储:** Kafka 默认会持久化存储所有写入的消息，消费消息并不会删除它们。
*   **自动删除:** 为了防止存储空间无限增长，Kafka 提供数据保留策略来自动删除旧消息。
*   **主要策略:**
    *   **基于时间 (`log.retention.hours`, `ms`等):** 删除超过指定时间的消息（例如，配置的 `log.retention.hours=168` 会删除超过 7 天的数据）。
    *   **基于大小 (`log.retention.bytes`):** 当分区日志大小达到阈值时，删除最早的消息以控制大小。
*   **结论:** 无需担心 Kafka 会耗尽存储，配置的保留策略会自动清理旧数据。但仍需监控磁盘使用，确保配置的保留时间和存储空间与业务需求和数据写入速率相匹配。

## 5. Kafka 功能测试示例 (`test-topic`)

在 Kafka 集群稳定运行后，可以通过以下步骤使用 Kafka 自带的命令行工具来测试其基本功能：

**前提:** 假设您已部署了一个 3 节点的 Kafka 集群，并且 kubectl 配置正确。

**步骤 1: 创建测试 Topic**

*   **目的:** 创建一个名为 `test-topic` 的主题，包含 3 个分区（用于并行处理）和 3 个副本（用于高可用，分布在 3 个 Broker 上）。
*   **命令 (在本地终端执行):**
    ```bash
    kubectl exec -it kafka-0 -n dci -- \
      kafka-topics.sh --create \
      --topic test-topic \
      --partitions 3 \
      --replication-factor 3 \
      --bootstrap-server localhost:9092
    ```
*   **预期输出:** 类似 `Created topic test-topic.` 的成功消息。

**步骤 2: 启动消息消费者 (在一个终端)**

*   **目的:** 启动一个消费者来监听 `test-topic` 并打印接收到的消息。
*   **命令 (在一个终端窗口执行):**
    ```bash
    # 方式一：每次都从头读取 (用于观察所有历史消息)
    kubectl exec -it kafka-0 -n dci -- \
      kafka-console-consumer.sh --topic test-topic \
      --from-beginning \
      --bootstrap-server localhost:9092

    # 方式二：使用固定消费者组，从上次位置继续 (模拟实际消费场景)
    kubectl exec -it kafka-0 -n dci -- \
      kafka-console-consumer.sh --topic test-topic \
      --bootstrap-server localhost:9092 \
      --group my-test-consumer-group 
    ```
*   **行为:** 消费者启动后会保持运行，等待消息。

**步骤 3: 启动消息生产者并发送消息 (在另一个终端)**

*   **目的:** 启动一个生产者，手动输入消息并发送到 `test-topic`。
*   **命令 (在另一个终端窗口执行):**
    ```bash
    kubectl exec -it kafka-0 -n dci -- \
      kafka-console-producer.sh --topic test-topic \
      --bootstrap-server localhost:9092
    ```
*   **操作:** 命令执行后，光标会等待输入。输入文本内容，每行按 Enter 发送。
    ```
    > Hello Kafka!
    > This message goes to test-topic.
    > KRaft mode test successful.
    ```

**步骤 4: 观察消费者输出**

*   **目的:** 验证消费者是否能接收到生产者发送的消息。
*   **操作:** 切换到运行消费者的终端窗口。
*   **预期结果:** 如果测试成功，您应该能看到在生产者处输入的文本消息被打印出来。

**测试完成:**

*   以上步骤成功执行，表明 Kafka 集群的主题创建、消息生产和消费功能正常工作。
*   使用 `Ctrl+C` 可以停止生产者和消费者进程。 