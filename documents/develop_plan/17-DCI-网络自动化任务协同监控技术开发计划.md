---
title: |
  DCI-网络自动化任务协同监控技术开发计划

subtitle: |
  基于现有代码结构的功能开发实施计划
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-07-31 | 顾铠羟 | 初始版本           |

# 1. 概述

## 1.1 开发目标

基于现有DCI监控系统代码结构，实现网络自动化任务协同监控功能，为网络配置变更提供全方位的监控支持和影响评估。

## 1.2 依据技术设计文档

本开发计划基于《17-DCI-网络自动化任务协同监控技术方案设计.md》中的详细技术方案设计，实现以下核心功能：

- ✅任务信号接收与处理机制
- ✅任务监控会话创建与管理
- ✅任务相关数据采集与分析流程
- ✅任务告警关联与统计查询
- ✅与网络自动化控制系统的接口定义

## 1.3 技术约束

### 1.3.1 架构规范约束
- **必须**遵循Handler → Service → DAO架构模式
- **必须**按功能模块进行代码组织
- **严禁**跨层依赖和反向依赖
- 每个模块包含：handler.go、service.go、dao.go、model.go

### 1.3.2 技术栈约束
- **语言**：Go 1.24.2
- **Web框架**：Gin v1.9.1+
- **API设计**：RESTful风格，使用Swagger
- **数据库**：MySQL 8.0+，使用go-sql-driver/mysql
- **消息队列**：Apache Kafka 3.x+，使用IBM/sarama
- **配置管理**：Viper v1.18.2+
- **日志**：Zap v1.27.0+
- **时区处理**：统一使用Asia/Shanghai时区

### 1.3.3 代码规范约束
- **API设计**：遵循RESTful风格，使用标准响应格式
- **文档化**：所有API必须使用Swagger在代码中完整注释
- **错误处理**：严禁忽略error返回值
- **请求跟踪**：必须为每个请求生成唯一请求ID

## 1.4 现有代码分析

### 1.4.1 已实现功能
- **Alert模块**：完整的任务关联功能，包括设备ID提取、延展监测、TaskSession管理
- **基础模块**：topology、device、traffic、kafka等模块已按规范重构
- **数据表**：`monitor_network_auto_task_monitoring_sessions`、`monitor_alert`等相关表已存在

### 1.4.2 功能缺口
- ✅缺少独立的任务协同监控模块
- ✅缺少任务信号接收和处理机制
- ✅缺少数据收集和报告生成功能
- ✅缺少完整的任务协同监控API

# 2. 模块开发计划

## 2.1 新增模块结构

### 2.1.1 任务协同监控模块 (`internal/task_collaboration`)

创建独立的任务协同监控模块，负责管理整个任务监控生命周期。

**代码文件结构**：
```
internal/task_collaboration/
├── model.go              # ✅ 数据模型定义
├── dao.go                # ✅ 数据访问层
├── service.go            # ✅ 业务逻辑层
├── handler.go            # ✅ HTTP请求处理层
├── signal_processor.go   # ✅ 任务信号处理器
├── data_collector.go     # ✅ 数据收集器
├── init.go               # ✅ 模块初始化和生命周期管理
└── README.md             # ✅ 模块说明文档
```

**数据库文件结构**：
```
sql/task_collaboration/
├── task_collaboration.sql # ✅ 模块相关表结构
└── README.md              # ✅ 数据库说明文档
```

**核心数据模型**：
遵循《03-DCI项目API及代码规范指南.md》中的数据模型设计规范，包含：
- TaskSignal：任务信号数据结构
- TaskMonitoringRequest：任务监控请求结构
- TaskAlertStatistics：任务告警统计结构
- TimeRange、TimeSlotCount：时间相关辅助结构

### 2.1.2 任务信号处理模块 (`signal_processor.go`)

**功能职责**：
- 接收和验证任务信号
- 解析任务信号内容
- 触发相应的监控会话操作
- 发送处理结果通知

### 2.1.3 数据收集模块 (`data_collector.go`)

**功能职责**：
- 根据任务目标设备收集相关数据
- 从Prometheus查询性能指标
- 从日志系统查询相关日志
- 关联任务期间的告警信息

## 2.2 API接口开发

遵循《03-DCI项目API及代码规范指南.md》中的API设计规范，实现以下接口：

### 2.2.1 任务信号接收接口
- **路由**: `POST /api/v1/tasks/signals` ✅
- **功能**: 接收来自网络自动化控制系统的任务信号并创建监控会话 ✅

### 2.2.2 任务监控管理接口
- **启动监控**: `POST /api/v1/tasks/{taskId}/monitoring/start` ✅
- **停止监控**: `POST /api/v1/tasks/{taskId}/monitoring/manual/stop` ✅
- **查询监控状态**: `GET /api/v1/tasks/{taskId}/monitoring/status` ✅

### 2.2.3 任务告警查询接口
- **任务告警列表**: `GET /api/v1/tasks/{taskId}/alerts` ✅
- **任务告警统计**: `GET /api/v1/tasks/{taskId}/alerts/statistics` ✅

## 2.3 数据库扩展

### 2.3.1 任务告警关联表 ✅
基于现有的`monitor_alert`表，新增关联表：

```sql
-- 任务告警关联表
CREATE TABLE monitor_task_alert_associations (
    id VARCHAR(64) PRIMARY KEY COMMENT '关联记录唯一标识符',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    alert_id VARCHAR(64) NOT NULL COMMENT '告警ID',
    association_type ENUM('active_execution', 'post_completion') NOT NULL COMMENT '关联类型',
    device_id VARCHAR(64) NOT NULL COMMENT '设备ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(Asia/Shanghai)',
    INDEX idx_task_id (task_id),
    INDEX idx_alert_id (alert_id),
    INDEX idx_device_id (device_id),
    INDEX idx_association_type (association_type),
    FOREIGN KEY (alert_id) REFERENCES monitor_alert(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务告警关联表';
```

### 2.3.2 任务数据收集记录表 ✅

```sql
-- 任务数据收集记录表
CREATE TABLE monitor_task_data_collections (
    id VARCHAR(64) PRIMARY KEY COMMENT '记录唯一标识符',
    session_id VARCHAR(64) NOT NULL COMMENT '任务会话ID',
    data_type ENUM('metrics', 'logs', 'alerts', 'events') NOT NULL COMMENT '数据类型',
    device_id VARCHAR(64) NOT NULL COMMENT '设备ID',
    collection_start TIMESTAMP NOT NULL COMMENT '收集开始时间(Asia/Shanghai)',
    collection_end TIMESTAMP NULL COMMENT '收集结束时间(Asia/Shanghai)',
    data_count INT DEFAULT 0 COMMENT '收集的数据条数',
    status ENUM('collecting', 'completed', 'failed') NOT NULL DEFAULT 'collecting' COMMENT '收集状态',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(Asia/Shanghai)',
    INDEX idx_session_id (session_id),
    INDEX idx_data_type (data_type),
    INDEX idx_device_id (device_id),
    INDEX idx_status (status),
    FOREIGN KEY (session_id) REFERENCES monitor_network_auto_task_monitoring_sessions(session_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务数据收集记录表';
```

## 2.4 服务集成开发

### 2.4.1 与Alert模块集成 ✅

**扩展Alert Service**:
- ✅ 增强现有的`GetTaskByDeviceWithExtendedMonitoring`方法
- ✅ 实现批量告警关联处理
- ✅ 提供任务相关告警统计功能

### 2.4.2 与Kafka模块集成 ✅

**配置任务信号Topic**:
- ✅ Topic: `dci.task.signals`
- ✅ 接收网络自动化控制系统的任务信号
- ✅ 发送任务处理结果通知

### 2.4.3 与Prometheus集成 ✅

实现设备性能指标数据收集接口，包括：
- ✅ CPU使用率查询
- ✅ 内存使用率查询
- ✅ 网络接口流量查询
- ✅ 网络接口错误统计查询

# 3. 开发实施步骤

## 3.1 第一阶段：基础架构搭建 ✅

### 3.1.1 创建task_collaboration模块 ✅
1. ✅ 创建代码模块目录结构
2. ✅ 定义基础数据模型 (model.go)
3. ✅ 实现基础DAO层 (dao.go)
4. ✅ 创建基础Service层 (service.go)
5. ✅ 创建数据库扩展脚本

### 3.1.2 数据库扩展 ✅
1. ✅ 创建任务告警关联表的SQL脚本
2. ✅ 创建任务数据收集记录表的SQL脚本
3. ✅ 更新数据库初始化脚本

## 3.2 第二阶段：核心功能实现 ✅

### 3.2.1 任务信号处理 ✅
1. ✅ 实现SignalProcessor (signal_processor.go)
2. ✅ 实现任务信号验证逻辑
3. ✅ 实现任务开始/结束/中止的处理逻辑
4. ✅ 与现有TaskDAO集成

### 3.2.2 任务会话管理增强 ✅
1. ✅ 扩展现有TaskDAO功能
2. ✅ 实现任务会话状态管理
3. ✅ 实现延展监测逻辑完善
4. ✅ 集成数据收集触发机制

### 3.2.3 数据收集功能 ✅
1. ✅ 实现DataCollector (data_collector.go)
2. ✅ 集成Prometheus数据查询
3. ✅ 集成日志系统查询
4. ✅ 实现告警关联处理

## 3.3 第三阶段：API接口开发 ✅

### 3.3.1 HTTP Handler实现 ✅
1. ✅ 实现Handler层 (handler.go)
2. ✅ 实现任务信号接收接口
3. ✅ 实现任务监控管理接口
4. ✅ 实现任务告警查询接口

### 3.3.2 API文档完善 ✅
1. ✅ 添加完整的Swagger注释
2. ✅ 更新API文档
3. ✅ 提供接口使用示例

### 3.3.3 路由注册 ✅
1. ✅ 在cmd/server.go中注册新路由
2. ✅ 集成中间件（请求ID、日志等）
3. ✅ 配置API访问权限

## 3.4 第四阶段：服务集成 ✅

### 3.4.1 Alert模块集成 ✅
1. ✅ 扩展Alert Service的任务相关功能
2. ✅ 实现批量告警关联处理
3. ✅ 完善任务告警统计功能
4. ✅ 测试告警与任务关联的正确性

### 3.4.2 Kafka集成 ✅
1. ✅ 配置任务信号Topic
2. ✅ 实现Kafka消息消费者
3. ✅ 实现任务处理结果通知
4. ✅ 测试消息传递可靠性

### 3.4.3 监控指标集成 ✅
1. ✅ 实现Prometheus数据查询接口
2. ✅ 集成现有的traffic模块功能
3. ✅ 实现设备性能指标收集
4. ✅ 测试数据收集的准确性

## 3.5 第五阶段：测试与优化

### 3.5.1 单元测试
1. 为核心Service方法编写单元测试
2. 为DAO层方法编写单元测试
3. 为数据收集功能编写测试
4. 为API接口编写集成测试

### 3.5.2 功能测试
1. 测试完整的任务监控流程
2. 测试任务信号处理的正确性
3. 测试告警关联的准确性
4. 测试延展监测功能

### 3.5.3 完善文档和示例 ✅
1. ✅ 完善模块README文档
2. ✅ 提供API使用示例
3. ✅ 更新系统集成文档

## 3.6 阶段交付物检查

### 3.6.1 每阶段必须交付
- 可编译的代码
- 更新的配置文件
- 阶段性文档

### 3.6.2 最终交付检查清单
- [ ] 代码通过gofmt格式化
- [✅] 所有API具有完整Swagger文档
- [✅] 数据库脚本可执行
- [✅] 配置文件已更新
- [✅] README文档完整
- [ ] 单元测试覆盖核心功能
