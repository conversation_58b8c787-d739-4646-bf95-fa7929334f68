# API 需求文档：获取业务流量图和摘要数据 (平均/最大/最小/最近)

本文档描述了用于查询指定业务连接两端端口在特定时间窗口内的 **平均、最大、最小** 流量时间序列数据 (用于图表) 以及 **流量摘要** (最近/最小/平均/最大) 的 API 接口。

## 需求描述

http://wiki.cindustry.citic/pages/viewpage.action?pageId=81625323

流量图展示及摘要信息：
点击已开通业务列表-详情-性能标签：

展示要求：

1.  **流量摘要表:** 显示最近流量、时间段内最小、平均、最大流量。
    *   "最近流量" 仅在选择预设时间范围 (如 "最近1小时") 时显示实时速率；选择自定义时间段时，该列为空。
2.  **流量图:** 分别展示时间段内最大值、平均值、最小值的流量变化折线图。

查询配置：颗粒度设置、时间范围、自定义时间范围

 - 颗粒度设置：1分钟、5分钟、1小时(对应 月)、1天(对应 年)   

 - 时间范围：1小时、2小时、12小时、1天、7天、1月（自然月或最近30天可选）、1年  （冲突校验：选择1分钟时，只能选择1小时、2小时、12小时、1天；选择5分钟时，只能选择1小时、2小时、12小时、1天、7天、1月；选择1小时，只能选择1天、7天、1月、1年；选择一天时，只能选择1年）

 - 自定义时间范围：开始时间 - 结束时间

举例：

颗粒度1分钟，时间范围1天

## 接口概述

提供四类接口：三类用于获取图表所需的时间序列数据（平均、最大、最小），一类用于获取页面顶部的流量摘要信息。

*   **HTTP 方法:** `GET` (适用于所有接口)

## 通用请求参数 (Query Parameters)

以下参数适用于**所有**四个接口 (`/average`, `/maximum`, `/minimum`, `/summary`)：

| 参数名        | 类型   | 是否必需 | 描述                                                                 | 允许值/格式                                                                                                       |
| ------------- | ------ | -------- | -------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------- |
| `granularity` | string | **是**   | 数据聚合的时间粒度。                                                     | `"1m"`, `"5m"`, `"1h"`, `"1d"`                                                                                    |
| `time_range`  | string | 否       | 预设的时间范围（结束时间为当前）。与 `start_time`/`end_time` 互斥。       | `"1h"`, `"2h"`, `"12h"`, `"1d"`, `"7d"`, `"1M"` (最近30天), `"1Y"` (最近365天)                                       |
| `start_time`  | string | 否       | 自定义查询范围的开始时间。若提供，必须同时提供 `end_time`，且不能使用 `time_range`。 | RFC3339 UTC 格式 (e.g., `2023-10-27T10:00:00Z`)                                                                 |
| `end_time`    | string | 否       | 自定义查询范围的结束时间。若提供，必须同时提供 `start_time`，且不能使用 `time_range`。 | RFC3339 UTC 格式 (e.g., `2023-10-27T11:00:00Z`)                                                                 |
| `a_switch_id` | string | **是**   | 连接 A 端的交换机 ID。                                                | (例如: `"CE1"`)                                                                                                   |
| `a_port_id`   | string | **是**   | 连接 A 端的端口 ID (需要 URL 编码, e.g., `"GE1%2F0%2F1"`)。       | (例如: `"GE1/0/1"`)                                                                                             |
| `z_switch_id` | string | **是**   | 连接 Z 端的交换机 ID。                                                | (例如: `"CE2"`)                                                                                                   |
| `z_port_id`   | string | **是**   | 连接 Z 端的端口 ID (需要 URL 编码, e.g., `"GE1%2F0%2F2"`)。       | (例如: `"GE1/0/2"`)                                                                                             |

**注意:**

*   必须提供 `time_range` **或者** 同时提供 `start_time` 和 `end_time`。
*   A 端和 Z 端用于标识连接的两端，查询将返回这两端端口的入/出流量。

## 通用后端校验逻辑

服务器端会对请求参数执行严格校验 (适用于所有接口)：

1.  **参数完整性与互斥性:** 确保必需参数 (`granularity`, `a_switch_id`, `a_port_id`, `z_switch_id`, `z_port_id` 及时间参数) 存在，并检查 `time_range` 和 `start_time`/`end_time` 的互斥性。
2.  **参数格式与值校验:** 检查各参数是否为允许的值或符合指定格式。`start_time` 必须早于 `end_time`。端口 ID 格式校验。
3.  **时间范围与颗粒度冲突校验:**
    *   计算查询的总时间跨度 (Duration)。
    *   根据 `granularity` 检查 Duration 是否在允许范围内：
        *   `granularity=1m`: Duration <= 1 天
        *   `granularity=5m`: Duration <= 1 个月 (按30天计)
        *   `granularity=1h`: Duration <= 1 年 (按365天计)
        *   `granularity=1d`: Duration 必须 ≈ 1 年 (例如 360-370 天)
    *   若校验失败，返回 `400 Bad Request`。


## 图表数据接口 (`/chart/...`)

### 通用图表响应体结构

`/average`, `/maximum`, `/minimum` 接口的成功响应 (200 OK) 都遵循以下基本结构，区别在于 `series` 中数据的含义和 `name` 字段。

```json
{
    "request_id": "e9f1b2c3-d4e5-f6a7-b8c9-d0e1f2a3b4c5", // 示例 Request ID
    "query_details": {
        "a_switch_id": "CE1",
        "a_port_id": "GE1/0/1",
        "z_switch_id": "CE2",
        "z_port_id": "GE1/0/2",
        "granularity": "5m",            // 示例粒度
        "start_time": "2023-10-27T08:00:00Z", // 实际查询开始时间
        "end_time": "2023-10-27T14:55:00Z"   // 实际查询结束时间 (最后一个聚合窗口的开始时间)
    },
    "chart_data": {
        "unit": "Mbps", // 所有 series 的数据单位
        "timestamps": [ // 与 series 数据一一对应的时间戳数组 (RFC3339 UTC)
            "2023-10-27T08:00:00Z",
            "2023-10-27T08:05:00Z",
            // ... more timestamps ...
            "2023-10-27T14:55:00Z"
        ],
        "series": [ 
            // 具体 Series 结构见下方各接口描述
        ]
    }
}
```

### 接口 1.1: 获取平均流量图数据

*   **URL:** `/api/v1/traffic/chart/average`
*   **功能:** 查询指定时间窗口内，按 `granularity` 聚合后的 **平均** 流量时间序列值。

#### 成功响应示例 (Average Chart)

```json
// ... (省略 request_id, query_details)
    "chart_data": {
        "unit": "Mbps",
        "timestamps": [ /* ... timestamps ... */ ],
        "series": [ // 平均流量数据系列数组
            {
                "name": "A端 (CE1-GE1/0/1) 平均入流量", // 系列名称
                "data": [ 1.05, 1.10, /* ... */, 1.23 ] // 示例平均值数据 (假设单位是Mbps)
            },
            {
                "name": "A端 (CE1-GE1/0/1) 平均出流量",
                "data": [ 2.10, 2.15, /* ... */, 2.34 ]
            },
            {
                "name": "Z端 (CE2-GE1/0/2) 平均入流量",
                "data": [ 0.98, 1.02, /* ... */, 1.12 ]
            },
            {
                "name": "Z端 (CE2-GE1/0/2) 平均出流量",
                "data": [ 1.95, 2.01, /* ... */, 2.23 ]
            }
        ]
    }
// ...
```

### 接口 1.2: 获取最大流量图数据

*   **URL:** `/api/v1/traffic/chart/maximum`
*   **功能:** 查询指定时间窗口内，按 `granularity` 聚合后的 **最大** (峰值) 流量时间序列值。

#### 成功响应示例 (Maximum Chart)

```json
// ... (省略 request_id, query_details)
    "chart_data": {
        "unit": "Mbps",
        "timestamps": [ /* ... timestamps ... */ ],
        "series": [ // 最大流量数据系列数组
            {
                "name": "A端 (CE1-GE1/0/1) 最大入流量", // 系列名称
                "data": [ 1.50, 1.65, /* ... */, 1.70 ] // 示例最大值数据 (假设单位是Mbps)
            },
            {
                "name": "A端 (CE1-GE1/0/1) 最大出流量",
                "data": [ 3.05, 3.10, /* ... */, 3.23 ]
            },
            {
                "name": "Z端 (CE2-GE1/0/2) 最大入流量",
                "data": [ 1.40, 1.45, /* ... */, 1.54 ]
            },
            {
                "name": "Z端 (CE2-GE1/0/2) 最大出流量",
                "data": [ 2.98, 3.02, /* ... */, 3.12 ]
            }
        ]
    }
// ...
```

### 接口 1.3: 获取最小流量图数据

*   **URL:** `/api/v1/traffic/chart/minimum`
*   **功能:** 查询指定时间窗口内，按 `granularity` 聚合后的 **最小** (谷值) 流量时间序列值。

#### 成功响应示例 (Minimum Chart)

```json
// ... (省略 request_id, query_details)
    "chart_data": {
        "unit": "Mbps",
        "timestamps": [ /* ... timestamps ... */ ],
        "series": [ // 最小流量数据系列数组
            {
                "name": "A端 (CE1-GE1/0/1) 最小入流量", // 系列名称
                "data": [ 0.50, 0.55, /* ... */, 0.62 ] // 示例最小值数据 (假设单位是Mbps)
            },
            {
                "name": "A端 (CE1-GE1/0/1) 最小出流量",
                "data": [ 1.05, 1.11, /* ... */, 1.23 ]
            },
            {
                "name": "Z端 (CE2-GE1/0/2) 最小入流量",
                "data": [ 0.48, 0.51, /* ... */, 0.53 ]
            },
            {
                "name": "Z端 (CE2-GE1/0/2) 最小出流量",
                "data": [ 0.95, 1.05, /* ... */, 1.04 ]
            }
        ]
    }
// ...
```

## 接口二: 获取流量摘要数据

*   **URL:** `/api/v1/traffic/summary`
*   **功能:** 查询指定时间窗口内，A/Z 端端口流量的 **摘要统计信息** (最近流量、最小流量、平均流量、最大流量)。
*   **请求参数:** 与图表接口 (`/chart/...`) 完全相同。

### 成功响应 (200 OK)

```json
{
    "request_id": "a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6", // 示例 Request ID
    "query_details": { // 与图表接口相同的查询详情
        "a_switch_id": "CE1",
        "a_port_id": "GE1/0/1",
        "z_switch_id": "CE2",
        "z_port_id": "GE1/0/2",
        "granularity": "1h",            // 示例粒度
        "start_time": "2023-10-26T00:00:00Z", // 实际查询开始时间
        "end_time": "2023-10-27T00:00:00Z"   // 实际查询结束时间
    },
    "summary_data": {
        "unit": "Mbps", // 所有流量值的单位
        "items": [      // 摘要信息数组，每个对象代表表格的一行
            {
                "name": "A端入流量",
                // "latest" 字段仅在请求使用 `time_range` 时出现且有值；
                // 若使用 `start_time`/`end_time`, 此字段为 null 或不存在。
                "latest": 1.15,     // 示例: 最近一次的流量速率 (nullable)
                "minimum": 0.98,    // 示例: 时间段内最小值
                "average": 1.05,    // 示例: 时间段内平均值
                "maximum": 1.50     // 示例: 时间段内最大值
            },
            {
                "name": "A端出流量",
                "latest": 2.25,
                "minimum": 1.95,
                "average": 2.10,
                "maximum": 3.05
            },
            {
                "name": "Z端入流量",
                "latest": 1.08,
                "minimum": 0.95,
                "average": 1.02,
                "maximum": 1.40
            },
            {
                "name": "Z端出流量",
                // "latest": null, // 使用自定义时间范围时的示例
                "minimum": 1.90,
                "average": 2.01,
                "maximum": 2.98
            }
        ]
    }
}
```

## 通用失败响应

所有接口 (`/chart/...` 和 `/summary`) 的失败响应格式相同：

*   **400 Bad Request:** 请求参数错误。
    ```json
    {
        "request_id": "...",
        "error": "参数校验失败",
        "details": "时间范围 '7d' 不支持 '1m' 的颗粒度。" // 或其他参数错误信息
    }
    ```
*   **404 Not Found:** 指定的交换机或端口不存在。
    ```json
    {
        "request_id": "...",
        "error": "资源未找到",
        "details": "交换机 'CE1' 或端口 'InvalidPort' 未找到。"
    }
    ```
*   **500 Internal Server Error:** 服务器内部错误。
    ```json
    {
        "request_id": "...",
        "error": "服务器内部错误"
    }
    ```

## 通用请求示例

假设 API 服务器地址为 `http://your-api-server.com`。
查询 CE1 的 GE1/0/1 端口与 CE2 的 GE1/0/2 端口之间的连接流量。

**示例 1: 平均流量图 - 最近 1 小时，1 分钟颗粒度**
(端口ID中的 `/` 需要 URL 编码为 `%2F`)

```
GET /api/v1/traffic/chart/average?granularity=1m&time_range=1h&a_switch_id=CE1&a_port_id=GE1%2F0%2F1&z_switch_id=CE2&z_port_id=GE1%2F0%2F2
```

**示例 2: 最大流量图 - 最近 7 天，5 分钟颗粒度**

```
GET /api/v1/traffic/chart/maximum?granularity=5m&time_range=7d&a_switch_id=CE1&a_port_id=GE1%2F0%2F1&z_switch_id=CE2&z_port_id=GE1%2F0%2F2
```

**示例 3: 最小流量图 - 自定义时间范围 (2023-10-26 08:00 - 12:00 UTC)，5 分钟颗粒度**
(时间和端口ID都需要 URL 编码)

```
GET /api/v1/traffic/chart/minimum?granularity=5m&start_time=2023-10-26T08%3A00%3A00Z&end_time=2023-10-26T12%3A00%3A00Z&a_switch_id=CE1&a_port_id=GE1%2F0%2F1&z_switch_id=CE2&z_port_id=GE1%2F0%2F2
```

**示例 4: 流量摘要 - 最近 2 小时，5 分钟颗粒度**

```
GET /api/v1/traffic/summary?granularity=5m&time_range=2h&a_switch_id=CE1&a_port_id=GE1%2F0%2F1&z_switch_id=CE2&z_port_id=GE1%2F0%2F2
```

**示例 5: 触发冲突校验错误 (适用于所有接口)**
(7 天范围，1 分钟颗粒度)

```
GET /api/v1/traffic/chart/average?granularity=1m&time_range=7d&a_switch_id=CE1&a_port_id=GE1%2F0%2F1&z_switch_id=CE2&z_port_id=GE1%2F0%2F2
```
(预期返回 400 Bad Request)
