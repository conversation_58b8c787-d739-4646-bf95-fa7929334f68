# DCI监测系统Kafka命令工具使用指南

## 1. 概述

DCI监测系统的Kafka命令工具是一个基于Go语言开发的命令行工具，用于管理系统中使用的Kafka主题。该工具提供了创建、查看Kafka主题等功能，支持通过命令行参数或配置文件进行配置。相比于传统的Shell脚本方式，该工具提供了更好的跨平台支持、参数验证和错误处理能力。

## 2. 命令层级结构

DCI-Monitor的Kafka命令工具采用分层命令结构，目前包含以下命令：

```
dci-monitor kafka              # Kafka管理工具根命令
└── init-topics                # 初始化Kafka主题命令
```

### 2.1 根命令 `kafka`

`kafka` 是所有Kafka相关操作的根命令，它提供了一些全局选项，这些选项适用于所有子命令。

### 2.2 子命令 `init-topics`

`init-topics` 子命令用于初始化Kafka主题，支持创建单个主题或批量创建所有配置中定义的主题。

## 3. 命令参数详解

### 3.1 根命令 `kafka` 参数

| 参数 | 描述 | 默认值 | 示例 |
|------|------|--------|------|
| `--kafka-config` | Kafka配置文件路径 | `./config/kafka.yaml` | `--kafka-config=/etc/dci/kafka.yaml` |
| `--brokers` | Kafka服务器连接字符串 | (配置文件中的设置) | `--brokers=kafka1:9092,kafka2:9092` |
| `-h, --help` | 显示帮助信息 | - | `--help` |

### 3.2 子命令 `init-topics` 参数

| 参数 | 短参数 | 描述 | 默认值 | 示例 |
|------|--------|------|--------|------|
| `--all` | `-a` | 初始化所有定义的主题 | `false` | `--all` |
| `--topic` | `-t` | 指定要初始化的单个主题名称 | - | `--topic=dci.monitor.v1.defaultchannel.metrics.telegraf` |
| `--dry-run` | `-d` | 演示模式，不实际执行操作 | `false` | `--dry-run` |
| `--help` | `-h` | 显示帮助信息 | - | `--help` |

## 4. 配置文件格式

Kafka命令工具可以使用YAML格式的配置文件。默认配置文件位于`./config/kafka.yaml`，可通过`--kafka-config`参数指定其他路径。

配置文件示例：

```yaml
kafka:
  # Kafka集群连接信息
  brokers:
    - "dcikafka.intra.citic-x.com:30002"
  
  # 安全配置（可选）
  security:
    tls:
      enabled: false
      # certFile: /path/to/cert.pem
      # keyFile: /path/to/key.pem
      # caFile: /path/to/ca.pem
    sasl:
      enabled: false
      # mechanism: PLAIN
      # username: user
      # password: pass
  
  # 主题配置
  topics:
    - name: "dci.monitor.v1.defaultchannel.topology.lldp"
      partitions: 3
      replicationFactor: 2
      retentionHours: 24  # 1天
      config:
        # 额外的主题级别配置
        "cleanup.policy": "delete"
        
    - name: "dci.monitor.v1.defaultchannel.metrics.telegraf"
      partitions: 6
      replicationFactor: 2
      retentionHours: 168  # 7天
      
    - name: "dci.monitor.v1.defaultchannel.logs.syslog"
      partitions: 3
      replicationFactor: 2
      retentionHours: 720  # 30天
      
    - name: "dci.monitor.v1.defaultchannel.tasks.control"
      partitions: 3
      replicationFactor: 2
      retentionHours: 24  # 1天
```

### 4.1 配置项说明

#### Kafka集群配置

- `brokers`: Kafka服务器列表，格式为`主机名:端口`

#### 安全配置 (security)

- `tls.enabled`: 是否启用TLS加密
- `tls.certFile`: 客户端证书文件路径
- `tls.keyFile`: 客户端密钥文件路径
- `tls.caFile`: CA证书文件路径

- `sasl.enabled`: 是否启用SASL认证
- `sasl.mechanism`: SASL认证机制（如PLAIN、SCRAM-SHA-256等）
- `sasl.username`: SASL用户名
- `sasl.password`: SASL密码

#### 主题配置 (topics)

- `name`: 主题名称
- `partitions`: 分区数
- `replicationFactor`: 副本因子
- `retentionHours`: 数据保留时间（小时）
- `config`: 额外的主题级别配置，键值对形式

## 5. 使用示例

### 5.1 显示帮助信息

```bash
dci-monitor kafka --help
dci-monitor kafka init-topics --help
```

### 5.2 初始化所有主题

```bash
# 使用默认配置文件
dci-monitor kafka init-topics --all

# 指定配置文件
dci-monitor kafka init-topics --all --kafka-config=/path/to/custom/kafka.yaml

# 演示模式（不实际创建）
dci-monitor kafka init-topics --all --dry-run
```

### 5.3 初始化单个主题

```bash
# 初始化特定主题
dci-monitor kafka init-topics --topic=dci.monitor.v1.defaultchannel.metrics.telegraf

# 使用自定义Broker地址
dci-monitor kafka init-topics --topic=dci.monitor.v1.defaultchannel.metrics.telegraf --brokers=localhost:9092
```

## 6. 常见问题与解决方案

### 6.1 连接错误

**问题**：无法连接到Kafka服务器

**解决方案**：
- 检查Kafka服务器是否在运行
- 确认brokers地址和端口是否正确
- 检查网络连接是否正常
- 如果启用了安全认证，确保安全配置正确

### 6.2 权限错误

**问题**：创建主题时出现权限错误

**解决方案**：
- 确认当前用户有权限创建主题
- 检查SASL认证信息是否正确
- 联系Kafka集群管理员获取适当的权限

### 6.3 参数错误

**问题**：命令执行失败，提示参数错误

**解决方案**：
- `--all`和`--topic`不能同时使用，必须选择其一
- 使用`--help`查看正确的参数用法
- 确保配置文件格式正确

## 7. 与Shell脚本方式对比与迁移建议

DCI监测系统之前使用Shell脚本(`dci-monitor/kafka/scripts/create_kafka_topics.sh`)创建Kafka主题，下面是两种方式的对比和迁移建议：

### 7.1 功能对比

| 功能 | Shell脚本方式 | Go命令行工具 |
|------|--------------|-------------|
| 创建多个主题 | ✅ 支持 | ✅ 支持 |
| 创建单个主题 | ✅ 支持 | ✅ 支持 |
| 演示模式 | ✅ 支持 | ✅ 支持 |
| 配置文件支持 | ❌ 不支持 | ✅ 支持 |
| K8s模式操作 | ✅ 支持 | ❌ 暂不支持 |
| 外部直连模式 | ✅ 支持 | ✅ 支持 |
| 跨平台支持 | ❌ 仅Unix/Linux | ✅ 全平台支持 |
| 参数验证 | ⚠️ 基础验证 | ✅ 完整验证 |
| 错误处理 | ⚠️ 基础处理 | ✅ 完整处理 |

### 7.2 命令对应关系

| Shell脚本命令 | Go工具命令 | 说明 |
|--------------|-----------|------|
| `./create_kafka_topics.sh -a` | `dci-monitor kafka init-topics --all` | 创建所有主题 |
| `./create_kafka_topics.sh -t <topic>` | `dci-monitor kafka init-topics --topic=<topic>` | 创建指定主题 |
| `./create_kafka_topics.sh -d -a` | `dci-monitor kafka init-topics --all --dry-run` | 演示模式 |
| `./create_kafka_topics.sh -e -a` | `dci-monitor kafka init-topics --all` | 外部连接模式 |

### 7.3 迁移建议

1. **基本迁移**
   - 直接替换使用Go命令行工具的等效命令
   - 使用YAML配置文件管理主题配置，而非脚本内硬编码

2. **K8s环境迁移注意事项**
   - 当前Go命令行工具不直接支持kubectl操作，如需在K8s环境使用：
     - 将Go工具构建为容器镜像
     - 创建K8s Job运行命令
     - 或使用ConfigMap挂载配置文件

3. **自动化脚本调整**
   - 更新涉及Kafka主题创建的CI/CD脚本
   - 确保在运行环境中安装了Go工具

4. **并行期**
   - 在完全迁移之前，两种方式可以并行使用
   - 确保在Shell脚本和配置文件中保持主题定义一致

## 8. 未来功能规划

未来版本计划添加的功能：

1. 列出现有主题：`dci-monitor kafka list-topics`
2. 修改主题配置：`dci-monitor kafka alter-topic`
3. 删除主题：`dci-monitor kafka delete-topic`
4. 查看主题详情：`dci-monitor kafka describe-topic`
5. K8s集成支持：直接通过K8s API创建主题
6. 主题数据管理功能：清理、压缩等

## 9. 参考资料

- [Kafka主题设计与规划文档](../designs/analysis/04-DCI-Kafka主题规划及负载均衡连接设计.md)
- [Kafka命令工具架构设计](../designs/analysis/05-DCI-Kafka命令工具架构设计.md) 