# DCI数据监测系统 - Kafka安全配置技术方案

## 1. 简介

### 1.1 目的

本文档旨在为DCI数据监测系统的Kafka消息队列设计并实现安全加固方案，通过启用TLS加密和SASL认证机制，保障数据传输安全性及访问控制，满足企业安全合规要求。

### 1.2 背景

DCI数据监测系统作为DCI(数据中心互联)整体解决方案的子系统，使用Kafka作为核心消息队列组件，承载各类监控数据的传输与分发。当前Kafka部署采用Kubernetes StatefulSet模式，使用KRaft共识机制，已集成多个数据源。随着系统的正式部署与扩展，需要增强消息队列的安全防护能力，防止未授权访问和数据泄露风险。

### 1.3 范围

本方案主要涵盖：

- Kafka集群TLS加密配置
- SASL认证机制实现
- ACL权限控制策略
- 客户端适配与迁移策略
- 证书与密钥管理
- 方案验证与回滚机制

## 2. 系统架构

### 2.1 当前架构

当前Kafka集群配置：

- 基于Kubernetes的StatefulSet部署，3节点高可用集群
- 使用KRaft共识协议，无ZooKeeper依赖
- 外部通过NodePort服务暴露（30010-30012端口）
- 使用PLAINTEXT协议，无加密和认证机制
- 通过SLB和域名（dcikafka.intra.citic-x.com）对外提供服务

### 2.2 安全加固后架构

加固后架构变更：

- 所有监听器配置TLS加密（INTERNAL、EXTERNAL、CONTROLLER）
- 外部访问启用SASL_SSL认证（SASL机制：PLAIN）
- 内部集群通信（broker间）使用TLS加密
- 控制器通信使用TLS加密
- 实现基于角色的ACL权限控制
- 证书由私有CA签发，统一管理

### 2.3 组件关系

```mermaid
graph TD
    A[客户端应用] -->|SASL_SSL| B[Kafka外部监听器]
    B -->|SSL| C[Kafka Broker]
    C -->|SSL| D[Kafka Broker间通信]
    C -->|SSL| E[Kafka Controller通信]
    F[Telegraf Agent] -->|SASL_SSL| B
    G[FlowData服务] -->|SASL_SSL| B
    H[Topic管理工具] -->|SASL_SSL| B
    I[证书管理] --> J[Kubernetes Secret]
    J --> C
```

## 3. 详细设计

### 3.1 TLS加密配置

#### 3.1.1 证书结构设计

建立三级证书结构：

1. **根CA证书**：离线保存，用于签发中间CA
2. **中间CA证书**：签发服务器和客户端证书
3. **终端证书**：
   - Broker服务器证书（SAN包含所有可能的访问域名和IP）
   - 客户端证书（按应用类型分组）

#### 3.1.2 证书生成与管理脚本

为了简化证书生成和管理过程，我们提供了一个自动化脚本 `dci-monitor/kafka/scripts/generate_certs.sh`。该脚本封装了所有必要的 `openssl` 命令，并自动处理了在实施过程中发现的兼容性问题（如PKCS#8格式转换和PEM密钥库打包）。

**脚本核心功能:**

- **创建三级证书体系**: 自动生成根CA、中间CA、服务器证书和客户端证书。
- **处理Java兼容性**: 自动将所有私钥转换为Java兼容的PKCS#8格式。
- **生成组合密钥库**: 为Kafka服务器创建包含私钥和证书链的组合PEM文件 (`kafka.server.keystore.pem`)。
- **生成JAAS配置**: 创建Kafka所需的`kafka_server_jaas.conf`文件。
- **生成部署脚本**: 创建一个`apply_secrets.sh`脚本，用于将生成的凭证一键部署为Kubernetes Secrets。

**使用说明:**

1.  **进入脚本所在目录**:
    ```bash
    cd dci-monitor/kafka/scripts
    ```
2.  **执行脚本**:
    ```bash
    ./generate_certs.sh
    ```
3.  **检查输出**:
    脚本执行成功后，所有证书、密钥和辅助脚本都会被创建在新的 `dci-kafka-certs` 目录下。
4.  **应用到Kubernetes**:
    进入 `dci-kafka-certs` 目录，执行`apply_secrets.sh`脚本，即可将所有凭证安全地存储到Kubernetes集群中。
```bash
    cd dci-kafka-certs
    ./apply_secrets.sh
```

#### 3.1.3 Kubernetes Secret配置

创建包含证书和密钥的Secret：

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: kafka-tls-secret
  namespace: dci
type: Opaque
data:
  kafka.server.keystore.pem: {{ server_keystore_base64 }}
  ca-intermediate-cert.pem: {{ ca_cert_base64 }}
  # SASL凭证(JAAS配置)
  kafka_server_jaas.conf: {{ jaas_config_base64 }}
```

#### 3.1.4 Broker配置变更

修改Kafka配置模板以支持TLS：

```properties
# TLS相关配置
ssl.keystore.type=PEM
ssl.keystore.location=/etc/kafka/tls/kafka.server.keystore.pem
ssl.truststore.location=/etc/kafka/tls/ca-intermediate-cert.pem
ssl.truststore.type=PEM
ssl.client.auth=required

# 监听器和安全协议配置
listener.security.protocol.map=INTERNAL:SSL,EXTERNAL:SASL_SSL,CONTROLLER:SSL
inter.broker.listener.name=INTERNAL
```

### 3.2 SASL认证配置

#### 3.2.1 认证机制选择

选择SASL/PLAIN作为认证机制，结合TLS加密确保凭证安全传输。理由：
- 部署简单，配置直观
- 客户端支持广泛（包括Telegraf、Go等）
- 用户管理灵活，支持集中式凭证管理

#### 3.2.2 用户角色设计

设计四类用户角色：

1. **管理员用户**：拥有集群管理权限
   - 用户：`kafka-admin`
   - 权限：所有主题的读写和管理权限

2. **生产者用户**：数据采集与写入
   - 用户：`dci-producer`
   - 权限：特定主题的写入权限

3. **消费者用户**：数据处理与分析
   - 用户：`dci-consumer`
   - 权限：特定主题的读取权限

4. **应用专用用户**：特定应用专用
   - 例如：`dci-flowdata`、`dci-telegraf`
   - 权限：对应用相关主题的读写权限

#### 3.2.3 JAAS配置文件

创建JAAS配置文件（kafka_server_jaas.conf）：

```
KafkaServer {
  org.apache.kafka.common.security.plain.PlainLoginModule required
  username="kafka-admin"
  password="admin-secret-password"
  user_kafka-admin="admin-secret-password"
  user_dci-producer="producer-secret-password"
  user_dci-consumer="consumer-secret-password"
  user_dci-flowdata="flowdata-secret-password"
  user_dci-telegraf="telegraf-secret-password";
};
```

#### 3.2.4 Broker SASL配置

添加SASL配置到Kafka服务器配置中。注意：在容器化部署（如Kubernetes）中，通常通过环境变量向JVM注入JAAS配置文件路径，而不是直接写入`server.properties`。

```properties
# SASL配置
sasl.enabled.mechanisms=PLAIN
sasl.mechanism.inter.broker.protocol=PLAIN
```

在Kubernetes的StatefulSet配置中，应添加如下环境变量：
```yaml
env:
- name: KAFKA_OPTS
  value: "-Djava.security.auth.login.config=/etc/kafka/tls/kafka_server_jaas.conf"
```

### 3.3 ACL权限配置

#### 3.3.1 ACL策略设计

基于"最小权限原则"设计ACL规则：

| 用户 | 资源类型 | 资源名称 | 操作 |
|------|--------|---------|------|
| kafka-admin | * | * | ALL |
| dci-producer | Topic | dci.monitor.v1.*.* | WRITE, DESCRIBE |
| dci-consumer | Topic | dci.monitor.v1.*.* | READ, DESCRIBE |
| dci-flowdata | Topic | dci.monitor.v1.*.flows.* | READ, WRITE, DESCRIBE |
| dci-telegraf | Topic | dci.monitor.v1.*.metrics.* | WRITE, DESCRIBE |

#### 3.3.2 ACL实现命令

```bash
# 启用ACL授权 (注意：KRaft模式下使用StandardAuthorizer)
echo "authorizer.class.name=org.apache.kafka.server.authorizer.StandardAuthorizer" >> server.properties
echo "super.users=User:kafka-admin" >> server.properties

# 为管理员用户授权
kafka-acls.sh --bootstrap-server localhost:9092 \
  --command-config admin.properties \
  --add --allow-principal User:kafka-admin \
  --operation All --cluster

# 为生产者用户授权
kafka-acls.sh --bootstrap-server localhost:9092 \
  --command-config admin.properties \
  --add --allow-principal User:dci-producer \
  --operation Write,Describe \
  --topic "dci.monitor.v1.*.*"

# 为消费者用户授权
kafka-acls.sh --bootstrap-server localhost:9092 \
  --command-config admin.properties \
  --add --allow-principal User:dci-consumer \
  --operation Read,Describe \
  --topic "dci.monitor.v1.*.*"
```

### 3.4 客户端配置适配

#### 3.4.1 Telegraf客户端配置

修改Telegraf配置文件：

```toml
[[outputs.kafka]]
  ## Kafka Broker地址列表
  brokers = ["dcikafka.intra.citic-x.com:30010", "dcikafka.intra.citic-x.com:30011", "dcikafka.intra.citic-x.com:30012"]
  
  ## 主题名称
  topic = "dci.monitor.v1.defaultchannel.metrics.telegraf"
  
  ## TLS/SSL配置
  tls_ca = "/etc/telegraf/tls/ca-intermediate-cert.pem"
  tls_cert = "/etc/telegraf/tls/telegraf-client-cert.pem"
  tls_key = "/etc/telegraf/tls/telegraf-client-key.pem"
  
  ## SASL认证
  sasl_username = "dci-telegraf"
  sasl_password = "telegraf-secret-password"
  sasl_mechanism = "PLAIN"
```

#### 3.4.2 Go应用客户端配置（FlowData等）

```go
import (
	"crypto/tls"
	"crypto/x509"
	"io/ioutil"

	"github.com/IBM/sarama"
)

func createSecureKafkaClient(bootstrapServers string) (sarama.Client, error) {
	cfg := sarama.NewConfig()
	cfg.Version = sarama.V3_0_0_0
	cfg.Metadata.Full = true
	
	// 启用TLS
	cfg.Net.TLS.Enable = true
	
	// 加载CA证书
	caCert, err := ioutil.ReadFile("/etc/dci/tls/ca-intermediate-cert.pem")
	if err != nil {
		return nil, err
	}
	caCertPool := x509.NewCertPool()
	caCertPool.AppendCertsFromPEM(caCert)
	
	// 加载客户端证书和密钥
	cert, err := tls.LoadX509KeyPair(
		"/etc/dci/tls/client-cert.pem",
		"/etc/dci/tls/client-key.pem",
	)
	if err != nil {
		return nil, err
	}
	
	cfg.Net.TLS.Config = &tls.Config{
		RootCAs:      caCertPool,
		Certificates: []tls.Certificate{cert},
	}
	
	// 启用SASL认证
	cfg.Net.SASL.Enable = true
	cfg.Net.SASL.Mechanism = sarama.SASLTypePlaintext
	cfg.Net.SASL.User = "dci-flowdata"
	cfg.Net.SASL.Password = "flowdata-secret-password"
	
	// 连接Kafka
	brokers := strings.Split(bootstrapServers, ",")
	client, err := sarama.NewClient(brokers, cfg)
	if err != nil {
		return nil, err
	}
	
	return client, nil
}
```

#### 3.4.3 CLI工具配置

创建客户端配置文件（例如：`kafka-cli.properties`）：

```properties
bootstrap.servers=dcikafka.intra.citic-x.com:30010,dcikafka.intra.citic-x.com:30011,dcikafka.intra.citic-x.com:30012
security.protocol=SASL_SSL
ssl.truststore.location=/etc/dci/tls/truststore.jks
ssl.truststore.password=truststore-password
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required \
  username="kafka-admin" \
  password="admin-secret-password";
```

### 3.5 部署与迁移策略

#### 3.5.1 分阶段实施计划

1. **准备阶段（2天）**
   - 生成所有证书和密钥
   - 创建Kubernetes Secret资源
   - 更新Kafka配置模板
   - 准备回滚脚本

2. **双协议过渡阶段（3天）**
   - 配置Kafka同时支持加密和非加密连接
   - 配置监听器：`INTERNAL_PLAINTEXT`、`INTERNAL_SSL`、`EXTERNAL_PLAINTEXT`、`EXTERNAL_SASL_SSL`
   - 测试新安全连接的可用性

3. **客户端迁移阶段（7天）**
   - 更新各客户端配置
   - 逐个迁移并验证客户端连接
   - 持续监控客户端连接状态

4. **安全锁定阶段（1天）**
   - 禁用非加密连接
   - 启用ACL权限控制
   - 完成全部安全配置切换

#### 3.5.2 配置更新方法

使用Kubernetes滚动更新策略：

```bash
# 更新ConfigMap
kubectl apply -f kafka-secure-config.yaml

# 执行滚动更新
kubectl rollout restart statefulset/kafka -n dci

# 监控滚动更新状态
kubectl rollout status statefulset/kafka -n dci
```

#### 3.5.3 回滚方案

1. **快速回滚步骤**：
   - 应用原始ConfigMap：`kubectl apply -f kafka-original-config.yaml`
   - 重启StatefulSet：`kubectl rollout restart statefulset/kafka -n dci`
   - 恢复客户端配置到非加密模式

2. **回滚触发条件**：
   - 集群节点无法正常启动超过5分钟
   - 客户端连接失败率超过30%
   - 消息处理延迟增加超过200%

### 3.6 证书轮换策略

#### 3.6.1 证书生命周期管理

- **服务器证书**：有效期825天，提前90天开始轮换
- **客户端证书**：有效期365天，提前60天开始轮换
- **中间CA证书**：有效期1825天，提前180天开始轮换

#### 3.6.2 证书轮换流程

1. 生成新证书，同时保留旧证书
2. 更新Kubernetes Secret，包含新旧证书
3. 配置Kafka信任新旧证书
4. 分批更新客户端使用新证书
5. 监控一个完整周期后，移除旧证书

## 4. 接口设计

### 4.1 认证接口

所有客户端通过SASL_PLAIN机制进行认证：

```
username=<用户名>&password=<密码>
```

认证失败响应：
- 错误码：SASL认证失败
- 连接关闭

### 4.2 配置文件接口

更新`dci-monitor/kafka/kafka.yaml`配置模板：

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: kafka-conf
  namespace: dci
data:
  server-template.properties: |
    # 安全配置
    listener.security.protocol.map=INTERNAL:SSL,EXTERNAL:SASL_SSL,CONTROLLER:SSL
    ssl.keystore.type=PEM
    ssl.keystore.location=/etc/kafka/tls/kafka.server.keystore.pem
    ssl.truststore.location=/etc/kafka/tls/ca-intermediate-cert.pem
    ssl.truststore.type=PEM
    ssl.client.auth=required
    
    # SASL配置
    sasl.enabled.mechanisms=PLAIN
    sasl.mechanism.inter.broker.protocol=PLAIN

    # ACL配置
    authorizer.class.name=org.apache.kafka.server.authorizer.StandardAuthorizer
    super.users=User:kafka-admin

---
apiVersion: apps/v1
kind: StatefulSet
# ... 其他StatefulSet配置 ...
spec:
  # ...
  template:
    # ...
    spec:
      containers:
      - name: kafka
        # ...
        env:
        - name: KAFKA_OPTS
          value: "-Djava.security.auth.login.config=/etc/kafka/tls/kafka_server_jaas.conf"
        # ...
        volumeMounts:
        - name: kafka-tls
          mountPath: /etc/kafka/tls
      volumes:
      - name: kafka-tls
        secret:
          secretName: kafka-tls-secret
```

### 4.3 客户端API接口

`dcimonitor/cmd/kafka/kafka_client.go`需更新的方法：

```go
// 创建安全Kafka客户端
func createSecureKafkaClient(configPath string) (sarama.Client, error) {
    // 加载配置
    cfg, err := loadConfig(configPath)
    if err != nil {
        return nil, err
    }
    
    // 配置TLS与SASL
    kafkaConfig := sarama.NewConfig()
    kafkaConfig.Net.TLS.Enable = true
    kafkaConfig.Net.SASL.Enable = true
    kafkaConfig.Net.SASL.Mechanism = sarama.SASLTypePlaintext
    kafkaConfig.Net.SASL.User = cfg.Security.SASL.Username
    kafkaConfig.Net.SASL.Password = cfg.Security.SASL.Password
    
    // 配置证书
    if cfg.Security.TLS.CertFile != "" && cfg.Security.TLS.KeyFile != "" {
        // 加载客户端证书
    }
    
    // 连接服务器
    client, err := sarama.NewClient(cfg.Brokers, kafkaConfig)
    if err != nil {
        return nil, err
    }
    
    return client, nil
}
```

## 5. 数据结构

### 5.1 配置文件结构

更新`dci-monitor/config/kafka.yaml`配置格式：

```yaml
kafka:
  brokers:
    - "dcikafka.intra.citic-x.com:30010"
    - "dcikafka.intra.citic-x.com:30011"
    - "dcikafka.intra.citic-x.com:30012"
  
  security:
    tls:
      enabled: true
      certFile: "/etc/dci/tls/client-cert.pem"
      keyFile: "/etc/dci/tls/client-key.pem"
      caFile: "/etc/dci/tls/ca-intermediate-cert.pem"
    sasl:
      enabled: true
      mechanism: "PLAIN"
      username: "dci-client"
      password: "client-secret-password"
  
  topics:
    - name: "dci.monitor.v1.defaultchannel.topology.lldp"
      partitions: 3
      replicationFactor: 2
      retentionHours: 24
```

### 5.2 证书存储结构

```
/etc/dci/tls/
├── ca-intermediate-cert.pem  # CA证书
├── client-cert.pem           # 客户端证书
├── client-key.pem            # 客户端私钥
└── kafka-truststore.jks      # Java客户端信任库
```

## 6. 风险与缓解措施

### 6.1 已识别风险

| 风险 | 可能性 | 影响 | 缓解措施 |
|------|-------|------|---------|
| 证书配置错误导致连接失败 | 中 | 高 | 保留双协议支持，提供回滚方案 |
| 性能下降 | 低 | 中 | 优化TLS配置，增加缓存，监控性能指标 |
| 密钥泄露 | 低 | 高 | 实施证书轮换机制，密钥隔离存储 |
| 客户端兼容性问题 | 中 | 高 | 全面测试各类客户端，提供迁移指南 |
| 管理复杂性增加 | 高 | 中 | 提供自动化脚本，编写详细文档 |

### 6.2 应急预案

1. **连接失败应急措施**：
   - 立即启用PLAINTEXT协议作为备用
   - 执行回滚脚本恢复之前配置
   - 分析日志确定根本原因

2. **性能下降应对**：
   - 增加服务器资源（CPU/内存）
   - 调整TLS会话缓存大小
   - 优化密码套件选择

3. **证书问题处理**：
   - 维护证书备份，确保快速替换
   - 预设紧急证书更新流程
   - 定期演练证书轮换操作

## 7. 测试计划

### 7.1 单元测试

针对客户端代码的TLS和SASL支持进行单元测试：

```go
func TestKafkaSecureConnection(t *testing.T) {
    // 测试TLS配置是否正确应用
    // 测试SASL认证是否正确配置
    // 测试不同证书情况下的行为
}
```

### 7.2 集成测试

1. **安全连接测试**：
   - 测试所有客户端与安全Kafka的连接
   - 验证TLS握手过程
   - 验证SASL认证过程

2. **ACL授权测试**：
   - 测试不同用户的权限限制
   - 验证越权操作被拒绝
   - 测试通配符ACL规则

3. **性能对比测试**：
   - 对比加密前后的吞吐量变化
   - 测量加密对延迟的影响
   - 评估资源使用增加情况

### 7.3 验收测试

| 测试项 | 预期结果 | 验收标准 |
|-------|---------|----------|
| TLS连接 | 所有客户端可通过TLS安全连接 | 100%连接成功率 |
| SASL认证 | 正确凭证可认证，错误凭证被拒绝 | 认证机制工作正常 |
| ACL权限 | 用户只能访问被授权资源 | 权限隔离有效 |
| 性能指标 | 吞吐量下降不超过10% | 性能影响在可接受范围 |
| 证书轮换 | 证书更新不中断服务 | 零停机证书更新 |

## 8. 实施计划

### 8.1 工作分解

| 任务 | 负责人 | 工作量(人日) | 前置任务 |
|-----|--------|------------|---------|
| 证书生成与管理 | 安全工程师 | 2 | 无 |
| Kafka配置更新 | 系统工程师 | 3 | 证书生成 |
| 双协议过渡配置 | 系统工程师 | 1 | Kafka配置更新 |
| Telegraf客户端适配 | 开发工程师 | 2 | 双协议配置 |
| FlowData服务适配 | 开发工程师 | 2 | 双协议配置 |
| Topic管理工具适配 | 开发工程师 | 1 | 双协议配置 |
| ACL规则配置 | 系统工程师 | 1 | 客户端适配 |
| 集成测试 | 测试工程师 | 3 | 客户端适配 |
| 生产环境部署 | 运维工程师 | 2 | 集成测试 |
| 文档完善 | 技术文档工程师 | 2 | 全部 |

### 8.2 实施时间表

- **第1周**：准备与配置阶段
  - 证书生成与配置
  - Kafka配置更新
  - 双协议过渡配置

- **第2周**：客户端适配阶段
  - 各客户端安全配置适配
  - 单元测试与调试
  - 初步集成测试

- **第3周**：测试与部署阶段
  - 全面集成测试
  - 安全配置检查与审计
  - 生产环境部署
  - 文档完善

### 8.3 里程碑

1. **M1**：安全配置设计完成 - T+5天
2. **M2**：双协议配置验证通过 - T+10天
3. **M3**：所有客户端适配完成 - T+15天
4. **M4**：集成测试通过 - T+18天
5. **M5**：生产环境部署完成 - T+20天

## 9. 参考文档

1. [Kafka安全配置官方文档](https://kafka.apache.org/documentation/#security)
2. [Kafka SSL/TLS配置指南](https://docs.confluent.io/platform/current/kafka/authentication_ssl.html)
3. [Kafka SASL认证文档](https://docs.confluent.io/platform/current/kafka/authentication_sasl/index.html)
4. [DCI监测系统Kafka子命令工具使用指南](../../apis/07-DCI监测系统Kafka子命令工具使用指南.md)
5. [DCI-Kafka主题规划及负载均衡连接设计](../13-DCI-Kafka主题规划及负载均衡连接设计.md)

## 10. 附录

### 10.1 安全配置清单

- [ ] 生成并部署CA证书
- [ ] 生成并部署服务器证书
- [ ] 生成并部署客户端证书
- [ ] 配置Kafka TLS设置
- [ ] 配置Kafka SASL设置
- [ ] 配置ACL权限规则
- [ ] 更新所有客户端配置
- [ ] 验证全部安全功能
- [ ] 禁用非安全连接

### 10.2 客户端配置示例

#### Telegraf完整配置

```toml
[[outputs.kafka]]
  brokers = ["dcikafka.intra.citic-x.com:30010", "dcikafka.intra.citic-x.com:30011"]
  topic = "dci.monitor.v1.defaultchannel.metrics.telegraf"
  
  # 安全配置
  tls_ca = "/etc/telegraf/ca-intermediate-cert.pem"
  tls_cert = "/etc/telegraf/client-cert.pem" 
  tls_key = "/etc/telegraf/client-key.pem"
  sasl_username = "dci-telegraf"
  sasl_password = "telegraf-secret-password"
  sasl_mechanism = "PLAIN"
  
  # 性能配置
  batch_size = 100
  compression_codec = "snappy"
  max_retry = 3
```

#### kafka-client.go安全连接示例

```go
func GetSecureClient() (sarama.Client, error) {
    config := sarama.NewConfig()
    config.Version = sarama.V3_0_0_0
    
    // TLS配置
    tlsConfig := createTLSConfig(
        "/etc/dci/tls/ca-cert.pem",
        "/etc/dci/tls/client-cert.pem",
        "/etc/dci/tls/client-key.pem",
    )
    config.Net.TLS.Enable = true
    config.Net.TLS.Config = tlsConfig
    
    // SASL配置
    config.Net.SASL.Enable = true
    config.Net.SASL.Mechanism = sarama.SASLTypePlaintext
    config.Net.SASL.User = "dci-flowdata"
    config.Net.SASL.Password = "flowdata-secret-password"
    
    // 连接Brokers
    brokers := []string{
        "dcikafka.intra.citic-x.com:30010",
        "dcikafka.intra.citic-x.com:30011",
        "dcikafka.intra.citic-x.com:30012",
    }
    
    return sarama.NewClient(brokers, config)
}
```