---
title: |
  DCI网络控制-Prometheus指标类告警联动技术方案设计

subtitle: |
  网络自动化任务与Prometheus指标监控的深度集成方案
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-07-30 | 顾铠羟 | 初始版本           |

# 1 文档介绍

## 1.1 文档目的

本文档专注于设计网络自动化控制系统与DCI数据监测系统中Prometheus指标类告警的深度联动机制。通过建立任务级别的监控增强策略，实现网络配置变更期间的精细化监控，为网络自动化任务的执行提供全方位的性能监测和风险预警。

## 1.2 文档范围

本文档涵盖以下技术内容：
- 基于现有设计的Prometheus指标告警联动配置方案
- 网络自动化任务与Prometheus告警的集成策略
- 任务监控期间的指标监测增强方案
- 告警联动的部署和配置指南
- 具体使用场景和操作流程

## 1.3 文档关联

本文档专注于Prometheus指标告警场景下的告警联动实现，基于以下现有设计：
- 《17-DCI-网络自动化任务协同监控技术方案设计.md》：提供任务协同监控的基础架构和API
- 《24-02-DCI-统一告警模型设计.md》：提供告警数据模型和设备ID提取机制
- 《24-01-DCI-prometheus指标类数据AlertManager技术方案设计.md》：提供Prometheus AlertManager集成方案
- 《25-DCI-告警联动技术方案设计框架.md》：提供整体告警联动框架

# 2 总体设计

## 2.1 设计目标

基于现有17号任务协同监控和24-*号告警设计的Prometheus指标告警联动目标：

- **复用现有架构**：充分利用17号文档的任务协同监控框架和24号文档的告警处理架构
- **Prometheus场景优化**：针对Prometheus指标告警的特定需求，优化设备ID提取和告警关联逻辑  
- **配置简化**：提供针对Prometheus指标监控的配置模板和最佳实践
- **监控增强**：在网络自动化任务期间，针对Prometheus指标提供增强监控策略
- **运维友好**：利旧当前K8S部署模式，本地只维护yaml文件，在K8S服务端手动部署

## 2.2 架构设计

### 2.2.1 基于现有设计的集成架构

```mermaid
graph TB
    subgraph "网络自动化系统"
        NetCtrl["网络控制系统"]
    end
    
    subgraph "Prometheus监控系统"
        Prometheus["Prometheus Server"]
        AlertMgr["Alertmanager"]
        PrometheusConfig["prometheus.yml<br/>+ alert rules"]
    end
    
    subgraph "DCI监控系统 (17号+24号设计)"
        subgraph "17号: 任务协同监控"
            TaskAPI["任务协同API<br/>/api/v1/tasks/{id}/monitoring"]
            TaskService["任务会话管理器<br/>告警关联服务"]
            TaskDB["monitor_network_auto_task_monitoring_sessions"]
        end
        
        subgraph "24号: 统一告警模型"
            AlertWebHook["告警WebHook<br/>/api/v1/alerts/webhook/prometheus"]
            AlertService["告警处理器<br/>设备ID提取+任务关联"]
            AlertDB["monitor_alert<br/>(task_id字段)"]
        end
    end
    
    %% 数据流向
    NetCtrl -->|"启动监控"| TaskAPI
    TaskAPI --> TaskService
    TaskService --> TaskDB
    
    Prometheus --> PrometheusConfig
    PrometheusConfig --> AlertMgr
    AlertMgr -->|"WebHook"| AlertWebHook
    AlertWebHook --> AlertService
    AlertService -->|"查询任务关联"| TaskService
    AlertService --> AlertDB
    
    %% 样式定义
    classDef external fill:#ff9999
    classDef design17 fill:#99ff99
    classDef design24 fill:#99ccff
    classDef prometheus fill:#ffcc99
    
    class NetCtrl external
    class Prometheus,AlertMgr,PrometheusConfig prometheus
    class TaskAPI,TaskService,TaskDB design17
    class AlertWebHook,AlertService,AlertDB design24
```

### 2.2.2 Prometheus指标告警联动流程

```mermaid
sequenceDiagram
    participant NetCtrl as 网络控制系统
    participant Task17 as 17号:任务协同API
    participant Prom as Prometheus
    participant Alert24 as 24号:告警处理
    
    NetCtrl->>Task17: 启动任务监控<br/>POST /api/v1/tasks/{id}/monitoring/start
    Task17->>Task17: 创建任务会话<br/>(device_ids, start_time)
    
    Prom->>Alert24: AlertManager WebHook<br/>POST /api/v1/alerts/webhook/prometheus
    Alert24->>Alert24: 提取设备ID<br/>(来自Labels)
    Alert24->>Task17: 查询设备关联任务<br/>GetTaskByDeviceWithExtendedMonitoring()
    Task17-->>Alert24: 返回task_id
    Alert24->>Alert24: 设置alert.task_id<br/>增强告警元数据
    
    NetCtrl->>Task17: 停止任务监控<br/>POST /api/v1/tasks/{id}/monitoring/stop
    Task17->>Task17: 结束会话<br/>(end_time, status='completed')
    Task17-->>NetCtrl: 返回告警统计摘要
```

## 2.3 Prometheus指标告警的特殊考虑

### 2.3.1 设备ID标签映射策略

Prometheus指标告警中，设备标识信息可能出现在不同的标签中，需要建立标准的映射策略：

**常见Prometheus设备标签格式**：
```yaml
# SNMP采集的设备指标
dci_snmp_interface_status{device_id="switch001", device_ip="************", instance="************:161", ifName="GigabitEthernet0/1"}

# 网络设备指标  
network_interface_bytes_total{device="core-switch-01", host="************", instance="************:9100"}

# 系统指标
node_cpu_usage{instance="************:9100", hostname="server001"}
```

**映射优先级**(基于24-02文档的ExtractDeviceID函数)：
1. `device_id` > `device` > `device_ip`
2. `instance`(去除端口) > `host` > `hostname`  
3. 自定义映射规则(可配置)

### 2.3.2 告警规则标签规范

为确保告警能正确关联到网络自动化任务，Prometheus告警规则需要包含设备标识标签：

**推荐的告警规则配置**：
```yaml
# 示例：接口状态告警规则
- alert: InterfaceDown
  expr: dci_snmp_interface_status{ifOperStatus="2"} == 1
  for: 30s
  labels:
    severity: critical
    category: interface
    # 确保包含设备标识标签
    device_id: "{{ $labels.device_id }}"
    device_ip: "{{ $labels.device_ip }}"
  annotations:
    summary: "接口 {{ $labels.ifName }} 在设备 {{ $labels.device_id }} 上状态异常"
    description: "设备 {{ $labels.device_id }} ({{ $labels.device_ip }}) 的接口 {{ $labels.ifName }} 状态为Down"
    
# 示例：CPU使用率告警规则  
- alert: HighCPUUsage
  expr: dci_snmp_device_cpu_usage > 80
  for: 5m
  labels:
    severity: warning
    category: performance
    device_id: "{{ $labels.device_id }}"
  annotations:
    summary: "设备 {{ $labels.device_id }} CPU使用率过高"
    description: "设备 {{ $labels.device_id }} CPU使用率为 {{ $value }}%，持续超过5分钟"
```

## 2.4 与现有设计的集成策略

### 2.4.1 基于17号任务协同监控的集成

**使用17号文档提供的功能**：
- 任务监控会话管理(`monitor_network_auto_task_monitoring_sessions`表)
- 告警自动关联服务(`AlertAssociationService`)
- 简化的任务协同API(`/api/v1/tasks/{id}/monitoring/start|stop`)
- 延展监测机制(任务结束后1小时)

**Prometheus场景的特殊配置**：
- 针对网络设备监控任务，建议设置延展监测时间为1-2小时
- 支持基于设备IP范围的批量任务创建
- 针对Prometheus采集频率，优化任务会话的时间精度

### 2.4.2 基于24号告警模型的集成

**使用24-02文档提供的功能**：
- 统一告警数据模型(`monitor_alert`表，包含`task_id`字段)
- 设备ID提取机制(`ExtractDeviceID`函数)
- 任务关联机制(`GetTaskByDeviceWithExtendedMonitoring`)
- 告警元数据增强(`EnhanceAlertMetadata`)

**Prometheus场景的优化**：
- 针对Prometheus Labels格式，优化设备ID提取逻辑
- 支持Prometheus告警的特有字段(如generator_url)
- 基于Prometheus指标的告警分类和统计

### 2.4.3 与24-01 AlertManager的协同

**使用24-01文档提供的功能**：
- AlertManager WebHook集成
- 告警规则管理机制
- 告警通知路由配置

**增强配置建议**：
- 为网络自动化任务期间的告警设置特殊通知策略
- 配置任务相关告警的分组和抑制规则
- 设置任务执行期间的告警静默策略

## 2.5 部署要求

基于现有DCI项目部署架构，无需额外组件：

- **数据库**：使用现有MySQL实例，执行24-02和17号文档中的表结构变更
- **应用服务**：在现有dcimonitor服务中集成告警联动功能
- **配置管理**：通过现有配置文件管理告警联动参数
- **监控指标**：复用现有Prometheus和AlertManager实例

# 3 配置与实施

## 3.1 基础配置

### 3.1.1 数据库配置

基于17号和24-02文档的数据库设计，执行以下SQL脚本：

**1. 创建任务监控会话表(来自17号文档)**：
```sql
CREATE TABLE monitor_network_auto_task_monitoring_sessions (
    session_id VARCHAR(64) PRIMARY KEY COMMENT '会话唯一标识符',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    device_ids JSON NOT NULL COMMENT '监控设备ID列表',
    start_time TIMESTAMP NOT NULL COMMENT '监控开始时间(Asia/Shanghai)',
    end_time TIMESTAMP NULL COMMENT '监控结束时间(Asia/Shanghai)',
    status ENUM('active', 'completed') NOT NULL DEFAULT 'active' COMMENT '会话状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(Asia/Shanghai)',
    INDEX idx_task_id (task_id),
    INDEX idx_status_time (status, start_time),
    INDEX idx_end_time (end_time) COMMENT '支持延展监测查询优化'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网络自动化协同任务监控会话表';
```

**2. 扩展告警表添加设备ID字段(来自24-02文档)**：
```sql
ALTER TABLE monitor_alert ADD COLUMN device_id VARCHAR(128) NULL COMMENT '设备ID(从labels中提取并存储，便于查询优化)';
ALTER TABLE monitor_alert ADD INDEX idx_device_id (device_id);
```

**3. 创建任务告警关联表(来自24-02文档)**：
```sql
CREATE TABLE monitor_task_alert_associations (
    id VARCHAR(36) PRIMARY KEY COMMENT '关联记录ID',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    alert_id VARCHAR(36) NOT NULL COMMENT '告警ID(关联monitor_alert.id)',
    association_type ENUM('active','post_task') NOT NULL COMMENT '关联类型(active=任务执行期间,post_task=任务后续影响)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '关联时间',
    INDEX idx_task_id (task_id),
    INDEX idx_alert_id (alert_id),
    INDEX idx_association_type (association_type),
    INDEX idx_task_type (task_id, association_type),
    FOREIGN KEY (alert_id) REFERENCES monitor_alert(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务告警关联表';
```

### 3.1.2 Prometheus告警规则配置

为确保告警能正确关联到网络自动化任务，需要在Prometheus告警规则中包含设备标识标签：

**推荐的告警规则模板**：
```yaml
# /etc/prometheus/alert-rules/network-device-rules.yml
groups:
- name: network.device.alerts
  rules:
  # 接口状态告警 - 包含设备标识
  - alert: NetworkInterfaceDown
    expr: dci_snmp_interface_status{ifOperStatus="2"} == 1
    for: 30s
    labels:
      severity: critical
      category: interface
      device_id: "{{ $labels.device_id }}"    # 关键：设备ID标签
      device_ip: "{{ $labels.device_ip }}"    # 关键：设备IP标签
    annotations:
      summary: "接口 {{ $labels.ifName }} 在设备 {{ $labels.device_id }} 上状态异常"
      description: "设备 {{ $labels.device_id }} ({{ $labels.device_ip }}) 的接口 {{ $labels.ifName }} 状态为Down"
      
  # CPU使用率告警 - 包含设备标识
  - alert: DeviceHighCPU
    expr: dci_snmp_device_cpu_usage{entPhysicalClass="9"} > 80
    for: 5m
    labels:
      severity: warning
      category: performance
      device_id: "{{ $labels.device_id }}"
    annotations:
      summary: "设备 {{ $labels.device_id }} CPU使用率过高"
      description: "设备 {{ $labels.device_id }} CPU使用率为 {{ $value }}%，持续超过5分钟"

  # 内存使用率告警 - 包含设备标识
  - alert: DeviceHighMemory
    expr: dci_snmp_device_memory_usage{entPhysicalClass="9"} > 85
    for: 3m
    labels:
      severity: warning
      category: performance  
      device_id: "{{ $labels.device_id }}"
    annotations:
      summary: "设备 {{ $labels.device_id }} 内存使用率过高"
      description: "设备 {{ $labels.device_id }} 内存使用率为 {{ $value }}%"
```

### 3.1.3 AlertManager配置

基于24-01文档的AlertManager集成，针对任务期间告警设置特殊处理：

**alertmanager.yml增强配置**：
```yaml
# 路由配置 - 为任务期间告警设置特殊处理
route:
  group_by: ['alertname', 'device_id']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  routes:
  # 为网络自动化任务相关告警设置特殊路由
  - match:
      category: 'interface'
    group_by: ['alertname', 'device_id', 'task_id']
    receiver: 'network-task-alerts'
    
  - match:
      category: 'performance'
    group_by: ['alertname', 'device_id']
    receiver: 'performance-alerts'

# WebHook配置 - 发送到DCI监控系统
receivers:
- name: 'default'
  webhook_configs:
  - url: 'http://dcimonitor:8080/api/v1/alerts/webhook/prometheus'
    http_config:
      basic_auth:
        username: 'prometheus'
        password: 'webhook_secret'
    send_resolved: true

- name: 'network-task-alerts'
  webhook_configs:
  - url: 'http://dcimonitor:8080/api/v1/alerts/webhook/prometheus'
    http_config:
      basic_auth:
        username: 'prometheus' 
        password: 'webhook_secret'
    send_resolved: true
    # 任务期间告警的特殊标记
    title: 'Network Task Alert: {{ .GroupLabels.alertname }}'
    
- name: 'performance-alerts'
  webhook_configs:
  - url: 'http://dcimonitor:8080/api/v1/alerts/webhook/prometheus'
    send_resolved: true

# 抑制规则 - 避免任务期间的告警风暴
inhibit_rules:
- source_match:
    alertname: 'NetworkInterfaceDown'
  target_match:
    alertname: 'InterfaceTrafficLow'
  equal: ['device_id', 'ifName']
```

## 3.2 应用配置

### 3.2.1 dcimonitor服务配置

在现有dcimonitor配置文件中添加告警联动参数：

**config/dcimonitor.yaml增强配置**：
```yaml
# 现有配置保持不变...

# 任务协同监控配置(基于17号文档)
task_monitoring:
  enabled: true
  # 延展监测配置
  extended_monitoring:
    enabled: true
    duration: "1h"        # 任务结束后延展监测时长
  # 会话管理配置
  session_management:
    cleanup_interval: "24h"    # 清理已完成会话的间隔
    max_sessions: 1000         # 最大并发会话数

# 告警联动配置(基于24-02文档)  
alert_association:
  enabled: true
  # 设备ID提取配置
  device_id_extraction:
    # 设备ID标签优先级
    priority_labels:
      - "device_id"
      - "device"
      - "device_ip"
    # instance标签解析
    instance_parsing:
      enabled: true
      remove_port: true       # 从instance中移除端口号
    # 其他设备标识标签
    fallback_labels:
      - "host"
      - "hostname"
      - "node"
  # 任务关联查询优化
  association_query:
    cache_enabled: true
    cache_ttl: "5m"          # 活跃会话缓存时间
    batch_size: 100          # 批量查询大小

# Prometheus集成配置
prometheus_integration:
  # WebHook处理优化
  webhook_processing:
    async: true              # 异步处理WebHook
    queue_size: 1000         # 处理队列大小
    workers: 5               # 并发处理器数量
  # 告警增强配置
  alert_enhancement:
    add_device_annotations: true    # 添加设备相关注释
    add_task_context: true          # 添加任务上下文信息
```

### 3.2.2 设备ID映射规则配置

针对不同的Prometheus采集源，配置设备ID映射规则：

**device_mapping.yaml**：
```yaml
# 设备ID映射规则配置
device_id_mapping:
  # SNMP采集的设备
  snmp_devices:
    source_pattern: "dci_snmp_.*"
    device_id_labels:
      - "device_id"         # 优先使用device_id标签
      - "device_ip"         # 其次使用device_ip标签
    instance_pattern: "^(\\d+\\.\\d+\\.\\d+\\.\\d+):(\\d+)$"
    instance_device_group: 1   # 正则表达式中设备IP的组号

  # 网络设备Agent采集
  network_agents:
    source_pattern: "network_.*"
    device_id_labels:
      - "device"
      - "host"
    instance_pattern: "^([^:]+):(\\d+)$"
    instance_device_group: 1

  # 系统监控指标
  system_metrics:
    source_pattern: "node_.*"
    device_id_labels:
      - "instance"         # 对于node_exporter，instance就是设备标识
      - "hostname" 
    instance_pattern: "^([^:]+):(\\d+)$"
    instance_device_group: 1
```

## 3.3 使用流程

### 3.3.1 网络自动化任务启动流程

基于17号文档的任务协同API，网络自动化任务的标准启动流程：

**1. 启动任务监控**：
```bash
# 网络自动化系统调用DCI监控API
curl -X POST "http://dcimonitor:8080/api/v1/tasks/network-upgrade-001/monitoring/start" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -d '{
    "deviceIds": ["************", "************", "switch001", "switch002"]
  }'

# 响应示例
{
  "success": true,
  "sessionId": "session-uuid-001",
  "message": "Task monitoring started",
  "data": {
    "taskId": "network-upgrade-001", 
    "deviceCount": 4,
    "startTime": "2025-01-11T10:15:30Z"
  }
}
```

**2. 执行网络自动化任务**：
- 网络自动化系统开始执行配置变更
- Prometheus持续采集相关设备指标
- AlertManager根据告警规则检测异常
- 触发的告警通过WebHook发送到DCI监控系统

**3. 自动告警关联处理**(基于24-02文档的设备ID提取机制)：
```go
// DCI监控系统处理告警WebHook时的自动关联逻辑
func (s *AlertService) ProcessPrometheusWebhook(alerts []*PrometheusAlert) {
    for _, promAlert := range alerts {
        // 1. 提取设备ID(使用24-02文档的ExtractDeviceID函数)
        deviceID := ExtractDeviceID(promAlert.Labels)
        
        // 2. 查询设备关联的任务(使用17号文档的关联服务)
        taskResult := s.taskService.GetTaskByDeviceWithExtendedMonitoring(deviceID)
        
        // 3. 创建告警记录
        alert := ConvertPrometheusAlert(promAlert)
        if deviceID != "" {
            alert.DeviceID = deviceID  // 设置设备ID字段
        }
        
        // 4. 增强告警元数据(使用24-02文档的增强逻辑)
        if taskResult.TaskID != "" {
            EnhanceAlertMetadata(alert, deviceID, taskResult)
        }
        
        // 5. 保存到monitor_alert表
        alertID := s.alertDAO.Create(alert)
        
        // 6. 创建任务告警关联记录(如果有关联任务)
        if taskResult.TaskID != "" {
            association := &TaskAlertAssociation{
                ID: uuid.New().String(),
                TaskID: taskResult.TaskID,
                AlertID: alertID,
                AssociationType: taskResult.AlertType,
            }
            s.taskService.CreateTaskAlertAssociation(ctx, association)
        }
    }
}
```

**4. 停止任务监控**：
```bash
# 网络自动化任务完成后调用停止API
curl -X POST "http://dcimonitor:8080/api/v1/tasks/network-upgrade-001/monitoring/stop" \
  -H "Authorization: Bearer ${API_TOKEN}"

# 响应示例
{
  "success": true,
  "message": "Task monitoring stopped", 
  "data": {
    "taskId": "network-upgrade-001",
    "sessionId": "session-uuid-001",
    "endTime": "2025-01-11T11:30:00Z",
    "duration": "1h14m30s",
    "alertSummary": {
      "totalAlerts": 8,
      "criticalAlerts": 2,
      "warningAlerts": 5,
      "infoAlerts": 1,
      "activePhaseAlerts": 6,
      "postTaskAlerts": 2
    }
  }
}
```

### 3.3.2 任务相关告警查询

基于17号文档的告警查询API，获取任务执行期间的告警信息：

**查询任务告警列表**：
```bash
# 查询任务相关的所有告警
curl "http://dcimonitor:8080/api/v1/tasks/network-upgrade-001/alerts?start_time=2025-01-11T10:00:00Z&end_time=2025-01-11T12:00:00Z&limit=20" \
  -H "Authorization: Bearer ${API_TOKEN}"

# 响应示例
{
  "success": true,
  "data": {
    "alerts": [
      {
        "id": "alert-001",
        "name": "NetworkInterfaceDown", 
        "level": "critical",
        "status": "resolved",
        "deviceId": "************",
        "startsAt": "2025-01-11T10:20:00Z",
        "endsAt": "2025-01-11T10:25:00Z", 
        "taskRelation": "任务执行期间",
        "monitoringPhase": "active_execution",
        "description": "接口 GigabitEthernet0/1 在设备 ************ 上状态异常"
      },
      {
        "id": "alert-002",
        "name": "DeviceHighCPU",
        "level": "warning", 
        "status": "firing",
        "deviceId": "switch001",
        "startsAt": "2025-01-11T11:35:00Z",
        "endsAt": null,
        "taskRelation": "任务后续影响", 
        "monitoringPhase": "post_completion",
        "description": "设备 switch001 CPU使用率为 85%，持续超过5分钟"
      }
    ],
    "statistics": {
      "totalCount": 8,
      "levelCount": {"critical": 2, "warning": 5, "info": 1},
      "phaseCount": {"active_execution": 6, "post_completion": 2}
    }
  }
}
```

**查询任务告警统计**：
```bash
# 获取任务告警的统计信息
curl "http://dcimonitor:8080/api/v1/tasks/network-upgrade-001/alerts/statistics" \
  -H "Authorization: Bearer ${API_TOKEN}"

# 响应示例  
{
  "success": true,
  "data": {
    "taskId": "network-upgrade-001",
    "timeRange": {
      "startTime": "2025-01-11T10:15:30Z",
      "endTime": "2025-01-11T12:30:00Z"
    },
    "totalCount": 8,
    "levelCount": {"critical": 2, "warning": 5, "info": 1},
    "statusCount": {"firing": 1, "resolved": 7},
    "phaseCount": {"active_execution": 6, "post_completion": 2},
    "deviceCount": {"************": 3, "************": 2, "switch001": 2, "switch002": 1},
    "timeDistribution": [
      {"timeSlot": "2025-01-11T10:15:00Z", "count": 1},
      {"timeSlot": "2025-01-11T10:30:00Z", "count": 3},
      {"timeSlot": "2025-01-11T11:00:00Z", "count": 2},
      {"timeSlot": "2025-01-11T11:30:00Z", "count": 2}
    ]
  }
}
```

# 4 部署与维护

## 4.1 部署检查清单

### 4.1.1 数据库部署
- [ ] 执行任务监控会话表创建脚本(来自17号文档)
- [ ] 执行告警表device_id字段添加脚本(来自24-02文档)
- [ ] 执行任务告警关联表创建脚本(来自24-02文档)
- [ ] 验证索引和外键约束创建成功
- [ ] 确认现有告警数据完整性

### 4.1.2 应用配置部署
- [ ] 更新dcimonitor配置文件，添加任务协同监控配置
- [ ] 配置设备ID映射规则文件
- [ ] 验证API访问权限配置
- [ ] 确认告警WebHook处理配置

### 4.1.3 Prometheus配置部署  
- [ ] 更新告警规则文件，确保包含设备标识标签
- [ ] 验证AlertManager WebHook配置
- [ ] 测试告警规则语法正确性
- [ ] 确认告警通知路由配置

### 4.1.4 集成测试
- [ ] 创建测试任务监控会话
- [ ] 触发测试告警验证自动关联
- [ ] 验证延展监测机制
- [ ] 确认告警查询API正常工作

## 4.2 监控指标

基于现有监控体系，添加告警联动相关的监控指标：

**任务协同监控指标**：
```yaml
# 活跃任务会话数量
dci_task_sessions_active_total

# 告警关联成功率  
dci_alert_association_success_rate

# 设备ID提取成功率
dci_device_id_extraction_success_rate

# WebHook处理延迟
dci_webhook_processing_duration_seconds

# 延展监测匹配数量
dci_extended_monitoring_matches_total
```

## 4.3 故障排查

### 4.3.1 常见问题及解决方案

**问题1：告警无法关联到任务**
- 检查设备ID提取逻辑，确认告警Labels包含设备标识
- 验证任务会话是否正确创建，设备ID列表是否包含该设备
- 检查任务会话状态，确认为active状态
- 检查monitor_task_alert_associations表中是否创建了关联记录

**问题2：延展监测期告警丢失**
- 检查任务结束时间设置是否正确
- 验证延展监测配置duration设置
- 确认数据库时区设置正确(Asia/Shanghai)

**问题3：WebHook处理性能问题**
- 启用WebHook异步处理配置
- 调整处理队列大小和并发处理器数量
- 检查告警关联查询性能，考虑启用缓存

### 4.3.2 日志查看

**关键日志位置**：
```bash
# 任务协同监控日志
tail -f /var/log/dcimonitor/task-monitoring.log

# 告警关联处理日志  
tail -f /var/log/dcimonitor/alert-association.log

# WebHook处理日志
tail -f /var/log/dcimonitor/webhook-processing.log
```

**重要日志关键词**：
- `TaskMonitoringSession created` - 任务会话创建
- `AlertAssociation found` - 告警关联成功
- `DeviceID extracted` - 设备ID提取
- `ExtendedMonitoring matched` - 延展监测匹配

## 4.4 性能优化建议

### 4.4.1 数据库优化
- 定期清理已完成的任务会话数据(建议保留30天)
- 为高频查询字段添加复合索引
- 使用读写分离提升查询性能

### 4.4.2 应用优化
- 启用活跃任务会话内存缓存
- 使用连接池优化数据库连接
- 批量处理WebHook请求提升吞吐量

### 4.4.3 监控优化
- 根据实际业务调整延展监测时长
- 优化告警规则减少无效告警
- 设置合理的告警分组和抑制规则
