---
title: |
  DCI-任务协同期指标类数据联动技术方案

subtitle: |
  网络自动化任务执行期间指标监测与变化分析技术设计
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | --------- | ----- | ----------------- |
| V1.0 | 2025-01-18 | 顾铠羟 | 技术大纲初始版本   |

# 1 文档介绍

## 1.1 文档目的

本文档设计任务协同期指标类数据联动技术方案，实现网络自动化任务执行前、期间、后的设备指标数据记录与统计呈现。该方案基于现有的任务协同监控模块和 Prometheus 指标体系，提供设备 CPU、内存、流量等关键指标的基线统计、对比数据记录和时序数据查询能力。

## 1.2 文档范围

本文档涵盖的内容包括：

- 指标数据联动机制设计
- 任务期间指标基线统计与对比数据记录
- 指标数据记录与时序数据管理
- 与现有任务协同监控模块的集成方案
- 指标查询与数据呈现 API 设计

## 1.3 文档关联

- 《17-DCI-网络自动化任务协同监控技术方案设计.md》：任务协同监控的基础架构
- 《17-01-DCI-任务协同模块API说明.md》：现有任务协同 API 接口
- 《18-DCI-设备端口流量查询服务设计方案.md》：流量指标查询服务
- 《19-01-DCI-流量类数据prometheus指标设计.md》：Prometheus 流量指标体系
- 《22-DCI-交换机状态监测技术设计.md》：交换机 CPU、内存监测
- 《03-DCI项目API及代码规范指南.md》：API 设计和代码规范

## 1.4 主要专有名词定义说明

### 1.4.1 任务协同监控相关

- **任务协同监控**：网络自动化任务与 DCI 监测系统的协同工作机制，通过接收任务信号实现任务执行全生命周期的监控管理
- **任务监控会话**：为每个网络自动化任务创建的监控实例，包含任务元数据、监测状态和关联数据
- **延展监测**：任务结束后 1 小时内的持续监测期，用于捕获网络配置变更的滞后影响

### 1.4.2 时间参数配置化设计

**配置化原则**：本方案中的关键时间参数支持通过配置文件进行调整，避免硬编码，提高系统灵活性。

**配置文件路径**：`config/config.yaml`

**时间参数配置**：
```yaml
task_collaboration:
  metrics_monitoring:
    baseline_duration: "30m"      # 基线期时长：任务开始前的数据分析时间段
    extended_duration: "1h"       # 延展监测时长：任务结束后的持续监测时间
```

**设计说明**：文档中涉及的"30分钟"、"1小时"等具体时间值均为配置的默认值示例，实际运行时以配置文件中的设置为准。

### 1.4.3 指标监测相关

- **指标基线**：任务执行前 30 分钟内设备指标的统计特征，包括平均值、最大值、最小值、标准差，作为任务执行期间指标变化的参考基准
- **指标联动**：基于任务信号触发，从 Prometheus 中查询任务目标设备的历史和实时指标数据（CPU、内存、流量等），建立基线统计并记录数据变化，实现任务执行与指标数据收集的同步协调

### 1.4.4 监测阶段定义

- **基线期**：任务开始前 30 分钟（baseline_duration）的历史时间段，用于从已有数据中建立指标基线统计
- **任务期**：任务实际执行期间，从持续的数据流中进行实时指标数据记录
- **延展期**：任务结束后 1 小时（extended_duration），继续从数据流中记录指标数据变化

### 1.4.5 技术组件相关

- **指标数据收集器 (MetricsCollector)**：负责从 Prometheus 查询设备指标数据的核心组件
- **基线统计计算器 (BaselineCalculator)**：负责建立、管理和计算指标基线统计的组件
- **指标数据记录器 (MetricsRecorder)**：负责记录指标数据变化情况和基础统计计算的组件
- **PromQL**：Prometheus 查询语言，用于从时序数据库中查询指标数据

# 2 总体设计

## 2.1 设计目标

### 2.1.1 核心目标

1. **指标基线统计**：在收到任务执行消息后，自动从 Prometheus 查询目标设备过去 30 分钟（可配置）的历史指标数据并建立基线统计
2. **实时指标记录**：在任务执行期间持续记录指标数据变化
3. **延展期跟踪**：在任务结束后 1 小时（可配置）内继续跟踪指标变化
4. **变化统计**：提供任务前后指标对比数据和变化趋势统计
5. **数据呈现**：通过API接口提供完整的指标时序数据供人工分析

### 2.1.2 功能特性

- 支持 CPU 使用率、内存使用率、网络流量等多维度指标数据记录
- 提供指标数据的时间线查询和可视化数据API接口
- 集成现有的任务协同监控生命周期管理
- 支持多设备并行指标数据收集和统计计算
- 提供完整的指标时序数据供人工分析和后续算法开发

## 2.2 技术架构

### 2.2.1 整体架构设计

指标数据联动功能实现为独立的指标监测模块 `internal/metrics_monitoring`，与任务协同监控模块并行运行，通过统一的API网关和服务注册机制协同工作。

```mermaid
graph TD
    subgraph "网络自动化控制系统"
        NetAutoCtrl[自动化控制服务]
    end
    
    subgraph "DCI监测系统"
        subgraph "任务协同模块 (task_collaboration)"
            TaskReceiver[任务信号接收器]
            TaskSession[任务会话管理器]
        end
        
        subgraph "指标监测模块 (metrics_monitoring)"
            MetricsCollector[指标数据收集器]
            BaselineCalculator[基线统计计算器]
            MetricsRecorder[指标数据记录器]
            MetricsHandler[指标API处理器]
        end
        
        subgraph "现有服务"
            TrafficService[流量查询服务]
            StatusService[状态监测服务]
            PrometheusAPI[Prometheus查询API]
        end
        
        subgraph "存储层"
            Prometheus[(Prometheus<br/>指标数据)]
            MySQL[(MySQL<br/>任务数据)]
        end
    end
    
    NetAutoCtrl -->|任务信号| TaskReceiver
    TaskReceiver --> TaskSession
    TaskSession -.->|任务状态通知| MetricsCollector
    MetricsCollector --> TrafficService
    MetricsCollector --> StatusService
    MetricsCollector --> PrometheusAPI
    MetricsCollector --> BaselineCalculator
    BaselineCalculator --> MetricsRecorder
    MetricsHandler --> MetricsCollector
    
    TrafficService --> Prometheus
    StatusService --> Prometheus
    PrometheusAPI --> Prometheus
    TaskSession --> MySQL
    MetricsRecorder --> MySQL
    
    classDef primary fill:#d9f,stroke:#333,stroke-width:1px
    classDef metrics fill:#f9d,stroke:#333,stroke-width:1px
    classDef secondary fill:#bbf,stroke:#333,stroke-width:1px
    classDef storage fill:#ffd,stroke:#333,stroke-width:1px
    
    class TaskReceiver,TaskSession primary
    class MetricsCollector,BaselineCalculator,MetricsRecorder,MetricsHandler metrics
    class TrafficService,StatusService,PrometheusAPI secondary
    class Prometheus,MySQL storage
```

### 2.2.2 核心组件职责

1. **指标数据收集器 (MetricsCollector)**：负责从 Prometheus 查询指标数据
2. **基线统计计算器 (BaselineCalculator)**：负责建立和管理指标基线统计
3. **指标数据记录器 (MetricsRecorder)**：负责记录指标数据变化情况和基础统计计算
4. **任务会话管理器 (TaskSession)**：扩展现有功能，集成指标联动

## 2.3 数据流程设计

### 2.3.1 指标联动流程

指标联动基于已有的持续指标采集体系，通过任务信号触发对现有数据的分析和监测：

1. **持续采集基础**：telegraf 持续采集设备指标并存储到 Prometheus，形成连续的时序数据流
2. **任务信号触发**：接收到任务开始信号后，立即启动实时监测，同时异步进行基线分析
3. **实时记录优先**：优先启动对后续数据流的实时数据记录，确保不遗漏任务期间的关键数据
4. **基线数据统计**：并行或异步从任务开始时间往前30分钟的历史数据中建立指标基线统计，为数据对比提供参考

```mermaid
sequenceDiagram
    participant Telegraf as Telegraf采集器
    participant SNMPStatus as dcimonitor-snmpstatus
    participant Prometheus as Prometheus
    participant NetCtrl as 网络自动化系统
    participant TaskSession as 任务会话管理器
    participant MetricsCollector as 指标收集器
    participant BaselineCalculator as 基线统计计算器
    participant MetricsRecorder as 指标数据记录器
    
    Note over Telegraf,Prometheus: 持续的指标采集体系（已运行）
    loop 持续采集
        Telegraf->>SNMPStatus: SNMP指标采集
        SNMPStatus->>Prometheus: 存储指标数据
    end
    
    Note over NetCtrl,TaskSession: 任务协同流程启动
    NetCtrl->>TaskSession: 任务开始信号
    TaskSession->>MetricsCollector: 启动指标监测会话
    
    Note over MetricsCollector: 优先启动实时数据记录
    MetricsCollector->>MetricsRecorder: 启动实时数据记录
    
    par 并行执行
        Note over MetricsCollector: 实时数据记录（优先）
        loop 任务执行期间
            Telegraf->>SNMPStatus: 继续采集指标
            SNMPStatus->>Prometheus: 存储新数据
            MetricsCollector->>Prometheus: 查询最新指标数据
            Prometheus-->>MetricsCollector: 返回当前数据
            MetricsCollector->>MetricsRecorder: 记录数据变化
        end
    and
        Note over MetricsCollector: 基线数据统计（异步）
        MetricsCollector->>Prometheus: 查询任务开始前30分钟指标
        Prometheus-->>MetricsCollector: 返回历史基线数据
        MetricsCollector->>BaselineCalculator: 建立指标基线统计
        BaselineCalculator-->>MetricsRecorder: 基线数据就绪
    end
    
    Note over MetricsRecorder: 基线就绪后开始对比数据记录
    loop 任务执行期间（基线就绪后）
        MetricsRecorder->>BaselineCalculator: 对比基线数据
        MetricsRecorder-->>MetricsCollector: 数据记录结果
    end
    
    NetCtrl->>TaskSession: 任务结束信号
    TaskSession->>MetricsCollector: 切换到延展数据记录模式
    
    Note over MetricsCollector: 延展期数据记录（继续监听数据流）
    loop 延展监测期(1小时)
        Telegraf->>SNMPStatus: 继续采集指标
        SNMPStatus->>Prometheus: 存储新数据
        MetricsCollector->>Prometheus: 查询指标数据
        Prometheus-->>MetricsCollector: 返回数据
        MetricsCollector->>MetricsRecorder: 记录延展期数据
    end
    
    TaskSession->>TaskSession: 完成指标数据记录会话
```

# 3 核心功能模块

## 3.1 指标数据收集器 (MetricsCollector)

### 3.1.1 功能职责

- 根据任务目标设备列表确定监测范围
- 从 Prometheus 中查询已有的指标时序数据（基线期、任务期、延展期）
- 调用现有的流量查询服务和状态监测服务
- 管理数据查询频率和分析处理，无需重复采集

### 3.1.2 支持的指标类型

**基于 dcimonitor-snmpstatus 实际实现，验证支持以下指标**：

1. **设备状态指标**
   - CPU 使用率：`dci_snmp_status_cpu_usage`
     - 标签：`device_id`, `device_name`, `device_ip`, `entity_index`, `entity_name`, `entity_type`
   - 内存使用率：`dci_snmp_status_memory_usage`
     - 标签：`device_id`, `device_name`, `device_ip`, `entity_index`, `entity_name`, `entity_type`

2. **网络流量指标**
   - 入向流量：`dci_snmp_flow_ifHCInOctets`
     - 标签：`device_id`, `device_name`, `device_ip`, `ifName`, `ifIndex`
   - 出向流量：`dci_snmp_flow_ifHCOutOctets`
     - 标签：`device_id`, `device_name`, `device_ip`, `ifName`, `ifIndex`

3. **接口状态指标**
   - 运行状态：`dci_snmp_status_interface`
     - 标签：`device_id`, `device_name`, `device_ip`, `ifName`, `ifIndex`
     - 值：1=up, 2=down, 3=testing, 4=unknown, 5=dormant, 6=notPresent, 7=lowerLayerDown
   - 管理状态：`dci_snmp_status_interface_admin`
     - 标签：`device_id`, `device_name`, `device_ip`, `ifName`, `ifIndex`
     - 值：1=up, 2=down, 3=testing

### 3.1.3 数据查询策略

- **基线期数据**：任务开始时，查询过去 30 分钟的历史数据进行基线分析
- **任务期监测**：任务执行期间，每分钟查询最新数据进行实时分析
- **延展期监测**：任务结束后 1 小时内，每分钟查询数据进行影响分析

**指标可用性说明**：
- 以上指标均由 telegraf + dcimonitor-snmpstatus 持续采集和导出到 Prometheus
- 设备状态指标（CPU、内存）基于华为设备的 SNMP OID 采集
- 流量指标基于标准 IF-MIB 中的高容量计数器采集
- 接口状态指标基于标准 IF-MIB 中的管理和运行状态采集

注：本模块仅负责数据查询和分析，不重复采集指标数据

## 3.2 基线统计计算器 (BaselineCalculator)

### 3.2.1 功能职责

- 建立任务执行前的指标基线统计
- 计算基线统计特征（平均值、最大值、最小值、标准差）
- 提供基线统计数据的查询和对比数据计算
- 支持历史基线统计数据的存储和管理

### 3.2.2 基线数据模型

```go
type MetricBaseline struct {
    TaskID      string    `json:"taskId"`
    DeviceID    string    `json:"deviceId"`
    MetricName  string    `json:"metricName"`
    TimeRange   TimeRange `json:"timeRange"`
    AvgValue    float64   `json:"avgValue"`
    MaxValue    float64   `json:"maxValue"`
    MinValue    float64   `json:"minValue"`
    StdDev      float64   `json:"stdDev"`
    SampleCount int       `json:"sampleCount"`
    CreatedAt   time.Time `json:"createdAt"`
}
```

## 3.3 指标数据记录器 (MetricsRecorder)

### 3.3.1 功能职责

- 持续记录指标数据和变化情况（优先启动，无需等待基线）
- 计算指标变化的基础统计数据（基线就绪后进行对比计算）
- 记录指标数据变化事件到数据库
- 提供变化数据的API查询接口
- 支持基线对比数据计算和时序数据管理

### 3.3.2 异常检测算法

**当前实施方案**：异常检测在具体计算方面的算法暂且不开发，只做指标数据记录和通过API做数据呈现。

**功能定位**：
- **数据收集**：按照配置的查询间隔（如每分钟）持续从Prometheus收集目标设备的指标数据
- **数据存储**：将实时指标值、基线统计数据存储到数据库中
- **数据呈现**：通过API接口提供指标的时序数据、基线对比数据、变化趋势数据

**记录的数据类型**：
1. **实时指标值**：每分钟查询的CPU、内存、流量等当前数值
2. **基线统计特征**：任务开始前30分钟的平均值、最大值、最小值、标准差
3. **变化对比数据**：当前值与基线值的差值、变化百分比
4. **时序数据**：完整的指标时间序列，支持前端图表展示

# 4 数据模型设计

## 4.1 数据表关联关系

### 4.1.1 表关联关系图

```mermaid
erDiagram
    monitor_network_auto_task_monitoring_sessions ||--o{ monitor_task_metrics_sessions : "1:N 关联"
    monitor_task_metrics_sessions ||--o{ monitor_task_metrics_baselines : "1:N 关联"
    monitor_task_metrics_sessions ||--o{ monitor_task_metrics_changes : "1:N 关联"
    
    monitor_network_auto_task_monitoring_sessions {
        string session_id PK "任务会话ID"
        string task_id "任务ID"
        enum status "会话状态"
        timestamp created_at "创建时间"
    }
    
    monitor_task_metrics_sessions {
        string session_id PK "指标监测会话ID"
        string task_id "任务ID"
        string task_session_id FK "关联任务会话ID"
        json device_ids "监测设备ID列表"
        json metrics_types "监测指标类型列表"
        timestamp baseline_start "基线分析开始时间"
        timestamp baseline_end "基线分析结束时间"
        timestamp monitoring_start "任务监测开始时间"
        timestamp monitoring_end "任务监测结束时间"
        timestamp extended_end "延展监测结束时间"
        enum status "监测状态"
        timestamp created_at "创建时间"
    }
    
    monitor_task_metrics_baselines {
        string id PK "基线记录ID"
        string metrics_session_id FK "指标监测会话ID"
        string device_id "设备ID"
        string metric_name "指标名称"
        double avg_value "平均值"
        double max_value "最大值"
        double min_value "最小值"
        double std_dev "标准差"
        int sample_count "样本数量"
        timestamp time_range_start "时间范围开始"
        timestamp time_range_end "时间范围结束"
        timestamp created_at "创建时间"
    }
    
    monitor_task_metrics_changes {
        string id PK "数据记录ID"
        string metrics_session_id FK "指标监测会话ID"
        string device_id "设备ID"
        string metric_name "指标名称"
        enum change_type "记录类型"
        double current_value "当前值"
        double baseline_value "基线值"
        double change_magnitude "变化幅度"
        timestamp detection_time "数据记录时间"
        enum monitoring_phase "监测阶段"
        enum severity "严重程度"
        text description "数据描述"
        timestamp created_at "创建时间"
    }
```

### 4.1.2 关联关系说明

**数据关联方式说明**：
- **外键关联**：表间关系通过标准的关系型数据库外键实现，每条关联数据都是独立的数据行
- **JSON字段**：在 `monitor_task_metrics_sessions` 表中使用JSON数组存储设备列表和指标类型列表

**具体关联关系**：

1. **任务会话 → 指标监测会话**：一对一关系
   - 一个任务会话对应一个指标监测会话
   - 通过 `task_session_id` 外键关联到 `monitor_network_auto_task_monitoring_sessions.session_id`
   - **删除策略**：限制删除，尝试删除指标监测会话时数据库会阻止操作 (`ON DELETE RESTRICT`)

2. **指标监测会话 → 指标基线数据**：一对多关系
   - 一个指标监测会话对应多条基线记录（每个设备、每种指标类型一条独立记录）
   - 通过 `metrics_session_id` 外键关联到 `monitor_task_metrics_sessions.session_id`
   - **删除策略**：限制删除，尝试删除基线数据时数据库会阻止操作 (`ON DELETE RESTRICT`)

3. **指标监测会话 → 指标数据记录**：一对多关系
   - 一个指标监测会话对应多条指标数据记录（按时间、设备、指标类型分组的独立记录）
   - 通过 `metrics_session_id` 外键关联到 `monitor_task_metrics_sessions.session_id`
   - **删除策略**：限制删除，尝试删除指标数据记录时数据库会阻止操作 (`ON DELETE RESTRICT`)

### 4.1.3 删除策略设计说明

**统一限制删除策略**：
- 所有子表均采用 `ON DELETE RESTRICT` 限制删除策略
- 任何尝试单独删除子表数据的操作都会被数据库阻止，返回外键约束错误
- 这确保了数据的完整性和一致性，防止出现孤立的父表记录

**设计原因**：
1. **数据一致性保护**：防止意外删除子表数据导致父表数据失去完整性
2. **强制正确删除顺序**：必须按照正确的层级顺序删除数据，确保关联关系的完整性
3. **错误防护机制**：通过数据库约束自动防止不当的删除操作

**正确的删除流程**：
1. **完整删除**：删除任务会话时，需要先手动删除所有关联的指标数据
2. **分层删除**：先删除指标数据记录 → 再删除指标基线数据 → 再删除指标监测会话 → 最后删除任务会话
3. **程序化删除**：在应用层实现级联删除逻辑，确保按正确顺序删除所有关联数据

### 4.1.4 索引设计说明

- **主键索引**：所有表均使用字符串主键，确保全局唯一性
- **外键索引**：在所有外键字段上建立索引，优化关联查询性能
- **业务查询索引**：
  - 按状态和时间组合查询的复合索引
  - 按设备ID查询的单列索引
  - 按监测阶段和时间组合查询的复合索引

## 4.2 核心数据模型

### 4.2.1 任务指标监测会话

```sql
-- 任务指标监测会话表（与现有task_collaboration表结构保持一致）
CREATE TABLE IF NOT EXISTS monitor_task_metrics_sessions (
    session_id VARCHAR(64) PRIMARY KEY COMMENT '指标监测会话ID',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    task_session_id VARCHAR(64) NOT NULL COMMENT '关联任务会话ID', 
    device_ids JSON NOT NULL COMMENT '监测设备ID列表',
    metrics_types JSON NOT NULL COMMENT '监测指标类型列表',
    baseline_start TIMESTAMP NOT NULL COMMENT '基线统计开始时间(Asia/Shanghai)',
    baseline_end TIMESTAMP NOT NULL COMMENT '基线统计结束时间(Asia/Shanghai)',
    monitoring_start TIMESTAMP NOT NULL COMMENT '任务数据记录开始时间(Asia/Shanghai)',
    monitoring_end TIMESTAMP NULL COMMENT '任务数据记录结束时间(Asia/Shanghai)',
    extended_end TIMESTAMP NULL COMMENT '延展数据记录结束时间(Asia/Shanghai)',
    status ENUM('monitoring_active', 'baseline_analyzing', 'fully_active', 'extended_monitoring', 'completed') NOT NULL DEFAULT 'monitoring_active' COMMENT '数据记录状态：monitoring_active=实时数据记录已启动，baseline_analyzing=基线统计中，fully_active=基线统计+数据记录全部就绪，extended_monitoring=延展数据记录中，completed=已完成',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(Asia/Shanghai)',
    INDEX idx_task_id (task_id),
    INDEX idx_status (status),
    INDEX idx_status_time (status, monitoring_start),
    FOREIGN KEY (task_session_id) REFERENCES monitor_network_auto_task_monitoring_sessions(session_id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务指标监测会话表，与任务协同监控会话关联';
```

### 4.2.2 指标基线数据

```sql
-- 任务指标基线表（与现有数据库规范保持一致）
CREATE TABLE IF NOT EXISTS monitor_task_metrics_baselines (
    id VARCHAR(36) PRIMARY KEY COMMENT '基线记录ID',
    metrics_session_id VARCHAR(64) NOT NULL COMMENT '指标监测会话ID',
    device_id VARCHAR(64) NOT NULL COMMENT '设备ID',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    avg_value DOUBLE COMMENT '平均值',
    max_value DOUBLE COMMENT '最大值',
    min_value DOUBLE COMMENT '最小值',
    std_dev DOUBLE COMMENT '标准差',
    sample_count INT COMMENT '样本数量',
    time_range_start TIMESTAMP NOT NULL COMMENT '时间范围开始(Asia/Shanghai)',
    time_range_end TIMESTAMP NOT NULL COMMENT '时间范围结束(Asia/Shanghai)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(Asia/Shanghai)',
    INDEX idx_session_device (metrics_session_id, device_id),
    INDEX idx_metric_name (metric_name),
    INDEX idx_time_range (time_range_start, time_range_end),
    FOREIGN KEY (metrics_session_id) REFERENCES monitor_task_metrics_sessions(session_id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务指标基线统计表，存储任务执行前30分钟的指标统计特征';
```

### 4.2.3 指标数据记录

```sql
-- 任务指标数据记录表（当前阶段：仅记录数据，不做异常判断）
CREATE TABLE IF NOT EXISTS monitor_task_metrics_changes (
    id VARCHAR(36) PRIMARY KEY COMMENT '数据记录ID',
    metrics_session_id VARCHAR(64) NOT NULL COMMENT '指标监测会话ID',
    device_id VARCHAR(64) NOT NULL COMMENT '设备ID',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    change_type ENUM('data_record', 'threshold_exceeded', 'trend_change', 'sudden_change', 'pattern_anomaly') NOT NULL DEFAULT 'data_record' COMMENT '记录类型：当前阶段统一使用data_record，其他类型为后续异常检测算法预留',
    current_value DOUBLE NOT NULL COMMENT '当前值',
    baseline_value DOUBLE COMMENT '基线值（基线就绪后填充）',
    change_magnitude DOUBLE COMMENT '变化幅度（当前值-基线值）',
    detection_time TIMESTAMP NOT NULL COMMENT '数据记录时间(Asia/Shanghai)',
    monitoring_phase ENUM('baseline', 'task_execution', 'extended') NOT NULL COMMENT '监测阶段',
    severity ENUM('info', 'low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'info' COMMENT '当前阶段统一使用info，其他级别为后续算法预留',
    description TEXT COMMENT '数据描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(Asia/Shanghai)',
    INDEX idx_session_device (metrics_session_id, device_id),
    INDEX idx_detection_time (detection_time),
    INDEX idx_severity (severity),
    INDEX idx_phase_time (monitoring_phase, detection_time),
    FOREIGN KEY (metrics_session_id) REFERENCES monitor_task_metrics_sessions(session_id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务指标数据记录表，当前阶段仅记录指标数据，为后续异常检测算法预留扩展字段';
```

# 5 API 接口设计

## 5.0 设计原则

### 5.0.1 API设计原则

- **自动化优先**：指标数据记录由任务信号自动触发，API主要提供查询和管理功能
- **数据导向**：API接口直接对应数据表操作，确保与数据模型的一致性
- **只读为主**：主要提供数据查询和展示功能，符合"数据记录和呈现"的设计定位
- **删除管理**：由于采用限制删除策略，提供专门的数据管理接口处理级联删除
- **RESTful风格**：遵循标准的RESTful API设计规范

### 5.0.2 自动化流程说明

**正常工作流程**（无需手动API调用）：
1. 任务开始信号 → 自动创建指标数据记录会话 → 启动实时数据记录和基线统计
2. 任务结束信号 → 自动切换到延展数据记录模式
3. 延展期结束 → 自动完成数据记录会话

**手动API的作用**：
- 调试和测试验证
- 独立于任务信号的测试场景

### 5.0.3 数据表对应关系

- `monitor_task_metrics_sessions` ↔ 5.1 指标数据记录管理接口
- `monitor_task_metrics_baselines` ↔ 5.2 指标基线查询接口  
- `monitor_task_metrics_changes` ↔ 5.3 指标数据查询接口
- 级联操作 ↔ 5.4 数据管理接口

## 5.1 指标数据记录管理接口

### 5.1.1 查询指标数据记录状态

- **URL**: `GET /api/v1/tasks/{taskId}/metrics-monitoring/status`
- **功能**: 查询任务指标数据记录会话的当前状态
- **说明**: 返回 `monitor_task_metrics_sessions` 表中的状态信息，指标数据记录会话由任务信号自动创建

### 5.1.2 手动启动指标数据记录（调试用）

- **URL**: `POST /api/v1/tasks/{taskId}/metrics-monitoring/manual-start`
- **功能**: 手动启动任务的指标数据记录（仅用于调试）
- **说明**: 
  - **正常流程**：指标数据记录由任务开始信号自动触发，无需手动调用
  - **调试用途**：用于开发调试、测试验证或自动启动失败时的补充操作
  - **独立启动**：可在没有任务信号的情况下独立启动指标数据记录会话

### 5.1.3 手动停止指标数据记录

- **URL**: `POST /api/v1/tasks/{taskId}/metrics-monitoring/manual-stop`
- **功能**: 手动停止任务的指标数据记录（仅用于调试）
- **说明**: 
  - **正常流程**：指标数据记录由任务结束信号自动停止并切换到延展数据记录模式
  - **特殊情况**：用于紧急停止、异常处理或测试场景

## 5.2 指标基线查询接口

### 5.2.1 查询指标基线

- **URL**: `GET /api/v1/tasks/{taskId}/metrics-baseline`
- **功能**: 查询任务的指标基线统计数据
- **说明**: 返回 `monitor_task_metrics_baselines` 表中的基线统计特征数据

### 5.2.2 查询指标对比数据

- **URL**: `GET /api/v1/tasks/{taskId}/metrics-comparison`
- **功能**: 查询任务前后的指标对比数据
- **说明**: 返回基线值与实时值的对比数据，供前端展示和人工分析

## 5.3 指标数据查询接口

### 5.3.1 查询指标数据记录

- **URL**: `GET /api/v1/tasks/{taskId}/metrics-data`
- **功能**: 查询任务期间的指标数据记录（时序数据）
- **说明**: 返回 `monitor_task_metrics_changes` 表中的完整数据记录，支持按设备、指标类型、监测阶段、时间范围筛选

### 5.3.2 查询指标趋势数据

- **URL**: `GET /api/v1/tasks/{taskId}/metrics-trends`
- **功能**: 查询任务期间的指标变化趋势数据
- **说明**: 基于 `monitor_task_metrics_changes` 表数据生成时间序列图表数据，支持前端可视化展示

### 5.3.3 查询指标统计汇总

- **URL**: `GET /api/v1/tasks/{taskId}/metrics-statistics`
- **功能**: 查询任务的指标统计汇总数据
- **说明**: 基于基线表和数据记录表生成统计汇总，包括基线值、当前值、变化幅度等信息

## 5.4 数据管理接口

### 5.4.1 级联删除指标数据

- **URL**: `DELETE /api/v1/tasks/{taskId}/metrics-monitoring/cascade`
- **功能**: 按正确顺序级联删除任务的所有指标相关数据
- **说明**: 由于采用了限制删除策略，提供应用层的级联删除功能，按照正确顺序删除数据记录→基线数据→数据记录会话

### 5.4.2 查询数据关联状态

- **URL**: `GET /api/v1/tasks/{taskId}/metrics-monitoring/dependencies`
- **功能**: 查询指标数据记录的关联依赖状态
- **说明**: 返回各表间的数据关联情况，用于删除前的依赖检查

### 5.4.3 数据清理接口

- **URL**: `DELETE /api/v1/tasks/{taskId}/metrics-data/cleanup`
- **功能**: 清理指定时间范围外的历史指标数据记录
- **说明**: 支持按时间范围清理过期数据，需要按照正确的删除顺序处理

# 6 集成方案设计

## 6.1 与任务协同模块协作

### 6.1.1 独立模块架构

指标监测功能实现为独立的 `internal/metrics_monitoring` 模块，具备完整的分层架构：

```
internal/metrics_monitoring/
├── handler.go              # HTTP API处理器 (11个API接口)
├── service.go              # 业务逻辑层
├── dao.go                  # 数据访问层
├── model.go                # 数据模型定义
├── metrics_collector.go    # 指标数据收集器
├── baseline_calculator.go  # 基线统计计算器
└── metrics_recorder.go     # 指标数据记录器
```

**模块特点**：
- **独立Handler**：提供完整的11个REST API接口，支持指标监测的全生命周期管理
- **独立Service**：实现指标监测的核心业务逻辑，包括基线计算、数据记录等
- **独立DAO**：管理指标相关的3个数据库表，支持完整的CRUD操作
- **复用TrafficDAO**：通过依赖注入使用现有的 `internal/traffic/dao.go` 中的Prometheus查询接口

### 6.1.2 模块间协作机制

两个独立模块通过以下方式实现协作：

**任务协同模块** (`task_collaboration`) **职责**：
- 接收和处理来自网络自动化系统的任务信号
- 管理任务监控会话的生命周期
- 通过依赖注入的接口调用指标监测模块进行数据记录

**指标监测模块** (`metrics_monitoring`) **职责**：
- 提供独立的API接口供外部调用
- 管理指标数据的采集、基线计算和变化记录
- 响应任务协同模块的直接函数调用，自动启动/停止指标监测

**协作实现方式**：
- **接口定义**：在`task_collaboration`中定义`MetricsService`接口，避免循环依赖
- **依赖注入**：通过setter方法将`metrics_monitoring.Service`注入到`task_collaboration.Service`
- **适配器模式**：使用适配器包装不同的方法签名，实现接口适配
- **可选集成**：指标监测功能为可选，失败不影响主要任务流程

### 6.1.3 具体集成方式

#### 接口定义和依赖注入

**任务协同模块接口定义** (`task_collaboration/service.go`)：
```go
// MetricsService 定义指标监测服务接口，避免循环依赖
type MetricsService interface {
    StartMetricsMonitoring(ctx context.Context, taskID, taskSessionID string, deviceIDs []string, metricTypes []string) error
    StopMetricsMonitoring(ctx context.Context, taskID string) error
}

// Service 结构体扩展
type Service struct {
    dao            *DAO
    taskDAO        *alert.TaskDAO
    alertDAO       *alert.DAO
    metricsService MetricsService // 可选的指标监测服务
    logger         *zap.Logger
}

// SetMetricsService 设置指标监测服务（可选集成）
func (s *Service) SetMetricsService(metricsService MetricsService) {
    s.metricsService = metricsService
    s.logger.Info("指标监测服务已集成")
}
```

#### 任务信号处理集成

**任务开始信号处理** (`task_collaboration/service.go`)：
```go
func (s *Service) handleTaskStart(signal *TaskSignal, requestID string) (*TaskSignalResponse, error) {
    // 现有逻辑：创建任务会话、启动数据收集
    session := s.createTaskSession(signal)
    s.startDataCollection(session)
    
    // 新增：可选启动指标监测
    if s.metricsService != nil {
        ctx := context.Background()
        metricTypes := []string{"cpu", "memory", "traffic"}
        err = s.metricsService.StartMetricsMonitoring(ctx, signal.TaskID, session.SessionID, signal.TargetDevices, metricTypes)
        if err != nil {
            s.logger.Warn("启动指标监测失败（不影响主要流程）", zap.Error(err))
        }
    }
}
```

#### 服务器层面集成

**主服务器集成** (`cmd/server.go`)：
```go
// 初始化两个独立模块
metricsService := metrics_monitoring.NewService(db, trafficDAO, metricsConfig, logger)
taskCollaborationModule := task_collaboration.NewModule(db, taskDAO, alertDAO, config, logger)

// 通过适配器实现接口适配
type metricsServiceAdapter struct {
    service *metrics_monitoring.Service
}

func (m *metricsServiceAdapter) StartMetricsMonitoring(ctx context.Context, taskID, taskSessionID string, deviceIDs []string, metricTypes []string) error {
    _, err := m.service.StartMetricsMonitoring(ctx, taskID, taskSessionID, deviceIDs, metricTypes)
    return err // 只返回错误，忽略详细响应
}

// 依赖注入集成
if taskCollaborationModule != nil && taskCollaborationModule.IsEnabled() {
    metricsAdapter := &metricsServiceAdapter{service: metricsService}
    taskCollaborationModule.Handler.SetMetricsService(metricsAdapter)
    logger.Info("任务协同模块与指标监测模块集成成功")
}
```

#### 模块协作集成

**任务协同模块调用指标监测模块**：
```go
// 在任务开始时，可选择调用指标监测API
func (s *Service) handleTaskStart(signal *TaskSignal, requestID string) {
    // 现有逻辑：创建任务会话、启动数据收集
    session := s.createTaskSession(signal)
    s.startDataCollection(session)
    
    // 可选：通过HTTP API调用启动指标监测（独立模块协作）
    // POST /api/v1/tasks/{taskId}/metrics-monitoring/manual-start
    // 或直接通过未来的消息队列/事件总线通知
}
```

#### 配置层面集成

**统一配置管理**：
```yaml
# config.yaml
metrics_monitoring:
  enabled: true
  baseline_duration: "30m"
  extended_duration: "1h"
  query_interval: "1m"
  supported_metrics: ["cpu", "memory", "traffic"]
```

#### 模块协作流程

两个独立模块通过直接函数调用进行协作，确保高效性和类型安全：
```
任务信号 → task_collaboration.ProcessTaskSignal → 创建任务会话 → 启动数据收集 → 告警关联
                                                          ↓ (依赖注入函数调用)
                                           s.metricsService.StartMetricsMonitoring()
                                                          ↓
                                           创建指标会话 → 实时监测(优先) → 变化检测
                                                          ↓        ↑
                                                     基线分析(异步)  查询实时数据
                                                          ↑        ↑
                                                    查询历史数据  持续监听
                                                          ↓        ↓
                                                        Prometheus（持续采集的数据）
```

#### 架构优势

1. **高效通信**：同进程内直接函数调用，避免HTTP网络开销
2. **类型安全**：编译时检查接口一致性，避免运行时错误
3. **故障隔离**：指标监测模块故障不影响任务协同核心功能
4. **可选集成**：通过依赖注入实现松耦合，可配置开关控制
5. **接口抽象**：通过接口定义避免循环依赖，保持模块独立性

## 6.2 与现有服务集成

### 6.2.1 Prometheus 查询集成

复用现有的 Prometheus 查询能力，确保架构一致性：

#### 查询接口复用
- **TrafficDAO接口**：复用 `internal/traffic/dao.go` 的查询方法
  - `QueryRate(ctx, query, time)` - 即时查询
  - `QueryVectorOverTime(ctx, query, start, end, step)` - 范围查询
- **查询客户端**：使用现有的 `prometheus/client_golang` 包
- **错误处理**：遵循现有的错误处理和日志记录模式

#### 指标名称一致性
复用现有的指标名称规范：
- CPU使用率：`dci_snmp_status_cpu_usage{device_id="", device_name="", entity_index=""}`
- 内存使用率：`dci_snmp_status_memory_usage{device_id="", device_name="", entity_index=""}`
- 流量指标：`dci_snmp_flow_ifHCInOctets{device_id="", ifName=""}`
- 接口状态：`dci_snmp_status_interface{device_id="", ifName=""}`

#### PromQL查询示例
```go
// 基线期数据查询（30分钟历史数据）
baselineQuery := fmt.Sprintf(`avg_over_time(dci_snmp_status_cpu_usage{device_id="%s"}[30m])`, deviceID)
baseline, err := s.trafficDAO.QueryRate(ctx, baselineQuery, time.Now())

// 实时数据查询
currentQuery := fmt.Sprintf(`dci_snmp_status_cpu_usage{device_id="%s"}`, deviceID)
current, err := s.trafficDAO.QueryRate(ctx, currentQuery, time.Now())

// 流量基线查询
flowBaselineQuery := fmt.Sprintf(`avg_over_time(dci_snmp_flow_ifHCInOctets{device_id="%s", ifName="%s"}[30m])`, deviceID, ifName)
flowBaseline, err := s.trafficDAO.QueryRate(ctx, flowBaselineQuery, time.Now())
```

### 6.2.2 配置管理集成

**配置加载**：
- 在模块初始化时从 `config.yaml` 加载时间参数配置
- 使用 Viper 配置管理库进行配置解析和类型转换
- 提供配置验证，确保时间参数的合理性（如基线期 ≥ 5分钟，延展期 ≤ 24小时）

### 6.2.3 告警系统集成

与现有告警系统联动：
- 任务期间的指标数据为告警分析提供数据支持
- 关联任务相关的告警事件与指标数据记录
- 集成到统一数据查询和分析流程

# 7 实施计划

## 7.1 开发阶段

### 7.1.1 第一阶段：基础架构搭建

1. 扩展任务协同模块，新增指标相关组件
2. 设计和创建指标数据记录相关数据库表  
3. 配置文件扩展，添加时间参数配置项
4. 实现基础的指标数据查询器

### 7.1.2 第二阶段：核心功能开发

1. 实现基线统计计算器和指标数据记录器
2. 集成 Prometheus 指标查询接口
3. 实现指标数据记录的生命周期管理

### 7.1.3 第三阶段：API 接口开发

1. 实现指标数据记录管理 API
2. 实现指标查询和数据呈现 API  
3. 完善 Swagger 文档和接口测试

### 7.1.4 第四阶段：系统集成

1. 与任务信号处理集成
2. 与告警系统联动集成
3. 性能优化和测试验证
