# DCI-prometheus类告警规则模块技术设计

## 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-07-9 | 顾铠羟 | 初始版本           |

## 1 文档介绍

### 1.1 文档目的

本文档旨在详细设计DCI监测系统中Prometheus类告警规则管理模块，包括告警规则的定义、存储、管理和与AlertManager的集成机制。该模块将支持告警规则的动态配置、版本管理和灵活的告警条件设置，为整个DCI监测系统提供标准化的异常检测能力。

### 1.2 文档范围

本设计覆盖Prometheus告警规则模块的架构设计、数据流向、接口设计和实现方案，包括：
- 告警规则的数据模型和存储方案
- 告警规则的管理接口与配置流程
- 与现有系统组件的集成方式
- 告警规则的生效机制与同步策略

### 1.3 文档关联

- 《06-dcimonitor服务端技术方案设计.md》：提供整体服务端架构
- 《22-DCI-交换机状态监测技术设计.md》：提供基于指标的告警设计基础
- 《24-01-DCI-prometheus指标类数据AlertManager技术方案设计.md》：提供AlertManager集成设计
- 《24-02-DCI-统一告警模型设计.md》：提供统一告警模型设计

## 2 总体设计

### 2.1 设计目标

1. 建立统一的Prometheus告警规则管理体系，支持规则的创建、修改、禁用和删除
2. 实现告警规则的动态更新与同步，确保配置变更能够实时生效
3. 提供规则验证机制，避免无效规则导致系统故障
4. 实现告警规则的版本管理与历史记录
5. 与现有监控系统和告警通知系统高效集成
6. 支持基于设备、指标和业务场景的分类管理

### 2.2 架构设计

#### 2.2.1 总体架构

```mermaid
graph TD
    Client["前端/客户端"] --> |HTTP| API["告警规则API"]
    API --> RuleService["告警规则服务"]
    RuleService --> RuleDAO["告警规则DAO"]
    RuleDAO --> MySQL[(MySQL)]
    
    RuleService --> |同步| PrometheusConfigSync["Prometheus配置同步器"]
    PrometheusConfigSync --> |更新规则| K8sConfigMaps["K8S ConfigMaps"]
    K8sConfigMaps --> |挂载| Prometheus["Prometheus"]
    Prometheus --> |触发告警| AlertManager["AlertManager"]
    AlertManager --> |通知| WebhookReceiver["告警接收器"]
    WebhookReceiver --> AlertService["告警服务"]
    AlertService --> AlertDAO["告警DAO"]
    AlertDAO --> MySQL
```

#### 2.2.2 模块组件关系

```mermaid
classDiagram
    class RuleAPI {
        +CreateRule()
        +UpdateRule()
        +DeleteRule()
        +EnableRule()
        +DisableRule()
        +GetRuleByID()
        +ListRules()
    }
    
    class RuleService {
        +CreateRule()
        +UpdateRule()
        +DeleteRule()
        +EnableRule()
        +DisableRule()
        +GetRuleByID()
        +ListRules()
        +SyncRulesToPrometheus()
    }
    
    class RuleDAO {
        +Create()
        +Update()
        +Delete()
        +GetByID()
        +List()
        +UpdateStatus()
    }
    
    class ConfigSyncService {
        +SyncRulesToConfigMap()
        +GenerateRuleFiles()
        +ApplyConfigMap()
        +ValidateRules()
    }
    
    RuleAPI --> RuleService
    RuleService --> RuleDAO
    RuleService --> ConfigSyncService
```

### 2.3 数据流向

告警规则从创建到生效的数据流向如下：

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as 告警规则API
    participant Service as 告警规则服务
    participant DAO as 告警规则DAO
    participant SyncService as 配置同步服务
    participant K8S as Kubernetes API
    participant Prometheus as Prometheus
    
    Client->>API: 创建/更新告警规则
    activate API
    API->>Service: 调用服务层方法
    activate Service
    Service->>Service: 验证规则有效性
    Service->>DAO: 保存规则到数据库
    activate DAO
    DAO-->>Service: 返回保存结果
    deactivate DAO
    
    Service->>SyncService: 触发规则同步
    activate SyncService
    SyncService->>DAO: 获取有效规则
    activate DAO
    DAO-->>SyncService: 返回所有有效规则
    deactivate DAO
    
    SyncService->>SyncService: 生成规则YAML配置
    SyncService->>K8S: 更新ConfigMap
    activate K8S
    K8S-->>SyncService: ConfigMap更新结果
    deactivate K8S
    
    SyncService-->>Service: 同步结果
    deactivate SyncService
    
    Service-->>API: 返回操作结果
    deactivate Service
    API-->>Client: 响应请求
    deactivate API
    
    Note over K8S,Prometheus: Prometheus自动重新加载规则
    K8S->>Prometheus: 配置变更通知
    Prometheus->>Prometheus: 重新加载告警规则
```

### 2.4 模块化设计

告警规则模块采用分层架构设计：

1. **API层**：提供REST API接口，处理HTTP请求
2. **服务层**：实现业务逻辑，规则验证和规则同步
3. **数据访问层**：处理数据库操作，提供CRUD功能
4. **配置同步层**：负责将规则同步到Kubernetes ConfigMap

TODO: 参考 documents/designs/templates/03-DCI项目API及代码规范指南.md 补充具体go文件名

```mermaid
graph TD
    subgraph "API层"
        RuleHandler["规则处理器"]
        RuleGroupHandler["规则组处理器"]
    end
    
    subgraph "服务层"
        RuleService["告警规则服务"]
        RuleValidator["规则验证器"]
        RuleSyncManager["规则同步管理器"]
    end
    
    subgraph "数据访问层"
        RuleDAO["规则DAO"]
        RuleHistoryDAO["规则历史DAO"]
    end
    
    subgraph "配置同步层"
        ConfigGenerator["配置生成器"]
        K8sClient["Kubernetes客户端"]
    end
    
    RuleHandler --> RuleService
    RuleGroupHandler --> RuleService
    RuleService --> RuleValidator
    RuleService --> RuleSyncManager
    RuleService --> RuleDAO
    RuleService --> RuleHistoryDAO
    RuleSyncManager --> ConfigGenerator
    RuleSyncManager --> K8sClient
```

### 2.5 技术选型

| 技术/组件     | 版本   | 用途                       | 选择理由                                   |
|--------------|--------|---------------------------|-------------------------------------------|
| Go           | 1.21+  | 开发语言                   | 高性能，与Prometheus生态系统兼容            |
| Gin          | 1.9.0  | Web框架                    | 轻量级、高性能、API友好                    |
| client-go    | 0.28.0 | Kubernetes API客户端       | 官方库，功能完善，支持ConfigMap操作         |
| yaml.v3      | 3.0.1  | YAML解析与生成             | 支持复杂YAML结构，适用于Prometheus规则配置  |
| zap          | 1.24.0 | 日志框架                   | 高性能、结构化日志，支持多输出目标         |
| promtool     | 2.45.0 | Prometheus规则验证工具      | 官方工具，确保规则语法正确                 |
| MySQL        | 8.0    | 规则存储数据库             | 可靠的关系型数据库，支持复杂查询和事务     |

TODO: 根据 documents/designs/templates/01-DCI-数据监测系统-项目目录及技术栈约束指南.md 修正技术栈

## 3 详细设计

### 3.1 功能模块

#### 3.1.1 告警规则API

告警规则API模块提供REST接口，用于管理Prometheus告警规则。

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as 告警规则API
    participant Service as 告警规则服务
    
    Client->>API: POST /api/v1/alert-rules
    activate API
    API->>Service: CreateRule(rule)
    activate Service
    Service-->>API: 返回创建结果
    deactivate Service
    API-->>Client: 响应创建结果
    deactivate API
    
    Client->>API: PUT /api/v1/alert-rules/{id}
    activate API
    API->>Service: UpdateRule(id, rule)
    activate Service
    Service-->>API: 返回更新结果
    deactivate Service
    API-->>Client: 响应更新结果
    deactivate API
    
    Client->>API: DELETE /api/v1/alert-rules/{id}
    activate API
    API->>Service: DeleteRule(id)
    activate Service
    Service-->>API: 返回删除结果
    deactivate Service
    API-->>Client: 响应删除结果
    deactivate API
    
    Client->>API: PATCH /api/v1/alert-rules/{id}/status
    activate API
    API->>Service: UpdateRuleStatus(id, status)
    activate Service
    Service-->>API: 返回状态更新结果
    deactivate Service
    API-->>Client: 响应状态更新结果
    deactivate API
    
    Client->>API: GET /api/v1/alert-rules
    activate API
    API->>Service: ListRules(filters)
    activate Service
    Service-->>API: 返回规则列表
    deactivate Service
    API-->>Client: 响应规则列表
    deactivate API
```

#### 3.1.2 告警规则服务

告警规则服务实现业务逻辑，包括规则的验证、保存和同步：

```mermaid
flowchart TD
    Start([开始]) --> ValidateRule[验证规则]
    ValidateRule --> IsValid{规则有效?}
    IsValid -- 否 --> Return([返回错误])
    IsValid -- 是 --> SaveRule[保存规则到数据库]
    SaveRule --> SaveOK{保存成功?}
    SaveOK -- 否 --> Return
    SaveOK -- 是 --> SyncRequired{需要同步?}
    SyncRequired -- 否 --> Return2([返回成功])
    SyncRequired -- 是 --> SyncRules[同步规则到Prometheus]
    SyncRules --> SyncOK{同步成功?}
    SyncOK -- 否 --> HandleSyncError[记录同步错误]
    SyncOK -- 是 --> Return2
    HandleSyncError --> Return2
```

#### 3.1.3 规则验证器

规则验证器负责验证告警规则的有效性，包括语法检查和语义验证：

1. **规则表达式验证**：检查PromQL表达式的语法
2. **标签验证**：检查标签格式和必需标签
3. **持续时间验证**：检查告警持续时间格式
4. **依赖指标存在性验证**：检查表达式中使用的指标是否存在

```mermaid
flowchart TD
    Start([开始验证]) --> ValidateSyntax[验证PromQL语法]
    ValidateSyntax --> SyntaxOK{语法正确?}
    SyntaxOK -- 否 --> ReturnSyntaxError([返回语法错误])
    SyntaxOK -- 是 --> ValidateLabels[验证标签格式]
    ValidateLabels --> LabelsOK{标签有效?}
    LabelsOK -- 否 --> ReturnLabelError([返回标签错误])
    LabelsOK -- 是 --> ValidateDuration[验证持续时间]
    ValidateDuration --> DurationOK{持续时间有效?}
    DurationOK -- 否 --> ReturnDurationError([返回持续时间错误])
    DurationOK -- 是 --> ValidateMetrics[验证指标存在性]
    ValidateMetrics --> MetricsOK{指标存在?}
    MetricsOK -- 否 --> ReturnMetricWarning([返回指标警告])
    MetricsOK -- 是 --> ReturnValid([验证通过])
    ReturnMetricWarning --> ReturnValid
```

#### 3.1.4 配置同步服务

配置同步服务负责将数据库中的规则同步到Kubernetes ConfigMap：

```mermaid
sequenceDiagram
    participant Service as 规则服务
    participant SyncService as 同步服务
    participant Generator as 配置生成器
    participant K8S as Kubernetes API
    participant Prometheus as Prometheus
    
    Service->>SyncService: SyncRules()
    activate SyncService
    SyncService->>SyncService: 获取有效规则
    SyncService->>Generator: 生成规则YAML
    activate Generator
    Generator-->>SyncService: 返回规则YAML
    deactivate Generator
    
    SyncService->>K8S: 获取当前ConfigMap
    activate K8S
    K8S-->>SyncService: 返回ConfigMap
    deactivate K8S
    
    SyncService->>SyncService: 比较变更
    
    alt 需要更新
        SyncService->>K8S: 更新ConfigMap
        activate K8S
        K8S-->>SyncService: 更新结果
        deactivate K8S
        
        Note over K8S,Prometheus: ConfigMap变更触发Prometheus重载
        K8S->>Prometheus: 配置变更通知
        Prometheus->>Prometheus: 重载规则配置
    end
    
    SyncService-->>Service: 返回同步结果
    deactivate SyncService
```

### 3.2 数据模型

#### 3.2.1 告警规则模型

告警规则存储在MySQL数据库中，主要包括以下表结构：

##### monitor_alert_rule表

```mermaid
classDiagram
    class MonitorAlertRule {
        +int id
        +string name
        +string description
        +string expr
        +string for
        +string labels
        +string annotations
        +string source
        +string level
        +int status
        +int group_id
        +string created_by
        +string updated_by
        +datetime created_at
        +datetime updated_at
    }
```

| 字段          | 类型         | 描述                                 |
|---------------|-------------|--------------------------------------|
| id            | int         | 主键                                 |
| name          | varchar(64) | 规则名称                             |
| description   | text        | 规则描述                             |
| expr          | text        | PromQL表达式                         |
| for           | varchar(32) | 持续时间                             |
| labels        | json        | 标签（JSON格式）                     |
| annotations   | json        | 注释（JSON格式）                     |
| source        | varchar(32) | 数据源（prometheus/elasticsearch）   |
| level         | varchar(16) | 告警级别（info/warning/critical）    |
| status        | tinyint     | 状态（0=禁用/1=启用）                |
| group_id      | int         | 规则组ID                             |
| created_by    | varchar(64) | 创建者                               |
| updated_by    | varchar(64) | 更新者                               |
| created_at    | datetime    | 创建时间                             |
| updated_at    | datetime    | 更新时间                             |

##### monitor_alert_rule_group表

```mermaid
classDiagram
    class MonitorAlertRuleGroup {
        +int id
        +string name
        +string description
        +int interval
        +string source
        +int status
        +datetime created_at
        +datetime updated_at
    }
```

| 字段          | 类型         | 描述                               |
|---------------|-------------|------------------------------------|
| id            | int         | 主键                               |
| name          | varchar(64) | 规则组名称                         |
| description   | text        | 规则组描述                         |
| interval      | int         | 评估间隔（秒）                     |
| source        | varchar(32) | 数据源（prometheus/elasticsearch） |
| status        | tinyint     | 状态（0=禁用/1=启用）              |
| created_at    | datetime    | 创建时间                           |
| updated_at    | datetime    | 更新时间                           |

##### monitor_alert_rule_history表

```mermaid
classDiagram
    class MonitorAlertRuleHistory {
        +int id
        +int rule_id
        +int version
        +text content
        +string change_type
        +string changed_by
        +datetime changed_at
    }
```

| 字段          | 类型         | 描述                                      |
|---------------|-------------|-------------------------------------------|
| id            | int         | 主键                                      |
| rule_id       | int         | 规则ID                                    |
| version       | int         | 版本号                                    |
| content       | json        | 规则完整内容（JSON格式）                  |
| change_type   | varchar(16) | 变更类型（create/update/disable/enable）  |
| changed_by    | varchar(64) | 变更者                                    |
| changed_at    | datetime    | 变更时间                                  |

#### 3.2.2 规则同步状态模型

```mermaid
classDiagram
    class MonitorAlertRuleSync {
        +int id
        +string config_name
        +string namespace
        +datetime last_sync_time
        +int last_sync_status
        +string error_message
        +text rule_hashes
        +datetime created_at
        +datetime updated_at
    }
```

| 字段             | 类型         | 描述                            |
|------------------|-------------|--------------------------------|
| id               | int         | 主键                            |
| config_name      | varchar(64) | ConfigMap名称                   |
| namespace        | varchar(64) | Kubernetes命名空间              |
| last_sync_time   | datetime    | 最后同步时间                    |
| last_sync_status | tinyint     | 同步状态（0=失败/1=成功）       |
| error_message    | text        | 错误信息                        |
| rule_hashes      | text        | 规则哈希列表（用于检测变更）    |
| created_at       | datetime    | 创建时间                        |
| updated_at       | datetime    | 更新时间                        |

### 3.3 接口设计

#### 3.3.1 REST API设计

TODO: 具体见 documents/designs/24-04-DCI-prometheus类告警模块API设计.md

#### 3.3.2 配置同步接口设计

配置同步服务提供以下内部接口：

```go
// ConfigSyncService 配置同步服务接口
type ConfigSyncService interface {
    // SyncRules 同步规则到Prometheus
    SyncRules(ctx context.Context) error
    
    // ValidateRule 验证规则表达式
    ValidateRule(ctx context.Context, rule *AlertRule) (*ValidationResult, error)
    
    // GetSyncStatus 获取同步状态
    GetSyncStatus(ctx context.Context) (*SyncStatus, error)
    
    // ForceSync 强制同步规则
    ForceSync(ctx context.Context) error
}

// SyncStatus 同步状态
type SyncStatus struct {
    LastSyncTime   time.Time
    LastSyncStatus string
    ErrorMessage   string
    RuleCount      int
    RuleGroupCount int
}

// ValidationResult 验证结果
type ValidationResult struct {
    Valid    bool
    Errors   []string
    Warnings []string
}
```

### 3.4 规则同步机制

#### 3.4.1 规则文件结构

Prometheus告警规则使用YAML格式定义。规则文件示例：

```yaml
groups:
- name: dci_device_status_alerts
  interval: 1m
  rules:
  - alert: DeviceCPUHighUsage
    expr: dci_snmp_device_cpu_usage{entPhysicalClass="9"} > 80
    for: 5m
    labels:
      severity: warning
      category: device_status
    annotations:
      summary: "交换机 {{ $labels.device_name }} CPU使用率过高"
      description: "设备 {{ $labels.device_name }} ({{ $labels.device_ip }}) 的CPU使用率为 {{ $value }}%，持续超过5分钟。"
```

#### 3.4.2 ConfigMap更新流程

```mermaid
flowchart TD
    Start([开始]) --> LoadRules[从数据库加载有效规则]
    LoadRules --> GroupRules[按规则组分组]
    GroupRules --> GenYAML[生成YAML配置]
    GenYAML --> GetCM[获取当前ConfigMap]
    GetCM --> Compare{配置是否变更?}
    Compare -- 否 --> NoChange[无需更新]
    Compare -- 是 --> UpdateCM[更新ConfigMap]
    UpdateCM --> Success{更新成功?}
    Success -- 是 --> UpdateStatus[更新同步状态]
    Success -- 否 --> HandleError[记录错误信息]
    NoChange --> End([结束])
    UpdateStatus --> End
    HandleError --> End
```

#### 3.4.3 规则变更检测

使用规则哈希来检测变更，避免不必要的ConfigMap更新：

1. 对每个规则计算内容哈希
2. 存储哈希值到`monitor_alert_rule_sync`表
3. 同步时比较当前哈希与存储的哈希

### 3.5 Prometheus重载机制

Prometheus支持通过HTTP接口重载配置，无需重启服务。在DCI系统中，利用Kubernetes的ConfigMap挂载机制自动重载规则：

1. 当ConfigMap更新时，Kubernetes会更新对应的挂载文件
2. Prometheus配置了`--web.enable-lifecycle`选项，支持配置重载
3. 系统会在ConfigMap更新后调用Prometheus的`/-/reload`接口

如果调用重载接口失败，系统会进行以下处理：
1. 记录错误日志
2. 更新同步状态表中的错误信息
3. 触发告警通知管理员
4. 在下一个同步周期重试

```mermaid
sequenceDiagram
    participant SyncService as 同步服务
    participant K8S as Kubernetes
    participant Prometheus as Prometheus
    
    SyncService->>K8S: 更新ConfigMap
    activate K8S
    K8S-->>SyncService: 更新成功
    deactivate K8S
    
    Note over K8S,Prometheus: Kubernetes自动更新挂载文件
    K8S->>Prometheus: 更新配置文件
    
    alt 手动触发重载
        SyncService->>Prometheus: POST /-/reload
        activate Prometheus
        Prometheus->>Prometheus: 重载配置
        Prometheus-->>SyncService: 重载结果
        deactivate Prometheus
    else 自动重载
        Prometheus->>Prometheus: 检测到配置文件变更
        Prometheus->>Prometheus: 自动重载配置
    end
```

## 4 安全设计

### 4.1 访问控制

告警规则管理API通过以下机制实现安全控制：

1. **认证**：使用JWT令牌认证用户身份
2. **授权**：基于RBAC模型控制权限
3. **审计**：记录所有规则变更操作
4. **数据验证**：验证所有输入，防止注入攻击

### 4.2 敏感信息处理

1. **标签过滤**：过滤告警标签中的敏感信息
2. **Prometheus认证**：通过Kubernetes Secrets管理Prometheus访问凭据
3. **规则验证**：验证规则表达式，避免恶意注入

### 4.3 变更控制

1. **版本控制**：记录所有规则版本
2. **变更审计**：记录谁在什么时间做了什么变更
3. **回滚机制**：支持规则回滚到之前版本

## 5 本设计的代码实现文件列表

TODO: 与当前代码结构融合，依据 documents/designs/templates/03-DCI项目API及代码规范指南.md 补充具体go文件名

```
dci-monitor/src/dcimonitor/
├── internal/
│   ├── alert/
│   │   ├── rule/
│   │   │   ├── api.go                  # 告警规则API处理
│   │   │   ├── service.go              # 告警规则服务实现
│   │   │   ├── dao.go                  # 告警规则数据访问
│   │   │   ├── model.go                # 告警规则模型定义
│   │   │   ├── validator.go            # 规则验证器
│   │   │   └── sync.go                 # 配置同步服务
│   │   └── common/
│   │       ├── types.go                # 公共类型定义
│   │       └── constants.go            # 常量定义
│   ├── k8s/
│   │   ├── client.go                   # Kubernetes客户端
│   │   ├── configmap.go                # ConfigMap操作
│   │   └── prometheus.go               # Prometheus交互
│   └── prometheus/
│       ├── generator.go                # 规则配置生成器
│       └── validator.go                # Prometheus规则验证
```

### 5.1 部署文件

TODO: 与现有 融合 。alert-rule-service是一个独立的二进制吗？

```
dci-monitor/kubernetes/
├── alert-rule-service/
│   ├── deployment.yaml                 # 部署配置
│   ├── service.yaml                    # 服务配置
│   └── rbac.yaml                       # RBAC权限配置
```

## 6 配置示例

TODO: 怎么在配置文件中体现规则ID？且与告警关联？

### 6.1 Prometheus告警规则示例

以下是Prometheus告警规则配置示例，定义了设备状态监控相关的告警规则：

```yaml
groups:
- name: dci_device_status_alerts
  interval: 1m
  rules:
  - alert: DeviceCPUHighUsage
    expr: dci_snmp_device_cpu_usage{entPhysicalClass="9"} > 80
    for: 5m
    labels:
      severity: warning
      category: device_status
    annotations:
      summary: "交换机 {{ $labels.device_name }} CPU使用率过高"
      description: "设备 {{ $labels.device_name }} ({{ $labels.device_ip }}) 的CPU使用率为 {{ $value }}%，持续超过5分钟。"

  - alert: DeviceMemoryHighUsage
    expr: dci_snmp_device_memory_usage{entPhysicalClass="9"} > 85
    for: 10m
    labels:
      severity: critical
      category: device_status
    annotations:
      summary: "交换机 {{ $labels.device_name }} 内存使用率过高"
      description: "
      description: "设备 {{ $labels.device_name }} ({{ $labels.device_ip }}) 的内存使用率为 {{ $value }}%，持续超过10分钟。"

  - alert: InterfaceStatusMismatch
    expr: dci_snmp_interface_admin_status == 1 and dci_snmp_interface_oper_status == 2
    for: 2m
    labels:
      severity: warning
      category: interface_status
    annotations:
      summary: "交换机 {{ $labels.device_name }} 接口 {{ $labels.port_name }} 状态异常"
      description: "设备 {{ $labels.device_name }} ({{ $labels.device_ip }}) 的接口 {{ $labels.port_name }} 管理状态为UP但实际状态为DOWN，可能存在物理链路问题。"

- name: dci_traffic_alerts
  interval: 2m
  rules:
  - alert: InterfaceHighTrafficRate
    expr: rate(dci_snmp_flow_ifHCInOctets[5m]) * 8 / 1000000 > 800
    for: 5m
    labels:
      severity: warning
      category: traffic
    annotations:
      summary: "交换机 {{ $labels.device_name }} 接口 {{ $labels.port_name }} 入向流量过高"
      description: "设备 {{ $labels.device_name }} 的接口 {{ $labels.port_name }} 入向流量达到 {{ $value | humanize }}Mbps，超过预警阈值。"
```

### 6.2 AlertManager接收配置示例

以下是AlertManager接收告警的配置示例，定义了告警通知路由和接收方式：

```yaml
route:
  group_by: ['alertname', 'category']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 4h
  receiver: 'dci-webhook-receiver'
  routes:
  - match:
      severity: critical
    receiver: 'dci-webhook-critical'
    continue: true
  - match:
      severity: warning
    receiver: 'dci-webhook-warning'
    continue: true

receivers:
- name: 'dci-webhook-receiver'
  webhook_configs:
  - url: 'http://dcimonitor-service:8080/api/v1/alerts/webhook'
    send_resolved: true

- name: 'dci-webhook-critical'
  webhook_configs:
  - url: 'http://dcimonitor-service:8080/api/v1/alerts/webhook/critical'
    send_resolved: true

- name: 'dci-webhook-warning'
  webhook_configs:
  - url: 'http://dcimonitor-service:8080/api/v1/alerts/webhook/warning'
    send_resolved: true
```

## 7 实施计划

1. **阶段一：数据库设计**
   - 设计告警规则表结构
   - 设计规则组表结构
   - 设计历史记录表结构
   - 创建数据库迁移脚本

2. **阶段二：核心服务开发**
   - 开发告警规则DAO层
   - 开发规则验证器
   - 开发规则服务层
   - 开发配置同步服务

3. **阶段三：API开发**
   - 开发规则管理REST API
   - 开发规则组管理API
   - 开发同步状态API
   - 编写API文档

4. **阶段四：Kubernetes集成**
   - 开发ConfigMap操作模块
   - 开发Prometheus重载模块
   - 测试配置同步机制

## 8 总结

本文档详细设计了DCI监测系统中的Prometheus类告警规则管理模块，包括架构设计、数据流、数据模型、接口设计和配置同步机制。该模块通过统一的规则管理平台，支持规则的动态配置和同步，使告警规则的管理更加灵活和高效。

规则管理模块与现有的监控和告警系统紧密集成，支持多种告警场景和业务需求。通过基于MySQL的规则存储和基于Kubernetes ConfigMap的配置同步，确保规则的高效管理和实时生效。

未来可进一步增强该模块的功能，如支持更复杂的规则模板、规则依赖管理、告警静默期配置等，为DCI监测系统提供更加全面和智能的告警能力。