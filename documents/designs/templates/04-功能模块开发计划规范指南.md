---
title: |
  DCI项目功能模块开发计划规范指南

subtitle: |
  新功能模块开发计划制定的标准化指导
---

# 1. 规范目的

本指南规范化功能模块开发计划的制定流程，确保所有新功能开发都遵循统一的架构标准、技术约束和质量要求，提高开发效率和代码质量。

# 2. 开发计划文档结构

## 2.1 必备章节结构

每个功能模块开发计划**必须**包含以下章节：

```
1. 概述
   1.1 开发目标
   1.2 依据技术设计文档
   1.3 主要专有名词定义说明
   1.4 技术约束
   1.5 现有代码分析

2. 模块开发计划
   2.1 新增/变更模块结构
   2.2 API接口开发
   2.3 数据库扩展
   2.4 服务集成开发

3. 开发实施步骤
   3.1 第一阶段：基础架构搭建
   3.2 第二阶段：核心功能实现
   3.3 第三阶段：API接口开发
   3.4 第四阶段：服务集成
   3.5 第五阶段：测试与优化

4. 质量保证
5. 配置管理
6. 依赖关系
7. 总结
```

## 2.2 章节内容要求

### 2.2.1 概述章节
- **开发目标**：明确功能目标和预期效果
- **依据技术设计文档**：阐明与本设计文档相关联的文档及作用，必须基于已有的架构和实现，不是从零开始
- **主要专有名词定义说明**：定义模块中主要专有名词概念
- **技术约束**：列明架构规范、技术栈约束、代码规范
- **现有代码分析**：分析已实现功能和功能缺口

### 2.2.2 模块开发计划章节
- **新增模块结构**：定义模块目录结构和核心组件
- **API接口开发**：定义REST API规范和Swagger文档要求
- **数据库扩展**：设计新增表结构和数据模型
- **服务集成开发**：规划与现有模块的集成方案

# 3. 技术约束分析规范

## 3.1 约束分类

开发计划**必须**明确以下三类技术约束：

### 3.1.1 架构规范约束
```
- 必须遵循Handler → Service → DAO架构模式
- 必须按功能模块进行代码组织
- 严禁跨层依赖和反向依赖
- 每个模块包含：handler.go、service.go、dao.go、model.go
```

### 3.1.2 技术栈约束
```
- 语言：Go 1.24.2
- Web框架：Gin v1.9.1+
- API设计：RESTful风格，使用Swagger
- 数据库：MySQL 8.0+，使用go-sql-driver/mysql
- 消息队列：Apache Kafka 3.x+，使用IBM/sarama
- 配置管理：Viper v1.18.2+
- 日志：Zap v1.27.0+
- 时区处理：统一使用Asia/Shanghai时区
```

### 3.1.3 代码规范约束
```
- API设计：遵循RESTful风格，使用标准响应格式
- 文档化：所有API必须使用Swagger在代码中完整注释
- 错误处理：严禁忽略error返回值
- 请求跟踪：必须为每个请求生成唯一请求ID
```

## 3.2 现有代码分析方法

### 3.2.1 分析维度
1. **已实现功能清单**：列出可复用的现有模块和功能
2. **功能缺口识别**：明确需要新开发或必须修改的功能点
3. **集成点分析**：识别需要与现有模块集成的接口

### 3.2.2 分析输出
- 可复用组件列表
- 需要扩展的现有模块
- 完全新增的功能模块
- 数据库表的新增和扩展需求

# 4. 模块化开发标准

## 4.1 模块目录结构规范

**代码文件标准结构**：
```
internal/[module_name]/
├── model.go          # 数据模型定义
├── dao.go            # 数据访问层
├── service.go        # 业务逻辑层
├── handler.go        # HTTP请求处理层
├── [specific].go     # 特定功能组件（如需要）
├── [externs].go      # 扩展组件（按需添加）
└── README.md         # 模块说明文档
```

**数据库文件标准结构**：
```
sql/[module_name]/
├── [module_name].sql # 模块包含的表结构
└── README.md         # 数据库说明文档
```

## 4.2 数据模型设计规范

### 4.2.1 模型定义要求
```go
// 必须包含的元素
type ModelName struct {
    ID        string    `json:"id" db:"id" binding:"required"`
    CreatedAt time.Time `json:"created_at" db:"created_at"`
    UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
    // 业务字段...
}

// 必须实现TableName方法
func (ModelName) TableName() string {
    return "table_name"
}
```

### 4.2.2 请求响应模型
遵循 documents/designs/templates/03-DCI项目API及代码规范指南.md 相关规范

# 5. API设计规范
遵循 documents/designs/templates/03-DCI项目API及代码规范指南.md 相关规范

## 5.2 Swagger文档规范

### 5.2.1 必备注释
```go
// @Summary API功能概要（一句话）
// @Description API详细描述
// @Tags API分类标签
// @Accept json
// @Produce json
// @Param paramName paramType paramDataType isRequired "参数描述" example
// @Success 200 {object} ResponseType "成功响应描述"
// @Failure 400 {object} ErrorResponse "错误响应描述"
// @Router /api/v1/path [method]
```

- `@Param`注释**必须**包含参数名称、位置(`path`/`query`/`body`)、类型、是否必需、描述和示例。
- `@Success`和`@Failure`的`example`中**必须**提供完整且真实的JSON示例。

### 5.2.2 响应格式规范
遵循 documents/designs/templates/03-DCI项目API及代码规范指南.md 相关规范

# 6. 数据库设计规范

## 6.1 表设计标准

### 6.1.1 命名规范
```sql
-- 表名：模块前缀 + 功能描述（小写+下划线）
monitor_[module]_[entity]

-- 字段名：小写+下划线
device_id, created_at, start_time

-- 索引名：idx_ + 字段组合
INDEX idx_device_id (device_id)
INDEX idx_status_time (status, created_at)
```

### 6.1.2 必备字段
```sql
-- 每个业务表必须包含
id VARCHAR(64) PRIMARY KEY COMMENT '唯一标识符',
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(Asia/Shanghai)',

-- 可更新的表还需包含
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间(Asia/Shanghai)',
```

### 6.1.3 关联表设计
```sql
-- 关联表命名：主表_关联表_associations
CREATE TABLE monitor_[main]_[related]_associations (
    id VARCHAR(64) PRIMARY KEY COMMENT '关联记录唯一标识符',
    [main]_id VARCHAR(64) NOT NULL COMMENT '主表ID',
    [related]_id VARCHAR(64) NOT NULL COMMENT '关联表ID',
    association_type ENUM('type1', 'type2') COMMENT '关联类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(Asia/Shanghai)',
    
    INDEX idx_[main]_id ([main]_id),
    INDEX idx_[related]_id ([related]_id),
    FOREIGN KEY ([main]_id) REFERENCES [main_table](id) ON DELETE CASCADE,
    FOREIGN KEY ([related]_id) REFERENCES [related_table](id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关联表说明';
```

# 7. 开发实施步骤模板

## 7.1 五阶段开发流程

### 7.1.1 第一阶段：基础架构搭建
1. 创建代码模块目录结构
2. 定义基础数据模型
3. 实现基础DAO层
4. 创建基础Service层
5. 创建数据库扩展脚本

### 7.1.2 第二阶段：核心功能实现
1. 实现特定功能组件
2. 实现业务逻辑处理
3. 实现数据验证和转换
4. 集成外部服务调用
5. 实现错误处理和日志记录

### 7.1.3 第三阶段：API接口开发
1. 实现Handler层
2. 实现所有REST API接口
3. 添加完整的Swagger注释
4. 实现请求验证和响应封装
5. 注册路由和中间件

### 7.1.4 第四阶段：服务集成
1. 与现有模块集成
2. 配置消息队列集成
3. 集成外部服务接口
4. 实现配置管理
5. 测试集成功能

## 7.2 阶段交付物检查

### 7.2.1 每阶段必须交付
- 可编译的代码
- 更新的配置文件
- 阶段性文档

### 7.2.2 最终交付检查清单
- [ ] 代码通过gofmt格式化
- [ ] 所有API具有完整Swagger文档
- [ ] 数据库脚本可执行
- [ ] 配置文件已更新
- [ ] README文档完整

# 8. 质量保证要求

## 8.1 代码质量标准
- **格式化**：使用gofmt或goimports
- **错误处理**：严禁忽略error返回值
- **注释**：关键函数和复杂逻辑必须注释
- **命名**：遵循Go语言命名约定

## 8.2 API质量标准
- **RESTful**：严格遵循REST设计原则
- **状态码**：正确使用HTTP状态码
- **文档**：完整的Swagger注释
- **验证**：严格的输入参数验证

## 8.3 数据质量标准
- **事务**：涉及多表操作必须使用事务
- **索引**：查询字段必须建立索引
- **约束**：合理使用外键和唯一约束
- **备注**：所有表和字段必须有中文注释

# 9. 配置管理规范

## 9.1 配置文件扩展
新功能的配置**必须**添加到`config/config.yaml`：

## 9.2 环境变量支持
**必须**支持环境变量覆盖：
```
DCI_[MODULE]_[SETTING]=[VALUE]
```

# 10. 依赖管理规范

## 10.1 内部依赖声明
明确列出所有内部模块依赖：
```
- internal/[module] - 功能描述
- internal/models - 通用数据模型
- internal/middleware - 请求处理中间件
- internal/utils/timeutil - 时间处理工具
```

## 10.2 外部依赖管理
- 严格使用技术栈约束中指定的依赖
- 新增依赖必须在go.mod中明确版本
- 避免引入不必要的第三方库

# 11. 模板使用指导

## 11.1 使用流程
1. 复制本规范作为开发计划模板
2. 根据具体功能需求填充各章节内容
3. 详细分析技术约束和现有代码
4. 设计具体的模块结构和API接口
5. 制定详细的实施步骤，**不必包含时间安排**
