# DCI项目API设计与Go编码规范指南

## 1. 文档概述

本指南为DCI项目的API开发与Go代码实现的**强制性**规范，为所有技术方案设计和代码开发提供统一标准。所有参与本项目的开发人员**必须**严格遵循此文档中定义的全部约束，以确保代码的一致性、可维护性和系统的稳定性。

**文档适用范围**：DCI项目所有微服务的设计、开发与维护。

---

# 第一部分：API 设计规范

本部分定义了所有对外暴露的HTTP API接口必须遵循的设计标准，确保API的规范性、一致性和易用性。

## 2. API 设计核心原则

### 2.1 RESTful 设计规范

- **必须**使用标准HTTP方法表达语义：
  - `GET`：获取资源
  - `POST`：创建资源
  - `PUT`：更新资源（全量更新）
  - `PATCH`：部分更新资源
  - `DELETE`：删除资源

- URI路径设计**必须**遵循以下规则：
  - 使用名词复数形式表示资源集合，如`/api/v1/alerts`、`/api/v1/devices`。
  - 使用资源ID标识特定资源，如`/api/v1/alerts/{id}`。
  - 使用子资源表示从属关系，如`/api/v1/devices/{id}/interfaces`。
  - **仅在**非资源操作时使用动词，如`/api/v1/alerts/{id}/acknowledge`。

- 查询参数**必须**规范使用：
  - 过滤：`?status=active&level=critical`
  - 分页：`?page=1&page_size=20`
  - 排序：`?sort=created_at:desc`

### 2.2 API 版本控制

- **必须**在URL中明确标明API版本，格式为 `/api/v{n}`。当前为：`/api/v1/resource`。

### 2.3 命名规范

- API路径**必须**使用小写字母和连字符，如`/api/v1/alert-rules`。
- 查询参数**必须**使用小写字母和下划线（snake_case），如`device_id`, `start_time`。
- JSON字段**必须**使用小驼峰命名法（lowerCamelCase），如`deviceId`, `startTime`。
- **必须**保持命名一致性，同一概念在不同API中应使用完全相同的命名。

## 3. API 请求与响应规范

### 3.1 请求格式

- 请求内容类型（Content-Type）**必须**统一使用`application/json`。
- 请求体**必须**使用结构化的JSON格式。
- 所有请求结构体**必须**在`internal/models`包中统一定义，**严禁**在`handler`中使用内联匿名结构体。

### 3.2 响应格式

- **所有**API响应**必须**使用标准结构，**严禁**使用其他格式：

```json
{
  "code": 200,
  "data": {
    // 响应数据或错误详情
  }
}
```

- 成功响应**必须**使用对应的HTTP状态码（200、201等）。
- 所有响应结构体**必须**在`internal/models`包中定义，并使用统一的`Response`结构进行包装。

### 3.3 分页响应规范

- 分页API的响应**必须**返回标准的分页信息结构：

```json
{
  "code": 200,
  "data": {
    "items": [...], // 当前页数据列表
    "total": 100,      // 总记录数
    "page": 1,         // 当前页码
    "pageSize": 20,    // 每页数量
    "pageCount": 5     // 总页数
  }
}
```

- 分页参数默认值：`page=1`, `page_size=20`。
- 最大页面大小限制：`max_page_size=100`，服务器端**必须**强制校验。

## 4. API 错误处理规范

### 4.1 错误响应格式

- 错误响应**必须**遵循统一格式，`data`字段中包含`error`和`request_id`：
```json
{
  "code": 400,
  "data": {
    "error": "错误详细描述",
    "request_id": "123e4567-e89b-12d3-a456-426614174000"
  }
}
```

### 4.2 HTTP 状态码使用规范

- **必须**严格按照以下规范使用HTTP状态码：
  - `200 OK`：请求成功。
  - `201 Created`：资源创建成功。
  - `204 No Content`: 操作成功，但无需返回内容（如DELETE）。
  - `400 Bad Request`：客户端请求错误（参数无效、格式错误等）。
  - `401 Unauthorized`：未认证。
  - `403 Forbidden`：已认证但权限不足。
  - `404 Not Found`：请求的资源不存在。
  - `409 Conflict`：资源冲突（如重复创建）。
  - `422 Unprocessable Entity`：请求格式正确但服务器无法处理其语义。
  - `500 Internal Server Error`：服务器内部未知错误。
  - `503 Service Unavailable`：服务暂时不可用（如维护或过载）。

## 5. API 安全规范

### 5.1 数据验证

- **严禁**在未经验证的情况下使用任何用户输入。
- **必须**使用Gin的`binding`标签为所有请求结构体的字段定义严格的验证规则。
- **必须**针对不同类型的字段使用合适的验证规则，例如：
  - 字符串长度：`binding:"required,min=1,max=100"`
  - 数值范围：`binding:"min=1,max=1000"`
  - 格式验证：`binding:"ip"`, `binding:"email"`, `binding:"uuid"`
  - 枚举值：`binding:"oneof=active inactive maintenance"`

### 5.2 安全响应

- **严禁**在响应的错误消息中暴露任何系统内部敏感信息，如：代码堆栈、数据库错误详情、服务器路径等。

## 6. API 文档规范 (Swagger)

### 6.1 文档完整性

- **所有**对外暴露的API**必须**使用Swagger注释进行完整、准确的文档化。
- 接口的Swagger文档是**功能交付的必要组成部分**，未提供或不完善的文档将被视为功能未完成。

### 6.2 Swagger 注释规范

- API处理函数**必须**包含以下所有注释：
  - `@Summary`：API功能概要（一句话）
  - `@Description`：API详细描述
  - `@Tags`：API分类标签
  - `@Accept json`
  - `@Produce json`
  - `@Param`：所有请求参数的详细定义
  - `@Success`：成功响应的详细定义及示例
  - `@Failure`：所有可能发生的错误响应的详细定义及示例
  - `@Router`：API路由路径和HTTP方法

- `@Param`注释**必须**包含参数名称、位置(`path`/`query`/`body`)、类型、是否必需、描述和示例。
- `@Success`和`@Failure`的`example`中**必须**提供完整且真实的JSON示例。

---

# 第二部分：Go 编码与架构规范

本部分定义了所有Go后端代码必须遵循的架构、组织、编码和工具使用标准。

## 7. 架构与代码组织

### 7.1 文件与包结构

为实现高内聚、低耦合的架构，项目**必须**遵循按功能领域（Domain）进行模块化组织。所有核心业务代码**必须**放在`internal`目录下，并按功能模块划分。

```
dci-monitor/
├── ELK/                      # ELK Stack 部署与配置文件
├── kafka/                    # Kafka 部署与管理脚本
├── prometheus/               # Prometheus & Alertmanager 部署配置
├── sqls/                     # 数据库初始化与迁移脚本
│   ├── README.md
│   ├── alert_tables.sql
│   ├── create_constraints.sql
│   ├── init_database.sql
│   └── topology_lldp_tables.sql
│
└── src/                      # 所有Go源代码
    ├── common/               # 跨服务共享的通用库
    │   └── logger/           # 标准化日志库 (logger.go, adapter.go)
    │
    ├── dciagent/             # DCI Agent (部署在被监控网络)
    │   ├── cmd/              # Agent CLI 命令 (root.go, telegraf.go)
    │   └── internal/         # Agent 核心逻辑 (Telegraf控制)
    │
    ├── dcimonitor/           # 核心后台服务 (API Server)
    │   ├── cmd/              # 服务启动与CLI命令
    │   │   ├── root.go
    │   │   └── server.go
    │   ├── config/           # 服务配置文件目录 (config.yaml)
    │   ├── docs/             # 自动生成的Swagger API文档 (docs.go, swagger.json)
    │   ├── internal/         # **核心业务逻辑 (严禁外部项目导入)**
    │   │   ├── alert/        # 告警模块 (handler.go, service.go, dao.go, model.go)
    │   │   ├── device/       # 设备模块 (handler.go, service.go, dao.go, model.go)
    │   │   ├── dbsync/       # 数据库同步模块 (service.go, scheduler.go, mapper.go)
    │   │   ├── kafka/        # Kafka客户端封装 (client.go, producer.go, consumer.go)
    │   │   ├── middleware/   # Gin 中间件 (gin_logger.go, request_id.go)
    │   │   ├── models/       # 跨模块通用模型 (request.go, response.go, error.go)
    │   │   ├── topology/     # 网络拓扑模块 (handler.go, service.go, dao.go)
    │   │   └── utils/        # 通用工具 (timeutil/timeutil.go)
    │   └── main.go           # 程序主入口
    │
    └── dcimonitor-snmpstatus/ # SNMP数据处理微服务
        ├── internal/         # 服务内部逻辑
        │   └── snmp_processor/ # SNMP 数据处理核心
        │       ├── processor.go# 数据处理主逻辑
        │       ├── handler.go  # Kafka消息处理器
        │       ├── mapper.go   # 设备/端口映射 (DB <-> SNMP)
        │       ├── metrics.go  # Prometheus 指标导出
        │       └── models.go   # 数据模型
        └── main.go           # 服务主入口 (Kafka Consumer)
```

### 7.2 功能模块职责分工

每个功能模块包含以下核心文件，各司其职：

- **`handler.go`**: 
  - **唯一职责**：处理HTTP请求与响应，是API的入口。
  - **依赖**：仅能依赖`Service`层。
  - **核心功能**：解析与验证请求参数、调用`Service`、封装`Response`、处理HTTP层面的错误。**严禁**包含任何业务逻辑。

- **`service.go`**: 
  - **唯一职责**：实现与领域相关的核心业务逻辑。
  - **依赖**：可依赖`DAO`层及其他`Service`。
  - **核心功能**：执行业务规则、编排多个`DAO`操作、管理事务、转换数据模型。

- **`dao.go`** (Data Access Object): 
  - **唯一职责**：数据访问层，封装对单一数据源（数据库、外部API等）的交互。
  - **核心功能**：执行SQL查询、调用Prometheus API、缓存操作等。**严禁**包含任何业务逻辑。

- **`model.go`**: 
  - **唯一职责**：定义当前模块的领域数据模型（结构体）。
  - **核心功能**：表示业务实体，定义数据结构。

对于功能复杂的模块（如`alert`模块），可以将不同数据源的DAO或业务逻辑拆分到不同文件中，但**必须**遵循统一的命名和职责划分。例如：
- 文件名**必须**采用 `[模块名]_[职责].go` 或 `[数据源]_[职责].go` 的格式。
- `alert`模块的DAO层可以拆分为`alert_mysql_dao.go`和`alert_prometheus_dao.go`。
- 所有拆分后的文件仍需严格遵守`Handler -> Service -> DAO`的单向依赖原则。

### 7.3 依赖流向

**必须**严格保持单向依赖流：`Handler` → `Service` → `DAO`。**严禁**出现反向依赖（如`Service`依赖`Handler`）或跨层依赖（如`Handler`直接依赖`DAO`）。此架构是保证代码可测试性、可维护性的基石。

### 7.4 通用代码组织

- **`internal/models/`**: 存放**跨模块共享**的通用数据结构，如标准`Request`/`Response`体。
- **`internal/middleware/`**: 存放Gin中间件，如请求日志、认证、Panic恢复等。
- **`common/`**: 存放可被多个系统及服务（如`dcimonitor`和`dcimonitor-snmpstatus`）共享的通用代码。

### 7.5 代码复用

- 公共功能**必须**抽取为独立函数、通用`Service`或中间件。
- **严禁**在多个`Handler`或`Service`中编写功能重复的代码。

## 8. 编码实践规范

### 8.1 错误处理

- **必须**处理所有可能返回`error`的函数调用。
- **严禁**使用 `_` 丢弃 `error`，除非明确知道不会有错误发生且有注释说明。
- 在`DAO`层和`Service`层，**应当**使用`fmt.Errorf`或`errors.Wrap`为错误添加上下文信息，便于追踪。

### 8.2 日志记录

- 客户端错误（4xx）**必须**使用`WARN`级别日志记录。
- 服务器错误（5xx）**必须**使用`ERROR`级别日志记录，并附带堆栈信息。
- 日志内容**必须**包含请求ID、错误详情和关键上下文信息。

### 8.3 请求跟踪

- **必须**为每个请求生成唯一的请求ID。
- **所有**日志中**必须**包含该请求ID，以便于全链路问题排查。
- 错误响应中**必须**包含请求ID。

请求ID通过`RequestIDMiddleware`中间件自动处理，开发者可在`Handler`层通过`middleware.GetRequestID(c *gin.Context)`函数获取。

## 9. 核心工具使用规范

### 9.1 时间与时区处理规范

为确保系统中所有时间数据的一致性和可靠性，**必须**严格遵循以下时间和时区处理规范。

- **默认时区**：系统**必须**统一使用北京时间（`Asia/Shanghai`，UTC+8）作为标准时区，并在数据库注释、代码注释、SWAG注释中明确标注。
- **时区配置**：应用启动时**必须**显式设置全局时区。
- **数据库时区**：数据库连接**必须**配置为`Asia/Shanghai`。
- **时间展示**：所有面向用户的时间展示**必须**转换为北京时间。
- **集中管理**：所有时区和时间处理逻辑**必须**使用`dci-monitor/internal/utils/timeutil`包中提供的工具函数。
- **严禁直接使用**：代码中**严禁**直接调用`time.Now()`或`time.Now().In(time.Local)`等原生方法，以避免时区不一致问题。
