---
title: |
  DCI数据监测系统

subtitle: |
  需求分析
---

## 1. 需求分析与背景

本系统作为 DCI (数据中心互联) 整体解决方案中的监控子系统，旨在与多云管理系统、网络自动化控制系统等协同工作，实现对 DCI 网络环境（骨干网、城域网、业务网、IDC）的全方位实时监测与分析。在拓扑数据处理方面，本系统负责采集和处理原始的LLDP邻居信息，执行设备和端口标识符到系统标准ID的转换，并将处理后的拓扑变更数据发送给多云管理系统，由多云管理系统负责图数据的存储、丰富和可视化展示。

当前阶段聚焦于**业务网**的监控，核心目标包括：
1.  **实时业务网络状态监测及告警:** 通过指标数据、日志数据等监控跨机房 A-Z 专线流量、网络节点状态（如交换机性能、链路质量），提供计费所需的原始流量数据（计费逻辑由其他系统处理），并进行异常告警。
2.  **网络自动化任务协同监控:** 接收网络自动化控制系统下发的任务启停信号，为网络变更任务提供监控服务，监控配置变更任务期间的网络影响（相关日志、性能波动、网络状态、拓扑变更、异常告警等），并在任务结束后生成分析报告。
3.  **数据可视化与报告:** 为数据大屏展示提供数据支撑，聚焦核心业务指标和网络状态；具备生成周期性报告（日报、周报、月报）及特定事件报告（如自动化任务报告）的能力。数据可视化还需服务于大屏展示。
4.  **拓扑数据处理及转发:** 采集并处理网络设备拓扑相关信息（如LLDP邻居信息），执行设备和端口标识符到系统标准ID的转换，生成拓扑数据，并将处理后的数据发送给多云管理系统进行存储与展示。
5.  **数据服务化【暂不涉及】:** 为经过授权的内部或外部租户（如 IDC 租户）提供按需访问其相关网络监控数据的接口，并确保数据隔离与安全。

### 1.1 数据源分析

本系统需要集成和处理来自多种网络监控协议和信号的数据源，每种数据源都有其独特的价值和特性，共同构成全方位的监控视图。
#### 1.1.1 网络设备采集协议
*   **SNMP (Simple Network Management Protocol):**
    *   **作用:** 作为基础的网络设备状态和性能监控手段，提供广泛的标准化指标。
    *   **关键信息:**
        *   **设备健康状态:** CPU 使用率、内存利用率、温度等，用于判断节点健康度。
        *   **接口统计:** `ifInOctets`, `ifOutOctets`, `ifInDiscards`, `ifOutDiscards`, `ifInErrors`, `ifOutErrors` 等标准 MIB-II 接口计数器。这是计算 **A-Z 专线流量**的基础数据之一（通过差值计算速率），也是判断接口健康度的关键。
        *   **接口状态:** `ifOperStatus`, `ifAdminStatus`，用于监控链路物理和管理状态。
        *   **设备信息:** `sysUpTime` (判断设备重启), `sysDescr` (设备型号), `sysName` (设备名) 等基础信息。
    *   **考量:**
        *   **实时性有限:** SNMP 通常基于轮询（Polling），采集频率受限于轮询间隔（如 15s 或更长），对于瞬时峰值或快速变化可能不够敏感。
        *   **设备负载:** 大量 OID 的频繁轮询可能增加网络设备 CPU 负担。
        *   **标准化与私有 MIB:** 需要同时支持标准 MIB 和特定厂商的私有 MIB 以获取更详细信息。

*   **LLDP (Link Layer Discovery Protocol，基于SNMP的LLDP MIB获取):**
    *   **作用:** 采集和处理网络设备的LLDP邻居信息，是构建网络拓扑图的核心数据源，用于发现和可视化网络连接关系。
    *   **关键信息:**
        *   **本地端口标识符:** 包括端口ID子类型（如接口名称、MAC地址）和具体的端口ID值，用于标识本地设备的连接端口。
        *   **远端设备标识符:** 远端设备的机箱ID子类型（通常是MAC地址）和具体的机箱ID值，用于唯一标识邻居设备。
        *   **远端端口标识符:** 远端设备的端口ID子类型和端口ID值，标识邻居设备与本地设备相连的具体端口。
        *   **远端系统名称:** 邻居设备的系统名称，提供便于人类识别的设备标识。
    *   **考量:**
        *   **标识符转换:** 需要将LLDP中的各种标识符（MAC地址、系统名称、端口名称等）映射到系统内部统一的设备和端口ID，实现跨系统的标准化标识。
        *   **拓扑变更计算:** 需要比较前后采集的LLDP数据，计算出网络拓扑的变更情况（新增或移除的连接）。
        *   **数据稳定性:** LLDP数据相对稳定，不需要高频采集，可优化采集频率（如5-15分钟一次）以减少设备负担。
        *   **多厂商兼容性:** 不同厂商设备的LLDP实现可能有细微差异，需要具备适配能力。

*   **Syslog:**
    *   **作用:** 捕获异步的、离散的设备事件和日志信息，对于故障诊断、安全审计和理解特定操作（如自动化任务）期间发生的情况至关重要。
    *   **关键信息:**
        *   **交换机接口状态变化:** Link Up/Down 事件。
        *   **路由协议邻居状态:** BGP/OSPF 邻居 Up/Down 事件。
        *   **设备重启/异常:** 硬件故障、电源事件、系统错误日志。
        *   **配置变更日志:** 当网络自动化任务执行时，设备可能会生成相应的配置日志，用于关联验证任务执行情况。
        *   **安全事件:** 登录失败、访问控制列表 (ACL) 命中等。
    *   **考量:**
        *   **格式多样性:** 不同厂商、不同模块的 Syslog 格式差异很大，需要强大的解析能力（如 Logstash/Fluentd 的 Grok 或 Dissect）。
        *   **信息丢失风险:** Syslog 通常基于 UDP，存在丢失可能（除非配置 TCP Syslog）。
        *   **关联性:** 需要将日志事件与具体的设备、接口、时间点及自动化任务 ID 进行有效关联。

*   **sFlow/NetFlow (及 IPFIX 等变体)【低优先级】:**
    *   **作用:** 提供详细的网络流量信息，是理解"谁在和谁通信、通信了多少"的核心数据源，对 **A-Z 业务流量监控、统计、计费数据提供**以及安全分析至关重要。
    *   **关键信息 (流记录 - Flow Records):**
        *   **五元组/七元组:** 源/目的 IP 地址、源/目的端口、协议号。
        *   **流量统计:** 数据包数 (Packets)、字节数 (Bytes)。
        *   **接口信息:** 输入接口索引 (Input Interface)、输出接口索引 (Output Interface)。
        *   **时间戳:** 流开始和结束时间。
        *   **(可选) TCP 标志位, VLAN ID, ToS/DSCP 值等。**
    *   **关键信息 (计数器样本 - Counter Samples, sFlow):**
        *   类似 SNMP 的接口计数器，但由设备主动推送。
    *   **考量:**
        *   **数据量巨大:** 流记录数据量可能非常庞大，对采集、传输、存储和处理能力要求高。
        *   **采样 (sFlow):** sFlow 基于采样，结果是估算值，对于精确计费可能需要结合其他数据或进行校准。NetFlow 通常是全量采集（但配置也可能采样）。
        *   **处理复杂性:** 需要专门的流处理逻辑来聚合、分析流记录，例如计算 A-Z 端点间的总流量。Elasticsearch 对这类高基数数据的聚合分析能力很强。

*   **Telemetry (流式遥测)【低优先级】:**
    *   **作用:** 提供高频率（秒级甚至亚秒级）、高粒度的指标和状态数据推送，弥补 SNMP 实时性不足的缺点，尤其适用于对性能和状态变化敏感的场景。
    *   **关键信息:**
        *   **高频接口计数器:** 比 SNMP 更实时地反映接口流量变化，用于更精确的速率计算和拥塞检测。
        *   **队列深度/缓冲区占用:** 监控设备内部转发平面的拥塞情况。
        *   **网络质量指标:** 如果设备通过 YANG 模型推送时延、抖动、丢包率等信息，将非常有价值。
        *   **设备状态推送:** 更实时的 CPU/内存利用率、温度等。
    *   **考量:**
        *   **厂商差异:** 不同厂商对 Telemetry 的支持（协议如 gNMI/gRPC, NETCONF，数据模型 YANG，编码格式 GPB/JSON）差异较大，需要适配。
        *   **配置复杂性:** 需要在设备上精确配置订阅路径和推送频率。
        *   **数据量:** 高频推送同样会产生大量数据。

*   **BMP (BGP Monitoring Protocol)【低优先级】:**
    *   **作用:** 专注于 BGP 路由信息的监控，提供 BGP 邻居状态变化、收到的路由更新（包括撤销）、路由表快照等详细信息。主要用于分析路由稳定性、排查路由问题、了解路由策略影响。在业务网监控阶段，如果业务依赖于特定的 BGP 路由，则有监控价值。
    *   **关键信息:** Peer Up/Down 事件、收到的 BGP Update 消息（NLRI, AS_PATH, Communities 等路径属性）。
    *   **考量:** 需要专门的 BMP Collector 进行协议解析。数据结构复杂，分析门槛较高。

*   **BGP-LS (BGP Link-State)【低优先级】:**
    *   **作用:** 利用 BGP 协议分发网络拓扑和链路状态信息（通常来自 IGP），用于构建全局网络拓扑视图、路径计算 (PCE)、流量工程等。对于监控系统，可用于**网络拓扑可视化**和关联故障影响范围。
    *   **关键信息:** Node NLRI (节点信息), Link NLRI (链路信息及属性，如度量值、带宽), Prefix NLRI。
    *   **考量:** 需要 BGP Collector 支持 BGP-LS。数据用于拓扑构建，与指标/日志分析侧重点不同，可能需要图数据库或专门处理逻辑。

*   **JMX (Java Management Extensions)【未来补充】:**

*   **APM (Application Performance Monitoring)【未来补充】:**

#### 1.1.2 数据源背景信息

交换机部署于IDC中，跨机房 A-Z 专线流量，其中A和Z代表：某个机房中的某个交换机的某个端口，这两个端口用专线连接起来，实现A和Z之间的专线通信。一共有50个机房，每个机房有2台交换机，每个交换机有48个端口，这是最基础的数据采集对象数量。

**数据保留要求：**
- 日志元数据存储至少保留1年1个月（396天）
- 流量元数据存储至少保留1年1个月（396天）
- CPU、内存、磁盘、风扇、温度、电源以及其他可能存在的指标数据存储至少保留1年（365天）
- 时序数据热数据保留1.5年（547天），冷数据（降采样后）保留5年（1825天）

**采集规模与压力评估：**

1. **采集对象数量：**
   - 交换机总数：200台交换机
   - 端口总数：200台 × 48端口/台 = 9,600个端口
   - 专线连接数：根据业务需求，预计有2,000条A-Z专线连接

2. **采集频率与数据量：**
   - SNMP指标采集：
     - 基础指标（CPU、内存、磁盘、风扇、温度、电源等）：每60秒/次，每台设备约15个指标
     - 接口指标（流量、错误等）：每15秒/次，每个端口约8个指标
     - 估算每分钟数据点数：(200台 × 15指标 × 1次) + (9,600端口 × 8指标 × 4次) = 3,000 + 307,200 = 310,200点/分钟
   - Syslog日志：
     - 平均每台设备每分钟5条日志，高峰期可达50条/分钟
     - 估算每分钟日志量：200台 × 5条/分钟 = 1,000条/分钟（常态），高峰期10,000条/分钟
   - LLDP拓扑数据：
     - 采集频率：每5分钟/次
     - 估算数据量：200台 × 48端口/台 × 1次/5分钟 = 1,920条/5分钟
   - 流量记录(NetFlow/sFlow)：
     - 采样率：1:1000（每1000个数据包采样1个）
     - 估算每分钟流记录数：根据网络流量，预计平均4,000条/分钟，高峰期可达40,000条/分钟

3. **系统资源需求评估表：**

| 组件类型 | 资源类别 | 配置明细 | 数量 | 单节点核数 | 单节点内存（GB） | 存储需求(GB) | 总存储(GB) | 说明 |
|---------|---------|---------|------|------|----------|------------|----------|------|
| **存储资源** |
| Prometheus/Thanos | 时序数据库 | 热存储 | 3节点集群 | 2 | 4 | 500 | 1,500 | 保留547天热数据，3倍复制因子 |
| Prometheus/Thanos | 时序数据库 | 冷存储 | - | - | - | 300 | 300 | 保留1825天冷数据(降采样) |
| Elasticsearch | 日志存储 | 日志数据 | 3节点集群 | 2 | 4 | 800 | 2,400 | 保留396天，2倍复制因子 |
| MySQL | 关系型数据库 | 元数据 | 3节点集群 | 0 | 0 | 0 | 0 | 主从复制，含索引和备份，与多云系统共用，不做统计 |
| **消息队列** |
| Kafka | 消息队列 | 消息处理 | 3节点集群 | 2 | 4 | 120 | 360 | 3天消息保留，3倍复制因子 |
| **采集处理** |
| Logstash | 日志处理 | 日志解析与转发 | 3节点集群 | 4 | 8 | 50 | 150 | 临时文件存储与缓存 |
| dciagent | 采集代理 | 采集节点 | 10节点 | 2 | 4 | 5 | 200 | 每20台设备1个agent节点 |
| **应用服务** |
| 应用服务器 | 应用计算 | Web服务与API | 3节点集群 | 4 | 8 | 50 | 150 | 含操作系统和应用程序 |
| **系统合计** |
| 全系统资源（不含dciagent） | 合计 | - | 15节点 | 42 | 84 | 4,860 | 总计约4.9TB存储空间 |
| 全系统资源（含dciagent） | 合计 | - | 25节点 | 62 | 124 | 4,910 | 总计约4.9TB存储空间 |

4. **系统合计资源：**

| 组件 | 节点数 | 总核数 | 总内存 | 总存储(GB) |
|------|-------|-------|-------|-----------|
| Prometheus/Thanos | 3 | 6 | 12 | 1,800 |
| Elasticsearch | 3 | 6 | 12 | 2,400 |
| MySQL | 0 | 0 | 0 | 0 |
| Kafka | 3 | 6 | 12 | 360 |
| Logstash | 3 | 12 | 24 | 150 |
| dciagent | 10 | 20 | 40 | 50 |
| 应用服务器 | 3 | 12 | 24 | 150 |
| **总计** | **25** | **62** | **124** | **4,910** |

5. **数据吞吐量与带宽需求：**

| 数据类型 | 常态吞吐量(KB/秒) | 峰值吞吐量(KB/秒) | 日均数据量(GB) |
|---------|-----------------|-----------------|--------------|
| SNMP指标数据 | 310,200点/分钟×50字节 = 258 | 310 | 33.4 |
| Syslog日志数据 | 1,000条/分钟×500字节 = 8.3 | 83.3 | 1.1 |
| 流量记录数据 | 4,000条/分钟×200字节 = 13.3 | 133.3 | 1.7 |
| LLDP拓扑数据 | 1,920条/5分钟×300字节 = 1.9 | 2.0 | 0.3 |
| 总计网络带宽 | 约282KB/秒 | 约529KB/秒 | 约36.5GB/天 |

6. **Kafka资源详细配置：**

| 配置项 | 数值 | 说明 |
|-------|------|------|
| 主要Topic | 5个 | metrics, logs_events, flows, topology_raw, control |
| 总分区数 | 40个 | metrics(16), logs_events(8), flows(8), topology_raw(4), control(4) |
| 单Broker内存 | 4 | 基础开销(1), 页缓存(2), 分区内存(0.5), 处理开销(0.5) |
| 单Broker CPU | 2核 | 基础开销(1核), 消息处理(0.5核), 冗余(0.5核) |
| 单Broker存储 | 120GB | 3天数据(约36.5GB×3×复制因子)/3节点 |
| 集群规模 | 3节点 | 确保高可用性 |
| 集群总资源 | 6核, 12 | 3个节点总和 |

7. **dciagent详细配置：**

| 配置项 | 数值 | 说明 |
|-------|------|------|
| 部署方式 | 分布式 | 根据设备地理位置和网络分区部署 |
| 采集能力 | 每节点20台设备 | 基于SNMP轮询压力估算 |
| 单节点CPU | 2核 | 采集处理(1核), 数据处理(0.5核), 冗余(0.5核) |
| 单节点内存 | 4 | 基础开销(1), 数据缓存(2), 处理开销(1) |
| 单节点存储 | 5GB | 配置文件, 临时数据, 日志存储 |
| 节点数量 | 10个 | 200台设备÷20台/节点 |
| 总体资源 | 20核, 40 | 10个节点总和 |

8. **Logstash详细配置：**

| 配置项 | 数值 | 说明 |
|-------|------|------|
| 部署方式 | 集中式集群 | 中心化日志处理 |
| 处理能力 | 每节点15,000条/分钟 | 基于常态日志量估算 |
| 单节点CPU | 4核 | 输入处理(1核), 过滤解析(2核), 输出处理(1核) |
| 单节点内存 | 8 | JVM堆内存(4), 系统内存(4) |
| 单节点存储 | 50GB | 系统(10GB), 临时队列(30GB), 日志(10GB) |
| 节点数量 | 3个 | 高可用集群 |
| 总体资源 | 12核, 24 | 3个节点总和 |

9. **数据监测系统总体资源需求汇总：**

| 资源类型 | CPU核心数 | 内存（GB） | 存储容量(GB) | 备注 |
|---------|-----------|---------|------------|------|
| Prometheus/Thanos | 6 | 12 | 1,800 | 3节点高可用集群总和 |
| Elasticsearch | 6 | 12 | 2,400 | 3节点高可用集群总和 |
| MySQL | 0 | 0 | 0 | 与多云系统共用 |
| Kafka | 6 | 12 | 360 | 3节点高可用集群总和 |
| Logstash | 12 | 24 | 150 | 3节点高可用集群总和 |
| 采集Agent（dciagent） | 20 | 40 | 50 | 10节点分布式部署总和 |
| 采集系统服务端 | 12 | 24 | 150 | 3节点高可用集群总和 |
| **总计（含采集Agent）** | **62** | **124** | **4,910** | **高可用集群全部组件** |

1.  **扩展性考虑：**
   - 系统设计支持监控对象数量增长到现有规模的5倍
   - 关键组件采用集群部署，支持水平扩展
   - 采用数据分层存储策略，优化存储成本和查询性能
   - 预留30%额外资源用于处理突发负载和未来扩展

### 1.2 数据库选型

为支撑上述多样化的数据处理和分析需求，采用混合数据库方案（Polyglot Persistence）是最佳选择：
*   **时间序列数据库 (TSDB -选用 Prometheus):** 最适合存储和查询 SNMP 指标、sFlow/NetFlow 计数器、Telemetry 指标、网络质量指标等时间序列数据，提供高效的聚合、降采样和查询性能。
*   **日志/搜索引擎 (选用 Elasticsearch):** 适用于存储和分析 Syslog 日志、sFlow/NetFlow 流记录（用于细节钻取和安全审计）、Telemetry 事件、自动化任务相关日志及生成的报告内容，提供强大的全文搜索和聚合能力。
*   **关系型数据库 (选用 MySQL):** 用于存储结构化的元数据和关联信息，包括：Telegraf 客户端配置和状态、自动化任务元数据（ID、时间、状态、报告关联）、A-Z 业务链路定义、租户与设备/接口的映射关系（用于数据供应权限控制）、周期性报告和自动化任务报告的元数据。

此组合能够发挥各类数据库的优势，满足系统对不同数据类型在存储效率、查询性能和分析灵活性上的要求。

### 1.3 系统功能模块需求

系统功能模块从采集、处理、存储到展示层面，围绕监控需求实现全方位覆盖：

#### 1.3.1 数据采集层

数据采集层负责从各类数据源获取原始数据，为后续分析提供基础。

*   **多源数据采集模块:**
    *   支持对网络设备通过 SNMP、Syslog、sFlow/NetFlow、Telemetry 等多种协议并行采集，支持对网络设备进行采集 BGP 路由信息。优先使用开源且成熟的采集器，Golang开发客户端管理模块，控制多源采集器生命周期和配置下发。
    *   采集数据通过 Kafka 队列进行缓冲，削峰填谷
    *   支持分布式部署采集节点，实现对地理分散环境的全面覆盖
    *   具备采集节点的自动注册功能，安装部署后自动上线
    *   支持采集策略配置下发，自动重载及状态上报
    *   提供采集节点健康状态监控，确保采集模块运行正常，采集任务可靠执行

*   **自定义采集模块:**
    *   通过 Web 界面提供可视化的采集规则定义功能，远程配置新的采集任务【优先级低】
    *   支持自定义采集频率、采集深度、采集条件与阈值设置【优先通过配置文件实现】
    *   提供对采集数据的预处理能力，包括过滤、转换、聚合、计算等操作
    *   提供接口与第三方采集工具集成，扩展采集能力（基于Kafka）【优先级低】
    *   支持对采集任务的调度管理，包括一次性、周期性和事件触发型、API接口触发型采集

#### 1.3.2 数据处理层

数据处理层负责对原始数据进行清洗、转换和分析。

*   **数据清洗模块:** 【优先级低】
    *   实现数据去重功能，针对重复上报的数据进行识别与合并 【优先级低】
    *   通过规则引擎进行数据过滤，去除不符合业务逻辑的异常数据 【优先级低】
    *   执行数据标准化处理，统一不同来源数据的格式、单位和表示方式
    *   提供数据质量评估功能，从完整性、准确性、一致性等维度评估数据质量 【优先级低】
    *   支持数据清洗规则的灵活配置与版本管理 【优先级低】

*   **数据解析模块:** 【优先级低】
    *   支持多种格式数据的解析 
    *   支持对解析过程的监控与故障诊断

*   **实时分析模块:**
    *   基于A-Z端对端流量计算业务流量大小
    *   通过异常告警规则对数据流进行匹配识别，检测异常事件

*   **基线分析与告警模块:**
    *   **指标数据告警机制：**
        *   基于Prometheus实现指标数据的告警规则体系
        *   支持静态阈值告警，针对不同指标设置固定阈值
        *   提供PromQL查询机制，定期从Prometheus获取监控目标的最新数据
        *   支持多种条件操作符比较（大于、小于、等于、不等于等）
        *   实现持续时间验证功能，确保指标超过阈值一定时间后才触发告警
        *   支持标签匹配，精确定位告警目标（如特定设备、特定端口）
        *   提供告警规则的版本管理和历史记录功能
        *   预留基于动态基线的告警机制扩展接口，用于未来增强 【优先级低】
    
    *   **日志数据告警机制：**
        *   基于Elasticsearch实现日志数据的告警规则体系
        *   支持基于日志内容的关键字匹配、正则表达式匹配
        *   提供基于日志聚合的统计告警能力，如特定错误日志在一定时间内出现次数超过阈值
        *   支持日志事件关联分析，识别跨设备、跨时间的复杂事件模式
        *   实现日志分类和优先级标记，针对不同类型日志设置不同告警级别
        *   提供日志告警规则的模板管理，支持快速创建常见场景的告警规则
        *   支持日志告警的上下文捕获，便于故障定位和根因分析
    
    *   **统一告警管理【优先级低】：**
        *   提供统一的告警数据模型，整合指标告警和日志告警
        *   实现告警级别划分（如关键、警告、信息等），便于分级处理
        *   支持告警的去重、抑制和分组功能，避免告警风暴
        *   提供告警静默机制，支持在维护窗口期间暂停特定告警
        *   实现告警路由，根据告警类型、级别、来源等属性路由到不同处理流程
        *   支持告警的状态管理，包括未确认、已确认、处理中、已解决等
        *   提供告警的统计分析功能，支持按设备、类型、时间等维度进行分析
        *   实现告警与自动化任务的关联，识别因配置变更导致的告警

*   **拓扑数据处理模块:**
    *   支持通过SNMP协议采集支持LLDP标准MIB的网络设备邻居信息
    *   支持多厂商设备的LLDP数据解析与标准化处理
    *   提供设备和端口标识符（如设备MAC地址、系统名称、接口名称等）到数据库标准ID（dci_device.id和dci_logic_port.id）的转换功能
    *   实现拓扑变更计算，生成节点和连接的增删改手动操作指令 【由多云管理系统负责】
    *   支持将处理后的拓扑数据发送至多云管理系统，实现与图数据库的集成
    *   通过Agent的分布式部署架构，支持大规模网络环境下的拓扑数据采集与处理
    *   针对LLDP数据的相对稳定特性，优化采集频率（5min），减少设备和网络负担

#### 1.3.3 数据存储层

数据存储层提供高效、安全的数据持久化和查询能力。

*   **时间序列数据库:**
    *   采用高压缩比的存储引擎，降低大规模时序数据的存储成本
    *   支持多粒度数据降采样与预聚合，提升查询效率
    *   提供数据分区与分片功能，实现水平扩展
    *   支持热数据与冷数据分层存储策略
    *   实现数据自动归档与清理功能，控制存储增长
    *   提供高可用集群部署方案，确保服务连续性
    *   支持数据备份与恢复功能，防止数据丢失

*   **关系型数据库:**
    *   存储系统配置、采集模块配置、客户端配置、元数据、规则定义等结构化信息
    *   支持事务处理，确保数据一致性
    *   云数据库服务器提供高可用性保障，定期备份

#### 1.3.4 展示层

展示层通过直观的界面将数据价值呈现给用户，支持决策与分析。

*   **大屏展示模块:**
    *   支持大屏展示模式，为大屏展示提供数据支持

*   **报表生成模块:**
    *   支持日报、周报、月报等周期性报表的自动生成
    *   实现报表的个性化定制功能，包括内容选择、排版设计等【优先级低】
    *   支持多种报表格式，包括PDF、Word等
    *   提供报表的自动分发功能，支持邮件、共享存储等分发方式
    *   实现报表的版本管理与历史查询功能
    *   支持报表数据的导出与二次处理

*   **告警管理模块:**
    *   提供告警规则的完整生命周期管理，包括创建、查询、更新、删除
    *   支持通过API接收外部规则配置 【优先级低】
    *   实现定时轮询机制，定期评估所有启用的告警规则
    *   支持告警风暴抑制功能，如去重等，防止海量重复告警 【优先级低】
    *   提供多渠道告警通知（电子邮件、短信、消息系统、Webhook等）【优先级低】
    *   实现通知模板定制，支持不同场景的消息格式 【优先级低】
    *   提供通知分组功能，根据告警级别和类型路由到不同接收人 【优先级低】
    *   展示分级分类的告警信息，突出显示高优先级告警
    *   支持告警的状态跟踪功能，包括未确认、已确认、处理中、已解决等
    *   实现告警的关联分析与根因定位
    *   提供告警处理流程管理功能，支持分配、升级、知识库关联等
    *   支持告警的统计与趋势分析功能
    *   提供灵活的告警过滤与搜索功能

#### 1.3.5 系统管理层

系统管理层提供对整个监测系统的配置、控制和状态监控能力。

*   **配置管理模块:**
    *   提供集中化的系统参数配置管理界面
    *   支持采集节点配置的模板化管理与批量下发
    *   实现配置版本控制，支持配置回滚与历史追溯
    *   提供配置校验功能，防止错误配置导致系统问题
    *   支持配置的导入导出，便于环境迁移与备份
    *   实现配置变更的审计与权限控制

*   **系统监控模块:**
    *   监控监测系统自身的运行状态与性能指标
    *   提供组件级别的健康检查与故障诊断
    *   实现系统资源（CPU、内存、磁盘等）使用监控
    *   支持系统关键事件的日志记录与分析
    *   提供系统容量规划与性能瓶颈分析工具
    *   实现系统状态的可视化展示

#### 1.3.6 网络自动化任务协同监控

网络自动化任务协同监控负责与网络自动化控制系统进行交互，监测网络配置变更过程中的各项指标，评估变更影响并生成分析报告。

*   **任务信号处理模块:**
    *   接收来自网络自动化控制系统的任务启动和结束信号
    *   自动创建监控会话，关联任务元数据和目标设备
    *   针对性收集与任务相关的系统日志、设备日志和性能指标
    *   支持多维度评估，包括设备健康状况（CPU、内存、磁盘等占用情况变化）、网络吞吐量、网络延迟、丢包率变化等
    *   支持接收任务异常中断和回滚信号，启动变更任务协同监控方面相应的异常记录流程
    *   提供 API 接口作为网络自动化任务信号接收通道

*   **任务监控增强模块:** 【优先级低】
    *   在任务执行期间提高相关设备的监控频率（如交换机CPU、内存资源占用状态，网络带宽等采集频率），获取更精细的性能数据【优先级低】
    *   联动如基线指标等已执行的告警检测规则，监测配置变更对网络流量和链路状态的实时影响。【优先级低】
    *   建立变更前后的动态基线指标对比（如交换机CPU、内存资源占用状态，网络带宽等平均值在变更前后的变化），分析变更前后的网络性能指标差异，识别潜在问题，并用于**网络变更影响评估**【优先级低】
    *   自动补齐监控任务全生命周期关键节点，包括执行前阶段（如获取配置变更前60分钟，网络流量和链路状态的基线指标数据）、执行阶段、完成阶段和执行后阶段（如获取配置变更后2分钟，网络流量和链路状态的基线指标数据） 【暂无此需求，优先级低】

*   **任务追踪与审计模块:**
    *   为每个自动化任务生成唯一标识符，联动与任务相关的流量、指标、日志、拓扑等各个类型数据，贯穿整个监控过程
    *   记录任务执行的详细时间线，包括开始时间、结束时间和关键节点时间戳，时间戳以服务器时间为准
    *   建立配置变更与网络事件的关联关系，支持根因分析 【优先级低】
    *   提供任务执行历史查询功能，支持按时间、设备、结果等条件筛选
    *   实现任务监控数据的长期归档，便于后期审计和分析

*   **报告生成模块:**
    *   在任务完成后自动生成任务执行分析报告
    *   报告包含任务摘要、变更内容、任务期间数据监测结果、网络影响评估和性能对比
    *   提供网络性能指标的变更前后对比图表，直观展示变更影响【优先级低】
    *   提供变更期间的异常事件和警告信息
    *   支持自定义报告模板，满足不同场景的报告需求 【优先级低】
    *   生成的报告可导出为 PDF、Word 或其他标准格式 【优先级低】
    *   支持报告的自动分发，通过邮件或通知系统发送给相关人员 【优先级低】