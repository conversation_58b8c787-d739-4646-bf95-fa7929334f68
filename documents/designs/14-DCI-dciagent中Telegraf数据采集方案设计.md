---
title: |
  DCI-Telegraf数据采集方案设计

subtitle: |
  数据监测系统指标采集与处理策略
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-12 | 顾铠羟 | 初始版本           |
| V1.1 | 2025-05-19 | 顾铠羟 | 调整架构设计，明确Kafka中转模式作为标准实现方式 |
| V1.2 | 2025-05-21 | 顾铠羟 | 更新架构图和配置管理描述 |
| V1.3 | 2025-05-27 | 顾铠羟 | 重构文档，突出数据采集策略，移除部署相关内容 |

# 1 文档介绍

## 1.1 文档目的

本文档详细描述 DCI 数据监测系统中 Telegraf 的数据采集策略和配置方案。文档为后续 Telegraf 实施提供规范化的设计指导，确保采集数据的一致性、可靠性和完整性，支持整体监控系统的高效运行。

## 1.2 文档范围

本文档涵盖 DCI 数据监测系统中 Telegraf 采集器的采集策略设计，包括：

1. 数据采集范围与指标定义
2. 指标设计和数据模型
3. 与 Prometheus 和 Kafka 的数据集成模式
4. 标签与元数据策略
5. 配置文件结构与模板

## 1.3 相关文档

本文档为 Telegraf 数据采集策略设计文档，与以下文档有密切联系：

1. **《11-DCI-Telegraf-SNMP采集实现方案.md》** - 提供 SNMP 采集的具体实现细节，是本文档在 SNMP 领域的技术实施指南
2. **《15-DCI-dciagent中Telegraf部署架构与流程.md》** - 详细说明 Telegraf 的部署架构和运维管理流程
3. **《02-网络自动化平台-数据监测系统技术概要设计.md》** - 提供整体系统架构，本文档是其中数据采集策略部分的详细设计

# 2 总体设计

## 2.1 设计目标

Telegraf 数据采集方案设计实现以下目标：

1. 构建高效、可扩展的指标采集架构
2. 确保采集数据的准确性和完整性
3. 最小化对被监控设备的性能影响
4. 支持横向扩展以应对监控规模增长
5. 与 Prometheus 和 Kafka 实现无缝集成
6. 确保遵循 DCI 监测系统"Agent 端各类采集数据均通过 Kafka Topic 发送"的核心设计原则

## 2.2 数据采集架构

Telegraf 在 DCI 数据监测系统中扮演关键的数据采集角色，负责从网络设备和服务器采集各类监控指标。数据采集架构设计如下：

```mermaid
graph TD
    subgraph "数据源"
        Devices[网络设备]
        Servers[服务器]
        Apps[应用服务]
    end
    
    subgraph "数据采集层"
        subgraph "Telegraf实例"
            T1[数据采集]
            T2[数据处理]
            T3[数据输出]
        end
    end
    
    subgraph "数据传输层"
        Kafka[Kafka集群]
    end
    
    subgraph "数据处理与存储层"
        Exporter[Kafka-Prometheus Connector]
        Prom[Prometheus]
        Thanos[Thanos]
    end
    
    Devices -- SNMP --> T1
    Servers -- 系统指标 --> T1
    Apps -- 应用指标 --> T1
    
    T1 --> T2
    T2 --> T3
    
    T3 -- 数据输出 --> Kafka
    
    Kafka -- 数据消费 --> Exporter
    Kafka -.-> 其他系统
    
    Exporter -- 暴露指标 --> Prom
    
    Prom --> Thanos
    
    %% 样式定义
    classDef primary fill:#d9f,stroke:#333,stroke-width:1px
    classDef secondary fill:#9cf,stroke:#333,stroke-width:1px
    classDef connect fill:#afc,stroke:#333,stroke-width:1px
    
    class Kafka secondary
    class Exporter connect
    class Prom,Thanos primary
```

## 2.3 技术选型

1. **Telegraf**: 选择 Telegraf 作为主要采集代理，基于以下优势：
   - 支持多种输入插件，适配不同数据源
   - 支持多种输出格式，包括 Prometheus 兼容格式和 Kafka 输出
   - 资源占用低，适合分布式部署
   - 配置灵活，支持静态配置和动态配置
   - 良好的社区支持和插件生态

2. **数据输出模式**:
   - **Kafka 集成**: 采用 Telegraf 的 Kafka 输出插件作为标准方式，符合 DCI 系统设计原则
   - **Prometheus 集成**: 通过 Kafka-Prometheus 连接器实现，而非直接输出

3. **数据格式**:
   - JSON 格式：结构化的消息格式，便于多系统处理
   - 支持标签和字段的灵活组合

# 3 详细设计

## 3.1 功能模块

### 3.1.1 数据采集模块

数据采集模块负责从各类数据源获取监控指标，主要包括以下采集类型：

1. **SNMP采集**:
   - 设备基本信息（sysInfo, sysName等）
   - 接口指标（流量、错误、丢包率等）
   - CPU和内存使用率
   - 温度等环境参数
   - LLDP邻居信息（用于拓扑发现）
   
   **注意**: SNMP采集的详细实现方案，包括MIB管理、厂商特定配置，请参考《11-DCI-Telegraf-SNMP采集实现方案.md》。本文档主要关注总体采集策略设计。

2. **系统指标采集**:
   - 服务器CPU、内存、磁盘、网络指标
   - 进程和服务状态

3. **应用指标采集**:
   - 应用自定义指标（通过HTTP接口或其他方式）
   - 数据库性能指标
   - Web服务性能指标

4. **日志采集**（可选）:
   - Syslog数据
   - 应用日志

### 3.1.2 数据输出模块

数据输出模块负责将采集的数据发送到不同的目标系统：

1. **Kafka输出**（标准方式）:
   - 将数据输出到Kafka主题，用于集中处理和存储
   - 支持多种序列化格式（JSON、Influx Line Protocol等）
   - 配置正确的分区策略和消息键
   - 符合DCI系统"Agent端各类采集数据均通过Kafka Topic发送"的核心设计原则

2. **Prometheus输出**（可选辅助方式）:
   - 通过HTTP服务暴露指标（默认端口9273）
   - 支持Prometheus pull模式采集
   - 仅用于本地监控或特殊需求场景

## 3.2 数据模型

### 3.2.1 指标设计

采用统一的指标命名和结构设计：

1. **指标命名规范**:
   - 格式：`{domain}_{subsystem}_{metric}`
   - 示例：`network_interface_rx_bytes`、`system_cpu_usage`
   - 所有指标名使用小写和下划线分隔

2. **标签设计**:
   - 设备标识标签：`device_id`、`hostname`
   - 位置标签：`location`、`rack`、`datacenter`
   - 分类标签：`device_type`、`vendor`、`model`
   - 组件标签：`interface`、`cpu_id`等

3. **默认指标集**:
   - 网络设备基础指标集
   - 服务器基础指标集
   - 应用服务基础指标集

### 3.2.2 与Prometheus集成模型

1. **指标暴露**:
   - 通过Kafka-Prometheus连接器转换数据
   - 使用Prometheus数据模型（指标名、标签、样本值）

2. **服务发现**:
   - 支持Prometheus的服务发现机制
   - 使用K8s服务发现或基于文件的服务发现

### 3.2.3 与Kafka集成模型

为确保与DCI监测系统架构设计保持一致，所有Telegraf采集的数据必须首先写入Kafka消息队列，而非直接暴露给Prometheus。这是DCI监测系统的核心设计原则之一："Agent端各类采集数据（如SNMP, Syslog, sFlow/NetFlow, Telemetry等）均通过Kafka Topic发送"。

Kafka作为DCI系统的中央消息总线，是所有监控数据必经的中转站。Telegraf采集的所有数据配置为输出到Kafka，实现系统的统一数据流和多系统协同能力。直接输出到Prometheus的方式不符合系统架构设计原则，不在生产环境中使用。

1. **消息格式**:
   - 使用结构化JSON格式
   - 包含指标名、标签、值和时间戳

2. **主题设计**:
   - 主题名：`dci.monitor.v1.defaultchannel.metrics.telegraf`
   - 按照设备ID或agent ID进行分区

## 3.3 采集频率与策略

### 3.3.1 采集频率设计

采集频率设计基于数据重要性和资源消耗平衡：

1. **基础设备状态**:
   - 采集频率：60秒
   - 包括设备基本信息和状态指标

2. **关键性能指标**:
   - 采集频率：15-30秒
   - 包括CPU、内存、关键接口流量等

3. **非关键指标**:
   - 采集频率：5分钟
   - 包括环境参数、次要接口状态等

4. **事件数据**:
   - 实时采集（基于事件触发）
   - 包括SNMP Trap、Syslog等

### 3.3.2 资源优化策略

1. **批处理机制**:
   - 设置合理的批处理大小（默认1000点）
   - 配置适当的刷新间隔（默认10秒）

2. **采集调度**:
   - 使用jitter机制错开采集时间，避免采集风暴
   - 配置采集超时和重试机制

3. **数据过滤**:
   - 配置智能过滤规则，减少无用数据传输
   - 支持正则表达式和条件过滤

# 4 配置模板设计

## 4.1 配置文件结构

Telegraf配置采用分层模块化结构，便于管理和复用：

```
telegraf.conf            # 主配置文件（加载其他配置）
telegraf.d/              # 配置片段目录
  ├── inputs/            # 输入插件配置
  │   ├── snmp.conf      # SNMP采集配置
  │   └── system.conf    # 系统指标采集配置
  ├── outputs/           # 输出插件配置
  │   ├── kafka.conf     # Kafka输出配置
  │   └── prometheus.conf # Prometheus输出配置（可选）
  └── processors/        # 处理器配置
      └── converter.conf # 数据转换处理器
```

## 4.2 配置模板示例

### 4.2.1 基础全局配置

```toml
[agent]
  interval = "60s"
  round_interval = true
  metric_batch_size = 1000
  metric_buffer_limit = 10000
  collection_jitter = "5s"
  flush_interval = "30s"
  flush_jitter = "5s"
  precision = "s"
  hostname = "$HOSTNAME"
  omit_hostname = false
```

### 4.2.2 Kafka输出配置（推荐标准配置）

```toml
[[outputs.kafka]]
  ## Kafka brokers
  brokers = ["kafka1:9092", "kafka2:9092"]
  
  ## Topic for metrics
  topic = "dci.monitor.v1.defaultchannel.metrics.telegraf"
  
  ## Compression codec
  compression_codec = "snappy"
  
  ## Optional TLS config
  # tls_ca = "/etc/telegraf/ca.pem"
  # tls_cert = "/etc/telegraf/cert.pem"
  # tls_key = "/etc/telegraf/key.pem"
  
  ## Use SASL authentication
  # sasl_username = "telegraf"
  # sasl_password = "metrics"
  
  ## Data format
  data_format = "json"
  
  ## Max message size
  max_message_bytes = 1000000
```

### 4.2.3 SNMP采集配置

```toml
[[inputs.snmp]]
  ## Agent addresses
  agents = ["udp://switch1.example.com:161"]
  
  ## Timeout for each request
  timeout = "5s"
  
  ## SNMP version
  version = 2
  
  ## SNMP community string
  community = "public"
  
  ## Measurement name
  name = "switch"
  
  ## System fields
  [[inputs.snmp.field]]
    name = "sysName"
    oid = "RFC1213-MIB::sysName.0"
    is_tag = true
  
  [[inputs.snmp.field]]
    name = "sysUpTime"
    oid = "RFC1213-MIB::sysUpTime.0"
  
  ## Interface metrics table
  [[inputs.snmp.table]]
    name = "interface"
    inherit_tags = ["sysName"]
    oid = "IF-MIB::ifTable"
    
    [[inputs.snmp.table.field]]
      name = "ifName"
      oid = "IF-MIB::ifName"
      is_tag = true
```

### 4.2.4 处理器配置

```toml
[[processors.converter]]
  ## Tags to convert
  ##
  ## The table key determines the target type, and the array of key-values
  ## select the keys to convert.  The array may contain globs.
  ##   <target-type> = [<tag-key>...]
  [processors.converter.tags]
    string = ["vlan_id", "port_id"]
    integer = ["ifIndex"]
    
  ## Fields to convert
  ##
  ## The table key determines the target type, and the array of key-values
  ## select the keys to convert.  The array may contain globs.
  ##   <target-type> = [<field-key>...]
  [processors.converter.fields]
    tag = ["status", "state"]
    float = ["utilization", "*_ratio"]
    integer = ["counter*", "total*"]
```

# 5 采集模块风险评估

## 5.1 性能风险

1. **SNMP查询负载**:
   - 风险：大量SNMP查询可能导致网络负载增加
   - 对策：实施合理的采集间隔和批处理策略，避免SNMP风暴
   - 采用jitter机制错开采集时间点

2. **数据传输量**:
   - 风险：高频采集产生大量数据，增加网络和存储负担
   - 对策：实施数据过滤和聚合策略，减少无用数据传输
   - 使用压缩算法减少数据传输量

## 5.2 扩展性风险

1. **大规模部署**:
   - 风险：监控设备数量增加时可能超出单个采集实例能力
   - 对策：实施分布式采集架构，按区域或设备类型划分采集职责

2. **配置管理复杂性**:
   - 风险：大量不同类型设备的配置难以维护
   - 对策：实施基于模板的配置生成和分发机制

## 5.3 兼容性风险

1. **厂商特定实现**:
   - 风险：不同厂商设备的SNMP实现可能存在差异
   - 对策：为每种主要设备类型开发和测试专用配置模板
   - 维护厂商特定MIB文件库

2. **协议版本兼容性**:
   - 风险：SNMPv1/v2c/v3版本混合环境导致配置复杂
   - 对策：构建版本适配层，统一上层接口

# 6 为什么选择Kafka中转模式

Kafka中转模式是DCI监测系统架构的核心设计原则之一，确保所有Agent采集的数据通过统一的Kafka消息队列传输，提供统一的数据集成平台。这种方式提供了以下显著优势：

1. **架构一致性**：符合DCI监测系统"Agent端各类采集数据均通过Kafka Topic发送"的设计原则
2. **系统解耦**：数据生产者与消费者解耦，提高系统弹性
3. **数据缓冲**：防止监控系统波动影响数据采集
4. **多系统集成**：同一份数据可被多个系统并行处理
5. **故障隔离**：下游系统故障不影响数据采集
6. **数据持久化**：支持数据的临时持久化，便于故障恢复
7. **横向扩展**：方便扩展以应对数据量增长

# 7 参考资料

1. Telegraf官方文档：https://docs.influxdata.com/telegraf/
2. Prometheus集成指南：https://prometheus.io/docs/guides/node-exporter/
3. SNMP指标采集最佳实践：https://docs.influxdata.com/telegraf/v1.34/data_formats/input/snmp/
4. Kafka生产者配置指南：https://docs.confluent.io/platform/current/clients/producer.html 