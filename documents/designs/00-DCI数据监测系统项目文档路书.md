# DCI 数据监测系统项目文档路书

# 版本记录


| 版本 | 日期       | 作者 | 修订说明             |
| ---- | ---------- | ---- | -------------------- |
| V1.0 | 2025-03-06 | 顾铠羟 | 初始版本         |
| V2.0 | 2025-08-07 | 顾铠羟 | 完成1-25号文档及整体结构更新 |

## 1 文档阅读指南

本路书旨在快速了解 DCI 数据监测系统的背景、需求、整体架构设计和功能实现技术设计。文档按照从需求到实现的逻辑顺序组织，按照以下顺序阅读。

### 1.1 项目背景与需求（基础认知层）

1. [DCI 项目网络环境说明](00-01-DCI项目网络环境说明.md)
   - 了解项目的网络环境背景
   - 理解 DCI 在整体解决方案中的定位

2. [数据监测系统需求分析](00-02-DCI-数据监测系统需求分析.md)
   - 核心目标：业务网络状态监测、自动化任务协同监控、数据可视化
   - 数据源分析：SNMP、Syslog、sFlow/NetFlow、Telemetry 等
   - 数据库选型考量：Prometheus/Thanos、Elasticsearch、MySQL 的混合使用

### 1.2 技术架构设计（架构认知层）"重要"

1. [**项目目录及技术栈约束指南**](01-DCI-数据监测系统-项目目录及技术栈约束指南.md)
   - **项目核心目录结构说明**（documents、dci-monitor、dci_self_test）
   - **各目录职责与用途**（设计文档、代码开发、本地自测环境）
   - **技术选型依据与版本约束**
   - **代码组织规范与开发标准**
   - **此文档是所有开发和设计工作的基础参考**

2. [技术概要设计](02-网络自动化平台-数据监测系统技术概要设计.md)
   - 系统整体架构
   - 核心模块职责
   - 数据流转路径

3. [Prometheus 和 Thanos 架构设计](07-DCI-Prometheus和Thanos架构设计.md)
   - 监控数据存储方案
   - 高可用性设计

4. [网络自动化任务协同监控技术方案设计](17-DCI-网络自动化任务协同监控技术方案设计.md)
   - 任务信号接收与处理机制
   - 任务执行监控流程
   - 任务报告生成与分发
   - 与网络自动化系统的集成接口

### 1.3 核心功能实现（实现认知层）

1. [拓扑数据技术设计](03-dci_拓扑数据技术设计.md)
   - 网络拓扑数据模型
   - 拓扑数据采集和更新机制

2. [指标数据基线监测及告警技术方案](04-指标数据基线监测及告警技术方案设计.md)
   - 基线计算方法
   - 告警规则设计
   - 告警通知机制

3. [异常告警检测技术方案设计](24-DCI-异常告警检测技术方案设计.md)
   - 日志类数据告警机制
   - 统一告警管理
   - 复杂事件关联分析

4. [流量统计及监测算法](05-流量统计及流量速率监测算法及技术方案.md)
   - 流量数据采集方案
   - 统计算法实现
   - 性能优化策略

5. [Agent客户端技术方案](#dciagent模块技术方案组)
   - Agent 架构设计
   - 数据采集实现
   - 配置管理机制
   - 部署与安装流程

6. [设备端口流量查询服务设计方案](18-DCI-设备端口流量查询服务设计方案.md)
   - A-Z链路及单端口流量查询
   - 后端Prometheus真实数据对接
   - VNI参数支持

### 1.4 告警管理与联动（告警认知层）

1. [Prometheus指标类数据AlertManager技术方案设计](24-01-DCI-prometheus指标类数据AlertManager技术方案设计.md)
   - AlertManager集成架构
   - 告警规则管理
   - 告警通知路由配置

2. [统一告警模型设计](24-02-DCI-统一告警模型设计.md)
   - 统一告警数据模型
   - 多源告警统一处理
   - 告警生命周期管理

3. [Prometheus类告警规则模块技术设计](24-03-DCI-prometheus类告警规则模块技术设计.md)
   - 告警规则动态管理
   - 规则模板化设计
   - 规则验证与测试

4. [Prometheus类告警模块API设计](24-04-DCI-prometheus类告警模块API设计.md)
   - 告警管理API接口
   - 告警查询与统计
   - 告警确认与处理

5. [告警联动技术方案设计框架](25-DCI-告警联动技术方案设计框架.md)
   - 多源告警统一管理
   - 自动化任务协同监控架构
   - 告警联动整体框架

6. [网络控制-Prometheus指标类告警联动技术方案设计](25-01-DCI-网络控制-prometheus指标类告警联动技术方案设计.md)
   - 网络自动化任务与Prometheus指标告警深度集成
   - 任务级别监控增强策略
   - 告警联动配置与实施

### 1.5 数据管理与集成（数据认知层）

1. [Prometheus 指标和标签规范](08-DCI-Prometheus指标和标签规范.md)
   - 指标命名规范
   - 标签使用规则

2. [数据保留与降采样策略](09-DCI-Prometheus数据保留与降采样策略.md)
   - 数据生命周期管理
   - 存储优化策略

3. [查询接口适配层设计](10-DCI-Prometheus查询接口适配层设计.md)
   - API 设计
   - 查询优化

4. [MySQL数据库设计](12-DCI数据监测系统-MySQL数据库设计.md)
   - 数据模型设计
   - 表结构定义
   - 索引优化

5. [流量类数据接收及存储技术设计](19-DCI-流量类数据接收及存储技术设计.md)
   - Kafka到Prometheus的数据链路
   - 设备端口VNI映射
   - 原始计数器存储与流速计算

6. [流量类数据Prometheus指标设计](19-01-DCI-流量类数据prometheus指标设计.md)
   - 流量指标命名与标签体系
   - PromQL查询模式与示例

7. [流量类数据Prometheus指标字段说明](19-02-流量类数据prometheus指标字段说明.md)
   - 核心指标详细说明
   - 标签字段含义解释
   - 查询索引与映射关系

8. [MySql数据库同步-设备端口VNI基础信息](20-DCI-MySql数据库同步-设备端口VNI基础信息.md)
   - `dci`与`dci_monitor`数据库同步机制
   - 解决FlowData服务依赖的基础数据问题

9. [Mysql设备-端口-逻辑端口-VNI关联关系](20-01-DCI-Mysql设备-端口-逻辑端口-VNI关联关系.md)
   - 设备端口VNI的数据库关联模型
   - 多表关联查询设计

### 1.6 设备状态监测（监测认知层）

1. [交换机状态监测技术设计](22-DCI-交换机状态监测技术设计.md)
   - 交换机CPU、内存、接口状态等指标的采集与处理
   - 状态类数据的Prometheus指标设计

2. [Prometheus-SNMP接口状态监测服务技术设计](23-DCI-prometheus-snmp接口状态监测服务技术设计.md)
   - 实现基于SNMP的交换机接口状态的实时监测服务
   - 定义接口状态的Prometheus指标和查询API

3. [交换机接口状态查询API简介](23-01-交换机接口状态查询API简介.md)
   - 提供外部系统查询交换机接口状态的API接口规范

### 1.7 部署与集成（运维认知层）

1. [dciagent客户端技术方案](#dciagent模块技术方案组)
   - 客户端组件基于操作系统直接安装的部署架构设计
   - 目录结构与文件规划
   - 安装、配置与集成流程
   - 备份与恢复设计
   - 此系列文档为部署架构设计的主要参考文档

2. [Telegraf-SNMP 采集实现方案](11-DCI-Telegraf-SNMP采集实现方案.md)
   - SNMP 采集配置
   - 性能优化建议
   - 部署方式以 15-02 号文档为参考标准

3. [Kafka 主题设计与规划](13-DCI-Kafka主题规划及负载均衡连接设计.md)
   - 消息队列架构
   - 主题划分策略

4. [Kafka 安全配置技术方案](21-DCI-kafka安全配置技术方案.md)
   - Kafka集群的认证、授权和加密配置
   - TLS/SSL证书配置与管理
   - SASL认证机制实现

5. [Telegraf 数据采集方案](14-DCI-dciagent中Telegraf数据采集方案设计.md)
   - 采集配置管理
   - 数据处理流程
   - 部署架构和操作流程参考 15-02 号文档

6. [dciagent 模块版本与 SNMP 采集对象关系设计](16-DCI-dciagent模块版本与采集模块关系设计.md)
   - 模块版本管理
   - SNMP 采集对象关联机制

## 2 文档依赖关系

**注意**：图中的依赖箭头表示"依赖于"关系，即箭头指向的文档是被依赖的基础文档。

### 2.1 核心依赖链

```mermaid
graph TD
    A[00-DCI数据监测系统项目文档路书] --> B0[00-02-DCI-数据监测系统需求分析]
    B0 --> B[01-DCI-数据监测系统-项目目录及技术栈约束指南]
    
    B --> C[02-网络自动化平台-数据监测系统技术概要设计]

    C --> D1[15-01-DCI-dciagent客户端架构设计]
    D1 --> D2[15-02-DCI-dciagent部署与配置设计]
    D2 --> D3[15-03-DCI-dciagent与服务端集成设计]
    D3 --> D4[15-04-DCI-dciagent数据流向设计]
    D4 --> D5[15-05-DCI-dciagent运维与监控设计]
    D5 --> D6[15-06-DCI-dciagent安全与测试设计]
    
    F --> K[03-dci_拓扑数据技术设计]
    
    C --> R[06-dcimonitor服务端技术方案设计]
    R --> Q[17-DCI-网络自动化任务协同监控技术方案设计]

    C --> H[12-DCI数据监测系统-MySQL数据库设计]
    C --> O[13-DCI-Kafka主题规划及负载均衡连接设计]

    C --> N[07-DCI-Prometheus和Thanos架构设计]
    N --> E[08-DCI-Prometheus指标和标签规范]
    E --> I[09-DCI-Prometheus数据保留与降采样策略]
    E --> J[10-DCI-Prometheus查询接口适配层设计]

    D1 --> G[14-DCI-dciagent中Telegraf数据采集方案设计]
    G --> F[11-DCI-Telegraf-SNMP采集实现方案]
    D1 --> P[16-DCI-dciagent模块版本与采集模块关系设计]
    F --> L[04-指标数据基线监测及告警技术方案设计]
    F --> M[05-流量统计及流量速率监测算法及技术方案]

    %% 流量相关文档的依赖关系
    R --> Doc18[18-设备端口流量查询服务设计方案]
    Doc18 --> Doc19[19-流量类数据接收及存储技术设计]
    Doc19 --> Doc1901[19-01-流量类数据prometheus指标设计]
    Doc19 --> Doc1902[19-02-流量类数据prometheus指标字段说明]
    Doc19 --> Doc20[20-MySql数据库同步-设备端口VNI基础信息]
    Doc20 --> Doc2001[20-01-Mysql设备-端口-逻辑端口-VNI关联关系]
    
    %% 状态监测相关文档的依赖关系
    R --> Doc22[22-DCI-交换机状态监测技术设计]
    Doc22 --> Doc23[23-DCI-prometheus-snmp接口状态监测服务技术设计]
    Doc23 --> Doc2301[23-01-交换机接口状态查询API简介]

    %% 告警相关文档的依赖关系
    R --> Doc2401[24-01-DCI-prometheus指标类数据AlertManager技术方案设计]
    Doc2401 --> Doc2402[24-02-DCI-统一告警模型设计]
    Doc2402 --> Doc2403[24-03-DCI-prometheus类告警规则模块技术设计]
    Doc2403 --> Doc2404[24-04-DCI-prometheus类告警模块API设计]
    Doc2402 --> Doc25[25-DCI-告警联动技术方案设计框架]
    Doc25 --> Doc2501[25-01-DCI-网络控制-prometheus指标类告警联动技术方案设计]
    
    %% 异常告警检测
    R --> Doc24[24-DCI-异常告警检测技术方案设计]

    %% Kafka安全配置
    O --> Doc21[21-Kafka安全配置技术方案]

```

### 2.2 数据库设计依赖 

系统使用多种数据库技术存储不同类型的数据，各功能模块对数据库的依赖关系如下：

```mermaid
graph TD
    %% 核心文档
    A[00-DCI数据监测系统项目文档路书] --> B0[01-DCI-数据监测系统-项目目录及技术栈约束指南]
    B0 --> B[00-02-DCI-数据监测系统需求分析]
    
    %% 数据库设计文档
         H[12-DCI数据监测系统-MySQL数据库设计]
         E[08-DCI-Prometheus指标和标签规范]
         ES[Elasticsearch存储设计]
         KF[13-DCI-Kafka主题规划及负载均衡连接设计]
    
    %% 需求分析到数据库设计的依赖
    B --> H
    B --> ES
    B --> E
    B --> KF
    
    %% 功能模块
        K[03-拓扑数据技术设计]
        L[04-指标数据基线监测及告警技术方案]
        M[05-流量统计及监测算法]
        D1[15-01-dciagent客户端架构设计]
        D2[15-02-dciagent部署与配置设计]
        D3[15-03-dciagent与服务端集成设计]
        D4[15-04-dciagent数据流向设计]
        Q[17-网络自动化任务协同监控]
    
    %% 功能模块对数据库的依赖关系
    %% 拓扑数据模块依赖
    H --> K
    KF --> K
    
    %% 告警模块依赖
    H --> L
    E --> L
    
    %% 流量统计模块依赖
    E --> M
    
    %% Agent部署模块依赖
    H --> D1
    H --> D2
    
    %% Agent与Kafka的依赖关系
    KF --> D3
    KF --> D4
    
    %% 任务协同监控模块依赖
    H --> Q
    E --> Q
    ES --> Q
    KF --> Q

    %% 新增流量相关模块
    Doc18[18-设备端口流量查询]
    Doc19[19-流量接收存储]
    Doc1901[19-01-流量指标设计]
    Doc1902[19-02-流量指标字段说明]
    Doc20[20-MySQL同步]
    Doc2001[20-01-设备端口VNI关联关系]
    Doc21[21-Kafka安全配置]
    Doc22[22-交换机状态监测]
    Doc23[23-prometheus-snmp接口状态监测服务]
    Doc2301[23-01-交换机接口状态查询API]
    
    %% 告警联动相关模块
    Doc2401[24-01-AlertManager技术方案]
    Doc2402[24-02-统一告警模型]
    Doc2403[24-03-告警规则模块]
    Doc2404[24-04-告警模块API]
    Doc24[24-异常告警检测]
    Doc25[25-告警联动框架]
    Doc2501[25-01-prometheus指标告警联动]

    %% 新增模块的数据库依赖
    H --> Doc18
    E --> Doc18

    H --> Doc19
    E --> Doc19
    KF --> Doc19
    
    E --> Doc1901
    E --> Doc1902

    H --> Doc20
    KF --> Doc20
    H --> Doc2001

    KF --> Doc21

    H --> Doc22
    E --> Doc22
    KF --> Doc22

    H --> Doc23
    E --> Doc23
    KF --> Doc23
    Doc23 --> Doc2301
    
    %% 告警模块数据库依赖
    H --> Doc2401
    E --> Doc2401
    
    H --> Doc2402
    E --> Doc2402
    
    H --> Doc2403
    E --> Doc2403
    
    H --> Doc2404
    E --> Doc2404
    
    H --> Doc24
    E --> Doc24
    ES --> Doc24
    KF --> Doc24
    
    H --> Doc25
    E --> Doc25
    ES --> Doc25
    KF --> Doc25
    
    H --> Doc2501
    E --> Doc2501
```

**数据库依赖说明**：

1. **MySQL数据库(12-DCI数据监测系统-MySQL数据库设计)**
   - 拓扑数据模块：存储设备信息、端口信息、拓扑关系等结构化数据
   - 告警模块：存储告警规则配置、告警历史记录、通知策略等
   - Agent部署模块：存储Agent注册信息、配置模板、部署状态等
   - 任务协同监控模块：存储任务元数据、执行状态、关联信息等
   - 数据同步模块 (20)：作为数据同步的源和目标，并存储同步状态
   - 流量接收存储模块 (19)：查询设备、端口、VNI的映射关系
   - 设备端口流量查询模块 (18)：查询设备、端口、VNI的映射关系以验证参数
   - 交换机状态监测模块 (22)：查询设备、端口的映射关系
   - 告警联动模块 (24-25系列)：存储任务监控会话、告警关联记录、告警规则等

2. **Prometheus/Thanos(07/08文档)**
   - 告警模块：存储指标数据、提供告警规则评估引擎
   - 流量统计模块：存储流量时序数据、提供查询和聚合能力
   - 任务协同监控模块：存储任务执行期间的性能指标数据
   - 流量接收存储模块 (19)：存储流量指标时序数据
   - 设备端口流量查询模块 (18)：查询流量指标，计算速率和总流量
   - 交换机状态监测模块 (22)：存储设备状态指标时序数据
   - 告警联动模块 (24-25系列)：存储告警指标、提供告警规则评估

3. **Elasticsearch**
   - 任务协同监控模块：存储任务相关日志、事件数据，支持全文检索
   - 流量分析模块：存储NetFlow/sFlow流记录（可选）
   - 告警联动模块：存储日志类告警数据，支持复杂事件关联分析

4. **Kafka消息队列(13-DCI-Kafka主题规划及负载均衡连接设计)**
   - Agent数据流向：作为数据传输的中间层，连接客户端和服务端
   - Agent与服务端集成：提供注册、心跳和命令下发的通信通道
   - 任务协同监控：接收任务状态变更事件和执行结果
   - 流量接收存储模块 (19)：作为SNMP流量数据的来源
   - 数据同步模块 (20)：消费由Telegraf采集并上报的接口索引信息
   - Kafka安全配置模块 (21)：定义了Kafka的安全访问策略
   - 交换机状态监测模块 (22)：作为SNMP状态数据的来源
   - 告警联动模块：作为告警事件的传输通道

### 2.3 告警联动架构依赖关系

告警联动系统作为DCI监控系统的重要组成部分，其架构依赖关系如下：

```mermaid
graph TD
    %% 基础架构
    Base[17-网络自动化任务协同监控] --> Framework[25-告警联动技术方案设计框架]
    
    %% 告警处理组件
    Framework --> AlertMgr[24-01-AlertManager技术方案]
    Framework --> UnifiedModel[24-02-统一告警模型]
    UnifiedModel --> RuleModule[24-03-告警规则模块]
    RuleModule --> AlertAPI[24-04-告警模块API]
    
    %% 具体实现
    Framework --> PrometheusIntegration[25-01-prometheus指标告警联动]
    Framework --> AnomalyDetection[24-异常告警检测]
    
    %% 数据支撑
    UnifiedModel --> MySQL[(MySQL数据库)]
    UnifiedModel --> Prometheus[(Prometheus)]
    AnomalyDetection --> Elasticsearch[(Elasticsearch)]
    
    %% 与其他系统集成
    PrometheusIntegration --> TaskMonitoring[任务协同监控]
    PrometheusIntegration --> NetworkAutomation[网络自动化系统]
    
    classDef database fill:#e1f5fe
    classDef framework fill:#f3e5f5
    classDef implementation fill:#e8f5e8
    
    class MySQL,Prometheus,Elasticsearch database
    class Framework,UnifiedModel framework
    class PrometheusIntegration,AnomalyDetection implementation
```

### 2.4 文档更新指南

文档更新时，遵循以下原则：

1. **向上同步：** 修改涉及实现细节时，检查是否需要更新上层架构文档
2. **向下同步：** 修改涉及架构调整时，相关的实现文档同步更新
3. **横向同步：** 修改涉及多个模块的交互时，相关模块的文档更新
4. **以最新为准：** 若存在文档内容不一致，以**版本日期更新的文档**为准，并同步更新其他相关文档

#### 更新检查清单

- [ ] 检查修改是否影响系统架构
- [ ] 检查修改是否影响接口定义
- [ ] 检查修改是否影响数据模型
- [ ] 检查修改是否影响部署架构（重点关注 15 号文档）
- [ ] 检查相关文档的版本号是否需要更新
- [ ] 在文档中标注修改历史

## 3 最佳实践

1. **循序渐进：** 按照路书顺序阅读，确保对系统有完整认知
2. **关注依赖：** 理解文档间的依赖关系，确保修改时同步更新相关文档
3. **实践验证：** 结合代码实现验证文档内容，确保文档与代码一致
4. **持续更新：** 定期检查文档时效性，及时更新过时内容
5. **优先参考最新文档：** 对于相同主题有多个文档时，以最新版本日期的文档为准

## 4 常见问题

1. **Q: 如何快速了解系统架构？**
   A: 优先阅读技术概要设计文档，结合架构图理解系统整体结构

2. **Q: 如何理解数据流转过程？**
   A: 参考技术概要设计中的数据流图，结合 Kafka 主题设计文档

3. **Q: 如何进行新功能开发？**
   A: 先理解需求分析文档，然后参考相关模块的详细设计文档

4. **Q: 如何处理文档不一致？**
   A: 以最新版本日期的文档为准，并按照文档依赖关系，从上到下检查并更新相关文档

5. **Q: 为什么客户端部署方式与部分文档描述不符？**
   A: 系统设计经过迭代优化，最新确定采用操作系统直接安装方式部署客户端，而非 K8S/Docker。以 15 号文档《Telegraf 部署架构与流程》的描述为准。

6. **Q: 告警联动功能如何与现有系统集成？**
   A: 告警联动基于现有的17号任务协同监控架构，通过24-25系列文档提供的统一告警模型和联动框架实现，无需大幅修改现有系统。

## 5 文档维护

1. **版本控制：** 所有文档都有版本号和修改历史
2. **评审流程：** 重要文档的更新经过技术评审
3. **反馈机制：** 文档改进建议渠道畅通
4. **定期审查：** 每季度审查文档时效性，确保内容准确性
5. **文档一致性：** 对于相同主题的多个文档，内容保持一致，内容冲突时以版本日期最新的文档为准

## 6 注意事项

1. 文档内容与代码实现保持同步
2. 重要的架构决策记录决策理由
3. 文档更新同步到相关团队
4. 保持文档的简洁性和可维护性
5. 针对部署架构，参考 15-02 号文档《15-02-DCI-dciagent部署与配置设计.md》，包含最新的部署设计和运维流程

## 7 快速入门指南

### 7.1 阅读路径推荐

根据不同目的，推荐以下阅读路径：

1. **整体架构了解**：00 → 01(目录及技术栈) → 00-02 → 02 → 07
2. **开发告警功能**：00 → 01(目录及技术栈) → 00-02 → 02 → 04 → 24-01 → 24-02 → 25
3. **开发告警联动功能**：00 → 01 → 02 → 17 → 25 → 25-01
4. **开发数据采集**：00 → 01(目录及技术栈) → 00-02 → 02 → 15-01 → 15-02 → 11/14
5. **开发任务协同监控**：00 → 01(目录及技术栈) → 00-02 → 02 → 17
6. **开发流量查询服务**：00 → 01 → 02 → 18 → 19 → 19-01 → 19-02
7. **开发设备状态监测**：00 → 01 → 02 → 22 → 23 → 23-01

### 7.2 重要技术栈变更说明

1. **[重要] 系统已从TDEngine迁移至Prometheus/Thanos作为时序数据库**
   - 文档02和17已更新反映此变更
   - 文档13(TDengine存储设计)仅作为历史参考，不再适用于当前系统

2. **[重要] 客户端部署方式确定为操作系统直接安装，而非容器化部署**
   - 文档15包含最新的部署架构设计

3. **[新增] 告警联动系统已完成设计**
   - 24-25系列文档提供完整的告警联动技术方案
   - 基于现有架构扩展，保持系统一致性

### 7.3 系统技术栈概览

- **数据采集**：dciagent，含 Telegraf  采集模块
- **消息队列**：Kafka
- **时序数据库**：Prometheus/Thanos (替代原TDEngine)
- **日志存储**：Elasticsearch
- **元数据存储**：MySQL
- **API框架**：Go语言的Gin框架
- **前端框架**：Vue.js
- **告警管理**：AlertManager + 统一告警模型

### 7.4 术语表

| 术语 | 描述 |
|-----|------|
| DCI | 数据中心互联(Data Center Interconnect)，本项目的核心网络环境 |
| Agent | 部署在被监控设备上的数据采集组件，基于Telegraf实现 |
| Prometheus | 开源的时序数据库和监控系统，用于存储和查询指标数据 |
| Thanos | Prometheus的扩展组件，提供全局查询视图、长期存储和高可用性 |
| SNMP | 简单网络管理协议，用于网络设备管理和监控 |
| LLDP | 链路层发现协议，用于获取网络拓扑信息 |
| Syslog | 系统日志协议，用于收集设备日志 |
| NetFlow/sFlow | 网络流量监控协议，用于收集流量统计数据 |
| Telemetry | 流式遥测，提供高频率的指标和状态数据推送 |
| AlertManager | Prometheus的告警管理组件，处理告警路由和通知 |
| VNI | Virtual Network Identifier，虚拟网络标识符 |
| 告警联动 | 网络自动化任务与告警系统的协同监控机制 |

## 8 dciagent模块技术方案组

dciagent客户端技术方案已拆分为以下六个独立文档，共同构成完整的Agent端技术方案：

1. [15-01-DCI-dciagent客户端架构设计](15-01-DCI-dciagent客户端架构设计.md) - 详述客户端整体架构设计和核心组件
2. [15-02-DCI-dciagent部署与配置设计](15-02-DCI-dciagent部署与配置设计.md) - 详述部署架构、目录结构和配置流程
3. [15-03-DCI-dciagent与服务端集成设计](15-03-DCI-dciagent与服务端集成设计.md) - 详述与服务端的集成机制和API交互
4. [15-04-DCI-dciagent数据流向设计](15-04-DCI-dciagent数据流向设计.md) - 详述数据采集、处理和传输流程
5. [15-05-DCI-dciagent运维与监控设计](15-05-DCI-dciagent运维与监控设计.md) - 详述运维监控机制和管理策略
6. [15-06-DCI-dciagent安全与测试设计](15-06-DCI-dciagent安全与测试设计.md) - 详述安全机制和测试验证方案

这些文档共同涵盖了原《15-DCI-dciagent客户端技术方案设计.md》的所有内容，并按技术模块进行了更清晰的拆分。