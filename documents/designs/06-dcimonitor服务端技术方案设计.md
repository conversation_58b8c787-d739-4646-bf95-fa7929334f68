---
title: |
  dcimonitor服务端技术方案设计

subtitle: |
  DCI数据监测系统服务端架构与实现方案
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-06-23 | 顾铠羟 | 初始版本           |

# 1 文档介绍

## 1.1 文档目的

本文档详细描述 dcimonitor 服务端的技术方案设计，包括架构设计、模块划分、数据流向和接口设计。文档旨在为开发团队提供清晰的技术指导，确保服务端开发符合整体系统设计要求，并与其他组件高效协同工作。

## 1.2 文档范围

本文档涵盖 dcimonitor 服务端的整体架构设计、核心模块功能、数据处理流程、API 接口设计以及与其他系统组件的集成方式。不包括客户端（dciagent）的具体实现细节和前端界面设计。

## 1.3 文档关联

根据《00-DCI数据监测系统项目文档路书.md》，本文档是 DCI 监测系统核心设计文档之一，与以下文档密切关联：

- 上游依赖：《02-网络自动化平台-数据监测系统技术概要设计.md》
- 下游文档：《%待设计%-异常告警检测技术方案设计.md》、《17-DCI-网络自动化任务协同监控技术方案设计.md》、《18-DCI-设备端口流量查询服务设计方案.md》、《22-DCI-交换机状态监测技术设计.md》
- 横向关联：《12-DCI数据监测系统-MySQL数据库设计.md》、《13-DCI-Kafka主题规划及负载均衡连接设计.md》、《07-DCI-Prometheus和Thanos架构设计.md》

# 2 总体设计

## 2.1 设计目标

dcimonitor 服务端作为 DCI 数据监测系统的核心组件，设计目标包括：

1. 构建高可用、可扩展的微服务架构，支持系统功能模块独立部署和扩展
2. 提供统一的 API 层，为前端和第三方系统提供标准化的数据访问接口
3. 实现对网络设备的实时状态监控、流量数据分析和异常检测
4. 支持与 Prometheus、MySQL、Kafka 等多种数据源和存储系统的高效集成
5. 确保系统具备良好的可观测性，包括日志记录、监控指标和追踪能力
6. 提供灵活的配置管理机制，支持不同环境下的系统参数调整

## 2.2 架构设计

dcimonitor 服务端采用基于 Go 语言的微服务架构，各功能模块通过 RESTful API 和消息队列进行通信。系统部署在 Kubernetes 集群中，确保高可用性和可扩展性。

### 2.2.1 详细架构图

```mermaid
graph TD
    Client["客户端/前端"] --> |HTTP| APIGateway["API Gateway"]
    APIGateway --> |HTTP| CoreServices["核心服务集群"]
    
    subgraph "dcimonitor 服务端"
        CoreServices --> SwitchMonitor["交换机状态监控服务"]
        CoreServices --> TrafficMonitor["流量监控服务"]
        CoreServices --> SinglePortMonitor["单端口监控服务"]
        CoreServices --> TopologyService["拓扑服务"]
        CoreServices --> TaskMonitor["任务协同监控"]
        
        SwitchMonitor --> |查询| DAOLayer["数据访问层"]
        TrafficMonitor --> |查询| DAOLayer
        SinglePortMonitor --> |查询| DAOLayer
        TopologyService --> |查询| DAOLayer
        TaskMonitor --> |查询| DAOLayer
        
        DAOLayer --> PortMappingDAO["端口映射DAO"]
        DAOLayer --> TrafficDAO["流量数据DAO"]
        DAOLayer --> InterfaceStatusDAO["接口状态DAO"]
        DAOLayer --> IdentifierService["标识符服务"]
    end
    
    PortMappingDAO --> |SQL| MySQL[(MySQL)]
    TrafficDAO --> |PromQL| Prometheus[(Prometheus)]
    InterfaceStatusDAO --> |PromQL| Prometheus
    IdentifierService --> |SQL| MySQL
    
    Kafka[(Kafka)] --> |消费| dcimonitor
    dciagent["dciagent"] --> |生产| Kafka
```

### 2.2.2 极简架构图

```mermaid
graph LR
    Client["客户端"] --> |HTTP| API["API层"]
    API --> Services["服务层"]
    Services --> DAO["数据访问层"]
    DAO --> DataSources["数据源<br>(MySQL/Prometheus/Kafka)"]
```

## 2.3 数据流/流程图

dcimonitor 服务端的主要数据流包括设备状态监控、流量数据查询和拓扑数据处理。以设备状态查询为例：

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as API层
    participant Monitor as 监控服务
    participant DAO as 数据访问层
    participant Prometheus as Prometheus
    
    Client->>API: 请求设备状态
    activate API
    API->>Monitor: 转发请求
    activate Monitor
    Monitor->>DAO: 查询设备数据
    activate DAO
    DAO->>Prometheus: 执行PromQL查询
    activate Prometheus
    Prometheus-->>DAO: 返回查询结果
    deactivate Prometheus
    DAO-->>Monitor: 返回设备数据
    deactivate DAO
    Monitor-->>API: 返回处理结果
    deactivate Monitor
    API-->>Client: 返回设备状态
    deactivate API
```

## 2.4 模块化设计

dcimonitor 服务端由以下主要模块组成：

1. **API 层**：基于 Gin 框架实现的 RESTful API，处理 HTTP 请求路由和响应
2. **服务层**：实现核心业务逻辑，包括交换机监控、流量分析、拓扑管理等功能模块
3. **数据访问层**：封装对各种数据源的访问，提供统一的数据操作接口
4. **公共组件**：包括日志记录、配置管理、认证授权等跨功能模块

各模块作为独立的包进行开发，通过接口进行解耦，便于单元测试和功能扩展。

## 2.5 技术选型

dcimonitor 服务端采用以下技术栈：

| 技术/组件     | 版本   | 用途                           | 选择理由                                   |
|--------------|--------|-------------------------------|-------------------------------------------|
| Go           | 1.21+  | 后端开发语言                    | 高性能、并发支持好、适合微服务开发           |
| Gin          | 1.9.0  | Web 框架                       | 轻量级、高性能、API 友好                    |
| Cobra        | 1.7.0  | 命令行框架                     | 支持复杂命令行应用，易于构建子命令           |
| Prometheus   | 2.45.0 | 时序数据存储与查询              | 高性能时序数据库，适合监控指标存储           |
| MySQL        | 8.0    | 关系型数据库                   | 存储结构化数据，如设备信息、端口映射关系      |
| Kafka        | 3.4.0  | 消息队列                       | 高吞吐量、可靠的消息传递，适合数据流处理      |
| Zap          | 1.24.0 | 日志框架                       | 高性能、结构化日志，支持多输出目标           |
| Viper        | 1.15.0 | 配置管理                       | 支持多种配置源，动态配置更新                 |
| Swagger      | 2.0    | API 文档                       | 自动生成 API 文档，便于接口调试              |

# 3 详细设计

## 3.1 功能模块

### 3.1.1 命令行框架

命令行框架基于 Cobra 实现，提供统一的命令行接口，支持多种子命令和选项。主要包括：

- **root 命令**：提供基础配置和全局标志
- **server 子命令**：启动 API 服务器
- **kafka 子命令**：处理 Kafka 相关操作
- **topology 子命令**：处理拓扑数据相关操作

命令行框架的工作流程如下：

```mermaid
sequenceDiagram
    participant User as 用户
    participant Root as rootCmd
    participant Server as serverCmd
    participant Config as 配置管理
    participant Logger as 日志系统
    
    User->>Root: 执行命令
    activate Root
    Root->>Config: 初始化配置
    activate Config
    Config-->>Root: 配置加载完成
    deactivate Config
    
    alt 执行 server 子命令
        Root->>Server: 执行 server 命令
        activate Server
        Server->>Logger: 初始化日志
        Server->>Server: 初始化服务依赖
        Server->>Server: 启动 HTTP 服务器
        Server-->>Root: 服务启动完成
        deactivate Server
    else 执行其他子命令
        Root->>Root: 处理其他子命令
    end
    
    Root-->>User: 命令执行结果
    deactivate Root
```

### 3.1.2 API 服务器

API 服务器基于 Gin 框架实现，提供 RESTful API 接口，支持设备状态查询、流量监控、拓扑数据访问等功能。主要特点：

- 支持路由分组，便于 API 版本管理
- 集成中间件，提供日志记录、错误恢复、认证授权等功能
- 支持 Swagger 文档自动生成
- 提供健康检查端点，便于监控系统集成

API 服务器的请求处理流程：

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Router as 路由器
    participant Middleware as 中间件链
    participant Handler as 处理器
    participant Service as 服务层
    
    Client->>Router: HTTP请求
    activate Router
    Router->>Middleware: 路由匹配
    activate Middleware
    Middleware->>Handler: 请求预处理
    activate Handler
    Handler->>Service: 调用业务逻辑
    activate Service
    Service-->>Handler: 返回处理结果
    deactivate Service
    Handler-->>Middleware: 响应处理
    deactivate Handler
    Middleware-->>Router: 后处理
    deactivate Middleware
    Router-->>Client: HTTP响应
    deactivate Router
```

### 3.1.3 监控服务模块

监控服务模块是 dcimonitor 的核心功能，分为以下子模块：

#### ******* 交换机状态监控 (SwitchMonitor)

负责获取交换机的整体状态和端口详细信息，主要接口：

- `GetSwitchStatus`：获取交换机整体状态
- `GetPortData`：获取端口详细数据
- `GetPortAdminStatus`：获取端口管理状态

#### ******* 流量监控 (TrafficMonitor)

负责 A-Z 链路的流量数据查询和分析，主要接口：

- `GetAverageTrafficChart`：获取平均流量图表数据
- `GetMaximumTrafficChart`：获取最大流量图表数据
- `GetMinimumTrafficChart`：获取最小流量图表数据
- `GetTrafficSummary`：获取流量摘要信息

#### ******* 单端口监控 (SinglePortMonitor)

负责单个端口的流量数据查询，主要接口：

- `GetPortFlow`：获取端口实时流量数据
- `GetPortFlowHistory`：获取端口历史流量数据

监控服务的数据获取流程：

```mermaid
sequenceDiagram
    participant Monitor as 监控服务
    participant Store as 数据存储服务
    participant DAO as 数据访问层
    participant Prometheus as Prometheus
    
    Monitor->>Store: 请求设备/端口数据
    activate Store
    Store->>DAO: 查询数据
    activate DAO
    
    par 查询接口状态
        DAO->>Prometheus: 查询状态指标
        Prometheus-->>DAO: 返回状态数据
    and 查询流量数据
        DAO->>Prometheus: 查询流量指标
        Prometheus-->>DAO: 返回流量数据
    end
    
    DAO-->>Store: 合并查询结果
    deactivate DAO
    Store-->>Monitor: 返回完整数据
    deactivate Store
```

### 3.1.4 数据访问层

数据访问层封装对各种数据源的访问，提供统一的接口，主要组件：

#### ******* 端口映射 DAO (PortMappingDAO)

负责设备端口与逻辑标识符之间的映射关系查询，主要接口：

- `GetDeviceIPForPort`：根据设备 ID、端口 ID 和 VNI 获取设备 IP
- `GetDeviceIPForIfName`：根据设备 ID、接口名称和 VNI 获取设备 IP

#### ******* 流量数据 DAO (TrafficDAO)

负责流量数据的查询和分析，主要接口：

- `QueryRate`：查询速率数据
- `QueryIncrease`：查询增量数据
- `QueryVectorOverTime`：查询时间序列数据

#### ******* 接口状态 DAO (InterfaceStatusDAO)

负责接口状态数据的查询，主要接口：

- `GetInterfaceStatus`：获取接口操作状态
- `GetInterfaceAdminStatus`：获取接口管理状态

#### ******* 标识符服务 (IdentifierService)

负责设备和端口标识符的管理和映射，主要接口：

- `GetDeviceIDByIdentifier`：通过标识符获取设备 ID
- `GetPortIDByIdentifier`：通过标识符获取端口 ID
- `MapLLDPNeighbor`：映射 LLDP 邻居信息

数据访问层的工作流程：

```mermaid
sequenceDiagram
    participant Service as 业务服务
    participant DAO as 数据访问层
    participant Cache as 缓存层
    participant DB as 数据库/Prometheus
    
    Service->>DAO: 请求数据
    activate DAO
    
    alt 缓存命中
        DAO->>Cache: 查询缓存
        activate Cache
        Cache-->>DAO: 返回缓存数据
        deactivate Cache
    else 缓存未命中
        DAO->>DB: 查询原始数据
        activate DB
        DB-->>DAO: 返回查询结果
        deactivate DB
        DAO->>Cache: 更新缓存
    end
    
    DAO-->>Service: 返回处理结果
    deactivate DAO
```

### 3.1.5 公共组件

#### ******* 日志系统

基于 Zap 实现的高性能日志系统，支持多级别日志、结构化日志和多输出目标。主要特点：

- 支持 JSON 格式输出，便于日志采集和分析
- 支持日志轮转，避免日志文件过大
- 提供上下文日志，支持请求级别的日志跟踪

#### ******* 配置管理

基于 Viper 实现的配置管理系统，支持多种配置源和动态配置更新。主要特点：

- 支持 YAML、JSON、环境变量等多种配置源
- 支持配置热重载
- 支持配置参数校验和默认值设置

## 3.2 数据模型

dcimonitor 服务端使用多种数据模型，主要包括：

### 3.2.1 设备和端口模型

```mermaid
classDiagram
    class SwitchInfo {
        +string ID
        +string Name
        +string IP
        +string Community
    }
    
    class SwitchStatus {
        +SwitchInfo Info
        +string Status
        +time.Time LastUpdated
        +[]Port Ports
    }
    
    class Port {
        +string PortID
        +string Name
        +string PhysicalState
        +string ProtocolState
        +int64 InErrors
        +int64 OutErrors
        +string Description
        +PortTraffic Traffic
    }
    
    class PortTraffic {
        +int64 TotalInBytes
        +int64 TotalOutBytes
        +int64 TotalInPkts
        +int64 TotalOutPkts
        +*int64 InRate
        +*int64 OutRate
        +string RateUnit
        +time.Time Timestamp
    }
    
    SwitchStatus "1" *-- "1" SwitchInfo
    SwitchStatus "1" *-- "n" Port
    Port "1" *-- "1" PortTraffic
```

### 3.2.2 流量数据模型

```mermaid
classDiagram
    class TimeValuePair {
        +time.Time Timestamp
        +float64 Value
    }
    
    class TrafficChartData {
        +string Unit
        +[]string Timestamps
        +[]TrafficSeries Series
    }
    
    class TrafficSeries {
        +string Name
        +[]float64 Data
    }
    
    class PortFlowData {
        +string UnitRate
        +string UnitTotal
        +float64 InRate
        +float64 OutRate
        +float64 InTotal
        +float64 OutTotal
    }
    
    TrafficChartData "1" *-- "n" TrafficSeries
```

### 3.2.3 请求和响应模型

```mermaid
classDiagram
    class SinglePortFlowQueryParams {
        +string DeviceID
        +string IfName
        +string VNI
        +time.Time StartTime
        +time.Time EndTime
    }
    
    class AZLinkQueryParams {
        +string Granularity
        +string TimeRange
        +time.Time StartTime
        +time.Time EndTime
        +string ASwitchID
        +string APortIDRaw
        +string ZSwitchID
        +string ZPortIDRaw
        +string VNI
        +string APortID
        +string ZPortID
    }
    
    class PortFlowResponse {
        +string RequestID
        +PortFlowQueryDetails QueryDetails
        +PortFlowData FlowData
    }
    
    class TrafficChartResponse {
        +string RequestID
        +any QueryDetails
        +TrafficChartData ChartData
    }
```

## 3.3 接口设计

dcimonitor 服务端提供 RESTful API 接口，主要分为以下几类：

### 3.3.1 交换机状态接口

| 接口路径 | 方法 | 描述 | 参数 |
|---------|------|------|------|
| `/api/v1/switches/:deviceID` | GET | 获取交换机状态 | deviceID: 设备 ID |
| `/api/v1/switches/:deviceID/ports/:ifName` | GET | 获取端口数据 | deviceID: 设备 ID<br>ifName: 端口名称 (Base64 编码)<br>data: 数据类型 (status/upstream/downstream) |
| `/api/v1/switches/:deviceID/ports/:ifName/admin` | GET | 获取端口管理状态 | deviceID: 设备 ID<br>ifName: 端口名称 (Base64 编码) |
| `/api/v1/switches/:deviceID/ports/:ifName/flow` | GET | 获取端口流量 | deviceID: 设备 ID<br>ifName: 端口名称 (Base64 编码)<br>vni: VNI 标识符<br>start_time: 开始时间<br>end_time: 结束时间 |
| `/api/v1/switches/:deviceID/ports/:ifName/flow/history` | GET | 获取端口历史流量 | deviceID: 设备 ID<br>ifName: 端口名称 (Base64 编码)<br>vni: VNI 标识符<br>start_time: 开始时间<br>end_time: 结束时间<br>step: 步长 |

### 3.3.2 流量监控接口

| 接口路径 | 方法 | 描述 | 参数 |
|---------|------|------|------|
| `/api/v1/traffic/chart/average` | GET | 获取平均流量图表 | a_switch_id: A 端设备 ID<br>a_port_id: A 端端口 ID<br>z_switch_id: Z 端设备 ID<br>z_port_id: Z 端端口 ID<br>granularity: 粒度<br>vni: VNI 标识符 |
| `/api/v1/traffic/chart/maximum` | GET | 获取最大流量图表 | 同上 |
| `/api/v1/traffic/chart/minimum` | GET | 获取最小流量图表 | 同上 |
| `/api/v1/traffic/summary` | GET | 获取流量摘要 | 同上 |

### 3.3.3 健康检查接口

| 接口路径 | 方法 | 描述 | 参数 |
|---------|------|------|------|
| `/health` | GET | 健康检查 | 无 |

# 4 安全设计

dcimonitor 服务端的安全设计包括：

1. **API 安全**：
   - 使用 TLS/SSL 加密 HTTP 通信
   - 实现基于 JWT 的认证授权机制
   - API 路由访问控制

2. **数据安全**：
   - 数据库访问权限控制
   - 敏感信息加密存储
   - 数据传输加密

3. **日志安全**：
   - 敏感信息脱敏
   - 安全事件记录
   - 日志访问控制

4. **配置安全**：
   - 敏感配置项加密存储
   - 配置文件访问权限控制
   - 默认安全配置

# 5 本设计的代码实现文件列表

```
dci-monitor/src/dcimonitor/
├── main.go                      # 程序入口
├── cmd/                         # 命令行相关
│   ├── root.go                  # 根命令
│   ├── server.go                # 服务器命令
│   ├── kafka/                   # Kafka 相关命令
│   └── topology/                # 拓扑相关命令
├── internal/                    # 内部包
│   ├── middleware/              # 中间件
│   │   └── gin_logger.go        # Gin 日志中间件
│   ├── models/                  # 数据模型
│   │   ├── error.go             # 错误模型
│   │   ├── lldp.go              # LLDP 模型
│   │   ├── port_admin.go        # 端口管理模型
│   │   ├── prometheus.go        # Prometheus 数据模型
│   │   ├── request.go           # 请求模型
│   │   ├── response.go          # 响应模型
│   │   └── switch.go            # 交换机模型
│   ├── monitors/                # 监控服务
│   │   ├── single_port_monitor.go # 单端口监控
│   │   ├── switch_monitor.go    # 交换机监控
│   │   └── traffic_monitor.go   # 流量监控
│   └── services/                # 业务服务
│       ├── dao.go               # 数据访问对象
│       ├── identifier_service.go # 标识符服务
│       ├── identifier_test.go   # 标识符服务测试
│       ├── switch_service.go    # 交换机服务
│       └── traffic_service.go   # 流量服务
└── common/                      # 公共组件
    └── logger/                  # 日志组件
        ├── adapter.go           # 日志适配器
        ├── logger.go            # 日志核心
        └── logger_test.go       # 日志测试
```
