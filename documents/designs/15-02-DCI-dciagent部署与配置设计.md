---
title: |
  DCI-dciagent部署与配置设计

subtitle: |
  数据监测系统Agent端部署方案与配置规范
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-21 | 顾铠羟 | 从原《15-DCI-dciagent客户端技术方案设计》拆分而来 |

# 1 文档介绍

## 1.1 文档目的

本文档详细设计DCI数据监测系统中dciagent的部署架构、安装流程、配置管理和权限设置，为系统管理员和运维人员提供部署和配置dciagent的技术指导。文档详细说明了在各种环境中的安装方法、目录结构规划、权限设置以及与其他系统如Kafka的集成方式。

## 1.2 文档范围

本文档涵盖DCI数据监测系统中dciagent部署与配置相关的内容，包括：

1. 部署环境规划（测试环境和生产环境）
2. 目录结构与文件规划
3. 安装与配置流程
4. 权限与安全设置
5. Kafka集成配置
6. 配置文件管理规范

# 2 总体设计

## 2.1 设计目标

dciagent部署与配置设计旨在实现以下目标：

1. 简化部署过程，提高部署效率
2. 建立清晰的目录结构与文件管理规范
3. 确保各环境下的一致性部署体验
4. 优化权限配置，保障系统安全
5. 提供与Kafka等外部系统的标准集成方法
6. 支持自动化部署和批量配置

## 2.2 架构设计

dciagent部署架构遵循"本地安装，集中管理"的原则，在操作系统直接安装部署：

```mermaid
graph TD
    subgraph "服务器托管环境"
        subgraph "主目录结构" 
            Home[/opt/dci/dciagent]
            Bin[/bin - 可执行文件]
            Conf[/conf - 配置文件]
            Logs[/logs - 日志文件]
            Backup[/backup - 备份文件]
            Versions[/versions - 版本管理]
        end
        
        subgraph "系统集成"
            Service[系统服务]
            Systemd[Systemd单元]
            Cron[定时任务]
            Network[网络连接]
        end
    end
    
    subgraph "外部组件"
        KafkaCluster[Kafka集群]
        PromCluster[Prometheus集群]
        DciMonitor[DCI Monitor服务]
    end
    
    Home --> Bin
    Home --> Conf
    Home --> Logs
    Home --> Backup
    Home --> Versions
    
    Service --> Bin
    Systemd --> Service
    
    Network --> KafkaCluster
    Network --> DciMonitor
    Network --> PromCluster
    
    %% 样式定义
    classDef primary fill:#d9f,stroke:#333,stroke-width:1px
    classDef secondary fill:#9cf,stroke:#333,stroke-width:1px
    classDef external fill:#faa,stroke:#333,stroke-width:1px
    
    class Home,Bin,Conf,Logs,Backup,Versions primary
    class Service,Systemd,Network,Cron secondary
    class KafkaCluster,PromCluster,DciMonitor external
```

## 2.3 技术选型

1. **部署方法**：
   - 操作系统原生部署，不依赖容器技术
   - 支持RPM/DEB包管理安装和二进制部署两种方式
   - 统一配置目录结构

2. **配置管理**：
   - 采用TOML格式作为Telegraf配置文件标准
   - 采用YAML格式作为dciagent配置文件标准
   - 使用分层配置目录结构管理不同组件配置

3. **服务管理**：
   - 使用Systemd管理服务生命周期
   - 支持自定义环境变量和限制设置
   - 实现标准化的服务依赖配置

4. **权限管理**：
   - 遵循最小权限原则
   - 使用专用系统用户运行服务
   - 严格控制配置文件和证书权限

5. **集成技术**：
   - 标准化Kafka Topic命名和数据格式
   - 支持TLS加密传输
   - 采用Prometheus兼容的监控指标格式

# 3 详细设计

## 3.1 部署环境规划

### 3.1.1 测试环境部署

测试环境Telegraf部署配置：

1. **规模**：
   - dciagent运行于本地电脑或独立网域中的物理机，采集模块可包含1个Telegraf模块，未来可拓展其他模块。
   - 单区域部署
   - 监控设备数量<100

2. **系统要求**：
   - 支持的操作系统：Linux (CentOS/RHEL 7.x及以上，Ubuntu 18.04及以上，测试机为MacOS)
   - CPU：2核及以上
   - 内存：2GB及以上
   - 存储：15GB可用空间（主目录统一管理所有文件）

3. **特殊配置**：
   - 采集间隔较短（10s-60s）
   - 启用详细日志记录
   - 开启调试选项（根据需要）

### 3.1.2 生产环境部署

生产环境 Telegraf 部署配置：

1. **规模**：
   - dciagent运行于本地电脑或独立网域中的物理机，采集模块可包含1个Telegraf模块，未来可拓展其他模块。
   - 多区域部署
   - 每个实例监控设备数量<200

2. **系统要求**：
   - 支持的操作系统：Linux (CentOS/RHEL 7.x及以上，Ubuntu 18.04及以上)
   - CPU：4核及以上
   - 内存：4GB及以上
   - 存储：40GB可用空间（主目录统一管理所有文件）

3. **特殊配置**：
   - 采集间隔优化（15s-60s）
   - 配置系统资源限制
   - 启用数据缓存机制
   - 启用详细日志记录

### 3.1.3 高可用设计

确保 Telegraf 部署的高可用性，由于dciagent运行于本地电脑或独立网域中的物理机，Telegraf 的高可用待具体设计。

1. **自动恢复**：
   - 配置系统服务或守护进程监控Telegraf进程
   - 实现定期健康检查
   - 建立监控告警机制

## 3.2 目录结构与文件规划

### 3.2.1 标准目录结构

dciagent采用以下标准目录结构，确保系统组件分离和便于管理：

```
/opt/dci/dciagent/                 # 主目录
├── bin/                           # 可执行文件目录
│   ├── dciagent                   # dciagent主程序
│   └── telegraf                   # Telegraf二进制文件（符号链接）
├── conf/                          # 配置文件目录
│   ├── dciagent.yaml              # dciagent主配置文件
│   ├── credentials/               # 凭据和证书目录（权限限制）
│   │   ├── agent.token            # Agent认证令牌
│   │   ├── snmp_credentials.toml  # SNMP凭据配置
│   │   └── tls/                   # TLS证书和密钥
│   ├── telegraf.conf              # Telegraf主配置文件
│   └── telegraf.d/                # Telegraf配置片段目录
│       ├── inputs/                # 输入插件配置
│       ├── outputs/               # 输出插件配置
│       └── processors/            # 处理器配置
├── logs/                          # 日志文件目录
│   ├── dciagent.log               # dciagent日志
│   └── telegraf.log               # Telegraf日志
├── backup/                        # 备份目录
│   └── 20250520-123045/           # 按时间戳组织的备份
├── versions/                      # 版本管理目录
│   └── telegraf/                  # Telegraf版本
│       ├── 1.34.2/                # 特定版本目录
│       └── 1.35.0/                # 另一个版本
└── data/                          # 数据缓存目录
    └── telegraf/                  # Telegraf数据缓存
```

### 3.2.2 配置文件层次结构

配置文件采用层次化结构，确保灵活性和易维护性：

```
/opt/dci/dciagent/conf/
├── dciagent.yaml                  # dciagent主配置文件
├── telegraf.conf                  # Telegraf主配置文件
└── telegraf.d/                    # Telegraf配置片段目录
    ├── inputs/                    # 输入插件配置，按采集类型分类
    │   ├── snmp/                  # SNMP采集配置
    │   │   ├── base.conf          # 基础SNMP配置
    │   │   ├── huawei_s6720.conf  # 特定设备型号配置
    │   │   └── cisco_nexus.conf   # 特定设备型号配置
    │   └── syslog/                # Syslog采集配置
    │       └── base.conf          # 基础Syslog配置
    ├── outputs/                   # 输出插件配置
    │   ├── kafka.conf             # Kafka输出配置
    │   └── prometheus.conf        # Prometheus输出配置
    └── processors/                # 处理器配置
        └── converter.conf         # 数据转换处理器
```

### 3.2.3 权限配置

文件和目录权限配置遵循最小权限原则：

| 路径 | 权限 | 所有者:组 | 说明 |
|------|------|-----------|------|
| /opt/dci/dciagent/ | 0750 | dciagent:dciagent | 主目录，需要运行权限 |
| /opt/dci/dciagent/bin/ | 0750 | dciagent:dciagent | 可执行文件目录 |
| /opt/dci/dciagent/conf/ | 0750 | dciagent:dciagent | 配置目录 |
| /opt/dci/dciagent/conf/credentials/ | 0700 | dciagent:dciagent | 敏感凭据目录，严格限制访问 |
| /opt/dci/dciagent/logs/ | 0750 | dciagent:dciagent | 日志目录 |
| /opt/dci/dciagent/backup/ | 0750 | dciagent:dciagent | 备份目录 |

## 3.3 安装与配置流程

### 3.3.1 Telegraf模块初始化安装流程

通过dciagent子命令将Telegraf安装到指定的主目录，整个安装流程遵循以下步骤：

1. **初始请求**：dciagent上报自身信息（包括系统架构、OS类型、当前版本等）到DCI Monitor服务
2. **下载信息获取**：DCI Monitor根据上报信息分析并返回适合该Agent的Telegraf下载信息（包括版本、下载URL、校验和等）
3. **二进制下载**：dciagent根据返回的信息下载Telegraf二进制文件
4. **完整性验证**：验证下载文件的完整性（校验SHA256等）
5. **目录准备**：创建所需的目录结构（如配置目录、日志目录等）
6. **文件解压与安装**：解压Telegraf包并将二进制文件安装到配置文件所指定位置
7. **权限设置**：设置适当的文件和目录权限
8. **配置模板部署**：部署基础配置模板
9. **启动验证**：验证安装后的Telegraf可以正常启动

```mermaid
sequenceDiagram
    participant User as 操作人
    participant DAgent as dciagent
    participant DMon as DCI Monitor
    participant Repo as 文件仓库

    User->>DAgent: 执行telegraf install命令
    activate DAgent
    
    DAgent->>DAgent: 加载配置文件
    DAgent->>DAgent: 检查安装前提条件
    
    DAgent->>DMon: 上报Agent信息(系统架构/OS/版本)
    activate DMon
    DMon->>DMon: 选择合适的Telegraf版本
    DMon->>DAgent: 返回下载信息(URL/版本/校验和)
    deactivate DMon
    
    DAgent->>Repo: 下载Telegraf二进制包
    activate Repo
    Repo->>DAgent: 返回二进制包
    deactivate Repo
    
    DAgent->>DAgent: 验证包完整性(校验和)
    DAgent->>DAgent: 创建目录结构
    DAgent->>DAgent: 解压并安装二进制文件
    DAgent->>DAgent: 设置文件权限
    DAgent->>DAgent: 部署配置模板
    DAgent->>DAgent: 创建符号链接
    
    DAgent->>DAgent: 验证安装结果
    DAgent->>User: 返回安装结果
    deactivate DAgent
```

### 3.3.2 安装功能实现

安装功能采用命令行接口与核心安装逻辑分离的设计模式。主要组件及功能如下：

- **命令入口(cmd/telegraf/install.go)**：负责解析命令行参数，加载配置，并将控制权交给安装器。
- **安装器(internal/telegraf/install/installer.go)**：实现完整的安装逻辑，协调各个安装步骤。
- **下载管理(internal/telegraf/install/downloader.go)**：负责与DCI Monitor通信获取下载信息，执行下载功能。
- **配置管理(internal/telegraf/config/template.go)**：处理配置模板的读取和应用。
- **权限管理(internal/telegraf/install/permissions.go)**：设置适当的文件和目录权限。

安装器确保在各种环境（先通信再安装/先安装再通信）下均能正确安装Telegraf。

### 3.3.3 配置文件加载

配置文件加载模块负责读取和解析配置文件，支持多种配置源和格式。其主要功能包括：

- **配置文件查找**：按优先级顺序查找配置文件
  1. 命令行指定的配置文件
  2. 环境变量指定的配置文件
  3. 默认位置的配置文件（如/etc/dci/dciagent.yaml）
- **配置默认值管理**：为各配置项设置合理的默认值
- **环境变量覆盖**：支持通过环境变量覆盖配置文件中的设置
- **配置验证**：验证加载的配置有效性和完整性

```mermaid
flowchart TD
    A[开始加载配置] --> B{检查命令行参数}
    B -->|指定了配置文件| C[读取指定文件]
    B -->|未指定配置文件| D{检查环境变量}
    D -->|有环境变量| E[读取环境变量指定文件]
    D -->|无环境变量| F[尝试默认位置]
    F --> G{默认位置存在配置?}
    G -->|是| H[读取默认位置配置]
    G -->|否| I[使用内置默认值]
    C --> J[解析配置]
    E --> J
    H --> J
    I --> J
    J --> K[应用环境变量覆盖]
    K --> L[验证配置]
    L --> M{配置有效?}
    M -->|是| N[返回配置对象]
    M -->|否| O[报告错误]
```

## 3.4 Kafka集成配置

遵循DCI监测系统架构整体设计原则，所有采集模块（包括Telegraf实例）的采集类、日志类通信必须配置为将数据输出到Kafka。

### 3.4.1 Kafka输出配置模板

主目录部署中的Kafka输出配置（/opt/dci/dciagent/conf/telegraf.d/outputs/kafka.conf）：

```toml
# Kafka输出配置
[[outputs.kafka]]
  ## Kafka代理地址列表
  brokers = ["kafka1:9092", "kafka2:9092", "kafka3:9092"]
  
  ## 主题名
  topic = "dci.monitor.v1.defaultchannel.metrics.telegraf"
  
  ## 路由主题
  # 可选: 基于标签路由指标到不同主题
  # topic_suffix = "_system"
  # 可选: 路由选择函数
  # topic = "topic.${tag:device_type}.${tag:metric_type}.telegraf"
  
  ## 压缩算法
  compression_codec = "snappy"
  
  ## 确认级别
  ## 0=无确认, 1=等待本地确认, -1=等待所有确认
  required_acks = 1
  
  ## 数据格式
  data_format = "json"
  
  ## 认证
  # sasl_username = "${KAFKA_USER}"
  # sasl_password = "${KAFKA_PASSWORD}"
  
  ## TLS配置
  # tls_ca = "/opt/dci/dciagent/conf/credentials/kafka_ca.pem"
  # tls_cert = "/opt/dci/dciagent/conf/credentials/kafka_cert.pem"
  # tls_key = "/opt/dci/dciagent/conf/credentials/kafka_key.pem"
  
  ## 重试设置
  max_retry = 3
  
  ## 批处理设置
  batch_size = 1000
  
  ## 保留部分标签用于路由和标识
  tag_keys = ["device_id", "hostname", "agent_id"]
```

### 3.4.2 Prometheus辅助输出配置

对于需要本地监控的场景，可以添加Prometheus输出（但数据流的标准路径仍是通过Kafka）：

```toml
# Prometheus输出配置 (可选的辅助输出)
[[outputs.prometheus_client]]
  ## 监听地址
  listen = ":9273"
  
  ## 路径
  path = "/metrics"
  
  ## 过期时间
  expiration_interval = "60s"
  
  ## 指标前缀
  metric_prefix = "dci_telegraf_"
  
  ## 字符串转换设置
  string_as_label = true
```

### 3.4.3 从Kafka到Prometheus的连接器

在服务端部署Kafka-Prometheus连接器 prometheus-exporter ，从Kafka消费指标数据并转换为Prometheus格式：

```yaml
# 连接器配置示例 (以配置文件形式)
kafka:
  brokers:
    - kafka1:9092
    - kafka2:9092
    - kafka3:9092
  topic: dci.monitor.v1.defaultchannel.metrics.telegraf
  consumer_group: prometheus-exporter
  
prometheus:
  port: 9090
  endpoint: /metrics
  
metrics:
  prefix: dci_monitor_
  default_labels:
    source: telegraf
    environment: production
```

## 3.5 权限与安全设置

### 3.5.1 目录与文件权限

遵循最小权限原则配置Telegraf主目录：

1. **用户和组**：
   - 使用专用的telegraf用户和组
   - telegraf用户不具备登录shell权限（/bin/false）
   - telegraf用户仅属于telegraf组，减少潜在的权限扩展

2. **目录权限**：
   - 主目录：0750 (telegraf用户可读写执行，telegraf组可读执行)
   - 配置目录：0750 (telegraf用户可读写执行，telegraf组可读执行)
   - 凭据目录：0700 (仅telegraf用户可访问)
   - 日志目录：0750 (telegraf用户可读写执行，telegraf组可读执行)
   - 数据目录：0750 (telegraf用户可读写执行，telegraf组可读执行)
   - 备份目录：0750 (telegraf用户可读写执行，telegraf组可读执行)

3. **文件权限**：
   - 可执行文件：0750 (telegraf用户可读写执行，telegraf组可读执行)
   - 配置文件：0640 (telegraf用户可读写，telegraf组可读)
   - 凭据文件：0600 (仅telegraf用户可读写)
   - 日志文件：0640 (telegraf用户可读写，telegraf组可读)

4. **特殊权限设置**：
   ```bash
   # 设置主目录权限
   sudo chown -R telegraf:telegraf /opt/dci/dciagent
   sudo chmod -R 750 /opt/dci/dciagent
   
   # 设置配置文件权限
   sudo find /opt/dci/dciagent/conf -type f -exec chmod 640 {} \;
   
   # 设置凭据目录权限
   sudo chmod 700 /opt/dci/dciagent/conf/credentials
   sudo find /opt/dci/dciagent/conf/credentials -type f -exec chmod 600 {} \;
   
   # 设置可执行文件权限
   sudo chmod 750 /opt/dci/dciagent/bin/*
   ```

### 3.5.2 守护进程设置

为Telegraf创建自定义系统服务：

```ini
# /etc/systemd/system/telegraf.service

[Unit]
Description=Telegraf Data Collection Service
Documentation=https://github.com/influxdata/telegraf
After=network.target

[Service]
User=telegraf
Group=telegraf
Type=simple
WorkingDirectory=/opt/dci/dciagent
ExecStart=/opt/dci/dciagent/bin/telegraf --config /opt/dci/dciagent/conf/telegraf.conf --config-directory /opt/dci/dciagent/conf/telegraf.d
ExecReload=/bin/kill -HUP $MAINPID
Restart=on-failure
RestartForceExitStatus=SIGPIPE
KillMode=control-group
PIDFile=/opt/dci/dciagent/telegraf.pid

# 安全限制
CapabilityBoundingSet=CAP_NET_RAW CAP_NET_ADMIN
NoNewPrivileges=true
ProtectSystem=full
ProtectHome=true
PrivateTmp=true
ProtectControlGroups=true
ProtectKernelModules=true
MemoryDenyWriteExecute=true

[Install]
WantedBy=multi-user.target
```

### 3.5.3 凭据管理

安全管理SNMP社区字符串、API密钥等敏感信息：

1. **凭据文件模板**（/opt/dci/dciagent/conf/credentials/snmp_credentials.toml）：
   ```toml
   # SNMP凭据配置 - 权限应设置为600
   
   [auth]
   community = "your_community_string"
   version = "2c"  # 可选: 1, 2c, 3
   
   # 适用于SNMPv3
   #username = "your_username"
   #auth_protocol = "MD5"  # 可选: MD5, SHA
   #auth_password = "your_auth_password"
   #priv_protocol = "DES"  # 可选: DES, AES
   #priv_password = "your_priv_password"
   ```

2. **凭据引用方式**：
   ```toml
   # 在/opt/dci/dciagent/conf/telegraf.d/inputs/snmp.conf中引用凭据
   [[inputs.snmp]]
     ## 凭据文件引用
     agents = ["udp://192.168.1.1:161"]
     version = {{ snmpCred.version | default "2c" }}
     community = {{ snmpCred.community }}
   ```

3. **凭据备份与轮换策略**：
   - 凭据文件每次修改前先备份
   - 定期轮换敏感信息（例如每季度更换SNMP社区字符串）
   - 使用不同环境的不同凭据

# 4 安全设计

## 4.1 敏感数据保护

1. **凭据加密**：
   - 配置文件中的敏感信息（如数据库密码、API密钥）使用环境变量引用
   - 支持使用外部密钥管理系统（如HashiCorp Vault）获取凭据
   - 提供凭据轮换机制

2. **传输加密**：
   - Kafka连接支持SASL/SCRAM认证
   - 支持TLS加密通道
   - 配置最低TLS版本和强密码套件

## 4.2 文件系统安全

1. **数据隔离**：
   - 使用专用目录存储敏感配置
   - 严格控制目录权限
   - 定期审计文件权限

2. **备份保护**：
   - 备份文件采用加密存储
   - 限制备份文件访问权限
   - 实施备份文件生命周期管理

# 5 测试方案

## 5.1 部署测试范围

1. **安装测试**：
   - 在各种支持的操作系统上验证安装流程
   - 测试各种安装参数组合
   - 验证离线安装能力

2. **配置测试**：
   - 验证配置加载机制
   - 测试各种配置模板生成和应用
   - 验证权限设置的正确性

3. **集成测试**：
   - 测试与Kafka的连接和数据传输
   - 验证与Prometheus的集成
   - 测试多实例部署和协作

## 5.2 测试指标

1. **安装性能指标**：
   - 安装耗时：< 60秒（标准环境）
   - 配置应用时间：< 5秒
   - 首次启动时间：< 10秒

2. **功能指标**：
   - 安装成功率：> 99%
   - 配置部署一致性：> 99%
   - Kafka集成稳定性：> 99.5% 