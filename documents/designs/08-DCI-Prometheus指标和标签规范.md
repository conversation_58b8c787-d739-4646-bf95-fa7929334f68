---
title: |
  Prometheus指标和标签规范

subtitle: |
  DCI数据监测系统指标命名与标签体系设计
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-13 | 顾铠羟 | 初始版本           |

TODO: 将废弃，被取代

# 1 规范概述

## 1.1 背景与目标

在将DCI数据监测系统的时序数据库从TDengine迁移到Prometheus+Thanos架构的过程中，需要建立统一的指标命名和标签体系，以确保数据模型的一致性和可维护性。一个良好设计的指标命名和标签体系对于查询效率、系统可维护性和数据可用性有着重要影响。

本规范旨在：

1. 建立规范化的指标命名体系，确保指标名称的一致性和可理解性
2. 定义标准的标签集，满足不同监控对象的描述需求
3. 提供TDengine数据模型到Prometheus标签模型的映射指导
4. 确保监控数据的完整性和可查询性
5. 遵循Prometheus最佳实践，优化查询性能和存储效率

## 1.2 适用范围

本规范适用于DCI监测系统中所有通过Prometheus收集的指标数据，包括但不限于：

1. 网络设备监控指标（路由器、交换机、防火墙等）
2. 服务器资源监控指标（CPU、内存、磁盘、网络等）
3. 应用性能监控指标（响应时间、错误率、吞吐量等）
4. 业务指标（用户活动、交易量等）
5. 自定义指标

## 1.3 基本原则

在制定指标命名和标签体系规范时，遵循以下基本原则：

1. **一致性**：保持命名和标签使用的一致性
2. **语义化**：命名和标签应具有明确的语义
3. **扁平化**：避免过度嵌套的命名结构
4. **基数控制**：控制标签的基数，避免标签爆炸问题
5. **兼容性**：与现有TDengine数据模型保持兼容，便于迁移
6. **可扩展性**：支持未来新增监控对象和指标类型

# 2 指标命名规范

## 2.1 命名结构

Prometheus指标名称采用以下结构：

```
<命名空间>_<子系统>_<指标名>_<单位后缀>
```

### 示例：

```
dci_network_interface_received_bytes_total
dci_server_cpu_usage_ratio
dci_application_request_duration_seconds
```

## 2.2 命名规则详解

### 2.2.1 命名空间

- 所有DCI系统指标统一使用`dci`作为命名空间前缀
- 第三方组件的指标保留其原有命名空间（如`node_exporter`的`node`）

### 2.2.2 子系统

子系统表示指标所属的大类，常见子系统包括：

| 子系统     | 描述                             |
| ---------- | -------------------------------- |
| network    | 网络设备和接口相关指标           |
| server     | 服务器资源指标                   |
| application| 应用性能指标                     |
| database   | 数据库性能指标                   |
| storage    | 存储系统指标                     |
| kubernetes | K8s集群指标                      |
| service    | 微服务指标                       |

### 2.2.3 指标名

指标名应清晰描述测量的内容，采用蛇形命名法（snake_case），必要时可包含多个词：

- 对于计数器类型，应使用`_total`后缀（如`requests_total`）
- 对于表示事件发生率的指标，使用`_per_second`后缀
- 对于表示事件大小分布的指标，使用`_bucket`后缀

### 2.2.4 单位后缀

指标名称应包含适当的单位后缀，常见单位包括：

| 单位后缀   | 描述                             |
| ---------- | -------------------------------- |
| _seconds   | 时间秒数                         |
| _milliseconds | 时间毫秒数                    |
| _bytes     | 字节数                           |
| _bits      | 比特数                           |
| _percent   | 百分比值(0-100)                  |
| _ratio     | 比率值(0.0-1.0)                  |
| _total     | 累计值（用于Counter类型）        |
| _count     | 计数                             |
| _info      | 信息指标（通常为1）              |

## 2.3 指标类型使用规范

根据Prometheus的四种指标类型，规范其使用场景：

### 2.3.1 Counter（计数器）

- 用于表示单调递增的累计值，如请求总数、错误总数、传输字节总数
- 命名必须包含`_total`后缀
- 示例：`dci_network_interface_received_bytes_total`

### 2.3.2 Gauge（仪表盘）

- 用于表示可增可减的值，如当前内存使用量、温度、活跃连接数
- 不使用特定后缀标识
- 示例：`dci_server_memory_usage_bytes`

### 2.3.3 Histogram（直方图）

- 用于观察值分布情况，如请求延迟、响应大小
- 自动生成多个时间序列：`_bucket`、`_sum`、`_count`
- 示例：`dci_application_request_duration_seconds`（会自动产生`_bucket`、`_sum`、`_count`系列）

### 2.3.4 Summary（摘要）

- 类似Histogram，但提供分位数而不是分桶
- 较少使用，仅在需要客户端计算分位数时使用
- 示例：`dci_application_request_duration_seconds`（会自动产生`{quantile="0.5"}`等）

## 2.4 从TDengine迁移的命名映射

从TDengine迁移到Prometheus时，指标命名需要进行如下映射：

| TDengine模式                                 | Prometheus模式                                 |
|---------------------------------------------|------------------------------------------------|
| 超级表名作为度量类型                         | 转换为指标名前缀                              |
| 列名作为度量指标                            | 转换为指标名后缀或独立指标                    |
| 度量值单位内置在数据中                       | 单位作为指标名后缀                            |

### 映射示例：

TDengine中的网络接口表结构：
```sql
CREATE TABLE IF NOT EXISTS network_interface (
    ts TIMESTAMP,
    device_id NCHAR(64),
    interface_name NCHAR(64),
    rx_bytes DOUBLE,
    tx_bytes DOUBLE,
    rx_packets DOUBLE,
    tx_packets DOUBLE,
    rx_errors DOUBLE,
    tx_errors DOUBLE
);
```

映射到Prometheus中的指标名称：
```
dci_network_interface_received_bytes_total
dci_network_interface_transmitted_bytes_total
dci_network_interface_received_packets_total
dci_network_interface_transmitted_packets_total
dci_network_interface_received_errors_total
dci_network_interface_transmitted_errors_total
```

# 3 标签体系规范

## 3.1 标准标签集

为确保标签使用的一致性，定义以下标准标签：

| 标签名            | 描述                               | 示例值                |
|------------------|------------------------------------|-----------------------|
| device_id        | 设备唯一标识符                      | dev-1234              |
| device_type      | 设备类型                           | router, switch, server|
| location         | 设备物理位置                       | dc1-rack3             |
| environment      | 环境标识                           | prod, test, dev       |
| service          | 关联的服务名称                     | auth-service          |
| instance         | 实例标识（IP:端口）                | ********:9100         |
| job              | 采集作业名称                       | node_exporter         |
| zone             | 可用区                             | zone-a                |
| region           | 地区                               | cn-beijing            |
| tenant           | 租户标识                           | tenant-001            |
| cluster          | 集群标识                           | cluster-a             |

## 3.2 特定监控对象的标签集

### 3.2.1 网络设备标签

| 标签名            | 描述                               | 示例值                |
|------------------|------------------------------------|-----------------------|
| vendor           | 设备厂商                           | cisco, huawei, h3c    |
| model            | 设备型号                           | nexus-9000           |
| os_version       | 操作系统版本                       | ios-xe-16.9          |
| interface        | 接口标识                           | Ethernet1/1          |
| vrf              | VRF名称                            | management           |
| ip_address       | IP地址                             | ***********          |

### 3.2.2 服务器标签

| 标签名            | 描述                               | 示例值                |
|------------------|------------------------------------|-----------------------|
| os               | 操作系统                           | linux, windows       |
| os_version       | 操作系统版本                       | ubuntu-20.04         |
| kernel_version   | 内核版本                           | 5.4.0-65-generic     |
| cpu_arch         | CPU架构                            | x86_64, arm64        |
| virtualization   | 虚拟化类型                         | kvm, vmware, physical|
| instance_type    | 实例类型（云环境）                 | ecs.g6.xlarge        |
| mount_point      | 挂载点（存储相关）                 | /data                |
| filesystem       | 文件系统类型                       | ext4, xfs            |
| disk             | 磁盘设备名                         | sda, nvme0n1         |

### 3.2.3 应用标签

| 标签名            | 描述                               | 示例值                |
|------------------|------------------------------------|-----------------------|
| app              | 应用名称                           | dci-monitor          |
| component        | 组件名称                           | api, worker          |
| version          | 应用版本                           | v1.2.3               |
| endpoint         | API端点                            | /api/v1/metrics      |
| method           | HTTP方法                           | GET, POST            |
| status_code      | HTTP状态码                         | 200, 404, 500        |
| container_name   | 容器名称                           | nginx-proxy          |
| pod_name         | Pod名称                            | api-server-1234      |
| namespace        | K8s命名空间                        | monitoring           |

### 3.2.4 数据库标签

| 标签名            | 描述                               | 示例值                |
|------------------|------------------------------------|-----------------------|
| db_type          | 数据库类型                         | mysql, postgresql    |
| db_version       | 数据库版本                         | 8.0.23               |
| db_instance      | 数据库实例名                       | prod-db-1            |
| db_name          | 数据库名称                         | userdb               |
| table            | 表名                               | accounts             |
| query_type       | 查询类型                           | select, insert       |

## 3.3 标签使用最佳实践

为了确保标签使用的一致性和有效性，请遵循以下最佳实践：

1. **控制标签基数**：每个指标应避免使用超过10个标签，高基数标签（如用户ID、会话ID）不应直接用作标签
2. **标准化标签值**：标签值应标准化，例如使用小写，避免特殊字符
3. **避免冗余标签**：不要创建彼此高度相关的标签
4. **静态信息使用`_info`指标**：设备型号、OS版本等静态信息最好使用`_info`类型指标和标签组合表示
5. **常用过滤标签放前面**：常用于过滤的标签（如environment, service）应在标签列表前面
6. **保持标签命名一致性**：不要在不同指标中对同一概念使用不同的标签名

### 3.3.1 标签爆炸问题避免

高基数标签会导致时序数据库性能下降，应通过以下方式避免：

1. 使用聚合：对高基数维度预先聚合（如按分钟聚合而非按秒）
2. 限制使用：高基数维度（如请求ID）不用作标签，而是记录在日志中
3. 使用降采样：长期存储使用更粗粒度的聚合数据

## 3.4 TDengine标签映射

TDengine的超级表标签到Prometheus标签的映射规则：

| TDengine模式                                 | Prometheus模式                                 |
|---------------------------------------------|------------------------------------------------|
| 超级表标签（Tags）                           | 映射为Prometheus标签                          |
| 子表名与标签关系                            | 根据子表名提取标签值                          |

### 映射示例：

TDengine的标签定义：
```sql
CREATE TABLE IF NOT EXISTS network_interface (
    ...
) TAGS (
    device_id NCHAR(64),
    hostname NCHAR(128),
    device_type NCHAR(64),
    location NCHAR(64)
);
```

映射到Prometheus标签：
```
{device_id="dev-1234", hostname="switch-01", device_type="switch", location="dc1-rack3"}
```

# 4 常见监控对象的指标规范

## 4.1 网络设备指标

### 4.1.1 接口指标

| 指标名称                                    | 类型    | 标签                                | 描述                     |
|--------------------------------------------|---------|-------------------------------------|--------------------------|
| dci_network_interface_received_bytes_total | Counter | device_id, interface                | 接口接收的总字节数       |
| dci_network_interface_transmitted_bytes_total | Counter | device_id, interface              | 接口发送的总字节数       |
| dci_network_interface_received_packets_total | Counter | device_id, interface              | 接口接收的总数据包数     |
| dci_network_interface_transmitted_packets_total | Counter | device_id, interface            | 接口发送的总数据包数     |
| dci_network_interface_received_errors_total | Counter | device_id, interface               | 接口接收错误的总数       |
| dci_network_interface_transmitted_errors_total | Counter | device_id, interface            | 接口发送错误的总数       |
| dci_network_interface_received_drops_total | Counter | device_id, interface                | 接口接收丢弃的总数据包数 |
| dci_network_interface_transmitted_drops_total | Counter | device_id, interface             | 接口发送丢弃的总数据包数 |
| dci_network_interface_speed_bits_per_second | Gauge  | device_id, interface               | 接口速率                 |
| dci_network_interface_admin_status | Gauge  | device_id, interface                         | 接口管理状态(1=up, 0=down) |
| dci_network_interface_oper_status | Gauge  | device_id, interface                         | 接口运行状态(1=up, 0=down) |

### 4.1.2 设备指标

| 指标名称                                    | 类型    | 标签                                | 描述                     |
|--------------------------------------------|---------|-------------------------------------|--------------------------|
| dci_network_device_cpu_usage_ratio | Gauge  | device_id, cpu                          | CPU使用率(0.0-1.0)        |
| dci_network_device_memory_usage_ratio | Gauge  | device_id                            | 内存使用率(0.0-1.0)       |
| dci_network_device_temperature_celsius | Gauge  | device_id, sensor                    | 设备温度                  |
| dci_network_device_uptime_seconds | Gauge  | device_id                                | 设备运行时间              |
| dci_network_device_info | Gauge  | device_id, vendor, model, os_version, serial_number | 设备信息(值恒为1)         |

## 4.2 服务器指标

### 4.2.1 CPU指标

| 指标名称                                    | 类型    | 标签                                | 描述                     |
|--------------------------------------------|---------|-------------------------------------|--------------------------|
| dci_server_cpu_usage_ratio | Gauge  | instance, cpu                                    | CPU使用率(0.0-1.0)        |
| dci_server_cpu_usage_seconds_total | Counter | instance, cpu, mode                      | CPU使用时间（按模式）     |
| dci_server_load1 | Gauge  | instance                                                   | 1分钟平均负载             |
| dci_server_load5 | Gauge  | instance                                                   | 5分钟平均负载             |
| dci_server_load15 | Gauge  | instance                                                  | 15分钟平均负载            |

### 4.2.2 内存指标

| 指标名称                                    | 类型    | 标签                                | 描述                     |
|--------------------------------------------|---------|-------------------------------------|--------------------------|
| dci_server_memory_total_bytes | Gauge  | instance                                        | 总内存大小               |
| dci_server_memory_used_bytes | Gauge  | instance                                         | 已使用内存大小           |
| dci_server_memory_free_bytes | Gauge  | instance                                         | 空闲内存大小             |
| dci_server_memory_cached_bytes | Gauge  | instance                                       | 缓存内存大小             |
| dci_server_memory_buffer_bytes | Gauge  | instance                                       | 缓冲内存大小             |
| dci_server_swap_total_bytes | Gauge  | instance                                          | 总交换空间大小           |
| dci_server_swap_used_bytes | Gauge  | instance                                           | 已使用交换空间大小       |
| dci_server_swap_free_bytes | Gauge  | instance                                           | 空闲交换空间大小         |

### 4.2.3 磁盘指标

| 指标名称                                    | 类型    | 标签                                | 描述                     |
|--------------------------------------------|---------|-------------------------------------|--------------------------|
| dci_server_disk_total_bytes | Gauge  | instance, mount_point, filesystem                | 磁盘总空间               |
| dci_server_disk_used_bytes | Gauge  | instance, mount_point, filesystem                 | 磁盘已用空间             |
| dci_server_disk_free_bytes | Gauge  | instance, mount_point, filesystem                 | 磁盘可用空间             |
| dci_server_disk_read_bytes_total | Counter  | instance, device                         | 磁盘读取总字节数         |
| dci_server_disk_written_bytes_total | Counter  | instance, device                      | 磁盘写入总字节数         |
| dci_server_disk_io_time_seconds_total | Counter  | instance, device                    | 磁盘I/O时间总和          |
| dci_server_disk_io_time_weighted_seconds_total | Counter  | instance, device              | 加权磁盘I/O时间总和      |

## 4.3 应用指标

### 4.3.1 HTTP服务指标

| 指标名称                                    | 类型    | 标签                                | 描述                     |
|--------------------------------------------|---------|-------------------------------------|--------------------------|
| dci_application_http_requests_total | Counter  | service, instance, endpoint, method, status_code | HTTP请求总数           |
| dci_application_http_request_duration_seconds | Histogram  | service, instance, endpoint, method | 请求处理时间分布      |
| dci_application_http_request_size_bytes | Histogram  | service, instance, endpoint, method  | 请求大小分布           |
| dci_application_http_response_size_bytes | Histogram  | service, instance, endpoint, method | 响应大小分布           |
| dci_application_http_in_flight_requests | Gauge  | service, instance                       | 当前正在处理的请求数   |

### 4.3.2 数据库客户端指标

| 指标名称                                    | 类型    | 标签                                | 描述                     |
|--------------------------------------------|---------|-------------------------------------|--------------------------|
| dci_application_db_connections_total | Gauge  | service, instance, db_type, db_name       | 数据库连接总数         |
| dci_application_db_connections_in_use | Gauge  | service, instance, db_type, db_name       | 正在使用的数据库连接数 |
| dci_application_db_query_duration_seconds | Histogram  | service, instance, db_type, query_type | 数据库查询时间分布   |
| dci_application_db_errors_total | Counter  | service, instance, db_type, error_type       | 数据库错误总数         |

## 4.4 数据库服务器指标

### 4.4.1 通用数据库指标

| 指标名称                                    | 类型    | 标签                                | 描述                     |
|--------------------------------------------|---------|-------------------------------------|--------------------------|
| dci_database_connections | Gauge  | instance, db_type, db_name                          | 活跃连接数               |
| dci_database_max_connections | Gauge  | instance, db_type                               | 最大连接数               |
| dci_database_queries_total | Counter  | instance, db_type, db_name, query_type          | 查询总数                 |
| dci_database_query_duration_seconds | Histogram  | instance, db_type, db_name, query_type | 查询执行时间分布       |
| dci_database_errors_total | Counter  | instance, db_type, db_name, error_type            | 错误总数                 |
| dci_database_up | Gauge  | instance, db_type                                            | 数据库是否可用(1=up, 0=down) |

## 4.5 K8s相关指标

K8s相关指标优先使用kube-state-metrics和kubelet提供的标准指标，前缀保持原有的`kube_`和`kubelet_`，无需额外映射。

## 4.6 云资源指标

云资源指标从云服务商的监控系统中获取，映射到Prometheus时应添加云服务商信息标签：

| 指标名称                                    | 类型    | 标签                                | 描述                     |
|--------------------------------------------|---------|-------------------------------------|--------------------------|
| dci_cloud_cpu_usage_ratio | Gauge  | instance, cloud_provider, region                   | 云实例CPU使用率          |
| dci_cloud_memory_usage_ratio | Gauge  | instance, cloud_provider, region                | The cloud instance's memory usage ratio |
| dci_cloud_network_in_bytes_total | Counter  | instance, cloud_provider, region          | 入站网络流量总字节数     |
| dci_cloud_network_out_bytes_total | Counter  | instance, cloud_provider, region         | 出站网络流量总字节数     |

# 5 单位标准化

## 5.1 单位换算规则

为保持一致性，指标值应遵循以下单位换算规则：

| 度量类型     | 推荐单位     | 其他单位的换算                   |
|-------------|-------------|----------------------------------|
| 时间        | seconds     | ms -> seconds: 值/1000          |
| 内存/磁盘空间| bytes       | KB -> bytes: 值*1024            |
| 网络带宽    | bits_per_second | bytes/s -> bits/s: 值*8        |
| 百分比      | ratio       | percent -> ratio: 值/100        |

## 5.2 单位后缀使用

指标名应明确包含单位信息作为后缀：

1. 对于秒级时间，使用`_seconds`（如`response_time_seconds`）
2. 对于字节大小，使用`_bytes`（如`memory_used_bytes`）
3. 对于比特率，使用`_bits_per_second`（如`network_received_bits_per_second`）
4. 对于比率值(0-1)，使用`_ratio`（如`cpu_usage_ratio`）
5. 对于百分比值(0-100)，使用`_percent`（如`disk_usage_percent`）

# 6 元数据处理

## 6.1 设备和服务基本信息

对于监控对象的静态信息，应使用`_info`类型的指标和标签组合表示：

```
dci_network_device_info{device_id="sw1", vendor="cisco", model="nexus9k", os_version="7.0.3.I7.5", serial="FDO21350V5K"} 1
dci_server_node_info{instance="srv1", hostname="web-01", os="ubuntu", os_version="20.04", kernel="5.4.0-42-generic", arch="x86_64"} 1
```

## 6.2 拓扑关系表示

网络拓扑关系可通过以下方式在Prometheus中表示：

```
dci_network_connection_info{source_device="sw1", source_interface="Ethernet1/1", target_device="sw2", target_interface="Ethernet1/1", link_type="ethernet", speed_mbps="10000"} 1
```

## 6.3 服务依赖关系

服务依赖关系可通过以下方式表示：

```
dci_service_dependency_info{service="api-gateway", depends_on="auth-service", dependency_type="direct"} 1
```

# 7 从TDengine迁移的数据模型映射

## 7.1 表结构映射

下表展示了TDengine数据模型与Prometheus数据模型的映射关系：

| TDengine概念           | Prometheus概念                      | 映射方法                              |
|-----------------------|-------------------------------------|---------------------------------------|
| 超级表(STable)         | 指标名称前缀                        | 转换为{命名空间}_{子系统}部分         |
| 度量列(Fields)         | 独立指标或组合成同一指标的多个标签   | 每个度量列映射为独立指标或作为label   |
| 标签列(Tags)           | 标签(Label)                         | 直接映射为Prometheus标签              |
| 子表                   | 标签组合                            | 子表信息提取为一组特定的标签值组合     |
| 数据点时间戳           | 样本时间戳                          | 直接映射                              |
| 数据点取值             | 样本取值                            | 直接映射，注意单位换算                |

## 7.2 数据查询映射

TDengine SQL查询与Prometheus PromQL查询的基本映射：

| TDengine SQL                         | PromQL                                      |
|-------------------------------------|----------------------------------------------|
| SELECT last(value) FROM table       | dci_metric_name{labels}                      |
| SELECT avg(value) FROM table WHERE ts>=now-1h | avg_over_time(dci_metric_name{labels}[1h]) |
| SELECT max(value) FROM table GROUP BY tbname | max by (instance) (dci_metric_name{labels}) |

## 7.3 映射示例

### 7.3.1 接口指标映射

TDengine接口指标表：
```sql
CREATE TABLE network_interface (
    ts TIMESTAMP,
    rx_bytes DOUBLE,
    tx_bytes DOUBLE,
    rx_packets DOUBLE,
    tx_packets DOUBLE,
    rx_errors DOUBLE,
    tx_errors DOUBLE
) TAGS (
    device_id NCHAR(64),
    interface NCHAR(64)
);
```

映射到Prometheus指标：
```
dci_network_interface_received_bytes_total{device_id="device1", interface="eth0"} 1234567
dci_network_interface_transmitted_bytes_total{device_id="device1", interface="eth0"} 7654321
dci_network_interface_received_packets_total{device_id="device1", interface="eth0"} 9876
dci_network_interface_transmitted_packets_total{device_id="device1", interface="eth0"} 8765
dci_network_interface_received_errors_total{device_id="device1", interface="eth0"} 12
dci_network_interface_transmitted_errors_total{device_id="device1", interface="eth0"} 5
```

### 7.3.2 CPU指标映射

TDengine CPU指标表：
```sql
CREATE TABLE server_cpu (
    ts TIMESTAMP,
    usage_percent DOUBLE,
    user_percent DOUBLE,
    system_percent DOUBLE,
    idle_percent DOUBLE,
    iowait_percent DOUBLE
) TAGS (
    server_id NCHAR(64),
    cpu NCHAR(16)
);
```

映射到Prometheus指标：
```
dci_server_cpu_usage_ratio{server_id="server1", cpu="cpu0"} 0.45
dci_server_cpu_usage_seconds_total{server_id="server1", cpu="cpu0", mode="user"} 12345.6
dci_server_cpu_usage_seconds_total{server_id="server1", cpu="cpu0", mode="system"} 5432.1
dci_server_cpu_usage_seconds_total{server_id="server1", cpu="cpu0", mode="idle"} 23456.7
dci_server_cpu_usage_seconds_total{server_id="server1", cpu="cpu0", mode="iowait"} 1234.5
```

# 8 规范实施指南

## 8.1 Telegraf配置更新

为实现本规范定义的命名和标签体系，Telegraf配置需要进行以下更新：

1. 更新输出插件，使用Prometheus输出格式
2. 配置全局标签，确保所有指标包含必要的通用标签
3. 为每个输入插件配置指标名称前缀和自定义标签
4. 实现自定义处理器插件，进行名称转换和标签标准化

Telegraf配置示例：
```toml
[global_tags]
  environment = "production"
  tenant = "tenant-001"

[[outputs.prometheus_client]]
  listen = ":9273"
  metric_version = 2
  path = "/metrics"
  
[[processors.rename]]
  [[processors.rename.replace]]
    measurement = "cpu"
    dest = "dci_server_cpu"
    
  [[processors.rename.replace]]
    measurement = "mem"
    dest = "dci_server_memory"
    
  [[processors.rename.replace]]
    measurement = "net"
    dest = "dci_server_network_interface"
```

## 8.2 应用接入指南

接入系统的应用应遵循以下步骤实现指标命名和标签规范：

1. 使用适当的客户端库（如Prometheus官方客户端库）
2. 按照本规范定义的命名结构创建指标
3. 为所有指标添加标准标签集
4. 根据指标类型选择合适的指标类型（Counter, Gauge等）
5. 确保单位标准化和单位后缀正确使用

## 8.3 指标检验流程

新增指标或修改现有指标时，应通过以下检验流程确保符合规范：

1. 检查指标名称是否符合命名结构
2. 验证标签是否使用了标准标签集
3. 确认指标类型的选择是否正确
4. 验证单位后缀是否与实际单位一致
5. 检查标签基数是否控制在合理范围内
6. 确保静态信息使用了_info方式存储

## 8.4 常见问题解决方案

| 问题                                      | 解决方案                                    |
|------------------------------------------|---------------------------------------------|
| 指标名称过长                              | 适当缩写子系统名称，但保持指标名直观明了     |
| 标签基数过高                              | 避免使用高基数字段作为标签，考虑预聚合或采样 |
| 多个系统使用不一致命名                    | 使用统一的导出器或配置重命名规则            |
| 第三方组件指标不符合规范                  | 通过relabeling配置在抓取时进行规范化         |
| TDengine到Prometheus迁移时数据格式转换问题 | 开发适配工具或使用规则映射进行批量转换      |

# 9 附录

## 9.1 参考文档

1. [Prometheus命名最佳实践](https://prometheus.io/docs/practices/naming/)
2. [Prometheus指标和标签约定](https://prometheus.io/docs/concepts/data_model/)
3. [OpenMetrics规范](https://github.com/OpenObservability/OpenMetrics/blob/main/specification/OpenMetrics.md)
4. [Telegraf Prometheus输出插件文档](https://github.com/influxdata/telegraf/tree/master/plugins/outputs/prometheus_client)
5. [Google SRE监控分布式系统](https://sre.google/sre-book/monitoring-distributed-systems/) 