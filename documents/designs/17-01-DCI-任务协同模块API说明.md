# DCI-任务协同模块API说明

## 1 概述

任务协同监控模块是算力互联数据监测系统的核心功能模块，实现网络自动化任务与监控系统的协同工作。该模块负责接收来自网络自动化控制系统的任务信号，创建任务监控会话，收集任务执行期间的相关数据，并提供任务相关的告警查询和统计功能。

### 1.1 功能特性

- **任务信号处理**：接收并处理来自网络自动化控制系统的任务开始、结束、中止信号
- **任务监控会话管理**：创建、管理和停止任务监控会话
- **数据收集协调**：启动和停止任务相关的数据收集（性能指标、日志、告警、事件）
- **告警关联管理**：自动关联任务期间产生的告警，支持延展监测（任务结束后1小时）
- **统计查询服务**：提供任务相关告警的统计和查询功能

### 1.2 基础URL

```
基础URL: {host}/api/v1
```

### 1.3 状态枚举定义

| 状态值 | 含义 | 说明 |
|--------|------|------|
| 1 | 成功 | 任务执行成功 |
| 2 | 失败 | 任务执行失败 |
| 3 | 中止 | 任务被中止 |

## 2 API接口

### 2.1 任务信号处理

#### 2.1.1 接收任务信号

**接口描述**: 接收来自网络自动化控制系统的任务信号（开始、结束、中止），用于启动、结束或中止任务监控会话

- **URL**: `POST /tasks/signals`
- **Content-Type**: `application/json`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| signal_type | string | 是 | 信号类型 | `start`、`end`、`abort` |
| task_id | string | 是 | 任务ID（全局唯一标识符） | `550e8400-e29b-41d4-a716-446655440000` |
| timestamp | string | 否 | 时间戳（RFC3339格式） | `2025-01-11T10:15:30Z` |
| task_type | string | 是 | 任务类型（基于自动化系统Django choices规范） | `CONFIG_DEPLOY`、`CONFIG_RECOVERY`、`CONFIG_DOWNLOAD`、`OTHER` |
| target_devices | array | 是 | 目标设备ID列表（对应device_id，至少1个） | `["211", "210"]` |
| expected_duration | integer | 否 | 预期持续时间（秒） | `30` |
| status | string | 否 | 任务状态（仅end信号时需要），基于自动化系统Django choices规范：<br/>• `SUCCESS` - 任务执行成功<br/>• `FAILURE` - 任务执行失败<br/>• `REVOKED` - 任务被中止 | `SUCCESS` |
| port_name | string | 否 | 端口名称，用于描述任务涉及的端口 | `GigabitEthernet0/0/1` |
| port_id | string | 否 | 逻辑端口ID，用于描述任务涉及的端口 | `12345` |

**请求示例**:

```json
{
  "signal_type": "start",
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "timestamp": "2025-01-11T10:15:30Z",
  "task_type": "CONFIG_DEPLOY",
  "target_devices": ["211", "210"],
  "expected_duration": 30,
  "port_name": "GigabitEthernet0/0/1",
  "port_id": "12345"
}
```

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "success": true,
    "sessionId": "session-12345",
    "message": "任务监控会话创建成功",
    "taskId": "550e8400-e29b-41d4-a716-446655440000"
  }
}
```

### 2.2 任务监控管理

#### 2.2.1 启动任务监控

**接口描述**: 手动启动指定任务的监控会话，开始收集相关设备的性能指标和日志数据

- **URL**: `POST /tasks/{taskId}/monitoring/start`
- **Content-Type**: `application/json`

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| taskId | string | 是 | 任务ID | `550e8400-e29b-41d4-a716-446655440000` |

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| deviceIds | array | 是 | 设备ID列表（至少1个） | `["CE1", "CE2", "************"]` |

**请求示例**:

```json
{
  "deviceIds": ["211", "210", "************"]
}
```

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "success": true,
    "message": "任务监控启动成功",
    "taskId": "550e8400-e29b-41d4-a716-446655440000",
    "sessionId": "session-12345",
    "deviceCount": 3,
    "startTime": "2025-01-11T10:15:30Z"
  }
}
```

#### 2.2.2 停止任务监控

**接口描述**: 手动停止指定任务的监控会话

- **URL**: `POST /tasks/{taskId}/monitoring/manual/stop`

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| taskId | string | 是 | 任务ID | `550e8400-e29b-41d4-a716-446655440000` |

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "success": true,
    "message": "任务监控停止成功",
    "taskId": "550e8400-e29b-41d4-a716-446655440000",
    "sessionId": "session-12345",
    "endTime": "2025-01-11T10:45:30Z",
    "duration": "30m"
  }
}
```

#### 2.2.3 查询任务状态

**接口描述**: 查询指定任务的监控状态和基本信息

- **URL**: `GET /tasks/{taskId}/monitoring/status`

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| taskId | string | 是 | 任务ID | `550e8400-e29b-41d4-a716-446655440000` |

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "taskId": "550e8400-e29b-41d4-a716-446655440000",
    "status": "SUCCESS",
    "sessionId": "session-12345",
    "startTime": "2025-01-11T10:15:30Z",
    "endTime": null,
    "deviceCount": 3,
    "isActive": true
  }
}
```

### 2.3 任务告警查询

#### 2.3.1 获取任务相关告警列表

**接口描述**: 查询指定任务执行期间相关的告警列表，包括任务执行期间和延展监测期的告警

- **URL**: `GET /tasks/{taskId}/alerts`

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| taskId | string | 是 | 任务ID | `550e8400-e29b-41d4-a716-446655440000` |

**查询参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| start_time | string | 否 | 开始时间（RFC3339格式） | `2025-01-11T10:15:30Z` |
| end_time | string | 否 | 结束时间（RFC3339格式） | `2025-01-11T11:45:30Z` |
| limit | integer | 否 | 每页数量，默认20 | `20` |
| offset | integer | 否 | 偏移量，默认0 | `0` |

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "taskId": "550e8400-e29b-41d4-a716-446655440000",
    "alerts": [
      {
        "id": "alert-001",
        "name": "Interface Down",
        "level": "critical",
        "status": "firing",
        "deviceId": "211",
        "startsAt": "2025-01-11T10:20:00Z",
        "endsAt": null,
        "taskRelation": "任务执行期间",
        "monitoringPhase": "active_execution"
      },
      {
        "id": "alert-002",
        "name": "High CPU Usage",
        "level": "warning",
        "status": "resolved",
        "deviceId": "210",
        "startsAt": "2025-01-11T10:25:00Z",
        "endsAt": "2025-01-11T10:35:00Z",
        "taskRelation": "任务执行期间",
        "monitoringPhase": "active_execution"
      }
    ],
    "statistics": {
      "totalCount": 5,
      "levelCount": {
        "critical": 1,
        "warning": 3,
        "info": 1
      },
      "phaseCount": {
        "active_execution": 4,
        "post_completion": 1
      }
    },
    "pagination": {
      "total": 5,
      "limit": 20,
      "offset": 0,
      "page": 1,
      "pageSize": 20
    }
  }
}
```

#### 2.3.2 获取任务告警统计

**接口描述**: 查询指定任务执行期间的告警统计信息，包括按级别、状态、阶段和设备的统计分布

- **URL**: `GET /tasks/{taskId}/alerts/statistics`

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| taskId | string | 是 | 任务ID | `550e8400-e29b-41d4-a716-446655440000` |

**查询参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| start_time | string | 否 | 开始时间（RFC3339格式） | `2025-01-11T10:15:30Z` |
| end_time | string | 否 | 结束时间（RFC3339格式） | `2025-01-11T11:45:30Z` |

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "taskId": "550e8400-e29b-41d4-a716-446655440000",
    "timeRange": {
      "startTime": "2025-01-11T10:15:30Z",
      "endTime": "2025-01-11T11:45:30Z"
    },
    "totalCount": 5,
    "levelCount": {
      "critical": 1,
      "warning": 3,
      "info": 1
    },
    "statusCount": {
      "firing": 2,
      "resolved": 3
    },
    "phaseCount": {
      "active_execution": 4,
      "post_completion": 1
    },
    "deviceCount": {
      "211": 3,
      "210": 2
    },
    "timeDistribution": [
      {
        "timeSlot": "2025-01-11T10:15:00Z",
        "count": 2
      },
      {
        "timeSlot": "2025-01-11T10:30:00Z",
        "count": 3
      }
    ]
  }
}
```

### 2.4 系统状态查询

#### 2.4.1 获取系统状态

**接口描述**: 查询任务协同监控系统的运行状态

- **URL**: `GET /system/task-collaboration/status`

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "signal_processor": {
      "status": "running",
      "processed_signals": 156,
      "last_signal_time": "2025-01-11T10:45:00Z"
    },
    "data_collector": {
      "status": "running",
      "active_collections": 3,
      "total_data_points": 12845
    },
    "timestamp": "2025-01-11T10:50:00Z",
    "version": "1.0.0"
  }
}
```

#### 2.4.2 获取数据收集状态

**接口描述**: 查询指定会话的数据收集状态详情

- **URL**: `GET /system/task-collaboration/collections/{sessionId}`

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| sessionId | string | 是 | 会话ID | `session-12345` |

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "context": {
      "sessionId": "session-12345",
      "taskId": "550e8400-e29b-41d4-a716-446655440000",
      "status": "collecting",
      "startTime": "2025-01-11T10:15:30Z",
      "deviceCount": 3
    },
    "summary": {
      "sessionId": "session-12345",
      "taskId": "550e8400-e29b-41d4-a716-446655440000",
      "totalMetrics": 856,
      "totalLogs": 124,
      "totalAlerts": 5,
      "totalEvents": 23,
      "deviceStats": {
        "211": {
          "metrics": 320,
          "logs": 45,
          "alerts": 2,
          "events": 8
        },
        "210": {
          "metrics": 536,
          "logs": 79,
          "alerts": 3,
          "events": 15
        }
      }
    }
  }
}
```

## 3 数据模型

### 3.1 请求数据模型

#### TaskSignal - 任务信号

```json
{
  "signal_type": "start|end|abort",
  "task_id": "string (UUID)",
  "timestamp": "string (RFC3339)",
  "task_type": "CONFIG_DEPLOY|CONFIG_RECOVERY|CONFIG_DOWNLOAD|OTHER",
  "target_devices": ["string"],
  "expected_duration": "integer",
  "status": "string (枚举值: SUCCESS=成功, FAILURE=失败, REVOKED=中止)",
  "port_name": "string (端口名称，选填)",
  "port_id": "string (逻辑端口ID，选填)"
}
```

#### TaskMonitoringRequest - 任务监控请求

```json
{
  "deviceIds": ["string"]
}
```

### 3.2 响应数据模型

#### TaskSignalResponse - 任务信号响应

```json
{
  "success": "boolean",
  "sessionId": "string",
  "message": "string",
  "taskId": "string"
}
```

#### TaskMonitoringResponse - 任务监控响应

```json
{
  "success": "boolean",
  "message": "string",
  "taskId": "string",
  "sessionId": "string",
  "deviceCount": "integer",
  "startTime": "string (RFC3339)",
  "endTime": "string (RFC3339)",
  "duration": "string"
}
```

#### TaskStatusResponse - 任务状态响应

```json
{
  "taskId": "string",
  "status": "string",
  "sessionId": "string",
  "startTime": "string (RFC3339)",
  "endTime": "string (RFC3339)",
  "deviceCount": "integer",
  "isActive": "boolean"
}
```

**状态枚举说明**：
- `SUCCESS` - 任务执行成功
- `FAILURE` - 任务执行失败
- `REVOKED` - 任务被中止

#### TaskRelatedAlert - 任务相关告警

```json
{
  "id": "string",
  "name": "string",
  "level": "critical|warning|info",
  "status": "firing|resolved",
  "deviceId": "string",
  "startsAt": "string (RFC3339)",
  "endsAt": "string (RFC3339)",
  "taskRelation": "string",
  "monitoringPhase": "active_execution|post_completion"
}
```

#### TaskAlertStatistics - 任务告警统计

```json
{
  "taskId": "string",
  "timeRange": {
    "startTime": "string (RFC3339)",
    "endTime": "string (RFC3339)"
  },
  "totalCount": "integer",
  "levelCount": {
    "critical": "integer",
    "warning": "integer",
    "info": "integer"
  },
  "statusCount": {
    "firing": "integer",
    "resolved": "integer"
  },
  "phaseCount": {
    "active_execution": "integer",
    "post_completion": "integer"
  },
  "deviceCount": {
    "deviceId": "integer"
  },
  "timeDistribution": [
    {
      "timeSlot": "string (RFC3339)",
      "count": "integer"
    }
  ]
}
```

**字段说明**：
- `deviceCount`: 按设备ID统计的告警数量，记录每个设备产生的告警数量
- `deviceId`: 在deviceCount中作为键，表示具体的设备ID

#### PaginationInfo - 分页信息

```json
{
  "total": "integer",
  "limit": "integer",
  "offset": "integer",
  "page": "integer",
  "pageSize": "integer"
}
```

## 4 错误处理

### 4.1 统一错误响应格式

```json
{
  "code": "integer (HTTP状态码)",
  "data": {
    "error": "string (错误信息)",
    "request_id": "string (请求ID)",
    "details": "string (详细错误信息)"
  }
}
```

### 4.2 常见错误码

| HTTP状态码 | 错误类型 | 说明 |
|------------|----------|------|
| 400 | Bad Request | 请求参数格式错误 |
| 404 | Not Found | 任务或资源不存在 |
| 500 | Internal Server Error | 服务器内部错误 |

### 4.3 错误响应示例

```json
{
  "code": 400,
  "data": {
    "error": "请求参数格式错误",
    "request_id": "789fe92e-6e44-4df4-8734-0bbcc59a2689",
    "details": "invalid time format"
  }
}
```

## 5 使用示例

### 5.1 完整的任务监控流程

#### 步骤1: 任务开始时发送开始信号

```bash
curl -X POST "${BASE_URL}/api/v1/tasks/signals" \
  -H "Content-Type: application/json" \
  -d '{
    "signal_type": "start",
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "timestamp": "2025-01-11T10:15:30Z",
    "task_type": "CONFIG_DEPLOY",
    "target_devices": ["211", "210"],
    "expected_duration": 30,
    "port_name": "GigabitEthernet0/0/1",
    "port_id": "12345"
  }'
```

#### 步骤2: 查询任务状态

```bash
curl -X GET "${BASE_URL}/api/v1/tasks/550e8400-e29b-41d4-a716-446655440000/monitoring/status"
```

#### 步骤3: 查询任务相关告警

```bash
curl -X GET "${BASE_URL}/api/v1/tasks/550e8400-e29b-41d4-a716-446655440000/alerts?limit=20&offset=0"
```

#### 步骤4: 任务结束时发送结束信号

```bash
curl -X POST "${BASE_URL}/api/v1/tasks/signals" \
  -H "Content-Type: application/json" \
  -d '{
    "signal_type": "end",
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "timestamp": "2025-01-11T10:45:30Z",
    "task_type": "CONFIG_DEPLOY",
    "target_devices": ["211", "210"],
    "status": "SUCCESS"
  }'
```

#### 步骤5: 查询任务告警统计

```bash
curl -X GET "${BASE_URL}/api/v1/tasks/550e8400-e29b-41d4-a716-446655440000/alerts/statistics"
```

### 5.2 手动监控管理示例

#### 手动启动任务监控

```bash
curl -X POST "${BASE_URL}/api/v1/tasks/550e8400-e29b-41d4-a716-446655440000/monitoring/start" \
  -H "Content-Type: application/json" \
  -d '{
    "deviceIds": ["211", "210", "************"]
  }'
```

#### 手动停止任务监控

```bash
curl -X POST "${BASE_URL}/api/v1/tasks/550e8400-e29b-41d4-a716-446655440000/monitoring/manual/stop"
```

### 5.3 系统状态查询示例

#### 查询系统状态

```bash
curl -X GET "${BASE_URL}/api/v1/system/task-collaboration/status"
```

#### 查询数据收集状态

```bash
curl -X GET "${BASE_URL}/api/v1/system/task-collaboration/collections/session-12345"
```

## 6 特性说明

### 6.1 延展监测机制

任务协同监控系统支持延展监测功能：
- 任务结束后1小时内产生的告警仍会自动关联到该任务
- 在告警查询结果中，`monitoringPhase` 字段区分任务执行期间（`active_execution`）和延展监测期（`post_completion`）的告警

### 6.2 数据收集类型

系统支持四种类型的数据收集：
- **metrics**: 性能指标数据（CPU、内存、网络流量等）
- **logs**: 日志数据（配置变更日志、系统日志等）
- **alerts**: 告警数据（任务期间触发的告警）
- **events**: 事件数据（网络状态变化事件等）

### 6.3 告警关联策略

- **时间关联**: 基于任务执行时间范围自动关联告警
- **设备关联**: 基于任务目标设备列表关联相关设备的告警
- **延展关联**: 任务结束后1小时内的告警仍会关联到任务

## 7 注意事项

### 7.1 时间格式

- 所有时间字段均使用RFC3339格式（如：`2025-01-11T10:15:30Z`）
- 数据库中存储的时间使用Asia/Shanghai时区

### 7.2 设备ID格式

- 设备ID支持多种格式：设备名称（如`CE1`）、IP地址（如`************`）等
- 建议使用统一的设备标识符规范

### 7.3 任务ID规范

- 任务ID必须全局唯一，不可重复
- 建议使用UUID v4或其他唯一标识符格式生成任务ID
- 每个网络自动化任务对应唯一的任务ID和监控会话

### 7.4 分页限制

- 默认分页大小为20条记录
- 最大分页大小限制为100条记录
- 建议使用合理的分页大小以获得最佳性能
