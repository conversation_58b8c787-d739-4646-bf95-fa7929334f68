---
title: |
  DCI-dciagent与服务端集成设计

subtitle: |
  Management Agent与Telegraf集成及服务端交互方案
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-21 | 顾铠羟 | 从原《15-DCI-dciagent客户端技术方案设计》拆分而来 |

# 1 文档介绍

## 1.1 文档目的

本文档详细描述DCI数据监测系统中Management Agent与Telegraf的集成方式、交互机制，以及与服务端的通信流程和API设计。文档旨在明确dciagent各组件之间以及与服务端之间的通信与集成规范，为系统集成提供技术指导。

## 1.2 文档范围

本文档涵盖DCI数据监测系统中dciagent与服务端集成相关的内容，包括：

1. Management Agent与Telegraf集成机制
2. 配置同步与版本管理
3. 服务端管理设计
4. 服务端API接口设计
5. 配置分发逻辑
6. 多租户与权限设计

# 2 总体设计

## 2.1 设计目标

dciagent与服务端集成设计旨在实现以下目标：

1. 实现Management Agent与Telegraf的松耦合集成
2. 建立稳定可靠的服务端通信机制
3. 支持集中化的Agent管理与配置
4. 确保配置下发的一致性与可追踪性
5. 提供灵活的多租户支持
6. 实现基于角色的访问控制

## 2.2 架构设计

### 2.2.1 集成架构

Management Agent与Telegraf的集成架构如下：

```mermaid
graph TD
    subgraph "Agent节点"
        subgraph "Management Agent (MA)"
            Config[配置管理模块]
            Status[状态监控模块]
            Command[命令执行模块]
        end
        
        subgraph "Telegraf Agent (TA)" 
            TConfig[配置文件]
            TBin[可执行文件]
            TProcess[Telegraf进程]
            TStatus[运行状态]
            TLogs[日志文件]
        end
    end
    
    subgraph "dcimonitor 服务端"
        MgmtAPI[MgmtAPI服务]
        ConfigStore[配置存储]
        ClientRegistry[Agent注册/状态]
    end
    
    MgmtAPI -- HTTPS(配置下发) --> Config
    Config -- 写入/更新 --> TConfig
    TConfig -- 加载 --> TProcess
    
    TProcess -- 生成 --> TLogs
    TLogs -- 读取 --> Status
    TProcess -- 状态信息 --> TStatus
    TStatus -- 采集 --> Status
    Status -- HTTPS(心跳/状态上报) --> MgmtAPI
    MgmtAPI -- 读写 --> ConfigStore
    MgmtAPI -- 读写 --> ClientRegistry
    
    MgmtAPI -- HTTPS(操作命令) --> Command
    Command -- 调用脚本 --> TBin
    TBin -- 控制 --> TProcess
```

Management Agent是连接服务端与Telegraf的中间层，Telegraf本身不直接与服务端通信。

## 2.3 技术选型

1. **通信协议**:
   - 采用HTTPS协议确保通信安全
   - 使用JSON格式作为数据交换格式
   - 支持数据压缩减少传输量

2. **认证机制**:
   - 基于Token的API认证
   - JWT令牌用于短期会话管理
   - 支持证书验证

3. **配置管理**:
   - 使用TOML格式的配置文件
   - 支持配置版本控制
   - 支持配置模板与参数化

4. **状态管理**:
   - 心跳机制监控Agent状态
   - 事件驱动的状态更新
   - 批量状态管理

# 3 详细设计

## 3.1 Management Agent与Telegraf集成

### 3.1.1 Management Agent职责

Management Agent在与Telegraf集成时的主要职责包括：

1. **注册与认证**：
   - 向服务端MgmtAPI注册自身，获取唯一Agent ID
   - 使用预置Token进行身份认证
   - 注册成功后将Agent ID持久化

2. **配置管理**：
   - 定期（例如5分钟一次）从服务端拉取最新基础配置
   - 将配置片段写入Telegraf配置目录（telegraf.d/）
   - 发送SIGHUP信号触发Telegraf重新加载配置
   - 更新本地记录的配置版本号
   - 注册完成后服务端可根据Agent ID下发指定监测业务配置

3. **进程管理**：
   - 监控Telegraf进程状态
   - 执行启动/停止/重启操作
   - 响应来自服务端的控制命令

4. **状态上报**：
   - 定期（例如60秒一次）向服务端发送心跳
   - 上报自身状态和Telegraf状态
   - 上报配置应用结果

5. **日志管理**：
   - 收集Telegraf关键日志
   - 上报关键事件和错误

### 3.1.2 通信机制

Management Agent与Telegraf的通信机制基于文件系统操作和进程控制：

1. **文件系统交互**：
   - MA读写Telegraf配置文件
   - MA监控Telegraf日志文件
   - MA管理版本和备份文件

2. **进程控制**：
   - MA通过Go语言os库或命令行工具控制Telegraf进程
   - MA向Telegraf进程发送信号（如SIGHUP）
   - MA检查进程状态和健康度

3. **集成流程示例**：

```mermaid
sequenceDiagram
    participant MA as Management Agent
    participant API as MA内部API
    participant CMD as dciagent命令
    participant FS as 文件系统
    participant TP as Telegraf进程
    
    MA->>API: 请求执行Telegraf操作
    alt 使用内部API
        API->>TP: 直接控制Telegraf进程
    else 使用命令行工具
        API->>CMD: 调用dciagent telegraf命令
        CMD->>TP: 控制Telegraf进程
    end
    
    MA->>FS: 写入/更新配置文件
    FS->>TP: Telegraf加载配置
    
    MA->>TP: 发送SIGHUP信号
    TP->>FS: 重新加载配置
    
    TP->>FS: 写入日志文件
    MA->>FS: 读取日志和状态信息
    
    MA->>API: 获取Telegraf状态
    API->>TP: 检查进程状态
    TP-->>API: 返回状态信息
    API-->>MA: 报告Telegraf状态
```

### 3.1.3 配置同步与版本管理机制

Management Agent与Telegraf之间的配置同步和版本管理机制如下：

1. **配置文件结构**：
   配置文件采用模块化架构，便于分发和管理：
   ```
   /opt/dci/dciagent/conf/
   ├── telegraf.conf            # 主配置文件
   └── telegraf.d/              # 配置片段目录
       ├── inputs/              # 输入插件配置
       │   ├── snmp.conf
       │   └── system.conf
       ├── outputs/             # 输出插件配置
       │   ├── kafka.conf
       │   └── prometheus.conf
       └── processors/          # 处理器配置
           └── converter.conf
   ```

2. **配置版本标识**：
   每个配置片段包含版本元数据：
   ```toml
   # /opt/dci/dciagent/conf/telegraf.d/inputs/snmp.conf
   
   # 配置元数据（Management Agent使用）
   # @version: 2025.06.01.001
   # @generated: 2025-06-01T08:30:00Z
   # @template: huawei_s6720_basic_v2
   # @target: device-045
   
   [[inputs.snmp]]
     # 配置内容...
   ```

3. **配置同步流程**：

   ```mermaid
   sequenceDiagram
       participant Server as dcimonitor服务端
       participant MA as Management Agent
       participant FS as 文件系统
       participant Telegraf as Telegraf进程
   
       MA->>Server: 请求配置(Agent ID, 当前版本)
       Server->>Server: 检查配置是否需要更新
   
       alt 需要更新
           Server->>MA: 返回新配置片段集合
           MA->>MA: 备份当前配置
           MA->>FS: 写入新配置片段
           MA->>Telegraf: 发送SIGHUP信号
           Telegraf->>FS: 重新加载配置
           Telegraf->>MA: 状态反馈
           MA->>Server: 上报应用结果
       else 配置未变更
           Server->>MA: 返回无需更新
       end
   ```

4. **版本控制策略**：
   - 使用时间戳和序列号组合的版本标识（例如：2025.06.01.001）
   - 服务端维护配置版本历史
   - Agent本地存储最近10个版本的配置备份
   - 支持快速回滚到之前的配置版本

5. **冲突解决机制**：
   - 基于优先级解决配置片段冲突
   - 特定于设备的配置优先于通用配置
   - 手动配置可标记为"锁定"，避免自动更新覆盖
   - 配置冲突时生成警告日志并上报

6. **安全机制**：
   - 配置传输使用HTTPS加密
   - 支持配置签名验证
   - 敏感信息（如SNMP社区字符串）使用环境变量或密钥引用
   - 配置文件使用适当权限保护

### 3.1.4 配置管理流程

Management Agent管理Telegraf配置的工作流程如下：

1. MA定期调用GET /agents/{agent_id}/config获取最新配置
2. 服务端返回配置片段集合和版本号
3. MA比较本地版本与服务端版本
4. 若不同，MA备份当前配置
5. MA清空telegraf.d目录，写入新配置片段
6. MA更新本地版本记录
7. MA向Telegraf发送SIGHUP信号
8. Telegraf重新加载配置并应用
9. MA在下次心跳中上报配置应用状态

配置版本管理确保了配置的一致性和可追溯性，同时支持在配置出现问题时快速回滚。

## 3.2 服务端管理设计

### 3.2.1 服务端组件架构

dcimonitor服务端对dciagent的管理架构如下：

```mermaid
sequenceDiagram
    participant Agent as dciagent集群
    participant API as 管理API服务
    participant Registry as Agent注册表
    participant ConfigDB as 配置数据库
    participant ModuleRepo as 模块仓库
    participant Template as 配置模板库
    participant Target as 监控目标
    
    Agent->>API: 注册/心跳上报
    API->>Registry: 更新Agent状态
    
    Agent->>API: 请求配置/模块
    API->>ConfigDB: 查询配置信息
    API->>ModuleRepo: 获取模块资源
    API->>Template: 获取配置模板
    
    API->>Agent: 下发配置/模块
    
    Agent->>Target: 执行监控采集
    
    Note over API,Registry: 服务端维护Agent生命周期
    Note over API,Template: 服务端管理配置与模板
    Note over Agent,Target: Agent负责目标监控
```

### 3.2.2 服务端数据模型

服务端维护以下核心数据模型来管理dciagent及其采集对象：

1. **Agent注册表**：
```json
{
  "agent_id": "agent-001",
  "hostname": "dc1-rack3-server5",
  "ip_address": "*************",
  "location": {
    "datacenter": "DC1",
    "rack": "Rack-3",
    "region": "East"
  },
  "os_info": {
    "type": "linux",
    "distribution": "centos",
    "version": "7.9",
    "architecture": "amd64"
  },
  "modules": {
    "dciagent": {
      "version": "1.2.0",
      "status": "running",
      "last_heartbeat": "2025-05-20T15:30:45Z"
    },
    "telegraf": {
      "version": "1.34.2",
      "status": "running",
      "config_version": "20250520-112233"
    }
  },
  "monitoring_targets": ["device-045", "device-046"],
  "tags": ["production", "network", "core"],
  "created_at": "2025-01-15T08:30:00Z",
  "updated_at": "2025-05-20T15:30:45Z"
}
```

2. **监控对象映射**：
```json
{
  "mapping_id": "map-12345",
  "agent_id": "agent-001",
  "target_id": "device-045",
  "target_type": "network_switch",
  "vendor": "huawei",
  "model": "S6720",
  "collection_configs": [
    {
      "config_id": "cfg-678",
      "name": "基础监控",
      "template_id": "huawei_s6720_basic_v1",
      "priority": 100
    },
    {
      "config_id": "cfg-679",
      "name": "性能监控",
      "template_id": "huawei_s6720_perf_v2",
      "priority": 200
    }
  ],
  "effective_from": "2025-03-01T00:00:00Z",
  "status": "active"
}
```

3. **配置版本记录**：
```json
{
  "config_version": "20250520-112233",
  "agent_id": "agent-001",
  "generated_at": "2025-05-20T11:22:33Z",
  "module": "telegraf",
  "compatible_versions": ["1.33.0", "1.34.0", "1.34.2"],
  "status": "applied",
  "applied_at": "2025-05-20T11:25:10Z",
  "snippets": [
    {
      "name": "snmp_basic.conf",
      "template_id": "huawei_s6720_basic_v1",
      "target_path": "inputs/snmp_basic.conf",
      "hash": "sha256:abcdef123456..."
    }
  ],
  "targets": ["device-045", "device-046"],
  "created_by": "system"
}
```

### 3.2.3 服务端API设计

服务端提供以下API端点管理dciagent：

1. **Agent管理API**：
```
# Agent注册
POST /api/v1/agents/register

# Agent心跳与状态更新
POST /api/v1/agents/{agent_id}/heartbeat

# 获取Agent详情
GET /api/v1/agents/{agent_id}

# 列出所有Agents
GET /api/v1/agents?datacenter={dc}&status={status}

# 更新Agent标签/属性
PATCH /api/v1/agents/{agent_id}

# 注销Agent
DELETE /api/v1/agents/{agent_id}
```

2. **配置管理API**：
```
# 获取Agent配置状态
GET /api/v1/agents/{agent_id}/config

# 获取Agent配置内容
GET /api/v1/agents/{agent_id}/config/content

# 应用配置模板到Agent
POST /api/v1/agents/{agent_id}/config/apply
{
  "template_ids": ["huawei_s6720_basic_v1"],
  "parameters": {...},
  "force": false
}

# 重置Agent配置
POST /api/v1/agents/{agent_id}/config/reset
```

3. **模块管理API**：
```
# 获取Agent可用模块版本
GET /api/v1/agents/{agent_id}/modules/{module_name}/versions

# 触发模块升级
POST /api/v1/agents/{agent_id}/modules/{module_name}/upgrade
{
  "version": "1.35.0",
  "mode": "scheduled",
  "schedule_time": "2025-06-01T02:00:00Z"
}

# 获取升级历史
GET /api/v1/agents/{agent_id}/modules/{module_name}/upgrade-history
```

4. **映射管理API**：
```
# 创建Agent与监控对象映射
POST /api/v1/mappings
{
  "agent_id": "agent-001",
  "target_id": "device-047",
  "config_templates": ["huawei_s6720_basic_v1"]
}

# 获取Agent监控对象映射
GET /api/v1/agents/{agent_id}/mappings

# 获取监控对象对应的Agent
GET /api/v1/targets/{target_id}/mappings

# 更新映射
PATCH /api/v1/mappings/{mapping_id}

# 删除映射
DELETE /api/v1/mappings/{mapping_id}
```

### 3.2.4 配置分发逻辑

服务端配置分发采用基于事件的推送与基于时间的轮询相结合的方式：

1. **事件触发推送**：
   - 当管理员通过UI更新配置时触发
   - 当监控对象映射关系变更时触发
   - 当发布新版本模板时触发

2. **定时轮询同步**：
   - Agent定期(默认5分钟)检查配置版本
   - 服务端比对Agent当前配置版本与最新版本
   - 仅当版本不一致时下发完整配置

3. **分批推送策略**：
   - 按照Agent组别分批次推送配置变更
   - 先应用到测试组，验证后再应用到生产组
   - 支持按区域、按功能、按重要性分组

配置分发流程示例：

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant API as API服务
    participant ConfigDB as 配置数据库
    participant Registry as Agent注册表
    participant Agent as dciagent
    
    Admin->>API: 更新设备配置模板
    API->>ConfigDB: 存储新模板
    API->>Registry: 标记受影响的Agents
    
    loop 对每个受影响的Agent
        API->>API: 生成Agent专用配置
        API->>ConfigDB: 保存配置版本记录
    end
    
    Note over Agent,API: 定时轮询流程
    
    Agent->>API: 请求配置版本检查
    API->>ConfigDB: 获取Agent当前配置版本
    API->>API: 比对版本
    
    alt 配置需要更新
        API->>Agent: 返回新配置
        Agent->>Agent: 备份当前配置
        Agent->>Agent: 应用新配置
        Agent->>Agent: 重启服务(如需要)
        Agent->>API: 报告配置应用结果
        API->>ConfigDB: 更新配置状态
    else 配置已是最新
        API->>Agent: 确认配置为最新
    end
```

### 3.2.5 多租户与权限设计

为支持大规模部署和多团队协作，服务端实现多租户和权限管理：

1. **多租户隔离**：
   - 按业务单元划分租户空间
   - 不同租户的Agent和配置相互隔离
   - 共享模板库与模块仓库

2. **角色权限控制**：
   - 管理员：完全访问权限
   - 配置管理员：模板和配置管理权限
   - 运维人员：部署和状态监控权限
   - 只读用户：查看状态和配置权限

3. **操作审计**：
   - 记录所有管理操作
   - 支持操作回溯与责任追踪
   - 敏感操作二次授权

4. **区域隔离**：
   - 支持按地理区域划分管理范围
   - 区域管理员仅管理本区域Agent
   - 全局配置与区域特定配置分离

多租户权限管理确保在复杂多样的部署环境中，能够有序地管理不同位置、不同部门的dciagent实例，同时满足安全合规要求。

# 4 安全设计

## 4.1 API安全设计

服务端API的安全设计包括以下几个方面：

1. **传输安全**:
   - 所有API通信使用TLS 1.2+加密
   - 启用HTTP严格传输安全(HSTS)
   - 实施证书透明度(Certificate Transparency)检查

2. **认证与授权**:
   - 基于Bearer Token的API认证
   - 支持Token自动轮换
   - JWT令牌用于短期授权
   - 实施API访问速率限制

3. **数据安全**:
   - 敏感信息传输过程中加密
   - API请求与响应签名验证
   - 服务端存储的敏感信息加密保存

## 4.2 通信安全

1. **客户端证书验证**:
   - 服务端验证客户端证书有效性
   - 支持证书吊销检查
   - 证书链完整性验证

2. **通信加密强度**:
   - 使用强加密套件(TLS_AES_256_GCM_SHA384)
   - 禁用不安全密码套件
   - 支持密钥前向保密(Perfect Forward Secrecy)

# 5 测试方案

## 5.1 集成测试范围

1. **通信兼容性测试**:
   - 测试不同网络环境下的通信稳定性
   - 测试各种异常情况的容错机制
   - 验证高延迟网络下的性能表现

2. **功能测试**:
   - 配置同步与应用测试
   - 状态上报与心跳机制测试
   - 命令执行与控制测试
   - 安全机制验证

## 5.2 测试指标

1. **通信性能指标**:
   - 平均响应时间 < 200ms
   - 99%请求响应时间 < 500ms
   - 请求成功率 > 99.9%

2. **功能指标**:
   - 配置下发成功率 > 99.5%
   - 心跳丢失率 < 0.1%
   - 命令执行成功率 > 99% 