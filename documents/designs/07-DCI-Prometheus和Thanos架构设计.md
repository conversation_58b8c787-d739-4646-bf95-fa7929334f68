---
title: |
  Prometheus+Thanos架构设计

subtitle: |
  DCI数据监测系统时序数据库架构升级
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-13 | 顾铠羟 | 初始版本           |

# 1 方案概述

## 1.1 背景与目标

DCI数据监测系统目前使用TDengine作为时序数据库存储SNMP指标、Telemetry数据等时间序列数据。随着系统规模扩大和应用场景丰富，需要一个更具扩展性、生态更丰富的时序数据库解决方案。本方案旨在将系统时序数据库从TDengine调整为Prometheus+Thanos架构，同时保留现有的Telegraf作为客户端采集组件。

### 主要目标：

1. 提供更好的水平扩展能力，支持更大规模的指标数据存储和查询
2. 实现长期数据存储与降采样，优化存储成本与查询性能
3. 保持客户端采集架构稳定，最小化对现有Telegraf部署的影响
4. 提供兼容层，确保现有应用平滑过渡
5. 增强可视化和告警能力

## 1.2 适用范围

本设计文档适用于DCI数据监测系统的时序数据存储部分，包括：

1. Prometheus与Thanos服务器组件部署架构
2. 数据采集架构调整（Telegraf配置更新）
3. 查询接口适配
4. 数据保留策略
5. 高可用设计
6. 与现有系统的集成方式

## 1.3 关键指标

1. **可扩展性**：支持每秒50万以上的指标摄入
2. **查询性能**：简单查询响应时间<500ms，复杂聚合查询<3s
3. **存储效率**：通过多级降采样，优化长期数据存储空间占用
4. **高可用性**：系统整体可用性≥99.95%
5. **数据完整性**：确保所有写入的指标数据在保留期内可被查询

# 2 技术架构

## 2.1 整体架构

Prometheus+Thanos架构由多层组件构成，形成完整的时序数据处理链路：

```mermaid
graph TD
    subgraph 数据采集层
        Telegraf["Telegraf<br>(带Prometheus输出)"]
        OtherExp["其他Exporters<br>(可选)"]
    end
    
    subgraph Prometheus层
        Prom1["Prometheus实例1<br>(+Thanos Sidecar)"]
        Prom2["Prometheus实例2<br>(+Thanos Sidecar)"]
        Prom3["Prometheus实例3<br>(+Thanos Sidecar)"]
    end
    
    subgraph Thanos层
        ThQuery["Thanos Query<br>(查询联合)"]
        ThStore["Thanos Store<br>(对象存储访问)"]
        ThCompact["Thanos Compact<br>(压缩和降采样)"]
        ThRcv[Thanos Receive<br>(可选)]
    end
    
    subgraph 存储层
        ObjStore["对象存储<br>(长期数据)"]
    end
    
    subgraph 可视化与查询层
        Grafana["Grafana<br>(可视化)"]
        API["查询接口适配层"]
        Alertmanager["AlertManager<br>(告警)"]
    end
    
    Telegraf -->|metrics| Prom1
    Telegraf -->|metrics| Prom2
    Telegraf -->|metrics| Prom3
    OtherExp -->|metrics| Prom1
    OtherExp -->|metrics| Prom2
    OtherExp -->|metrics| Prom3
    
    Prom1 -->|数据上传| ObjStore
    Prom2 -->|数据上传| ObjStore
    Prom3 -->|数据上传| ObjStore
    
    Prom1 -->|GRPC| ThQuery
    Prom2 -->|GRPC| ThQuery
    Prom3 -->|GRPC| ThQuery
    ThStore -->|GRPC| ThQuery
    
    ObjStore -->|读取历史数据| ThStore
    ObjStore <-->|压缩和降采样| ThCompact
    
    Prom1 -->|告警规则| Alertmanager
    Prom2 -->|告警规则| Alertmanager
    Prom3 -->|告警规则| Alertmanager
    
    ThQuery -->|PromQL| Grafana
    ThQuery -->|PromQL| API
    Telegraf -->|可选remote_write| ThRcv
    ThRcv -->|写入| Prom1
    ThRcv -->|写入| Prom2
    ThRcv -->|写入| Prom3
```

## 2.2 关键组件

### 2.2.1 Prometheus

Prometheus是一个开源的监控与告警系统，在架构中负责：

1. **数据采集**：从暴露的端点抓取指标数据
2. **本地存储**：将采集的指标保存在本地时序数据库（TSDB）
3. **PromQL查询**：提供强大的查询语言处理时序数据
4. **告警规则评估**：根据配置的规则触发告警
5. **服务发现**：支持多种服务发现机制找到监控目标

在此架构中，Prometheus实例按功能或区域分片，每个实例负责采集特定范围的指标。初期阶段仅实现网络设备指标分片，服务器和应用指标分片将在后续阶段开发。每个Prometheus实例都附带一个Thanos Sidecar组件，负责将数据上传到对象存储并提供查询API。

### 2.2.2 Thanos Sidecar

Thanos Sidecar与Prometheus实例并行运行，提供以下功能：

1. **对象存储上传**：将Prometheus的TSDB块上传到对象存储
2. **查询API代理**：为Thanos Query提供统一的查询接口
3. **存储网关**：允许Thanos Query通过gRPC访问Prometheus数据

此组件确保本地Prometheus数据可以被全局查询和长期保存。

### 2.2.3 Thanos Store

Thanos Store Gateway是一个无状态组件，提供对对象存储中历史数据的访问：

1. 它将对象存储中的数据以Prometheus兼容的格式提供给Thanos Query
2. 处理查询时间范围内的历史数据，与实时数据无缝融合
3. 使用缓存提高查询性能

### 2.2.4 Thanos Query

Thanos Query是架构的核心查询层，负责：

1. **查询联合**：汇总来自多个Prometheus实例和Store Gateway的数据
2. **重复数据去除**：基于配置的副本标签删除重复指标
3. **提供Prometheus兼容API**：实现与Prometheus API完全兼容的查询端点

Thanos Query通过gRPC与其他Thanos组件通信，同时为客户端提供HTTP API。

### 2.2.5 Thanos Compact

Thanos Compactor负责对象存储中数据的压缩和降采样：

1. **压缩**：合并小块为大块，提高查询性能
2. **降采样**：创建5分钟、1小时和1天粒度的降采样数据，加速长时间范围查询
3. **保留策略执行**：根据配置删除过期数据

### 2.2.6 Thanos Receive（可选）

Thanos Receive提供兼容Prometheus remote_write API的接收端点：

1. 接收来自Telegraf、其他Prometheus实例或其他支持remote_write的客户端的指标
2. 将接收的指标分发给多个Prometheus实例
3. 提供额外的摄入扩展性和缓冲能力

## 2.3 数据流

以下序列图展示了数据从采集到查询的完整流程：

```mermaid
sequenceDiagram
    participant T as Telegraf
    participant P as Prometheus+Sidecar
    participant Q as Thanos Query
    participant S as Thanos Store
    participant O as 对象存储
    participant C as Thanos Compact
    participant G as Grafana/API
    
    T->>P: 暴露Prometheus格式指标
    P->>P: 抓取并存储指标(本地TSDB)
    P->>O: Sidecar上传TSDB块
    P->>P: 评估告警规则
    
    O->>C: 读取需压缩/降采样的数据
    C->>O: 写入压缩和降采样数据
    
    G->>Q: 发送PromQL查询
    Q->>P: 查询最新数据
    Q->>S: 查询历史数据
    S->>O: 读取对象存储中的数据块
    S->>Q: 返回历史数据
    P->>Q: 返回最新数据
    Q->>G: 返回合并去重的查询结果
```

# 3 实现细节

## 3.1 技术选型

### 3.1.1 核心组件

| 组件 | 版本 | 说明 |
|------|------|------|
| Prometheus | v2.45.0+ | 基础的指标采集与存储 |
| Thanos | v0.32.0+ | 分布式查询和长期存储 |
| Telegraf | 1.28+ | 数据采集Agent |
| Alertmanager | v0.26.0+ | 告警管理 |
| Grafana | 10.0.0+ | 可视化界面 暂时不部署 |
| 对象存储 | - | 阿里云OSS |

### 3.1.2 K8s相关组件

| 组件 | 用途 |
|------|------|
| StatefulSet | 部署Prometheus实例 |
| Deployment | 部署Thanos组件 |
| ConfigMap | 存储配置 |
| Secret | 存储敏感信息（如对象存储凭证） |
| Service | 服务发现和负载均衡 |
| PersistentVolume | Prometheus本地存储 |

## 3.2 核心算法

### 3.2.1 数据模型

Prometheus使用多维数据模型，每个时间序列由指标名称和一组键值对（标签）唯一标识：

```
cpu_usage_idle{host="switch01", cpu="0"} 95.2
interface_traffic_in_bytes{device="router01", interface="GigabitEthernet0/0"} 1024
```

与TDengine的表/超级表模型相比，Prometheus的标签模型提供更大的灵活性，但需要精心设计指标命名和标签体系。详细的指标命名规范将在《指标命名和标签体系规范》文档中定义。

### 3.2.2 数据保留策略

数据保留采用多层次策略：

| 存储层级 | 存储位置 | 保留期 | 数据精度 | 用途 |
|---------|---------|-------|---------|------|
| 热存储  | Prometheus本地存储 | 15天 | 原始精度 | 实时查询和告警 |
| 温存储  | 对象存储（降采样5m） | 60天 | 5分钟 | 短期趋势分析 |
| 温存储  | 对象存储（降采样1h） | 180天 | 1小时 | 中期趋势分析 |
| 冷存储  | 对象存储（降采样1d） | 5年 | 1天 | 长期趋势分析 |

### 3.2.3 对象存储集成

Thanos使用对象存储作为长期数据的存储后端，支持多种对象存储：
- 阿里云OSS
- Amazon S3
- Google Cloud Storage
- 任何S3兼容的存储服务

对象存储配置示例：
```yaml
type: ALIYUN
config:
  endpoint: "oss-cn-hangzhou-internal.aliyuncs.com"
  bucket: "dci-thanos-storage"
  prefix: "thanos/metrics"
```

访问凭证通过Kubernetes Secret安全存储。

## 3.3 接口设计

### 3.3.1 数据采集接口

Telegraf将配置为支持Prometheus格式的输出：

1. **Prometheus暴露端点**:
   ```toml
   [[outputs.prometheus_client]]
     listen = ":9273"
     metric_version = 2
   ```

2. **Remote Write输出** (可选高吞吐量场景):
   ```toml
   [[outputs.prometheus_remote_write]]
     url = "http://prometheus-receiver:9090/api/v1/write"
     [outputs.prometheus_remote_write.basic_auth]
       username = "${PROM_USERNAME}"
       password = "${PROM_PASSWORD}"
   ```

### 3.3.2 查询接口

Thanos Query提供与Prometheus兼容的HTTP API：

- 即时查询: `/api/v1/query`
- 范围查询: `/api/v1/query_range`
- 元数据查询: `/api/v1/metadata`
- 系列列表: `/api/v1/series`
- 标签查询: `/api/v1/labels` 和 `/api/v1/label/<name>/values`

### 3.3.3 查询适配层

为了确保现有应用平滑过渡，将开发适配层将SQL查询转换为PromQL：

```mermaid
graph LR
    App[应用] -- SQL --> Adapter[SQL到PromQL适配器]
    Adapter -- PromQL --> Thanos[Thanos Query]
    Thanos -- 结果 --> Adapter
    Adapter -- SQL结果格式 --> App
```

适配层将支持：
- 基本的SELECT查询转换为PromQL
- 数据类型兼容性处理
- 查询结果格式转换
- 错误码映射

## 3.4 数据迁移策略

数据迁移将采用双写双读策略，确保平滑过渡：

1. **阶段1 - 准备**：
   - 部署Prometheus+Thanos基础架构
   - 配置Telegraf双输出（TDengine + Prometheus）

2. **阶段2 - 并行运行**：
   - 启用应用对新架构的只读访问
   - 验证数据一致性和查询性能

3. **阶段3 - 切换**：
   - 应用切换到通过适配层访问Prometheus
   - 保留TDengine作为备份一段时间

4. **阶段4 - 完成**：
   - 停用TDengine写入
   - 根据需要保留历史数据或完全迁移

# 4 部署架构

## 4.1 部署拓扑

基于Kubernetes的部署拓扑：

```mermaid
graph TD
    subgraph K8s集群
        subgraph 查询层
            ThQ1[Thanos Query Pod 1]
            ThQ2[Thanos Query Pod 2]
            ThQ3[Thanos Query Pod 3]
            QLB[Query Service/LB]
        end
        
        subgraph 存储层
            ThS1[Thanos Store Pod 1]
            ThS2[Thanos Store Pod 2]
        end
        
        subgraph Prometheus层
            PS1[Prometheus StatefulSet 1<br>网络设备指标]
        end
        
        subgraph 工具层
            ThC[Thanos Compact Pod]
            AM[Alertmanager Pod]
            Grafana[Grafana Pod]
            Adapter[SQL适配器]
        end
    end
    
    subgraph 外部存储
        ObjS[对象存储]
    end
    
    subgraph 采集端
        Tel1[Telegraf 1]
        Tel2[Telegraf 2]
        TelN[Telegraf n...]
    end
    
    Tel1 & Tel2 & TelN -->|暴露指标| PS1
    PS1 -->|上传数据| ObjS
    ObjS -->|读取历史数据| ThS1 & ThS2
    ObjS <-->|压缩和降采样| ThC
    
    PS1 & ThS1 & ThS2 -->|提供查询| ThQ1 & ThQ2 & ThQ3
    ThQ1 & ThQ2 & ThQ3 --- QLB
    
    QLB -->|查询| Grafana & Adapter
    PS1 -->|告警规则| AM
```

### 功能分片示例：

| StatefulSet | 责任范围 | 资源分配 |
|------------|---------|---------|
| prometheus-network | 网络设备指标 | 4CPU, 16GB RAM, 500GB存储 |
| prometheus-server【暂不开发】 | 服务器指标 | 4CPU, 16GB RAM, 500GB存储 |
| prometheus-application【暂不开发】 | 应用指标 | 4CPU, 16GB RAM, 500GB存储 |

## 4.2 容量规划

### 4.2.1 存储需求估算

基于以下假设进行容量规划：
- 活跃时间序列数量：200万
- 每个样本大小：约2字节（压缩后）
- 采样间隔：30秒
- 每秒样本摄入量：约67K

**存储估算**（仅针对网络设备指标，服务器和应用指标分片暂不实施）：
- 热存储（15天）：约170GB原始数据/实例
- 温存储（降采样5m，60天）：约20GB/实例
- 温存储（降采样1h，180天）：约15GB/实例
- 冷存储（降采样1d，5年）：约30GB/实例

### 4.2.2 计算资源需求

| 组件 | 副本数 | CPU请求 | 内存请求 | 说明 |
|------|-------|---------|---------|------|
| Prometheus | 1 | 4 | 16GB | 仅部署网络设备指标分片 |
| Thanos Query | 3 | 2 | 8GB | 负载均衡 |
| Thanos Store | 2 | 2 | 8GB | 处理历史查询 |
| Thanos Compact | 1 | 2 | 8GB | 单实例 |
| Alertmanager | 3 | 1 | 2GB | 高可用集群 |
| Grafana | 2 | 1 | 2GB | 高可用 |
| 适配层 | 2 | 1 | 2GB | 高可用 |

## 4.3 高可用设计

Prometheus+Thanos架构的高可用设计涵盖多个层面：

1. **数据采集高可用**：
   - Prometheus实例通过功能分片和重复采集保证数据收集可靠性
   - 使用多个Telegraf实例提供冗余数据源

2. **存储高可用**：
   - Prometheus本地存储使用持久卷保证Pod重启数据不丢失
   - 对象存储配置适当的冗余和备份策略
   - 定期验证对象存储中数据的可恢复性

3. **查询高可用**：
   - 多个Thanos Query实例后接负载均衡
   - 查询失败自动重试和故障转移
   - 避免单点故障的组件设计

4. **告警高可用**：
   - Alertmanager集群模式部署（至少3节点）
   - 去重逻辑避免告警风暴

# 5 运维与监控

## 5.1 监控指标

关键需要监控的指标：

1. **Prometheus健康指标**:
   - `up{job="prometheus"}` - Prometheus实例状态
   - `prometheus_tsdb_head_samples_appended_total` - 样本摄入率
   - `rate(prometheus_tsdb_compactions_total[1h])` - 压缩操作
   - `prometheus_tsdb_storage_blocks_bytes` - 存储使用量

2. **Thanos健康指标**:
   - `up{job="thanos-*"}` - Thanos组件状态
   - `thanos_objstore_bucket_operation_failures_total` - 对象存储操作失败
   - `thanos_query_gate_queries_in_flight` - 查询并发量
   - `thanos_compact_group_compactions_total` - 压缩操作总数

3. **查询性能指标**:
   - `rate(thanos_query_api_request_duration_seconds_sum[5m]) / rate(thanos_query_api_request_duration_seconds_count[5m])` - 平均查询延迟
   - `histogram_quantile(0.99, rate(thanos_query_api_request_duration_seconds_bucket[5m]))` - 99分位查询延迟

## 5.2 告警策略

建议的关键告警配置：

1. **基础设施告警**：
   ```yaml
   - alert: PrometheusDown
     expr: up{job="prometheus"} == 0
     for: 5m
     labels:
       severity: critical
     annotations:
       summary: "Prometheus实例不可用"
       description: "Prometheus实例{{$labels.instance}}已经不可用超过5分钟"
   ```

2. **存储告警**：
   ```yaml
   - alert: PrometheusHighStorageUsage
     expr: (prometheus_tsdb_storage_blocks_bytes / prometheus_tsdb_storage_blocks_bytes_max) > 0.85
     for: 10m
     labels:
       severity: warning
     annotations:
       summary: "Prometheus存储空间不足"
       description: "Prometheus实例{{$labels.instance}}存储使用率超过85%"
   ```

3. **查询性能告警**：
   ```yaml
   - alert: HighQueryLatency
     expr: histogram_quantile(0.99, rate(thanos_query_api_request_duration_seconds_bucket[5m])) > 10
     for: 15m
     labels:
       severity: warning
     annotations:
       summary: "Thanos查询延迟过高"
       description: "Thanos查询99分位延迟超过10秒，已持续15分钟"
   ```

## 5.3 日常维护

主要维护操作包括：

1. **Prometheus配置更新**：
   - 使用ConfigMap更新配置
   - 利用`/-/reload` HTTP端点热加载配置
   - 使用版本控制管理配置变更

2. **存储维护**：
   - 定期监控存储使用量
   - 验证数据保留策略执行情况
   - 检查对象存储健康状态

3. **版本升级**：
   - 遵循滚动升级策略
   - 先升级非关键组件（如Thanos Store）
   - 后升级关键组件（如Prometheus）
   - 每次升级后进行全面测试

# 6 安全设计

## 6.1 数据安全

1. **加密策略**：
   - 传输加密（TLS）
   - 对象存储服务端加密
   - K8s Secret加密

2. **认证与授权**：
   - 基于角色的访问控制（RBAC）
   - API访问令牌
   - 细粒度权限控制

3. **数据隔离**：
   - 租户标签
   - 查询时的租户过滤器
   - 适配层权限控制

## 6.2 网络安全

1. **网络策略**：
   - 限制Pod间通信
   - 明确定义入站/出站规则
   - 隔离监控网络

2. **暴露服务安全**：
   - 内部组件不直接暴露到外网
   - 仅通过API网关暴露必要服务
   - 为外部接口启用TLS和认证

# 7 实施计划

## 7.1 实现步骤

| 阶段 | 任务 | 时间估计 | 依赖 |
|------|------|---------|------|
| 准备 | 完成指标命名和标签体系规范设计 | 1周 | 无 |
| 准备 | 设计Telegraf输出配置调整方案 | 1周 | 无 |
| 部署 | 部署Prometheus网络设备指标分片 | 2周 | 指标命名规范 |
| 部署 | 部署Thanos组件 | 2周 | Prometheus部署 |
| 部署 | 配置AlertManager | 1周 | Prometheus部署 |
| 部署 | 配置Grafana | 1周 | Thanos部署 |
| 集成 | 更新Telegraf配置支持Prometheus输出 | 2周 | Telegraf配置设计 |
| 集成 | 开发SQL到PromQL适配层 | 3周 | Thanos部署 |
| 测试 | Telegraf与Prometheus集成测试 | 2周 | Telegraf配置更新 |
| 测试 | 查询接口适配层测试 | 2周 | 适配层开发 |
| 测试 | 全面系统集成测试 | 2周 | 所有组件部署 |
| 迁移 | 应用双写双读策略 | 2周 | 集成测试通过 |
| 迁移 | 切换应用到新架构 | 2周 | 双写双读验证 |
| 迁移 | 旧系统数据备份与下线 | 2周 | 应用切换完成 |

# 8 测试方案

## 8.1 测试策略

1. **组件测试**：验证每个组件的基本功能
2. **集成测试**：验证组件间的交互
3. **端到端测试**：验证完整数据流
4. **性能测试**：验证系统在负载下的表现
5. **故障恢复测试**：验证高可用设计

## 8.2 测试用例

| 测试类型 | 测试用例 | 验收标准 |
|---------|---------|---------|
| 功能测试 | Telegraf生成Prometheus格式指标 | 指标符合命名规范并被成功抓取 |
| 功能测试 | Prometheus抓取和存储指标 | 指标可在Prometheus UI查询 |
| 功能测试 | Thanos Query联合查询 | 可查询所有Prometheus实例数据 |
| 功能测试 | Thanos Compact降采样 | 长时间范围查询使用降采样数据 |
| 功能测试 | 查询适配层转换SQL | SQL查询正确转换为PromQL并返回结果 |
| 性能测试 | 高并发写入测试 | 支持每秒50万指标写入 |
| 性能测试 | 长时间范围查询 | 1年数据范围查询<10秒完成 |
| 高可用测试 | Prometheus实例故障 | 数据收集和查询不中断 |
| 高可用测试 | Thanos Query实例故障 | 查询服务不中断 |
| 恢复测试 | 从对象存储恢复数据 | 数据完整恢复 |

# 9 风险与应对

| 风险点 | 影响程度 | 应对措施 |
|--------|---------|---------|
| Telegraf生成的指标格式不一致 | 高 | 制定严格的指标命名规范，提供转换工具 |
| 查询性能不如TDengine | 中 | 优化查询，实施有效的降采样策略，增加缓存 |
| 对象存储成本超出预期 | 中 | 调整数据保留策略，优化压缩比例 |
| SQL到PromQL转换不完整 | 高 | 先实现核心查询功能，增量扩展复杂查询支持 |
| 迁移过程中数据不一致 | 高 | 双写双读策略，开发数据验证工具 |
| 系统资源消耗高于预期 | 中 | 逐步扩容，实施资源使用监控和告警 |
| 操作复杂度提高 | 低 | 提供完善的操作文档，实施自动化运维 |

# 10 附录

## 10.1 参考文档

- [Prometheus官方文档](https://prometheus.io/docs/introduction/overview/)
- [Thanos官方文档](https://thanos.io/tip/thanos/getting-started.md/)
- [Telegraf Prometheus输出插件文档](https://github.com/influxdata/telegraf/tree/master/plugins/outputs/prometheus_client)
- [Grafana与Prometheus集成指南](https://grafana.com/docs/grafana/latest/datasources/prometheus/)
- [Kubernetes StatefulSet文档](https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/)

## 10.2 配置示例

### 5.2.1 Prometheus StatefulSet示例

```yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: prometheus-network
  namespace: monitoring
spec:
  serviceName: "prometheus-network"
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
      prometheus: network
  template:
    metadata:
      labels:
        app: prometheus
        prometheus: network
    spec:
      serviceAccountName: prometheus
      containers:
      - name: prometheus
        image: prom/prometheus:v2.45.0
        args:
          - "--config.file=/etc/prometheus/prometheus.yaml"
          - "--storage.tsdb.path=/prometheus"
          - "--storage.tsdb.retention.time=15d"
          - "--web.enable-lifecycle"
          - "--web.enable-admin-api"
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus
        - name: prometheus-storage
          mountPath: /prometheus
        resources:
          requests:
            memory: "8Gi"
            cpu: "2"
          limits:
            memory: "16Gi"
            cpu: "4"
      - name: thanos-sidecar
        image: thanosio/thanos:v0.32.0
        args:
        - "sidecar"
        - "--tsdb.path=/prometheus"
        - "--prometheus.url=http://localhost:9090"
        - "--objstore.config-file=/etc/thanos/objstore.yaml"
        ports:
        - containerPort: 10901
          name: grpc
        - containerPort: 10902
          name: http
        volumeMounts:
        - name: prometheus-storage
          mountPath: /prometheus
        - name: thanos-objstore
          mountPath: /etc/thanos
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-network-config
      - name: thanos-objstore
        secret:
          secretName: thanos-objstore
  volumeClaimTemplates:
  - metadata:
      name: prometheus-storage
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 500Gi
```

### 5.2.2 Thanos Query Deployment示例

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: thanos-query
  namespace: monitoring
spec:
  replicas: 3
  selector:
    matchLabels:
      app: thanos-query
  template:
    metadata:
      labels:
        app: thanos-query
    spec:
      containers:
      - name: thanos-query
        image: thanosio/thanos:v0.32.0
        args:
        - "query"
        - "--grpc-address=0.0.0.0:10901"
        - "--http-address=0.0.0.0:10902"
        - "--store=prometheus-network-0.prometheus-network:10901"
        - "--store=prometheus-server-0.prometheus-server:10901"
        - "--store=prometheus-application-0.prometheus-application:10901"
        - "--store=thanos-store-0.thanos-store:10901"
        - "--store=thanos-store-1.thanos-store:10901"
        - "--query.replica-label=replica"
        ports:
        - name: grpc
          containerPort: 10901
        - name: http
          containerPort: 10902
        resources:
          requests:
            memory: "4Gi"
            cpu: "1"
          limits:
            memory: "8Gi"
            cpu: "2"
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: http
        readinessProbe:
          httpGet:
            path: /-/ready
            port: http
```

## 5.3 对象存储配置示例

```yaml
type: ALIYUN
config:
  endpoint: "oss-cn-hangzhou-internal.aliyuncs.com"
  bucket: "dci-thanos-storage"
  prefix: "thanos/metrics"
  access_key_id: "${OSS_ACCESS_KEY}"
  access_key_secret: "${OSS_SECRET_KEY}"
``` 