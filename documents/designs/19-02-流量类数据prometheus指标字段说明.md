# DCI 流量类数据 Prometheus 指标说明

本文档旨在详细说明 DCI 监控系统中与网络设备流量相关的 Prometheus 指标：原始计数器指标和派生的速率指标。

## 1. 核心指标概览

主要有以下两组指标：

-   **原始计数器 (Counter):**
    -   `dci_snmp_flow_ifHCInOctets`: 从网络设备 SNMP 采集的接口入方向高精度（64位）字节计数器。数值随时间单调递增。
    -   `dci_snmp_flow_ifHCOutOctets`: 从网络设备 SNMP 采集的接口出方向高精度（64位）字节计数器。数值随时间单调递增。

-   **速率指标 (Rate):**
    -   `dci_snmp_flow_ifHCInOctets_rate`: 通过 Prometheus 记录规则（Recording Rule）对 `dci_snmp_flow_ifHCInOctets` 计算其在5分钟窗口内的平均增长速率得出的指标。
    -   `dci_snmp_flow_ifHCOutOctets_rate`: 同理，根据 `dci_snmp_flow_ifHCOutOctets` 计算得出的出方向平均速率。

### 示例数据

prometheus查询地址：http://dciprometheus.intro.citic-x.com:30006/

**速率指标 (`_rate`)**
```
dci_snmp_flow_ifHCInOctets_rate{agent_id="dci-agent", agent_ip="127.0.0.1", app="dcimonitor-flowdata", cluster="dci-monitoring", device_id="210", device_ip="************", device_name="骏豪26测试-华为交换机1", ifHighSpeed="10000", ifName="10GE1/0/1", if_index="11", instance="dcimonitor-flowdata-service.dci.svc:9090", interface_speed="10G", job="dci-snmp-rate-rules", kubernetes_namespace="dci", kubernetes_service_name="dcimonitor-flowdata-service", logic_port_id="443", port_id="1107", port_name="10GE1/0/1", unit="bps", vni_id="6005002"}
```

**原始计数器**
```
dci_snmp_flow_ifHCInOctets{agent_id="dci-agent", agent_ip="127.0.0.1", app="dcimonitor-flowdata", cluster="dci-monitoring", device_id="210", device_ip="************", device_name="骏豪26测试-华为交换机1", ifHighSpeed="10000", ifName="10GE1/0/1", if_index="11", instance="dcimonitor-flowdata-service.dci.svc:9090", interface_speed="10G", job="dcimonitor-flowdata", kubernetes_namespace="dci", kubernetes_service_name="dcimonitor-flowdata-service", logic_port_id="443", port_id="1107", port_name="10GE1/0/1", vni_id="6005002"}
```

## 2. 标签详解

以下表格详细解释了上述指标中每个标签的含义。

| 标签名 (Label)              | 示例值 (Example)                             | 说明 (Description)                                                                                                                              |
| ----------------------------- | -------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------- |
| `agent_id`                    | `dci-agent`                                  | 采集该数据的DCI Agent的唯一标识。                                                                                                                |
| `agent_ip`                    | `127.0.0.1`                                  | 采集该数据的DCI Agent的IP地址。                                                                                                                  |
| `app`                         | `dcimonitor-flowdata`                        | 暴露该指标的Kubernetes应用标签，指向`dcimonitor-flowdata`服务。                                                                                    |
| `cluster`                     | `dci-monitoring`                             | Prometheus的外部标签，标识该指标所属的监控集群。                                                                                                 |
| `device_id`                   | `210`                                        | 被监控设备在DCI系统中的唯一ID。查询mysql `dci_device`表获取。                                                                                                   |
| `device_ip`                   | `************`                               | 被监控设备的管理IP地址。                                                                                                                         |
| `device_name`                 | `骏豪26测试-华为交换机1`                       | 被监控设备的名称，通过`device_ip`查询mysql表获取。                                                                                                   |
| `ifHighSpeed`                 | `10000`                                      | 接口的高精度速率，单位为Mbps（兆比特每秒）。`10000` 表示 10Gbps。                                                                                   |
| `ifName`                      | `10GE1/0/1`                                  | 接口在设备上的原始名称，由SNMP Agent上报。此名称可能包含子接口或VNI信息（例如 `10GE1/0/1.6005002`）。                                           |
| `if_index`                    | `11`                                         | 接口的SNMP索引（`ifIndex`），是在设备上唯一标识一个接口的数字。                                                                                    |
| `instance`                    | `dcimonitor-flowdata-service.dci.svc:9090`   | Prometheus抓取的目标实例地址。                                                                                                                  |
| `interface_speed`             | `10G`                                        | 接口速率的人性化可读标识。                                                                                                                      |
| `job`                         | `dcimonitor-flowdata` / `dci-snmp-rate-rules` | Prometheus的抓取任务名称。原始指标的`job`为`dcimonitor-flowdata`，速率指标的`job`是由记录规则设置的`dci-snmp-rate-rules`。                       |
| `kubernetes_namespace`        | `dci`                                        | 暴露指标的Pod所在的Kubernetes命名空间。                                                                                                          |
| `kubernetes_service_name`     | `dcimonitor-flowdata-service`                | 暴露指标的Kubernetes服务名称。                                                                                                                  |
| `logic_port_id`               | `443`                                        | DCI业务逻辑端口ID，用于关联业务信息，查询mysql `dci_logic_port_device`表获取。                                                                                             |
| `port_id`                     | `1107`                                       | 接口在DCI系统中的唯一数据库ID，对应`dci_logic_port_device`表的主键。                                                                              |
| `port_name`                   | `10GE1/0/1`                                  | 查询mysql `dci_logic_port_device`表的port字段获取。                                                                                       |
| `unit`                        | `bps`                                        | 指标值的单位。仅在速率指标（`_rate`）上出现，表示比特/秒（bits per second）。                                                                     |
| `vni_id`                      | `6005002`                                    | 接口关联的VNI，与Mysql `dci_node_business`表的`vni_id`字段相一致。                                                              |

查询指标的查询索引：device_id+port_id+logic_port_id+vni_id 四个都要匹配。