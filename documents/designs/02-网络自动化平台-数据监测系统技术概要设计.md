---
title: |
  DCI数据监测系统

subtitle: |
  技术概要设计
---

# 版本记录


| 版本 | 日期       | 作者 | 修订说明             |
| ---- | ---------- | ---- | -------------------- |
| V1.0 | 2025-03-06 | 顾铠羟 | 初始版本         |
| V2.0 | 2025-04-30 | 顾铠羟 | 细化各模块技术方案，明确技术栈，里程碑  |
| V3.0 | 2025-05-12 | 顾铠羟 | 统一术语，完善架构设计，更新客户端部署方式，删里程碑 |
| V3.1 | 2025-05-16 | 顾铠羟 | 明确客户端组件基于操作系统直接安装，详见15号文档 |
| V3.2 | 2025-05-19 | 顾铠羟 | 补充网络架构设计与网络拓扑图 |
| V3.3 | 2025-05-28 | 顾铠羟 | 调整自动化任务监控流程、拓扑相关流程 |

# 1 文档介绍

## 1.1 文档目的

DCI监测系统技术设计方案，可作为产品设计、项目开发、测试的参考依据。

## 1.2 文档范围

DCI日志及网络状态监测相关设计内容，包括数据的采集、**处理（包括但不限于ID转换、格式化、清洗）**、存储（本系统负责指标/日志/事件存储，拓扑由多云管理系统负责）、以及拓扑数据的发送。

## 1.3 网络环境背景

DCI项目整体部署于中信阿里云的 Kubernetes (K8s) 集群环境中。本数据监测系统的所有服务端组件均作为K8s Pod运行，并通过K8s服务发现机制进行内部通信。对于需要从集群外部访问的服务（例如，Management Agent连接`MgmtAPI`，或外部系统访问Kafka），则依赖阿里云SLB和Ingress进行暴露和路由。特别地，Kafka集群的`advertised.listeners`需配置为外部可达的域名和端口，以确保Agent及其他外部消费者/生产者的正常连接。

测试环境网络环境背景具体见《00-DCI项目网络环境说明》。

## 1.4 部署环境说明

系统采用混合部署模式：

1. **客户端组件（Agent 端）**：
   - 采用**基于操作系统直接安装**方式部署，不依赖 Kubernetes 或 Docker
   - 包括 dciagent（Management Agent）和 Telegraf 等采集组件
   - 具体部署架构和实现细节请参考《15-DCI-dciagent中Telegraf部署架构与流程》

2. **服务端组件**：
   - 采用 Kubernetes 部署
   - 包括 dcimonitor 服务、Kafka、Prometheus 等组件

这种混合部署方式能更好地适应复杂的网络环境和客户端部署限制，同时保持服务端的可扩展性和高可用。

# 2 建设目标

*   **观测平台**
    *   高效采集与定位：实现高效采集，能在分钟级完成故障定位发现，及时掌握网络运行状态。
    *   自定义采集与分析：实现多源数据采集，可自定义采集方式和分析过程，定义监控指标，支持基线检查。
    *   实时分析处理：能灵活配置解析规则，对数据流进行实时分析和处理，及时发现异常情况。
    *   可视化大盘和报表：提供可视化大盘和报表功能，直观展示网络运行数据和状态 (拓扑图由**多云管理系统**负责)，便于用户进行监控和决策。
    *   自动化任务协同：监控网络自动化任务影响，生成分析报告。
    *   数据服务化：为授权租户提供数据访问接口。

# 3 总体设计

## 3.1 总体架构设计 (分布式 Agent + 远程管理)

本架构采用分布式部署的客户端组件 (包含采集器和管理代理)，由 `dcimonitor` 服务端进行远程集中管理。该 C/S 架构允许部署多个客户端实例，以适应大规模采集对象（如大量交换机进行分布式采集）的监控需求，并能应对复杂的网络环境（需要 Agent 作为数据转发跳板的场景）。服务端的关键数据处理和存储组件采用高可用 (HA) 集群部署模式，以满足可靠性要求。数据流经 Kafka 解耦后，由 Prometheus 处理时序指标，Elasticsearch 处理日志/事件/流记录。本系统处理后的拓扑数据（执行 LLDP 标识符到数据库 ID 的转换）被发送给**多云管理系统**，MySQL 存储本系统的管理元数据及业务关联信息。网络拓扑的可视化展示、图数据存储及数据丰富均由**多云管理系统**负责。

系统设计确保所有监控数据尽可能地关联到 DCI 数据库中定义的设备 ID (`dci_device.id`) 和逻辑端口 ID (`dci_logic_port.id`)，以保证跨系统的数据一致性和关联性。

一期目标：完成完整的高可用数据流链路部署，实现对至少一种交换机的日志和网络状态的采集 (总索引基于 Mysql数据库 device ID)、处理、存储和展示，实现基于 LLDP 的基础网络拓扑数据处理（索引 设备ID 转换）与发送给**多云管理系统**，并实现对至少一种交换机的网络自动化任务的监控 (基于Mysql数据库 ID)，生成任务报告（待细化或暂定文本模式），完成最简闭环。

### 3.1.0 网络架构设计

数据监测系统采用分层网络架构设计，Agent+K8S服务端集群模式适应复杂的网络环境。

#### 3.1.0.1 网络分区

系统网络架构分为三个主要区域：

* **服务端区域**：K8S集群内部署的全部服务组件，通过服务发现通信
* **采集端区域**：部署在可访问被监控设备网络的dciagent与Telegraf及其他采集组件
* **接入区域**：提供安全的服务接入点，通过阿里云SLB和Ingress实现

#### 3.1.0.2 核心通信路径

* **采集通信**：设备 → 采集端(SNMP/Syslog) → Kafka(TLS加密)
* **管理通信**：dciagent → 管理服务API(HTTPS)
* **外部集成**：自动化系统 → API网关(HTTPS)，处理后拓扑数据 → 多云管理系统
* **内部通信**：基于K8S服务发现机制，Kafka作为核心数据总线

#### 3.1.0.3 网络安全设计

系统网络安全设计基于以下核心机制：

* **传输加密**：所有外部通信强制TLS加密，确保数据传输安全
* **访问控制**：API网关和Kafka采用身份验证和授权机制
* **最小权限**：K8S网络策略实施服务间最小权限通信
* **安全监控**：记录关键连接事件，异常行为告警

### 3.1.1 核心架构图

```mermaid
graph TB
    %% DCI项目K8S集群内的子系统
    subgraph DCI项目K8S集群
        subgraph 网络自动化控制系统
            NetAutoCtrl(自动化控制服务)
        end
        
        subgraph 多云管理系统
            MultiCloud(拓扑展示/图数据库)
        end
        
        subgraph 数据监测系统
            subgraph 核心服务
                TaskMon(自动化任务监控服务)
                TopoProc(拓扑数据处理服务)
                MgmtAPI(远程管理API)
                AlertEngine(告警引擎)
            end
            
            subgraph 消息队列
                Kafka[(Kafka)]
            end
            
            subgraph 存储层
                TSDB[(时序数据库<br/>Prometheus/Thanos)]
                ES[(日志/事件<br/>Elasticsearch)]
                MySQL[(元数据<br/>MySQL)]
            end
            
            subgraph 展示层
                UI(监控大屏/UI)
            end
        end
    end

    subgraph 客户端
        Telegraf(Telegraf采集组件)
        MgmtAgent(dciagent管理代理)
        MgmtAgent --- Telegraf
    end

    subgraph 网络设备
        Switches(交换机/路由器)
    end

    %% 数据流（使用直线连接）
    Switches --- Telegraf
    Telegraf --- Kafka
    MgmtAgent --- MgmtAPI
    
    Kafka --- TSDB
    Kafka --- ES
    Kafka --- TopoProc
    
    TopoProc --- MySQL
    TopoProc --- MultiCloud
    
    NetAutoCtrl --- TaskMon
    TaskMon --- MySQL
    TaskMon --- TSDB
    TaskMon --- ES
    
    UI --- TSDB
    UI --- ES
    UI --- MySQL
    
    AlertEngine --- TSDB
    AlertEngine --- ES

    %% 样式定义
    style Telegraf fill:#add,stroke:#333,stroke-width:1px
    style MgmtAgent fill:#dda,stroke:#333,stroke-width:1px
    style MgmtAPI fill:#fcc,stroke:#333,stroke-width:1px
    style TaskMon fill:#fcc,stroke:#333,stroke-width:1px
    style TopoProc fill:#cff,stroke:#333,stroke-width:1px
    style AlertEngine fill:#fcc,stroke:#333,stroke-width:1px
    style TSDB fill:#f9d,stroke:#333,stroke-width:2px,shape:cylinder
    style ES fill:#ccf,stroke:#333,stroke-width:2px,shape:cylinder
    style MySQL fill:#fce,stroke:#333,stroke-width:2px,shape:cylinder
    style Kafka fill:#dfd,stroke:#333,stroke-width:2px,shape:cylinder
    style MultiCloud fill:#d9f,stroke:#333,stroke-width:1px
    style NetAutoCtrl fill:#eee,stroke:#333,stroke-width:1px
```

## 3.2 功能设计

### 3.2.1 数据采集层

本层（Agent层）由多个独立部署的客户端组件实例构成，每个实例通常包含一个或多个数据采集器和一个管理代理。

#### 3.2.1.1 客户端组件

系统将在需要进行数据采集的多个位置部署客户端组件实例。是采集器 + 管理代理的多实例部署，每个实例负责监控其覆盖范围内的网络设备或作为数据中继点。

**数据采集组件:**

负责实际的数据采集工作。每个客户端实例上可以运行一个或多个此类组件，具体类型和数量由该实例的监控任务决定。一期中包含标准的 Telegraf 实例，后续可扩展支持其他类型。其基础配置极简，核心配置由配对的 `Management Agent` 动态管理。

*   **Agent端 Telegraf:**
    *   `outputs.kafka`: 配置其数据路由到 Kafka 不同 Topic。
    *   `inputs`: 根据服务端下发的配置动态加载具体的输入插件和参数。例如，`inputs.snmp` 的配置包含目标设备的 IP/主机名以及需要采集的 OID 列表，其中涉及端口的指标（如接口计数器）通过配置指定 NMS 提供的端口 ID 或索引来确定采集目标。

**Management Agent (Go应用):**

*   **核心职责:** 在每个客户端实例上运行，作为服务端的代理，负责在本机管理该实例上的一个或多个数据采集组件的生命周期和配置。

*   **具体职责:**
    *   **Agent注册:** 每个 Management Agent 实例启动时向 `dcimonitor` 的 `MgmtAPI` 注册，获取唯一的 ClientID。
    *   **心跳/状态上报:** 定期向服务端发送心跳，报告自身状态以及其管理的本地采集组件的运行状态。
    *   **拉取配置:** 定期轮询 `MgmtAPI` 获取分配给本 Agent 实例的最新采集组件配置。
    *   **应用配置:** 将配置应用到本地相应的采集组件，并通知其重载。
    *   **拉取命令:** 定期轮询 `MgmtAPI` 获取针对本实例上特定采集组件的待执行命令。
    *   **执行命令:** 调用系统命令或管理接口来管理本地受管采集组件的生命周期。
    *   **日志上报:** 发送自身及本地受管组件的关键日志或错误信息给服务端。

*   **架构考虑:** `Management Agent` 的设计具备可扩展性，支持管理多种类型的采集器。

*   **部署:** `Management Agent` 通常与其管理的采集组件同机部署，形成一个客户端实例。系统支持在大量节点上部署这样的客户端实例，以实现：
    *   可扩展性: 通过增加 Agent 实例数量来覆盖更多监控对象。
    *   网络适应性: 在无法直接访问监控对象的网络区域部署 Agent 作为数据采集代理或跳板。

#### 3.2.1.2 多源数据采集支持

*   SNMP: 采集设备健康、接口统计、接口状态、**LLDP邻居信息 (用于拓扑)** 等。采集目标（设备、端口）基于 NMS 统一 ID 进行配置和识别。SNMP采集器设计见《14-DCI-dciagent中Telegraf数据采集方案设计》、《11-DCI-Telegraf-SNMP采集实现方案》。
*   Syslog: 采集设备事件、状态变化、配置变更日志等。日志内容解析时提取并关联 NMS 设备 ID 和端口 ID。
*   sFlow/NetFlow: 采集详细流量记录，用于流量分析。
*   Telemetry: (未来扩展) 高频指标推送。
*   **其他:** (未来扩展) JMX, APM, BMP, BGP-LS 等。

#### 3.2.1.3 自定义与预处理

*   服务端集中配置: 通过 `dcimonitor` 服务端的 `MgmtAPI` 和 `ConfigStore` (MySQL) 实现对客户端 Telegraf 采集策略的集中配置与动态下发。
*   Agent 端预处理（优先级低）: 该部分考量的出发点是节省数据采集的带宽占用。Telegraf 本身支持部分 Processor 插件进行过滤、转换、打标签等预处理。复杂的预处理仍在服务端数据处理层进行。

#### 3.2.1.4 Agent端核心设计概要
Agent 端主体为 dciagent， dciagent 是 DCI 数据监测系统的 Agent 端，涵盖Telegraf等各种采集组件。由轻量级的 `Management Agent (MA)` (Go 语言实现) 和采集模块构成，其中采集模块包括标准的 `Telegraf Agent (TA)` 。MA 作为服务端的代理，负责通过 HTTPS 与服务端 `MgmtAPI` 通信，进行注册认证、上报心跳与状态、拉取并应用动态的 Telegraf 配置片段（存放于 `telegraf.d` 目录），以及管理本机 `Telegraf Agent` 的生命周期。`Telegraf Agent` 则依据 MA 管理的配置执行具体的数据采集任务，并将数据发送至 Kafka。

客户端（本设计中涉及到的 dciagent、Management Agent、Telegraf）基于操作系统直接安装部署，不依赖Kubernetes或Docker，未来可能兼容K8S/Docker方式。服务端（Prometheus、Kafka、Management Agent等）基于K8S部署。

相关工具开发采用Go语言实现，使用模块化设计，除Telegraf等采集模块外，采用统一的子命令结构而非独立二进制，使用配置文件替代命令行参数，提供更好的灵活性。

*   具体设计见《15-DCI-dciagent客户端架构设计》系列文档。

### 3.2.2 数据处理层

#### 3.2.2.1 服务端组件

服务端由多个核心组件构成，它们协同工作，提供数据处理、存储和API服务：

*   **API服务 (API_Orig - Go Web服务):**
    *   **具体职责:**
        *   **RESTful API:** 提供统一的HTTP接口，供前端、第三方系统访问数据和功能。
        *   **数据格式化:** 将内部数据结构转换为统一的API响应格式。
        *   **错误处理:** 统一的错误响应机制，确保客户端获得明确的错误信息。
    *   **架构考虑:** 
        *   模块化设计，易于扩展新的API端点。
        *   多实例部署，支持负载均衡。
        *   通过中间件实现横切关注点（如日志、指标收集）。

*   **管理API服务 (MgmtAPI - Go Web服务):**
    *   **具体职责:**
        *   **Agent管理:** 处理客户端Agent的注册请求，分配唯一ClientID。
        *   **配置管理:** 提供配置下发和版本控制接口。
        *   **命令管理:** 提供远程命令下发和执行结果收集接口。
        *   **状态监控:** 接收并处理Agent上报的心跳和状态信息。
    *   **架构考虑:** 
        *   高可用设计，确保客户端管理功能不间断。
        *   支持大规模Agent集群的并发连接和数据处理。

*   **流量查询服务 (FlowQuerySvc - Go服务):**
    *   **具体职责:**
        *   **设备端口映射:** 从MySQL数据库中获取设备ID、物理端口和VNI的映射关系。
        *   **流量数据查询:** 根据映射关系查询Prometheus中的流量数据。
        *   **结果计算与处理:** 执行流量聚合计算，转换为标准格式。
        *   **API接口提供:** 通过API_Orig暴露流量查询接口，支持单点查询和批量查询。
        *   **流量时序查询:** 支持查询特定时间范围内的流量时序数据。
    *   **架构考虑:**
        *   复用现有流量图查询功能，扩展支持设备ID、端口和VNI组合查询。
        *   优化查询性能，支持高并发请求。
        *   实现缓存机制，减少重复查询开销。
    *   **详细设计:**
        *   具体设计见《18-DCI-设备端口流量查询服务设计方案》。

*   **配置存储服务 (ConfigStore - MySQL):**
    *   **具体职责:**
        *   **配置持久化:** 存储系统配置、Agent配置、采集器配置等。
        *   **版本控制:** 管理配置的历史版本，支持回滚。
        *   **元数据存储:** 存储设备、端口、业务映射等元数据。
    *   **架构考虑:**
        *   高可用集群设计，确保数据安全。
        *   优化索引结构，提高查询效率。

#### 3.2.2.2 高可用消息队列 (Kafka 集群)
*   作用：系统内部各组件解耦、数据缓冲、峰值处理，集群部署确保高可用。
*   范围：Agent端各类采集数据（如SNMP, Syslog, sFlow/NetFlow, Telemetry等），分成流量、指标、日志、拓扑四类，通过对应Kafka Topic发送.
*   Kafka Topic的具体设计见《13-DCI-Kafka主题规划及负载均衡连接设计》。

#### 3.2.2.3 流量数据处理 (FlowProcessor - Go 服务)
*   消费 Kafka 流量类主题中的原始数据。
*   职责:
    *   数据解析与标准化: 解析 sFlow/NetFlow 数据包，提取源地址、目标地址、端口、协议、字节数等关键信息。
    *   **设备/接口标识符关联:** 与 MySQL (`dci_device`, `dci_logic_port_device` 等表) 关联，将流量数据中的设备地址和接口索引转换为标准 `dci_device.id` 和 `dci_logic_port.id`。
    *   流量聚合: 按时间窗口、设备、接口、协议等维度进行流量聚合计算，生成多粒度统计数据。
    *   结构化数据输出: 将处理后的流量数据发送至 Prometheus/Thanos (指标型数据) 和 Elasticsearch (详细流记录)。
    *   (优先级低) 流量基线分析: 建立正常流量基线，用于异常检测。
    *   具体设计见《05-流量统计及流量速率监测算法及技术方案》。

#### ******* 指标数据处理 (MetricsProcessor - Go 服务)
*   消费 Kafka 指标类主题中的原始指标数据。
*   职责:
    *   数据解析与过滤: 解析 Telegraf 采集的 JSON 格式指标数据，过滤无效或冗余数据点。
    *   **标识符关联:** 将指标数据中的设备名称、IP地址等标识符映射为标准的 `dci_device.id` 和 `dci_logic_port.id`，确保指标数据与其他系统数据的关联性。
    *   指标转换与计算: 根据需求对原始指标进行单位转换、派生计算（如利用率、可用性百分比等）。
    *   指标聚合: 按时间窗口和设备分组实现多粒度聚合指标。
    *   数据路由: 根据指标类型将数据分发至 Prometheus/Thanos 对应的时序数据存储。
    *   (优先级低) 指标异常检测: 实时检测指标异常值，生成初步告警信息。
    *   具体设计见《04-指标数据基线监测及告警技术方案设计》。

#### ******* 日志数据处理 (Logstash 集群)
*   消费 Kafka 日志类主题中的原始数据。
*   职责：
    *   数据清洗: 过滤、格式标准化。
    *   数据解析: 解析 Syslog (Grok/Dissect), sFlow/NetFlow, JSON 等。解析过程中需提取或关联 NMS/多云管理系统设备/端口 ID。
    *   数据路由: 将处理后的数据发送到 Elasticsearch。

#### 3.2.2.6 拓扑数据处理 (TopologyProcessor - Go 服务)
*   消费 Kafka 拓扑类主题中的原始数据。
*   职责：
    *   数据解析与清洗: 解析 LLDP JSON 数据。
    *   **ID 转换:** 查询 MySQL (`dci_device`, `dci_logic_port_device` 等表)，将 LLDP 中的设备/端口标识符转换为 DCI 数据库中定义的标准 `dci_device.id` 和 `dci_logic_port.id`。
    *   拓扑计算: 基于标准 ID 构建图节点和边的变更信息 (UPSERT/DELETE 操作)。
    *   **不进行数据丰富:** 本系统不查询或附加与拓扑结构无关的设备元数据，该部分由**多云管理系统**负责。
    *   **拓扑数据发送:** 将结构化的、仅包含标准 ID 和基础连接信息的拓扑变更数据发送到 `topology_processed` Kafka Topic 或直接调用**多云管理系统**提供的 API。
    *   (优先级低) 拓扑变更事件生成: 当检测到拓扑变化时，生成结构化日志发送到本系统内部的 Elasticsearch 用于审计。
*   具体设计见《03-DCI_拓扑数据技术设计》。

#### 3.2.2.7 指标告警机制概要
DCI-Monitor 的指标告警功能（初期重点为静态阈值告警）由独立的服务模块 `Alerting_Engine` (Go语言实现，暂定)负责。其核心机制包括：`Alerting_Engine` 定期从 **Prometheus/Thanos** 查询指标数据，依据存储在 **MySQL** 中的告警规则（包含指标查询、条件、阈值、持续时间、级别等参数）进行评估。告警规则通过专门的 RESTful API (`RuleConfigAPI`) 由**多云管理系统**进行配置管理。当指标数据持续满足告警条件后，将生成告警事件并通过 Kafka Topic 发送。此机制为后续扩展动态基线等复杂告警策略奠定基础。
*   具体设计见《04-指标数据基线监测及告警技术方案设计》。

#### 3.2.2.8 设备端口流量查询服务

*   **概述**：
    *   设备端口流量查询服务是DCI数据监测系统的核心组件，提供基于设备ID、端口号和VNI组合的统一流量查询接口。
    *   该服务桥接了MySQL中的设备端口映射关系与Prometheus中的流量数据，实现精确查询和高效计算。
    
*   **核心功能**：
    *   **端口映射查询**：从MySQL数据库中查询`dci_device`、`dci_logic_port`和`dci_logic_port_device`等表，获取设备ID、物理端口号和VNI到接口索引的映射关系。
    *   **流量数据查询**：基于映射关系构造Prometheus查询表达式，获取`ifHCInOctets`、`ifHCOutOctets`等流量计数器数据。
    *   **流量计算处理**：对原始流量数据进行速率计算、单位转换、聚合处理等。
    *   **结果格式化**：将处理后的流量数据转换为标准化的JSON响应格式。
    
*   **接口类型**：
    *   **单点查询**：根据单个设备ID、端口号和VNI组合查询流量数据。
    *   **批量查询**：支持一次查询多个端口的流量数据。
    *   **时序数据查询**：查询特定时间范围内的流量时序数据。
    *   **图表数据查询**：提供适用于前端图表展示的聚合数据格式。

*   **具体设计**：
    *   详细设计见《18-DCI-设备端口流量查询服务设计方案》。

#### 3.2.2.9 流量统计与速率计算概要
系统利用 Prometheus/Thanos 存储 SNMP 接口计数器数据 (如 `ifHCInOctets`)，通过 `rate()` 和 `increase()` 等函数计算接口的实时速率 (bps/pps) 和指定时间段的总流量，同时考虑计数器翻转问题。针对逻辑链路（如专线），通过映射两端物理接口，实现对 A->Z 及 Z->A 双向流量的分别统计。相关计算结果通过 `API_Orig` 的 `/chart/*` 和 `/traffic/summary` 端点对外提供服务。
*   具体设计见《05-流量统计及流量速率监测算法及技术方案》。

#### 3.2.2.10 (未来扩展) 实时分析引擎 (Flink/Spark Streaming)
*   适配其他扩展协议，处理指定数据指标/日志分析，消费 Kafka Topic，进行复杂的实时计算、异常检测、关联分析。

#### 3.2.2.11 网络自动化任务协同监控

网络自动化任务协同监控功能由四个核心模块组成，用于实现与网络自动化控制系统的交互，监测网络配置变更过程中的各项指标，评估变更影响并生成分析报告。

*   **任务信号处理模块 (`TaskSignalReceiver` - Go 服务):**
    *   **信号接收:** 提供 REST API 接口接收来自网络自动化控制系统的任务信号，包括任务启动、结束、异常中断和回滚信号
    *   **任务元数据管理:** 在 MySQL `TaskMetadataDB` 表中创建和更新任务记录，包含任务 ID、任务类型、目标设备列表、开始/结束时间等信息
    *   **状态管理:** 实时更新任务状态（准备中、运行中、已完成、已回滚、失败等），生成状态变更事件
    *   **设备关联:** 将任务与相关的设备和端口 ID 建立关联关系，便于后续数据查询和分析

*   **任务监控增强模块 (`TaskMonitorSvc` - Go 服务):**
    *   **监控增强:** 任务执行期间，可根据配置针对目标设备提高相关监控频率，获取更精细的性能数据 【优先级低】
    *   **数据收集:** 在检测到任务开始信号后，针对性收集与任务相关的系统日志、设备日志、网络流量和性能指标
    *   **多维度评估:** 对目标设备进行全面监控，包括:
        *   设备健康状况（CPU、内存、磁盘等资源占用情况的变化）
        *   网络吞吐量（接口流量的变化）
        *   网络延迟、丢包率的实时变化
    *   **告警联动:** 监测配置变更对网络流量和链路状态的实时影响，与已有告警规则联动 【优先级低】
    *   **报告触发:** 在任务状态变更为已完成或已回滚时，触发报告生成服务

*   **任务追踪与审计模块 (基于 `TaskMonitorSvc` 实现):**
    *   **唯一标识:** 为每个自动化任务生成或使用来自网络自动化系统的唯一标识符，贯穿整个监控过程
    *   **时间线记录:** 记录任务执行的详细时间线，包括开始时间、结束时间和关键节点时间戳，时间戳以服务器时间为准
    *   **事件关联:** 将配置变更与网络事件关联，在特定任务上下文中查看相关日志和指标变化 【优先级低】
    *   **历史查询:** 提供任务执行历史查询功能，支持按时间、设备、结果等条件筛选，通过前端界面或 API 接口查询
    *   **数据存储:** 任务相关的监控数据和元数据分别存储在不同数据源，建立关联关系：
        *   指标数据存储在 Prometheus/Thanos
        *   日志和事件存储在 Elasticsearch
        *   任务元数据和关联关系存储在 MySQL

*   **报告生成模块 (`ReportGenSvc` - Go 服务):**
    *   **报告生成:** 在任务完成后自动生成任务执行分析报告，整合多个数据源的信息
    *   **内容组织:** 报告内容结构化，包含任务摘要、变更内容、监测结果、性能对比分析等部分
    *   **图表可视化:** 生成网络性能指标的变更前后对比图表，直观展示变更影响（由前端部分进行配合）
    *   **异常突出:** 在报告中突出显示变更期间的异常事件和警告信息
    *   **格式支持:** 支持生成多种格式的报告，如 HTML（默认）、PDF、Docx 等 【优先级低】
    *   **自动分发:** 报告生成后支持通过邮件或通知系统自动分发给相关人员 【优先级低】
    *   **存储管理:** 将报告存储在指定位置（如 S3/NAS），并在 MySQL 中维护报告元数据和任务关联关系

### 3.2.3 数据存储层

#### 3.2.3.1 时间序列数据库 (Prometheus+Thanos 集群)
*   存储内容: SNMP 指标、sFlow/NetFlow 计数器样本、Telemetry 指标等时间序列数据。数据写入时，将 DCI 数据库提供的设备 ID (`device_id`) 和端口 ID (`port_id`) 作为核心的标签 (labels) 存储。
*   接入: 通过 Telegraf 采集并经由 Kafka 中转后通过专用 Exporter 暴露给 Prometheus 进行抓取
*   特性: 高效写入、查询、压缩、长期存储，集群部署提供高可用。利用Thanos实现数据的长期保存、降采样和全局查询能力，同时支持多种查询函数满足不同分析需求。
*   具体设计见《07-DCI-Prometheus和Thanos架构设计》。

#### 3.2.3.2 日志/搜索引擎 (Elasticsearch 集群)
*   存储内容: 处理后的 Syslog、sFlow/NetFlow 流记录、Telemetry 事件、自动化任务相关日志、报告内容（可选），以及可选的拓扑变更事件日志 (由 TopologyProcessor 生成)。
*   接入: 由 Logstash/Fluentd 推送（日志/流数据），由 `TopologyProcessor` 推送（拓扑变更事件）。
*   特性: 全文搜索、聚合分析，集群部署提供高可用。

#### 3.2.3.3 关系型数据库 (MySQL)
*   特性: 事务性、结构化数据存储、关联查询，其中 MySQL 部分由云服务提供高可用。
*   具体设计见《02-DCI数据监测系统-数据库设计》。

### 3.2.4 展示与应用层

#### 3.2.4.1 可视化/UI (**本系统部分**)
*   技术选型: 与前端对接上线，以前端选型为准(目前Vue2，大屏展示用Grafana)。
*   功能: 数据大屏、实时仪表盘、日志/流数据查询、自动化任务监控视图、报告查阅、数据监测系统的客户端管理界面、告警展示与处理等。**网络拓扑图及相关功能由多云管理系统提供。**

## 3.3 工作流程示例

### 3.3.1 配置下发
1. 管理员通过UI/API更新配置
2. `MgmtAPI`将配置存储到`ConfigStore`(MySQL)并生成配置版本
3. `MgmtAgent`定期轮询`MgmtAPI`获取配置版本信息
4. 当`MgmtAgent`发现新配置版本时，请求完整配置数据
5. `MgmtAPI`返回结构化的配置数据(YAML格式)
6. `MgmtAgent`对配置进行验证，备份当前配置
7. `MgmtAgent`将新配置应用到对应目录(如telegraf.d/)
8. `MgmtAgent`通知`Telegraf`重载配置(通过API或系统信号)
9. `MgmtAgent`在下一次心跳中上报配置应用状态
10. `MgmtAPI`将状态更新到`ClientRegistry`

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant UI as Web UI
    participant API as MgmtAPI
    participant DB as ConfigStore (MySQL)
    participant Agent as MgmtAgent
    participant Tel as Telegraf

    Admin->>UI: 更新Telegraf配置
    UI->>API: 提交新配置
    API->>DB: 存储配置，生成新版本
    
    loop 轮询配置
        Agent->>API: 获取配置版本信息
        API->>DB: 查询最新配置版本
        API-->>Agent: 返回版本信息
        
        opt 发现新版本
            Agent->>API: 请求完整配置
            API->>DB: 获取完整配置
            API-->>Agent: 返回YAML配置
            Agent->>Agent: 验证配置
            Agent->>Agent: 备份旧配置
            Agent->>Agent: 写入新配置到telegraf.d/
            Agent->>Tel: 发送重载信号
            Tel-->>Agent: 重载完成
        end
    end
    
    loop 心跳上报
        Agent->>API: 上报状态(含配置版本)
        API->>DB: 更新ClientRegistry
        API-->>Agent: 确认接收
        
        opt 配置已应用
            API-->>UI: 通知配置应用成功
            UI-->>Admin: 显示配置应用成功
        end
    end
```

### 3.3.2 重启命令
管理员发送命令 -> `MgmtAPI` 记录待执行命令 -> `MgmtAgent` 轮询获取 -> `MgmtAgent` 执行系统命令重启 `Telegraf` -> `MgmtAgent` 心跳上报命令结果和新状态存 `ClientRegistry`。

### 3.3.3 自动化任务监控流程
1.  网络自动化控制系统通过 REST API 接口向数据监测系统发送"任务开始"信号，信号中包含任务唯一标识 (TaskID)、任务类型、目标设备/服务列表、任务计划开始/结束时间等元数据。
2.  `TaskSignalReceiver` 接收请求，在数据库 `TaskMetadataDB` 表中创建任务记录，状态更新为 "Running"，并关联目标设备和端口信息。
3.  `TaskMonitorSvc` 被触发，开始针对性地标注与任务相关的系统日志、设备日志和性能指标数据。根据配置对目标设备的指标采集进行调频，包括设备健康状况（CPU、内存、磁盘等占用情况）、网络吞吐量、网络延迟、丢包率等指标。提网络变更任务监测执行状态 API 接口，供网络自动化控制系统查询并展示。
4.  网络自动化控制系统任务执行完毕后，通过 REST API 接口发送"任务结束"信号，或者在任务异常中断时发送自动化任务监控中断信号。
5.  `TaskSignalReceiver` 更新 `TaskMetadataDB` 中对应任务状态为 "Ended" 或 "Rolled Back"，记录任务的详细时间线，包括开始时间、结束时间和关键节点时间戳。
6.  `TaskMonitorSvc` 检测到状态变更，触发 `ReportGenSvc`。
7.  `ReportGenSvc` 查询 `TaskMetadataDB` 获取任务上下文（包含任务元数据、目标设备/端口ID、任务时间范围等）。
8.  `ReportGenSvc` 查询多个数据源获取任务期间的监控数据：
    * 从 Prometheus/Thanos 获取目标设备的时序性能指标数据及告警信息
    * 从 Elasticsearch 获取相关日志、事件和流记录、相关告警
    * 从 MySQL 获取设备元数据、拓扑数据和关联信息
    * 根据配置，执行任务执行结束后延长监测时间的数据监测任务【优先级低】
9.  `ReportGenSvc` 生成任务执行分析报告，报告包含以下内容：
    * 任务摘要（类型、时间、目标设备等）
    * 变更内容分析
    * 任务期间数据监测结果
    * 网络性能指标的变更前后对比图表
    * 变更期间检测到的异常事件和警告信息
10. `ReportGenSvc` 将生成的报告存储到报告存储系统（文件存储系统+MySQL），并更新 `ReportMetadataDB` 和 `TaskMetadataDB` 中的相关记录，维护任务与报告的关联关系。
11. 完成的报告可通过管理界面查看，支持按时间、设备、结果等条件筛选历史任务及其报告。

```mermaid
sequenceDiagram
    participant NetAutoCtrl as 网络自动化控制系统
    participant TaskSig as 任务信号处理器
    participant TaskMon as 任务监控服务
    participant ReportGen as 报告生成服务
    participant DB as 元数据库(MySQL)
    participant TSDB as 时序数据库(Prometheus)
    participant ES as 日志存储(ES)
    participant UI as 监控界面

    NetAutoCtrl->>TaskSig: 发送任务开始信号
    Note over NetAutoCtrl,TaskSig: 包含任务ID、类型、目标设备等

    TaskSig->>DB: 创建任务记录(状态:Running)
    Note over TaskSig,DB: 更新TaskMetadataDB表
    
    TaskSig->>TaskMon: 触发任务监控
    
    TaskMon->>TaskMon: 标注相关日志和指标数据
    TaskMon->>TaskMon: 根据配置调整采集频率
    
    TaskMon-->>NetAutoCtrl: 提供任务监测状态API
    Note over TaskMon,NetAutoCtrl: 供自动化系统查询并展示
    
    loop 任务监控期间
        TaskMon->>TSDB: 查询目标设备性能指标
        TaskMon->>ES: 查询相关日志事件
        NetAutoCtrl-->>TaskMon: 查询任务监测状态
    end
    
    alt 正常完成
        NetAutoCtrl->>TaskSig: 发送任务结束信号
    else 任务中断
        NetAutoCtrl->>TaskSig: 发送任务监控中断信号
    end
    
    TaskSig->>DB: 更新任务状态(Ended/Rolled Back)
    Note over TaskSig,DB: 记录详细时间线
    
    TaskSig->>TaskMon: 通知状态变更
    
    TaskMon->>ReportGen: 触发报告生成
    
    ReportGen->>DB: 查询任务上下文和元数据
    ReportGen->>TSDB: 获取性能指标数据和告警
    ReportGen->>ES: 获取日志、事件和告警
    ReportGen->>DB: 获取设备元数据和拓扑数据
    
    opt 配置了延长监测
        ReportGen->>ReportGen: 执行延长监测时间任务
        Note over ReportGen: 优先级低
    end
    
    ReportGen->>ReportGen: 生成分析报告
    Note over ReportGen: 包含任务摘要、性能对比等
    
    ReportGen->>DB: 存储报告元数据
    ReportGen->>DB: 更新任务-报告关联
    Note over ReportGen,DB: 使用文件系统+MySQL存储
    
    UI->>DB: 查询任务列表/详情
    UI->>ReportGen: 获取报告内容
```

### 3.3.4 拓扑数据处理与发送流程

#### 3.3.4.1 两阶段采集策略

为确保准确构建拓扑关系，系统采用两阶段采集策略：

1. **端口信息采集** (低频率，例如每24小时)：遍历数据库 dci_device 中全部设备SNMP，采集设备基础信息和本地端口信息，存储到相关表，作为后续处理的基础数据。
   * 采集系统基本信息，包括设备名称(`.1.3.6.1.2.1.1.5.0`)和系统MAC地址(`.1.3.6.1.2.1.17.1.1.0`)
   * 采集端口信息，包括端口名称与ifIndex映射(`.1.0.8802.1.1.2.1.3.7.1.3`)及端口MAC地址(`.1.3.6.1.2.1.2.2.1.6`)
   * 将数据标记为 `data_source_type = "port_info"` 发送到Kafka

2. **LLDP邻居采集** (较高频率，例如每10分钟)：采集设备的LLDP邻居关系数据，包括远端设备名称、MAC地址和端口等信息。
   * 采集包括远端设备名称 (`.1.0.8802.1.1.2.1.4.1.1.9`)、远端端口 (`.1.0.8802.1.1.2.1.4.1.1.7`) 等OID
   * 采集远端设备机箱ID (`.1.0.8802.1.1.2.1.4.1.1.5`)，通常为设备MAC地址
   * 将数据标记为 `data_source_type = "lldp_info"` 发送到Kafka

#### 3.3.4.2 拓扑数据处理流程

1.  `Telegraf` 通过 SNMP 采集LLDP数据，发送到 Kafka `dci.monitor.v1.defaultchannel.topology.raw_data` Topic。
2.  `TopologyProcessor` 消费原始数据Topic，根据 `data_source_type` 标签区分处理流程。
3.  对于端口信息数据：
    * 解析端口信息并存储到 `monitor_device_port_info` 表，建立 ifIndex 到端口名称的映射关系。
4.  对于LLDP邻居数据：
    * 从OID索引结构中提取本地端口索引 (localPortIndex)，该索引是关联本地端口的关键。
    * 查询已存储的端口信息，获取本地端口名称。
5.  `TopologyProcessor` 执行标识符转换：
    * 本地设备标识 → `dci_device.id`：通过设备名称、IP或MAC地址查询设备ID
    * 本地端口标识 → `dci_logic_port.id`：通过设备ID和端口名称/MAC地址查询端口ID
    * 远端设备标识 → `dci_device.id`：通过远端设备名称或机箱ID(MAC地址)查询设备ID
    * 远端端口标识 → `dci_logic_port.id`：通过远端设备ID和端口名称查询端口ID
6.  `TopologyProcessor` 计算拓扑变更信息 (UPSERT_VERTEX/UPSERT_EDGE/DELETE_EDGE)。
7.  拓扑数据存储与发送：
    * 将快照数据存储到MySQL (`monitor_topology_snapshot`, `monitor_topology_node`, `monitor_topology_edge` 表)
    * 将结构化的拓扑变更数据发送到 Kafka `dci.monitor.v1.defaultchannel.topology.processed` Topic
8.  **多云管理系统** 消费处理后的拓扑数据，更新其内部图数据库并执行数据丰富。
9.  (可选) `TopologyProcessor` 将拓扑变更事件发送到本系统 Elasticsearch，用于审计和分析。

#### ******* 拓扑数据结构

处理后的拓扑数据采用标准化的图数据JSON格式：

* **节点(Vertex)数据**：包含设备ID、类型、名称、MAC地址等属性
  ```json
  {
    "operation": "UPSERT_VERTEX",
    "vertex_id": "device_123",
    "tag": "device",
    "properties": {
      "name": "Switch-01",
      "device_type": "switch",
      "mac_address": "44:9B:C1:07:8C:71",
      "system_description": "Cisco IOS XE Software"
    }
  }
  ```

* **边(Edge)数据**：包含连接关系、端口信息、MAC地址等属性
  ```json
  {
    "operation": "UPSERT_EDGE",
    "edge_type": "connected_to",
    "source_vid": "device_123",
    "destination_vid": "device_456",
    "properties": {
      "local_port_id": "port_789",
      "remote_port_id": "port_101",
      "local_port_name": "10GE1/0/1",
      "remote_port_name": "10GE1/0/2",
      "local_port_mac": "44:9B:C1:07:8C:72",
      "remote_port_mac": "A0:B1:C2:D3:E4:F5",
      "link_speed": "10Gbps",
      "last_updated": 1683727291
    }
  }
  ```

#### ******* 拓扑数据流转图

```mermaid
sequenceDiagram
    participant Device as 网络设备
    participant Telegraf as 采集组件
    participant Kafka as 消息队列
    participant TopoProc as 拓扑处理服务
    participant MySQL as 元数据库
    participant ES as Elasticsearch
    participant MultiCloud as 多云管理系统

    Device->>Telegraf: SNMP采集(系统信息、端口、LLDP)
    
    alt 设备基础信息和端口采集 (每24小时)
        Telegraf->>Kafka: 设备系统信息(含MAC)
        Telegraf->>Kafka: 端口信息(含端口MAC)
    else LLDP邻居采集 (每10分钟)
        Telegraf->>Kafka: LLDP邻居数据(含远端MAC)
    end
    
    Kafka->>TopoProc: 消费拓扑原始数据
    
    alt 端口信息数据 (data_source_type="port_info")
        TopoProc->>MySQL: 存储设备信息(含MAC)
        TopoProc->>MySQL: 存储端口信息(含MAC)
    else LLDP邻居数据 (data_source_type="lldp_info")
        TopoProc->>MySQL: 查询设备信息(含MAC)
        MySQL-->>TopoProc: 返回映射数据
        TopoProc->>TopoProc: 提取OID索引，获取本地端口索引
        TopoProc->>MySQL: 查询设备/端口ID和MAC地址映射
        MySQL-->>TopoProc: 返回标准ID和MAC信息
        
        TopoProc->>TopoProc: 计算拓扑变更
        Note over TopoProc: 构建节点和边的变更信息(含MAC)
        
        TopoProc->>MySQL: 存储拓扑快照(含MAC信息)
        TopoProc->>Kafka: 发送处理后拓扑数据(含MAC信息)
        
        opt 审计记录
            TopoProc->>ES: 发送拓扑变更事件(可选)
        end
    end
    
    Kafka->>MultiCloud: 消费处理后拓扑数据（Kafka）
    
    MultiCloud->>MultiCloud: 更新图数据库
    Note over MultiCloud: 存储并丰富拓扑数据
```

### 3.3.5 拓扑展示流程 (在多云管理系统进行)
1.  用户访问**多云管理系统**的 UI 界面。
2.  **多云管理系统** UI 调用其自身的后端 API。
3.  **多云管理系统**后端 API 查询其内部图数据库获取拓扑结构。
4.  **多云管理系统**负责查询所需数据源（包括NMS或DCI数据库）进行数据丰富。
5.  **多云管理系统** UI 使用图可视化库渲染拓扑图。

### 3.3.6 流量查询服务流程

流量查询服务提供了一套标准化的流程，使多云系统能够获取设备端口的流量数据：

1. **查询请求**: 多云系统通过调用 DCI 数据监测系统提供的 REST API 发起流量查询请求，传递设备ID、物理端口号、VNI等参数。
   
2. **数据映射**: 流量查询服务(FlowQuerySvc)接收请求后，查询MySQL数据库获取设备ID、端口名称与实际监控对象（如接口索引）的映射关系。
   ```
   多云系统 -> API_Orig -> FlowQuerySvc -> MySQL(dci_device, dci_logic_port等表)
   ```

3. **流量数据获取**: 基于映射关系，构造Prometheus查询表达式，从时序数据库中获取对应的流量数据。
   ```
   FlowQuerySvc -> Prometheus/Thanos -> 流量数据
   ```

4. **流量计算与处理**: 对原始流量数据进行计算处理，如速率计算、格式转换、单位统一等。
   ```
   原始数据(ifHCInOctets) -> rate()计算 -> 比特率转换 -> 数据格式化
   ```
   
5. **结果返回**: 将处理后的流量数据以标准JSON格式返回给多云系统。
   ```
   FlowQuerySvc -> API_Orig -> 多云系统
   ```

### 3.3.7 流量查询数据流转图

```mermaid
sequenceDiagram
    participant MultiCloud as 多云管理系统
    participant API as API_Orig
    participant FlowSvc as FlowQuerySvc
    participant MySQL as MySQL数据库
    participant Prom as Prometheus/Thanos
    
    MultiCloud->>API: 请求流量数据(设备ID/端口名/VNI)
    API->>FlowSvc: 转发流量查询请求
    
    FlowSvc->>MySQL: 查询设备端口映射关系
    MySQL-->>FlowSvc: 返回映射数据(接口索引等)
    
    FlowSvc->>Prom: 查询流量数据(带标签筛选)
    Prom-->>FlowSvc: 返回原始流量数据
    
    FlowSvc->>FlowSvc: 计算速率、格式化数据
    
    FlowSvc-->>API: 返回处理后的流量数据
    API-->>MultiCloud: 返回标准格式的流量数据
    
    Note over MultiCloud: 展示流量图表/数据
```

该流程充分利用了MySQL中存储的设备端口映射关系，实现了从设备ID、端口名称到流量数据的精确查询，为多云系统提供了标准化的流量数据接口。详细设计见《18-DCI-设备端口流量查询服务设计方案》。

## 3.4 非功能特性

### 3.4.1 性能要求

*   数据采集延迟 < 500ms。
*   端到端数据处理延迟 < 3秒 (指标/事件)。
*   查询响应：简单查询秒级，复杂聚合分钟级内。
*   (待测) 单个 Agent 实例支持监控一定数量（例如，目标 50-100 台，具体取决于采集指标复杂度、设备响应速度）的网络交换机对象，需进行压力测试验证。

### 3.4.2 可靠性

*   系统可用性 ≥ 99.95% (核心数据管道和查询服务)。此目标将通过部署关键组件的高可用集群 （Kafka, Prometheus, Thanos, ES, MySQL 等）以及服务的冗余实例来实现。
*   数据持久化率 100%：写入系统的有效数据（经过处理、符合格式要求）在配置的数据保留期内，不会因为系统软件故障（不包括硬件彻底损坏、灾难等）而丢失。通常通过数据库的多副本、备份机制来保障。

### 3.4.3 安全性

*   传输安全: 生产环境所有 API 和 Agent 通信使用 TLS/HTTPS 加密。
*   认证授权:
    *   服务端 API 使用 Token 或其他机制进行认证。
    *   访问控制基于角色 (RBAC) 实现。
    *   租户数据供应严格校验 Token 和资源权限。
*   审计: 系统记录关键操作和配置变更日志。
*   Agent 安全: Agent 凭据进行安全存储。
*   **Agent-服务端安全通信**: `Management Agent` 与服务端 `MgmtAPI` 之间强制采用 HTTPS 进行安全通信。此方案基于单向 TLS 认证，即服务端配置由内部私有 CA 签发的 TLS 证书供 Agent 端验证其身份，确保数据传输的机密性与完整性。客户端认证则通过应用层 Bearer Token 实现，Agent 在每个请求中携带预置 Token。
    *   具体设计见《07-数据监测系统-Agent端与服务端HTTPS证书及通信技术设计.md》。

### 3.4.4 扩展性

*   水平扩展: Agent, Kafka, Logstash, Elasticsearch, Prometheus, Thanos, `dcimonitor`服务均支持水平扩展。
*   客户端扩展: dciagent采用模块化设计，支持通过子命令结构添加新的采集组件或功能。