---
title: |
  DCI-Monitor 流量统计及流量速率监测算法与技术方案
subtitle: |
  基于 TDengine 的接口流量分析
---

# 1. 引言

## 1.1 文档目的
本文档旨在详细阐述 DCI-Monitor 系统中用于网络接口流量统计和流量速率监测的技术方案与核心算法。重点在于如何利用存储在 TDengine 中的 SNMP 接口计数器数据，计算出接口的比特率 (bps)、包速率 (pps)，并支持对逻辑链路（如专线）进行双向流量分析，最终通过 `API_Orig` 对外提供服务。

## 1.2 文档范围
本文档覆盖以下内容：

*   流量相关指标在 TDengine 中的存储模型假设。
*   从 SNMP 计数器计算流量速率 (bps, pps) 的核心算法，包括处理计数器翻转的考虑。
*   计算特定时间段内总流量的算法。
*   针对逻辑链路（如 A-Z 专线）计算双向流量（A->Z 和 Z->A）的方法。
*   上述计算结果如何映射到 `API_Orig` 提供的 `/chart/*` 和 `/traffic/summary` 端点。
*   涉及的主要组件交互（`API_Orig`, `TSDB_Query`, TDengine）。

# 2. 技术方案设计

## 2.1 数据源与存储模型 (TDengine)
本方案假设基础的网络接口计数器数据已通过 Telegraf Agent (SNMP input) 采集并存储在 TDengine 中。

*   **数据存储:** 推荐为每个指标类型（如入方向字节数、出方向字节数、入方向单播包数、出方向单播包数等）创建单独的超表 (Super Table)。

*   **核心指标:**

    *   `ifHCInOctets` (.1.3.6.1.2.1.31.1.1.1.6): 64位入方向字节计数器 (推荐使用 HC 版本以减少翻转问题)。
    *   `ifHCOutOctets` (.1.3.6.1.2.1.31.1.1.1.10): 64位出方向字节计数器。
    *   `ifHCInUcastPkts` (.1.3.6.1.2.1.31.1.1.1.7): 64位入方向单播包计数器。
    *   `ifHCOutUcastPkts` (.1.3.6.1.2.1.31.1.1.1.11): 64位出方向单播包计数器。

*   **表结构 (示例 - 超表 `if_hc_in_octets`):**

    *   `ts`: TIMESTAMP (时间戳, 主键)。
    *   `value`: BIGINT (计数器值)。

    *   **Tags (标签):**

        *   `device_id`: BIGINT (关联 `dci_device.id`, 用于标识设备)。
        *   `port_id`: BIGINT (关联 `dci_logic_port.id`, 用于标识逻辑端口)。

*   **数据写入:** Telegraf Agent 将采集到的计数器值连同时间戳和对应的 `device_id`, `port_id` 标签写入相应的超表。

## 2.2 核心算法

### 2.2.1 流量速率计算 (bps, pps)
流量速率反映了单位时间内的流量变化。由于 SNMP 提供的是累计计数器，速率计算的核心是获取两个时间点之间计数器的差值，再除以时间间隔。

*   **利用 TDengine 函数:** TDengine 提供了计算差值的函数，简化了速率计算：

    *   `DIFFERENCE(column)`: 计算时间序列中当前值与前一个值的差值。
    *   `SPREAD(column)`: 计算指定时间窗口内的最大值与最小值之差 (也可用于计算增量，但 `DIFFERENCE` 更常用于速率计算)。

*   **比特率 (bps) 计算:**

    *   查询逻辑: `SELECT DIFFERENCE(value) * 8 FROM if_hc_in_octets WHERE device_id = ? AND port_id = ? AND ts >= ? AND ts < ?` (针对入方向)
    *   上述查询得到的是每个采集间隔内的比特数增量。要得到平均速率 (bps)，还需除以采集间隔的秒数。如果需要某个时间窗口内的平均速率，可结合 `INTERVAL` 和 `AVG` 函数：
        `SELECT AVG(rate) FROM (SELECT DIFFERENCE(value) * 8 / (ts - prev(ts)) as rate FROM if_hc_in_octets WHERE ... AND ts > ?) WHERE ... INTERVAL(1m);` (此处的 `ts - prev(ts)` 代表采集间隔，但更推荐使用固定采集间隔或TDengine 3.0后的 `RATE` 函数)

    *   **推荐方法 (TDengine 3.0+):** 使用 `RATE(column, [interval])` 函数直接计算速率。

        `SELECT RATE(value, 1s) * 8 FROM if_hc_in_octets WHERE ...` (计算每秒比特率)

*   **包速率 (pps) 计算:**

    *   查询逻辑: 类似于 bps，但使用包计数器超表 (`if_hc_in_ucast_pkts`, `if_hc_out_ucast_pkts`)，并且不需要乘以 8。
        `SELECT RATE(value, 1s) FROM if_hc_in_ucast_pkts WHERE ...` (计算每秒入方向包速率)

*   **计数器翻转处理:**

    *   使用 64 位计数器 (HC MIBs) 可以极大程度上避免在高速链路上短时间内发生翻转。
    *   TDengine 的 `DIFFERENCE` 和 `RATE` 函数通常能正确处理单次翻转（假设增量计算结果为负时视为翻转）。如果出现连续异常数据或长时间宕机导致多次翻转，计算结果可能不准确，需要更复杂的逻辑或数据清洗机制，但这超出了本系统的基本范围。

### 2.2.2 时间段总流量计算
计算指定时间段内的总流量（总字节数或总包数），使用 `SPREAD` 函数计算该时间段内计数器的总增量。

*   **总字节数计算:**
    `SELECT SPREAD(value) FROM if_hc_in_octets WHERE device_id = ? AND port_id = ? AND ts >= ? AND ts < ?` (计算入方向总字节数)

*   **总包数计算:**
    `SELECT SPREAD(value) FROM if_hc_in_ucast_pkts WHERE device_id = ? AND port_id = ? AND ts >= ? AND ts < ?` (计算入方向总包数)

### 2.2.3 逻辑链路双向流量计算 (A-Z, Z-A)

计算一条逻辑链路（如特定 ID 的专线）的双向流量，关键在于确定该链路两端的物理接口，并分别计算其出、入方向的流量速率。

1.  **链路端点映射:** `API_Orig` 或其调用的服务（可能需要查询多云管理系统或本地 MySQL 映射表）根据输入的逻辑链路标识符 (例如 `dci_link.id`)，查询得到该链路两端的设备和逻辑端口信息：

    *   端点 A: (`device_id_A`, `port_id_A`)
    *   端点 Z: (`device_id_Z`, `port_id_Z`)

2.  **A->Z 流量速率计算:** 该方向的流量通常以端点 A 的出方向速率来衡量。

    *   查询 TDengine 计算 (`device_id_A`, `port_id_A`) 的出方向比特率 (使用 `if_hc_out_octets` 超表和 `RATE` 函数)。
    *   查询 TDengine 计算 (`device_id_A`, `port_id_A`) 的出方向包速率 (使用 `if_hc_out_ucast_pkts` 超表和 `RATE` 函数)。

3.  **Z->A 流量速率计算:** 该方向的流量通常以端点 Z 的出方向速率来衡量。

    *   查询 TDengine 计算 (`device_id_Z`, `port_id_Z`) 的出方向比特率。
    *   查询 TDengine 计算 (`device_id_Z`, `port_id_Z`) 的出方向包速率。

4.  **总流量计算:** 同样地，可以计算 A->Z 和 Z->A 在指定时间段内的总出方向流量（字节数/包数）使用 `SPREAD` 函数。

**注意:** 也可以选择使用入方向速率来衡量（例如，A->Z 流量用端点 Z 的入方向速率衡量）。选择哪种方式（出向或入向）作为衡量标准取决于具体的业务需求和指标定义，但通常建议在文档和 API 中明确所采用的标准。本方案默认使用出方向速率。对于全双工链路，两端的出方向速率是描述链路双向流量的关键指标。

## 2.3 API 映射
`API_Orig` 提供的流量相关端点基于上述算法实现。这些端点与 `02-网络自动化平台-数据监测系统技术概要设计.md` 中描述的 `API_Orig` 职责一致，并对应 `src/dcimonitor/cmd/server.go` 中定义的实际路由。

*   **`/traffic/summary` 端点:**

    *   **用途:** 获取指定对象（设备、端口、或逻辑链路）在指定时间范围内的流量统计摘要。

    *   **请求参数 (示例):** `target_type` (device/port/link), `target_id`, `start_time`, `end_time`。

    *   **处理逻辑:**

        *   根据 `target_type` 和 `target_id` 确定查询的 `device_id` 和 `port_id` (对于 link 类型，执行链路端点映射)。
        *   调用 `TSDB_Query` 执行 `SPREAD` 查询，计算出、入方向的总字节数和总包数。
        *   对于 link 类型，分别计算 A->Z 和 Z->A 的总出方向流量。

    *   **响应 (示例):** JSON 对象，包含总入字节、总出字节、总入包、总出包 (对于 link，则包含 A->Z 和 Z->A 的总出流量)。

*   **`/chart/*` 端点 (`/chart/average`, `/chart/maximum`, `/chart/minimum`):**

    *   **用途:** 获取指定对象在指定时间范围内的流量速率时间序列数据，用于图表绘制。不同的端点 (`/average`, `/maximum`, `/minimum`) 指定了在每个时间粒度 (`interval`) 内对速率数据进行的聚合方式。

    *   **请求参数 (示例):** `target_type`, `target_id`, `start_time`, `end_time`, `metric` (in_bps/out_bps/in_pps/out_pps), `interval` (e.g., 1m, 5m)。

    *   **处理逻辑:**

        *   确定查询的 `device_id`, `port_id` 和相应的指标超表。
        *   构造 TDengine 查询，使用 `RATE` 函数计算基础速率，并结合 `INTERVAL` 子句和相应的聚合函数 (`AVG` for `/average`, `MAX` for `/maximum`, `MIN` for `/minimum`) 计算每个时间间隔内的聚合速率。

            *   示例 (请求 `/chart/average` 计算 5 分钟平均入向 bps): `SELECT AVG(rate) FROM (SELECT RATE(value, 1s) * 8 as rate FROM if_hc_in_octets WHERE ... AND ts >= ? AND ts < ?) WHERE ts >= ? AND ts < ? INTERVAL(5m);`

        *   对于 link 类型，分别计算 A->Z 和 Z->A 的出方向速率时间序列。

    *   **响应 (示例):** JSON 数组，每个元素包含时间戳和对应的聚合速率值 `[{timestamp: ..., value: ...}, ...] `。

## 2.4 组件交互

1.  前端 UI 或外部系统通过 `API_Orig` 请求流量数据。
2.  `API_Orig` 解析请求参数，确定查询目标和所需指标/统计类型。
3.  如果请求涉及逻辑链路，`API_Orig` (或其调用的服务) 查询 MySQL 或多云管理系统获取链路的端点信息。
4.  `API_Orig` 调用 `TSDB_Query` 模块，传递构造好的 TDengine 查询语句。
5.  `TSDB_Query` 执行对 TDengine 的查询。
6.  TDengine 返回查询结果 (总流量或速率时间序列)。
7.  `TSDB_Query` 将结果返回给 `API_Orig`。
8.  `API_Orig` 格式化响应并返回给请求方。

# 3. 总结
本方案利用 TDengine 的时序数据处理能力，特别是 `RATE` 和 `SPREAD` 函数，结合清晰的数据模型（使用 64 位计数器和标准 Tag），实现了网络接口流量速率和总流量的计算。通过逻辑链路端点映射，可以支持对专线等逻辑链路的双向流量分析。计算结果通过 `API_Orig` 的 `/chart/*` 和 `/traffic/summary` 端点对外提供服务，满足流量监控和统计的需求。
