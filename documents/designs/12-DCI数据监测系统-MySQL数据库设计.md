---
title: |
  DCI数据监测系统

subtitle: |
  MySQL数据库设计文档
---

# 1 文档介绍

## 1.1 文档目的

本文档详细描述DCI数据监测系统所需的MySQL数据库表设计，包括表结构、字段说明、索引设计和表之间的关系。作为系统开发和维护的重要参考。

## 1.2 文档范围

文档涵盖DCI数据监测系统所有MySQL数据库表的设计，主要包括以下方面：

- 代理端管理相关表（客户端注册、配置管理）
- 自动化任务相关表（任务元数据、报告管理）
- 租户资源映射表（租户数据访问控制）

# 2 数据库设计原则

## 2.1 命名规范

- 所有表名必须使用"monitor_"前缀
- 表名和字段名采用小写字母和下划线分隔的命名方式（snake_case）
- 主键统一命名为"id"，并使用UUID或BIGINT类型
- 外键命名格式为"表名_id"
- 表名应具有描述性，明确表示其包含的数据类型
- 字段名应清晰表达其存储的数据内容

## 2.2 设计原则

- 遵循第三范式设计，减少数据冗余
- 每个表必须有主键
- 合理使用外键约束，确保数据一致性
- 为常用查询添加适当索引，优化查询性能
- 考虑高并发场景，避免过度使用锁定机制
- 重要的操作和状态变更记录时间戳
- 为表和字段添加清晰的注释

# 3 数据库表设计

## 3.1 monitor_client_registry

### 3.1.1 表概述

该表用于存储 Agent 客户端的注册信息和状态。**Agent ID (`id`) 由服务端在 Agent 首次注册时生成并分配，以保证全局唯一性**。Agent 实例需要将获取到的 ID 持久化存储在本地，用于后续与服务端的通信。此 ID 是 Agent 特定安装实例的不可变标识符。客户端通过心跳机制定期更新状态。

### 3.1.2 表结构

```sql
CREATE TABLE `monitor_client_registry` (
  `id` varchar(36) NOT NULL COMMENT '客户端唯一标识，UUID格式',
  `hostname` varchar(255) NOT NULL COMMENT '客户端主机名',
  `ip_address` varchar(50) NOT NULL COMMENT '客户端IP地址',
  `version` varchar(50) NOT NULL COMMENT 'Agent版本号',
  `status` enum('online','offline','error') NOT NULL DEFAULT 'offline' COMMENT '客户端状态',
  `capabilities` json DEFAULT NULL COMMENT '客户端支持的功能，JSON格式',
  `telegraf_status` enum('running','stopped','error') DEFAULT NULL COMMENT 'Telegraf进程状态',
  `last_heartbeat` timestamp NULL DEFAULT NULL COMMENT '最后一次心跳时间',
  `tags` json DEFAULT NULL COMMENT '可自定义标签，JSON格式',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '客户端注册时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '客户端信息更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_last_heartbeat` (`last_heartbeat`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_hostname` (`hostname`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户端注册与状态管理表';
```

### 3.1.3 字段说明

| 字段名         | 数据类型          | 是否必填 | 默认值       | 说明                      |
|----------------|-------------------|----------|--------------|---------------------------|
| id             | varchar(36)       | 是       | 无           | 客户端唯一标识，UUID格式  |
| hostname       | varchar(255)      | 是       | 无           | 客户端主机名              |
| ip_address     | varchar(50)       | 是       | 无           | 客户端IP地址              |
| version        | varchar(50)       | 是       | 无           | Agent版本号               |
| status         | enum              | 是       | 'offline'    | 客户端状态                |
| capabilities   | json              | 否       | NULL         | 客户端支持的功能          |
| telegraf_status| enum              | 否       | NULL         | Telegraf进程状态          |
| last_heartbeat | timestamp         | 否       | NULL         | 最后一次心跳时间          |
| tags           | json              | 否       | NULL         | 可自定义标签              |
| create_time    | timestamp         | 是       | CURRENT_TIMESTAMP | 客户端注册时间        |
| update_time    | timestamp         | 是       | CURRENT_TIMESTAMP | 客户端信息更新时间    |

### 3.1.4 索引设计

| 索引名           | 索引类型 | 索引字段       | 索引目的                          |
|------------------|----------|----------------|-----------------------------------|
| PRIMARY          | 主键     | id             | 唯一标识客户端                    |
| idx_status       | 普通索引 | status         | 加速按状态查询客户端              |
| idx_last_heartbeat | 普通索引 | last_heartbeat | 加速查询特定时间范围内的心跳      |
| idx_ip_address   | 普通索引 | ip_address     | 加速按IP地址查询客户端            |
| idx_hostname     | 普通索引 | hostname       | 加速按主机名查询客户端            |

## 3.2 monitor_config_store

### 3.2.1 表概述

该表用于存储 Telegraf 采集器的配置及其版本信息。服务端可通过 API 更新配置，Agent 定期轮询获取**其有效配置**。表支持配置版本控制，并能区分全局配置和特定 Agent 配置：
- **全局/默认配置**: `client_id` 为 `NULL` 的记录代表适用于所有 Agent 的默认配置（针对特定 `config_type`）。
- **特定 Agent 配置**: `client_id` 不为 `NULL` 的记录代表仅适用于该 Agent 的配置。

**服务端的逻辑负责根据请求 Agent 的 ID，合并全局配置和特定 Agent 配置（特定配置优先），计算出最终生效的配置集返回给 Agent。**

### 3.2.2 表结构

```sql
CREATE TABLE `monitor_config_store` (
  `id` varchar(36) NOT NULL COMMENT '配置ID，UUID格式',
  `client_id` varchar(36) DEFAULT NULL COMMENT '客户端ID，NULL表示全局配置',
  `config_type` varchar(50) NOT NULL COMMENT '配置类型，如telegraf, agent',
  `version` int NOT NULL DEFAULT 1 COMMENT '配置版本号',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否为当前活动配置',
  `config_content` text NOT NULL COMMENT '配置内容',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `created_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_client_type_version` (`client_id`,`config_type`,`version`),
  KEY `idx_client_active` (`client_id`,`config_type`,`is_active`),
  CONSTRAINT `fk_config_client` FOREIGN KEY (`client_id`) REFERENCES `monitor_client_registry` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Telegraf配置存储表';
```

### 3.2.3 字段说明

| 字段名         | 数据类型          | 是否必填 | 默认值       | 说明                      |
|----------------|-------------------|----------|--------------|---------------------------|
| id             | varchar(36)       | 是       | 无           | 配置ID，UUID格式          |
| client_id      | varchar(36)       | 否       | NULL         | 客户端ID，NULL表示全局配置 |
| config_type    | varchar(50)       | 是       | 无           | 配置类型                  |
| version        | int               | 是       | 1            | 配置版本号                |
| is_active      | tinyint(1)        | 是       | 1            | 是否为当前活动配置        |
| config_content | text              | 是       | 无           | 配置内容                  |
| description    | varchar(255)      | 否       | NULL         | 配置描述                  |
| created_by     | varchar(50)       | 是       | 无           | 创建者                    |
| create_time    | timestamp         | 是       | CURRENT_TIMESTAMP | 创建时间              |
| update_time    | timestamp         | 是       | CURRENT_TIMESTAMP | 更新时间              |

### 3.2.4 索引设计

| 索引名           | 索引类型 | 索引字段                     | 索引目的                          |
|------------------|----------|------------------------------|-----------------------------------|
| PRIMARY          | 主键     | id                           | 唯一标识配置记录                  |
| uk_client_type_version | 唯一索引 | client_id, config_type, version | 确保每个客户端每种配置的版本唯一 |
| idx_client_active | 普通索引 | client_id, config_type, is_active | 加速查询客户端当前活动配置        |

## 3.3 monitor_task_metadata

### 3.3.1 表概述

该表用于存储自动化任务的元数据信息。系统通过Kafka接收任务启停信号，记录任务执行状态和相关设备信息，为报告生成提供基础数据。

### 3.3.2 表结构

```sql
CREATE TABLE `monitor_task_metadata` (
  `id` varchar(36) NOT NULL COMMENT '任务ID，UUID格式',
  `task_name` varchar(255) NOT NULL COMMENT '任务名称',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型',
  `status` enum('pending','running','completed','failed','canceled') NOT NULL COMMENT '任务状态',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '任务开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '任务结束时间',
  `duration` int DEFAULT NULL COMMENT '任务持续时间(秒)',
  `related_devices` json DEFAULT NULL COMMENT '相关设备IDs，JSON数组',
  `parameters` json DEFAULT NULL COMMENT '任务参数，JSON格式',
  `result_summary` text DEFAULT NULL COMMENT '任务结果摘要',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `report_id` varchar(36) DEFAULT NULL COMMENT '关联的报告ID',
  `created_by` varchar(50) NOT NULL COMMENT '任务创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_report_id` (`report_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自动化任务元数据表';
```

### 3.3.3 字段说明

| 字段名         | 数据类型          | 是否必填 | 默认值       | 说明                      |
|----------------|-------------------|----------|--------------|---------------------------|
| id             | varchar(36)       | 是       | 无           | 任务ID，UUID格式          |
| task_name      | varchar(255)      | 是       | 无           | 任务名称                  |
| task_type      | varchar(50)       | 是       | 无           | 任务类型                  |
| status         | enum              | 是       | 无           | 任务状态                  |
| start_time     | timestamp         | 否       | NULL         | 任务开始时间              |
| end_time       | timestamp         | 否       | NULL         | 任务结束时间              |
| duration       | int               | 否       | NULL         | 任务持续时间(秒)          |
| related_devices| json              | 否       | NULL         | 相关设备IDs               |
| parameters     | json              | 否       | NULL         | 任务参数                  |
| result_summary | text              | 否       | NULL         | 任务结果摘要              |
| error_message  | text              | 否       | NULL         | 错误信息                  |
| report_id      | varchar(36)       | 否       | NULL         | 关联的报告ID              |
| created_by     | varchar(50)       | 是       | 无           | 任务创建者                |
| create_time    | timestamp         | 是       | CURRENT_TIMESTAMP | 创建时间              |
| update_time    | timestamp         | 是       | CURRENT_TIMESTAMP | 更新时间              |

### 3.3.4 索引设计

| 索引名           | 索引类型 | 索引字段       | 索引目的                          |
|------------------|----------|----------------|-----------------------------------|
| PRIMARY          | 主键     | id             | 唯一标识任务                      |
| idx_status       | 普通索引 | status         | 加速按状态查询任务                |
| idx_task_type    | 普通索引 | task_type      | 加速按任务类型查询                |
| idx_start_time   | 普通索引 | start_time     | 加速按开始时间查询任务            |
| idx_end_time     | 普通索引 | end_time       | 加速按结束时间查询任务            |
| idx_report_id    | 普通索引 | report_id      | 加速按报告ID查询任务              |

## 3.4 monitor_report_metadata

### 3.4.1 表概述

该表用于存储任务报告的元数据信息。系统自动为完成的任务生成报告，记录报告类型、存储位置和元数据，方便用户查询和访问。

### 3.4.2 表结构

```sql
CREATE TABLE `monitor_report_metadata` (
  `id` varchar(36) NOT NULL COMMENT '报告ID，UUID格式',
  `task_id` varchar(36) NOT NULL COMMENT '关联的任务ID',
  `report_type` varchar(50) NOT NULL COMMENT '报告类型',
  `report_format` varchar(20) NOT NULL COMMENT '报告格式，如pdf、html、text',
  `title` varchar(255) NOT NULL COMMENT '报告标题',
  `summary` text DEFAULT NULL COMMENT '报告摘要',
  `content_location` varchar(512) NOT NULL COMMENT '报告内容存储位置',
  `size_bytes` bigint DEFAULT NULL COMMENT '报告大小(字节)',
  `is_archived` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已归档',
  `access_count` int NOT NULL DEFAULT 0 COMMENT '访问次数',
  `last_accessed` timestamp NULL DEFAULT NULL COMMENT '最后访问时间',
  `tags` json DEFAULT NULL COMMENT '标签，JSON数组',
  `created_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_report_type` (`report_type`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_report_task` FOREIGN KEY (`task_id`) REFERENCES `monitor_task_metadata` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报告元数据表';
```

### 3.4.3 字段说明

| 字段名           | 数据类型          | 是否必填 | 默认值       | 说明                      |
|------------------|-------------------|----------|--------------|---------------------------|
| id               | varchar(36)       | 是       | 无           | 报告ID，UUID格式          |
| task_id          | varchar(36)       | 是       | 无           | 关联的任务ID              |
| report_type      | varchar(50)       | 是       | 无           | 报告类型                  |
| report_format    | varchar(20)       | 是       | 无           | 报告格式                  |
| title            | varchar(255)      | 是       | 无           | 报告标题                  |
| summary          | text              | 否       | NULL         | 报告摘要                  |
| content_location | varchar(512)      | 是       | 无           | 报告内容存储位置          |
| size_bytes       | bigint            | 否       | NULL         | 报告大小(字节)            |
| is_archived      | tinyint(1)        | 是       | 0            | 是否已归档                |
| access_count     | int               | 是       | 0            | 访问次数                  |
| last_accessed    | timestamp         | 否       | NULL         | 最后访问时间              |
| tags             | json              | 否       | NULL         | 标签                      |
| created_by       | varchar(50)       | 是       | 无           | 创建者                    |
| create_time      | timestamp         | 是       | CURRENT_TIMESTAMP | 创建时间              |
| update_time      | timestamp         | 是       | CURRENT_TIMESTAMP | 更新时间              |

### 3.4.4 索引设计

| 索引名           | 索引类型 | 索引字段       | 索引目的                          |
|------------------|----------|----------------|-----------------------------------|
| PRIMARY          | 主键     | id             | 唯一标识报告                      |
| idx_task_id      | 普通索引 | task_id        | 加速按任务ID查询报告              |
| idx_report_type  | 普通索引 | report_type    | 加速按报告类型查询                |
| idx_create_time  | 普通索引 | create_time    | 加速按创建时间查询报告            |

## 3.5 monitor_tenant_map

### 3.5.1 表概述

该表用于存储租户资源映射关系，是实现多租户数据隔离和访问控制的关键。它定义了哪个租户 (`tenant_id`) 对哪个具体资源 (`resource_type` 和 `resource_id`) 拥有何种权限 (`permission_level`)。

**工作机制示例：** 当代表租户 'TenantA' 的用户请求访问设备 'DeviceX' (其在 DCI 数据库中的 ID 为 'uuid-dev-x') 的指标数据时，数据供应 API 的授权模块会查询此表，寻找是否存在记录满足 `tenant_id='TenantA'`, `resource_type='device'`, `resource_id='uuid-dev-x'`, `is_active=1` 并且 `permission_level` 至少为 'read'。只有校验通过，才会执行后续的数据查询。

**`resource_id` 通常关联 DCI 系统内部其他核心资源表的主键**，例如 `dci_device.id`、`monitor_report_metadata.id` 等。

### 3.5.2 表结构

```sql
CREATE TABLE `monitor_tenant_map` (
  `id` varchar(36) NOT NULL COMMENT '映射ID，UUID格式',
  `tenant_id` varchar(36) NOT NULL COMMENT '租户ID',
  `resource_type` varchar(50) NOT NULL COMMENT '资源类型，如device, port, report',
  `resource_id` varchar(36) NOT NULL COMMENT '资源ID',
  `permission_level` enum('read','write','admin') NOT NULL DEFAULT 'read' COMMENT '权限级别',
  `effective_from` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',
  `effective_to` timestamp NULL DEFAULT NULL COMMENT '失效时间，NULL表示永久有效',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `description` varchar(255) DEFAULT NULL COMMENT '映射描述',
  `created_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_resource` (`tenant_id`,`resource_type`,`resource_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_resource_id` (`resource_id`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户资源映射表';
```

### 3.5.3 字段说明

| 字段名           | 数据类型          | 是否必填 | 默认值       | 说明                      |
|------------------|-------------------|----------|--------------|---------------------------|
| id               | varchar(36)       | 是       | 无           | 映射ID，UUID格式          |
| tenant_id        | varchar(36)       | 是       | 无           | 租户ID                    |
| resource_type    | varchar(50)       | 是       | 无           | 资源类型                  |
| resource_id      | varchar(36)       | 是       | 无           | 资源ID                    |
| permission_level | enum              | 是       | 'read'       | 权限级别                  |
| effective_from   | timestamp         | 是       | CURRENT_TIMESTAMP | 生效时间              |
| effective_to     | timestamp         | 否       | NULL         | 失效时间                  |
| is_active        | tinyint(1)        | 是       | 1            | 是否激活                  |
| description      | varchar(255)      | 否       | NULL         | 映射描述                  |
| created_by       | varchar(50)       | 是       | 无           | 创建者                    |
| create_time      | timestamp         | 是       | CURRENT_TIMESTAMP | 创建时间              |
| update_time      | timestamp         | 是       | CURRENT_TIMESTAMP | 更新时间              |

### 3.5.4 索引设计

| 索引名           | 索引类型 | 索引字段                     | 索引目的                          |
|------------------|----------|------------------------------|-----------------------------------|
| PRIMARY          | 主键     | id                           | 唯一标识映射记录                  |
| uk_tenant_resource | 唯一索引 | tenant_id, resource_type, resource_id | 确保租户资源映射唯一 |
| idx_tenant_id    | 普通索引 | tenant_id                    | 加速按租户ID查询                  |
| idx_resource_id  | 普通索引 | resource_id                  | 加速按资源ID查询                  |
| idx_is_active    | 普通索引 | is_active                    | 加速按激活状态查询                |

# 4 表关系设计

## 4.1 实体关系图

```mermaid
erDiagram
    monitor_client_registry ||--o{ monitor_config_store : "配置关联"
    monitor_task_metadata ||--o{ monitor_report_metadata : "生成报告"
    
    monitor_client_registry {
        varchar id PK
        varchar hostname
        varchar ip_address
        varchar version
        enum status
        json capabilities
        enum telegraf_status
        timestamp last_heartbeat
    }
    
    monitor_config_store {
        varchar id PK
        varchar client_id FK
        varchar config_type
        int version
        tinyint is_active
        text config_content
    }
    
    monitor_task_metadata {
        varchar id PK
        varchar task_name
        varchar task_type
        enum status
        timestamp start_time
        timestamp end_time
        int duration
        json related_devices
        varchar report_id
    }
    
    monitor_report_metadata {
        varchar id PK
        varchar task_id FK
        varchar report_type
        varchar report_format
        varchar title
        text summary
        varchar content_location
    }
    
    monitor_tenant_map {
        varchar id PK
        varchar tenant_id
        varchar resource_type
        varchar resource_id
        enum permission_level
        tinyint is_active
    }
```

## 4.2 关系描述

1. **客户端与配置关系**：
   - 一个客户端（monitor_client_registry）可以有多个配置版本（monitor_config_store）
   - 通过client_id外键关联
   - 配置可以指定为某个客户端专用，也可以设为全局配置（client_id为NULL）

2. **任务与报告关系**：
   - 一个任务（monitor_task_metadata）可以生成一个或多个报告（monitor_report_metadata）
   - 通过task_id外键关联
   - 任务完成后，自动触发报告生成，并在任务元数据中更新report_id

3. **租户资源映射**：
   - monitor_tenant_map表独立存在，定义租户与各类资源的访问权限
   - 不直接与其他表建立外键关系，通过业务逻辑实现权限控制

# 5 数据库表使用场景

## 5.1 Agent注册与心跳

1. Agent首次启动时，如果本地没有持久化的 `agent_id`，调用注册API，服务端验证 Token 后生成唯一 `agent_id`，在 `monitor_client_registry` 表中创建记录，并将 `agent_id` 返回给 Agent。
2. **Agent 必须将获取到的 `agent_id` 持久化存储在本地。**
3. Agent 定期使用其 `agent_id` 发送心跳，服务端更新 `monitor_client_registry` 表中的 `last_heartbeat` 和 `status`。
4. 服务端可监控 `last_heartbeat` 字段，检测 Agent 离线情况。

## 5.2 配置管理

1. 管理员通过 Web 界面或 API 更新 Telegraf 配置（可能是全局或针对特定 Agent），系统在 `monitor_config_store` 表中创建/更新配置记录（标记 `client_id`）。
2. Agent 定期使用其 `agent_id` 轮询配置 API (`GET /agents/{agent_id}/config`)。
3. **服务端根据 Agent ID 计算有效的配置集（合并全局和特定配置，特定优先），并将最终的配置片段和整体版本号返回给 Agent。**
4. Agent 比较版本号，发现新版本时，下载并应用服务端返回的配置片段（覆盖 `telegraf.d`），然后通过心跳上报当前应用的配置版本。

## 5.3 自动化任务监控

1. 网络自动化控制系统发送任务启动信号，系统在monitor_task_metadata表中创建任务记录
2. 任务执行过程中，系统持续更新任务状态和相关信息
3. 任务完成后，系统自动触发报告生成服务

## 5.4 报告生成与查询

1. 报告生成服务根据任务ID查询相关监控数据，生成结构化报告
2. 系统在monitor_report_metadata表中记录报告元数据，并更新monitor_task_metadata表中的report_id
3. 用户通过API查询报告列表和报告内容

## 5.5 租户数据服务

1. 系统管理员在 `monitor_tenant_map` 表中定义租户可访问的资源及其权限。
2. 租户通过数据供应 API 访问数据时，API 请求会携带用户的租户身份信息。
3. **API 的授权中间件或服务逻辑会查询 `monitor_tenant_map` 表，使用请求中的 `tenant_id`、目标资源的 `resource_type` 和 `resource_id` 来验证用户是否具有所需的操作权限 (`permission_level`)。**
4. 只有权限验证通过，API 才会继续向底层数据存储（TDengine, ES, MySQL）发起**经过过滤的**查询，确保只返回该租户有权访问的数据。

# 6 数据库优化建议

## 6.1 索引优化

1. 针对频繁查询的字段添加了合适的索引
2. 对于需要联合查询的场景，创建了复合索引
3. 建议定期分析查询性能，优化索引设计

## 6.2 大数据量处理

1. 对于client_registry和task_metadata等表，可能需要定期归档历史数据
2. 考虑使用分区表技术，按时间范围分区，提高查询效率
3. 配置内容等大字段，考虑存储优化或压缩

## 6.3 事务和并发控制

1. 对于高并发操作（如大量Agent同时心跳），需要优化事务处理
2. 使用乐观锁或版本控制机制，减少锁冲突
3. 配置适当的连接池大小，满足并发需求

# 7 附录

## 7.1 表关系示意图

以上ER图展示了表之间的关系。主要依赖关系：
- monitor_config_store依赖monitor_client_registry
- monitor_report_metadata依赖monitor_task_metadata

## 7.2 版本历史

| 版本 | 日期       | 作者   | 修订说明             |
| ---- | ---------- | ------ | -------------------- |
| V1.0 | 2025-05-05 | 系统   | 初始版本             | 