以下是告警规则管理模块提供的REST API接口：

##### 1. 创建告警规则

- **URL**: `/api/v1/alert-rules`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "name": "高CPU使用率告警",
    "description": "当CPU使用率超过80%持续5分钟时触发告警",
    "expr": "dci_snmp_device_cpu_usage{entPhysicalClass=\"9\"} > 80",
    "for": "5m",
    "labels": {
      "severity": "warning",
      "category": "device_status"
    },
    "annotations": {
      "summary": "交换机 {{ $labels.device_name }} CPU使用率过高",
      "description": "设备 {{ $labels.device_name }} ({{ $labels.device_ip }}) 的CPU使用率为 {{ $value }}%，持续超过5分钟。"
    },
    "source": "prometheus",
    "level": "warning",
    "group_id": 1
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 123,
      "name": "高CPU使用率告警",
      "status": 1,
      "created_at": "2025-07-10T10:15:30Z"
    }
  }
  ```

##### 2. 更新告警规则

- **URL**: `/api/v1/alert-rules/{id}`
- **方法**: `PUT`
- **请求体**: 与创建接口相同
- **响应**: 与创建接口相同

##### 3. 删除告警规则

- **URL**: `/api/v1/alert-rules/{id}`
- **方法**: `DELETE`
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": null
  }
  ```

##### 4. 更新规则状态

- **URL**: `/api/v1/alert-rules/{id}/status`
- **方法**: `PATCH`
- **请求体**:
  ```json
  {
    "status": 0  // 0=禁用, 1=启用
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 123,
      "status": 0,
      "updated_at": "2025-07-10T11:20:15Z"
    }
  }
  ```

##### 5. 获取单个规则

- **URL**: `/api/v1/alert-rules/{id}`
- **方法**: `GET`
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 123,
      "name": "高CPU使用率告警",
      "description": "当CPU使用率超过80%持续5分钟时触发告警",
      "expr": "dci_snmp_device_cpu_usage{entPhysicalClass=\"9\"} > 80",
      "for": "5m",
      "labels": {
        "severity": "warning",
        "category": "device_status"
      },
      "annotations": {
        "summary": "交换机 {{ $labels.device_name }} CPU使用率过高",
        "description": "设备 {{ $labels.device_name }} ({{ $labels.device_ip }}) 的CPU使用率为 {{ $value }}%，持续超过5分钟。"
      },
      "source": "prometheus",
      "level": "warning",
      "status": 1,
      "group_id": 1,
      "created_by": "admin",
      "updated_by": "admin",
      "created_at": "2025-07-10T10:15:30Z",
      "updated_at": "2025-07-10T10:15:30Z"
    }
  }
  ```

##### 6. 获取规则列表

- **URL**: `/api/v1/alert-rules`
- **方法**: `GET`
- **查询参数**:
  - `page`: 页码
  - `page_size`: 每页大小
  - `status`: 状态过滤
  - `level`: 级别过滤
  - `source`: 来源过滤
  - `group_id`: 规则组ID
  - `name`: 名称模糊搜索
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "total": 100,
      "page": 1,
      "page_size": 10,
      "items": [
        {
          "id": 123,
          "name": "高CPU使用率告警",
          "level": "warning",
          "source": "prometheus",
          "status": 1,
          "group_name": "设备状态告警",
          "created_at": "2025-07-10T10:15:30Z",
          "updated_at": "2025-07-10T10:15:30Z"
        },
        // 更多规则...
      ]
    }
  }
  ```

##### 7. 验证告警规则

- **URL**: `/api/v1/alert-rules/validate`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "expr": "dci_snmp_device_cpu_usage{entPhysicalClass=\"9\"} > 80",
    "for": "5m",
    "source": "prometheus"
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "valid": true,
      "warnings": ["指标dci_snmp_device_cpu_usage在过去24小时内没有数据点"]
    }
  }
  ```

##### 8. 手动触发规则同步

- **URL**: `/api/v1/alert-rules/sync`
- **方法**: `POST`
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "sync_id": 456,
      "sync_time": "2025-07-10T14:30:45Z",
      "status": "success"
    }
  }
  ```
