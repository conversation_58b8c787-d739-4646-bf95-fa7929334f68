---
title: |
  DCI-Monitor 告警技术方案

subtitle: |
  指标数据与日志数据告警设计
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-03-15 | 顾铠羟 | 初始版本（基于TDengine的指标告警） |
| V2.0 | 2025-05-20 | 顾铠羟 | 更新为基于Prometheus的综合告警方案 |

# 1 文档介绍

## 1.1 文档目的

本文档详细阐述 DCI-Monitor 系统中告警功能的技术实现方案，包括指标数据告警和日志数据告警的规则处理、配置管理以及通知触发流程，同时说明与网络自动化任务监控的联动机制。

## 1.2 文档范围

本文档覆盖 `AlertingEngine` 组件的设计，包括：
* 基于 Prometheus 的指标数据告警机制 
* 基于 Elasticsearch 的日志数据告警机制
* 告警规则的数据模型与评估逻辑
* 与网络自动化任务监控的联动机制
* 告警通知与处理流程
* 告警与根因分析的闭环设计

# 2 总体设计

## 2.1 设计目标

* 构建统一的告警框架，支持指标数据和日志数据两种告警类型
* 实现告警规则的集中管理和灵活配置
* 提供告警与网络自动化任务的关联分析能力
* 支持告警的分级、分类、路由和通知
* 为根因分析提供数据支持，形成闭环

## 2.2 架构设计

告警系统采用模块化设计，分为指标告警引擎和日志告警引擎两大核心组件，共享统一的规则管理和通知分发机制。

架构图如下：

```mermaid
graph TD
    subgraph "DCI 其他系统"
        MultiCloudMgmtUI("多云管理系统 UI/后台")
        MultiCloudMgmtAPI("多云管理系统 API")
        NetAutoCtrl("网络自动化控制系统")
    end

    subgraph "dcimonitor服务端"
        subgraph "告警引擎"
            MetricAlertEngine("指标告警引擎")
            LogAlertEngine("日志告警引擎")
            AlertCorrelation("告警关联分析器")
        end
        
        subgraph "任务监控"
            TaskMonitor("任务监控服务")
        end
        
        subgraph "规则管理"
            RuleConfigAPI("告警规则配置 API")
            RuleStore[(MySQL: 规则存储)]
        end
        
        subgraph "告警处理"
            AlertProcessor("告警处理器")
            AlertStore[(MySQL: 告警存储)]
            NotificationSvc("通知服务")
        end
        
        subgraph "数据源"
            Prometheus[(Prometheus/Thanos)]
            ES[(Elasticsearch)]
            Kafka[(Kafka)]
        end
    end

    MultiCloudMgmtUI -- "管理告警规则" --> MultiCloudMgmtAPI
    MultiCloudMgmtAPI -- "CRUD 操作" --> RuleConfigAPI
    RuleConfigAPI -- "读写规则" --> RuleStore
    
    MetricAlertEngine -- "查询指标" --> Prometheus
    LogAlertEngine -- "查询日志" --> ES
    
    MetricAlertEngine -- "加载规则" --> RuleStore
    LogAlertEngine -- "加载规则" --> RuleStore
    
    MetricAlertEngine -- "生成告警" --> AlertProcessor
    LogAlertEngine -- "生成告警" --> AlertProcessor
    
    AlertProcessor -- "存储告警" --> AlertStore
    AlertProcessor -- "发送通知" --> NotificationSvc
    AlertProcessor -- "告警关联" --> AlertCorrelation
    
    NetAutoCtrl -- "任务信号" --> TaskMonitor
    TaskMonitor -- "查询相关告警" --> AlertStore
    TaskMonitor -- "任务上下文" --> AlertCorrelation
    
    AlertCorrelation -- "关联分析" --> AlertProcessor
    NotificationSvc -- "告警通知" --> Kafka
```

## 2.3 数据流/流程图

告警评估与处理的基本流程如下：

```mermaid
sequenceDiagram
    participant Rule as 规则存储
    participant Engine as 告警引擎
    participant DataSrc as 数据源
    participant Processor as 告警处理器
    participant Notifier as 通知服务
    participant Task as 任务监控
    
    Engine->>Rule: 加载启用的告警规则
    loop 定期评估
        Engine->>DataSrc: 查询监控数据
        DataSrc-->>Engine: 返回数据
        Engine->>Engine: 评估告警条件
        
        alt 触发告警
            Engine->>Processor: 生成告警事件
            Processor->>Processor: 去重与分组
            
            opt 任务关联
                Processor->>Task: 查询相关任务
                Task-->>Processor: 返回任务上下文
                Processor->>Processor: 关联任务信息
            end
            
            Processor->>Notifier: 发送告警通知
        end
    end
```

## 2.4 模块化设计

告警系统由以下核心模块组成：

1. **指标告警引擎**：基于 Prometheus 的指标数据告警机制
2. **日志告警引擎**：基于 Elasticsearch 的日志数据告警机制
3. **规则管理模块**：负责告警规则的存储、验证和分发
4. **告警处理器**：负责告警的去重、分组、存储和路由
5. **通知服务**：负责将告警发送到不同的通知渠道
6. **告警关联分析器**：负责告警之间以及告警与任务的关联分析

## 2.5 技术选型

* **指标告警**：基于 Prometheus AlertManager 和自定义告警引擎
* **日志告警**：基于 Elasticsearch Alerting 和自定义告警引擎
* **规则存储**：MySQL 数据库
* **告警存储**：MySQL 数据库（元数据）和 Elasticsearch（详细内容）
* **消息队列**：Kafka 用于告警通知和系统间通信
* **开发语言**：Go 语言实现核心告警引擎和处理器

# 3 详细设计

## 3.1 功能模块

### 3.1.1 指标告警引擎

指标告警引擎负责基于 Prometheus 的时序数据进行告警规则评估。

**核心功能**：
* 加载并解析指标告警规则
* 通过 PromQL 查询 Prometheus/Thanos 获取指标数据
* 评估指标数据是否满足告警条件
* 处理告警状态转换和持续时间判断
* 生成标准化的告警事件

**告警规则类型**：
* **静态阈值告警**：基于固定阈值的告警规则
* **趋势告警**：基于指标变化趋势的告警规则
* **动态基线告警**：基于历史数据计算的动态基线的告警规则（优先级低）

**实现方式**：
* 直接使用 Prometheus AlertManager 的规则评估能力
* 开发自定义告警引擎，通过 Prometheus API 查询数据并评估规则

### 3.1.2 日志告警引擎

日志告警引擎负责基于 Elasticsearch 的日志数据进行告警规则评估。

**核心功能**：
* 加载并解析日志告警规则
* 通过 Elasticsearch Query DSL 查询日志数据
* 评估日志数据是否满足告警条件
* 处理告警状态转换和聚合计算
* 生成标准化的告警事件

**告警规则类型**：
* **关键字匹配告警**：基于日志内容关键字的告警规则
* **频率告警**：基于日志出现频率的告警规则
* **模式识别告警**：基于日志模式识别的告警规则（优先级低）

**实现方式**：
* 利用 Elasticsearch Alerting 功能
* 开发自定义告警引擎，通过 Elasticsearch API 查询数据并评估规则

### 3.1.3 规则管理模块

规则管理模块负责告警规则的存储、验证和分发。

**核心功能**：
* 提供统一的规则管理 API
* 存储和维护告警规则
* 验证规则的有效性和合法性
* 将规则分发给相应的告警引擎

**API 接口**：
* `POST /api/v1/alerts/rules`：创建新告警规则
* `GET /api/v1/alerts/rules`：获取告警规则列表
* `GET /api/v1/alerts/rules/{rule_id}`：获取指定告警规则详情
* `PUT /api/v1/alerts/rules/{rule_id}`：更新指定告警规则
* `DELETE /api/v1/alerts/rules/{rule_id}`：删除指定告警规则

### 3.1.4 告警处理器

告警处理器负责告警的去重、分组、存储和路由。

**核心功能**：
* 接收来自告警引擎的告警事件
* 对告警进行去重和分组处理
* 存储告警事件和状态
* 根据告警属性进行路由
* 与任务监控服务进行关联

**告警处理流程**：
1. 接收告警事件
2. 查询是否存在相同或相似告警
3. 进行去重和分组处理
4. 查询相关的网络自动化任务
5. 关联任务信息（如果存在）
6. 存储告警事件
7. 根据告警级别和类型进行路由
8. 发送到通知服务

### 3.1.5 通知服务

通知服务负责将告警发送到不同的通知渠道。

**核心功能**：
* 接收来自告警处理器的通知请求
* 根据通知目标选择适当的通知渠道
* 格式化通知内容
* 发送通知并跟踪状态

**通知渠道**：
* Email 邮件通知
* Webhook 回调通知
* Kafka 消息队列
* 企业即时通讯工具（如企业微信、钉钉等）

### 3.1.6 告警关联分析器

告警关联分析器负责告警之间以及告警与任务的关联分析。

**核心功能**：
* 分析多个告警之间的关联性
* 关联告警与网络自动化任务
* 识别潜在的根因
* 提供关联分析结果

**关联分析方法**：
* 时间相关性分析
* 拓扑相关性分析
* 设备/服务相关性分析
* 历史模式匹配

## 3.2 数据模型

### 3.2.1 告警规则模型

告警规则统一存储在 MySQL 数据库中，根据类型分为指标告警规则和日志告警规则。

**通用字段**：
```sql
-- MySQL数据库表定义
CREATE TABLE alert_rules (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    rule_type ENUM('metric', 'log') NOT NULL,
    severity ENUM('critical', 'warning', 'info') NOT NULL,
    enabled BOOLEAN NOT NULL DEFAULT TRUE,
    notification_targets JSON,
    labels JSON,
    annotations JSON,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**指标告警规则**：
```sql
-- MySQL数据库表定义
CREATE TABLE metric_alert_rules (
    id VARCHAR(36) PRIMARY KEY,
    rule_id VARCHAR(36) NOT NULL,
    query TEXT NOT NULL,
    condition_operator VARCHAR(10) NOT NULL,
    threshold_value DOUBLE NOT NULL,
    duration_seconds INT NOT NULL,
    FOREIGN KEY (rule_id) REFERENCES alert_rules(id) ON DELETE CASCADE
);
```

**日志告警规则**：
```sql
-- MySQL数据库表定义
CREATE TABLE log_alert_rules (
    id VARCHAR(36) PRIMARY KEY,
    rule_id VARCHAR(36) NOT NULL,
    index_pattern VARCHAR(255) NOT NULL,
    query TEXT NOT NULL,
    time_window_minutes INT NOT NULL,
    threshold_count INT,
    threshold_operator VARCHAR(10),
    pattern_match TEXT,
    FOREIGN KEY (rule_id) REFERENCES alert_rules(id) ON DELETE CASCADE
);
```

### 3.2.2 告警事件模型

告警事件存储在 MySQL 和 Elasticsearch 中，MySQL 存储元数据，Elasticsearch 存储详细内容。

**告警元数据**：
```sql
-- MySQL数据库表定义
CREATE TABLE alert_events (
    id VARCHAR(36) PRIMARY KEY,
    rule_id VARCHAR(36) NOT NULL,
    status ENUM('firing', 'resolved', 'acknowledged') NOT NULL,
    severity ENUM('critical', 'warning', 'info') NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    acknowledged_time TIMESTAMP,
    acknowledged_by VARCHAR(255),
    source_type ENUM('metric', 'log') NOT NULL,
    source_id VARCHAR(255) NOT NULL,
    task_id VARCHAR(36),
    labels JSON,
    summary VARCHAR(255) NOT NULL,
    FOREIGN KEY (rule_id) REFERENCES alert_rules(id)
);
```

**告警详情**（存储在 Elasticsearch）：
```json
{
  "alert_id": "uuid",
  "rule_id": "uuid",
  "timestamp": "2025-05-20T10:00:00Z",
  "status": "firing",
  "severity": "critical",
  "source_type": "metric",
  "source_id": "device_id:port_id",
  "summary": "CPU usage exceeded 90%",
  "description": "Device CPU usage has been above 90% for more than 5 minutes",
  "value": 95.2,
  "labels": {
    "device_id": "device-001",
    "device_name": "core-switch-01",
    "metric": "cpu_usage"
  },
  "annotations": {
    "dashboard": "https://grafana.example.com/d/abc123"
  },
  "task_context": {
    "task_id": "task-001",
    "task_type": "config_change",
    "task_start_time": "2025-05-20T09:55:00Z"
  }
}
```

## 3.3 接口设计

### 3.3.1 告警规则管理 API

**创建告警规则**：
```
POST /api/v1/alerts/rules
Content-Type: application/json

{
  "name": "高 CPU 使用率告警",
  "description": "当设备 CPU 使用率超过 90% 持续 5 分钟时触发",
  "rule_type": "metric",
  "severity": "critical",
  "notification_targets": ["email_group_1", "webhook_1"],
  "labels": {
    "category": "resource",
    "component": "cpu"
  },
  "annotations": {
    "summary": "设备 {{ $labels.device_name }} CPU 使用率高",
    "description": "设备 {{ $labels.device_name }} ({{ $labels.device_id }}) CPU 使用率为 {{ $value }}%，已持续 5 分钟"
  },
  "metric_rule": {
    "query": "max by(device_id, device_name) (cpu_usage{job=\"snmp\"})",
    "condition_operator": ">",
    "threshold_value": 90,
    "duration_seconds": 300
  }
}
```

**创建日志告警规则**：
```
POST /api/v1/alerts/rules
Content-Type: application/json

{
  "name": "接口状态变更告警",
  "description": "当接口状态发生变更时触发",
  "rule_type": "log",
  "severity": "warning",
  "notification_targets": ["email_group_2"],
  "labels": {
    "category": "network",
    "component": "interface"
  },
  "annotations": {
    "summary": "设备 {{ device_name }} 接口状态变更",
    "description": "设备 {{ device_name }} 接口 {{ interface_name }} 状态从 {{ old_status }} 变为 {{ new_status }}"
  },
  "log_rule": {
    "index_pattern": "syslog-*",
    "query": "message:\"Interface * changed state to *\" AND device_id:*",
    "time_window_minutes": 5,
    "pattern_match": "Interface (\\S+) changed state to (\\S+)"
  }
}
```

### 3.3.2 告警查询 API

**查询告警列表**：
```
GET /api/v1/alerts/events?status=firing&severity=critical&page=1&size=20
```

**查询告警详情**：
```
GET /api/v1/alerts/events/{alert_id}
```

### 3.3.3 告警处理 API

**确认告警**：
```
PUT /api/v1/alerts/events/{alert_id}/acknowledge
Content-Type: application/json

{
  "acknowledged_by": "operator_name",
  "comment": "正在处理中"
}
```

**解决告警**：
```
PUT /api/v1/alerts/events/{alert_id}/resolve
Content-Type: application/json

{
  "resolution": "已修复引起高 CPU 使用率的问题",
  "resolved_by": "operator_name"
}
```

# 4 安全设计

告警系统的安全设计包括以下几个方面：

* **API 安全**：所有 API 调用通过 HTTPS 加密传输，并使用 JWT 或 API Key 进行身份验证
* **权限控制**：基于角色的访问控制 (RBAC)，确保只有授权用户才能管理告警规则
* **数据安全**：敏感信息（如通知目标中的凭据）进行加密存储
* **审计日志**：记录所有关键操作，包括规则创建、修改、删除以及告警确认和解决

# 5 测试方案

## 5.1 功能测试范围

* 指标告警规则创建和评估
* 日志告警规则创建和评估
* 告警通知发送和接收
* 告警与任务关联
* 告警处理流程（确认、解决）
* 告警规则管理 API

## 5.2 测试指标

* 告警评估准确性：误报率 < 1%，漏报率 < 0.5%
* 告警评估性能：单规则评估延迟 < 500ms
* 通知延迟：从告警触发到通知发送 < 3 秒
* 系统可扩展性：支持同时评估 1000+ 告警规则
* 高可用性：系统组件故障自动恢复，无数据丢失
