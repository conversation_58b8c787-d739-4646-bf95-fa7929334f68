---
title: |
  DCI-任务协同期指标类数据联动API说明

subtitle: |
  网络自动化任务期间指标数据记录与查询API接口文档
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | --------- | ----- | ----------------- |
| V1.0 | 2025-08-20 | 顾铠羟 | 初始版本           |

# 1. API概述

## 1.1 功能简介

本API提供网络自动化任务协同期间指标数据记录与查询功能，实现任务执行前、期间、后的设备指标数据采集、基线统计和数据呈现。

## 1.2 设计原则

- **自动化优先**：指标数据记录由任务信号自动触发，API主要提供查询和管理功能
- **数据导向**：API接口直接对应数据表操作，确保与数据模型的一致性
- **只读为主**：主要提供数据查询和展示功能，符合"数据记录和呈现"的设计定位
- **RESTful风格**：遵循标准的RESTful API设计规范

## 1.2.1 规范符合性声明

本API文档严格遵循《03-DCI项目API及代码规范指南.md》的所有强制性要求：
- ✅ RESTful设计规范（HTTP方法、URI路径、查询参数）
- ✅ API版本控制（/api/v1/格式）
- ✅ 命名规范（API路径连字符、查询参数snake_case、JSON字段lowerCamelCase）
- ✅ 标准响应格式（成功响应、错误响应、分页响应）
- ✅ HTTP状态码使用规范
- ✅ 错误处理规范（统一错误格式、request_id跟踪）
- ✅ 时区处理（统一使用Asia/Shanghai）

## 1.3 基础信息

- **Base URL**: `/api/v1`
- **Content-Type**: `application/json`
- **编码格式**: UTF-8
- **时区**: Asia/Shanghai

## 1.4 自动化工作流程

**正常工作流程**（无需手动API调用）：
1. 任务开始信号 → 自动创建指标数据记录会话 → 启动实时数据记录和基线统计
2. 任务结束信号 → 自动切换到延展数据记录模式
3. 延展期结束 → 自动完成数据记录会话

**手动API的作用**：
- 调试和测试验证
- 独立于任务信号的测试场景

# 2. 通用响应格式

## 2.1 成功响应

所有API成功响应统一使用以下格式：

```json
{
  "code": 200,
  "data": {
    // 具体响应数据
  }
}
```

## 2.2 错误响应

错误响应格式：

```json
{
  "code": 400,
  "data": {
    "error": "错误详细描述",
    "request_id": "123e4567-e89b-12d3-a456-426614174000"
  }
}
```

## 2.3 分页响应

分页API响应格式：

```json
{
  "code": 200,
  "data": {
    "items": [...],    // 当前页数据列表
    "total": 100,      // 总记录数
    "page": 1,         // 当前页码
    "pageSize": 20,    // 每页数量
    "pageCount": 5     // 总页数
  }
}
```

**分页参数说明**：
- 默认值：`page=1`, `page_size=20`
- 最大页面大小限制：`max_page_size=100`（服务器端强制校验）

# 3. 指标数据记录管理接口

## 3.1 查询指标数据记录状态

### 基本信息

- **URL**: `GET /api/v1/tasks/{taskId}/metrics-monitoring/status`
- **功能**: 查询任务指标数据记录会话的当前状态
- **权限**: 需要认证

### 请求参数

**路径参数**：

| 参数名 | 类型   | 必需 | 描述       | 示例                                   |
|--------|--------|------|------------|----------------------------------------|
| taskId | string | 是   | 任务ID     | "task_20250118_001"                    |

### 响应示例

**成功响应 (200)**：

```json
{
  "code": 200,
  "data": {
    "sessionId": "metrics_session_20250118_001",
    "taskId": "task_20250118_001",
    "taskSessionId": "session_20250118_001",
    "deviceIds": ["device_001", "device_002"],
    "metricsTypes": ["cpu", "memory", "traffic"],
    "baselineStart": "2025-01-18T09:00:00+08:00",
    "baselineEnd": "2025-01-18T09:30:00+08:00",
    "monitoringStart": "2025-01-18T09:30:00+08:00",
    "monitoringEnd": null,
    "extendedEnd": null,
    "status": "fully_active",
    "createdAt": "2025-01-18T09:30:00+08:00"
  }
}
```

**错误响应 (404)**：

```json
{
  "code": 404,
  "data": {
    "error": "任务指标数据记录会话不存在",
    "request_id": "123e4567-e89b-12d3-a456-426614174000"
  }
}
```

### 状态枚举说明

| 状态值              | 描述                     |
|--------------------|--------------------------|
| monitoring_active  | 实时数据记录已启动       |
| baseline_analyzing | 基线统计中               |
| fully_active       | 基线统计+数据记录全部就绪 |
| extended_monitoring| 延展数据记录中           |
| completed          | 已完成                   |

## 3.2 手动启动指标数据记录（调试用）

### 基本信息

- **URL**: `POST /api/v1/tasks/{taskId}/metrics-monitoring/manual-start`
- **功能**: 手动启动任务的指标数据记录（仅用于调试）
- **权限**: 需要管理员权限

### 请求参数

**路径参数**：

| 参数名 | 类型   | 必需 | 描述   | 示例                |
|--------|--------|------|--------|---------------------|
| taskId | string | 是   | 任务ID | "task_20250118_001" |

**请求体参数**：

```json
{
  "deviceIds": ["device_001", "device_002"],
  "metricsTypes": ["cpu", "memory", "traffic"],
  "baselineDuration": "30m",
  "extendedDuration": "1h"
}
```

| 参数名            | 类型     | 必需 | 描述           | 示例                              |
|-------------------|----------|------|----------------|-----------------------------------|
| deviceIds         | []string | 是   | 监测设备ID列表 | ["device_001", "device_002"]     |
| metricsTypes      | []string | 是   | 指标类型列表   | ["cpu", "memory", "traffic"]     |
| baselineDuration  | string   | 否   | 基线期时长     | "30m" (默认30分钟)               |
| extendedDuration  | string   | 否   | 延展监测时长   | "1h" (默认1小时)                 |

### 响应示例

**成功响应 (201)**：

```json
{
  "code": 201,
  "data": {
    "sessionId": "metrics_session_20250118_001",
    "taskId": "task_20250118_001",
    "status": "monitoring_active",
    "message": "指标数据记录会话已成功启动"
  }
}
```

## 3.3 手动停止指标数据记录

### 基本信息

- **URL**: `POST /api/v1/tasks/{taskId}/metrics-monitoring/manual-stop`
- **功能**: 手动停止任务的指标数据记录（仅用于调试）
- **权限**: 需要管理员权限

### 请求参数

**路径参数**：

| 参数名 | 类型   | 必需 | 描述   | 示例                |
|--------|--------|------|--------|---------------------|
| taskId | string | 是   | 任务ID | "task_20250118_001" |

### 响应示例

**成功响应 (200)**：

```json
{
  "code": 200,
  "data": {
    "sessionId": "metrics_session_20250118_001",
    "taskId": "task_20250118_001",
    "status": "completed",
    "message": "指标数据记录会话已停止"
  }
}
```

# 4. 指标基线查询接口

## 4.1 查询指标基线

### 基本信息

- **URL**: `GET /api/v1/tasks/{taskId}/metrics-baseline`
- **功能**: 查询任务的指标基线统计数据
- **权限**: 需要认证

### 请求参数

**路径参数**：

| 参数名 | 类型   | 必需 | 描述   | 示例                |
|--------|--------|------|--------|---------------------|
| taskId | string | 是   | 任务ID | "task_20250118_001" |

**查询参数**：

| 参数名     | 类型   | 必需 | 描述         | 示例       |
|------------|--------|------|--------------|------------|
| device_id  | string | 否   | 设备ID筛选   | "device_001" |
| metric_name| string | 否   | 指标名称筛选 | "cpu"      |

### 响应示例

**成功响应 (200)**：

```json
{
  "code": 200,
  "data": {
    "items": [
      {
        "id": "baseline_001",
        "metricsSessionId": "metrics_session_20250118_001",
        "deviceId": "device_001",
        "metricName": "dci_snmp_status_cpu_usage",
        "avgValue": 25.6,
        "maxValue": 45.2,
        "minValue": 15.3,
        "stdDev": 8.4,
        "sampleCount": 30,
        "timeRangeStart": "2025-01-18T09:00:00+08:00",
        "timeRangeEnd": "2025-01-18T09:30:00+08:00",
        "createdAt": "2025-01-18T09:30:00+08:00"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 20,
    "pageCount": 1
  }
}
```

## 4.2 查询指标对比数据

### 基本信息

- **URL**: `GET /api/v1/tasks/{taskId}/metrics-comparison`
- **功能**: 查询任务前后的指标对比数据
- **权限**: 需要认证

### 请求参数

**路径参数**：

| 参数名 | 类型   | 必需 | 描述   | 示例                |
|--------|--------|------|--------|---------------------|
| taskId | string | 是   | 任务ID | "task_20250118_001" |

**查询参数**：

| 参数名     | 类型   | 必需 | 描述         | 示例       |
|------------|--------|------|--------------|------------|
| device_id  | string | 否   | 设备ID筛选   | "device_001" |
| metric_name| string | 否   | 指标名称筛选 | "cpu"      |

### 响应示例

**成功响应 (200)**：

```json
{
  "code": 200,
  "data": {
    "items": [
      {
        "deviceId": "device_001",
        "metricName": "dci_snmp_status_cpu_usage",
        "baseline": {
          "avgValue": 25.6,
          "maxValue": 45.2,
          "minValue": 15.3
        },
        "taskPeriod": {
          "avgValue": 35.8,
          "maxValue": 62.1,
          "minValue": 22.4
        },
        "extendedPeriod": {
          "avgValue": 28.2,
          "maxValue": 48.7,
          "minValue": 18.9
        },
        "changes": {
          "baselineToTask": "+10.2",
          "taskToExtended": "-7.6",
          "overallChange": "+2.6"
        }
      }
    ]
  }
}
```

# 5. 指标数据查询接口

## 5.1 查询指标数据记录

### 基本信息

- **URL**: `GET /api/v1/tasks/{taskId}/metrics-data`
- **功能**: 查询任务期间的指标数据记录（时序数据）
- **权限**: 需要认证

### 请求参数

**路径参数**：

| 参数名 | 类型   | 必需 | 描述   | 示例                |
|--------|--------|------|--------|---------------------|
| taskId | string | 是   | 任务ID | "task_20250118_001" |

**查询参数**：

| 参数名           | 类型   | 必需 | 描述           | 示例                              |
|------------------|--------|------|----------------|-----------------------------------|
| device_id        | string | 否   | 设备ID筛选     | "device_001"                      |
| metric_name      | string | 否   | 指标名称筛选   | "dci_snmp_status_cpu_usage"       |
| monitoring_phase | string | 否   | 监测阶段筛选   | "task_execution"                  |
| start_time       | string | 否   | 开始时间       | "2025-01-18T09:30:00+08:00"       |
| end_time         | string | 否   | 结束时间       | "2025-01-18T10:30:00+08:00"       |
| page             | int    | 否   | 页码           | 1                                 |
| page_size        | int    | 否   | 每页数量       | 20                                |

### 监测阶段枚举

| 阶段值          | 描述     |
|-----------------|----------|
| baseline        | 基线期   |
| task_execution  | 任务期   |
| extended        | 延展期   |

### 响应示例

**成功响应 (200)**：

```json
{
  "code": 200,
  "data": {
    "items": [
      {
        "id": "record_001",
        "metricsSessionId": "metrics_session_20250118_001",
        "deviceId": "device_001",
        "metricName": "dci_snmp_status_cpu_usage",
        "changeType": "data_record",
        "currentValue": 32.5,
        "baselineValue": 25.6,
        "changeMagnitude": 6.9,
        "detectionTime": "2025-01-18T09:35:00+08:00",
        "monitoringPhase": "task_execution",
        "severity": "info",
        "description": "任务执行期间CPU使用率记录",
        "createdAt": "2025-01-18T09:35:00+08:00"
      }
    ],
    "total": 150,
    "page": 1,
    "pageSize": 20,
    "pageCount": 8
  }
}
```

## 5.2 查询指标趋势数据

### 基本信息

- **URL**: `GET /api/v1/tasks/{taskId}/metrics-trends`
- **功能**: 查询任务期间的指标变化趋势数据
- **权限**: 需要认证

### 请求参数

**路径参数**：

| 参数名 | 类型   | 必需 | 描述   | 示例                |
|--------|--------|------|--------|---------------------|
| taskId | string | 是   | 任务ID | "task_20250118_001" |

**查询参数**：

| 参数名     | 类型   | 必需 | 描述         | 示例                        |
|------------|--------|------|--------------|----------------------------|
| device_id  | string | 否   | 设备ID筛选   | "device_001"               |
| metric_name| string | 否   | 指标名称筛选 | "dci_snmp_status_cpu_usage"|
| interval   | string | 否   | 时间间隔     | "5m" (默认1分钟)           |

### 响应示例

**成功响应 (200)**：

```json
{
  "code": 200,
  "data": {
    "deviceId": "device_001",
    "metricName": "dci_snmp_status_cpu_usage",
    "timeRange": {
      "start": "2025-01-18T09:00:00+08:00",
      "end": "2025-01-18T11:30:00+08:00"
    },
    "baseline": {
      "avgValue": 25.6,
      "timeRange": {
        "start": "2025-01-18T09:00:00+08:00",
        "end": "2025-01-18T09:30:00+08:00"
      }
    },
    "dataPoints": [
      {
        "timestamp": "2025-01-18T09:30:00+08:00",
        "value": 26.8,
        "phase": "task_execution"
      },
      {
        "timestamp": "2025-01-18T09:35:00+08:00",
        "value": 32.5,
        "phase": "task_execution"
      }
    ]
  }
}
```

## 5.3 查询指标统计汇总

### 基本信息

- **URL**: `GET /api/v1/tasks/{taskId}/metrics-statistics`
- **功能**: 查询任务的指标统计汇总数据
- **权限**: 需要认证

### 请求参数

**路径参数**：

| 参数名 | 类型   | 必需 | 描述   | 示例                |
|--------|--------|------|--------|---------------------|
| taskId | string | 是   | 任务ID | "task_20250118_001" |

### 响应示例

**成功响应 (200)**：

```json
{
  "code": 200,
  "data": {
    "taskId": "task_20250118_001",
    "sessionId": "metrics_session_20250118_001",
    "summary": {
      "totalDevices": 2,
      "totalMetrics": 6,
      "monitoringDuration": "2h30m",
      "dataPoints": 1800
    },
    "deviceStatistics": [
      {
        "deviceId": "device_001",
        "deviceName": "Switch_001",
        "metrics": [
          {
            "metricName": "dci_snmp_status_cpu_usage",
            "baseline": {
              "avgValue": 25.6,
              "maxValue": 45.2,
              "minValue": 15.3
            },
            "taskPeriod": {
              "avgValue": 35.8,
              "maxValue": 62.1,
              "minValue": 22.4
            },
            "extendedPeriod": {
              "avgValue": 28.2,
              "maxValue": 48.7,
              "minValue": 18.9
            },
            "changeSummary": {
              "maxIncrease": 16.9,
              "avgChange": 2.6,
              "volatility": "medium"
            }
          }
        ]
      }
    ]
  }
}
```

# 6. 数据管理接口

## 6.1 级联删除指标数据

### 基本信息

- **URL**: `DELETE /api/v1/tasks/{taskId}/metrics-monitoring/cascade`
- **功能**: 按正确顺序级联删除任务的所有指标相关数据
- **权限**: 需要管理员权限

### 请求参数

**路径参数**：

| 参数名 | 类型   | 必需 | 描述   | 示例                |
|--------|--------|------|--------|---------------------|
| taskId | string | 是   | 任务ID | "task_20250118_001" |

### 响应示例

**成功响应 (200)**：

```json
{
  "code": 200,
  "data": {
    "taskId": "task_20250118_001",
    "deletedItems": {
      "metricsDataRecords": 1800,
      "baselineRecords": 6,
      "metricsSession": 1
    },
    "message": "指标数据已成功级联删除"
  }
}
```

## 6.2 查询数据关联状态

### 基本信息

- **URL**: `GET /api/v1/tasks/{taskId}/metrics-monitoring/dependencies`
- **功能**: 查询指标数据记录的关联依赖状态
- **权限**: 需要认证

### 请求参数

**路径参数**：

| 参数名 | 类型   | 必需 | 描述   | 示例                |
|--------|--------|------|--------|---------------------|
| taskId | string | 是   | 任务ID | "task_20250118_001" |

### 响应示例

**成功响应 (200)**：

```json
{
  "code": 200,
  "data": {
    "taskId": "task_20250118_001",
    "hasMetricsSession": true,
    "dependencies": {
      "metricsSession": {
        "sessionId": "metrics_session_20250118_001",
        "status": "completed"
      },
      "baselineRecords": {
        "count": 6,
        "deviceIds": ["device_001", "device_002"]
      },
      "dataRecords": {
        "count": 1800,
        "timeRange": {
          "start": "2025-01-18T09:00:00+08:00",
          "end": "2025-01-18T11:30:00+08:00"
        }
      }
    },
    "canDelete": true
  }
}
```

## 6.3 数据清理接口

### 基本信息

- **URL**: `DELETE /api/v1/tasks/{taskId}/metrics-data/cleanup`
- **功能**: 清理指定时间范围外的历史指标数据记录
- **权限**: 需要管理员权限

### 请求参数

**路径参数**：

| 参数名 | 类型   | 必需 | 描述   | 示例                |
|--------|--------|------|--------|---------------------|
| taskId | string | 是   | 任务ID | "task_20250118_001" |

**查询参数**：

| 参数名    | 类型   | 必需 | 描述     | 示例                        |
|-----------|--------|------|----------|-----------------------------|
| before    | string | 是   | 删除此时间之前的数据 | "2025-01-17T00:00:00+08:00" |

### 响应示例

**成功响应 (200)**：

```json
{
  "code": 200,
  "data": {
    "taskId": "task_20250118_001",
    "cleanupBefore": "2025-01-17T00:00:00+08:00",
    "deletedRecords": 120,
    "message": "历史数据清理完成"
  }
}
```

# 7. 错误码说明

## 7.1 通用错误码

| HTTP状态码 | 错误码 | 错误描述                 |
|------------|--------|--------------------------|
| 400        | 40001  | 请求参数格式错误         |
| 400        | 40002  | 必需参数缺失             |
| 400        | 40003  | 参数值超出允许范围       |
| 401        | 40101  | 认证信息缺失             |
| 401        | 40102  | 认证信息无效             |
| 403        | 40301  | 权限不足                 |
| 404        | 40401  | 请求的资源不存在         |
| 409        | 40901  | 资源冲突                 |
| 422        | 42201  | 请求格式正确但无法处理   |
| 500        | 50001  | 服务器内部错误           |
| 503        | 50301  | 服务暂时不可用           |

## 7.2 业务错误码

| HTTP状态码 | 错误码 | 错误描述                      |
|------------|--------|-------------------------------|
| 404        | 40404  | 任务不存在                    |
| 404        | 40405  | 指标数据记录会话不存在        |
| 409        | 40902  | 指标数据记录会话已存在        |
| 422        | 42202  | 任务状态不允许该操作          |
| 422        | 42203  | 指标数据记录会话状态异常      |
| 422        | 42204  | 设备ID列表为空                |
| 422        | 42205  | 指标类型列表为空              |

# 8. 数据模型定义

## 8.1 指标数据记录会话

```json
{
  "sessionId": "string",
  "taskId": "string", 
  "taskSessionId": "string",
  "deviceIds": ["string"],
  "metricsTypes": ["string"],
  "baselineStart": "2025-01-18T09:00:00+08:00",
  "baselineEnd": "2025-01-18T09:30:00+08:00",
  "monitoringStart": "2025-01-18T09:30:00+08:00",
  "monitoringEnd": "2025-01-18T10:30:00+08:00",
  "extendedEnd": "2025-01-18T11:30:00+08:00",
  "status": "completed",
  "createdAt": "2025-01-18T09:30:00+08:00"
}
```

## 8.2 指标基线统计

```json
{
  "id": "string",
  "metricsSessionId": "string",
  "deviceId": "string",
  "metricName": "string",
  "avgValue": 25.6,
  "maxValue": 45.2,
  "minValue": 15.3,
  "stdDev": 8.4,
  "sampleCount": 30,
  "timeRangeStart": "2025-01-18T09:00:00+08:00",
  "timeRangeEnd": "2025-01-18T09:30:00+08:00",
  "createdAt": "2025-01-18T09:30:00+08:00"
}
```

## 8.3 指标数据记录

```json
{
  "id": "string",
  "metricsSessionId": "string",
  "deviceId": "string",
  "metricName": "string",
  "changeType": "data_record",
  "currentValue": 32.5,
  "baselineValue": 25.6,
  "changeMagnitude": 6.9,
  "detectionTime": "2025-01-18T09:35:00+08:00",
  "monitoringPhase": "task_execution",
  "severity": "info",
  "description": "string",
  "createdAt": "2025-01-18T09:35:00+08:00"
}
```

# 9. 使用示例

## 9.1 查询任务指标状态

```bash
curl -X GET \
  "http://localhost:8080/api/v1/tasks/task_20250118_001/metrics-monitoring/status" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json"
```

## 9.2 查询指标趋势数据

```bash
curl -X GET \
  "http://localhost:8080/api/v1/tasks/task_20250118_001/metrics-trends?device_id=device_001&metric_name=dci_snmp_status_cpu_usage" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json"
```

## 9.3 手动启动指标记录（调试用）

```bash
curl -X POST \
  "http://localhost:8080/api/v1/tasks/task_20250118_001/metrics-monitoring/manual-start" \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "deviceIds": ["device_001", "device_002"],
    "metricsTypes": ["cpu", "memory", "traffic"],
    "baselineDuration": "30m",
    "extendedDuration": "1h"
  }'
```

# 10. 注意事项

## 10.1 使用建议

1. **自动化优先**：正常情况下，指标数据记录由任务信号自动触发，无需手动调用启动/停止接口
2. **数据查询优化**：大量数据查询时建议使用分页参数，避免一次性获取过多数据
3. **时间范围限制**：建议查询时间范围不超过7天，超长时间范围可能影响响应性能

## 10.1.1 开发实现规范要求

**根据《03-DCI项目API及代码规范指南.md》，实现时必须遵循**：

1. **请求结构体定义**：所有请求结构体必须在`internal/models`包中统一定义，严禁在handler中使用内联匿名结构体
2. **数据验证**：必须使用Gin的`binding`标签为所有请求结构体字段定义严格验证规则
   - 字符串长度：`binding:"required,min=1,max=100"`
   - 枚举值：`binding:"oneof=cpu memory traffic"`
3. **Swagger注释**：所有API必须包含完整的Swagger注释（@Summary, @Description, @Tags, @Param, @Success, @Failure, @Router）
4. **错误处理**：严禁在错误消息中暴露系统内部敏感信息
5. **请求跟踪**：必须为每个请求生成唯一请求ID并在日志中记录

## 10.2 限制说明

1. **分页限制**：每页最大返回100条记录
2. **查询频率**：建议查询间隔不少于1秒，避免频繁请求影响系统性能
3. **权限要求**：手动启动/停止和删除操作需要管理员权限

## 10.3 数据保留策略

1. **指标数据记录**：默认保留30天，超期数据可通过清理接口删除
2. **基线统计数据**：与任务会话同生命周期，任务删除时一并删除
3. **会话数据**：与任务协同监控会话关联，遵循任务生命周期管理
