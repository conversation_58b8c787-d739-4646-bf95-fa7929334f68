---
title: |
  16-DCI-dciagent模块版本与采集模块关系设计

subtitle: |
  数据监测系统SNMP采集关联关系管理
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-19 | 顾铠羟 | 初始版本，详细定义dciagent模块版本与SNMP采集对象关系 |

# 1 文档介绍

## 1.1 文档目的

本文档详细设计DCI数据监测系统中dciagent各模块版本与SNMP采集对象之间的关系管理机制，包括服务端配置管理、版本控制策略、兼容性机制以及升级流程。该设计旨在确保采集模块版本与采集对象之间的适配性，解决不同厂商设备MIB差异，以及支持持续演进的采集需求。

## 1.2 文档范围

本文档涵盖以下内容：

1. 服务端对dciagent模块版本的管理
2. SNMP采集对象与模块版本的关联关系
3. 采集模块版本控制与兼容性策略
4. 模块与配置升级协同机制
5. 服务端API设计

本文档与《15-DCI-dciagent中Telegraf部署架构与流程》《11-DCI-Telegraf-SNMP采集实现方案》等文档紧密关联，主要聚焦在服务端管理部分。

# 2 总体设计

## 2.1 设计目标

本文档针对dciagent模块版本与SNMP采集对象关系的设计旨在实现以下目标：

1. **版本统一管理**：通过服务端集中管理各采集模块版本，确保版本一致性和可控升级
2. **采集对象关联**：建立采集模块版本与SNMP采集对象之间的映射关系
3. **兼容性保障**：解决不同设备厂商、不同MIB版本的兼容性问题
4. **配置同步**：确保模块版本与配置协同更新，避免因版本不匹配导致的采集问题
5. **灵活部署**：支持渐进式部署和特定场景的版本定制

## 2.2 架构设计

dciagent模块版本与SNMP采集对象关系的管理架构如下：

```mermaid
graph TD
    subgraph "dcimonitor服务端"
        ModuleRepo[模块仓库]
        VersionRegistry[版本注册表]
        ConfigStore[配置存储]
        CompatMatrix[兼容性矩阵]
        DeviceRegistry[设备注册表]
        
        API[模块管理API]
        AdminUI[管理界面]
    end
    
    subgraph "Agent节点"
        Agent[dciagent]
        Telegraf[Telegraf]
    end
    
    subgraph "设备资产"
        DeviceDB[(设备数据库)]
        DeviceInfoSvc[设备信息服务]
    end
    
    AdminUI -- 管理 --> API
    API -- 读写 --> ModuleRepo
    API -- 读写 --> VersionRegistry
    API -- 读写 --> ConfigStore
    API -- 读写 --> CompatMatrix
    
    DeviceInfoSvc -- 提供 --> DeviceRegistry
    DeviceDB -- 同步 --> DeviceRegistry
    
    Agent -- 拉取模块 --> API
    Agent -- 拉取配置 --> API
    Agent -- 管理 --> Telegraf
    
    CompatMatrix -- 关联 --> VersionRegistry
    CompatMatrix -- 关联 --> DeviceRegistry
    
    style ModuleRepo fill:#f9d,stroke:#333,stroke-width:1px
    style VersionRegistry fill:#bbf,stroke:#333,stroke-width:1px
    style ConfigStore fill:#dfd,stroke:#333,stroke-width:1px
    style CompatMatrix fill:#fda,stroke:#333,stroke-width:1px
```

### 2.2.1 核心组件

1. **模块仓库（ModuleRepo）**：
   - 存储各版本dciagent及其采集模块的二进制文件
   - 提供版本查询和下载功能
   - 维护校验和和签名信息

2. **版本注册表（VersionRegistry）**：
   - 记录所有可用模块版本及其元数据
   - 包含版本号、发布日期、特性列表、兼容性说明等信息
   - 支持版本标记（如稳定版、测试版）

3. **配置存储（ConfigStore）**：
   - 存储SNMP采集配置模板
   - 按模块版本和设备类型组织
   - 支持继承和覆盖机制

4. **兼容性矩阵（CompatMatrix）**：
   - 建立模块版本与设备型号/OS版本的兼容关系
   - 定义特定场景下的最佳版本选择规则
   - 记录已知问题和限制

5. **设备注册表（DeviceRegistry）**：
   - 存储受监控设备的基本信息
   - 包含设备ID、型号、厂商、操作系统版本等
   - 与外部设备资产管理系统保持同步

6. **模块管理API**：
   - 提供统一的模块查询、下载、配置接口
   - 实现基于设备兼容性的版本推荐
   - 支持管理员操作和Agent自动更新

## 2.3 核心概念

### 2.3.1 模块定义

dciagent模块体系由以下部分组成：

1. **核心模块**：dciagent主程序，管理所有子模块
2. **采集模块**：如Telegraf、未来可能扩展的其他采集工具
3. **插件/扩展**：Telegraf内部的输入/输出插件等

### 2.3.2 版本标识

版本采用三段式语义化版本号（Semantic Versioning）：

- **主版本号**：不兼容的API变更
- **次版本号**：向后兼容的功能新增
- **修订号**：向后兼容的问题修复

例如：`1.2.3`表示主版本1，次版本2，修订号3。

### 2.3.3 关联关系

SNMP采集对象与采集模块版本的关联关系包含以下维度：

1. **设备厂商维度**：不同厂商设备的MIB差异
2. **设备型号维度**：同一厂商不同型号设备的特性差异
3. **功能维度**：基础监控、性能监控、拓扑发现等不同功能对模块的要求
4. **场景维度**：监控对象数量、采集频率等场景特征

# 3 详细设计

## 3.1 服务端管理设计

### 3.1.1 模块仓库设计

dcimonitor服务端的模块仓库采用分层存储结构：

```
/modules/
|-- dciagent/
|   |-- 1.0.0/
|   |   |-- linux_amd64/
|   |   |   |-- dciagent_1.0.0_linux_amd64.tar.gz
|   |   |   |-- dciagent_1.0.0_linux_amd64.sha256
|   |   |-- darwin_amd64/
|   |   |   |-- dciagent_1.0.0_darwin_amd64.tar.gz
|   |   |   |-- dciagent_1.0.0_darwin_amd64.sha256
|   |-- 1.1.0/
|       |-- ...
|-- telegraf/
    |-- 1.34.2/
    |   |-- linux_amd64/
    |   |   |-- telegraf_1.34.2_linux_amd64.tar.gz
    |   |   |-- telegraf_1.34.2_linux_amd64.sha256
    |   |-- darwin_amd64/
    |       |-- ...
    |-- 1.35.0/
        |-- ...
```

每个模块版本包含以下元数据：

```json
{
  "name": "telegraf",
  "version": "1.34.2",
  "release_date": "2025-05-15",
  "compatibility": {
    "min_dciagent_version": "1.0.0",
    "recommended_dciagent_version": "1.1.0",
    "os_compatibility": ["linux", "darwin"],
    "arch_compatibility": ["amd64", "arm64"]
  },
  "features": [
    "basic_metrics",
    "snmp_v2c",
    "snmp_v3",
    "lldp_discovery"
  ],
  "files": [
    {
      "os": "linux",
      "arch": "amd64",
      "file": "telegraf_1.34.2_linux_amd64.tar.gz",
      "size": 15678945,
      "sha256": "abcdef123456...",
      "download_url": "/api/v1/modules/telegraf/versions/1.34.2/download?os=linux&arch=amd64"
    },
    // 其他系统/架构的文件
  ],
  "release_notes": "改进SNMP查询性能，新增华为S系列交换机支持",
  "status": "stable", // stable, testing, deprecated
  "requires_restart": true
}
```

### 3.1.2 版本兼容矩阵

版本兼容矩阵定义各模块版本与设备的兼容关系：

```json
{
  "device_compatibility": [
    {
      "vendor": "huawei",
      "model_pattern": "S6720.*",
      "os_version": "V200R.*",
      "compatible_modules": {
        "telegraf": {
          "preferred_version": "1.34.2",
          "supported_versions": ["1.33.0", "1.34.0", "1.34.1", "1.34.2"],
          "known_issues": [
            {
              "version": "1.33.0",
              "issue": "CPU利用率采集不准确",
              "workaround": "使用custom_cpu OID替代标准OID"
            }
          ]
        }
      },
      "required_mibs": ["HUAWEI-ENTITY-EXTENT-MIB", "HUAWEI-PORT-MIB"],
      "custom_configs": [
        {
          "name": "huawei_s6720_basic",
          "description": "华为S6720基础监控配置",
          "template_id": "huawei_s6720_basic_v1"
        }
      ]
    },
    // 其他设备型号
  ]
}
```

### 3.1.3 配置模板管理

配置模板按设备类型和功能分类：

1. **基础模板**：适用于所有同类设备的基础配置
2. **厂商模板**：适用于特定厂商所有设备的配置
3. **设备型号模板**：特定设备型号的专用配置
4. **功能模板**：特定功能模块的配置（如LLDP拓扑发现）

模板之间支持继承和组合，通过优先级规则解决冲突：

```json
{
  "template_id": "huawei_s6720_basic_v1",
  "description": "华为S6720基础监控配置",
  "applicable_to": {
    "vendor": "huawei",
    "model_pattern": "S6720.*"
  },
  "parent_templates": ["huawei_common_v1", "base_switch_v1"],
  "override_priority": 100,
  "min_module_version": {
    "telegraf": "1.33.0"
  },
  "config_snippets": {
    "01-basic.conf": "[[inputs.snmp]]\n  # 华为S6720基础配置\n  ...",
    "02-cpu.conf": "[[inputs.snmp.table]]\n  name = \"cpu\"\n  ...",
    "03-memory.conf": "[[inputs.snmp.table]]\n  name = \"memory\"\n  ..."
  },
  "mib_dependencies": ["HUAWEI-ENTITY-EXTENT-MIB.txt", "HUAWEI-PORT-MIB.txt"]
}
```

## 3.2 Agent端协同设计

### 3.2.1 版本检测与升级流程

Agent启动时执行以下流程：

1. 自身版本检测
2. 向服务端报告版本和受监控设备信息
3. 服务端基于兼容矩阵提供版本建议
4. Agent根据配置策略决定是否升级

```mermaid
sequenceDiagram
    participant Agent as dciagent
    participant Server as dcimonitor服务
    participant CompatDB as 兼容性数据库
    
    Agent->>Agent: 检测当前版本
    Agent->>Agent: 收集监控对象信息
    Agent->>Server: 注册/心跳(包含版本和设备信息)
    
    Server->>CompatDB: 查询版本兼容性
    CompatDB-->>Server: 返回最佳版本建议
    
    alt 需要升级
        Server-->>Agent: 返回升级建议
        
        alt 自动升级模式
            Agent->>Server: 请求新版本下载
            Server-->>Agent: 下载二进制文件
            Agent->>Agent: 备份当前版本
            Agent->>Agent: 安装新版本
            Agent->>Agent: 重启服务
        else 手动确认模式
            Agent->>Agent: 记录升级建议
            Note over Agent: 等待管理员确认
        end
    else 版本兼容
        Server-->>Agent: 确认当前版本可用
    end
    
    Agent->>Server: 拉取配置更新
    Server-->>Agent: 返回适配当前版本的配置
```

### 3.2.2 配置与版本协同

Agent端实现以下协同机制：

1. **版本绑定配置**：每个配置版本关联到特定模块版本范围
2. **渐进式部署**：支持部分Agent实例先升级测试
3. **回滚支持**：保留历史版本和配置，支持快速回滚
4. **健康检查**：升级后监控采集状态，异常时自动回滚

配置与版本关联示例：

```toml
# Meta section in telegraf.conf
[meta]
  # 配置版本及其适用的模块版本范围
  config_version = "2025.05.01-huawei-s6720-basic"
  min_telegraf_version = "1.33.0"
  max_telegraf_version = "1.35.999"
  # 配置生成时间戳
  generated_at = "2025-05-01T10:00:00Z"
  # 配置ID用于追踪和回溯
  config_id = "f8d7e6c5-b4a3-2d1e-0f9c-8b7a6e5d4c3b"
```

## 3.3 SNMP采集对象关系管理

### 3.3.1 SNMP采集对象分类

根据不同采集目标对模块版本的要求，将SNMP采集对象分为以下几类：

1. **标准MIB对象**：使用标准MIB定义的对象，如IF-MIB、RFC1213-MIB等
   - 兼容性好，不同模块版本之间差异小
   - 优先使用最新稳定版本

2. **厂商MIB对象**：使用厂商特定MIB定义的对象
   - 受模块版本和MIB解析能力影响
   - 根据厂商和设备型号推荐特定版本

3. **拓扑发现对象**：用于网络拓扑发现的LLDP/CDP数据
   - 涉及多个MIB和数据转换
   - 版本要求较高，需确保拓扑数据的准确性

4. **性能指标对象**：高频采集的性能数据
   - 关注采集效率和数据处理能力
   - 根据采集规模和频率推荐版本

### 3.3.2 MIB文件管理

MIB文件与模块版本的关联管理：

1. **MIB集合**：根据采集需求组织MIB文件集合
   - 基础MIB集合：包含标准MIB
   - 厂商MIB集合：按厂商分类的专用MIB
   - 扩展MIB集合：特殊场景使用的MIB

2. **MIB版本控制**：
   - 每个MIB文件带有版本标识
   - 记录MIB之间的依赖关系
   - 与Telegraf版本的兼容性说明

3. **MIB打包与分发**：
   - 随模块版本一起打包关联的MIB文件
   - 支持按需下载特定MIB集合
   - 确保MIB和配置的一致性

MIB管理记录示例：

```json
{
  "mib_id": "HUAWEI-ENTITY-EXTENT-MIB",
  "version": "V2.0",
  "file_name": "HUAWEI-ENTITY-EXTENT-MIB.txt",
  "sha256": "a1b2c3d4e5f6...",
  "dependencies": [
    "ENTITY-MIB",
    "HUAWEI-MIB"
  ],
  "supported_devices": [
    {
      "vendor": "huawei",
      "model_pattern": "S\\d{4}.*"
    }
  ],
  "compatible_telegraf_versions": [">=1.33.0"],
  "key_oids": [
    {
      "name": "hwEntityCpuUsage",
      "oid": "*******.4.1.2011.*********.*******",
      "description": "CPU使用率",
      "data_type": "Integer32"
    }
  ]
}
```

### 3.3.3 采集配置与版本映射

建立SNMP采集配置与模块版本的映射关系：

1. **配置标签**：为配置添加版本兼容性标签
   - 指定最低兼容版本
   - 指定推荐版本
   - 指定最高兼容版本

2. **配置自适应**：根据模块版本自动调整配置
   - 高版本特性条件包含
   - 低版本兼容性替代设置
   - 版本特定参数调整

3. **配置验证**：服务端验证配置与模块版本的兼容性
   - 静态分析配置内容
   - 检查功能依赖
   - 记录潜在冲突

## 3.4 API设计

### 3.4.1 模块管理API

```
# 获取模块列表
GET /api/v1/modules

# 获取特定模块的版本列表
GET /api/v1/modules/{module_name}/versions

# 获取特定版本的详细信息
GET /api/v1/modules/{module_name}/versions/{version}

# 下载特定版本的模块二进制文件
GET /api/v1/modules/{module_name}/versions/{version}/download
  ?os={os}&arch={arch}

# 获取校验和信息
GET /api/v1/modules/{module_name}/versions/{version}/checksum
  ?os={os}&arch={arch}

# 上传新版本模块
POST /api/v1/modules/{module_name}/versions/{version}/upload
```

### 3.4.2 兼容性查询API

```
# 查询特定设备的最佳模块版本
GET /api/v1/compatibility/device/{device_id}/recommended-versions

# 查询特定模块版本支持的设备列表
GET /api/v1/compatibility/module/{module_name}/version/{version}/devices

# 检查特定模块版本与设备的兼容性
GET /api/v1/compatibility/check
  ?device_id={device_id}&module={module}&version={version}
```

### 3.4.3 配置管理API

```
# 获取适用于特定设备和模块版本的配置模板
GET /api/v1/config/templates
  ?device_id={device_id}&module={module}&version={version}

# 获取特定配置模板的详细信息
GET /api/v1/config/templates/{template_id}

# 获取特定配置模板的配置片段
GET /api/v1/config/templates/{template_id}/snippets/{snippet_name}

# 创建或更新配置模板
POST /api/v1/config/templates
```

## 3.5 升级与回滚机制

### 3.5.1 升级策略

采用分阶段升级策略：

1. **评估阶段**：
   - 新版本在测试环境部署
   - 兼容性测试覆盖不同设备型号
   - 性能和稳定性基准测试

2. **灰度发布**：
   - 选择少量非关键节点升级
   - 监控升级后的采集质量和性能
   - 收集问题反馈和调整建议

3. **全面部署**：
   - 按区域或设备类型分批升级
   - 制定应急回滚计划
   - 实时监控升级状态

4. **回顾与调整**：
   - 总结升级经验
   - 完善兼容性矩阵
   - 更新文档和最佳实践

### 3.5.2 回滚流程

当检测到升级后的问题时，执行以下回滚流程：

1. **问题确认**：
   - 确认问题与模块版本相关
   - 评估影响范围和严重性
   - 决定是回滚还是修复

2. **回滚准备**：
   - 确认回滚目标版本
   - 验证回滚配置兼容性
   - 准备回滚通知

3. **回滚执行**：
   - 停止当前版本进程
   - 恢复旧版本二进制文件
   - 恢复关联的配置文件
   - 启动并验证旧版本进程

4. **回滚验证**：
   - 确认采集功能恢复
   - 监控性能和数据质量
   - 记录回滚结果

5. **问题分析**：
   - 分析导致回滚的根本原因
   - 更新兼容性矩阵和文档
   - 制定修复计划
