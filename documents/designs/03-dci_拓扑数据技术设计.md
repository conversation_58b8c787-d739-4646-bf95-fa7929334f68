---
title: |
  DCI拓扑数据技术设计

subtitle: |
  DCI-Monitor拓扑数据处理与发送技术方案
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-15 | 顾铠羟 | 初始版本           |
| V2.0 | 2025-05-22 | 顾铠羟 | 优化LLDP数据处理逻辑 |

# 1 文档介绍

## 1.1 文档目的

本文档详细阐述基于SNMP从网络设备采集LLDP数据，通过DCI-Monitor系统处理并转换为标准化拓扑数据的技术方案。主要涵盖从数据采集、处理、标识符转换到生成图数据库可用JSON格式的端到端技术实现。

## 1.2 文档范围

本文档覆盖从Telegraf SNMP配置、Kafka传输、后端数据处理（包括设备/端口标识符转换和拓扑计算）、到生成结构化拓扑数据的全流程设计。图数据的存储、数据丰富和UI展示由多云管理系统负责。

## 1.3 文档关联

本拓扑数据技术设计与以下文档密切关联：

1. **《02-网络自动化平台-数据监测系统技术概要设计》**：提供整体技术架构，本文档实现其中拓扑数据处理部分。

2. **《11-DCI-Telegraf-SNMP采集实现方案》**：本文档采用的SNMP数据采集方法与配置基于该文档标准。

3. **《13-DCI-Kafka主题设计与规划》**：本文档中的Kafka主题设计遵循该文档规范。

4. **《12-DCI数据监测系统-MySQL数据库设计》**：拓扑数据处理过程中涉及的数据库表结构和查询基于该文档。

根据文档路书中的数据库依赖关系图，拓扑数据模块主要依赖于MySQL数据库进行设备信息、端口信息和拓扑关系的存储。

# 2 LLDP数据采集设计

## 2.1 LLDP协议与OID概述

LLDP (Link Layer Discovery Protocol) 是一种链路层发现协议，用于获取网络设备间的连接关系。LLDP信息通常存储在标准LLDP-MIB (`.1.0.8802.*******`) 中。通过SNMP协议可采集这些信息，构建网络拓扑视图。

### 2.1.1 关键OID说明

以下是拓扑数据采集的关键OID：

* **本地端口标识符 (lldpLocPortId)**：
  * OID: `.1.0.8802.*******.*******.<ifIndex>`
  * 获取设备所有端口的名称，如 STRING: "10GE1/0/1"
  * 示例：`iso.0.8802.*******.*******.8 = STRING: "10GE1/0/1"`

* **远端设备名称 (lldpRemSysName)**：
  * OID: `.1.0.8802.*******.4.1.1.9.<timeMark>.<localPortIndex>.<index>`
  * 获取与本地端口连接的远端设备名称
  * 示例：`iso.0.8802.*******.4.1.1.9.123851676.8.1 = STRING: "SW2"`

* **远端端口标识符 (lldpRemPortId)**：
  * OID: `.1.0.8802.*******.4.1.1.7.<timeMark>.<localPortIndex>.<index>`
  * 获取与本地端口连接的远端端口名称
  * 示例：`iso.0.8802.*******.4.1.1.7.123851676.8.1 = STRING: "10GE1/0/1"`

* **远端设备机箱ID (lldpRemChassisId)**：
  * OID: `.1.0.8802.*******.4.1.1.5.<timeMark>.<localPortIndex>.<index>`
  * 获取远端设备的唯一标识（通常为MAC地址）
  * 示例：`iso.0.8802.*******.4.1.1.5.123851676.8.1 = Hex-STRING: 44 9B C1 07 8C 71`

### 2.1.2 OID索引结构分析

LLDP远端表的OID索引结构为`.<timeMark>.<localPortIndex>.<index>`，其中：

* `timeMark`：时间戳标记，可忽略具体值
* `localPortIndex`：本地端口索引，对应ifIndex，**这是关联本地端口的关键**
* `index`：远端设备索引，通常为1

## 2.2 Telegraf SNMP采集配置

### 2.2.1 两阶段采集策略

为确保准确构建拓扑关系，采用两阶段采集策略：

1. **阶段一：采集本地端口信息**
   * 采集`.1.0.8802.*******.*******`获取所有本地端口名称与索引映射
   * 这些数据会存储在服务端数据库中，用于后续匹配

2. **阶段二：采集LLDP邻居信息**
   * 采集`.1.0.8802.*******.4.1.1.9`获取远端设备名称
   * 采集`.1.0.8802.*******.4.1.1.7`获取远端端口名称
   * 采集`.1.0.8802.*******.4.1.1.5`获取远端设备标识

### 2.2.2 Telegraf配置示例

采用单个Telegraf实例，通过为不同的SNMP输入插件设置独立的采集周期（`interval`）和标签（`tags`），实现对端口信息和LLDP邻居信息的分别采集。所有原始数据将发送到同一个Kafka Topic，由下游TopologyProcessor根据标签进行区分处理。

```toml
[agent]
  # 全局Agent配置，可以设置一个通用或较短的刷新间隔
  # 具体采集间隔由各input插件自行定义
  interval = "1m"  # 全局默认采集间隔，实际会被input覆盖
  round_interval = true
  metric_batch_size = 1000
  metric_buffer_limit = 10000
  collection_jitter = "0s"
  flush_interval = "30s" # 统一的刷新间隔，确保数据及时发送
  flush_jitter = "0s"
  precision = ""
  debug = true
  quiet = false
  logfile = "${LOG_FILE}" # 应从环境变量或配置文件中获取
  hostname = "" # 建议由Telegraf自动获取或在特定场景下配置
  omit_hostname = false

# --- 端口信息采集 (每24小时一次) ---
[[inputs.snmp]]
  interval = "24h"  # 专门为端口信息设置的采集间隔
  agents = [
    "udp://************:161",
    "udp://************:161"
  ]
  timeout = "15s"
  retries = 3
  version = 2
  community = "dcilab2025"

  [inputs.snmp.tags]
    data_source_type = "port_info" # 用于下游区分数据来源
    data_collection_stage = "raw"

  # 系统基本信息
  [[inputs.snmp.field]]
    name = "sysName"
    oid = ".*******.*******.0"  # SNMPv2-MIB::sysName.0
    is_tag = true
    
  # 本地端口信息表
  [[inputs.snmp.table]]
    name = "lldp_local_ports"
    inherit_tags = ["sysName"]
    oid = ".1.0.8802.*******.3.7"  # LLDP-MIB::lldpLocPortTable

    [[inputs.snmp.table.field]]
      name = "ifIndex"
      oid = ".1.0.8802.*******.*******"  # LLDP-MIB::lldpLocPortNum (lldpLocPortTable的索引)
      is_tag = true

    [[inputs.snmp.table.field]]
      name = "portId"
      oid = ".1.0.8802.*******.*******"  # LLDP-MIB::lldpLocPortId

    [[inputs.snmp.table.field]]
      name = "portDesc"
      oid = ".1.0.8802.*******.*******"  # LLDP-MIB::lldpLocPortDesc

  # 接口表信息以增强端口数据
  [[inputs.snmp.table]]
    name = "interface"
    inherit_tags = ["sysName"]
    oid = ".*******.2.1.2.2"  # IF-MIB::ifTable

    [[inputs.snmp.table.field]]
      name = "ifIndex"
      oid = ".*******.2.1.2.2.1.1"  # IF-MIB::ifIndex
      is_tag = true

    [[inputs.snmp.table.field]]
      name = "ifDescr"
      oid = ".*******.2.1.2.2.1.2"  # IF-MIB::ifDescr

    [[inputs.snmp.table.field]]
      name = "ifType"
      oid = ".*******.2.1.2.2.1.3"  # IF-MIB::ifType

    [[inputs.snmp.table.field]]
      name = "ifAdminStatus"
      oid = ".*******.2.1.2.2.1.7"  # IF-MIB::ifAdminStatus

    [[inputs.snmp.table.field]]
      name = "ifOperStatus"
      oid = ".*******.2.1.2.2.1.8"  # IF-MIB::ifOperStatus

# --- LLDP邻居信息采集 (每10分钟一次) ---
[[inputs.snmp]]
  interval = "10m"  # 专门为LLDP邻居信息设置的采集间隔
  agents = [
    "udp://************:161",
    "udp://************:161"
  ]
  timeout = "15s"
  retries = 3
  version = 2
  community = "dcilab2025"

  [inputs.snmp.tags]
    data_source_type = "lldp_info" # 用于下游区分数据来源
    data_collection_stage = "raw"
  
  # 系统基本信息
  [[inputs.snmp.field]]
    name = "sysName"
    oid = ".*******.*******.0"  # SNMPv2-MIB::sysName.0
    is_tag = true
    
  # LLDP远端设备表 - 使用独立的field采集，因为其索引结构复杂，不适合标准table
  [[inputs.snmp.field]]
    name = "lldpRemSysName"
    oid = ".1.0.8802.*******.4.1.1.9"  # LLDP-MIB::lldpRemSysName
    
  [[inputs.snmp.field]]
    name = "lldpRemPortId"
    oid = ".1.0.8802.*******.4.1.1.7"  # LLDP-MIB::lldpRemPortId
    
  [[inputs.snmp.field]]
    name = "lldpRemChassisId"
    oid = ".1.0.8802.*******.4.1.1.5"  # LLDP-MIB::lldpRemChassisId

# --- Kafka 输出 (单一 Topic) ---
[[outputs.kafka]]
  brokers = ["dcikafka.intra.citic-x.com:30002"]
  # 所有原始拓扑相关数据发送到此共享Topic
  topic = "dci.monitor.v1.defaultchannel.topology.raw_data"
  data_format = "json"
  compression_codec = 2  # 2 = snappy
  max_retry = 3
  max_message_bytes = 1000000
  # Telegraf会自动将input插件中定义的tags附加到metric上
  # 无需在此处配置tagpass/tagexclude，下游处理器根据data_source_type区分
```

### 2.2.3 采集频率与策略

LLDP信息相对稳定，采集频率设置为10-15分钟一次较为合理。对于大型网络，可采用分布式部署多个Telegraf实例，每个实例负责部分网络设备的采集。

# 3 拓扑数据处理设计

## 3.1 数据流转架构

```mermaid
graph LR
    A[Telegraf SNMP] --> B[Kafka原始数据]
    B --> C[TopologyProcessor]
    C --> D[数据库查询与标识符转换]
    D --> E[拓扑关系计算]
    E --> F[图数据JSON生成]
    F --> H[MySQL存储图数据JSON]
    F --> G[Kafka发送处理后数据]
```

## 3.2 Kafka消息传输

### 3.2.1 Topic设计

按照DCI项目的命名规范，拓扑数据相关的Kafka主题如下：

* `dci.monitor.v1.defaultchannel.topology.raw_data`：存储Telegraf采集的原始拓扑相关数据（包括端口信息和LLDP邻居信息）。TopologyProcessor将从此主题消费数据，并根据消息中的`data_source_type`标签区分处理。
* `dci.monitor.v1.defaultchannel.topology.processed`：存储处理后的结构化拓扑数据。

主题配置参数：
* 原始数据主题（raw_data）：
  * 分区数：3  # 考虑到可能的数据量和并行处理需求
  * 副本因子：2
  * 数据保留期限：3天（72小时） # 原始数据保留时间可适当延长，便于问题追溯

* 处理后数据主题（processed）：
  * 分区数：3
  * 副本因子：2
  * 数据保留期限：7天（168小时） # 处理后的标准数据可保留更长时间

这些主题遵循《13-DCI-Kafka主题设计与规划》中定义的格式：`{项目前缀}.{子系统名称}.{版本}.{渠道ID}.{数据类型}.{来源/细分}`，其中：
* 项目前缀：`dci`
* 子系统名称：`monitor`
* 版本：`v1`
* 渠道ID：`defaultchannel`（占位符）
* 数据类型：`topology`
* 来源/细分：`raw_data`（原始混合数据）、`processed`（处理后数据）

### 3.2.2 Raw消息格式示例

由于端口信息和LLDP邻居信息将发送到同一个Topic，下游处理器需要通过消息中的`tags`（特别是`data_source_type`）来识别数据类型。

#### ******* 端口信息消息格式 (示例)

```json
{
  "fields": {
    "portId": "10GE1/0/1",
    "portDesc": "Connect to SW2-interface"
  },
  "name": "lldp_local_ports", // Telegraf measurement name
  "tags": {
    "agent_host": "************",
    "sysName": "SW1",
    "ifIndex": "8",
    "data_source_type": "port_info", // 关键区分标签
    "data_collection_stage": "raw"
  },
  "timestamp": 1747903450
}
```

#### ******* LLDP邻居信息消息格式 (示例)

```json
{
  "fields": {
    "lldpRemChassisId": "44:9B:C1:07:8C:71",
    "lldpRemPortId": "10GE1/0/1",
    "lldpRemSysName": "SW2"
  },
  "name": "snmp", // 当使用[[inputs.snmp.field]]时，measurement name通常是'snmp'
  "tags": {
    "agent_host": "************",
    "sysName": "SW1", // 如果在[[inputs.snmp]]中采集并设为tag
    // 注意：LLDP远端表的索引 (timeMark, localPortIndex, index) 不会直接作为顶层tag出现
    // 它们是OID的一部分，Telegraf采集原始field时，其值会直接打平到fields中，或者需要特定配置提取
    // 在我们当前的[[inputs.snmp.field]]配置下，索引信息不会直接作为tag出现
    // 我们需要依赖从原始OID字符串中解析这些信息，或者调整Telegraf配置来提取它们作为tag (如果Telegraf支持对field的OID索引部分进行tagging)
    // 鉴于目前的Telegraf配置，localPortIndex需要从LLDP Rem OID中提取，而不是直接作为tag
    "data_source_type": "lldp_info", // 关键区分标签
    "data_collection_stage": "raw"
  },
  "timestamp": 1747903450
}
```

**重要说明关于LLDP邻居信息的tags**：

在使用 `[[inputs.snmp.field]]` 采集如 `lldpRemSysName` 等表数据时，Telegraf默认不会将构成OID索引的各个部分（如 `timeMark`, `localPortIndex`, `index`）自动提取为tags。这些索引值是数据点标识符的一部分，包含在返回的OID中。下游的`TopologyProcessor`在处理`lldp_info`类型的消息时，需要：
1.  获取每个field的完整OID（Telegraf通常会在原始数据中提供，或需要配置Telegraf的`name_override`或类似机制来保留完整的OID信息）。
2.  从该OID字符串中解析出`localPortIndex`等关键索引信息。这是因为我们之前设计的通过`[[inputs.snmp.table]]`配合`index_as_tag = true`来自动获取索引作为tag的方式，在当前合并为一个Telegraf实例且LLDP远端信息使用`[[inputs.snmp.field]]`采集时，行为会有所不同。

如果希望将`localPortIndex`等作为tag直接出现在Kafka消息中，需要进一步研究Telegraf的`processors.regex`或`processors.parser`等插件，在数据发送到Kafka前对metric进行处理，从OID中提取并添加这些tag。目前的设计假定`TopologyProcessor`具备从OID字符串解析索引的能力。

## 3.3 数据处理与标识符转换

### 3.3.1 TopologyProcessor设计

创建专门的Go服务组件TopologyProcessor，负责处理拓扑数据。其核心功能包括：

1. 消费共享的原始数据Kafka Topic: `dci.monitor.v1.defaultchannel.topology.raw_data`。
2. 解析JSON消息，检查`tags`中的`data_source_type`字段。
3. **数据分流处理**：
    * 如果 `data_source_type` 为 `"port_info"`：
        * 解析端口信息，存储到`monitor_device_port_info`表。
    * 如果 `data_source_type` 为 `"lldp_info"`：
        * 解析LLDP邻居信息。
        * 执行标识符转换和端口映射 (依赖已存储的端口信息)。
        * 计算拓扑关系。
        * 生成图数据库可用的JSON格式。
        * 将处理后的数据推送到`dci.monitor.v1.defaultchannel.topology.processed`主题。
        * 将拓扑快照存储到MySQL相关表 (`monitor_topology_snapshot`, `monitor_topology_node`, `monitor_topology_edge`)。
4. 错误处理和日志记录。

### 3.3.2 OID索引提取算法

从LLDP远端设备OID中提取本地端口索引的核心算法：

#### 3.3.2.1 端口信息预采集机制

本地端口信息采集是LLDP拓扑发现的前置步骤。本地端口索引（ifIndex）通过OID `.1.0.8802.*******.*******` 预先采集并存储在数据库中，为后续LLDP邻居信息处理提供必要的映射关系。这种两阶段处理机制的设计原因如下：

1. **数据变化频率差异**：本地端口信息相对稳定，变化频率低，而LLDP邻居信息可能随网络拓扑变化而频繁更新
2. **数据关联需求**：LLDP邻居信息中的本地端口索引需要映射到实际端口名称（如"10GE1/0/1"）以便理解和展示
3. **采集效率考虑**：分离采集可降低系统负载，优化网络带宽使用

因此，系统采用两个独立的采集周期和数据流：
- 端口信息采集: 每天一次或设备变更时触发
- LLDP邻居采集: 每10-15分钟执行一次

#### 3.3.2.2 端口信息存储设计

为存储设备端口信息，系统增加以下数据库表：

```sql
CREATE TABLE `monitor_device_port_info` (
  `id` varchar(36) NOT NULL COMMENT '记录ID，UUID格式',
  `device_id` varchar(36) NOT NULL COMMENT '设备ID',
  `if_index` int NOT NULL COMMENT '接口索引号(ifIndex)',
  `port_name` varchar(255) NOT NULL COMMENT '端口名称，如"10GE1/0/1"',
  `port_desc` varchar(512) DEFAULT NULL COMMENT '端口描述',
  `port_type` varchar(50) DEFAULT NULL COMMENT '端口类型',
  `admin_status` tinyint DEFAULT NULL COMMENT '管理状态: 1=up, 2=down',
  `oper_status` tinyint DEFAULT NULL COMMENT '运行状态: 1=up, 2=down',
  `mac_address` varchar(17) DEFAULT NULL COMMENT '端口MAC地址',
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_ifindex` (`device_id`,`if_index`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_last_updated` (`last_updated`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备端口信息表';
```

#### ******* 端口信息采集流程

端口信息采集采用单独的Telegraf配置和独立的Kafka主题：

1. **专用采集配置**：
   ```toml
   [[inputs.snmp]]
     # 设备列表
     agents = ["udp://{{.IP}}:161"]
     community = "{{.Community}}"
     
     # 本地端口信息表
     [[inputs.snmp.table]]
       name = "lldp_local_ports"
       oid = ".1.0.8802.*******.3.7"
       
       [[inputs.snmp.table.field]]
         name = "ifIndex"
         oid = ".1.0.8802.*******.*******"
         is_tag = true
       
       [[inputs.snmp.table.field]]
         name = "portId"
         oid = ".1.0.8802.*******.*******"
         
       [[inputs.snmp.table.field]]
         name = "portDesc"
         oid = ".1.0.8802.*******.*******"
   ```

2. **专用Kafka主题**：
   ```
   dci.monitor.v1.defaultchannel.topology.port_info
   ```
   
   主题配置：
   - 分区数：2
   - 副本因子：2
   - 数据保留期限：7天（168小时）

#### ******* LLDP数据处理中的端口索引映射

在TopologyProcessor中，LLDP邻居信息处理时会执行以下映射逻辑：

```go
func extractLocalPortIndex(oidIndex string) (string, error) {
    // 输入示例：123851676.8.1
    parts := strings.Split(oidIndex, ".")
    if len(parts) != 3 {
        return "", errors.New("invalid OID index format")
    }
    
    // 返回中间部分，即本地端口索引
    return parts[1], nil
}

func mapToPortName(deviceId string, ifIndex string) (string, error) {
    // 查询数据库获取端口名称
    var portName string
    err := db.QueryRow(
        "SELECT port_name FROM monitor_device_port_info WHERE device_id = ? AND if_index = ?",
        deviceId, ifIndex).Scan(&portName)
    
    if err != nil {
        return "", fmt.Errorf("port mapping not found: %v", err)
    }
    
    return portName, nil
}
```

通过这种方式，系统可以准确地将LLDP邻居信息中的本地端口索引映射到人类可读的端口名称，从而构建完整的网络拓扑视图。

### 3.3.3 标识符转换流程

标识符转换是将LLDP数据中的各种标识符转换为数据库标准ID的过程：

1. **本地设备标识转换**
   * 输入：LLDP消息中的`agent_host`（一般是IP地址）
   * 查询：在`dci_device`表中查找匹配的记录，获取`id`
   * SQL示例：`SELECT id FROM dci_device WHERE manage_ip = ?`

2. **本地端口标识转换**
   * 输入：本地设备ID和从LLDP OID提取的`localPortIndex`
   * 查询：在`dci_logic_port_device`表中查找匹配记录
   * SQL示例：`SELECT logic_port_id FROM dci_logic_port_device WHERE device_id = ? AND port = ?`

3. **远端设备标识转换**
   * 输入：LLDP消息中的`remSysName`或`remChassisId`
   * 查询：在`dci_device`表中查找匹配记录
   * SQL示例：`SELECT id FROM dci_device WHERE name = ? OR chassis_id = ?`

4. **远端端口标识转换**
   * 输入：远端设备ID和`remPortId`
   * 查询：在`dci_logic_port_device`表中查找匹配记录
   * SQL示例：`SELECT logic_port_id FROM dci_logic_port_device WHERE device_id = ? AND port = ?`

## 3.4 拓扑关系构建

### 3.4.1 拓扑模型设计

拓扑模型采用图结构，由节点(Vertex)和边(Edge)组成：

* **节点**: 网络设备，属性包括设备ID、名称等
* **边**: 设备间的连接关系，属性包括本地端口、远端端口等

### 3.4.2 拓扑变更计算

对于新采集的LLDP数据，与现有拓扑进行比较，计算拓扑变更：

1. 新增节点：数据库中不存在的设备
2. 新增边：数据库中不存在的连接关系
3. 删除边：数据库中存在但新数据中不存在的连接关系

### 3.4.3 处理算法流程

```mermaid
sequenceDiagram
    participant T as Telegraf
    participant KRaw as Kafka (topology.raw_data)
    participant P as TopologyProcessor
    participant D as Database
    participant KProcessed as Kafka (topology.processed)

    T->>KRaw: 发送端口信息数据 (tag: port_info)
    T->>KRaw: 发送LLDP邻居数据 (tag: lldp_info)

    KRaw->>P: 消费混合原始数据
    P->>P: 解析JSON, 检查data_source_type tag

    alt data_source_type == "port_info"
        P->>P: 解析端口信息
        P->>D: 存储端口信息至monitor_device_port_info
        D->>P: 确认存储
    else data_source_type == "lldp_info"
        P->>P: 解析LLDP邻居信息, 提取OID索引等
        P->>D: 查询设备基本信息
        D->>P: 返回设备信息
        P->>D: (依赖已存储的端口信息) 查询本地端口名称
        D->>P: 返回本地端口名称
        P->>D: 查询远端设备ID
        D->>P: 返回远端设备ID
        P->>D: 查询远端端口ID
        D->>P: 返回远端端口ID
        P->>P: 构建拓扑关系
        P->>P: 生成图数据库JSON
        P->>D: 存储拓扑快照到MySQL
        P->>KProcessed: 发送处理后拓扑数据
    end
```

TopologyProcessor处理来自共享Topic `dci.monitor.v1.defaultchannel.topology.raw_data` 的数据，其核心流程根据消息中的`data_source_type`标签进行分支：

#### 3.4.3.1 端口信息处理 (当 `data_source_type` = `"port_info"`)

此分支处理标记为端口信息的数据：

1. **端口信息消费与识别**
   * TopologyProcessor从`topology.raw_data`主题消费消息。
   * 通过检查消息`tags`中的`data_source_type == "port_info"`来识别此类数据。

2. **端口信息解析**
   * 解析JSON格式的端口数据，提取关键字段：
     * `sysName`：设备名称 (来自tag)
     * `ifIndex`：端口索引号 (来自tag)
     * `portId`：端口名称（如"10GE1/0/1") (来自field)
     * `portDesc`：端口描述（可选）(来自field)

3. **设备ID解析**
   * 根据`sysName`或`agent_host` (IP地址，来自tag) 查询设备数据库，获取内部设备ID。

4. **端口信息存储**
   * 将端口信息写入`monitor_device_port_info`表。
   * 每条记录包含设备ID、端口索引、端口名称等信息。
   * 带有时间戳，用于判断数据新鲜度。

5. **历史比对 (可选)**
   * 可对比历史端口信息，记录端口变更（如添加、删除、更名）。

#### ******* LLDP邻居信息处理 (当 `data_source_type` = `"lldp_info"`)

此分支处理标记为LLDP邻居信息的数据：

1. **LLDP数据消费与识别**
   * TopologyProcessor从`topology.raw_data`主题消费消息。
   * 通过检查消息`tags`中的`data_source_type == "lldp_info"`来识别此类数据。

2. **OID索引解析与数据提取**
   * 解析LLDP邻居信息。关键在于从 `fields` 中的原始OID字符串（或者Telegraf处理后保留的OID信息）中提取出本地端口索引（`localPortIndex`）。
   * 例如，如果`lldpRemSysName`的OID是 `.1.0.8802.*******.4.1.1.9.<timeMark>.<localPortIndex>.<index>`，则需要从中解析出`<localPortIndex>`。

3. **端口映射 (依赖端口信息)**
   * 使用先前已处理并存储在`monitor_device_port_info`表中的数据，将解析出的`localPortIndex`（结合设备ID）映射到实际的本地端口名称。
   * 如果找不到映射，记录错误并可能跳过该条LLDP数据或标记为不完整。

4. **设备标识符转换**
   * 将LLDP数据中的设备名称（`remSysName`）、MAC地址（`remChassisId`）等原始标识符转换为系统内部的设备ID。
   * 转换远端端口标识符（`remPortId`）为系统内部端口ID，这可能需要结合远端设备ID和端口名称查询`monitor_device_port_info`或类似表（如果远端设备也由本系统管理并采集了端口信息）。

5. **拓扑构建**
   * 根据源设备/端口和目标设备/端口构建连接关系。
   * 对比现有拓扑数据，识别变更（新增、删除连接）。

6. **结果存储 (MySQL)**
   * 将形成的拓扑数据（节点和边）存储到MySQL数据库中的`monitor_topology_snapshot`, `monitor_topology_node`, `monitor_topology_edge`表。
   * 创建新的拓扑快照记录或更新现有快照。

7. **数据发送 (Kafka)**
   * 将标准化的拓扑数据格式化为图数据库所需的JSON格式。
   * 发送到`dci.monitor.v1.defaultchannel.topology.processed`主题，供下游图数据库等系统消费。

这种单一Topic、按tag分流的处理方法简化了Telegraf的输出配置，并将数据分类逻辑集中到了TopologyProcessor中，使得数据流管理更为清晰。

# 4 输出数据格式设计

## 4.1 图数据JSON格式

处理后的数据采用标准化的JSON格式，用于图数据库存储：

### 4.1.1 节点(Vertex)格式

    ```json
    {
      "operation": "UPSERT_VERTEX",
  "vertex_id": "device_123",
      "tag": "device",
      "properties": {
    "name": "Switch-01",
    "nms_id": "device_123",
    "device_type": "switch"
  },
  "timestamp": 1747903450
}
```

### 4.1.2 边(Edge)格式

    ```json
    {
      "operation": "UPSERT_EDGE",
      "edge_type": "connected_to",
  "source_vid": "device_123",
  "destination_vid": "device_456",
      "properties": {
    "local_port_name": "10GE1/0/1",
    "remote_port_name": "10GE1/0/2",
    "local_port_id": "port_789",
    "remote_port_id": "port_101",
    "last_seen": 1747903450
  },
  "timestamp": 1747903450
}
```

## 4.2 数据字段说明

* **vertex_id/source_vid/destination_vid**: 使用`dci_device.id`值
* **local_port_id/remote_port_id**: 使用`dci_logic_port.id`值
* **operation**: 操作类型，支持UPSERT_VERTEX、UPSERT_EDGE、DELETE_EDGE
* **timestamp**: 数据生成时间戳，Unix时间格式

## 4.3 MySQL存储设计

为存储拓扑数据和支持拓扑变更追踪，系统在MySQL中设计了一套完整的表结构，包括设备信息表、端口信息表、标识符映射表、LLDP缓存表和拓扑快照表等，用于记录原始数据和计算后的拓扑关系。

### 4.3.1 设备和端口基础信息表

#### ******* 设备信息表 (monitor_device_info)

```sql
CREATE TABLE IF NOT EXISTS `monitor_device_info` (
  `id` varchar(36) NOT NULL COMMENT '设备ID，UUID格式',
  `device_name` varchar(255) NOT NULL COMMENT '设备名称',
  `device_type` varchar(50) DEFAULT NULL COMMENT '设备类型',
  `manage_ip` varchar(45) DEFAULT NULL COMMENT '管理IP地址',
  `chassis_id` varchar(50) DEFAULT NULL COMMENT '设备底盘ID（通常是MAC地址）',
  `vendor` varchar(100) DEFAULT NULL COMMENT '设备厂商',
  `model` varchar(100) DEFAULT NULL COMMENT '设备型号',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1=正常，0=禁用',
  `last_seen` timestamp NULL DEFAULT NULL COMMENT '最后一次发现时间',
  `created_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_name` (`device_name`),
  KEY `idx_manage_ip` (`manage_ip`),
  KEY `idx_chassis_id` (`chassis_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备信息表';
```

该表存储网络设备的基本信息，为拓扑节点提供设备层面的数据支持，包括设备名称、类型、IP地址和厂商等基础信息。

#### ******* 设备端口信息表 (monitor_device_port_info)

```sql
CREATE TABLE IF NOT EXISTS `monitor_device_port_info` (
  `id` varchar(36) NOT NULL COMMENT '记录ID，UUID格式',
  `device_id` varchar(36) NOT NULL COMMENT '设备ID',
  `if_index` int NOT NULL COMMENT '接口索引号(ifIndex)',
  `port_name` varchar(255) NOT NULL COMMENT '端口名称，如"10GE1/0/1"',
  `port_desc` varchar(512) DEFAULT NULL COMMENT '端口描述',
  `port_type` varchar(50) DEFAULT NULL COMMENT '端口类型',
  `admin_status` tinyint DEFAULT NULL COMMENT '管理状态: 1=up, 2=down',
  `oper_status` tinyint DEFAULT NULL COMMENT '运行状态: 1=up, 2=down',
  `mac_address` varchar(17) DEFAULT NULL COMMENT '端口MAC地址',
  `speed` bigint DEFAULT NULL COMMENT '端口速率(bps)',
  `mtu` int DEFAULT NULL COMMENT '最大传输单元(字节)',
  `is_physical` tinyint(1) DEFAULT 1 COMMENT '是否物理端口: 1=是, 0=否(逻辑接口)',
  `is_lldp_enabled` tinyint(1) DEFAULT NULL COMMENT '是否启用LLDP: 1=是, 0=否',
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `raw_data` json DEFAULT NULL COMMENT '原始采集数据，JSON格式',
  `created_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_ifindex` (`device_id`,`if_index`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_last_updated` (`last_updated`),
  KEY `idx_port_name` (`port_name`),
  KEY `idx_mac_address` (`mac_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备端口信息表';
```

该表存储设备端口的详细信息，为拓扑边提供端口层面的数据支持，包括端口名称、状态、速率和MTU等配置信息。表中的`if_index`字段是LLDP OID索引关联的关键字段。

### 4.3.2 标识符映射相关表

标识符映射表用于解决设备和端口标识符的多样性问题，实现不同类型标识符到系统内部ID的映射和转换。

#### ******* 设备标识符映射表 (monitor_device_identifier_mapping)

```sql
CREATE TABLE IF NOT EXISTS `monitor_device_identifier_mapping` (
  `id` varchar(36) NOT NULL COMMENT '记录ID，UUID格式',
  `device_id` varchar(36) NOT NULL COMMENT '系统中的标准设备ID',
  `identifier_type` varchar(50) NOT NULL COMMENT '标识符类型：name(设备名称)、chassis_id(机箱ID/MAC地址)、ip(管理IP)等',
  `identifier_value` varchar(255) NOT NULL COMMENT '标识符值',
  `priority` int NOT NULL DEFAULT 0 COMMENT '标识符优先级，数值越小优先级越高，用于解决冲突',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否活跃：1=活跃，0=不活跃',
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后一次发现时间',
  `created_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_value` (`identifier_type`, `identifier_value`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_last_seen` (`last_seen`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备标识符映射表';
```

该表存储不同类型标识符到系统设备ID的映射关系，支持通过设备名称、MAC地址和IP地址等多种标识符查找设备。

#### ******* 端口标识符映射表 (monitor_port_identifier_mapping)

```sql
CREATE TABLE IF NOT EXISTS `monitor_port_identifier_mapping` (
  `id` varchar(36) NOT NULL COMMENT '记录ID，UUID格式',
  `device_id` varchar(36) NOT NULL COMMENT '所属设备ID',
  `port_id` varchar(36) NOT NULL COMMENT '系统中的标准端口ID',
  `identifier_type` varchar(50) NOT NULL COMMENT '标识符类型：name(端口名称)、index(ifIndex)、desc(描述)等',
  `identifier_value` varchar(255) NOT NULL COMMENT '标识符值',
  `priority` int NOT NULL DEFAULT 0 COMMENT '标识符优先级，数值越小优先级越高',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否活跃：1=活跃，0=不活跃',
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后一次发现时间',
  `created_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_type_value` (`device_id`, `identifier_type`, `identifier_value`),
  KEY `idx_port_id` (`port_id`),
  KEY `idx_last_seen` (`last_seen`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='端口标识符映射表';
```

该表存储不同类型端口标识符到系统端口ID的映射关系，支持通过端口名称、ifIndex和描述等多种标识符查找端口。

### 4.3.3 LLDP相关表

#### 4.3.3.1 LLDP邻居关系缓存表 (monitor_lldp_neighbor_cache)

```sql
CREATE TABLE IF NOT EXISTS `monitor_lldp_neighbor_cache` (
  `id` varchar(36) NOT NULL COMMENT '记录ID，UUID格式',
  `local_device_identifier` varchar(255) NOT NULL COMMENT '本地设备标识符',
  `local_device_id_type` varchar(50) NOT NULL COMMENT '本地设备标识符类型',
  `local_port_index` varchar(50) NOT NULL COMMENT '本地端口索引',
  `remote_chassis_id` varchar(255) NOT NULL COMMENT '远端设备机箱ID',
  `remote_port_id` varchar(255) NOT NULL COMMENT '远端设备端口ID',
  `remote_sysname` varchar(255) DEFAULT NULL COMMENT '远端设备名称',
  `mapped_local_device_id` varchar(36) DEFAULT NULL COMMENT '映射后的本地设备ID',
  `mapped_local_port_id` varchar(36) DEFAULT NULL COMMENT '映射后的本地端口ID',
  `mapped_remote_device_id` varchar(36) DEFAULT NULL COMMENT '映射后的远端设备ID',
  `mapped_remote_port_id` varchar(36) DEFAULT NULL COMMENT '映射后的远端端口ID',
  `mapping_status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '映射状态：PENDING(待处理)、MAPPED(已映射)、PARTIAL(部分映射)、FAILED(映射失败)',
  `raw_data` json DEFAULT NULL COMMENT '原始LLDP数据，JSON格式',
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后一次发现时间',
  `error_message` varchar(512) DEFAULT NULL COMMENT '映射失败时的错误信息',
  `created_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_local_remote` (`local_device_identifier`, `local_port_index`, `remote_chassis_id`, `remote_port_id`),
  KEY `idx_local_device` (`local_device_identifier`),
  KEY `idx_remote_chassis` (`remote_chassis_id`),
  KEY `idx_last_seen` (`last_seen`),
  KEY `idx_mapping_status` (`mapping_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='LLDP邻居关系缓存表';
```

该表临时存储原始LLDP邻居关系信息，避免重复处理，同时保留原始数据便于问题排查。表中包含原始标识符和映射后的标准ID，以及映射状态和错误信息。

### 4.3.4 拓扑相关表

#### ******* 拓扑快照表 (monitor_topology_snapshot)

```sql
CREATE TABLE IF NOT EXISTS `monitor_topology_snapshot` (
  `id` varchar(36) NOT NULL COMMENT '快照ID，UUID格式',
  `name` varchar(255) NOT NULL COMMENT '快照名称',
  `description` varchar(1000) DEFAULT NULL COMMENT '快照描述',
  `node_count` int NOT NULL DEFAULT '0' COMMENT '节点数量',
  `edge_count` int NOT NULL DEFAULT '0' COMMENT '边数量',
  `is_current` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为当前活动快照',
  `created_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_is_current` (`is_current`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='拓扑快照元数据表';
```

该表存储拓扑图的快照信息，支持多个快照版本的管理，便于历史拓扑查询和比对。

#### ******* 拓扑节点表 (monitor_topology_node)

```sql
CREATE TABLE IF NOT EXISTS `monitor_topology_node` (
  `id` varchar(36) NOT NULL COMMENT '节点ID，UUID格式',
  `snapshot_id` varchar(36) NOT NULL COMMENT '关联的快照ID',
  `device_id` varchar(36) NOT NULL COMMENT '设备ID',
  `node_type` varchar(50) NOT NULL COMMENT '节点类型',
  `properties` json DEFAULT NULL COMMENT '节点属性JSON',
  `created_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_snapshot_id` (`snapshot_id`),
  KEY `idx_device_id` (`device_id`),
  CONSTRAINT `fk_node_snapshot` FOREIGN KEY (`snapshot_id`) REFERENCES `monitor_topology_snapshot` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='拓扑节点数据表';
```

该表存储拓扑图中的节点信息，即网络设备。每个节点关联到一个快照和一个设备，节点的详细属性以JSON格式存储。

#### 4.3.4.3 拓扑边表 (monitor_topology_edge)

```sql
CREATE TABLE IF NOT EXISTS `monitor_topology_edge` (
  `id` varchar(36) NOT NULL COMMENT '边ID，UUID格式',
  `snapshot_id` varchar(36) NOT NULL COMMENT '关联的快照ID',
  `source_node_id` varchar(36) NOT NULL COMMENT '源节点ID',
  `target_node_id` varchar(36) NOT NULL COMMENT '目标节点ID',
  `source_port_id` varchar(36) DEFAULT NULL COMMENT '源端口ID',
  `target_port_id` varchar(36) DEFAULT NULL COMMENT '目标端口ID',
  `edge_type` varchar(50) NOT NULL COMMENT '边类型',
  `properties` json DEFAULT NULL COMMENT '边属性JSON',
  `created_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_snapshot_id` (`snapshot_id`),
  KEY `idx_source_node_id` (`source_node_id`),
  KEY `idx_target_node_id` (`target_node_id`),
  CONSTRAINT `fk_edge_snapshot` FOREIGN KEY (`snapshot_id`) REFERENCES `monitor_topology_snapshot` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='拓扑边数据表';
```

该表存储拓扑图中的连接关系，即设备间的链接。每条边关联一个快照、两个节点和两个端口，边的详细属性以JSON格式存储。

### 4.3.5 表关系与数据流转

所有表之间存在逻辑关联关系，形成完整的数据链路：

1. 设备信息和端口信息表存储基础数据
2. 标识符映射表将原始标识符映射到系统标准ID
3. LLDP邻居缓存表存储原始LLDP数据并记录标识符映射结果
4. 拓扑快照、节点和边表存储计算后的拓扑关系

数据流转过程中，每个环节都有明确的处理逻辑和错误处理机制，确保拓扑数据的准确性和完整性。

# 5 实施计划

拓扑数据功能的实施将分为以下几个阶段进行：

## 5.1 阶段一：基础数据管理与处理

1. **设计并创建设备端口信息表**
   * 根据拓扑数据处理需求，设计`monitor_device_port_info`数据库表结构
   * 确保表结构包含设备ID、端口索引、端口名称、端口描述等必要字段
   * 设置适当的索引以提高查询性能
   * 将SQL语句保存至`dci-monitor/sqls/create_topology_tables.sql`文件

2. **端口信息处理模块开发**
   * 实现端口信息数据解析和处理逻辑
   * 开发端口信息数据库存储与更新机制
   * 实现端口信息历史版本比对功能 【待定】

3. **LLDP数据解析模块开发**
   * 实现LLDP邻居信息JSON解析功能
   * 开发OID索引结构解析功能，从OID字符串中提取`localPortIndex`
   * 实现LLDP数据分类和过滤机制
   * 设计LLDP数据缓存策略，提高处理效率

4. **设备和端口ID转换模块实现**
   * 开发设备名称、MAC地址到设备ID的转换逻辑
   * 实现端口索引到端口名称的映射功能
   * 开发远端设备和端口标识符解析逻辑
   * 设计ID转换失败处理机制

5. **拓扑关系构建模块开发**
   * 定义内部拓扑数据结构，表示网络中的节点和边
   * 实现基于LLDP数据的拓扑关系构建算法
   * 开发拓扑冗余链路处理逻辑
   * 实现拓扑变更检测和处理机制

## 5.2 阶段二：数据存储与输出

1. **拓扑数据持久化存储模块实现**
   * 设计并创建拓扑快照相关表：`monitor_topology_snapshot`、`monitor_topology_node`、`monitor_topology_edge`
   * 实现拓扑快照创建和管理功能
   * 开发节点和边数据的数据库存储逻辑
   * 实现拓扑快照元数据管理，如记录时间、节点数、边数等

2. **拓扑数据编码器实现**
   * 实现内存中拓扑关系转换为标准图数据JSON格式的功能
   * 支持`UPSERT_VERTEX`、`UPSERT_EDGE`、`DELETE_EDGE`等操作
   * 开发拓扑数据序列化和压缩功能
   * 实现数据格式版本兼容处理

3. **拓扑数据发送模块实现**
   * 开发向`dci.monitor.v1.defaultchannel.topology.processed`主题发送处理后数据的功能
   * 实现发送错误重试和恢复机制
   * 开发批量数据发送优化
   * 实现数据发送状态监控

## 5.3 阶段三：服务集成与框架

1. **TopologyProcessor服务集成实现**
   * 整合各个处理模块，形成完整的TopologyProcessor服务
   * 实现配置管理和参数化控制
   * 开发服务启动、停止和重启机制
   * 实现服务健康检查和状态报告功能

2. **TopologyProcessor服务注册与命令行集成**
   * 将TopologyProcessor注册为dcimonitor的子服务
   * 实现基于命令行的服务控制接口
   * 开发配置文件处理和环境变量支持
   * 实现日志配置和控制

## 5.4 阶段四：测试与优化

1. **TopologyProcessor单元测试开发**
   * 为各核心模块创建单元测试
   * 使用mock工具模拟外部依赖
   * 编写功能和异常处理测试用例
   * 实现测试数据生成工具

2. **TopologyProcessor集成测试开发**
   * 开发端到端集成测试用例
   * 测试与Kafka、MySQL等外部系统的交互
   * 验证拓扑数据处理的完整流程
   * 模拟各类异常情况的处理逻辑

3. **TopologyProcessor性能测试与优化**
   * 进行负载和容量测试
   * 测量和分析关键处理路径的性能
   * 实现性能优化，如批处理、缓存等
   * 验证在高负载场景下的稳定性

## 5.5 阶段五：监控与文档 【待定】

1. **拓扑处理监控与告警功能实现**
   * 实现关键性能指标收集和暴露
   * 开发处理异常和错误监控
   * 设计并实现告警规则和通知机制
   * 开发监控仪表板

2. **拓扑数据处理方案实施文档编写**
   * 编写详细的实施和部署指南
   * 创建配置参数说明文档
   * 编写故障排除和运维指南
   * 提供示例和最佳实践建议

## 5.6 阶段六：系统更新与未来规划【待定】

1. **更新系统概要设计中的拓扑数据处理部分**
   * 根据实际实现更新架构图和数据流图
   * 更新组件关系图和技术说明
   * 确保文档与实际实现保持一致
   * 更新文档版本记录

2. **开发拓扑可视化展示技术预研**
   * 调研适合展示网络拓扑的可视化工具和库
   * 开发拓扑数据可视化概念验证
   * 评估不同可视化方案的性能和兼容性
   * 提供拓扑可视化实施建议

3. **拓扑属性扩展实现方案设计**
   * 设计拓扑节点和边的属性扩展机制
   * 规划与其他数据源的集成方案
   * 开发拓扑数据查询API接口初步设计
   * 规划拓扑数据分析和应用场景