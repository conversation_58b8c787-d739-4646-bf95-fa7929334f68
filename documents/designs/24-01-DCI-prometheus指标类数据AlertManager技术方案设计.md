---
title: |
  DCI-prometheus指标类数据AlertManager技术方案设计

subtitle: |
  基于Prometheus AlertManager的指标告警机制详细设计
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-06-23 | 顾铠羟 | 初始版本           |

# 1 文档介绍

## 1.1 文档目的

本文档详细描述DCI监测系统中基于Prometheus AlertManager的指标告警机制的技术方案设计，作为《24-DCI-异常告警检测技术方案设计》的补充文档。该方案主要关注如何利用Prometheus生态系统中的AlertManager组件实现对网络设备指标数据的高效告警。

## 1.2 文档范围

本文档涵盖以下内容：
- Prometheus AlertManager的架构与工作原理
- 告警规则配置与管理方案
- AlertManager与dcimonitor系统的集成方式
- 告警通知路由与模板设计
- 告警分组与抑制策略
- 部署与配置最佳实践

## 1.3 文档关联

本文档与以下文档相关联：
- 《24-DCI-异常告警检测技术方案设计.md》：作为其补充文档，提供AlertManager相关的详细设计
- 《07-DCI-Prometheus和Thanos架构设计.md》：提供Prometheus整体架构设计
- 《08-DCI-Prometheus指标和标签规范.md》：提供指标命名和标签规范

# 2 总体设计

## 2.1 设计目标

1. 利用Prometheus AlertManager实现高效、可靠的指标告警机制
2. 设计灵活的告警规则管理系统，支持规则的动态更新
3. 实现与dcimonitor系统的无缝集成
4. 提供告警分组、抑制和静默功能，避免告警风暴
5. 支持多种通知渠道和自定义通知模板

## 2.2 架构设计

### 2.2.1 详细架构图

```mermaid
graph TD
    subgraph "Prometheus生态系统"
        Prometheus["Prometheus Server"]
        AlertManager["AlertManager"]
        RuleFiles["告警规则文件"]
        
        Prometheus --> |"评估规则"|RuleFiles
        Prometheus --> |"发送告警"|AlertManager
    end
    
    subgraph "DCI监测系统"
        AlertService["告警服务<br/>(AlertService)"]
        AlertDAO["告警数据访问层<br/>(AlertDAO)"]
        AlertAPI["告警API"]
        WebhookReceiver["Webhook接收器"]
        
        MySQL[(MySQL数据库)]
        
        WebhookReceiver --> AlertService
        AlertService --> AlertDAO
        AlertDAO --> MySQL
        AlertAPI --> AlertService
        
    end
    
    subgraph "通知目标"
        Email["邮件服务器"]
        WebhookTargets["外部Webhook"]
        KafkaTopics["Kafka主题"]
    end
    
    RuleManager["规则管理器"] --> |"生成"|RuleFiles
    AlertService --> |"管理规则"|RuleManager
    
    AlertManager --> |"发送通知"|Email
    AlertManager --> |"发送通知"|WebhookTargets
    AlertManager --> |"发送通知"|KafkaTopics
    
    AlertManager --> |"回调"|WebhookReceiver
```

### 2.2.2 极简架构图

```mermaid
sequenceDiagram
    participant Prometheus
    participant AlertManager
    participant DCI as DCI监测系统
    participant Notification as 通知目标
    
    Prometheus->>Prometheus: 评估告警规则
    Prometheus->>AlertManager: 发送告警
    AlertManager->>AlertManager: 分组与抑制
    AlertManager->>DCI: Webhook回调
    AlertManager->>Notification: 发送通知
    DCI->>DCI: 存储告警记录
```

## 2.3 数据流/流程图

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant DCI as DCI监测系统
    participant RuleFiles as 规则文件
    participant Prometheus
    participant AlertManager
    participant Notification as 通知目标
    
    Note over Admin,RuleFiles: 规则管理流程
    Admin->>DCI: 创建/编辑告警规则
    DCI->>DCI: 存储规则到MySQL
    DCI->>RuleFiles: 生成规则文件
    
    Note over Prometheus,AlertManager: 告警评估流程
    Prometheus->>RuleFiles: 加载规则
    Prometheus->>Prometheus: 定期评估规则
    Prometheus->>AlertManager: 发送告警
    
    Note over AlertManager,Notification: 告警处理流程
    AlertManager->>AlertManager: 分组与抑制
    AlertManager->>DCI: Webhook回调
    AlertManager->>Notification: 发送通知
    
    Note over DCI: 告警记录管理
    DCI->>DCI: 存储告警记录
    DCI->>DCI: 更新告警状态
```

## 2.4 模块化设计

AlertManager集成方案包含以下主要模块：

1. **规则管理器**：负责将MySQL中存储的规则转换为Prometheus规则文件
2. **Webhook接收器**：接收AlertManager发送的告警回调
3. **告警处理器**：处理接收到的告警，包括规范化、存储和通知
4. **AlertManager配置管理器**：负责生成和更新AlertManager的配置文件

## 2.5 技术选型

1. **AlertManager版本**：Prometheus AlertManager v0.25.0或更高版本
2. **规则文件格式**：YAML格式的Prometheus告警规则文件
3. **配置管理**：基于文件系统或Kubernetes ConfigMap的配置管理
4. **通知渠道**：支持Email、Webhook、Kafka等多种通知方式
5. **集成方式**：通过Webhook回调与dcimonitor系统集成

# 3 详细设计

## 3.1 功能模块

### 3.1.1 规则管理器

规则管理器负责将数据库中存储的告警规则转换为Prometheus可识别的规则文件：

```mermaid
sequenceDiagram
    participant API as 告警API
    participant Service as AlertService
    participant RuleManager as 规则管理器
    participant FileSystem as 文件系统
    participant Prometheus
    
    API->>Service: 创建/更新规则
    Service->>RuleManager: 转换规则
    RuleManager->>FileSystem: 写入规则文件
    Note right of FileSystem: rules/dci_alerts.yml
    
    alt Kubernetes环境
        RuleManager->>RuleManager: 更新ConfigMap
    end
    
    Prometheus->>FileSystem: 加载规则文件
    Note right of Prometheus: 自动重新加载
```

规则文件示例：

```yaml
groups:
- name: dci_device_status
  rules:
  - alert: DeviceCPUUsageHigh
    expr: dci_snmp_status_cpu_usage{device_id=~".+"} > 80
    for: 5m
    labels:
      severity: warning
      category: device
    annotations:
      summary: "设备CPU使用率过高"
      description: "设备 {{ $labels.device_id }} CPU使用率为 {{ $value }}%，已超过80%阈值持续5分钟"
      
  - alert: DeviceMemoryUsageHigh
    expr: dci_snmp_status_memory_usage{device_id=~".+"} > 85
    for: 5m
    labels:
      severity: warning
      category: device
    annotations:
      summary: "设备内存使用率过高"
      description: "设备 {{ $labels.device_id }} 内存使用率为 {{ $value }}%，已超过85%阈值持续5分钟"

- name: dci_interface_status
  rules:
  - alert: InterfaceDown
    expr: dci_snmp_status_interface{device_id=~".+"} == 2
    for: 1m
    labels:
      severity: critical
      category: interface
    annotations:
      summary: "接口状态为Down"
      description: "设备 {{ $labels.device_id }} 的接口 {{ $labels.ifName }} 状态为Down"
```

### 3.1.2 AlertManager配置管理器

负责生成和管理AlertManager的配置文件，包括路由规则、接收器定义和模板设置：

```mermaid
sequenceDiagram
    participant API as 告警API
    participant Service as AlertService
    participant ConfigManager as 配置管理器
    participant FileSystem as 文件系统
    participant AlertManager
    
    API->>Service: 更新通知配置
    Service->>ConfigManager: 生成AlertManager配置
    ConfigManager->>FileSystem: 写入配置文件
    Note right of FileSystem: alertmanager.yml
    
    alt Kubernetes环境
        ConfigManager->>ConfigManager: 更新ConfigMap
    end
    
    ConfigManager->>AlertManager: 重新加载配置
    Note right of AlertManager: HTTP POST /-/reload
```

这个配置示例展示了AlertManager的主要功能：
1. 告警分组 - 按alertname和category分组，减少告警风暴
2. 路由规则 - 根据告警属性将告警发送到不同的接收器
3. 通知方式 - webhook通知
4. 告警抑制 - 当出现高级别告警时抑制相关的低级别告警
5. 自定义模板 - 通过模板文件定制通知内容格式

AlertManager配置示例：

```yaml
# 全局配置，适用于所有告警
global:
  # 当告警条件不再满足后，等待多久才将告警标记为已解决
  resolve_timeout: 5m
  # HTTP客户端配置，适用于所有webhook
  http_config:
    follow_redirects: true  # 是否跟随HTTP重定向
    enable_http2: true      # 是否启用HTTP/2

# 通知模板配置，用于自定义通知内容
templates:
  - '/etc/alertmanager/templates/*.tmpl'  # 模板文件路径

# 路由配置，决定告警如何分发到不同的接收器
route:
  # 默认接收器，当没有匹配其他路由规则时使用
  receiver: 'dci-monitor-webhook'
  # 按哪些标签对告警进行分组
  group_by: ['alertname', 'category']
  # 初次等待时间，收到第一个告警后等待多久再发送通知，用于收集同组告警
  group_wait: 30s
  # 组内等待时间，同一组告警的发送间隔
  group_interval: 5m
  # 重复通知间隔，同一告警的重复发送间隔
  repeat_interval: 4h

# 接收器配置，定义不同的通知目标
receivers:
# DCI监测系统webhook接收器
- name: 'dci-monitor-webhook'
  webhook_configs:
  - url: 'http://dcimonitor-service.dci.svc.cluster.local:8080/api/v1/alerts/webhook/prometheus'
    send_resolved: true  # 发送已解决的告警
    # 自定义HTTP请求头
    headers:
      X-Webhook-Source: 'alertmanager'
      X-Auth-Token: '${WEBHOOK_AUTH_TOKEN}'  # 使用环境变量中的认证令牌

# 抑制规则配置，用于减少告警风暴
inhibit_rules:
# 当同一设备出现critical级别告警时，抑制该设备的warning级别告警
- source_match:  # 源告警匹配条件
    severity: 'critical'
  target_match:  # 目标告警匹配条件(被抑制的告警)
    severity: 'warning'
  equal: ['device_id']  # 要求这些标签值必须相同才抑制

# 当设备离线时，抑制该设备的所有其他告警
- source_match:
    alertname: 'DeviceDown'
  target_match_re:
    alertname: '.*'
  equal: ['device_id']  # 要求设备ID相同 
```

### 3.1.3 Webhook接收器

实现接收AlertManager告警回调的API接口：

```mermaid
sequenceDiagram
    participant AlertManager
    participant WebhookAPI as Webhook API
    participant AlertService
    participant AlertDAO
    participant MySQL
    
    AlertManager->>WebhookAPI: POST /api/v1/alerts/webhook/prometheus
    Note right of AlertManager: 包含告警数据的JSON
    
    WebhookAPI->>WebhookAPI: 验证请求
    WebhookAPI->>AlertService: 处理告警数据
    
    AlertService->>AlertService: 规范化告警格式
    AlertService->>AlertDAO: 存储/更新告警
    AlertDAO->>MySQL: 执行数据库操作
    
    WebhookAPI->>AlertManager: 返回HTTP 200
```

AlertManager回调数据示例：

```json
{
  "version": "4",
  "groupKey": "{}:{alertname=\"DeviceCPUUsageHigh\"}",
  "status": "firing",
  "receiver": "default-receiver",
  "groupLabels": {
    "alertname": "DeviceCPUUsageHigh"
  },
  "commonLabels": {
    "alertname": "DeviceCPUUsageHigh",
    "category": "device",
    "device_id": "switch-01",
    "severity": "warning"
  },
  "commonAnnotations": {
    "description": "设备 switch-01 CPU使用率为 92%，已超过80%阈值持续5分钟",
    "summary": "设备CPU使用率过高"
  },
  "alerts": [
    {
      "status": "firing",
      "labels": {
        "alertname": "DeviceCPUUsageHigh",
        "category": "device",
        "device_id": "switch-01",
        "severity": "warning"
      },
      "annotations": {
        "description": "设备 switch-01 CPU使用率为 92%，已超过80%阈值持续5分钟",
        "summary": "设备CPU使用率过高"
      },
      "startsAt": "2025-07-15T10:00:00Z",
      "endsAt": "0001-01-01T00:00:00Z",
      "generatorURL": "http://prometheus.dci.svc.cluster.local:9090/graph?g0.expr=dci_snmp_status_cpu_usage%7Bdevice_id%3D%22switch-01%22%7D+%3E+80&g0.tab=1"
    }
  ]
}
```

## 3.2 数据模型

### 3.2.1 Prometheus告警规则模型

```go
// PrometheusAlertRule 表示Prometheus告警规则
type PrometheusAlertRule struct {
    // 规则标识符
    ID          string `json:"id"`
    // 告警名称
    AlertName   string `json:"alert_name"`
    // PromQL表达式
    Expression  string `json:"expression"`
    // 持续时间（如"5m"）
    For         string `json:"for"`
    // 标签
    Labels      map[string]string `json:"labels"`
    // 注释
    Annotations map[string]string `json:"annotations"`
    // 所属规则组
    GroupName   string `json:"group_name"`
    // 是否启用
    Enabled     bool   `json:"enabled"`
}

// PrometheusRuleGroup 表示Prometheus规则组
type PrometheusRuleGroup struct {
    // 组名称
    Name  string               `json:"name"`
    // 组内规则
    Rules []PrometheusAlertRule `json:"rules"`
}

// PrometheusRuleFile 表示Prometheus规则文件
type PrometheusRuleFile struct {
    // 规则组列表
    Groups []PrometheusRuleGroup `json:"groups"`
}
```

### 3.2.2 AlertManager配置模型

```go
// AlertManagerConfig 表示AlertManager配置
type AlertManagerConfig struct {
    // 全局配置
    Global       GlobalConfig       `json:"global"`
    // 模板文件路径
    Templates    []string           `json:"templates"`
    // 路由配置
    Route        RouteConfig        `json:"route"`
    // 接收器配置
    Receivers    []ReceiverConfig   `json:"receivers"`
    // 抑制规则
    InhibitRules []InhibitRuleConfig `json:"inhibit_rules"`
}

// RouteConfig 表示路由配置
type RouteConfig struct {
    // 默认接收器
    Receiver        string        `json:"receiver"`
    // 分组依据
    GroupBy         []string      `json:"group_by"`
    // 分组等待时间
    GroupWait       string        `json:"group_wait"`
    // 分组间隔
    GroupInterval   string        `json:"group_interval"`
    // 重复间隔
    RepeatInterval  string        `json:"repeat_interval"`
    // 子路由
    Routes          []RouteConfig `json:"routes,omitempty"`
    // 匹配条件
    Match           map[string]string `json:"match,omitempty"`
    // 正则匹配条件
    MatchRE         map[string]string `json:"match_re,omitempty"`
    // 是否继续匹配其他路由
    Continue        bool          `json:"continue,omitempty"`
}

// ReceiverConfig 表示接收器配置
type ReceiverConfig struct {
    // 接收器名称
    Name           string             `json:"name"`
    // 邮件配置
    EmailConfigs   []EmailConfig      `json:"email_configs,omitempty"`
    // Webhook配置
    WebhookConfigs []WebhookConfig    `json:"webhook_configs,omitempty"`
    // Kafka配置
    KafkaConfigs   []KafkaConfig      `json:"kafka_configs,omitempty"`
}
```

## 3.3 接口设计

### 3.3.1 Prometheus规则管理API

```
# 获取所有Prometheus告警规则
GET /api/v1/alerts/prometheus/rules

# 创建Prometheus告警规则
POST /api/v1/alerts/prometheus/rules

# 获取指定Prometheus告警规则
GET /api/v1/alerts/prometheus/rules/:id

# 更新Prometheus告警规则
PUT /api/v1/alerts/prometheus/rules/:id

# 删除Prometheus告警规则
DELETE /api/v1/alerts/prometheus/rules/:id

# 启用/禁用Prometheus告警规则
POST /api/v1/alerts/prometheus/rules/:id/toggle

# 应用规则变更（重新生成规则文件）
POST /api/v1/alerts/prometheus/rules/apply
```

### 3.3.2 AlertManager配置管理API

```
# 获取AlertManager配置
GET /api/v1/alerts/prometheus/config

# 更新AlertManager配置
PUT /api/v1/alerts/prometheus/config

# 获取所有接收器
GET /api/v1/alerts/prometheus/receivers

# 创建接收器
POST /api/v1/alerts/prometheus/receivers

# 更新接收器
PUT /api/v1/alerts/prometheus/receivers/:name

# 删除接收器
DELETE /api/v1/alerts/prometheus/receivers/:name

# 应用配置变更（重新加载AlertManager）
POST /api/v1/alerts/prometheus/config/reload
```

### 3.3.3 AlertManager Webhook接收API

```
# 接收AlertManager告警回调
POST /api/v1/alerts/webhook/prometheus
```

告警回调处理流程：

```mermaid
sequenceDiagram
    participant AlertManager
    participant API as WebhookAPI
    participant Service as AlertService
    participant DAO as AlertDAO
    
    AlertManager->>API: POST /api/v1/alerts/webhook/prometheus
    API->>Service: 处理告警数据
    
    loop 对每个告警
        Service->>Service: 转换告警格式
        alt 新告警
            Service->>DAO: 创建告警记录
        else 已存在告警
            Service->>DAO: 更新告警状态
        end
    end
    
    API->>AlertManager: 返回处理结果
```

# 4 安全设计

AlertManager集成的安全设计包括：

1. **API访问控制**：
   - AlertManager API访问使用基本认证或OAuth2
   - 规则管理API遵循现有dcimonitor系统的认证和授权机制

2. **Webhook安全**：
   - 使用共享密钥验证Webhook回调请求
   - 支持HTTPS加密传输
   - 实现请求来源IP白名单验证

3. **敏感信息保护**：
   - 邮件服务器密码等敏感信息使用Kubernetes Secret存储
   - 通知模板中避免包含敏感信息

4. **配置文件安全**：
   - 规则文件和配置文件权限控制
   - 使用Kubernetes ConfigMap和Secret管理配置

# 5 本设计的代码实现文件列表

```
dci-monitor/src/dcimonitor/
├── internal/
│   ├── models/
│   │   ├── prom_alert.go       # Prometheus告警模型
│   │   └── alertmanager.go     # AlertManager配置模型
│   ├── services/
│   │   ├── prom_rule_manager.go # Prometheus规则管理器
│   │   ├── alertmanager_config.go # AlertManager配置管理器
│   │   └── webhook_handler.go  # Webhook处理器
│   └── monitors/
│       └── prom_alert_monitor.go # Prometheus告警API控制器
├── cmd/
│   └── alertmanager/
│       └── config.go           # AlertManager配置命令行工具
└── config/
    ├── prometheus/
    │   └── rules/              # 规则文件目录
    └── alertmanager/
        ├── alertmanager.yml    # AlertManager配置文件
        └── templates/          # 通知模板目录
```

## 6 实施计划

1. 设计并实现Prometheus告警规则数据模型和管理接口
2. 开发规则管理器，实现规则文件的动态生成
3. 设计并实现AlertManager配置管理功能
4. 开发Webhook接收器，处理AlertManager回调
5. 创建自定义通知模板
6. 编写部署文档和操作指南
7. 集成测试与性能测试
