---
title: |
  DCI-prometheus-snmp接口状态监测服务技术设计

subtitle: |
  实现基于Prometheus的接口状态查询监测服务
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-06-18 | 顾铠羟 | 初始版本           |

# 1 文档介绍

## 1.1 文档目的

本文档描述DCI监测系统中针对网络设备接口状态的监测服务设计。主要内容包括如何通过Prometheus获取接口状态数据，并通过REST API提供查询功能，使系统能够实时监控网络设备接口状态。

## 1.2 文档范围

本设计覆盖接口状态监测服务的数据流向、接口设计和实现方案，包括从Prometheus查询数据以及提供相关REST API的完整设计。

## 1.3 文档关联

本文档与以下文档相关联：
- 《02-网络自动化平台-数据监测系统技术概要设计》：提供系统整体架构
- 《19-DCI-流量类数据接收及存储技术设计》：提供数据处理基础架构
- 《22-DCI-交换机状态监测技术设计》：提供SNMP状态数据处理设计

# 2 总体设计

## 2.1 设计目标

1. 基于现有dcimonitor框架开发接口状态监测功能
2. 从Prometheus中获取真实接口状态数据替代当前模拟数据
3. 保持现有接口API结构不变，确保兼容性
4. 新增接口管理状态API，丰富接口状态监测

## 2.2 架构设计

### 2.2.1 详细架构图

```mermaid
graph TD
    subgraph "数据采集层"
        Telegraf["Telegraf"]
        SNMP["SNMP设备"]
        Telegraf --> |"采集SNMP数据"|SNMP
    end
    
    subgraph "数据传输层"
        Kafka["Kafka"]
        Telegraf --> |"发送接口状态数据"|Kafka
    end
    
    subgraph "数据处理层"
        SNMPProcessor["snmpstatus服务<br/>处理状态数据"]
        Kafka --> |"消费数据"|SNMPProcessor
        SNMPProcessor --> |"导出指标"|Prometheus["Prometheus"]
    end
    
    subgraph "服务访问层"
        PrometheusDAO["PrometheusDAO<br/>数据访问对象"]
        SwitchService["SwitchService<br/>业务逻辑"]
        SwitchMonitor["SwitchMonitor<br/>接口控制器"]
        
        PrometheusDAO --> |"查询指标"|Prometheus
        SwitchService --> |"调用"|PrometheusDAO
        SwitchMonitor --> |"调用"|SwitchService
    end
    
    API["REST API"] --> |"请求"|SwitchMonitor
    User["用户"] --> |"访问"|API
```

### 2.2.2 极简架构图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as REST API
    participant Service as SwitchService
    participant DAO as PrometheusDAO
    participant Prom as Prometheus
    
    Client->>API: 请求接口状态
    API->>Service: 转发请求
    Service->>DAO: 查询接口状态
    DAO->>Prom: 查询dci_snmp_status_interface
    Prom-->>DAO: 返回查询结果
    DAO-->>Service: 返回数据
    Service-->>API: 组装返回结构
    API-->>Client: 返回接口状态
```

## 2.3 数据流/流程图

```mermaid
sequenceDiagram
    participant Telegraf
    participant Kafka
    participant SNMPProcessor
    participant Prometheus
    participant API
    
    Telegraf->>+Kafka: 发送SNMP接口状态数据
    Kafka->>+SNMPProcessor: 消费接口状态数据
    SNMPProcessor->>SNMPProcessor: 解析接口状态
    SNMPProcessor->>+Prometheus: 存储接口状态指标
    
    API->>+Prometheus: 查询接口状态指标
    Prometheus-->>-API: 返回最新指标数据
    API->>API: 转换为API响应格式
    API-->>Client: 返回接口状态
```

## 2.4 模块化设计

- **SwitchMonitor控制器**：处理REST API请求，调用服务层
- **SwitchService服务**：负责业务逻辑，包括数据转换与处理
- **PrometheusDAO**：数据访问对象，负责与Prometheus交互
- **接口模型**：定义API请求与响应结构

## 2.5 技术选型

- **Prometheus Client**：使用官方Prometheus Go客户端查询指标
- **Gin框架**：提供REST API服务
- **JSON序列化**：使用jsoniter进行高性能JSON处理

# 3 详细设计

## 3.1 功能模块

### 3.1.1 Prometheus查询模块

该模块负责从Prometheus查询接口状态数据，主要使用以下指标：

- `dci_snmp_status_interface`：接口操作状态指标(1=up, 2=down)
- `dci_snmp_status_interface_admin`：接口管理状态指标(1=up, 2=down, 3=testing)

```mermaid
sequenceDiagram
    participant SwitchService
    participant PrometheusDAO
    participant Prometheus
    
    SwitchService->>+PrometheusDAO: 查询接口操作状态
    PrometheusDAO->>+Prometheus: Query("dci_snmp_status_interface{device_ip=\"x.x.x.x\", ifName=\"GE1/0/1\"}")
    Prometheus-->>-PrometheusDAO: 返回最新状态值
    PrometheusDAO-->>-SwitchService: 解析返回值
    
    SwitchService->>+PrometheusDAO: 查询接口管理状态
    PrometheusDAO->>+Prometheus: Query("dci_snmp_status_interface_admin{device_ip=\"x.x.x.x\", ifName=\"GE1/0/1\"}")
    Prometheus-->>-PrometheusDAO: 返回最新状态值
    PrometheusDAO-->>-SwitchService: 解析返回值
```

### 3.1.2 接口状态服务模块

改造SwitchService，使其从Prometheus获取真实数据：

```mermaid
sequenceDiagram
    participant SwitchMonitor
    participant SwitchService
    participant PrometheusDAO
    
    SwitchMonitor->>+SwitchService: GetPortStatus(switchID, portID)
    
    SwitchService->>SwitchService: 查询设备IP和接口名
    SwitchService->>+PrometheusDAO: 查询接口操作状态
    PrometheusDAO-->>-SwitchService: 返回状态值
    
    SwitchService->>+PrometheusDAO: 查询接口管理状态
    PrometheusDAO-->>-SwitchService: 返回状态值
    
    SwitchService->>SwitchService: 构建Port响应对象
    SwitchService-->>-SwitchMonitor: 返回Port对象
```

### 3.1.3 新增接口管理状态API

设计新的API端点，用于获取接口管理状态：

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant SwitchService
    participant PrometheusDAO
    
    Client->>+API: GET /api/v1/switches/{switchID}/ports/{portID}/admin
    API->>+SwitchService: GetPortAdminStatus(switchID, portID)
    SwitchService->>+PrometheusDAO: 查询管理状态
    PrometheusDAO-->>-SwitchService: 返回状态值
    SwitchService-->>-API: 返回管理状态
    API-->>-Client: 响应JSON数据
```

## 3.2 数据模型

### 接口操作状态

使用现有的Port结构表示接口操作状态：

```go
// Port 表示交换机端口的状态
type Port struct {
    PortID        string   `json:"portId"`        
    Name          string   `json:"name"`          
    PhysicalState string   `json:"physicalState"` // 物理状态: "up", "down", "admin_down"
    ProtocolState string   `json:"protocolState"` // 协议状态: "up", "down"
    InErrors      int64    `json:"inErrors"`
    OutErrors     int64    `json:"outErrors"`
    Description   string   `json:"description"`
    // 端口流量信息
    PortTraffic
}
```

### 接口管理状态

新增接口管理状态响应结构：

```go
// PortAdminStatus 接口管理状态
type PortAdminStatus struct {
    SwitchID      string    `json:"switchId"`
    PortID        string    `json:"portId"`        
    Name          string    `json:"name"`          
    AdminState    string    `json:"adminState"`    // 管理状态: "up", "down", "testing"
    AdminStateRaw int       `json:"adminStateRaw"` // 原始状态值: 1=up, 2=down, 3=testing
    Timestamp     time.Time `json:"timestamp"`     // 数据时间戳
}
```

## 3.3 接口设计

### 3.3.1 现有接口状态API

保持现有API不变，但改用真实数据：

```
GET /api/v1/switches/{switchID}/ports/{portID}?data=status
```

### 3.3.2 新增接口管理状态API

新增接口管理状态查询API：

```
GET /api/v1/switches/{switchID}/ports/{portID}/admin
```

**响应结构**：
```json
{
  "switchId": "210",
  "portId": "GE1/0/1",
  "name": "GigabitEthernet1/0/1",
  "adminState": "up",
  "adminStateRaw": 1,
  "timestamp": "2025-06-18T16:30:00Z"
}
```

# 4 安全设计

接口状态API遵循现有DCI监测系统安全机制，通过K8S集群内部通信保证安全性，对外提供的API通过服务器身份验证和网络隔离保障。

# 5 本设计的代码实现文件列表

```
dci-monitor/src/dcimonitor/
├── internal/
│   ├── models/
│   │   ├── switch.go           # 现有接口状态模型
│   │   └── port_admin.go       # 新增接口管理状态模型
│   ├── services/
│   │   ├── dao.go              # 数据访问层
│   │   └── switch_service.go   # 服务实现层
│   └── monitors/
│       └── switch_monitor.go   # API控制器
```

## 6 实施计划

1. 修改PrometheusDAO，增加接口状态查询功能
2. 改造SwitchService，从Prometheus获取真实数据
3. 升级Port结构，完善管理状态支持
4. 编写单元测试，验证功能正确性
5. 集成测试，确保与现有系统兼容
