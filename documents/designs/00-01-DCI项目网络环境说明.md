# DCI项目网络环境说明

## 环境概述

DCI项目部署在中信阿里云的Kubernetes环境中，具有如下特点：

- 所有服务以Kubernetes Pod形式运行
- 测试环境通过堡垒机访问，主要节点IP为************和************，仅用于调试，测试、开发及生产环境通过SLB访问
- 多个子系统（数据监测系统、多云管理系统、网络自动化控制系统等）在同一集群内运行
- 内部服务通过Kubernetes服务发现机制互相访问
- 外部组件（Agent、网络设备等）通过SLB、Ingress访问集群内服务

## 访问限制

1. **内部访问**：
   - Pod间可直接通过Service名称或ClusterIP进行通信
   - 同一命名空间内的服务可直接访问

2. **外部访问**：
   - 所有外部访问必须通过SLB、nginx-ingress-lb或Ingress配置
   - 关键服务域名：
     - Kafka: dcikafka.intra.citic-x.com:30002
     - 其他服务: [域名列表]

3. **操作限制**：
   - 所有Kubernetes操作命令（kubectl等）必须在堡垒机上执行
   - AI助手仅提供操作建议，不直接执行Kubernetes命令

## 网络拓扑

```
外部网络 -> SLB -> nginx-ingress-lb -> Kubernetes Service -> Pod
                     |
                     v
               tcp-services配置
                     |
                     v
                  NodePort服务
```

## 特殊注意事项

1. **Kafka配置**：
   - 必须使用可从外部访问的域名作为advertised.listeners
   - 避免使用Pod内部IP作为广播地址

2. **安全限制**：
   - 所有外部暴露服务必须考虑安全风险
   - 尽量使用命名空间隔离不同系统 