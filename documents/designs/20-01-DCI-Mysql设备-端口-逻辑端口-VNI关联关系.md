---
title: |
  DCI-Mysql设备-端口-逻辑端口-VNI关联关系

subtitle: |
  技术设计方案 (基于代码实现修订)
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-06-16 | 顾铠羟 | 初始版本           |
| V2.0 | 2025-06-20 | 顾铠羟 | 根据代码实现反向修订，补充缓存机制和分步查询逻辑 |

# 1 文档介绍

## 1.1 文档目的

本文档旨在详细阐述在 DCI 监控系统的 MySQL 数据库中，如何以设备管理IP地址（`device_management_ip`）和接口名称（`portName`）为起点，高效地索引并关联查询到该设备下的所有物理端口、逻辑端口以及与业务相关的 VNI 信息。**本文档基于 `dcimonitor/flowdata` 服务的实际代码逻辑编写，反映了包含缓存优化在内的生产实践**。

## 1.2 文档范围

本文档涵盖的内容包括：
-   相关数据库表的实体关系图
-   从设备 IP 和端口名称出发的详细索引步骤
-   实现中采用的缓存机制说明
-   数据关联的流程图
-   用于实践查询的分步 SQL 示例

## 1.3 文档关联

-   《20-DCI-MySql数据库同步-设备端口VNI基础信息.md》
-   《19-DCI-流量类数据接收及存储技术设计.md》
-   《19-02-流量类数据prometheus指标字段说明.md》

# 2 数据库实体关系总览

为理解索引逻辑，首先需要明晰各核心表之间的关联关系。`dci` 数据库（源）和 `dci_monitor` 数据库（目标）中均包含以下核心表，它们通过各类 ID 相互链接。

```mermaid
erDiagram
    dci_device {
        int id PK "设备ID"
        varchar device_management_ip "设备管理IP"
        varchar device_name "设备名称"
    }
    dci_node {
        int id PK "节点ID"
        int device_id FK "设备ID"
    }
    dci_logic_port_device {
        int id PK "物理端口ID (port_id)"
        int device_id FK "设备ID"
        varchar port "端口名称"
        varchar if_index "接口SNMP索引"
    }
    dci_node_business {
        int id PK
        int node_id FK "节点ID"
        int logic_port_id FK "逻辑端口ID"
        varchar vni FK "VNI"
    }
    dci_logic_port {
        int id PK "逻辑端口ID"
        int node_id FK "节点ID"
        varchar name "逻辑端口名称"
    }
    dci_vni {
        int id PK
        varchar vni_id PK "VNI"
    }

    dci_device ||--o{ dci_node : "包含"
    dci_device ||--o{ dci_logic_port_device : "拥有"
    dci_node ||--o{ dci_node_business : "关联"
    dci_node ||--o{ dci_logic_port : "拥有"
    dci_node_business }|--o{ dci_vni : "使用"
    dci_node_business }|--o{ dci_logic_port : "绑定"
```

# 3 索引路径详解 (优化后)

在 `flowdata` 等高吞吐量服务中，为避免重复执行耗时的多表 `JOIN` 查询，实际代码实现采用的是 **分步查询和内存缓存** 的优化策略。最终的端口映射信息（`PortMapping` 对象）会被整体缓存。

## 3.1 索引步骤说明

以下是从设备IP和端口名出发，获取完整映射信息的逻辑步骤：

1.  **[缓存检查]**：
    -   使用 `device_management_ip` 和 `portName`（物理端口名，如 "10GE1/0/1"）作为组合键，查询内存缓存。
    -   如果命中且未过期，直接返回缓存的完整 `PortMapping` 对象，流程结束。
    -   如果未命中，执行以下数据库查询。

2.  **[第1步] 从IP获取设备ID**:
    -   **输入**: `device_management_ip`
    -   **查询**: 在 `dci_device` 表中查找匹配的记录。
    -   **输出**: `device_id`, `device_name`。

3.  **[第2步] 获取物理端口ID**:
    -   **输入**: `device_id` (来自第1步), `portName` (来自 SNMP `ifName`)
    -   **查询**: 在 `dci_logic_port_device` 表中查找 `device_id` 和 `port` 都匹配的记录。
    -   **输出**: `port_id` (即 `dci_logic_port_device` 表的 `id`), `if_index`。

4.  **[第3步] 获取VNI和逻辑端口ID**:
    -   **输入**: `device_id` (来自第1步), `port_id` (来自第2步)
    -   **查询**: 这是一个核心关联步骤。实际代码中通过 `device_id` 和 `port_id` 在 `dci_node`, `dci_node_business` 和 `dci_logic_port` 之间进行关联查询。
    -   **输出**: `vni_id`, `logic_port_id`。

5.  **[第4步] 整合与缓存**:
    -   将以上步骤获取的所有ID和名称（`device_id`, `port_id`, `logic_port_id`, `vni_id` 等）组装成一个 `PortMapping` 对象。
    -   将此对象存入内存缓存，并设置一个 TTL（生存时间），供后续请求使用。

## 3.2 缓存机制说明

-   **目的**: 避免对每一条流入的遥测消息都执行数据库查询，极大提升处理性能并降低数据库负载。
-   **键 (Key)**: 使用 `device_ip:portName` 的组合字符串作为缓存键。
-   **值 (Value)**: 缓存完整的 `PortMapping` 对象，包含一次查询所需的所有ID和名称。
-   **TTL (Time-To-Live)**: 缓存项具有可配置的生存时间（例如5分钟），以确保映射信息的最终一致性，能够定期从数据库更新。

## 3.3 索引流程图 (优化后)

```mermaid
graph TD
    A["输入: 设备IP, 端口名"] --> B{缓存查询};
    B -- "命中" --> C["直接返回<br/>缓存的PortMapping"];
    B -- "未命中" --> D["执行数据库查询"];

    subgraph "数据库分步查询"
        direction LR
        D --> E{1. 查询 dci_device};
        E -- "device_ip" --> F["产出: device_id"];
        F --> G{2. 查询 dci_logic_port_device};
        G -- "device_id, portName" --> H["产出: port_id"];
        F & H --> I{3. 查询关联表};
        I -- "device_id, port_id" --> J["产出: vni_id, logic_port_id"];
    end

    J --> K["组装 PortMapping 对象"];
    K --> L{更新缓存};
    L --> M["返回新的<br/>PortMapping 对象"];
    
    C --> Z([结束]);
    M --> Z;

    classDef start-end fill:#9f9,stroke:#333,stroke-width:2px;
    classDef output fill:#lightblue,stroke:#333,stroke-width:2px;
    class A,Z start-end;
    class F,H,J,C,M output;
```

# 4 SQL查询示例 (分步)

以下是与上述优化流程对应的分步 SQL 查询语句。

### 4.1 第1步: 获取设备ID和名称

```sql
-- 输入: ? (设备管理IP, 例如 '************')
SELECT
    id AS device_id,
    device_name
FROM
    dci_device
WHERE
    device_management_ip = ?;
```

### 4.2 第2步: 获取物理端口ID和ifIndex

```sql
-- 输入: ? (device_id), ?? (端口名, 例如 '10GE1/0/1')
SELECT
    id AS port_id,
    if_index
FROM
    dci_logic_port_device
WHERE
    device_id = ? AND port = ?;
```

### 4.3 第3步: 获取VNI和逻辑端口ID

这步是最复杂的关联，实际实现可能依然是一个JOIN，但范围更小，且结果会被整体缓存。

```sql
-- 输入: ? (device_id)
SELECT
    biz.vni AS vni_id,
    biz.logic_port_id,
    lp.name as logic_port_name
FROM
    dci_node n
JOIN
    dci_node_business biz ON n.id = biz.node_id
JOIN
    dci_logic_port lp ON biz.logic_port_id = lp.id
WHERE
    n.device_id = ?;
```
注：第3步的查询返回该设备节点下的所有 VNI 与逻辑端口。业务代码逻辑负责使用第2步的物理端口信息进行匹配，以确定唯一的 VNI 关联。
