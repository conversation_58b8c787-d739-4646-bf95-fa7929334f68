---
title: |
  Telegraf SNMP采集实现方案

subtitle: |
  DCI数据监测系统在Prometheus+Thanos架构下的SNMP数据采集实现
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-14 | 顾铠羟 | 初始版本           |
| V1.1 | 2025-05-17 | 顾铠羟 | 增加与Kafka集成模式说明，更新架构图 |
| V1.2 | 2025-05-18 | 顾铠羟 | 调整推荐方案为Kafka中转模式 |
| V1.3 | 2025-05-19 | 顾铠羟 | 优化文档结构，明确与14号文档的关系 |
| V1.4 | 2025-05-26 | 顾铠羟 | 调整部署环境描述，确保与15号文档一致，明确客户端基于操作系统直接安装 |

# 1 方案概述

## 1.1 文档定位

本文档是《14-DCI-dciagent中Telegraf数据采集方案设计》中 SNMP 采集部分的具体技术实现方案。《14-DCI-dciagent中Telegraf数据采集方案设计》提供了整体的 Telegraf 架构设计，而本文档则聚焦于 SNMP 采集的技术细节，包括 MIB 管理、设备特定配置、运维管理和实施计划等内容。

阅读本文档前，建议先了解《14-DCI-dciagent中Telegraf数据采集方案设计》中的总体架构和设计原则，以保证实现的一致性。

**特别说明**：关于部署架构和运维流程的详细设计，请参考《15-DCI-dciagent中Telegraf部署架构与流程》文档，本文档主要聚焦于 SNMP 技术实现方案。

## 1.2 背景与目标

DCI 数据监测系统已确定采用 Prometheus+Thanos 架构替代原有的 TDengine 时序数据库方案，并选择 Telegraf 作为 SNMP 数据采集工具。本方案旨在详细规划 Telegraf 进行 SNMP 数据采集的实现细节，包括部署架构、配置方法、数据流转方式和与 Prometheus 的集成方式，以确保有效监控网络设备。

本设计文档的目标是：

1. 规划 Telegraf SNMP 采集的详细实现架构
2. 定义标准化的配置模板和参数
3. 制定 MIB 管理和维护策略
4. 优化采集性能和可靠性
5. 设计与 Prometheus 生态的高效集成方式

## 1.3 适用范围

本方案适用于 DCI 监测系统的 SNMP 数据采集模块，涵盖：

1. 网络设备 SNMP 数据采集
2. 多厂商设备 MIB 支持
3. Telegraf 配置管理
4. 采集数据与 Prometheus+Thanos 的集成

## 1.5 SNMP采集指标

以下是基于测试脚本中实际验证过的SNMP采集指标：

| 指标类别 | 指标名称 | OID | 描述 |
|---------|---------|-----|------|
| 系统基本信息 | sysName | .*******.*******.0 | 设备名称 |
| 系统基本信息 | sysDescr | .*******.*******.0 | 系统描述 |
| 系统基本信息 | sysUpTime | .*******.*******.0 | 系统运行时间 |
| 系统基本信息 | sysLocation | .*******.*******.0 | 设备位置 |
| 接口信息 | ifIndex | .*******.*******.1.1 | 接口索引 |
| 接口信息 | ifDescr | .*******.*******.1.2 | 接口描述 |
| 接口信息 | ifType | .*******.*******.1.3 | 接口类型 |
| 接口信息 | ifAdminStatus | .*******.*******.1.7 | 接口管理状态 |
| 接口信息 | ifOperStatus | .*******.*******.1.8 | 接口运行状态 |
| 接口扩展信息 | ifName | .*******.2.1.31.1.1.1.1 | 接口名称 |
| 接口扩展信息 | ifHCInOctets | .*******.2.1.31.1.1.1.6 | 接口入向流量(字节) |
| 接口扩展信息 | ifHCOutOctets | .*******.2.1.31.1.1.1.10 | 接口出向流量(字节) |
| 接口扩展信息 | ifHCInUcastPkts | .*******.2.1.31.1.1.1.7 | 接口入向单播包数 |
| 接口扩展信息 | ifHCOutUcastPkts | .*******.2.1.31.1.1.1.11 | 接口出向单播包数 |
| 华为设备性能 | hwEntityCpuUsage | .*******.4.1.2011.5.25.31.1.1.1.1.5.16842753 | CPU使用率(%) |
| 华为设备性能 | hwEntityMemUsage | .*******.4.1.2011.5.25.31.1.1.1.1.7.16842753 | 内存使用率(%) |
| 华为设备性能 | hwEntityTemperature | .*******.4.1.2011.5.25.31.1.1.1.1.11.16842753 | 设备温度 |
| 实体信息 | entPhysicalClass | .*******.2.1.47.1.1.1.1.5.16842753 | 实体类别 |
| 实体信息 | entPhysicalName | .*******.2.1.47.1.1.1.1.7.16842753 | 实体名称 |

## 1.6 SNMP采集LLDP

LLDP(链路层发现协议)是一种用于网络设备发现邻居设备的协议，对于自动化拓扑发现至关重要。以下是基于测试脚本验证过的LLDP相关OID：

| 指标类别 | 指标名称 | OID | 描述 | 索引格式示例 |
|---------|---------|-----|------|------------|
| LLDP远端设备 | lldpRemSysName | .1.0.8802.1.1.2.1.4.1.1.9 | 远端设备名称 | .1.0.8802.1.1.2.1.4.1.1.9.123851676.8.1 |
| LLDP远端设备 | lldpRemPortId | .1.0.8802.1.1.2.1.4.1.1.7 | 远端端口ID | .1.0.8802.1.1.2.1.4.1.1.7.123851676.8.1 |
| LLDP远端设备 | lldpRemSysDesc | .1.0.8802.1.1.2.1.4.1.1.10 | 远端系统描述 | .1.0.8802.1.1.2.1.4.1.1.10.123851676.8.1 |
| LLDP远端设备 | lldpRemPortDesc | .1.0.8802.1.1.2.1.4.1.1.8 | 远端端口描述 | .1.0.8802.1.1.2.1.4.1.1.8.123851676.8.1 |
| LLDP本地配置 | lldpNotificationInterval | .1.0.8802.1.1.******* | LLDP通知间隔 | .1.0.8802.1.1.*******.0 |

**特别说明：**
1. LLDP索引格式在不同厂商设备上可能有所不同。例如，在华为设备上，索引格式为"123851676.8.1"，其中123851676是本地接口索引，8是远程设备索引，1是远程接口索引。
2. 采集LLDP数据时，建议先使用snmpwalk命令探测设备上实际的索引格式，再进行配置。
3. 在Telegraf配置中，可以使用table模式采集完整的LLDP表，也可以使用field模式采集特定索引的LLDP信息。

# 2 架构设计

## 2.1 总体架构

在 Prometheus+Thanos 架构中，通过 Kafka 中转的方式集成 Telegraf 采集的数据。这种方式符合 DCI 监测系统"Agent 端各类采集数据均通过 Kafka Topic 发送"的总体设计原则，保持架构一致性。以下是架构图：

```mermaid
graph TD
    subgraph "设备层"
        D1[网络设备1]
        D2[网络设备2]
        D3[网络设备3]
        Dn[网络设备n...]
    end
    
    subgraph "采集层（操作系统直接安装）"
        subgraph "Telegraf集群1"
            T1[Telegraf实例1]
            T2[Telegraf实例2]
        end
        subgraph "Telegraf集群2"
            T3[Telegraf实例3]
            T4[Telegraf实例4]
        end
    end
    
    subgraph "消息队列"
        K[Kafka集群]
    end
    
    subgraph "数据转换层"
        C1[Kafka-Prometheus Connector/Exporter1]
        C2[Kafka-Prometheus Connector/Exporter2]
    end
    
    subgraph "存储层（K8S部署）"
        subgraph "Prometheus"
            P1[Prometheus实例1]
            P2[Prometheus实例2]
        end
        ThanosS[Thanos Sidecar]
        ThanosQ[Thanos Query]
        ThanosC[Thanos Compact]
        ObjStore[(对象存储)]
    end
    
    subgraph "展示层"
        Grafana[Grafana]
        API[查询API适配层]
    end
    
    D1 & D2 & D3 & Dn <--SNMP--> T1 & T2
    D1 & D2 & D3 & Dn <--SNMP--> T3 & T4
    
    %% Kafka中转路径
    T1 & T2 --数据写入--> K
    T3 & T4 --数据写入--> K
    
    K --数据消费--> C1
    K --数据消费--> C2
    K -.-> 其他系统
    
    C1 --暴露指标--> P1
    C2 --暴露指标--> P2
    
    P1 & P2 --> ThanosS
    ThanosS --> ThanosQ
    ThanosS --> ObjStore
    ThanosC --> ObjStore
    ThanosQ --> Grafana
    ThanosQ --> API
    
    %% 样式定义
    classDef primary fill:#d9f,stroke:#333,stroke-width:1px
    classDef secondary fill:#9cf,stroke:#333,stroke-width:1px
    classDef connect fill:#afc,stroke:#333,stroke-width:1px
    
    class K secondary
    class C1,C2 connect
    class P1,P2,ThanosS,ThanosQ,ThanosC primary
```

## 2.2 部署模式

为确保高可用性和可扩展性，采用分区部署模式：

1. **地域分区**：
   - 按照数据中心/地区部署独立 Telegraf 集群
   - 本地采集，减少网络延迟和故障影响范围

2. **设备分组**：
   - 根据设备类型（接入、汇聚、核心）进行分组
   - 按厂商和设备型号分组，适应不同 MIB 需求

3. **负载均衡**：
   - 单个 Telegraf 实例负责不超过 200 台设备监控
   - 集群内部署至少 2 个实例保证冗余

4. **部署方式**：
   - **客户端组件**：基于操作系统直接安装部署，不依赖 K8S/Docker
   - **服务端组件**：使用 Kubernetes 部署

5. **安装管理**：
   - 使用 dciagent 统一管理 Telegraf 的安装、配置和运行
   - 详细部署流程参考《15-DCI-dciagent中Telegraf部署架构与流程》

## 2.3 高可用设计

为确保SNMP采集的高可用性，采用以下策略：

1. **多实例冗余**：
   - 至少2个Telegraf实例互为备份
   - 确保部署在不同物理节点

2. **故障自愈**：
   - 利用系统服务（如systemd）实现自动重启
   - 通过dciagent监控Telegraf进程状态

3. **数据备份**：
   - 关键配置集中管理
   - MIB文件集中管理并版本控制

4. **监控Telegraf自身**：
   - 收集Telegraf内部运行指标
   - 设置告警阈值监控采集状态

# 3 SNMP采集配置

## 3.1 Telegraf配置模板

### 3.1.1 全局配置

```toml
# Global Agent Configuration
[agent]
  interval = "60s"
  round_interval = true
  metric_batch_size = 1000
  metric_buffer_limit = 10000
  collection_jitter = "5s"
  flush_interval = "30s"
  flush_jitter = "5s"
  precision = "s"
  hostname = ""  # 使用Kubernetes Pod名称
  omit_hostname = false
```

### 3.1.2 SNMP输入插件配置

以下是根据dci_self_test/scripts/snmp/snmp_test.sh脚本中实际测试验证过的SNMP输入配置模板：

```toml
[[inputs.snmp]]
  # 设备列表 - 将根据设备分组动态生成
  agents = [
    "udp://************:161",  # 测试华为交换机1
    "udp://************:161"   # 测试华为交换机2
  ]
  
  # 基本配置
  timeout = "15s"
  retries = 3
  version = 2
  community = "${SNMP_COMMUNITY}"  # 通过环境变量或Secret注入
  agent_host_tag = "agent_host"  # 添加agent_host标签，确保知道数据来自哪个设备
  
  # 高级配置
  max_repetitions = 10  # 用于SNMP GetBulk操作
  name = "snmp"  # 指标前缀
  
  # 系统基本信息 - 使用OID模式
  [[inputs.snmp.field]]
    name = "sysName"         # 设备名称
    oid = ".*******.*******.0"  # SNMPv2-MIB::sysName.0
    is_tag = true
    
  [[inputs.snmp.field]]
    name = "sysDescr"        # 系统描述
    oid = ".*******.*******.0"  # SNMPv2-MIB::sysDescr.0
    
  [[inputs.snmp.field]]
    name = "sysUpTime"       # 系统运行时间
    oid = ".*******.*******.0"  # SNMPv2-MIB::sysUpTime.0
    
  [[inputs.snmp.field]]
    name = "sysLocation"     # 设备位置
    oid = ".*******.*******.0"  # SNMPv2-MIB::sysLocation.0
    
  # 接口表信息 - 使用OID模式
  [[inputs.snmp.table]]
    name = "interface"
    inherit_tags = ["sysName"]
    oid = ".*******.*******"  # IF-MIB::ifTable
    
    # ifTable 字段
    [[inputs.snmp.table.field]]
      name = "ifIndex"
      oid = ".*******.*******.1.1"  # IF-MIB::ifIndex
      is_tag = true
      
    [[inputs.snmp.table.field]]
      name = "ifDescr"
      oid = ".*******.*******.1.2"  # IF-MIB::ifDescr
      
    [[inputs.snmp.table.field]]
      name = "ifType"
      oid = ".*******.*******.1.3"  # IF-MIB::ifType
      
    [[inputs.snmp.table.field]]
      name = "ifAdminStatus"
      oid = ".*******.*******.1.7"  # IF-MIB::ifAdminStatus
      
    [[inputs.snmp.table.field]]
      name = "ifOperStatus"
      oid = ".*******.*******.1.8"  # IF-MIB::ifOperStatus
    
  # 接口扩展表信息 - 使用OID模式
  [[inputs.snmp.table]]
    name = "interfaceX"
    inherit_tags = ["sysName"]
    oid = ".*******.2.1.31.1.1"  # IF-MIB::ifXTable
    
    # ifXTable 字段
    [[inputs.snmp.table.field]]
      name = "ifName"
      oid = ".*******.2.1.31.1.1.1.1"  # IF-MIB::ifName
      is_tag = true
    
    [[inputs.snmp.table.field]]
      name = "ifHCInOctets"
      oid = ".*******.2.1.31.1.1.1.6"  # IF-MIB::ifHCInOctets
    
    [[inputs.snmp.table.field]]
      name = "ifHCOutOctets"
      oid = ".*******.2.1.31.1.1.1.10"  # IF-MIB::ifHCOutOctets
      
    [[inputs.snmp.table.field]]
      name = "ifHCInUcastPkts"
      oid = ".*******.2.1.31.1.1.1.7"  # IF-MIB::ifHCInUcastPkts
      
    [[inputs.snmp.table.field]]
      name = "ifHCOutUcastPkts"
      oid = ".*******.2.1.31.1.1.1.11"  # IF-MIB::ifHCOutUcastPkts
      
  # LLDP远端设备信息 - 使用field模式，根据实际查询结果配置正确的索引
  [[inputs.snmp.field]]
    name = "lldpRemSysName"
    oid = ".1.0.8802.1.1.2.1.4.1.1.9.123851676.8.1"  # 华为设备上实际的索引格式
    
  [[inputs.snmp.field]]
    name = "lldpRemPortId"
    oid = ".1.0.8802.1.1.2.1.4.1.1.7.123851676.8.1"  # 远端端口ID
      
  [[inputs.snmp.field]]
    name = "lldpRemSysDesc"
    oid = ".1.0.8802.1.1.2.1.4.1.1.10.123851676.8.1"  # 远端系统描述
      
  [[inputs.snmp.field]]
    name = "lldpRemPortDesc"
    oid = ".1.0.8802.1.1.2.1.4.1.1.8.123851676.8.1"  # 远端端口描述

  # 华为特有实体MIB - CPU和内存使用率
  [[inputs.snmp.field]]
    name = "hwEntityCpuUsage_doc"
    oid = ".*******.4.1.2011.5.25.31.1.1.1.1.5.16842753"  # 文档中的示例索引，已验证有效
      
  [[inputs.snmp.field]]
    name = "hwEntityMemUsage_doc" 
    oid = ".*******.4.1.2011.5.25.31.1.1.1.1.7.16842753"  # 文档中的示例索引，已验证有效

  [[inputs.snmp.field]]
    name = "hwEntityTemperature_doc"
    oid = ".*******.4.1.2011.5.25.31.1.1.1.1.11.16842753"  # 文档中的示例索引，已验证有效

  # 实体物理信息
  [[inputs.snmp.field]]
    name = "entPhysicalClass_16842753"
    oid = ".*******.2.1.47.1.1.1.1.5.16842753"  # 文档中提到的索引
  
  [[inputs.snmp.field]]
    name = "entPhysicalName_16842753"
    oid = ".*******.2.1.47.1.1.1.1.7.16842753"  # 获取设备名称
```

### 3.1.3 Kafka输出插件配置

Telegraf通过Kafka输出插件将所有采集数据发送到Kafka，符合DCI监测系统的设计原则：

```toml
[[outputs.kafka]]
  # Kafka Broker连接信息
  brokers = ["kafka1:9092", "kafka2:9092", "kafka3:9092"]
  
  # 主题配置
  topic = "dci.monitor.v1.defaultchannel.metrics.telegraf"
  
  # 消息格式与压缩
  data_format = "json"
  compression_codec = "snappy"
  
  # 分区与批处理
  max_message_bytes = 1000000
  
  # 安全配置
  # tls_ca = "/etc/telegraf/kafka/ca.pem"
  # tls_cert = "/etc/telegraf/kafka/cert.pem"
  # tls_key = "/etc/telegraf/kafka/key.pem"
  # sasl_username = "${KAFKA_USERNAME}"
  # sasl_password = "${KAFKA_PASSWORD}"
```

# 4 与Prometheus集成

## 4.1 指标命名与标签规范

为确保指标符合Prometheus命名规范并便于查询，遵循以下规则：

### 4.1.1 指标命名规范

Telegraf收集的SNMP指标将映射为以下格式的Prometheus指标：

```
dci_snmp_{设备类型}_{指标名称}_{单位}
```

例如：
- `dci_snmp_interface_in_octets_bytes_total` (对应 ifHCInOctets)
- `dci_snmp_interface_out_octets_bytes_total` (对应 ifHCOutOctets)
- `dci_snmp_interface_in_packets_total` (对应 ifHCInUcastPkts)
- `dci_snmp_interface_out_packets_total` (对应 ifHCOutUcastPkts)
- `dci_snmp_cpu_usage_percent` (对应 hwEntityCpuUsage)
- `dci_snmp_memory_usage_percent` (对应 hwEntityMemUsage)
- `dci_snmp_temperature_celsius` (对应 hwEntityTemperature)

### 4.1.2 标签规范

标准标签集包括：

| 标签名称 | 描述 | 来源 |
|---------|------|------|
| `device` | 设备标识符 | sysName |
| `device_type` | 设备类型 | 配置文件 |
| `vendor` | 设备厂商 | 配置映射 |
| `model` | 设备型号 | sysDescr解析 |
| `datacenter` | 数据中心标识 | 环境变量 |
| `interface` | 接口名称 | ifDescr/ifName |
| `interface_index` | 接口索引 | ifIndex |
| `agent_host` | 采集Agent主机名 | Telegraf配置 |

## 4.2 Kafka到Prometheus的数据转换

根据DCI监测系统总体设计（02-网络自动化平台-数据监测系统技术概要设计.md），Agent端所有采集数据应通过Kafka Topic发送。在Prometheus+Thanos架构下，我们采用Kafka中转模式作为标准实现方式：

### 4.2.1 Kafka中转模式

在中转模式下，Telegraf的数据流转过程如下：

1. **Telegraf采集数据**：从SNMP设备获取监控数据
2. **写入Kafka**：通过kafka输出插件将数据写入指定主题
3. **数据转换**：使用Kafka-Prometheus连接器（如Prometheus Kafka Adapter或自定义Exporter）将数据转换为Prometheus格式
4. **Prometheus抓取**：Prometheus通过抓取连接器的HTTP端点获取指标

该模式的优势：
- 完全符合DCI监测系统"Agent端各类采集数据均通过Kafka Topic发送"的设计原则
- 提供强大的数据缓冲和解耦能力
- 支持多系统并行消费同一份数据
- 增强故障隔离和系统弹性
- 便于历史数据的长期存储和分析

Kafka-Prometheus连接器配置示例（使用自定义Exporter）：

```yaml
# Kafka-Prometheus Exporter配置
kafka:
  brokers:
    - "kafka1:9092"
    - "kafka2:9092"
    - "kafka3:9092"
  topic: "dci.monitor.v1.defaultchannel.metrics.telegraf"
  consumer_group: "prometheus-metrics-exporter"
  
prometheus:
  listen: ":9090"
  path: "/metrics"
  expiration: "5m"  # 指标过期时间

transformations:
  # 数据格式转换规则
  json_to_prometheus:
    enabled: true
    name_key: "name"
    value_key: "value"
    timestamp_key: "timestamp"
    tag_keys:
      - "device"
      - "interface"
      - "datacenter"
```

## 4.3 服务发现配置

在Prometheus中配置以下服务发现，自动采集Kafka-Prometheus连接器暴露的指标：

```yaml
scrape_configs:
  - job_name: 'kafka-snmp-metrics'
    kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
            - monitoring
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_label_app]
        regex: 'kafka-prometheus-exporter'
        action: keep
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: instance
      - source_labels: [__meta_kubernetes_pod_label_region]
        target_label: region
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'dci_snmp_.+'
        action: keep
```

## 4.4 采集频率与存储策略

基于《00-02-DCI-数据监测系统需求分析.md》中的采集要求，针对不同类型的SNMP数据配置如下采集频率和存储策略：

| 数据类型 | 采集频率 | 本地热存储 | 长期冷存储 | 降采样策略 |
|---------|---------|----------|-----------|----------|
| 接口流量统计 (ifHCInOctets/ifHCOutOctets) | 15秒 | 547天(1.5年) | 1825天(5年) | 15秒→5分钟→1小时→1天 |
| CPU/内存使用率 (hwEntityCpuUsage/hwEntityMemUsage) | 15秒 | 547天(1.5年) | 1825天(5年) | 15秒→5分钟→1小时→1天 |
| 设备状态信息（温度等） | 1分钟 | 547天(1.5年) | 1825天(5年) | 1分钟→5分钟→1小时→1天 |
| 接口状态变化 (ifOperStatus) | 60秒 | 547天(1.5年) | 1825天(5年) | 60秒→1小时→1天 |
| LLDP拓扑信息 (lldpRemSysName等) | 5分钟 | 547天(1.5年) | 1825天(5年) | 无（保留原始数据） |
| 系统基本信息 (sysName/sysDescr) | 5分钟 | 547天(1.5年) | 1825天(5年) | 无（保留原始数据） |

## 4.5 告警规则

基于采集的SNMP数据，在Prometheus中配置以下核心告警规则：

```yaml
groups:
- name: snmp-alerts
  rules:
  - alert: DeviceDown
    expr: up{job="kafka-snmp-metrics"} == 0
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "设备 {{ $labels.device }} 无法访问"
      description: "设备 {{ $labels.device }} 已经无法访问超过5分钟。"

  - alert: InterfaceDown
    expr: dci_snmp_interface_oper_status{interface=~"^(Ethernet|GigabitEthernet).*"} != 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "设备 {{ $labels.device }} 接口 {{ $labels.interface }} 宕机"
      description: "设备 {{ $labels.device }} 的接口 {{ $labels.interface }} 状态为down。"

  - alert: HighCpuUsage
    expr: dci_snmp_cpu_usage_percent > 80
    for: 15m
    labels:
      severity: warning
    annotations:
      summary: "设备 {{ $labels.device }} CPU使用率高"
      description: "设备 {{ $labels.device }} CPU使用率超过80%已持续15分钟。"

  - alert: HighMemoryUsage
    expr: dci_snmp_memory_usage_percent > 90
    for: 15m
    labels:
      severity: warning
    annotations:
      summary: "设备 {{ $labels.device }} 内存使用率高"
      description: "设备 {{ $labels.device }} 内存使用率超过90%已持续15分钟。"
      
  - alert: HighTemperature
    expr: dci_snmp_temperature_celsius > 65
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "设备 {{ $labels.device }} 温度过高"
      description: "设备 {{ $labels.device }} 温度超过65°C已持续5分钟。"
      
  - alert: InterfaceHighTraffic
    expr: rate(dci_snmp_interface_in_octets_bytes_total[5m]) * 8 / 1000000000 > 0.8 * on(interface) group_left interface_speed_gbps
    for: 15m
    labels:
      severity: warning
    annotations:
      summary: "设备 {{ $labels.device }} 接口 {{ $labels.interface }} 流量过高"
      description: "设备 {{ $labels.device }} 接口 {{ $labels.interface }} 流量超过接口带宽的80%已持续15分钟。"

  - alert: LLDPNeighborChange
    expr: changes(dci_snmp_lldp_rem_sys_name[1h]) > 0
    labels:
      severity: info
    annotations:
      summary: "设备 {{ $labels.device }} LLDP邻居设备发生变化"
      description: "设备 {{ $labels.device }} 的LLDP邻居设备在过去1小时内发生了变化，可能表明网络拓扑有调整。"
```

# 5 实施计划

## 5.1 分阶段实施

SNMP采集实施计划分为以下阶段：

1. **阶段一：基础设置（已完成）**
   - 准备Telegraf配置模板，配置针对两台测试交换机的SNMP采集任务
   - 使用OID模式替代MIB模式，避免MIB文件解析问题
   - 实现针对华为特殊索引格式的LLDP和设备性能指标采集
   - 验证基本采集功能，确保CPU、内存、接口流量等关键指标采集成功

2. **阶段二：小规模试点**
   - 选择1-2台设备进行试点
   - 验证MIB兼容性和数据质量
   - 优化配置参数和采集频率
   - 实现Kafka输出插件配置

3. **阶段三：全面部署**
   - 按区域/设备类型分批部署
   - 配置Prometheus服务发现
   - 设置基本告警规则

4. **阶段四：优化与扩展**
   - 性能优化和资源调整
   - 完善告警规则
   - 构建Grafana仪表板【优先级低】
   - 针对不同厂商设备优化配置模板

# 6 运维与管理

## 6.1 配置管理

Telegraf配置采用以下管理方法：

1. **配置文件结构**：
   - 全局配置：`telegraf.conf`
   - 公共SNMP配置：`snmp-common.conf`
   - 厂商特定配置：`snmp-cisco.conf`, `snmp-huawei.conf`等
   - 设备组配置：`snmp-core-switches.conf`, `snmp-access-switches.conf`等

# 附录

## 附录A 参考文档

1. [Telegraf SNMP Input Plugin官方文档](https://github.com/influxdata/telegraf/tree/master/plugins/inputs/snmp)
2. [Prometheus Exposition Format](https://prometheus.io/docs/instrumenting/exposition_formats/)
3. [Thanos Query API](https://thanos.io/tip/components/query.md/)
4. [SNMP MIB标准](https://www.ietf.org/rfc/rfc1213.txt)
5. [DCI监测系统Prometheus+Thanos架构设计文档](07-DCI-Prometheus和Thanos架构设计.md)
6. [DCI监测系统指标和标签规范](08-DCI-Prometheus指标和标签规范.md)
7. [DCI-Kafka主题规划及负载均衡连接设计](13-DCI-Kafka主题规划及负载均衡连接设计.md)
8. [DCI数据监测系统技术概要设计](02-网络自动化平台-数据监测系统技术概要设计.md)
9. [DCI数据监测系统需求分析](00-02-DCI-数据监测系统需求分析.md)
10. [DCI-dciagent中Telegraf数据采集方案设计](14-DCI-dciagent中Telegraf数据采集方案设计.md)
11. [DCI-dciagent客户端架构设计](15-01-DCI-dciagent客户端架构设计.md)
12. [DCI-dciagent部署与配置设计](15-02-DCI-dciagent部署与配置设计.md)
13. [DCI-Prometheus数据保留与降采样策略](09-DCI-Prometheus数据保留与降采样策略.md)
