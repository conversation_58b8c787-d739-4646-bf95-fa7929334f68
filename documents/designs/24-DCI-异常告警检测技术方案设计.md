---
title: |
  DCI-异常告警检测技术方案设计

subtitle: |
  基于Prometheus和Elasticsearch的多源数据告警机制
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-07-10 | 顾铠羟 | 初始版本           |
| V1.0 | 2025-07-31 | 顾铠羟 | 补充协同联动部分数据库相关    |

# 1 文档介绍

## 1.1 文档目的

本文档描述DCI监测系统中异常告警检测模块的技术方案设计，包括基于Prometheus的指标告警机制和基于Elasticsearch的日志告警机制，以及统一告警管理功能。该方案将集成到现有的dcimonitor服务中，实现对网络设备状态、流量异常和系统日志的实时监控和告警。

## 1.2 文档范围

本文档涵盖以下内容：
- 异常告警检测模块的整体架构设计
- 指标数据告警机制的实现方案
- 日志数据告警机制的实现方案
- 统一告警管理的设计
- 告警通知渠道的实现
- 与现有dcimonitor系统的集成方案

## 1.3 文档关联

本文档与以下文档相关联：
- 《00-DCI数据监测系统项目文档路书.md》：作为项目文档路书中的一部分，补充"%待设计%-DCI-异常告警检测技术方案设计"
- 《02-网络自动化平台-数据监测系统技术概要设计.md》：提供系统整体架构
- 《04-指标数据基线监测及告警技术方案设计.md》：提供基线监测的算法和方法
- 《07-DCI-Prometheus和Thanos架构设计.md》：提供Prometheus架构设计
- 《08-DCI-Prometheus指标和标签规范.md》：提供指标命名和标签规范
- 《23-DCI-prometheus-snmp接口状态监测服务技术设计.md》：提供接口状态监测服务设计

# 2 总体设计

## 2.1 设计目标

1. 实现基于Prometheus的指标数据告警机制，支持对网络设备状态、流量等指标的异常检测
2. 实现基于Elasticsearch的日志数据告警机制，支持对系统日志的异常模式识别
3. 设计统一的告警数据模型和管理接口，整合不同来源的告警信息
4. 提供灵活的告警规则配置和管理功能，支持静态阈值和动态基线告警
5. 实现多渠道告警通知机制，包括邮件、Webhook等
6. 与现有dcimonitor系统无缝集成，共享同一个二进制代码

## 2.2 架构设计

### 2.2.1 详细架构图

```mermaid
graph TD
    subgraph "数据源层"
        Prometheus["Prometheus<br/>指标数据"]
        Elasticsearch["Elasticsearch<br/>日志数据"]
        MySQL["MySQL<br/>元数据"]
    end
    
    subgraph "告警引擎层"
        PrometheusAlertManager["Prometheus<br/>AlertManager"]
        ESAlertEngine["ES告警引擎"]
        
        PrometheusRules["Prometheus<br/>告警规则"]
        ESRules["ES告警规则"]
        
        PrometheusAlertManager --> |"执行"|PrometheusRules
        ESAlertEngine --> |"执行"|ESRules
    end
    
    subgraph "告警处理层"
        AlertProcessor["告警处理器"]
        AlertStore["告警存储"]
        NotificationService["通知服务"]
    end
    
    subgraph "API服务层"
        AlertMonitor["告警监控器<br/>(AlertMonitor)"]
        AlertAPI["告警API"]
    end
    
    Prometheus --> PrometheusAlertManager
    Elasticsearch --> ESAlertEngine
    MySQL --> AlertStore
    
    PrometheusAlertManager --> |"告警事件"|AlertProcessor
    ESAlertEngine --> |"告警事件"|AlertProcessor
    
    AlertProcessor --> AlertStore
    AlertProcessor --> NotificationService
    
    AlertStore --> AlertAPI
    AlertAPI --> AlertMonitor
    
    User["用户"] --> AlertMonitor
```

### 2.2.2 极简架构图

```mermaid
sequenceDiagram
    participant DataSources as 数据源(Prometheus/ES)
    participant AlertEngines as 告警引擎
    participant AlertProcessor as 告警处理器
    participant NotificationService as 通知服务
    participant API as 告警API
    participant User as 用户
    
    DataSources->>AlertEngines: 数据查询与评估
    AlertEngines->>AlertProcessor: 触发告警事件
    AlertProcessor->>NotificationService: 发送通知
    AlertProcessor->>API: 存储告警记录
    User->>API: 查询告警信息
    API->>User: 返回告警数据
```

## 2.3 数据流/流程图

```mermaid
sequenceDiagram
    participant Prometheus
    participant AlertManager
    participant ESEngine as ES告警引擎
    participant Processor as 告警处理器
    participant Store as 告警存储
    participant Notifier as 通知服务
    participant API as 告警API
    
    Note over Prometheus,AlertManager: 指标数据告警流程
    Prometheus->>AlertManager: 评估告警规则
    AlertManager->>Processor: Webhook回调
    
    Note over ESEngine,Processor: 日志数据告警流程
    ESEngine->>ESEngine: 定期查询ES
    ESEngine->>Processor: 触发告警事件
    
    Note over Processor,API: 告警处理流程
    Processor->>Store: 存储告警记录
    Processor->>Processor: 去重与分组
    Processor->>Notifier: 发送告警通知
    
    Note over API: API访问流程
    API->>Store: 查询告警记录
    API->>API: 格式化响应
```

## 2.4 模块化设计

异常告警检测系统包含以下主要模块：

1. **告警监控器(AlertMonitor)**：作为dcimonitor中的新模块，处理告警相关的HTTP请求
2. **告警服务(AlertService)**：核心业务逻辑层，负责告警规则管理、告警处理和通知分发
3. **告警数据访问层(AlertDAO)**：负责与MySQL数据库交互，存储和查询告警记录和规则
4. **Prometheus告警适配器**：与Prometheus AlertManager集成，接收和处理指标告警
5. **ES告警引擎**：定期查询Elasticsearch，根据规则生成日志告警
6. **通知服务**：负责通过不同渠道发送告警通知，如邮件、Webhook等

## 2.5 技术选型

1. **告警规则存储**：MySQL数据库，与现有系统共用
2. **指标告警引擎**：Prometheus AlertManager，利用其强大的告警规则评估能力
3. **日志告警引擎**：自研ES告警引擎，基于Elasticsearch查询实现
4. **告警通知**：邮件(SMTP)、Webhook、Kafka消息
5. **API框架**：Gin框架，与现有dcimonitor保持一致
6. **开发语言**：Go语言

# 3 详细设计

## 3.1 功能模块

### 3.1.1 告警数据模型

设计统一的告警数据模型，用于表示来自不同数据源的告警：

```mermaid
classDiagram
    class Alert {
        +string ID
        +string Name
        +string Source
        +string Level
        +string Status
        +string Description
        +map[string]string Labels
        +map[string]string Annotations
        +time.Time StartsAt
        +time.Time EndsAt
        +time.Time UpdatedAt
        +string GeneratorURL
    }
    
    class AlertRule {
        +string ID
        +string Name
        +string Description
        +string Source
        +string Expression
        +string Level
        +int Duration
        +map[string]string Labels
        +map[string]string Annotations
        +bool Enabled
    }
    
    class AlertNotification {
        +string ID
        +string Name
        +string Type
        +map[string]string Settings
        +bool Enabled
    }
    
    Alert --> AlertRule: 由规则生成
    Alert --> AlertNotification: 通过通知发送
```

### 3.1.2 指标数据告警机制

基于Prometheus AlertManager实现指标数据的告警机制：

```mermaid
sequenceDiagram
    participant API as 告警API
    participant Service as AlertService
    participant DAO as AlertDAO
    participant PrometheusClient
    participant AlertManager
    
    Note over API,AlertManager: 告警规则管理流程
    API->>Service: 创建/更新告警规则
    Service->>DAO: 存储规则到MySQL
    Service->>PrometheusClient: 生成Prometheus规则文件
    PrometheusClient->>AlertManager: 应用规则配置
    
    Note over AlertManager,DAO: 告警触发流程
    AlertManager->>Service: Webhook回调(告警触发)
    Service->>Service: 规范化告警数据
    Service->>DAO: 存储告警记录
    Service->>Service: 发送通知
```

指标告警规则示例：

```yaml
groups:
- name: device_status
  rules:
  - alert: DeviceCPUUsageHigh
    expr: dci_snmp_status_cpu_usage > 80
    for: 5m
    labels:
      severity: warning
      category: device
    annotations:
      summary: "设备CPU使用率过高"
      description: "设备 {{ $labels.device_id }} CPU使用率为 {{ $value }}%，已超过80%阈值持续5分钟"
```

### 3.1.3 日志数据告警机制

基于Elasticsearch实现日志数据的告警机制：

```mermaid
sequenceDiagram
    participant Scheduler as 定时任务
    participant ESEngine as ES告警引擎
    participant ESClient as ES客户端
    participant Service as AlertService
    
    Scheduler->>ESEngine: 触发规则评估
    ESEngine->>ESEngine: 加载ES告警规则
    ESEngine->>ESClient: 执行ES查询
    ESClient->>ESEngine: 返回查询结果
    ESEngine->>ESEngine: 评估告警条件
    ESEngine->>Service: 触发告警(如果条件满足)
    Service->>Service: 处理告警
```

日志告警规则示例：

```json
{
  "name": "InterfaceFlappingDetection",
  "description": "检测接口频繁抖动",
  "query": {
    "bool": {
      "must": [
        { "match": { "message": "Interface state changed" } },
        { "range": { "@timestamp": { "gte": "now-15m" } } }
      ]
    }
  },
  "groupBy": ["device_id", "ifName"],
  "threshold": 5,
  "timeWindow": "15m",
  "severity": "warning"
}
```

### 3.1.4 统一告警管理

设计统一的告警管理接口，整合不同来源的告警：

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as 告警API
    participant Service as AlertService
    participant DAO as AlertDAO
    
    Client->>API: 获取告警列表
    API->>Service: 查询告警
    Service->>DAO: 从数据库查询
    DAO->>Service: 返回告警记录
    Service->>API: 返回处理后的告警
    API->>Client: 响应告警列表
    
    Client->>API: 确认告警
    API->>Service: 更新告警状态
    Service->>DAO: 存储状态变更
    DAO->>Service: 确认更新成功
    Service->>API: 返回更新结果
    API->>Client: 响应状态更新
```

### 3.1.5 告警通知服务

实现多渠道的告警通知机制：

```mermaid
sequenceDiagram
    participant Service as AlertService
    participant NotificationService
    participant EmailSender
    participant WebhookSender
    participant KafkaSender
    
    Service->>NotificationService: 发送告警通知
    NotificationService->>NotificationService: 查找通知渠道配置
    
    alt 邮件通知
        NotificationService->>EmailSender: 发送邮件
    else Webhook通知
        NotificationService->>WebhookSender: 发送HTTP请求
    else Kafka通知
        NotificationService->>KafkaSender: 发送Kafka消息
    end
    
    NotificationService->>Service: 返回发送结果
```

## 3.2 数据模型

### 3.2.1 告警记录表

```sql
CREATE TABLE `monitor_alert` (
  `id` varchar(36) NOT NULL COMMENT '告警ID',
  `name` varchar(255) NOT NULL COMMENT '告警名称',
  `source` varchar(50) NOT NULL COMMENT '告警来源(prometheus/elasticsearch)',
  `level` varchar(20) NOT NULL COMMENT '告警级别(critical/warning/info)',
  `status` varchar(20) NOT NULL COMMENT '告警状态(firing/resolved/acknowledged)',
  `description` text NOT NULL COMMENT '告警描述',
  `labels` json DEFAULT NULL COMMENT '告警标签',
  `annotations` json DEFAULT NULL COMMENT '告警注释',
  `starts_at` timestamp NOT NULL COMMENT '告警开始时间',
  `ends_at` timestamp NULL DEFAULT NULL COMMENT '告警结束时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `generator_url` varchar(255) DEFAULT NULL COMMENT '告警生成URL',
  PRIMARY KEY (`id`),
  KEY `idx_source_status` (`source`, `status`),
  KEY `idx_starts_at` (`starts_at`),
  KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警记录表';
```

### 3.2.2 告警规则表

```sql
CREATE TABLE `monitor_alert_rule` (
  `id` varchar(36) NOT NULL COMMENT '规则ID',
  `name` varchar(255) NOT NULL COMMENT '规则名称',
  `description` text COMMENT '规则描述',
  `source` varchar(50) NOT NULL COMMENT '规则来源(prometheus/elasticsearch)',
  `expression` text NOT NULL COMMENT '规则表达式',
  `level` varchar(20) NOT NULL COMMENT '告警级别(critical/warning/info)',
  `duration` int NOT NULL DEFAULT '0' COMMENT '持续时间(秒)',
  `labels` json DEFAULT NULL COMMENT '规则标签',
  `annotations` json DEFAULT NULL COMMENT '规则注释',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name_source` (`name`, `source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警规则表';
```

### 3.2.3 告警通知表

```sql
CREATE TABLE `monitor_alert_notification` (
  `id` varchar(36) NOT NULL COMMENT '通知ID',
  `name` varchar(255) NOT NULL COMMENT '通知名称',
  `type` varchar(50) NOT NULL COMMENT '通知类型(email/webhook/kafka)',
  `settings` json NOT NULL COMMENT '通知设置',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警通知表';
```

## 3.3 接口设计

### 3.3.1 告警规则管理API

```
# 获取告警规则列表
GET /api/v1/alerts/rules

# 创建告警规则
POST /api/v1/alerts/rules

# 获取指定告警规则
GET /api/v1/alerts/rules/:id

# 更新告警规则
PUT /api/v1/alerts/rules/:id

# 删除告警规则
DELETE /api/v1/alerts/rules/:id

# 启用/禁用告警规则
POST /api/v1/alerts/rules/:id/toggle
```

### 3.3.2 告警记录查询API

```
# 获取告警记录列表
GET /api/v1/alerts

# 获取指定告警记录
GET /api/v1/alerts/:id

# 确认告警
POST /api/v1/alerts/:id/acknowledge

# 获取告警统计信息
GET /api/v1/alerts/stats
```

### 3.3.3 告警通知管理API

```
# 获取通知渠道列表
GET /api/v1/alerts/notifications

# 创建通知渠道
POST /api/v1/alerts/notifications

# 获取指定通知渠道
GET /api/v1/alerts/notifications/:id

# 更新通知渠道
PUT /api/v1/alerts/notifications/:id

# 删除通知渠道
DELETE /api/v1/alerts/notifications/:id

# 测试通知渠道
POST /api/v1/alerts/notifications/:id/test
```

### 3.3.4 Prometheus AlertManager回调API

```
# 接收AlertManager的告警回调
POST /api/v1/alerts/webhook/prometheus
```

# 4 安全设计

告警系统的安全设计主要包括以下几个方面：

1. **API访问控制**：所有告警API遵循现有dcimonitor系统的认证和授权机制
2. **数据加密**：敏感配置信息（如邮件密码、Webhook密钥等）在数据库中加密存储
3. **告警规则验证**：对用户提交的告警规则进行严格验证，防止注入攻击
4. **通知渠道安全**：支持SMTP TLS、Webhook HTTPS等安全通信方式

# 5 本设计的代码实现文件列表

```
dci-monitor/src/dcimonitor/
├── internal/
│   ├── models/
│   │   ├── alert.go           # 告警相关数据模型
│   │   └── notification.go    # 通知相关数据模型
│   ├── services/
│   │   ├── alert_dao.go       # 告警数据访问层
│   │   ├── alert_service.go   # 告警服务实现
│   │   ├── prom_alert.go      # Prometheus告警适配器
│   │   ├── es_alert.go        # ES告警引擎
│   │   └── notification.go    # 通知服务
│   └── monitors/
│       └── alert_monitor.go   # 告警API控制器
├── cmd/
│   └── alert/
│       └── alert.go           # 告警命令行工具
└── config/
    └── alerting.yaml          # 告警配置文件模板
```

## 6 实施计划

1. 设计并实现告警数据模型和数据库表结构
2. 开发Prometheus告警适配器，实现与AlertManager的集成
3. 开发ES告警引擎，实现日志告警规则评估
4. 实现告警通知服务，支持多种通知渠道
5. 开发告警API，提供规则管理和告警查询功能
6. 集成到现有dcimonitor系统，进行单元测试和集成测试
7. 编写操作文档和用户手册

