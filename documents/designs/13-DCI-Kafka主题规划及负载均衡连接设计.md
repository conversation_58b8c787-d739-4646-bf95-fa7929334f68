---
title: |
  DCI 数据监测系统 - Kafka 主题设计与规划
subtitle: |
  定义消息总线的主题结构、格式与策略
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-06 | 顾铠羟 | 初始版本，定义核心主题 |
| V1.1 | 2025-05-09 | 顾铠羟 | 添加主题管理工具使用说明 |
| V1.2 | 2025-06-10 | 顾铠羟 | 新增网络连接与安全设计章节，实现内外网统一安全访问架构 |

# 1. 引言

## 1.1 文档目的

本文档旨在详细规划 DCI 数据监测系统中用于不同类型数据传输的 Kafka 主题，包括主题命名规范、分区策略、消息格式、保留策略以及相关的生产者和消费者交互模式。

## 1.2 文档范围

本文档覆盖系统运行所需的核心 Kafka 主题设计，主要包括监控指标、日志/事件和拓扑信息。

# 2. 设计原则

*   **清晰性**: 主题名称应清晰反映其承载的数据类型和来源。
*   **一致性**: 遵循统一的命名规范。
*   **可扩展性**: 设计应考虑未来可能增加的新数据类型或来源。
*   **性能**: 分区策略应有助于消费者负载均衡和特定场景下的消息有序性。
*   **管理**: 保留策略应平衡数据时效性和存储成本。

# 3. 主题命名规范

采用点分层级结构：`{项目前缀}.{子系统名称}.{版本}.{渠道ID-临时占用符}.{数据类型}.[{来源/细分}.{采集工具}]`

*   **项目前缀**: `dci` (固定)
*   **子系统名称**: `monitor` (固定，代表 DCI Monitor 子系统)
*   **版本**: `v1` (当前主题结构版本)
*   **渠道ID-临时占用符**: 这是一个临时占位符，未来可用于标识不同的数据来源渠道、租户或特定的数据流实例。在具体部署时，此占位符将被替换为实际的渠道标识符。在本文档的后续示例以及实际部署脚本中，我们将首先使用 `defaultchannel` 作为此占位符在具体主题名称中的值。
*   **数据类型**: 标识主要数据类别，目前主要分四大类：流量、指标、日志、拓扑，对应 `flows`, `metrics`, `logs`, `topology`。
*   **来源/细分 (可选)**: 进一步区分数据来源或子类别，如 `telegraf`, `syslog`, `lldp`, `control`。

# 4. 核心主题设计

## 4.1 接口类主题

Topic示例：`dci.monitor.v1.defaultchannel.interfaces.snmp.telegraf`

*   **用途**: 接收由 Telegraf Agent 采集的各类接口数据（如 SNMP 等）。

## 4.2 流量类主题

Topic示例：`dci.monitor.v1.defaultchannel.flows.sflow`

*   **用途**: 接收由 Telegraf Agent 采集的各类流量数据（如 sFlow, NetFlow 等）。

## 4.3 指标类主题 

Topic示例：`dci.monitor.v1.defaultchannel.metrics.telegraf`

*   **用途**: 接收由 Telegraf Agent 采集的各类监控指标数据（如 CPU, Mem, Net, SNMP 等）。
*   **消息格式**: JSON (采用 Telegraf Kafka 输出插件的默认 JSON 格式，包含 `fields`, `name`, `tags`, `timestamp`)。
*   **分区策略**: 建议按 `tags` 中的 `agent_id` 或 `hostname` 进行分区。这有助于将同一 Agent 的数据发送到同一分区，方便某些需要按 Agent 聚合或处理的场景，也能实现基本的负载均衡。分区数需根据预期 Agent 数量和 Kafka 集群规模设定。
*   **保留策略**: 建议 7 天。指标数据量大，通常不需要长期保留原始数据在 Kafka 中，后端存储 (TDengine) 会负责长期存储。
*   **生产者**: Telegraf Agent (`[[outputs.kafka]]`)。
*   **消费者**: 指标数据写入服务 (`3eee8844-...`)。
*   **消费者组**: `dci-metrics-writer-group`。

## 4.4 日志类主题

Topic示例：`dci.monitor.v1.defaultchannel.logs.syslog`

*   **用途**: 接收解析后的 Syslog 日志数据。
*   **消息格式**: JSON。字段应包含标准化后的信息，如 `timestamp`, `hostname`, `appname`, `severity`, `message`，以及原始日志 `raw_message`。
*   **分区策略**: 按来源设备 `hostname` 或 IP 地址分区。有助于按设备查询或处理日志，同时实现负载均衡。
*   **保留策略**: 30 天或根据要求设定。日志数据可能需要较长时间追溯。
*   **生产者**: Syslog 接收服务/Logstash/Fluentd 等日志收集组件。
*   **消费者**: 日志与事件数据写入服务 (`633d7432-...`)。
*   **消费者组**: `dci-logs-writer-group`。

## 4.5 拓扑类主题

Topic示例：`dci.monitor.v1.defaultchannel.topology.lldp`

*   **用途**: 接收 LLDP 邻居信息原始数据，通常由定制的采集脚本或特定 Telegraf 插件 (如 `inputs.exec` 或 `inputs.snmp`) 采集后发送。
*   **消息格式**: JSON。包含采集时间戳、采集 Agent 标识、源设备标识以及解析出的 LLDP 邻居信息（如邻居设备 ID、端口 ID 等）。
*   **分区策略**: 按采集 Agent ID (`agent_id`) 分区。
*   **保留策略**: 1 天。拓扑信息具有时效性，处理服务消费后生成拓扑快照，原始消息无需长期保留。
*   **生产者**: 定制的 LLDP 采集脚本/服务或配置了相关插件的 Telegraf Agent。
*   **消费者**: 拓扑数据处理服务 (`8b27b4eb-...`)。
*   **消费者组**: `dci-topology-processor-group`。



# 5. 生产者与消费者交互

*   所有生产者应配置必要的 `acks` 设置（例如 `acks=1` 或 `acks=all`）以确保消息传递的可靠性。
*   所有消费者应使用独立的 `group.id`，避免互相干扰。
*   消费者应妥善处理消息位移 (offset)，根据业务需求选择自动提交或手动提交，确保消息不丢失或不重复处理（或至少能幂等处理）。

# 6. 未来扩展

*   若引入新的数据类型（如配置变更事件、安全告警），可按照命名规范创建新的主题，例如 `dci.monitor.v1.defaultchannel.events.config_change`。
*   若数据量巨大，可考虑对特定主题增加分区数。

# 7. 主题管理工具

DCI 数据监测系统的命令行工具包含用于管理 Kafka 主题的子命令，该工具是基于 Go 语言实现的，提供更丰富的功能和更好的跨平台支持。

## 7.1 命令结构

DCI-Monitor 的 Kafka 管理工具采用分层命令结构：

```
dci-monitor kafka              # Kafka管理工具根命令
└── init-topics                # 初始化Kafka主题命令
```

### 7.1.1 `kafka` 根命令

`kafka` 是所有 Kafka 相关操作的根命令，提供全局选项：

| 参数 | 描述 | 默认值 | 示例 |
|------|------|--------|------|
| `--kafka-config` | Kafka配置文件路径 | `./config/kafka.yaml` | `--kafka-config=/etc/dci/kafka.yaml` |
| `--brokers` | Kafka服务器连接字符串 | (配置文件中的设置) | `--brokers=kafka1:9092,kafka2:9092` |

### 7.1.2 `init-topics` 子命令

`init-topics` 子命令用于初始化 Kafka 主题，支持：

| 参数 | 短参数 | 描述 | 默认值 | 示例 |
|------|--------|------|--------|------|
| `--all` | `-a` | 初始化所有定义的主题 | `false` | `--all` |
| `--topic` | `-t` | 指定要初始化的单个主题名称 | - | `--topic=dci.monitor.v1.defaultchannel.metrics.telegraf` |
| `--dry-run` | `-d` | 演示模式，不实际执行操作 | `false` | `--dry-run` |

## 7.2 配置文件格式

Kafka 主题管理工具使用 YAML 格式的配置文件，默认位于 `./config/kafka.yaml`：

```yaml
kafka:
  # Kafka集群连接信息
  brokers:
    - "dcikafka.intra.citic-x.com:30002"
  
  # 安全配置（可选）
  security:
    tls:
      enabled: false
      # certFile: /path/to/cert.pem
      # keyFile: /path/to/key.pem
      # caFile: /path/to/ca.pem
    sasl:
      enabled: false
      # mechanism: PLAIN
      # username: user
      # password: pass
  
  # 主题配置
  topics:
    - name: "dci.monitor.v1.defaultchannel.topology.lldp"
      partitions: 3
      replicationFactor: 2
      retentionHours: 24  # 1天
      config:
        # 额外的主题级别配置
        "cleanup.policy": "delete"
        
    - name: "dci.monitor.v1.defaultchannel.metrics.telegraf"
      partitions: 6
      replicationFactor: 2
      retentionHours: 168  # 7天
      
    - name: "dci.monitor.v1.defaultchannel.logs.syslog"
      partitions: 3
      replicationFactor: 2
      retentionHours: 720  # 30天
```

配置文件中的主题定义与本文档第 4 节中的核心主题设计保持一致，包含分区数、副本因子和保留策略等信息。

## 7.3 使用示例

### 7.3.1 初始化所有主题

```bash
# 使用默认配置文件
dci-monitor kafka init-topics --all

# 指定配置文件
dci-monitor kafka init-topics --all --kafka-config=/path/to/custom/kafka.yaml

# 演示模式（不实际创建）
dci-monitor kafka init-topics --all --dry-run
```

### 7.3.2 初始化单个主题

```bash
# 初始化特定主题
dci-monitor kafka init-topics --topic=dci.monitor.v1.defaultchannel.metrics.telegraf

# 使用自定义Broker地址
dci-monitor kafka init-topics --topic=dci.monitor.v1.defaultchannel.metrics.telegraf --brokers=localhost:9092
```

## 7.4 与原有 Shell 脚本对比

| Shell脚本命令 | Go工具命令 | 说明 |
|--------------|-----------|------|
| `./create_kafka_topics.sh -a` | `dci-monitor kafka init-topics --all` | 创建所有主题 |
| `./create_kafka_topics.sh -t <topic>` | `dci-monitor kafka init-topics --topic=<topic>` | 创建指定主题 |
| `./create_kafka_topics.sh -d -a` | `dci-monitor kafka init-topics --all --dry-run` | 演示模式 |

## 7.5 未来规划

未来版本计划为主题管理工具添加更多功能：

1. 列出现有主题：`dci-monitor kafka list-topics`
2. 修改主题配置：`dci-monitor kafka alter-topic`
3. 删除主题：`dci-monitor kafka delete-topic`
4. 查看主题详情：`dci-monitor kafka describe-topic`

更多详细使用说明，请参考[DCI监测系统Kafka子命令工具使用指南](../../apis/07-DCI监测系统Kafka子命令工具使用指南.md)。

# 8. Kafka网络连接与安全设计

本章节详细阐述 Kafka 集群的网络架构和安全配置，旨在实现一个能同时支持 Kubernetes 集群内部和外部客户端安全访问的统一消息平台。

## 8.1 设计目标

*   **内外网统一访问**: Kafka 集群须同时为部署在 Kubernetes 集群内部的服务（如 `dcimonitor-flowdata`）和集群外部的客户端（如 `Telegraf`）提供稳定、可靠的连接。
*   **强制安全通信**: 所有客户端与 Kafka Broker 之间的通信，以及 Broker 之间的内部通信，都必须启用 TLS 加密。
*   **身份认证**: 所有客户端（无论内外）连接 Kafka 都必须通过 SASL/PLAIN 机制进行用户名和密码认证。
*   **动态地址广播**: Kafka Broker 必须能根据客户端的来源网络，向其广播正确的、可解析的访问地址，避免因网络地址转换（NAT）或内外网隔离导致的连接问题。

## 8.2 架构设计

为满足上述目标，Kafka 集群采用多监听器（Multi-Listener）架构，为不同类型的流量提供专用的网络通道和安全协议。

### 8.2.1 详细架构图

架构图如下：
```mermaid
graph TD
    subgraph "Kubernetes 集群"
        subgraph "dci Namespace"
            K0[("kafka-0")]
            K1[("kafka-1")]
            K2[("kafka-2")]
            
            DF["dcimonitor-flowdata (内部客户端)"]

            InternalSvc[/"kafka-headless<br>INTERNAL_CLIENT (30002)"/]
            
            DF -- "SASL_SSL" --> InternalSvc
            InternalSvc --> K0
            InternalSvc --> K1
            InternalSvc --> K2

            K0 <-.->|"REPLICATION (9092)<br>SSL"| K1
            K1 <-.->|"REPLICATION (9092)<br>SSL"| K2
            K2 <-.->|"REPLICATION (9092)<br>SSL"| K0
            
            K0 -- "CONTROLLER (9093)<br>SSL" --- K1
            K1 -- "CONTROLLER (9093)<br>SSL" --- K2
            K2 -- "CONTROLLER (9093)<br>SSL" --- K0
        end
        
        subgraph "Kubernetes Services (NodePort)"
            ExtSvc0[/"kafka-0-external<br>NodePort 30010"/] --> K0
            ExtSvc1[/"kafka-1-external<br>NodePort 30011"/] --> K1
            ExtSvc2[/"kafka-2-external<br>NodePort 30012"/] --> K2
        end
    end

    subgraph "外部网络"
        Telegraf["Telegraf (外部客户端)"]
    end
    
    Telegraf -- "SASL_SSL<br>dcikafka.intra.citic-x.com:30010" --> ExtSvc0
    Telegraf -- "SASL_SSL<br>dcikafka.intra.citic-x.com:30011" --> ExtSvc1
    Telegraf -- "SASL_SSL<br>dcikafka.intra.citic-x.com:30012" --> ExtSvc2
    
    classDef kafkaPod fill:#f9f,stroke:#333,stroke-width:2px;
    class K0,K1,K2 kafkaPod;
    classDef client fill:#bbf,stroke:#33f,stroke-width:1px;
    class DF,Telegraf client;
```

## 8.3 监听器 (Listener) 核心配置

Kafka 集群配置了四个独立的监听器，每个监听器都有明确的角色和安全配置。

| 监听器名称 | Pod 内部端口 | 安全协议 | `advertised.listeners` 广播地址 | 用途说明 |
| :--- | :--- | :--- | :--- | :--- |
| `REPLICATION` | `9092` | `SSL` | `kafka-x.kafka-headless...:9092` | Kafka Broker 之间的数据复制和同步，为内部可信流量。 |
| `INTERNAL_CLIENT` | `30002` | `SASL_SSL` | `kafka-x.kafka-headless...:30002` | 专供 Kubernetes 集群**内部**的客户端连接，强制加密和认证。 |
| `EXTERNAL_CLIENT` | `30003` | `SASL_SSL` | `dcikafka.intra.citic-x.com:3001x` | 专供 Kubernetes 集群**外部**的客户端连接，强制加密和认证。 |
| `CONTROLLER` | `9093` | `SSL` | `kafka-x.kafka-headless...:9093` | KRaft 模式下控制器之间的通信，用于集群选举和元数据管理。 |

## 8.4 Kubernetes 服务 (Service) 暴露方案

*   **Headless Service (`kafka-headless`)**:
    *   **作用**: 为每个 Kafka Pod 提供一个稳定且唯一的内部 DNS FQDN（如 `kafka-0.kafka-headless.dci.svc.cluster.local`）。
    *   **关联监听器**: `REPLICATION`, `INTERNAL_CLIENT`, `CONTROLLER`。这些监听器的 `advertised.listeners` 地址使用此 DNS 名称，确保了内部通信的可靠性。

*   **NodePort Services (`kafka-x-external`)**:
    *   **作用**: 为每个 Kafka Pod 创建一个独立的 `NodePort` 服务，将外部流量精确路由到指定 Pod。
    *   **关联监听器**: `EXTERNAL_CLIENT`。每个 `NodePort` 服务（如 `30010`, `30011`, `30012`）将流量转发到对应 Kafka Pod 的 `30003` 端口，即 `EXTERNAL_CLIENT` 监听器。

## 8.5 客户端连接指引

### 8.5.1 集群内部客户端 (Internal Clients)

*   **适用场景**: 部署在与 Kafka 同一个 Kubernetes 集群内的应用程序，如 `dcimonitor-flowdata`。
*   **Bootstrap Servers**: 必须连接到内部客户端监听器。配置应指向 `kafka-headless` 服务，并使用 `INTERNAL_CLIENT` 的端口 `30002`：
    `kafka-0.kafka-headless.dci.svc.cluster.local:30002,kafka-1.kafka-headless.dci.svc.cluster.local:30002,kafka-2.kafka-headless.dci.svc.cluster.local:30002`
*   **安全配置**:
    *   启用 TLS，并提供 CA 证书用于服务端校验。
    *   提供客户端证书和私钥用于双向认证。
    *   启用 SASL/PLAIN，并提供对应的用户名和密码。

### 8.5.2 集群外部客户端 (External Clients)

*   **适用场景**: 部署在 Kubernetes 集群外部的应用程序，如物理机或虚拟机上的 `Telegraf`。
*   **Bootstrap Servers**: 必须通过外部暴露的 `NodePort` 连接。配置应指向公共域名和每个 Broker 对应的 `NodePort`：
    `dcikafka.intra.citic-x.com:30010,dcikafka.intra.citic-x.com:30011,dcikafka.intra.citic-x.com:30012`
*   **安全配置**:
    *   与内部客户端完全相同：必须启用 TLS 和 SASL/PLAIN，并提供所有必需的证书和凭证。

## 8.6 安全配置总结

*   **TLS 加密**:
    *   采用三级证书体系（根 CA -> 中间 CA -> 服务/客户端证书），增强安全性。
    *   Kafka Broker 强制要求客户端提供有效证书进行身份验证 (`ssl.client.auth=required`)。
*   **SASL 认证**:
    *   使用 `PLAIN` 机制，所有客户端必须在 TLS 握手成功后，提供正确的用户名和密码才能建立连接。
    *   不同客户端（如 `dci-flowdata`）使用独立的用户名，为未来实现基于用户的 ACL 权限控制奠定基础。

---
