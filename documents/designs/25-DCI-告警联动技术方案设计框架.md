---
title: |
  DCI告警联动技术方案设计框架

subtitle: |
  多源告警统一管理与自动化任务协同监控架构
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-01-11 | 顾铠羟 | 初始版本           |

# 1 文档介绍

## 1.1 文档目的

本文档旨在设计DCI数据监测系统中告警联动的整体技术框架，涵盖指标类告警、日志类告警、拓扑变更告警等多种告警源的统一管理，以及与网络自动化控制系统的协同监控机制。通过建立完整的告警联动架构，实现对网络环境的全方位实时监控和智能化响应。

## 1.2 文档范围

本文档涵盖以下内容：
- 告警联动的整体架构设计
- 多源告警的统一管理机制
- 与网络自动化任务的协同监控框架
- 告警规则管理与事件处理流程
- 系统间的接口设计和数据流转

## 1.3 文档关联

本文档是DCI数据监测系统设计文档体系的核心组成部分，与以下文档密切相关：
- 基于《00-02-DCI-数据监测系统需求分析.md》中的告警联动需求
- 遵循《01-DCI-数据监测系统-项目目录及技术栈约束指南.md》的技术约束
- 细化《25-01-DCI-网络控制-prometheus指标类告警联动技术方案设计.md》的具体实现

# 2 总体设计

## 2.1 设计目标

基于DCI数据监测系统的监控需求和现有架构，告警联动的核心设计目标包括：

- **最小扩展**：基于现有WebHook告警处理架构，最小化代码和数据库变更
- **设备关联**：通过设备ID建立告警与网络自动化任务的精确关联机制
- **任务感知**：在任务执行期间，自动识别并关联来自任务相关设备的告警
- **架构复用**：充分利用现有Handler->Service->DAO架构和alert表结构
- **实用优先**：避免过度设计，专注于核心任务协同监控功能

## 2.2 架构设计

### 2.2.1 详细架构图

```mermaid
graph TB
    subgraph "外部系统"
        NetAuto["网络自动化控制系统"]
    end
    
    subgraph "现有告警系统"
        Prometheus["Prometheus"]
        AlertMgr["Alertmanager"]
        ES["Elasticsearch"]
    end
    
    subgraph "DCI监控系统 (现有架构扩展)"
        subgraph "API层"
            TaskAPI["任务协同API<br/>/api/v1/tasks"]
            WebHookAPI["告警WebHook<br/>/api/v1/alerts/webhook/prometheus"]
        end
        
        subgraph "业务层 (扩展现有Service)"
            AlertService["AlertService<br/>(扩展ProcessPrometheusWebhook)"]
            TaskService["TaskService<br/>(新增)"]
        end
        
        subgraph "数据层"
            AlertDAO["AlertDAO<br/>(现有)"]
            TaskDAO["TaskDAO<br/>(新增)"]
        end
    end
    
    subgraph "存储层"
        MySQL_Alert["MySQL<br/>monitor_alert表<br/>(增加device_id字段)"]
        MySQL_Task["MySQL<br/>monitor_network_auto_task_monitoring_sessions表<br/>(新增)"]
    end
    
    %% 现有告警流程
    Prometheus --> AlertMgr
    AlertMgr -->|WebHook| WebHookAPI
    ES -.->|"日志告警<br/>(暂不开发)"| WebHookAPI
    
    %% 任务协同流程
    NetAuto --> TaskAPI
    TaskAPI --> TaskService
    TaskService --> TaskDAO
    TaskDAO --> MySQL_Task
    
    %% 告警关联流程
    WebHookAPI --> AlertService
    AlertService -->|"设备ID关联逻辑"| TaskDAO
    AlertService --> AlertDAO
    AlertDAO --> MySQL_Alert
    
    %% 样式定义
    classDef external fill:#ff9999
    classDef existing fill:#99ccff
    classDef extended fill:#ffcc99
    classDef new fill:#99ff99
    classDef storage fill:#cc99ff
    
    class NetAuto external
    class Prometheus,AlertMgr,ES existing
    class WebHookAPI,AlertService,AlertDAO extended
    class TaskAPI,TaskService,TaskDAO new
    class MySQL_Alert,MySQL_Task storage
```

### 2.2.2 极简架构图

```mermaid
graph LR
    A["Alertmanager<br/>WebHook"] --> B["现有告警处理<br/>(扩展设备ID关联)"]
    B --> C["MySQL存储<br/>(告警+任务关联)"]
    
    D["网络自动化系统"] --> E["任务协同API<br/>(设备ID列表)"]
    E --> F["任务会话管理"]
    F --> C
    
    B -.->|"基于设备ID关联"| F
```

## 2.3 数据流/流程图

基于现有WebHook架构的任务协同监控流程如下：

```mermaid
sequenceDiagram
    participant NA as 网络自动化系统
    participant API as 任务协同API
    participant TS as TaskService
    participant AM as Alertmanager
    participant WH as WebHook处理器
    participant AS as AlertService
    participant DB as MySQL数据库
    
    %% 任务启动流程
    NA->>API: POST /api/v1/tasks/{id}/monitoring/start<br/>{deviceIds: ["switch001", "switch002"]}
    activate API
    API->>TS: 创建任务监控会话
    activate TS
    TS->>DB: 保存任务会话信息<br/>(task_id, device_ids, start_time)
    TS-->>API: 返回session_id
    deactivate TS
    API-->>NA: 200 OK
    deactivate API
    
    %% 告警处理流程(现有架构扩展)
    AM->>WH: POST /api/v1/alerts/webhook/prometheus<br/>(现有WebHook)
    activate WH
    WH->>AS: ProcessPrometheusWebhook(扩展)
    activate AS
    AS->>AS: 解析告警，提取设备ID
    AS->>TS: 根据设备ID查询活跃任务
    activate TS
    TS->>DB: 查询monitor_network_auto_task_monitoring_sessions
    TS-->>AS: 返回匹配的task_id
    deactivate TS
    AS->>AS: 设置alert.task_id
    AS->>DB: 保存告警到monitor_alert表
    AS-->>WH: 处理完成
    deactivate AS
    WH-->>AM: 200 OK
    deactivate WH
    
    %% 任务结束流程
    NA->>API: POST /api/v1/tasks/{id}/monitoring/stop
    activate API
    API->>TS: 结束任务监控会话
    activate TS
    TS->>DB: 更新会话状态为completed
    TS-->>API: 返回任务告警统计
    deactivate TS
    API-->>NA: 200 OK + 报告摘要
    deactivate API
```

## 2.4 模块化设计

基于现有dcimonitor架构的最小化扩展设计：

- **现有告警模块扩展 (internal/alert)**：
  - 扩展现有AlertService.ProcessPrometheusWebhook方法，增加设备ID关联逻辑
  - 复用现有Alert模型，在monitor_alert表增加task_id字段
  - 保持现有WebHook API接口不变

- **新增任务协同模块 (internal/task)**：
  - TaskService：管理任务监控会话的生命周期
  - TaskDAO：处理monitor_network_auto_task_monitoring_sessions表的数据访问
  - TaskHandler：提供任务协同API接口

- **共享数据模型 (internal/models)**：
  - 扩展现有模型，增加任务相关的请求/响应结构体
  - 保持与现有API规范的一致性

## 2.5 技术选型

基于DCI项目技术栈约束，告警联动架构的技术选型如下：

- **核心语言**：Go 1.24.2，确保与现有系统的兼容性
- **Web框架**：Gin v1.9.1+，提供高性能的API服务
- **消息队列**：Apache Kafka 3.x+，作为事件驱动架构的消息总线
- **数据库**：MySQL 8.0+，存储告警规则和状态信息
- **监控系统**：Prometheus v2.45+，提供指标数据和告警检测
- **日志系统**：Elasticsearch，支持日志数据的全文检索和告警
- **配置管理**：Viper v1.18.2+，支持动态配置加载
- **日志记录**：Zap v1.27.0+，提供结构化日志输出

# 3 详细设计

## 3.1 功能模块

### 3.1.1 现有告警模块扩展

基于现有internal/alert包的最小化功能扩展：

**扩展AlertService.ProcessPrometheusWebhook方法**：
- 在现有告警处理流程中增加设备ID提取逻辑
- 调用TaskService查询活跃的任务监控会话
- 基于设备ID匹配结果设置alert.task_id字段
- 保持现有告警存储和通知逻辑不变

**扩展Alert数据模型**：
- 在现有Alert结构体中增加TaskID字段
- 在monitor_alert表中增加task_id列和相应索引
- 保持其他字段和业务逻辑完全不变

**保持现有API接口**：
- 现有的告警查询API自动支持按task_id过滤
- 现有的WebHook接口无需任何变更
- 现有的告警规则管理功能保持不变

### 3.1.2 告警源集成

基于现有告警架构的告警源处理：

**Prometheus指标告警**：
- 沿用现有Alertmanager WebHook机制
- 通过现有/api/v1/alerts/webhook/prometheus接口接收告警
- 在告警Labels中包含设备标识信息（如device_id, instance等）
- 无需修改现有Prometheus规则配置

**其他告警源【暂不开发】**：
- Elasticsearch日志告警：可通过现有WebHook接口接入
- 拓扑变更告警：通过现有告警模型和API处理
- 所有告警源统一存储到monitor_alert表

### 3.1.3 任务协同监控模块

新增的任务协同监控模块 (internal/task)：

**任务会话管理**：
- TaskService：管理任务监控会话的创建、查询和结束
- 存储任务ID、相关设备ID列表、时间范围等基础信息
- 提供活跃会话查询功能，支持设备ID到任务ID的映射

**时间范围管理详细说明**：
任务会话的时间范围包含以下关键时间点：
- `start_time`：任务开始执行时间，网络自动化系统调用启动API时记录
- `end_time`：任务结束时间，网络自动化系统调用停止API时记录，未结束任务此字段为NULL
- `task_result_created_at`：会话记录创建时间，用于审计和追踪

时间范围的作用：
- **告警关联判断**：包含两个时间段的告警都会被关联到任务
  - 任务执行期间：`start_time`到`end_time`之间的告警
  - 任务结束后持续监测：`end_time`到`end_time + 1小时`之间的告警（标记为任务后续影响）
- **活跃会话识别**：`end_time`为NULL且`status='active'`的会话被认为是活跃会话
- **任务报告生成**：基于完整时间范围（包括1小时延展期）统计任务相关的告警数量、类型和分布

**任务结束后持续监测机制**：
网络自动化变更任务执行完成后，可能存在延迟影响或配置生效滞后导致的告警，因此需要继续监测：
- **监测时长**：任务结束后1小时内（可以配置）
- **关联标识**：这些告警会被标记为"任务后续影响"类型
- **实现逻辑**：
  ```sql
  -- 判断告警是否在任务延展监测期内
  SELECT task_id FROM monitor_network_auto_task_monitoring_sessions 
  WHERE status = 'completed' 
  AND JSON_CONTAINS(device_ids, JSON_QUOTE(?))
  AND end_time IS NOT NULL
  AND ? BETWEEN end_time AND DATE_ADD(end_time, INTERVAL 1 HOUR)
  ```

**设备ID到任务ID映射详细说明**：
活跃会话查询功能的核心实现逻辑：

1. **活跃会话定义**：
   ```sql
   SELECT task_id FROM monitor_network_auto_task_monitoring_sessions 
   WHERE status = 'active' AND end_time IS NULL
   ```

2. **设备关联查询（包含延展监测）**：
   ```sql
   -- 查询活跃任务会话
   SELECT task_id, 'active' as alert_type FROM monitor_network_auto_task_monitoring_sessions 
   WHERE status = 'active' 
   AND JSON_CONTAINS(device_ids, JSON_QUOTE(?)) 
   AND start_time <= NOW()
   
   UNION
   
   -- 查询任务结束后1小时内的延展监测
   SELECT task_id, 'post_task' as alert_type FROM monitor_network_auto_task_monitoring_sessions 
   WHERE status = 'completed' 
   AND JSON_CONTAINS(device_ids, JSON_QUOTE(?))
   AND end_time IS NOT NULL
   AND NOW() BETWEEN end_time AND DATE_ADD(end_time, INTERVAL 1 HOUR)
   ```

3. **映射查询流程**：
   - 从告警Labels中提取设备ID（如`device_id`、`instance`等字段）
   - 在活跃任务会话和延展监测期内查找包含该设备ID的记录
   - 返回匹配的`task_id`和告警类型标识，如无匹配则返回NULL
   - 为告警设置`task_id`字段，并在告警注释中标明是否为任务后续影响

4. **查询优化**：
   - 通过`idx_status_time`索引快速定位活跃会话
   - 利用JSON_CONTAINS函数高效匹配设备ID
   - 内存缓存活跃会话列表，减少数据库查询频率

**设备关联逻辑**：
- 接收告警时，根据设备ID查询当前活跃的任务会话
- 自动为匹配的告警设置task_id字段
- 支持任务结束后的告警统计和报告生成

## 3.2 数据模型

基于现有数据库结构的最小化扩展：

**扩展现有monitor_alert表**：
```sql
-- 在现有表中增加任务关联字段
ALTER TABLE monitor_alert ADD COLUMN device_id VARCHAR(128) NULL COMMENT '设备ID(从labels中提取并存储)';
ALTER TABLE monitor_alert ADD INDEX idx_device_id (device_id);
```

**新增monitor_network_auto_task_monitoring_sessions表**：
```sql
CREATE TABLE monitor_network_auto_task_monitoring_sessions (
    session_id VARCHAR(64) PRIMARY KEY COMMENT '会话唯一标识符',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    device_ids JSON NOT NULL COMMENT '监控设备ID列表',
    start_time TIMESTAMP NOT NULL COMMENT '监控开始时间(Asia/Shanghai)',
    end_time TIMESTAMP NULL COMMENT '监控结束时间(Asia/Shanghai)',
    status ENUM('active', 'completed') NOT NULL DEFAULT 'active' COMMENT '会话状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(Asia/Shanghai)',
    INDEX idx_task_id (task_id),
    INDEX idx_status_time (status, start_time),
    INDEX idx_end_time (end_time) COMMENT '支持延展监测查询优化'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网络自动化协同任务监控会话表';

CREATE TABLE monitor_task_alert_associations (
    id VARCHAR(36) PRIMARY KEY COMMENT '关联记录ID',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    alert_id VARCHAR(36) NOT NULL COMMENT '告警ID(关联monitor_alert.id)',
    association_type ENUM('active','post_task') NOT NULL COMMENT '关联类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '关联时间',
    INDEX idx_task_id (task_id),
    INDEX idx_alert_id (alert_id),
    INDEX idx_association_type (association_type),
    FOREIGN KEY (alert_id) REFERENCES monitor_alert(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务告警关联表';
```

**保持现有表结构**：
- monitor_alert_rule表：无需修改，继续使用现有告警规则管理
- monitor_alert_notification表：无需修改，继续使用现有通知配置

## 3.3 接口设计

基于现有API架构的接口扩展：

**保持现有告警API不变**：
- `GET /api/v1/alerts`：现有告警查询API，自动支持task_id参数过滤
- 现有告警规则CRUD API保持不变
- 现有WebHook接口`POST /api/v1/alerts/webhook/prometheus`保持不变

**新增任务协同API（最小化）**：
```go
// 启动任务监控
POST /api/v1/tasks/{taskId}/monitoring/start
{
    "deviceIds": ["switch001", "switch002"]
}

// 停止任务监控  
POST /api/v1/tasks/{taskId}/monitoring/stop

// 查询任务告警统计
GET /api/v1/tasks/{taskId}/alerts?start_time=xxx&end_time=xxx
```