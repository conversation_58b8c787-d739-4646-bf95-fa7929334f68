---
title: |
  DCI 数据监测系统 - Agent 端与服务端 HTTPS 通信技术设计
subtitle: |
  基于单向 TLS 认证保障 Management Agent 与 dcimonitor Server 之间的安全通信
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明                                               |
| ---- | ---------- | ------ | ------------------------------------------------------ |
| V1.0 | 2025-05-06 | 顾铠羟 | 初始版本，定义单向 HTTPS 通信机制、证书管理策略及安全考虑 |

# 1. 引言

## 1.1 文档目的

本文档详细阐述 DCI-Monitor 项目中 `Management Agent` (MA) 与 `dcimonitor` 服务端之间通过 HTTPS 进行安全通信的技术方案。本方案采用单向 TLS 认证，即服务端提供证书供客户端验证，结合应用层 Token 实现客户端认证。

## 1.2 文档范围

本文档覆盖 Agent 与服务端之间 HTTPS 通信的协议要求、服务器端 TLS 证书管理生命周期（生成、部署）、客户端对服务端证书的验证机制、应用层认证方式以及相关的部署和安全运维考虑。由于证书有效期设置较长，证书续期暂不作为主要考虑范围。

# 2. 设计原则

## 2.1 强制 HTTPS 通信

MA 与 `dcimonitor` 服务端 (`MgmtAPI`) 之间的所有通信均强制使用 HTTPS 协议，保障数据在传输过程中的机密性和完整性。

## 2.2 服务器端 TLS 认证

`dcimonitor` 服务端配置有效的 TLS 证书。MA 在建立连接时，验证服务端的证书是由其信任的 CA 签发，且证书有效（未过期、未吊销、主机名匹配），以此确认服务端的身份。

## 2.3 应用层认证 (Bearer Token)

在 TLS 提供的传输层安全和服务器身份认证基础上，采用 `06-数据监测系统-Agent端技术设计.md` 中定义的 Bearer Token 机制进行应用层客户端认证。MA 在每个 HTTPS 请求的 `Authorization` Header 中携带预置的 `agent_token`，服务端据此验证 MA 的合法性。

## 2.4 不采用双向 TLS 认证 (mTLS)

本设计不采用双向 TLS 认证。MA 无需持有和提供客户端证书给服务端进行验证，简化 Agent 端的证书管理和部署。

# 3. TLS 证书管理 (服务器端)

为支持服务端 HTTPS，建立一套服务器端 TLS 证书管理流程，使用内部私有 CA (Certificate Authority)。

## 3.1 私有 CA 建立与管理

*   **工具**: 使用 `openssl` 命令行工具建立和管理私有 CA。

*   **根证书**: 生成 CA 根证书 (`ca.crt`) 及其私钥 (`ca.key`)。CA 私钥的安全存储至关重要，访问权限严格控制。

*   **证书有效期**: 设置长时间 CA 根证书及后续签发服务端证书的有效期（CA 根证书 50 年，服务端证书 30 年）。

## 3.2 服务端证书生成与部署

1.  **生成私钥**: 为 `dcimonitor` 服务端生成私钥 (`server.key`)。

2.  **生成证书签名请求 (CSR)**: 创建包含服务端标识信息（如 FQDN 或 IP 地址）的 CSR (`server.csr`)。

3.  **签发证书**: 使用私有 CA 的根证书和私钥签发服务端 TLS 证书 (`server.crt`)。证书的 `Subject Alternative Name` (SAN) 字段包含所有 MA 可能用于访问服务端的地址。

4.  **部署**: 将服务端证书 (`server.crt`) 和私钥 (`server.key`) 安全地部署到运行 `dcimonitor` 服务的所有实例上，配置 Web 服务器（如 Nginx 或 Go 内建 `net/http`）使用这些文件启用 HTTPS。

## 3.3 Agent 端 CA 证书部署与配置

*   **部署**: 将私有 CA 的根证书 (`ca.crt`) 分发并部署到所有 Agent 节点。

*   **配置**: MA 的 HTTP 客户端配置为信任此 `ca.crt`。在建立 HTTPS 连接时，MA 使用此 CA 证书验证从服务端接收到的 `server.crt` 的有效性。Go 语言中通过 `crypto/tls.Config` 的 `RootCAs` 字段加载 CA 证书。

## 3.4 证书续期（暂不考虑）

由于证书有效期设置较长，当前设计暂不包含证书自动续期流程。若未来有需要，需另行设计。

# 4. HTTPS 通信协议细节

## 4.1 TLS 版本

*   强制使用安全的 TLS 版本，最低要求 TLS 1.2，启用 TLS 1.3。

*   禁用已知不安全的 SSLv3 及更早版本。

## 4.2 密码套件 (Cipher Suites)

*   配置服务端优先选择并仅支持强密码套件。

*   移除包含已知漏洞（如 NULL, ADH, MD5, RC4 等）的套件。

*   使用具备前向保密 (Forward Secrecy) 特性的密码套件（如基于 ECDHE 的套件）。

## 4.3 HTTP Header 安全

*   服务端响应中包含安全 Header，例如：

    *   `Strict-Transport-Security` (HSTS)
    *   `Content-Security-Policy` (CSP) (若有 Web UI)
    *   `X-Frame-Options`
    *   `X-Content-Type-Options`

# 5. 安全考虑

## 5.1 CA 私钥安全

私有 CA 的根私钥是信任链的基础，其泄露将危及所有由该 CA 签发的证书。采取严格的安全措施保护 CA 私钥（如离线存储、HSM、访问控制）。

## 5.2 证书吊销

*   建立证书吊销机制是必要的安全实践。若服务端证书私钥泄露，及时吊销该证书。

*   使用证书吊销列表 (CRL) 或在线证书状态协议 (OCSP)。MA 在验证服务端证书时检查其吊销状态。CRL/OCSP 会增加系统复杂性，实施时需评估。

## 5.3 Bearer Token 安全

*   客户端认证依赖 Bearer Token，Token 的生成、分发、存储和传输必须安全进行。

*   Token 具有一定的有效期，并支持撤销。

*   防止 Token 泄露，HTTPS 是基础保障。

## 5.4 中间人攻击 (MitM) 防护

*   MA **强制验证**服务端证书的有效性（信任链、主机名匹配、有效期、吊销状态），这是防御 MitM 攻击的关键。

*   不采用证书固定 (Certificate Pinning)，以简化证书轮换管理。

# 6. 部署与运维

## 6.1 自动化部署

*   服务端证书的生成、签发和部署过程自动化（如使用配置管理工具或脚本）。

*   将 CA 根证书 (`ca.crt`) 集成到 MA 的部署包或配置流程中。

*   将服务端证书 (`server.crt`) 和私钥 (`server.key`) 集成到 `dcimonitor` 服务的部署流程中。

## 6.2 配置更新

*   服务端更新 TLS 证书后，需要重新加载服务配置或重启服务。

*   Agent 端在初始部署时配置信任的 CA 证书。

## 6.3 监控与告警

*   建立对服务端 TLS 证书有效期的监控，并在到期前告警。

*   监控 HTTPS 服务的可用性和 TLS 握手成功率。

---
