---
title: DCI数据监测系统-代码架构设计与开发计划
version: 1.0
date: 2025-05-05
---

# 版本记录

| 版本 | 日期       | 作者 | 修订说明             |
| ---- | ---------- | ---- | -------------------- |
| V1.0 | 2025-05-06 | 顾铠羟 | 初始版本         |

# 1. 引言

## 1.1. 文档目的
本文档旨在详细阐述 DCI 数据监测系统 (dcimonitor) 的代码层面架构设计，明确各模块职责、接口定义、技术选型，并制定详细的开发计划与时间表，作为后续开发工作的指导和依据。

## 1.2. 文档范围
本文档涵盖 dcimonitor 服务端的核心代码架构设计，包括但不限于：
- 整体架构概览
- 核心模块划分与职责
- 主要接口设计（内部与外部）
- 技术栈选型与理由
- 代码目录结构
- 编码规范与最佳实践
- 开发计划、里程碑与任务分配（待定）
- 风险识别与应对策略

## 1.3. 参考文档
- `documents/designs/analysis/01-DCI-DCI数据监测系统项目分析报告.md`
- `documents/designs/analysis/02-DCI数据监测系统-数据库设计.md`
- `documents/designs/analysis/04-DCI-Kafka主题规划及负载均衡连接设计.md`
- `documents/designs/analysis/05-DCI-TDengine数据模型设计.md` (待创建)
- `documents/designs/analysis/06-DCI-Elasticsearch索引设计.md` (待创建)

# 2. 系统架构设计

## 2.1. 架构概览
(简述系统整体架构，例如采用微服务、单体+模块化等，绘制高层架构图)
- **核心原则:** 高内聚、低耦合、可扩展、可观测
- **部署架构:** 基于 Kubernetes (K8S) 部署

```mermaid
graph TD
    subgraph "外部系统/用户"
        User(用户/管理系统)
        Agent(DCI Agent)
        NetAuto(网络自动化系统)
        CloudMan(多云管理系统)
    end

    subgraph "DCI Monitor 服务端 (K8S)"
        APIGW(API 网关/入口)
        AuthN(认证服务)
        AuthZ(授权服务 - Tenant Map)

        subgraph "核心服务"
            AgentSvc(Agent管理服务 - 注册/心跳/配置)
            TopologySvc(拓扑处理服务)
            TaskMonSvc(任务监控服务)
            ReportSvc(报告生成/查询服务)
            DataApiSvc(数据供应 API 服务)
            DataIngestSvc(数据写入服务 - TDengine/ES)
        end

        subgraph "基础设施/中间件"
            Kafka(Kafka 集群)
            MySQL(MySQL 数据库)
            TDengine(TDengine 集群)
            Elasticsearch(Elasticsearch 集群)
            Redis(Redis 缓存 - 可选)
        end
    end

    User --> APIGW
    NetAuto -- Kafka: Task Control --> TaskMonSvc
    Agent -- HTTP/gRPC --> APIGW
    APIGW --> AuthN
    APIGW -- Authenticated --> AgentSvc & DataApiSvc & ReportSvc
    AgentSvc --> MySQL
    AgentSvc --> Kafka
    Agent -- Kafka: LLDP/Metrics/Logs --> Kafka
    Kafka -- Topic: Topology --> TopologySvc
    Kafka -- Topic: Metrics --> DataIngestSvc
    Kafka -- Topic: Logs/Events --> DataIngestSvc
    Kafka -- Topic: Task Status --> TaskMonSvc
    TopologySvc -- Processed Data --> CloudMan 
    TopologySvc --> MySQL
    TaskMonSvc --> MySQL
    TaskMonSvc -- Trigger --> ReportSvc
    ReportSvc --> MySQL
    ReportSvc --> TDengine & Elasticsearch 
    DataIngestSvc --> TDengine
    DataIngestSvc --> Elasticsearch
    DataApiSvc --> AuthZ 
    DataApiSvc -- Authorized --> TDengine & Elasticsearch & MySQL
    AuthZ --> MySQL

    %% 服务间交互简化表示，实际可能更复杂
    AgentSvc <--> MySQL
    TaskMonSvc <--> MySQL
    ReportSvc <--> MySQL
    AuthZ <--> MySQL
    DataIngestSvc --> TDengine & Elasticsearch
    DataApiSvc --> TDengine & Elasticsearch & MySQL

```

## 2.2. 技术栈选型
- **后端语言:** Go (Gin Framework) - 高性能、并发友好、生态完善
- **数据库:**
    - MySQL: 存储关系型数据（Agent注册、配置、任务元数据、报告元数据、租户映射）
    - TDengine: 存储时序指标数据
    - Elasticsearch: 存储日志、事件数据，提供搜索能力
- **消息队列:** Kafka - 高吞吐量、解耦、削峰填谷
- **配置管理:** Viper
- **日志框架:** Zap
- **容器编排:** Kubernetes (K8S)
- **API 网关:** Nginx Ingress (暂未配置)
- **认证授权:** JWT

## 2.3. 服务端核心模块划分
(详细描述每个服务的职责)
- **`cmd/`**: 程序入口，服务启动与初始化。
    - `server.go`: 主要服务启动逻辑。
- **`internal/`**: 内部实现，不对外暴露。
    - **`config/`**: 配置加载与管理。
    - **`models/`**: 数据库/业务模型定义 (e.g., Agent, Task, Report)。
    - **`services/`**: 核心业务逻辑实现。
        - `agent_service.go`: 处理 Agent 注册、心跳、配置下发逻辑。
        - `topology_processor.go`: 处理拓扑数据。
        - `task_monitor_service.go`: 监控自动化任务。
        - `report_generator_service.go`: 生成和管理报告。
        - `tenant_api_service.go`: 提供面向租户的数据查询 API。**此服务包含授权逻辑，使用 `monitor_tenant_map` 过滤数据。**
        - `data_ingest_service.go`: (可能是一个或多个服务) 处理 Kafka 数据写入 TDengine 和 ES。
        - `auth_service.go`: (可能拆分) 处理认证逻辑。
    - **`handler/` / `api/`**: API 请求处理层 (HTTP/gRPC handlers)。
        - 路由定义、请求参数校验、调用 Service 层。
    - **`middleware/`**: 中间件 (认证、授权、日志、限流等)。
        - `auth.go`
        - `tenant_auth.go`: **实现基于 `monitor_tenant_map` 的租户授权检查逻辑。**
    - **`store/` / `dao/`**: 数据访问层，封装数据库操作。
        - `mysql/`
        - `tdengine/`
        - `elasticsearch/`
    - **`utils/`**: 通用工具函数 (e.g., kafka client, validation, crypto)。
- **`pkg/`**: 可对外共享的库/包。
    - `logger/`: 日志库封装。
    - `kafka/`: Kafka 生产者/消费者通用组件。
    - `cronjobs/`: 定时任务。
- **`api/` / `proto/`**: API 定义文件 (OpenAPI spec / Protocol Buffers)。
- **`configs/`**: 配置文件示例或模板。
- **`scripts/`**: 构建、部署等辅助脚本。
- **`