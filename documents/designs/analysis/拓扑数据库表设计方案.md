# 拓扑数据库表设计方案

## 1. 概述

本文档详细描述DCI数据监测系统中拓扑数据相关的数据库表设计。这些表用于存储网络设备基础信息、端口信息、标识符映射关系、LLDP邻居关系以及计算得到的拓扑数据，为拓扑数据处理和展示提供数据基础。

## 2. 设计原则

1. **完整性**：确保能够完整记录设备信息、端口信息和拓扑关系数据
2. **一致性**：保持数据一致，避免冗余和矛盾
3. **可扩展性**：表结构设计考虑未来功能扩展需求
4. **性能优化**：合理设置索引，优化查询性能
5. **数据追溯**：保留时间戳信息，支持历史数据查询和变更追踪

## 3. 表结构设计

### 3.1 设备和端口基础信息表

#### 3.1.1 设备信息表 (monitor_device_info)

存储网络设备的基本信息，为拓扑节点提供设备层面的数据支持。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | varchar(36) | PK, NOT NULL | 设备ID，UUID格式 |
| device_name | varchar(255) | NOT NULL | 设备名称 |
| device_type | varchar(50) | NULL | 设备类型 |
| manage_ip | varchar(45) | NULL | 管理IP地址 |
| chassis_id | varchar(50) | NULL | 设备底盘ID（通常是MAC地址） |
| vendor | varchar(100) | NULL | 设备厂商 |
| model | varchar(100) | NULL | 设备型号 |
| status | tinyint | NOT NULL, DEFAULT 1 | 状态：1=正常，0=禁用 |
| last_seen | timestamp | NULL | 最后一次发现时间 |
| created_by | varchar(50) | NOT NULL | 创建者 |
| create_time | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| update_time | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**：
- 主键：`id`
- 唯一键：`uk_device_name`（设备名称）
- 普通索引：`idx_manage_ip`（管理IP）, `idx_chassis_id`（底盘ID）

#### 3.1.2 设备端口信息表 (monitor_device_port_info)

存储设备端口的详细信息，为拓扑边提供端口层面的数据支持。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | varchar(36) | PK, NOT NULL | 记录ID，UUID格式 |
| device_id | varchar(36) | NOT NULL | 设备ID |
| if_index | int | NOT NULL | 接口索引号(ifIndex) |
| port_name | varchar(255) | NOT NULL | 端口名称，如"10GE1/0/1" |
| port_desc | varchar(512) | NULL | 端口描述 |
| port_type | varchar(50) | NULL | 端口类型 |
| admin_status | tinyint | NULL | 管理状态: 1=up, 2=down |
| oper_status | tinyint | NULL | 运行状态: 1=up, 2=down |
| mac_address | varchar(17) | NULL | 端口MAC地址 |
| speed | bigint | NULL | 端口速率(bps) |
| mtu | int | NULL | 最大传输单元(字节) |
| is_physical | tinyint(1) | DEFAULT 1 | 是否物理端口: 1=是, 0=否(逻辑接口) |
| is_lldp_enabled | tinyint(1) | NULL | 是否启用LLDP: 1=是, 0=否 |
| last_updated | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 最后更新时间 |
| raw_data | json | NULL | 原始采集数据，JSON格式 |
| created_by | varchar(50) | NOT NULL | 创建者 |
| create_time | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| update_time | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**：
- 主键：`id`
- 唯一键：`uk_device_ifindex`（设备ID和端口索引）
- 普通索引：`idx_device_id`（设备ID）, `idx_last_updated`（最后更新时间）, `idx_port_name`（端口名称）, `idx_mac_address`（MAC地址）

### 3.2 标识符映射相关表

#### 3.2.1 设备标识符映射表 (monitor_device_identifier_mapping)

存储不同类型标识符到系统设备ID的映射关系，用于标识符解析和冲突处理。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | varchar(36) | PK, NOT NULL | 记录ID，UUID格式 |
| device_id | varchar(36) | NOT NULL | 系统中的标准设备ID |
| identifier_type | varchar(50) | NOT NULL | 标识符类型：name(设备名称)、chassis_id(机箱ID/MAC地址)、ip(管理IP)等 |
| identifier_value | varchar(255) | NOT NULL | 标识符值 |
| priority | int | NOT NULL, DEFAULT 0 | 标识符优先级，数值越小优先级越高，用于解决冲突 |
| is_active | tinyint(1) | NOT NULL, DEFAULT 1 | 是否活跃：1=活跃，0=不活跃 |
| last_seen | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 最后一次发现时间 |
| created_by | varchar(50) | NOT NULL | 创建者 |
| create_time | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| update_time | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**：
- 主键：`id`
- 唯一键：`uk_type_value`（标识符类型和值）
- 普通索引：`idx_device_id`（设备ID）, `idx_last_seen`（最后发现时间）

#### 3.2.2 端口标识符映射表 (monitor_port_identifier_mapping)

存储不同类型端口标识符到系统端口ID的映射关系，用于端口标识符解析和冲突处理。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | varchar(36) | PK, NOT NULL | 记录ID，UUID格式 |
| device_id | varchar(36) | NOT NULL | 所属设备ID |
| port_id | varchar(36) | NOT NULL | 系统中的标准端口ID |
| identifier_type | varchar(50) | NOT NULL | 标识符类型：name(端口名称)、index(ifIndex)、desc(描述)等 |
| identifier_value | varchar(255) | NOT NULL | 标识符值 |
| priority | int | NOT NULL, DEFAULT 0 | 标识符优先级，数值越小优先级越高 |
| is_active | tinyint(1) | NOT NULL, DEFAULT 1 | 是否活跃：1=活跃，0=不活跃 |
| last_seen | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 最后一次发现时间 |
| created_by | varchar(50) | NOT NULL | 创建者 |
| create_time | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| update_time | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**：
- 主键：`id`
- 唯一键：`uk_device_type_value`（设备ID、标识符类型和值）
- 普通索引：`idx_port_id`（端口ID）, `idx_last_seen`（最后发现时间）

### 3.3 LLDP相关表

#### 3.3.1 LLDP邻居关系缓存表 (monitor_lldp_neighbor_cache)

临时存储原始LLDP邻居关系信息，避免重复处理，同时保留原始数据便于问题排查。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | varchar(36) | PK, NOT NULL | 记录ID，UUID格式 |
| local_device_identifier | varchar(255) | NOT NULL | 本地设备标识符 |
| local_device_id_type | varchar(50) | NOT NULL | 本地设备标识符类型 |
| local_port_index | varchar(50) | NOT NULL | 本地端口索引 |
| remote_chassis_id | varchar(255) | NOT NULL | 远端设备机箱ID |
| remote_port_id | varchar(255) | NOT NULL | 远端设备端口ID |
| remote_sysname | varchar(255) | NULL | 远端设备名称 |
| mapped_local_device_id | varchar(36) | NULL | 映射后的本地设备ID |
| mapped_local_port_id | varchar(36) | NULL | 映射后的本地端口ID |
| mapped_remote_device_id | varchar(36) | NULL | 映射后的远端设备ID |
| mapped_remote_port_id | varchar(36) | NULL | 映射后的远端端口ID |
| mapping_status | varchar(20) | NOT NULL, DEFAULT 'PENDING' | 映射状态：PENDING(待处理)、MAPPED(已映射)、PARTIAL(部分映射)、FAILED(映射失败) |
| raw_data | json | NULL | 原始LLDP数据，JSON格式 |
| last_seen | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 最后一次发现时间 |
| error_message | varchar(512) | NULL | 映射失败时的错误信息 |
| created_by | varchar(50) | NOT NULL | 创建者 |
| create_time | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| update_time | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**：
- 主键：`id`
- 唯一键：`uk_local_remote`（本地设备标识符、本地端口索引、远端机箱ID、远端端口ID）
- 普通索引：`idx_local_device`（本地设备标识符）, `idx_remote_chassis`（远端机箱ID）, `idx_last_seen`（最后发现时间）, `idx_mapping_status`（映射状态）

### 3.4 拓扑相关表

#### 3.4.1 拓扑快照表 (monitor_topology_snapshot)

存储拓扑图的快照信息，支持多个快照版本的管理。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | varchar(36) | PK, NOT NULL | 快照ID，UUID格式 |
| name | varchar(255) | NOT NULL | 快照名称 |
| description | varchar(1000) | NULL | 快照描述 |
| node_count | int | NOT NULL, DEFAULT 0 | 节点数量 |
| edge_count | int | NOT NULL, DEFAULT 0 | 边数量 |
| is_current | tinyint(1) | NOT NULL, DEFAULT 0 | 是否为当前活动快照 |
| created_by | varchar(50) | NOT NULL | 创建者 |
| create_time | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| update_time | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**：
- 主键：`id`
- 普通索引：`idx_is_current`（是否当前活动）, `idx_create_time`（创建时间）

#### 3.4.2 拓扑节点表 (monitor_topology_node)

存储拓扑图中的节点信息，即网络设备。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | varchar(36) | PK, NOT NULL | 节点ID，UUID格式 |
| snapshot_id | varchar(36) | NOT NULL | 关联的快照ID |
| device_id | varchar(36) | NOT NULL | 设备ID |
| node_type | varchar(50) | NOT NULL | 节点类型 |
| properties | json | NULL | 节点属性JSON |
| created_by | varchar(50) | NOT NULL, DEFAULT 'system' | 创建者 |
| create_time | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| update_time | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**：
- 主键：`id`
- 普通索引：`idx_snapshot_id`（快照ID）, `idx_device_id`（设备ID）
- 外键：`fk_node_snapshot` 引用 `monitor_topology_snapshot(id)`，级联删除

#### 3.4.3 拓扑边表 (monitor_topology_edge)

存储拓扑图中的连接关系，即设备间的链接。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | varchar(36) | PK, NOT NULL | 边ID，UUID格式 |
| snapshot_id | varchar(36) | NOT NULL | 关联的快照ID |
| source_node_id | varchar(36) | NOT NULL | 源节点ID |
| target_node_id | varchar(36) | NOT NULL | 目标节点ID |
| source_port_id | varchar(36) | NULL | 源端口ID |
| target_port_id | varchar(36) | NULL | 目标端口ID |
| edge_type | varchar(50) | NOT NULL | 边类型 |
| properties | json | NULL | 边属性JSON |
| created_by | varchar(50) | NOT NULL, DEFAULT 'system' | 创建者 |
| create_time | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| update_time | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**：
- 主键：`id`
- 普通索引：`idx_snapshot_id`（快照ID）, `idx_source_node_id`（源节点ID）, `idx_target_node_id`（目标节点ID）
- 外键：`fk_edge_snapshot` 引用 `monitor_topology_snapshot(id)`，级联删除

## 4. 表关系设计

### 4.1 实体关系图

```
+------------------------+      +----------------------------+
| monitor_device_info    |      | monitor_device_port_info   |
+------------------------+      +----------------------------+
| PK: id                 |<--+  | PK: id                     |
| device_name            |   |  | FK: device_id              |
| ...                    |   |  | if_index                   |
+------------------------+   |  | port_name                  |
                             |  | ...                        |
+----------------------------+  +----------------------------+
| monitor_device_identifier_|      |
| _mapping                  |      |
+----------------------------+      |
| PK: id                    |      |
| FK: device_id             |------+
| identifier_type           |      |
| identifier_value          |      |
| ...                       |      |
+----------------------------+      |
                                   |
+----------------------------+     |
| monitor_port_identifier_   |     |
| _mapping                   |     |
+----------------------------+     |
| PK: id                     |     |
| FK: device_id              |-----+
| FK: port_id                |<-+
| identifier_type            |  |
| identifier_value           |  |
| ...                        |  |
+----------------------------+  |
                                |
                                |
+----------------------------+  |
| monitor_lldp_neighbor_cache|  |
+----------------------------+  |
| PK: id                     |  |
| local_device_identifier    |  |
| local_port_index           |  |
| remote_chassis_id          |  |
| remote_port_id             |  |
| mapped_local_device_id     |--+
| mapped_local_port_id       |-+
| mapped_remote_device_id    ||
| mapped_remote_port_id      ||
| ...                        ||
+----------------------------+|
                              |
+------------------------+    |
| monitor_topology_      |    |
| snapshot               |    |
+------------------------+    |
| PK: id                 |    |
| name                   |    |
| ...                    |    |
+------------------------+    |
       |                      |
       |                      |
+------v-----------------+    |
| monitor_topology_node  |    |
+------------------------+    |
| PK: id                 |    |
| FK: snapshot_id        |    |
| FK: device_id          |----+
| ...                    |
+------------------------+
       |
       |
+------v-----------------+
| monitor_topology_edge  |
+------------------------+
| PK: id                 |
| FK: snapshot_id        |
| FK: source_node_id     |
| FK: target_node_id     |
| FK: source_port_id     |------+
| FK: target_port_id     |------+
| ...                    |
+------------------------+
```

### 4.2 主要外键关系

虽然在SQL创建语句中，外键约束被注释掉了（为了灵活性），但逻辑上存在以下外键关系：

1. `monitor_device_port_info.device_id` -> `monitor_device_info.id`
2. `monitor_device_identifier_mapping.device_id` -> `monitor_device_info.id`
3. `monitor_port_identifier_mapping.device_id` -> `monitor_device_info.id`
4. `monitor_port_identifier_mapping.port_id` -> `monitor_device_port_info.id`
5. `monitor_topology_node.snapshot_id` -> `monitor_topology_snapshot.id`
6. `monitor_topology_edge.snapshot_id` -> `monitor_topology_snapshot.id`
7. `monitor_topology_node.device_id` -> `monitor_device_info.id`
8. `monitor_topology_edge.source_node_id` -> `monitor_topology_node.id`
9. `monitor_topology_edge.target_node_id` -> `monitor_topology_node.id`
10. `monitor_topology_edge.source_port_id` -> `monitor_device_port_info.id`
11. `monitor_topology_edge.target_port_id` -> `monitor_device_port_info.id`

## 5. 数据流转设计

### 5.1 端口信息处理流程

1. Telegraf采集设备端口信息（SNMP）
2. 数据通过Kafka传输到TopologyProcessor
3. TopologyProcessor解析数据，获取设备ID、端口索引和端口名称等信息
4. 将数据写入`monitor_device_port_info`表
5. 更新`monitor_port_identifier_mapping`表中的端口标识符映射

### 5.2 LLDP邻居信息处理流程

1. Telegraf采集LLDP邻居信息（SNMP）
2. 数据通过Kafka传输到TopologyProcessor
3. TopologyProcessor解析数据，提取本地设备、本地端口、远端设备和远端端口信息
4. 将原始数据存入`monitor_lldp_neighbor_cache`表
5. 执行标识符转换，更新cache表中的mapped_*字段
6. 基于转换后的数据构建拓扑关系
7. 创建或更新拓扑快照（`monitor_topology_snapshot`）
8. 写入拓扑节点数据（`monitor_topology_node`）
9. 写入拓扑边数据（`monitor_topology_edge`）

## 6. 数据维护策略

### 6.1 数据清理策略

1. `monitor_lldp_neighbor_cache`表：建议保留30天数据，定期清理过期数据
2. 拓扑快照数据：建议保留历史快照，但可设置最大快照数量（如保留最近10个）
3. 端口信息表：当设备删除时级联删除相关记录
4. 标识符映射表：当标识符超过一定时间未更新（如90天）时，可将`is_active`设为0

### 6.2 数据一致性维护

1. 设备和端口信息的更新应通过事务操作确保一致性
2. 拓扑快照的创建应使用事务确保节点和边数据的一致性
3. 设备重命名或IP变更时，应更新相关标识符映射表
4. 定期执行数据一致性检查，修复可能的数据不一致问题

## 7. 性能优化建议

1. 对高频查询路径（如标识符转换）添加适当的索引
2. 对大表（如`monitor_lldp_neighbor_cache`）考虑分区策略
3. JSON字段（如`properties`）使用有选择性的索引
4. 考虑使用读写分离策略，提高查询性能
5. 对历史数据考虑归档策略，减少活跃数据量 