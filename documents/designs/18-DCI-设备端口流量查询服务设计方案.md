# 设备端口流量查询服务设计方案

## 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-31 | 顾铠羟 | 初始版本           |
| V1.1 | 2025-06-05 | 顾铠羟 | 精简文档内容，消除重复，增加引用 |
| V1.2 | 2025-06-20 | 顾铠羟 | 更新Prometheus实时查询实现，支持VNI查询参数 |
| V1.3 | 2025-06-06 | Gemini | 根据评审意见修订：明确支持A-Z链路和单端口两种API；修正技术实现细节。 |

## 1. 设计背景与目标

### 1.1 背景介绍

DCI数据监测系统需要为多云管理系统提供基于设备ID、物理端口号和VNI组合的流量查询功能。系统通过SNMP协议采集交换机流量数据并存储在Prometheus中，需要设计一个统一的查询服务将设备和端口映射关系与Prometheus中的流量数据关联起来,实现精准的流量查询。

### 1.2 主要目标

- **改造现有A-Z链路查询服务**：在为多云系统提供服务的现有A-Z链路流量查询API (`/api/v1/traffic/*`) 基础上，增加对VNI参数的支持，并将后端数据源从模拟数据切换为真实的Prometheus流量数据。
- **新增单端口查询服务**：为满足更灵活的查询需求，新增独立的单端口流量查询API，支持基于`设备ID`、`物理端口号`和可选的`VNI`进行查询。
- **建立统一的映射与查询机制**：后端建立统一的`设备/端口/VNI`到Prometheus指标标签的映射查询机制，确保数据准确。
- **确保性能与集成便利性**：确保查询性能满足实时监控需求，并为两套API提供标准、清晰的RESTful接口。

## 1.3 文档关联

- 《00-DCI 数据监测系统项目文档路书.md》：项目总体路书
- 《02-网络自动化平台-数据监测系统技术概要设计.md》：系统总体架构
- 《19-DCI-流量类数据接收及存储技术设计.md》：流量数据处理与存储详细设计
- 《20-DCI-MySql数据库同步-设备端口VNI基础信息.md》：数据库表结构及同步机制

## 2. 系统架构设计

### 2.1 整体架构

流量查询服务将作为 `dcimonitor` 的核心功能进行增强和扩展，而非创建一个独立的全新服务。我们将直接在现有代码结构上进行开发，以确保与日志、配置、中间件等现有基础设施的无缝集成。

其实现将遵循 `dcimonitor` 现有的分层架构：
- **API层 (`monitors`)**: 扩展现有的 `traffic_monitor.go` 以支持A-Z链路API的改造，并创建新的 `single_port_monitor.go` (暂定名) 来处理新增的单端口查询API。
- **服务层 (`services`)**: 设计新的服务来处理端口映射和Prometheus查询的业务逻辑。
- **数据访问层**: 设计DAO (Data Access Object) 来封装对MySQL（端口映射关系）和Prometheus（流量指标）的直接访问。

### 2.2 核心组件设计

#### 2.2.1 API层

API层负责处理所有与流量查询相关的HTTP请求，解析并校验参数，调用服务层处理业务逻辑，最后将结果格式化为JSON响应。

#### 2.2.2 服务层

服务层是核心业务逻辑的实现，负责：
1.  接收API层传递的查询参数（A-Z链路参数或单端口参数）。
2.  调用数据访问层，从MySQL中查询设备、端口和VNI的映射关系，获取用于查询Prometheus的`if_index`等标签。
3.  根据映射关系和查询参数，构建PromQL查询语句。
4.  调用数据访问层，从Prometheus中获取流量数据（累计值或时间序列）。
5.  对原始数据进行计算（如使用`rate()`或`increase()`）和格式化，组合成API响应所需的数据模型。

#### 2.2.3 数据访问层

数据访问层分为MySQL数据访问和Prometheus数据访问两部分。

##### MySQL数据访问

负责从 `dci_monitor` 数据库查询设备、端口及VNI的映射关系，为后续在Prometheus中查询流量提供必要的`label`（如 `if_index`）。

```go
// PortMappingDAO 端口映射数据访问接口
type PortMappingDAO interface {
    // GetPortMappingInfo 根据设备ID、端口名和VNI，查询其对应的if_index
    GetPortMappingInfo(ctx context.Context, deviceID, portName, vni string) (ifIndex string, err error)
}
```

##### Prometheus数据访问

负责执行PromQL查询，从Prometheus中获取原始的流量计数器数据。

```go
// TrafficDAO 流量数据访问接口
type TrafficDAO interface {
    // QueryRate 执行一个rate()查询，返回单个浮点数结果（速率）
    QueryRate(ctx context.Context, query string, t time.Time) (float64, error)
    
    // QueryIncrease 执行一个increase()查询，返回单个浮点数结果（增量）
    QueryIncrease(ctx context.Context, query string, t time.Time) (float64, error)
    
    // QueryVectorOverTime 执行一个范围查询，返回一个时间序列
    QueryVectorOverTime(ctx context.Context, query string, start, end time.Time, step time.Duration) ([]models.TimeValuePair, error)
}
```

## 3. 关键技术实现

本章节将详细阐述A-Z链路查询和单端口查询两种场景下的核心实现逻辑，包括参数验证、设备IP获取和流量计算。

### 3.1 参数验证与设备IP获取 (MySQL)

所有流量查询的第一步都是验证API传入的业务标识（`deviceID`, `portID`, `vni`）的合法性，并获取后续查询Prometheus所需的`设备IP`。

根据最新的设计，MySQL的核心职责是**验证**这组参数是否构成一个有效的、存在于系统中的逻辑端口，并**返回该端口所在设备的管理IP**。

```go
// GetDeviceIPForPort 实现了参数验证和设备IP获取的逻辑
func (d *PortMappingDAOImpl) GetDeviceIPForPort(ctx context.Context, deviceID, portID, vni string) (string, error) {
    // 该查询通过JOIN验证了 device, logic_port_device 和 node_business 之间基于ID和VNI的关联关系
    query := `
        SELECT d.device_management_ip
        FROM dci_logic_port_device AS lpd
        JOIN dci_device AS d ON lpd.device_id = d.id
        JOIN dci_node_business AS nb ON lpd.logic_port_id = nb.logic_port_id
        WHERE d.id = ? AND lpd.logic_port_id = ? AND nb.vni = ?
        LIMIT 1;
    `
    args := []interface{}{deviceID, portID, vni}
    
    var deviceIP string
    err := d.db.QueryRowContext(ctx, query, args...).Scan(&deviceIP)
    if err != nil {
        if err == sql.ErrNoRows {
            // 如果没有找到行，说明这组参数的组合是无效的
            return "", fmt.Errorf("验证失败：提供的device_id, port_id和vni组合不匹配或不存在")
        }
        return "", fmt.Errorf("数据库查询失败: %w", err)
    }

    if deviceIP == "" {
        return "", fmt.Errorf("数据不一致：找到了匹配的端口，但其设备IP为空")
    }
    
    return deviceIP, nil
}
```

### 3.2 流量查询与计算实现 (Prometheus)

获取到 `device_ip` 后，服务层将构建PromQL查询语句来计算速率和总流量。

#### 3.2.1 核心PromQL查询

Prometheus中的流量指标必须包含 `device_ip` 和 `vni` 标签才能支持此查询。

- **计算平均速率 (bps)**: 使用 `rate()` 函数计算每秒字节数，再乘以8得到比特率。
  `rate(dci_snmp_flow_in_octets{device_ip="...", vni="..."}[5m]) * 8`
- **计算总流量 (Bytes)**: 使用 `increase()` 函数计算时间窗口内的字节数增量。
  `increase(dci_snmp_flow_in_octets{device_ip="...", vni="..."}[5m])`

#### 3.2.2 A-Z链路查询实现逻辑

此场景服务于现有的 `/api/v1/traffic/*` 接口。

```go
// GetAZLinkTraffic 实现A-Z链路流量查询的核心逻辑
func (s *TrafficServiceImpl) GetAZLinkTraffic(ctx context.Context, params *models.AZLinkQueryParams) (*models.TrafficChartResponse, error) {
    // 1. 获取A端if_index
    a_ifIndex, err := s.mappingDAO.GetPortMappingInfo(ctx, params.ASwitchID, params.APortID, params.VNI)
    if err != nil {
        return nil, fmt.Errorf("A端端口映射失败: %w", err)
    }
    
    // 2. 获取Z端if_index
    z_ifIndex, err := s.mappingDAO.GetPortMappingInfo(ctx, params.ZSwitchID, params.ZPortID, params.VNI)
    if err != nil {
        return nil, fmt.Errorf("Z端端口映射失败: %w", err)
    }

    // 3. 并发查询A、Z两端的流量数据
    //    - 构建PromQL查询A端的入/出流量 (rate/increase)
    //    - 构建PromQL查询Z端的入/出流量 (rate/increase)
    //    - 使用goroutine和channel并发执行查询
    
    // 4. 合并结果
    //    将A、Z两端的查询结果合并到 TrafficChartResponse 或 TrafficSummaryResponse 结构体中。
    //    例如，图表响应的series将包含4个序列：A端入、A端出、Z端入、Z端出。

    // ... 实现并发查询和结果合并的详细代码 ...
    
    return &models.TrafficChartResponse{...}, nil
}
```

#### 3.2.3 单端口查询实现逻辑

此场景服务于新增的 `/api/v1/flow/*` 接口。

```go
// GetSinglePortFlow 实现单端口流量查询的核心逻辑
func (s *TrafficServiceImpl) GetSinglePortFlow(ctx context.Context, params *models.SinglePortQueryParams) (*models.PortFlow, error) {
    // 1. 调用DAO验证参数并获取设备IP
    deviceIP, err := s.mappingDAO.GetDeviceIPForPort(ctx, params.DeviceID, params.PortID, params.VNI)
    if err != nil {
        // 如果验证失败或查询出错，直接返回错误
        return nil, fmt.Errorf("端口参数验证失败: %w", err)
    }

    // 2. 构建Prometheus查询标签 (使用device_ip和vni)
    labels := map[string]string{
        "device_ip": deviceIP,
        "vni":       params.VNI,
    }

    // 3. 构建时间范围字符串, e.g., "300s"
    timeRange := fmt.Sprintf("%ds", int(params.EndTime.Sub(params.StartTime).Seconds()))

    // 4. 并发查询入向/出向的速率和总流量
    //    (此处省略并发实现细节，逻辑与原方案类似)

    // 示例：查询入向速率 (bps)
    inRateQuery := fmt.Sprintf(`rate(dci_snmp_flow_in_octets{%s}[%s]) * 8`, formatLabels(labels), timeRange)
    inRate, err := s.trafficDAO.QueryRate(ctx, inRateQuery, params.EndTime)
    if err != nil {
        // 处理错误, 可能为无数据，返回0
    }

    // ... 查询其他三个指标 ...
    
    // 5. 构建并返回结果
    // ...
    
    return flow, nil
}
```

#### 3.2.4 单接口查询时序图

```mermaid
sequenceDiagram
    participant Client as "API调用者"
    participant Monitor as "DCI Monitor (API层)"
    participant Service as "TrafficService (服务层)"
    participant DAO_MySQL as "PortMappingDAO"
    participant DB as "MySQL数据库"
    participant DAO_Prom as "TrafficDAO"
    participant Prometheus as "Prometheus数据库"

    activate Client
    Client->>Monitor: GET /api/v1/flow/port
    activate Monitor
    
    Monitor->>Service: GetSinglePortFlow(params)
    activate Service
    
    Service->>DAO_MySQL: GetDeviceIPForPort(...)
    activate DAO_MySQL

    DAO_MySQL->>DB: SELECT ...
    activate DB
    DB-->>DAO_MySQL: returns (device_ip, err)
    deactivate DB
    
    DAO_MySQL-->>Service: returns (device_ip, err)
    deactivate DAO_MySQL
    
    alt "err == nil (验证成功)"
        Service->>DAO_Prom: QueryRate/Increase(...)
        activate DAO_Prom

        DAO_Prom->>Prometheus: 执行PromQL
        activate Prometheus
        Prometheus-->>DAO_Prom: 返回流量数据
        deactivate Prometheus
        
        DAO_Prom-->>Service: 返回计算结果
        deactivate DAO_Prom
        
        Service-->>Monitor: 返回PortFlowResponse
        Monitor-->>Client: 200 OK (JSON响应)

    else "err != nil (验证失败)"
        Service-->>Monitor: 返回错误
        Monitor-->>Client: 500 Error (JSON响应)
    end

    deactivate Service
    deactivate Monitor
    deactivate Client
```

## 4. 与现有系统集成

本次功能开发将**直接集成**到现有的 `dcimonitor` 服务中，以复用其配置、日志、数据库连接和HTTP服务器实例。

### 4.1 代码结构

1.  **`traffic_monitor.go`**:
    -   **职责**: 继续负责处理现有的A-Z链路API (`/api/v1/traffic/*`)。
    -   **改造点**:
        -   修改 `chartQueryParams` 结构体，新增 `VNI` 字段。
        -   修改 `parseAndValidateParams` 函数，增加对 `VNI` 参数的解析。
        -   修改 `getTrafficChartData` 和 `GetTrafficSummary` 的实现，将原有的随机数逻辑替换为调用新的 `TrafficService`，执行我们设计的A-Z链路查询逻辑。

2.  **`services/traffic_service.go`**:
    -   **职责**: 包含所有流量查询的核心业务逻辑，供 `monitors` 层调用。
    -   **主要方法**:
        -   `GetAZLinkTraffic(...)`: 实现A-Z链路的查询逻辑。
        -   `GetSinglePortFlow(...)`: 实现新增的单端口查询逻辑。
        -   `GetSinglePortHistory(...)`: 实现单端口历史流量查询逻辑。
    -   该服务将依赖于`PortMappingDAO`和`TrafficDAO`。

3.  **`services/dao.go`**:
    -   **职责**: 包含`PortMappingDAO`和`TrafficDAO`的接口定义和实现，负责与MySQL和Prometheus直接交互。

4.  **`server.go`**:
    -   **职责**: 负责服务的初始化和路由注册。
    -   **改造点**:
        -   在 `startServer` 函数中，初始化 `PortMappingDAO`, `TrafficDAO` 和 `TrafficService`。
        -   将 `TrafficService` 实例注入到 `TrafficMonitor` 中。
        -   注册**新增**的单端口查询API路由 (`/api/v1/flow/*`)，并将其指向新的处理器方法。

### 4.2 依赖注入示例

在 `server.go` 的 `startServer` 函数中，我们将按以下方式组织服务初始化和注入：

```go
func startServer() {
    // ... 现有初始化代码: logger, viper, gin ...

    // 1. 初始化DAO
    // (假设已从config获取db和prometheus配置)
    db, err := gorm.Open(...) // 示例
    promClient, err := api.NewClient(...) // 示例

    portMappingDAO := services.NewPortMappingDAO(db)
    trafficDAO := services.NewTrafficDAO(promClient)

    // 2. 初始化核心服务
    trafficService := services.NewTrafficService(portMappingDAO, trafficDAO)

    // 3. 初始化API处理器 (Monitors) 并注入服务
    // 改造 trafficMonitor 以接收 trafficService
    trafficMonitor := monitors.NewTrafficMonitor(trafficService) 
    
    // (如果需要) 创建新的 singlePortMonitor 并注入服务
    // singlePortMonitor := monitors.NewSinglePortMonitor(trafficService)

    // 4. 设置API路由
    apiV1 := router.Group("/api/v1")
    {
        // 改造现有的A-Z链路路由
        trafficGroup := apiV1.Group("/traffic")
        {
            trafficGroup.GET("/chart/average", trafficMonitor.GetAverageTrafficChart)
            trafficGroup.GET("/chart/maximum", trafficMonitor.GetMaximumTrafficChart)
            trafficGroup.GET("/chart/minimum", trafficMonitor.GetMinimumTrafficChart)
            trafficGroup.GET("/summary", trafficMonitor.GetTrafficSummary)
        }

        // 新增单端口查询路由
        flowGroup := apiV1.Group("/flow")
        {
            // 此处路由指向新的处理器方法，这些方法内部调用trafficService
            // flowGroup.GET("/port", singlePortMonitor.GetPortFlow)
            // flowGroup.GET("/port/history", singlePortMonitor.GetPortFlowHistory)
        }
    }
    
    // ... 启动和关闭服务器 ...
}
```

## 5. 配置和部署

### 5.1 配置参数

所有配置项将统一放入 `config.yaml` 文件。除了现有的`server`, `logger`, `prometheus`之外，还需要确保数据库连接信息可用。

```yaml
# DCI监控系统通用配置文件

# MySQL数据库配置 (dbsync服务已将其同步到dci_monitor库)
database:
  driver: mysql
  host: ...
  port: 3306
  username: ...
  password: ...
  dbname: dci_monitor # 查询服务将连接此数据库
  charset: utf8mb4

# ... 其他现有配置 ...

# Prometheus 配置
prometheus:
  address: "http://dciprometheus.intro.citic-x.com:30006" # Prometheus 服务器地址
  timeout: 30s # 查询超时时间

# ... 其他现有配置 ...
```

## 6. API接口设计

本节定义API的最终形态，包括对现有API的改造和新增的API。

### 6.1 A-Z链路流量API (改造)

适用于为多云立方系统提供服务的现有接口。

#### 6.1.1 `GET /api/v1/traffic/chart/*` (average, maximum, minimum)

- **用途**: 获取A-Z链路在指定时间窗口内聚合后的流量图数据。
- **请求参数**:
    - `granularity` (string, required): `1m`, `5m`, `1h`, `1d`.
    - `time_range` (string, optional): `1h`, `2h`, ..., `1Y`. 与`start_time`/`end_time`互斥.
    - `start_time` (string, optional): RFC3339 UTC.
    - `end_time` (string, optional): RFC3339 UTC.
    - `a_switch_id` (string, required): A端设备ID.
    - `a_port_id` (string, required): A端端口ID (需URL编码).
    - `z_switch_id` (string, required): Z端设备ID.
    - `z_port_id` (string, required): Z端端口ID (需URL编码).
    - **`vni` (string, optional)**: **(新增)** 业务VNI标识。如果提供，将用于A、Z两端端口的同步校验。
- **返回示例**: (结构不变，数据源变为Prometheus真实数据)

#### 6.1.2 `GET /api/v1/traffic/summary`

- **用途**: 获取A-Z链路在指定时间窗口内的流量摘要统计。
- **请求参数**: 同上。
- **返回示例**: (结构不变，数据源变为Prometheus真实数据)

### 6.2 单端口流量API (新增)

适用于灵活的、针对单个端口的流量查询场景。

#### 6.2.1 `GET /api/v1/flow/port`

- **用途**: 查询单个端口在指定时间范围内的**平均速率**和**总流量**。
- **请求参数**:
    - `device_id` (string, required): 设备ID.
    - `port_name` (string, required): 端口名称 (e.g., "GE1/0/1").
    - `vni` (string, optional): VNI标识.
    - `start_time` (string, optional): RFC3339 UTC. 默认为15分钟前。
    - `end_time` (string, optional): RFC3339 UTC. 默认为当前时间。
- **返回示例**:
```json
{
    "request_id": "uuid-...",
    "query_details": {
        "device_id": "CE1",
        "port_name": "GE1/0/1",
        "vni": "6005002",
        "start_time": "2025-06-06T16:00:00Z",
        "end_time": "2025-06-06T16:15:00Z"
    },
    "flow_data": {
        "unit_rate": "Mbps",
        "unit_total": "MB",
        "in_rate": 150.75,
        "out_rate": 340.12,
        "in_total": 1695.94,
        "out_total": 3826.35
    }
}
```

#### 6.2.2 `GET /api/v1/flow/port/history`

- **用途**: 查询单个端口在指定时间范围内的**历史流量速率**时间序列。
- **请求参数**:
    - `device_id` (string, required): 设备ID.
    - `port_name` (string, required): 端口名称.
    - `vni` (string, optional): VNI标识.
    - `start_time` (string, required): RFC3339 UTC.
    - `end_time` (string, required): RFC3339 UTC.
    - `step` (string, optional): 步长 (e.g., "1m", "5m"). 默认为`1m`。
- **返回示例**:
```json
{
    "request_id": "uuid-...",
  "query_details": {
        "device_id": "CE1",
        "port_name": "GE1/0/1",
    "vni": "6005002",
        "start_time": "2025-06-06T16:00:00Z",
        "end_time": "2025-06-06T16:15:00Z",
        "step": "5m"
  },
  "chart_data": {
    "unit": "Mbps",
    "timestamps": [
            "2025-06-06T16:00:00Z",
            "2025-06-06T16:05:00Z",
            "2025-06-06T16:10:00Z",
            "2025-06-06T16:15:00Z"
    ],
    "series": [
            { "name": "入流量", "data": [150.1, 155.2, 148.9, 152.3] },
            { "name": "出流量", "data": [340.5, 330.1, 345.8, 342.6] }
    ]
  }
}
```

## 7. 本设计的代码实现文件列表

这份清单旨在详细说明为实现本设计所需创建和修改的文件，并阐明与现有代码的关联。

### 7.1 文件变更清单 (新增与修改)

```tree
dci-monitor/src/
└── dcimonitor/
    ├── cmd/
    │   └── server.go             # (修改) 服务主入口。将在此处初始化新的DAO和Service，将其注入到Monitors中，并注册新增的 /api/v1/flow/* 路由。
    ├── config/
    │   └── config.yaml           # (修改) 确保 'database' 部分的配置正确无误，以便服务能连接到dci_monitor数据库进行端口映射查询。
    ├── internal/
    │   ├── models/
    │   │   ├── request.go        # (新增) 定义新增API的请求体和查询参数结构体，例如 SinglePortQueryParams。
    │   │   └── response.go       # (修改/新增) 修改现有的 TrafficQueryDetails 以包含 VNI 字段。新增用于单端口API的响应结构，例如 PortFlow。
    │   ├── monitors/
    │   │   ├── traffic_monitor.go    # (修改) 改造现有A-Z链路API处理器。将注入新的 TrafficService，并调用其方法替换原有的随机数生成逻辑。
    │   │   └── single_port_monitor.go # (新增) 为新增的 /api/v1/flow/* API提供独立的处理器，负责解析请求并调用 TrafficService。
    │   └── services/
    │       ├── traffic_service.go  # (新增) 流量查询核心服务。封装A-Z链路和单端口查询的全部业务逻辑，是本次开发的核心。
    │       └── dao.go              # (新增) 数据访问对象。实现 PortMappingDAO 和 TrafficDAO 接口，专门负责与MySQL和Prometheus的交互。
    └── README.md                 # (修改) 更新API端点章节，加入新增的 /api/v1/flow/* 接口说明和使用示例。
```

### 7.2 关联模块说明 (无需修改)

以下文件和模块与本次开发相关，但其本身无需修改。新代码将复用它们提供的功能。

- **`cmd/root.go`**: Cobra命令入口，定义了`server`命令，是整个程序的起点。
- **`cmd/dbsync/main.go`**: 数据库同步服务。本服务依赖它正确地将设备端口信息同步到`dci_monitor`数据库。
- **`internal/middleware/gin_logger.go`**: Gin日志中间件。所有新旧API的请求都将通过此中间件记录日志。
- **`internal/models/error.go`**: 标准错误响应模型。新的API在处理错误时将复用这里定义的结构。
- **`internal/services/identifier_service.go`**: 现有的标识符服务。该服务主要用于LLDP拓扑发现时的复杂映射。为保持职责单一，本次流量查询将通过`services/dao.go`中更直接的SQL进行端口映射，而非使用此服务。
- **`common/logger/` (目录下所有文件)**: 全局日志包。所有新模块的日志输出都将调用此包提供的功能。

### 7.3 生成文件 (需要更新)

以下文件是根据代码注释自动生成的，在开发完成后，需要**手动执行命令**重新生成，以反映API的变更。

- **`docs/docs.go`**: Swag生成的Go代码文档。
- **`docs/swagger.json`**: Swagger 2.0 API 定义文件。
- **`docs/swagger.yaml`**: Swagger 2.0 API 定义文件 (YAML格式)。 