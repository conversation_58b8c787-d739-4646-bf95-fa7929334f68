---
title: |
  Prometheus数据保留与降采样策略

subtitle: |
  DCI数据监测系统时序数据存储管理方案
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-13 | 顾铠羟 | 初始版本           |

# 1 方案概述

## 1.1 背景与目标

DCI数据监测系统从TDengine迁移到Prometheus+Thanos架构的过程中，需要制定完整的数据保留与降采样策略，确保系统既能高效管理海量时序数据，又能平衡存储成本与查询性能。有效的数据保留策略对于监控系统的可持续运行至关重要，尤其是在指标数量和保留期限不断增长的情况下。

本文档旨在设计一套多层次的数据保留与降采样策略，满足以下目标：

1. 确保关键监控数据在合适的时间范围内可用
2. 通过降采样和压缩优化长期数据存储成本
3. 平衡数据精度与存储效率
4. 满足不同时间范围查询的性能要求
5. 为不同类型指标提供差异化的存储策略

## 1.2 适用范围

本策略适用于DCI监测系统中所有通过Prometheus+Thanos存储的时序数据，包括：

1. 网络设备监控指标
2. 服务器资源监控指标
3. 应用性能监控指标
4. 业务指标
5. 自定义指标

## 1.3 关键指标

1. **存储效率**：降采样后的数据相比原始数据的压缩比≥85%
2. **数据可用性**：保证99.99%的监控数据在规定保留期内可被查询
3. **查询性能**：
   - 小时级别数据（<7天）的查询响应时间<500ms
   - 天级别数据（7-60天）的查询响应时间<2s
   - 长期历史数据（>60天）的查询响应时间<5s
4. **成本控制**：长期（>180天）存储的成本比原始数据降低至少75%
5. **系统可扩展性**：支持指标数量每年增长50%的扩展能力

# 2 策略架构

## 2.1 多层次存储模型

本策略采用多层次存储模型，将数据按时间和重要程度分层管理：

```mermaid
graph TD
    subgraph 热存储[热存储 - 高精度]
        PromLocal["Prometheus本地存储<br>全精度数据<br>15天"]
    end
    
    subgraph 温存储[温存储 - 中精度]
        ObjStore5m["对象存储<br>5分钟聚合<br>60天"]
        ObjStore1h["对象存储<br>1小时聚合<br>180天"]
    end
    
    subgraph 冷存储[冷存储 - 低精度]
        ObjStore1d["对象存储<br>1天聚合<br>5年"]
    end
    
    PromLocal -->|上传原始数据| ObjStore5m
    PromLocal -->|超出保留期| ThanosCom["Thanos Compact<br>数据压缩和降采样"]
    ThanosCom -->|5分钟降采样| ObjStore5m
    ThanosCom -->|1小时降采样| ObjStore1h
    ThanosCom -->|1天降采样| ObjStore1d
    
    ObjStore5m -->|查询 7-60天| ThanosSto["Thanos Store<br>历史数据读取"]
    ObjStore1h -->|查询 60-180天| ThanosSto
    ObjStore1d -->|查询 >180天| ThanosSto
    
    PromLocal -->|查询 <7天| ThanosQ["Thanos Query<br>查询协调"]
    ThanosSto -->|历史数据| ThanosQ
```

## 2.2 数据降采样流程

Thanos Compact组件负责对对象存储中的数据执行降采样处理：

```mermaid
sequenceDiagram
    participant P as Prometheus
    participant S as Thanos Sidecar
    participant O as 对象存储
    participant C as Thanos Compact
    participant Q as Thanos Query
    
    P->>P: 采集原始数据
    P->>S: 数据块就绪通知
    S->>O: 上传原始精度数据块
    
    C->>O: 扫描数据块
    C->>C: 确定需要降采样的数据
    C->>C: 执行5分钟降采样
    C->>O: 写入5分钟降采样数据
    
    C->>O: 再次扫描数据块
    C->>C: 执行1小时降采样
    C->>O: 写入1小时降采样数据
    
    C->>O: 再次扫描数据块
    C->>C: 执行1天降采样
    C->>O: 写入1天降采样数据
    
    Q->>O: 根据查询时间范围请求数据
    O->>Q: 返回最适合的降采样级别数据
```

# 3 策略详解

## 3.1 数据保留周期

### 3.1.1 分层保留周期

| 存储层级 | 存储位置 | 保留期限 | 数据精度 | 主要用途 |
|---------|---------|---------|---------|---------|
| 热存储 | Prometheus本地TSDB | 15天 | 原始精度(~15s) | 实时查询、告警、短期分析 |
| 温存储-5m | 对象存储 | 60天 | 5分钟聚合 | 近期趋势分析、容量规划 |
| 温存储-1h | 对象存储 | 180天 | 1小时聚合 | 中期趋势分析、季度报告 |
| 冷存储-1d | 对象存储 | 5年 | 1天聚合 | 长期趋势分析、年度报告 |

### 3.1.2 差异化保留策略

针对不同重要性的指标类型，采用差异化的保留策略：

| 指标类型 | 示例 | 本地存储 | 5分钟聚合 | 1小时聚合 | 1天聚合 |
|---------|------|---------|---------|----------|---------|
| 关键系统指标 | CPU、内存、流量 | 30天 | 90天 | 1年 | 5年 |
| 一般系统指标 | 磁盘IO、TCP连接 | 15天 | 60天 | 180天 | 3年 |
| 详细调试指标 | 队列深度、缓存命中 | 7天 | 30天 | 90天 | 1年 |
| 内部状态指标 | 采集状态、内部计数器 | 3天 | 15天 | - | - |

## 3.2 降采样配置

### 3.2.1 降采样聚合函数

降采样过程中，不同类型的指标采用不同的聚合函数：

| 指标类型 | 聚合函数 | 适用场景 | 示例指标 |
|---------|---------|---------|---------|
| 累计型指标 | rate() + avg() | 计数器类指标 | 流量、请求数、错误数 |
| 瞬时值指标 | avg() | 仪表盘类指标 | CPU使用率、内存使用量 |
| 分布型指标 | histogram_quantile() | 直方图类指标 | 请求延迟、响应大小 |
| 峰值型指标 | max() | 需要捕获峰值的指标 | 最大连接数、最大延迟 |
| 谷值型指标 | min() | 需要捕获最小值的指标 | 最小可用内存、空闲磁盘 |

### 3.2.2 Thanos Compact配置

Thanos Compact组件配置示例：

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: thanos-compact
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: thanos-compact
  template:
    metadata:
      labels:
        app: thanos-compact
    spec:
      containers:
      - name: thanos
        image: thanosio/thanos:v0.32.0
        args:
        - "compact"
        - "--data-dir=/var/thanos/compact"
        - "--objstore.config-file=/etc/thanos/objstore.yaml"
        - "--retention.resolution-raw=60d"        # 原始精度数据保留60天
        - "--retention.resolution-5m=180d"        # 5分钟降采样数据保留180天
        - "--retention.resolution-1h=2y"          # 1小时降采样数据保留2年
        - "--downsampling.disable=false"          # 启用降采样
        - "--deduplication.replica-label=replica" # 去重标签
        - "--wait"                                # 持续运行模式
        volumeMounts:
        - name: data
          mountPath: /var/thanos/compact
        - name: thanos-objstore
          mountPath: /etc/thanos
        resources:
          requests:
            memory: "4Gi"
            cpu: "1"
          limits:
            memory: "8Gi"
            cpu: "2"
      volumes:
      - name: data
        emptyDir: {}
      - name: thanos-objstore
        secret:
          secretName: thanos-objstore
```

### 3.2.3 降采样优化参数

为优化降采样过程和结果，配置以下参数：

| 参数 | 推荐值 | 描述 |
|------|-------|------|
| block-sync-concurrency | 20 | 对象存储块同步并发数 |
| compact-concurrency | 4 | 压缩并发数 |
| wait-interval | 5m | 周期性运行间隔 |
| consistency-delay | 30m | 一致性检查延迟 |
| compaction-interval | 5m | 压缩检查间隔 |
| compaction-retention | 根据保留策略 | 各精度数据保留时间 |

## 3.3 本地存储配置

### 3.3.1 Prometheus TSDB参数

Prometheus本地存储配置示例：

```yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: prometheus-network
  namespace: monitoring
spec:
  # ... 其他配置 ...
  template:
    spec:
      containers:
      - name: prometheus
        image: prom/prometheus:v2.45.0
        args:
          - "--config.file=/etc/prometheus/prometheus.yaml"
          - "--storage.tsdb.path=/prometheus"
          - "--storage.tsdb.retention.time=15d"     # 数据保留时间
          - "--storage.tsdb.retention.size=500GB"   # 数据保留大小上限
          - "--storage.tsdb.max-block-duration=2h"  # 最大块持续时间
          - "--storage.tsdb.min-block-duration=2h"  # 最小块持续时间
          - "--storage.tsdb.wal-compression=true"   # WAL压缩
          - "--web.enable-lifecycle"
          - "--web.enable-admin-api"
        # ... 资源配置 ...
        volumeMounts:
        - name: prometheus-storage
          mountPath: /prometheus
  volumeClaimTemplates:
  - metadata:
      name: prometheus-storage
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 600Gi  # 存储容量
```

### 3.3.2 存储容量规划

根据指标量和保留期，计算所需存储空间：

| 存储层级 | 指标数量 | 采样频率 | 每点大小 | 保留期 | 预计存储空间 | 冗余系数 | 总空间需求 |
|---------|---------|---------|---------|--------|------------|---------|----------|
| Prometheus本地存储 | 100万 | 15s | ~2字节 | 15天 | ~280GB | 1.5 | ~420GB |
| 对象存储(全精度) | 100万 | 15s | ~2字节 | 60天 | ~1.1TB | 1.3 | ~1.5TB |
| 对象存储(5m降采样) | 100万 | 5m | ~2字节 | 180天 | ~400GB | 1.3 | ~520GB |
| 对象存储(1h降采样) | 100万 | 1h | ~2字节 | 2年 | ~280GB | 1.3 | ~364GB |
| 对象存储(1d降采样) | 100万 | 1d | ~2字节 | 5年 | ~170GB | 1.3 | ~221GB |

注：存储空间计算考虑了压缩率和索引开销，冗余系数包括格式开销、重复数据和安全裕度。

## 3.4 对象存储配置

### 3.4.1 对象存储选型

对象存储选型考虑以下因素：

1. **性能**：读写延迟、吞吐量
2. **成本**：存储费用、请求费用、流量费用
3. **集成**：与Thanos的兼容性、认证机制
4. **数据冗余**：提供的数据冗余和备份能力
5. **地域**：数据中心位置、可用区

推荐方案：

| 方案 | 优点 | 缺点 | 推荐情况 |
|------|------|------|---------|
| 阿里云OSS | 与现有阿里云环境集成、高可靠性 | 公有云厂商锁定 | ✅ 主推方案 |
| MinIO | 自托管、完全控制、兼容S3 API | 需自行管理扩展和高可用 | ⚠️ 备选方案 |
| 华为云OBS | 高可靠、兼容性良好 | 公有云厂商锁定 | ⚠️ 备选方案 |

### 3.4.2 对象存储连接配置

阿里云OSS配置示例：

```yaml
type: ALIYUN
config:
  endpoint: "oss-cn-beijing-internal.aliyuncs.com"
  bucket: "dci-prometheus-storage"
  prefix: "thanos/metrics"
  access_key: "${OSS_ACCESS_KEY}"
  secret_key: "${OSS_SECRET_KEY}"
```

MinIO配置示例：
```yaml
type: S3
config:
  bucket: "dci-prometheus-storage"
  endpoint: "minio.dci-monitoring.svc:9000"
  insecure: true
  access_key: "${MINIO_ACCESS_KEY}"
  secret_key: "${MINIO_SECRET_KEY}"
```

### 3.4.3 对象存储分区策略

为优化性能和成本，使用以下对象存储分区策略：

1. **按时间精度分区**：
   - `/thanos/metrics/raw/` - 原始精度数据
   - `/thanos/metrics/5m/` - 5分钟降采样数据
   - `/thanos/metrics/1h/` - 1小时降采样数据
   - `/thanos/metrics/1d/` - 1天降采样数据

2. **按监控对象类型分区**（可选）：
   - `/thanos/metrics/raw/network/` - 网络设备指标
   - `/thanos/metrics/raw/server/` - 服务器指标
   - `/thanos/metrics/raw/application/` - 应用指标

## 3.5 查询优化

### 3.5.1 查询路由策略

配置Thanos Query根据查询时间范围自动选择最合适的数据源：

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: thanos-query
  namespace: monitoring
spec:
  replicas: 3
  selector:
    matchLabels:
      app: thanos-query
  template:
    metadata:
      labels:
        app: thanos-query
    spec:
      containers:
      - name: thanos
        image: thanosio/thanos:v0.32.0
        args:
        - "query"
        - "--grpc-address=0.0.0.0:10901"
        - "--http-address=0.0.0.0:10902"
        - "--store=prometheus-network-0.prometheus-network:10901"  # Prometheus Sidecars
        - "--store=prometheus-network-1.prometheus-network:10901"
        - "--store=thanos-store-0.thanos-store:10901"              # Thanos Store
        - "--store=thanos-store-1.thanos-store:10901"
        - "--query.auto-downsampling=true"                         # 启用自动降采样
        - "--query.replica-label=replica"                          # 去重标签
        - "--selector-label=tenant=\"${TENANT}\""                  # 多租户标签
        ports:
        - name: grpc
          containerPort: 10901
        - name: http
          containerPort: 10902
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
```

### 3.5.2 查询规则与最佳实践

为优化查询性能，制定以下查询规则与最佳实践：

1. **时间范围适配**：
   - <7天数据优先查询Prometheus本地存储
   - 7-60天数据优先查询5分钟降采样数据
   - 60-180天数据优先查询1小时降采样数据
   - >180天数据优先查询1天降采样数据

2. **标签选择优化**：
   - 优先使用高基数标签进行过滤
   - 避免使用正则表达式查询
   - 利用索引标签加速查询

3. **聚合函数选择**：
   - 长时间范围查询使用更粗粒度的聚合函数
   - 使用预计算的速率和聚合函数

## 3.6 压缩策略

### 3.6.1 块压缩配置

Thanos Compact执行两种压缩操作：

1. **垂直压缩**：合并同一时间范围的小块为一个大块
2. **水平压缩**：创建降采样块

垂直压缩配置：
```yaml
- "--compact.concurrency=4"                  # 压缩并发度
- "--delete-delay=48h"                       # 删除延迟
- "--compact-concurrency=4"                  # 压缩并发数
- "--block-viewer.global.sync-block=true"    # 同步块查看
```

### 3.6.2 数据压缩比优化

为提高压缩比率，采用以下策略：

1. **指标名和标签优化**：
   - 避免冗长的指标名
   - 控制标签数量和大小
   - 移除低价值标签

2. **内部压缩优化**：
   - 启用Prometheus WAL压缩
   - 合理配置压缩级别

# 4 差异化存储策略

## 4.1 指标分类与存储策略

根据指标重要性和使用模式，将指标分为以下几类，并应用差异化存储策略：

| 指标类别 | 描述 | 存储策略 | 示例指标 |
|---------|------|---------|---------|
| 关键业务指标 | 影响业务连续性的核心指标 | 本地30天，全精度90天，降采样5年 | 核心服务可用性、关键接口延迟 |
| 标准监控指标 | 常规系统监控指标 | 本地15天，全精度60天，降采样3年 | CPU、内存、网络吞吐量 |
| 调试诊断指标 | 用于问题诊断的详细指标 | 本地7天，全精度30天，降采样1年 | 线程数、队列长度、缓存命中率 |
| 临时任务指标 | 特定任务产生的短期指标 | 本地3天，全精度15天，不降采样 | 测试任务数据、临时监控 |

## 4.2 高频指标特殊处理

对于采集频率特别高（<10s）的指标，采用特殊存储策略：

1. **预聚合**：在Telegraf端进行预聚合，减少写入压力
2. **独立分片**：将高频指标单独分配到专用的Prometheus实例
3. **动态采样**：根据指标变化率动态调整采样率
4. **立即降采样**：更快地将数据降采样到合适精度

## 4.3 大基数标签处理

对于具有高基数标签的指标（如client_id, user_id等），采用以下策略：

1. **标签规范化**：预处理标签减少基数（如ip分类为区域）
2. **选择性记录**：仅记录重要实例的详细标签
3. **聚合存储**：按标签组聚合后存储
4. **单独分片**：将高基数指标单独分配到专用实例

# 5 容量规划与成本预估

## 5.1 存储容量计算模型

使用以下模型估算存储需求：

```
存储大小 = 指标数量 × 采样点大小 × 采样频率 × 保留时间 × (1 - 压缩率) × 冗余系数
```

以平均每个时间序列每月消耗1-2MB存储空间为基准，结合实际指标数量进行估算。

## 5.2 存储需求预测

| 时间段 | 预计指标数量 | 本地存储需求 | 对象存储需求 | 总存储需求 |
|-------|------------|------------|------------|----------|
| 初始部署 | 100万 | ~500GB | ~2.5TB | ~3TB |
| 6个月后 | 150万 | ~750GB | ~3.8TB | ~4.5TB |
| 1年后 | 200万 | ~1TB | ~5TB | ~6TB |
| 2年后 | 300万 | ~1.5TB | ~7.5TB | ~9TB |

注：存储预测基于平均采样率15秒，并考虑了数据压缩和降采样因素。

## 5.3 成本优化措施

为控制存储和操作成本，采取以下优化措施：

1. **指标筛选**：定期审核并剔除低价值指标
2. **标签优化**：控制标签数量和大小
3. **采样频率调整**：根据指标变化率调整采样频率
4. **存储分级**：使用对象存储生命周期管理降低存储费用
5. **压缩优化**：调整压缩参数提高压缩比
6. **查询优化**：缓存热点查询减少计算资源消耗

# 6 实施方案

## 6.1 配置文件示例

### 6.1.1 Prometheus配置

```yaml
# prometheus.yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s

# 存储配置
storage:
  tsdb:
    path: /prometheus
    retention.time: 15d
    retention.size: 500GB
    wal-compression: true
```

### 6.1.2 Thanos Sidecar配置

```yaml
# thanos-sidecar.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: prometheus
spec:
  # ... 其他配置 ...
  template:
    spec:
      containers:
      # ... Prometheus容器 ...
      - name: thanos-sidecar
        image: thanosio/thanos:v0.32.0
        args:
        - "sidecar"
        - "--tsdb.path=/prometheus"
        - "--prometheus.url=http://localhost:9090"
        - "--objstore.config-file=/etc/thanos/objstore.yaml"
        - "--reloader.config-file=/etc/prometheus/prometheus.yaml"
        - "--reloader.rule-dir=/etc/prometheus/rules/"
        volumeMounts:
        - name: prometheus-storage
          mountPath: /prometheus
        - name: thanos-objstore
          mountPath: /etc/thanos
```

### 6.1.3 Thanos Compact配置

```yaml
# thanos-compact.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: thanos-compact
spec:
  # ... 其他配置 ...
  template:
    spec:
      containers:
      - name: thanos
        image: thanosio/thanos:v0.32.0
        args:
        - "compact"
        - "--data-dir=/var/thanos/compact"
        - "--objstore.config-file=/etc/thanos/objstore.yaml"
        - "--retention.resolution-raw=60d"
        - "--retention.resolution-5m=180d"
        - "--retention.resolution-1h=730d"
        - "--retention.resolution-1d=1825d"
        - "--downsampling.disable=false"
        - "--wait"
        volumeMounts:
        - name: data
          mountPath: /var/thanos/compact
        - name: thanos-objstore
          mountPath: /etc/thanos
```

## 6.2 部署步骤

1. **准备阶段**：
   - 配置对象存储账号和权限
   - 创建必要的存储桶和路径
   - 准备配置文件和密钥

2. **Prometheus部署**：
   - 部署带有适当存储配置的Prometheus
   - 配置Thanos Sidecar上传数据到对象存储

3. **Thanos组件部署**：
   - 部署Thanos Compact进行数据压缩和降采样
   - 部署Thanos Store访问对象存储数据
   - 部署Thanos Query提供统一查询

4. **验证和调整**：
   - 验证数据上传和降采样流程
   - 监控存储使用情况
   - 根据实际情况调整保留期和降采样参数

## 6.3 监控和维护

为确保存储策略有效实施，需建立以下监控和维护机制：

1. **存储使用监控**：
   - 监控Prometheus本地存储用量
   - 监控对象存储用量和费用
   - 设置存储使用告警

2. **降采样监控**：
   - 监控Thanos Compact组件运行状态
   - 验证降采样数据是否按期生成
   - 检查降采样数据的完整性

3. **性能监控**：
   - 监控查询延迟
   - 监控Prometheus和Thanos组件资源使用
   - 识别并优化低效查询

# 7 风险与应对措施

| 风险点 | 影响程度 | 应对措施 |
|-------|---------|---------|
| 存储增长超预期 | 高 | 1. 更积极的数据过滤<br>2. 调整降采样参数<br>3. 增加存储资源<br>4. 减少保留期 |
| 降采样导致重要信息丢失 | 中 | 1. 针对关键指标保留更高精度<br>2. 优化聚合函数选择<br>3. 建立降采样数据验证机制 |
| 对象存储访问性能问题 | 中 | 1. 优化查询模式<br>2. 调整Thanos Store缓存<br>3. 考虑更高性能的存储方案 |
| 数据迁移期间的数据丢失 | 高 | 1. 实施双写双读策略<br>2. 建立数据完整性验证机制<br>3. 分阶段迁移减少风险 |
| 成本超出预算 | 中 | 1. 更激进的降采样和删除策略<br>2. 优化查询减少请求费用<br>3. 考虑自托管存储方案 |

# 8 测试验证方案

## 8.1 功能测试

| 测试项 | 测试内容 | 验收标准 |
|-------|---------|---------|
| 数据上传测试 | 验证Prometheus数据通过Sidecar上传到对象存储 | 数据完整上传，无丢失 |
| 降采样测试 | 验证Thanos Compact正确生成降采样数据 | 所有精度的降采样数据生成正确 |
| 查询路由测试 | 验证不同时间范围查询路由至正确的数据源 | 查询自动选择合适的降采样级别 |
| 保留策略测试 | 验证过期数据自动清理 | 超出保留期的数据被正确清理 |

## 8.2 性能测试

| 测试项 | 测试内容 | 验收标准 |
|-------|---------|---------|
| 高并发写入测试 | 模拟大量指标同时写入 | 支持每秒50万指标点写入 |
| 查询性能测试 | 不同时间范围的查询性能 | 符合查询性能目标 |
| 压缩性能测试 | Thanos Compact压缩和降采样性能 | 24小时内完成所有降采样任务 |
| 长期运行测试 | 系统长期运行稳定性 | 7天无错误运行 |

# 9 附录

## 9.1 参考文档

1. [Prometheus存储文档](https://prometheus.io/docs/prometheus/latest/storage/)
2. [Thanos降采样设计](https://thanos.io/tip/components/compact.md/)
3. [对象存储性能优化](https://aws.amazon.com/blogs/storage/optimizing-amazon-s3-for-high-concurrency-in-distributed-workloads/)
4. [时序数据库降采样最佳实践](https://docs.timescale.com/timescaledb/latest/how-to-guides/compression/compression-best-practices/)
5. [Google SRE关于监控数据存储的建议](https://sre.google/sre-book/monitoring-distributed-systems/)

## 9.2 术语表

| 术语 | 说明 |
|------|------|
| TSDB | 时间序列数据库，Prometheus的本地存储引擎 |
| WAL | 预写日志，确保数据持久性的机制 |
| 降采样 | 将高精度数据转换为低精度数据的过程 |
| 垂直压缩 | 合并同一时间范围的多个小块为一个大块 |
| 水平压缩 | 创建不同精度的降采样数据 |
| 对象存储 | 用于存储大量非结构化数据的存储服务 |
| 聚合函数 | 用于将多个数据点组合为一个的计算函数 |
</rewritten_file> 