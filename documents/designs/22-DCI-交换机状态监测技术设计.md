---
title: |
  DCI-交换机状态监测技术设计

subtitle: |
  技术设计方案
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-06-16 | 顾铠羟 | 初始版本，基于标准MIB和计算方式 |
| V2.0 | 2025-06-18 | 顾铠羟 | 根据华为私有MIB更新，直接获取CPU和内存使用率 |
| V3.0 | 2025-06-20 | 顾铠羟 | 集成到现有流量监测系统架构，完善数据处理流程 |

# 1 文档介绍

## 1.1 文档目的

本文档旨在详细设计一套通过SNMP协议监测交换机关键状态（CPU、内存、接口状态）的技术方案。该方案将无缝集成到现有的DCI数据监测系统中，复用已建立的数据采集、传输、存储和告警管道，并与流量监测系统形成统一架构。

## 1.2 文档范围

本文档涵盖的内容包括：
-   SNMP采集配置设计
-   与流量监测系统的集成方案
-   Prometheus指标规范
-   数据处理流程设计
-   相关的告警规则设计
-   部署与实现细节

## 1.3 文档关联

-   《19-DCI-流量类数据接收及存储技术设计.md》：本设计将与流量数据采集处理系统共享相同的架构
-   《19-01-DCI-流量类数据prometheus指标设计.md》：本设计的指标命名将遵循相同规范
-   《19-02-流量类数据prometheus指标字段说明.md》：本设计的指标标签将采用相同体系
-   《15-02-DCI-dciagent部署与配置设计.md》：本设计将通过修改`telegraf.d`配置实现
-   《13-DCI-Kafka主题规划及负载均衡连接设计.md》：采集的数据将注入该文档定义的Kafka主题
-   《07-DCI-Prometheus和Thanos架构设计.md》：采集的数据最终存储在该架构中
-   《08-DCI-Prometheus指标和标签规范.md》：新指标将遵循此规范

# 2 总体设计

## 2.1 设计目标

1.  实现对交换机CPU使用率的监控
2.  实现对交换机内存使用率的监控
3.  实现对交换机物理接口和逻辑接口（如VLAN、Loopback）管理状态和运行状态的监控
4.  无缝集成到现有的流量监测系统架构中，复用其数据处理流程
5.  所有新指标遵循项目统一的命名和标签规范
6.  在Prometheus中配置相应的告警规则

## 2.2 架构设计

本方案将完全集成到现有的流量监测系统架构中，复用相同的数据采集、传输、处理和存储组件，仅在Telegraf配置层面进行扩展，实现无缝集成。

### 2.2.1 整体架构图

```mermaid
graph TB
    subgraph 客户端
        subgraph "dciagent (客户端)"
            A[Telegraf] -- 采集配置扩展 --> B((CPU/内存<br/>接口状态))
        end
    end

    subgraph 网络设备
        SW[交换机] -- "SNMP (HUAWEI-ENTITY-EXT-MIB)" --> A
        SW -- "SNMP (IF-MIB)" --> A
    end
    
    subgraph "dcimonitor (服务端)"
        subgraph "消息队列"
            K[Kafka]
        end
        
        subgraph "处理服务"
            FD[FlowData处理服务]
        end
        
        subgraph "存储层"
            P[Prometheus]
            MySQL[(MySQL<br/>设备端口映射)]
        end
        
        subgraph "告警层"
            AM[Alertmanager]
        end
        
        subgraph "查询层"
            API[查询API服务]
        end
    end
    
    A -- "JSON Metrics" --> K
    K -- "消费数据" --> FD
    FD -- "设备ID/端口映射查询" --> MySQL
    FD -- "暴露指标" --> P
    P -- "触发告警" --> AM
    API -- "查询指标" --> P
    
    style B fill:#f55,stroke:#333,stroke-width:2px
    style FD fill:#5af,stroke:#333,stroke-width:1px
```

### 2.2.2 重用现有流量监测组件

交换机状态监测与流量监测共享相同的组件和数据流路径：

1. **数据采集**：复用Telegraf采集组件，通过增加新的SNMP配置实现
2. **数据传输**：使用相同的Kafka主题，将状态指标与流量指标一起发送
3. **数据处理**：复用FlowData处理服务，处理CPU/内存/接口状态指标
4. **数据存储**：使用相同的Prometheus存储，相同的查询API服务
5. **设备映射**：复用MySQL数据库中的设备ID和端口映射关系
6. **指标暴露**：沿用相同的标签体系，便于统一查询和告警

## 2.3 数据流与处理流程

```mermaid
sequenceDiagram
    participant Telegraf
    participant Kafka
    participant FlowData
    participant MySQL
    participant Prometheus
    participant Alertmanager

    Note over Telegraf: 扩展SNMP采集配置<br/>增加CPU/内存/接口状态OID
    
    loop 定期轮询 (每1分钟)
        Telegraf->>+Telegraf: 获取SNMP状态数据
    end
    
    Telegraf->>Kafka: 发送采集数据 (JSON)
    Note right of Kafka: Topic: dci.monitor.v1.defaultchannel.flows.snmp
    
    Kafka->>FlowData: 消费数据
    
    FlowData->>MySQL: 查询设备ID和端口映射
    MySQL-->>FlowData: 返回映射信息
    
    Note over FlowData: 添加标准标签:<br/>device_id, device_name, port_id等
    
    FlowData->>Prometheus: 暴露带标签的指标
    
    loop 持续评估
        Prometheus->>Prometheus: 检查告警规则
        alt 触发告警条件
            Prometheus->>Alertmanager: 发送告警
        end
    end
```

# 3 详细设计

## 3.1 SNMP采集配置设计

### 3.1.1 CPU状态采集

使用华为私有MIB `HUAWEI-ENTITY-EXT-MIB` 直接获取CPU使用率。

-   **OID**: `hwEntityCpuUsage` (.*******.4.1.2011.5.25.31.1.1.1.1.5)
-   **说明**: 该OID直接返回实体（如主控板）的CPU占用率的整数值。
-   **关联OID**: 通过`ENTITY-MIB`的`entPhysicalClass` (.*******.2.1.47.1.1.1.1.5) 筛选出实体类型为`9`（设备）的条目，以确保获取的是整机CPU使用率。

### 3.1.2 内存状态采集

使用华为私有MIB `HUAWEI-ENTITY-EXT-MIB` 直接获取内存使用率。

-   **OID**: `hwEntityMemUsage` (.*******.4.1.2011.5.25.31.1.1.1.1.7)
-   **说明**: 该OID直接返回实体（如主控板）的内存占用率的整数值。

### 3.1.3 接口状态采集

接口状态的采集继续使用通用的`IF-MIB`，以保证方案的普遍适用性。

-   **管理状态OID**: `ifAdminStatus` (.*******.2.1.2.2.1.7)。值 `1`=up, `2`=down, `3`=testing。
-   **运行状态OID**: `ifOperStatus` (.*******.2.1.2.2.1.8)。值 `1`=up, `2`=down, `3`=testing, 等。

### 3.1.4 Telegraf配置设计

为了与现有流量数据采集保持一致，配置将遵循相同的格式。我们将增加CPU、内存和接口状态的采集OID，并使用相同的输出设置（输出到Kafka等）。

```toml
# 在现有的[[inputs.snmp]]块中添加
[[inputs.snmp]]
  # ... (继承现有的agents, community等配置) ...

  # CPU和内存状态采集
  [[inputs.snmp.table]]
    name = "dci_switch_entity_status"
    oid = "*******.2.1.47.1.1.1" # ENTITY-MIB::entPhysicalTable

    [[inputs.snmp.table.field]]
      name = "entPhysicalName"
      oid = "*******.2.1.47.1.1.1.1.7"
      is_tag = true

    [[inputs.snmp.table.field]]
      name = "entPhysicalClass"
      oid = "*******.2.1.47.1.1.1.1.5"
      is_tag = true
    
    [[inputs.snmp.table.field]]
      name = "hwEntityCpuUsage"
      oid = "*******.4.1.2011.5.25.31.1.1.1.1.5"

    [[inputs.snmp.table.field]]
      name = "hwEntityMemUsage"
      oid = "*******.4.1.2011.5.25.31.1.1.1.1.7"

  # 接口状态采集 (增加到现有接口采集中)
  [[inputs.snmp.table]]
    name = "dci_switch_interface_status"
    oid = "*******.2.1.2.2"  # IF-MIB::ifTable
    
    [[inputs.snmp.table.field]]
      name = "ifName"
      oid = "*******.2.1.31.1.1.1.1"
      is_tag = true
      
    [[inputs.snmp.table.field]]
      name = "ifIndex"
      oid = "*******.2.1.2.2.1.1"
      is_tag = true

    [[inputs.snmp.table.field]]
      name = "ifAdminStatus"
      oid = "*******.2.1.2.2.1.7"

    [[inputs.snmp.table.field]]
      name = "ifOperStatus"
      oid = "*******.2.1.2.2.1.8"

# Kafka输出配置与流量采集保持一致
[[outputs.kafka]]
  # ... (使用现有的Kafka输出配置) ...
  topic = "dci.monitor.v1.defaultchannel.flows.snmp"
```

## 3.2 指标体系设计

为了与现有的流量指标保持一致性，交换机状态指标将采用相同的命名规范和标签体系。

### 3.2.1 指标命名规范

遵循现有流量指标的命名规范，前缀为 `dci_snmp_`，CPU/内存指标使用 `device_` 前缀，接口状态指标使用 `interface_` 前缀：

- `dci_snmp_device_cpu_usage`: CPU使用率指标
- `dci_snmp_device_memory_usage`: 内存使用率指标
- `dci_snmp_interface_admin_status`: 接口管理状态指标
- `dci_snmp_interface_oper_status`: 接口操作状态指标

### 3.2.2 标签体系设计

所有指标都将包含现有的标准标签，确保与流量指标保持一致：

| 标签名称       | 说明                | 来源                |
|--------------|---------------------|-------------------|
| device_id    | 设备唯一标识符        | MySQL查询         |
| device_name  | 设备名称             | MySQL查询         |
| device_ip    | 设备管理IP地址        | SNMP采集          |
| agent_id     | 采集代理标识符        | Telegraf配置      |
| agent_ip     | 采集代理IP地址        | Telegraf配置      |

**CPU/内存指标特有标签：**

| 标签名称            | 说明                 | 来源                |
|-------------------|----------------------|-------------------|
| entPhysicalName   | 实体名称              | ENTITY-MIB        |
| entPhysicalClass  | 实体类别代码（如9=设备） | ENTITY-MIB        |

**接口状态指标特有标签：**

| 标签名称     | 说明              | 来源                |
|------------|-------------------|-------------------|
| port_id    | 端口唯一标识符      | MySQL查询         |
| port_name  | 端口名称           | MySQL查询         |
| if_index   | 接口索引           | SNMP采集          |
| ifName     | 接口原始名称        | SNMP采集          |
| vni_id     | VNI标识符(可选)     | MySQL查询         |

### 3.2.3 指标详细定义

#### CPU使用率指标

```
dci_snmp_device_cpu_usage
```

**类型**：Gauge
**单位**：百分比(%)
**值范围**：0-100
**采集来源**：HUAWEI-ENTITY-EXT-MIB::hwEntityCpuUsage
**标签**：device_id, device_name, device_ip, agent_id, agent_ip, entPhysicalName, entPhysicalClass
**说明**：实体CPU使用率。通过entPhysicalClass="9"筛选整机CPU。

#### 内存使用率指标

```
dci_snmp_device_memory_usage
```

**类型**：Gauge
**单位**：百分比(%)
**值范围**：0-100
**采集来源**：HUAWEI-ENTITY-EXT-MIB::hwEntityMemUsage
**标签**：device_id, device_name, device_ip, agent_id, agent_ip, entPhysicalName, entPhysicalClass
**说明**：实体内存使用率。通过entPhysicalClass="9"筛选整机内存。

#### 接口管理状态指标

```
dci_snmp_interface_admin_status
```

**类型**：Gauge
**值**：1(up), 2(down), 3(testing)
**采集来源**：IF-MIB::ifAdminStatus
**标签**：device_id, device_name, device_ip, agent_id, agent_ip, port_id, port_name, if_index, ifName, vni_id
**说明**：接口管理状态，表示管理员配置的接口预期状态。

#### 接口操作状态指标

```
dci_snmp_interface_oper_status
```

**类型**：Gauge
**值**：1(up), 2(down), 3(testing), 4(unknown), 5(dormant), 6(notPresent), 7(lowerLayerDown)
**采集来源**：IF-MIB::ifOperStatus
**标签**：device_id, device_name, device_ip, agent_id, agent_ip, port_id, port_name, if_index, ifName, vni_id
**说明**：接口操作状态，表示接口的实际运行状态。

## 3.3 数据处理流程设计

### 3.3.1 流程概述

为了无缝集成到现有流量监测系统中，CPU/内存/接口状态数据将按照相同的流程进行处理：

1. **数据采集**：由Telegraf通过SNMP采集所有指标
2. **数据传输**：通过Kafka发送到现有Topic
3. **数据消费**：由FlowData服务消费并处理
4. **设备ID映射**：通过MySQL查询设备ID和端口映射 
5. **标签添加**：为原始指标添加完整的标签集
6. **指标暴露**：暴露给Prometheus抓取

### 3.3.2 FlowData处理器扩展

现有的FlowData处理器可以通过扩展配置来支持新的CPU/内存/接口状态指标，无需修改核心处理逻辑。具体扩展方式：

1. **指标前缀配置**：在processor配置中添加新指标前缀到白名单
2. **字段映射**：在FlowData处理器中添加CPU/内存/接口状态字段的映射配置
3. **标签生成**：复用现有标签生成逻辑，为新指标生成完整的标签集

### 3.3.3 数据库查询扩展

通过沿用现有的FlowData服务中的设备和端口映射查询逻辑，实现同样的设备ID和端口ID标准化：

1. **设备ID查询**：通过设备IP查询标准化的设备ID
2. **端口ID查询**：通过设备ID和接口名称查询标准化的端口ID
3. **VNI关联**：通过端口ID查询关联的VNI信息

### 3.3.4 配置文件整合

扩展现有的flowdata配置文件，添加新指标相关的处理规则：

```yaml
# 处理器配置
processor:
  # 指标前缀
  metric_prefix: "dci_snmp_"
  # 启用CPU指标
  enable_device_cpu: true
  # 启用内存指标
  enable_device_memory: true
  # 启用接口状态指标
  enable_interface_status: true
  # 现有流量指标配置保持不变
  enable_in_octets: true
  enable_out_octets: true
  enable_errors: true
  enable_discards: true
  # 附加的标签
  metric_labels:
    kubernetes_namespace: "dci"
    kubernetes_service_name: "dcimonitor-flowdata-service"
    data_source_type: "snmp_status"
```

## 3.4 缓存机制设计

为了提高性能并减轻数据库负载，交换机状态监测系统将完全复用现有流量监测系统的缓存机制。

### 3.4.1 缓存复用架构

```mermaid
graph TD
    subgraph "缓存层"
        DB_Cache["数据库查询缓存<br>(设备和端口映射)"]
        API_Cache["API响应缓存<br>(查询结果)"]
    end
    
    subgraph "处理服务组件"
        Mapper["DeviceMapper<br>(映射查询组件)"]
        Processor["FlowDataProcessor<br>(数据处理组件)"]
        API["查询API服务"]
    end
    
    subgraph "状态监测"
        CPU["CPU监测"]
        MEM["内存监测"] 
        IF["接口状态监测"]
    end
    
    subgraph "流量监测"
        FLOW["流量监测"]
    end
    
    CPU --> Processor
    MEM --> Processor
    IF --> Processor
    FLOW --> Processor
    
    Processor -- "设备/端口查询" --> Mapper
    Mapper -- "缓存查询" --> DB_Cache
    Mapper -- "缓存未命中" --> MySQL
    MySQL -- "更新缓存" --> DB_Cache
    
    API -- "指标查询" --> API_Cache
    API -- "缓存未命中" --> Prometheus
    Prometheus -- "更新缓存" --> API_Cache
    
    style DB_Cache fill:#f9f,stroke:#333,stroke-width:2px
    style API_Cache fill:#f9f,stroke:#333,stroke-width:2px
```

### 3.4.2 缓存组件复用

状态监测系统将复用以下现有的缓存组件：

1. **DeviceMapper缓存**：
   - 设备ID映射缓存
   - 端口映射缓存
   - 接口索引映射缓存
   
2. **查询结果缓存**：
   - 设备状态查询结果缓存
   - 接口状态查询结果缓存

### 3.4.3 缓存配置复用

完全复用现有的缓存配置参数，确保一致性：

```yaml
# Mapper配置 - 端口映射查询MySQL缓存TTL
mapper:
  # 设备和端口映射缓存TTL（分钟）
  cache_ttl_minutes: 30
  
  # 缓存容量限制
  cache_capacity: 1000
  
  # 缓存清理间隔（分钟）
  cache_cleanup_interval: 10
```

### 3.4.4 缓存处理时序

```mermaid
sequenceDiagram
    participant MetricData as 原始指标数据
    participant Processor as FlowDataProcessor
    participant Mapper as DeviceMapper
    participant Cache as 本地缓存
    participant DB as MySQL数据库
    
    Note over MetricData,DB: CPU/内存/接口状态指标与流量指标共享相同的缓存处理流程，但使用不同命名空间

    MetricData->>Processor: 传入原始指标数据<br>(CPU/内存/接口状态/流量)
    
    Processor->>Processor: 解析指标类型
    
    alt CPU指标
        Processor->>Mapper: GetDeviceStatus(deviceIP, "cpu")
        Mapper->>Mapper: 构建带命名空间的缓存键<br>(cpu:deviceIP)
    else 内存指标
        Processor->>Mapper: GetDeviceStatus(deviceIP, "mem")
        Mapper->>Mapper: 构建带命名空间的缓存键<br>(mem:deviceIP)
    else 接口状态指标
        Processor->>Mapper: GetPortStatus(deviceIP, portName)
        Mapper->>Mapper: 构建带命名空间的缓存键<br>(port_status:deviceIP:portName)
    else 流量指标
        Processor->>Mapper: GetPortMapping(deviceIP, portName, vniID)
        Mapper->>Mapper: 构建带命名空间的缓存键<br>(flow:deviceIP:portName:vniID)
    end
    
    Mapper->>Cache: 查找缓存项(根据命名空间前缀)
    
    alt 缓存命中
        Cache-->>Mapper: 返回缓存的映射信息
        Mapper->>Mapper: 更新命名空间命中统计
        Mapper-->>Processor: 返回设备/端口映射
    else 缓存未命中
        Cache-->>Mapper: 未找到缓存项
        Mapper->>Mapper: 更新命名空间未命中统计
        
        alt CPU/内存指标查询
            Mapper->>DB: 查询设备信息(dci_device表)
        else 接口状态查询
            Mapper->>DB: 查询接口状态(dci_logic_port_device表)
        else 流量指标查询
            Mapper->>DB: 查询流量映射(多表关联查询)
        end
        
        DB-->>Mapper: 返回查询结果
        
        Mapper->>Mapper: 根据指标类型决定命名空间
        Mapper->>Cache: 存储查询结果到指定命名空间的缓存
        Note over Mapper,Cache: 使用命名空间前缀<br>设置统一TTL=30分钟
        
        Mapper-->>Processor: 返回设备/端口映射
    end
    
    Processor->>Processor: 根据指标类型添加相应标签
    Processor->>Processor: 生成最终的监测指标
```

### 3.4.5 缓存逻辑图

```mermaid
flowchart TB
    subgraph 缓存管理
        subgraph 不同指标类型
            流量指标["<b>流量指标</b><br>原始字段: ifHCInOctets/ifHCOutOctets"]
            CPU指标["<b>CPU指标</b><br>原始字段: cpmCPUTotal5min"]
            内存指标["<b>内存指标</b><br>原始字段: ciscoMemoryPoolUsed"]
            接口指标["<b>接口状态指标</b><br>原始字段: ifAdminStatus/ifOperStatus"]
        end
        
        subgraph 命名空间缓存键生成
            流量键["flow:192.168.1.1:GE1/0/1:1001"]
            CPU键["cpu:192.168.1.1"]
            内存键["mem:192.168.1.1"]
            接口键["port_status:192.168.1.1:GE1/0/1"]
        end
        
        subgraph 缓存存储分区
            流量区["流量数据缓存 (flow:*)"]
            CPU区["CPU数据缓存 (cpu:*)"]
            内存区["内存数据缓存 (mem:*)"]
            接口区["接口状态缓存 (port_status:*)"]
        end
    end
    
    subgraph 缓存操作
        缓存写入["缓存写入<br>(同一结构体，不同命名空间前缀)"]
        过期清理["过期清理<br>(统一后台任务，所有命名空间)"]
        命中统计["命中/未命中统计<br>(按命名空间分组)"]
    end

    流量指标 -->|生成缓存键| 流量键
    CPU指标 -->|生成缓存键| CPU键
    内存指标 -->|生成缓存键| 内存键
    接口指标 -->|生成缓存键| 接口键
    
    流量键 -->|存储| 流量区
    CPU键 -->|存储| CPU区
    内存键 -->|存储| 内存区
    接口键 -->|存储| 接口区
    
    流量区 --> 缓存写入
    CPU区 --> 缓存写入
    内存区 --> 缓存写入
    接口区 --> 缓存写入
    
    缓存写入 --> 过期清理
    缓存写入 --> 命中统计
```

### 3.4.6 缓存失效策略

状态监测系统将复用流量监测系统的缓存失效策略：

1. **基于时间的失效**：
   - 默认TTL为30分钟
   - 可通过配置文件调整

2. **基于容量的失效**：
   - 当缓存项数量达到上限时，使用LRU策略淘汰最久未使用的项
   - 可通过配置文件调整
   
3. **主动失效触发**：
   - 设备配置变更时触发相关缓存项失效
   - 端口配置变更时触发相关缓存项失效
   - 提供主动失效接口，用于手动或调动触发主动缓存失效

### 3.4.7 缓存共享与隔离

为了优化性能，同时确保数据隔离，采用以下策略：

1. **共享组件**：
   - 复用相同的缓存实现类
   - 复用相同的缓存配置参数
   - 复用相同的失效策略

2. **数据隔离**：
   - 为不同类型的查询使用独立的缓存命名空间
   - CPU/内存状态查询：`device_status`命名空间
   - 接口状态查询：`interface_status`命名空间
   - 流量查询：`flow_data`命名空间

## 3.5 告警规则设计

为新的状态指标配置告警规则，并将其集成到现有的Prometheus告警体系中：

```yaml
groups:
- name: dci_switch_status_alerts
  rules:
  - alert: DeviceCPUHighUsage
    expr: dci_snmp_device_cpu_usage{entPhysicalClass="9"} > 80
    for: 5m
    labels:
      severity: warning
      category: device_status
    annotations:
      summary: "交换机 {{ $labels.device_name }} CPU使用率过高"
      description: "设备 {{ $labels.device_name }} ({{ $labels.device_ip }}) 的CPU使用率为 {{ $value }}%，持续超过5分钟。"

  - alert: DeviceMemoryHighUsage
    expr: dci_snmp_device_memory_usage{entPhysicalClass="9"} > 85
    for: 10m
    labels:
      severity: critical
      category: device_status
    annotations:
      summary: "交换机 {{ $labels.device_name }} 内存使用率过高"
      description: "设备 {{ $labels.device_name }} ({{ $labels.device_ip }}) 的内存使用率为 {{ $value }}%，持续超过10分钟。"

  - alert: InterfaceStatusMismatch
    expr: dci_snmp_interface_admin_status == 1 and dci_snmp_interface_oper_status == 2
    for: 2m
    labels:
      severity: warning
      category: interface_status
    annotations:
      summary: "交换机 {{ $labels.device_name }} 接口 {{ $labels.port_name }} 状态异常"
      description: "设备 {{ $labels.device_name }} ({{ $labels.device_ip }}) 的接口 {{ $labels.port_name }} 管理状态为UP但实际状态为DOWN，可能存在物理链路问题。"
```

## 3.6 查询API整合

为了提供统一的查询体验，我们将整合现有的查询API，支持CPU/内存/接口状态查询：

1. **设备状态查询**：通过现有API新增设备状态查询接口
2. **接口状态查询**：通过现有接口查询API整合接口状态信息
3. **指标查询示例**：

```
# 设备CPU使用率查询
dci_snmp_device_cpu_usage{device_id="210", entPhysicalClass="9"}

# 内存使用率查询
dci_snmp_device_memory_usage{device_id="210", entPhysicalClass="9"}

# 接口状态查询
dci_snmp_interface_admin_status{device_id="210", port_name="10GE1/0/1"}
dci_snmp_interface_oper_status{device_id="210", port_name="10GE1/0/1"}

# 查询所有DOWN接口
dci_snmp_interface_oper_status{device_id="210"} == 2
```

## 3.7 记录规则设计

参考流量指标设计，为CPU/内存/接口状态指标添加记录规则，预先计算常用查询：

```yaml
groups:
- name: dci_device_status_rules
  interval: 1m
  rules:
  - record: device:cpu_usage
    expr: dci_snmp_device_cpu_usage{entPhysicalClass="9"}
    labels:
      job: dci-snmp-status

  - record: device:memory_usage
    expr: dci_snmp_device_memory_usage{entPhysicalClass="9"}
    labels:
      job: dci-snmp-status

  - record: interface:admin_status
    expr: dci_snmp_interface_admin_status
    labels:
      job: dci-snmp-status

  - record: interface:oper_status
    expr: dci_snmp_interface_oper_status
    labels:
      job: dci-snmp-status
```

## 3.8 指标名称层次结构设计

为确保指标体系的统一性和规范性，我们对CPU、内存和接口状态指标设计了完整的命名层次结构，与现有流量指标保持一致的组织方式，方便后续扩展和维护。

### 3.8.1 指标命名模式

所有指标遵循统一的命名模式：
```
dci_snmp_<资源类型>_<指标类别>_<指标名称>
```

- **dci_snmp**：前缀，表明数据来源是SNMP协议
- **资源类型**：device（设备级指标）或interface（接口级指标）
- **指标类别**：cpu、memory、status等分类
- **指标名称**：具体监测对象，如usage、admin_status等

### 3.8.2 CPU指标层次结构

```
dci_snmp_device_cpu_usage           # 基础CPU使用率指标
dci_snmp_device_cpu_usage_max       # 最高CPU使用率（1分钟内）
dci_snmp_device_cpu_usage_min       # 最低CPU使用率（1分钟内）
dci_snmp_device_cpu_usage_avg       # 平均CPU使用率（1分钟内）
dci_snmp_device_cpu_usage_5min      # 5分钟平均CPU使用率
```

### 3.8.3 内存指标层次结构

```
dci_snmp_device_memory_usage        # 内存使用率
dci_snmp_device_memory_total        # 总内存容量（字节）
dci_snmp_device_memory_used         # 已使用内存（字节）
dci_snmp_device_memory_free         # 可用内存（字节）
```

### 3.8.4 接口状态指标层次结构

```
dci_snmp_interface_admin_status     # 接口管理状态（配置状态）
dci_snmp_interface_oper_status      # 接口运行状态（实际状态）
dci_snmp_interface_status_mismatch  # 状态不匹配指标（1表示不匹配）
dci_snmp_interface_last_change      # 最后状态变更时间
dci_snmp_interface_speed            # 接口速率（bps）
dci_snmp_interface_mtu              # 接口MTU大小
```

### 3.8.5 指标标注属性

为每个指标提供统一的标注属性：

```yaml
- name: "dci_snmp_device_cpu_usage"
  type: "gauge"
  unit: "percent"
  help: "设备CPU使用率百分比"
  source: "SNMP"
  stability: "stable"
  
- name: "dci_snmp_device_memory_usage"
  type: "gauge"
  unit: "percent"
  help: "设备内存使用率百分比"
  source: "SNMP"
  stability: "stable"
  
- name: "dci_snmp_interface_admin_status"
  type: "gauge"
  unit: "enum"
  help: "接口管理状态（1=up, 2=down, 3=testing）"
  source: "SNMP"
  stability: "stable"
  
- name: "dci_snmp_interface_oper_status"
  type: "gauge"
  unit: "enum"
  help: "接口运行状态（1=up, 2=down, 3=testing, 4=unknown, 5=dormant, 6=notPresent, 7=lowerLayerDown）"
  source: "SNMP"
  stability: "stable"
```

## 3.9 数据映射与数据库设计

本方案中的设备、端口及 VNI 等信息的识别与映射，完全复用《20-DCI-MySql数据库同步-设备端口VNI基础信息.md》文档中定义的数据表结构和同步机制。处理服务通过查询 `dci_monitor` 数据库中的 `dci_device`、`dci_logic_port_device` 等表，完成设备 IP 和接口索引到系统标准 ID 的转换。

### 3.9.1 数据库复用策略

交换机状态监测与流量监测在数据库使用上共享相同的表结构和映射逻辑，主要包括：

1. **设备级指标 (CPU/内存)**：
   - 通过 `device_management_ip`（数据库字段）查询 `dci_device` 表获取 `device_id` 和 `device_name`
   - 复用流量监测中的设备标识符映射逻辑

2. **接口级指标 (接口状态)**：
   - 通过 `device_management_ip`（数据库字段）和 `ifIndex` 查询 `dci_logic_port_device` 等关联表
   - 获取 `device_id`、`port_id`、`logic_port_id` 等标准化 ID
   - 完全复用流量监测中的端口映射逻辑

### 3.9.2 数据库映射流程

```mermaid
graph TD
    A[SNMP状态数据<br>CPU/内存/接口状态] --> B{设备级或接口级?}
    
    B -->|设备级| C[设备映射流程]
    B -->|接口级| D[接口映射流程]
    
    subgraph "设备映射流程"
        C --> C1["查询: dci_device<br>条件: device_management_ip"]
        C1 --> C2["获取: device_id, device_name"]
    end
    
    subgraph "接口映射流程"
        D --> D1["查询: dci_device<br>条件: device_management_ip"]
        D1 --> D2["获取: device_id"]
        D2 --> D3["查询: dci_logic_port_device<br>条件: device_id, ifIndex/port"]
        D3 --> D4["获取: port_id, if_index"]
        D4 --> D5["查询: dci_node, dci_node_business<br>条件: device_id, port_id"]
        D5 --> D6["获取: logic_port_id, vni_id"]
    end
    
    C2 --> E[生成带标准ID的指标]
    D6 --> E
    
    style C1 fill:#f9f,stroke:#333,stroke-width:1px
    style D1 fill:#f9f,stroke:#333,stroke-width:1px
    style D3 fill:#f9f,stroke:#333,stroke-width:1px
    style D5 fill:#f9f,stroke:#333,stroke-width:1px
```

### 3.9.3 数据库表复用

交换机状态监测复用以下数据库表及其关系：

| 表名 | 用途 | 关键字段 |
|------|------|---------|
| dci_device | 设备基本信息 | id, device_name, device_management_ip |
| dci_logic_port_device | 设备物理端口信息 | id, device_id, port, if_index |
| dci_node | 设备节点信息 | id, device_id |
| dci_node_business | 业务关联信息 | node_id, logic_port_id, vni |
| dci_logic_port | 逻辑端口信息 | id, name |

### 3.9.4 映射查询优化

状态监测系统复用流量监测系统中已实现的查询优化策略：

1. **分步查询**：将复杂的多表JOIN拆分为多个简单查询，便于缓存和维护
2. **内存缓存**：使用设备管理IP（`device_management_ip`）和端口名作为键，缓存查询结果，减少数据库负载
3. **批量查询**：对于接口状态，支持批量查询多个接口的映射信息
4. **异步更新**：缓存失效后异步更新，不阻塞主处理流程

### 3.9.5 数据库字段与指标标签的映射关系

为确保术语使用的准确性，以下是数据库字段与最终生成的Prometheus指标标签之间的映射关系：

| 数据库字段 | Prometheus指标标签 | 说明 |
|----------|-----------------|------|
| device_management_ip | device_ip | 设备管理IP地址，在数据库中字段名为`device_management_ip`，在指标标签中简化为`device_ip` |
| id (dci_device表) | device_id | 设备唯一标识符 |
| device_name | device_name | 设备名称 |
| id (dci_logic_port_device表) | port_id | 端口唯一标识符 |
| if_index | if_index | 接口索引 |
| port | port_name | 端口名称，在数据库中字段名为`port`，在指标标签中为`port_name` |
| vni | vni_id | VNI标识符 |

这种映射关系确保了从数据库到最终指标的一致性转换，同时保持了与现有流量指标系统的兼容性。

## 3.10 数据类型识别与处理逻辑

由于流量数据和状态监测数据（CPU、内存、接口状态）共用同一个 Kafka topic (`dci.monitor.v1.defaultchannel.flows.snmp`)，系统需要一种机制来区分这两类数据并应用相应的处理逻辑。

### 3.10.1 数据区分策略

系统通过以下三种方式识别和区分不同类型的数据：

1. **指标名称前缀识别**：
   - 流量指标：`ifHCInOctets`、`ifHCOutOctets`、`ifInErrors`、`ifOutErrors` 等
   - CPU指标：`hwEntityCpuUsage` 或 `cpmCPUTotal5min` 等
   - 内存指标：`hwEntityMemUsage` 或 `ciscoMemoryPoolUsed` 等
   - 接口状态指标：`ifAdminStatus`、`ifOperStatus` 等

2. **Telegraf 表名识别**：
   - 流量数据：表名为 `dci_switch_interface_traffic`
   - CPU/内存数据：表名为 `dci_switch_entity_status`
   - 接口状态数据：表名为 `dci_switch_interface_status`

3. **数据源类型标签**：
   - 在 Telegraf 配置中为不同类型的数据添加 `data_source_type` 标签
   - 流量数据：`data_source_type = "flow_data"`
   - CPU/内存数据：`data_source_type = "device_status"`
   - 接口状态数据：`data_source_type = "interface_status"`

### 3.10.2 处理流程设计

```mermaid
flowchart TD
    A[Kafka消息] --> B{解析JSON消息}
    B --> C{检查数据类型}
    
    C -->|表名匹配| D[确定数据类型]
    C -->|指标名称匹配| D
    C -->|data_source_type标签| D
    
    D -->|流量数据| E[流量处理逻辑]
    D -->|CPU/内存数据| F[设备状态处理逻辑]
    D -->|接口状态数据| G[接口状态处理逻辑]
    
    E --> H[设备/端口ID映射]
    F --> I[设备ID映射]
    G --> J[设备/端口ID映射]
    
    H --> K[生成流量指标]
    I --> L[生成设备状态指标]
    J --> M[生成接口状态指标]
    
    K --> N[暴露Prometheus指标]
    L --> N
    M --> N
    
    style D fill:#f96,stroke:#333,stroke-width:2px
    style E fill:#9cf,stroke:#333,stroke-width:1px
    style F fill:#f99,stroke:#333,stroke-width:1px
    style G fill:#9f9,stroke:#333,stroke-width:1px
```

### 3.10.3 处理器配置扩展

为支持多类型数据处理，`FlowDataProcessor` 配置进行如下扩展：

```yaml
processor:
  # 指标类型识别配置
  metrics_identification:
    # 表名映射
    table_mappings:
      - table_name: "dci_switch_interface_traffic"
        data_type: "flow"
      - table_name: "dci_switch_entity_status"
        data_type: "device_status"
      - table_name: "dci_switch_interface_status"
        data_type: "interface_status"
    
    # 字段名映射
    field_mappings:
      # 流量相关字段
      - field_pattern: "ifHC(In|Out)Octets"
        data_type: "flow"
      - field_pattern: "if(In|Out)(Errors|Discards)"
        data_type: "flow"
      # CPU相关字段
      - field_pattern: "(hwEntityCpuUsage|cpmCPUTotal5min)"
        data_type: "device_status"
        metric_type: "cpu"
      # 内存相关字段
      - field_pattern: "(hwEntityMemUsage|ciscoMemoryPoolUsed)"
        data_type: "device_status"
        metric_type: "memory"
      # 接口状态相关字段
      - field_pattern: "if(Admin|Oper)Status"
        data_type: "interface_status"
```

### 3.10.4 处理逻辑代码设计

处理器根据识别到的数据类型，应用不同的处理逻辑：

```go
// 简化的处理逻辑伪代码
func (p *FlowDataProcessor) ProcessData(data []byte) error {
    // 解析JSON数据
    var flowData map[string]interface{}
    if err := json.Unmarshal(data, &flowData); err != nil {
        return err
    }
    
    // 识别数据类型
    dataType := p.identifyDataType(flowData)
    
    // 根据数据类型应用不同处理逻辑
    switch dataType {
    case "flow":
        return p.processFlowData(flowData)
    case "device_status":
        return p.processDeviceStatusData(flowData)
    case "interface_status":
        return p.processInterfaceStatusData(flowData)
    default:
        return fmt.Errorf("unknown data type")
    }
}

// 数据类型识别函数
func (p *FlowDataProcessor) identifyDataType(data map[string]interface{}) string {
    // 1. 检查data_source_type标签
    if tags, ok := data["tags"].(map[string]interface{}); ok {
        if sourceType, ok := tags["data_source_type"].(string); ok {
            switch sourceType {
            case "flow_data":
                return "flow"
            case "device_status":
                return "device_status"
            case "interface_status":
                return "interface_status"
            }
        }
    }
    
    // 2. 检查表名
    if name, ok := data["name"].(string); ok {
        switch name {
        case "dci_switch_interface_traffic":
            return "flow"
        case "dci_switch_entity_status":
            return "device_status"
        case "dci_switch_interface_status":
            return "interface_status"
        }
    }
    
    // 3. 检查字段名
    if fields, ok := data["fields"].(map[string]interface{}); ok {
        for fieldName := range fields {
            // 流量字段检查
            if strings.Contains(fieldName, "Octets") || 
               strings.Contains(fieldName, "Errors") ||
               strings.Contains(fieldName, "Discards") {
                return "flow"
            }
            
            // CPU字段检查
            if strings.Contains(fieldName, "CpuUsage") ||
               strings.Contains(fieldName, "CPUTotal") {
                return "device_status"
            }
            
            // 内存字段检查
            if strings.Contains(fieldName, "MemUsage") ||
               strings.Contains(fieldName, "MemoryPool") {
                return "device_status"
            }
            
            // 接口状态字段检查
            if strings.Contains(fieldName, "AdminStatus") ||
               strings.Contains(fieldName, "OperStatus") {
                return "interface_status"
            }
        }
    }
    
    // 默认当作流量数据处理
    return "flow"
}
```

### 3.10.5 数据处理映射表

下表总结了不同类型数据的处理映射关系：

| 数据类型 | 识别方式 | 处理函数 | 映射查询 | 生成指标前缀 |
|---------|---------|---------|---------|------------|
| 流量数据 | 表名=`dci_switch_interface_traffic`<br>字段包含=`Octets/Errors/Discards`<br>标签=`flow_data` | processFlowData | 设备+端口映射 | dci_snmp_flow_ |
| CPU数据 | 表名=`dci_switch_entity_status`<br>字段包含=`CpuUsage/CPUTotal`<br>标签=`device_status` | processDeviceStatusData | 设备映射 | dci_snmp_device_cpu_ |
| 内存数据 | 表名=`dci_switch_entity_status`<br>字段包含=`MemUsage/MemoryPool`<br>标签=`device_status` | processDeviceStatusData | 设备映射 | dci_snmp_device_memory_ |
| 接口状态数据 | 表名=`dci_switch_interface_status`<br>字段包含=`AdminStatus/OperStatus`<br>标签=`interface_status` | processInterfaceStatusData | 设备+端口映射 | dci_snmp_interface_ |

这种设计确保了系统能够准确区分和处理从同一 Kafka topic 中接收到的不同类型数据，同时保持了处理逻辑的模块化和可扩展性。

# 4 部署与实现

## 4.1 部署架构

本方案将完全复用现有的流量监测系统部署架构，无需额外的服务组件：

1. **客户端**：
   - 通过dciagent管理的Telegraf增加采集配置
   - 复用现有Kafka传输配置

2. **服务端**：
   - 复用现有FlowData处理服务，增加对CPU/内存/接口状态指标的处理
   - 复用现有MySQL数据库中的设备和端口映射关系
   - 复用现有Prometheus存储和查询API

## 4.2 配置文件更新

针对不同环境，需要更新以下配置文件：

1. **本地测试环境**：
   - 更新`dci-monitor/src/dcimonitor/config/dcimonitor-flowdata.yaml`，添加CPU/内存/接口状态指标处理配置

2. **云端环境**：
   - 更新`dci-monitor/src/dcimonitor/dcimonitor-flowdata/dcimonitor-flowdata-k8s.yaml`，确保配置一致

## 4.3 代码集成

为了支持新的指标类型，需要在FlowData处理服务中进行以下扩展：

1. **配置结构扩展**：增加CPU/内存/接口状态相关的配置选项
2. **处理逻辑扩展**：增加专用的指标处理和标签生成逻辑
3. **指标注册**：在MetricExporter中注册新的指标

## 4.4 相关代码文件

以下是实现交换机状态监测功能需要修改的关键代码文件：

### 4.4.1 配置相关文件

1. **Telegraf配置模板**:
   - `dci_self_test/config/telegraf/telegraf.d/inputs/snmp-huawei-test.conf`: 测试环境SNMP采集配置
   - `dciagent/configs/templates/telegraf.d/inputs/snmp-status.conf`: 生产环境SNMP状态采集配置模板

2. **FlowData配置文件**:
   - `dci-monitor/src/dcimonitor/config/dcimonitor-flowdata.yaml`: 本地测试环境配置
   - `dci-monitor/src/dcimonitor/dcimonitor-flowdata/dcimonitor-flowdata-config.yaml`: 生产环境配置

### 4.4.2 核心实现文件

1. **配置结构定义**:
   - `dci-monitor/src/dcimonitor/internal/flowdata/config.go`: 需扩展Config结构体，增加状态监测相关配置

   ```go
   // 需添加的配置结构体字段
   type Config struct {
       // ... 现有字段 ...
       
       // 设备状态监测配置
       EnableDeviceCPU      bool   `yaml:"enable_device_cpu"`
       EnableDeviceMemory   bool   `yaml:"enable_device_memory"`
       EnableInterfaceStatus bool  `yaml:"enable_interface_status"`
       
       // 指标识别配置
       MetricsIdentification MetricsIdentificationConfig `yaml:"metrics_identification"`
   }
   
   // 指标识别配置
   type MetricsIdentificationConfig struct {
       TableMappings []TableMapping `yaml:"table_mappings"`
       FieldMappings []FieldMapping `yaml:"field_mappings"`
   }
   ```

2. **数据处理核心文件**:
   - `dci-monitor/src/dcimonitor/internal/flowdata/processor.go`: 需扩展处理逻辑，增加状态监测数据处理

   ```go
   // 需添加的处理方法
   func (p *Processor) processDeviceStatusData(data map[string]interface{}) error {
       // 设备状态(CPU/内存)处理逻辑
       // ...
   }
   
   func (p *Processor) processInterfaceStatusData(data map[string]interface{}) error {
       // 接口状态处理逻辑
       // ...
   }
   
   // 数据类型识别方法
   func (p *Processor) identifyDataType(data map[string]interface{}) string {
       // 实现3.10.4节中的识别逻辑
       // ...
   }
   ```

3. **指标定义文件**:
   - `dci-monitor/src/dcimonitor/internal/flowdata/metrics.go`: 需定义新的状态监测指标

   ```go
   // 需添加的指标定义
   var (
       // CPU使用率指标
       deviceCPUUsage = prometheus.NewGaugeVec(
           prometheus.GaugeOpts{
               Name: "dci_snmp_device_cpu_usage",
               Help: "设备CPU使用率百分比",
           },
           []string{"device_id", "device_name", "device_ip", "agent_id", "agent_ip", "entPhysicalName", "entPhysicalClass"},
       )
       
       // 内存使用率指标
       deviceMemoryUsage = prometheus.NewGaugeVec(
           prometheus.GaugeOpts{
               Name: "dci_snmp_device_memory_usage",
               Help: "设备内存使用率百分比",
           },
           []string{"device_id", "device_name", "device_ip", "agent_id", "agent_ip", "entPhysicalName", "entPhysicalClass"},
       )
       
       // 接口管理状态指标
       interfaceAdminStatus = prometheus.NewGaugeVec(
           prometheus.GaugeOpts{
               Name: "dci_snmp_interface_admin_status",
               Help: "接口管理状态（1=up, 2=down, 3=testing）",
           },
           []string{"device_id", "device_name", "device_ip", "agent_id", "agent_ip", "port_id", "port_name", "if_index", "ifName", "vni_id"},
       )
       
       // 接口运行状态指标
       interfaceOperStatus = prometheus.NewGaugeVec(
           prometheus.GaugeOpts{
               Name: "dci_snmp_interface_oper_status",
               Help: "接口运行状态（1=up, 2=down, 3=testing, 4=unknown, 5=dormant, 6=notPresent, 7=lowerLayerDown）",
           },
           []string{"device_id", "device_name", "device_ip", "agent_id", "agent_ip", "port_id", "port_name", "if_index", "ifName", "vni_id"},
       )
   )
   
   // 注册指标
   func init() {
       // 注册现有流量指标
       // ...
       
       // 注册状态监测指标
       prometheus.MustRegister(deviceCPUUsage)
       prometheus.MustRegister(deviceMemoryUsage)
       prometheus.MustRegister(interfaceAdminStatus)
       prometheus.MustRegister(interfaceOperStatus)
   }
   ```

4. **数据映射文件**:
   - `dci-monitor/src/dcimonitor/internal/flowdata/mapper.go`: 需扩展映射逻辑，支持状态监测数据

   ```go
   // 需添加的映射方法
   func (m *Mapper) MapDeviceStatus(deviceIP string, statusType string) (*DeviceStatusMapping, error) {
       // 设备状态映射逻辑
       // ...
   }
   
   func (m *Mapper) MapInterfaceStatus(deviceIP string, ifIndex string) (*InterfaceStatusMapping, error) {
       // 接口状态映射逻辑
       // ...
   }
   ```

### 4.4.3 测试相关文件

1. **单元测试文件**:
   - `dci-monitor/src/dcimonitor/internal/flowdata/processor_test.go`: 处理器单元测试
   - `dci-monitor/src/dcimonitor/internal/flowdata/mapper_test.go`: 映射器单元测试

2. **集成测试文件**:
   - `dci_self_test/scripts/snmp/status_test.sh`: 状态监测集成测试脚本

## 4.5 实现注意事项

### 4.5.1 与现有数据库结构的集成

交换机状态监测功能需要与《20-01-DCI-Mysql设备-端口-逻辑端口-VNI关联关系.md》文档中定义的数据库结构保持一致。特别注意以下几点：

1. **设备标识映射**:
   - 使用 `dci_device` 表中的 `device_management_ip` 字段匹配SNMP采集的设备IP
   - 将 `dci_device` 表中的 `id` 字段作为标准化的 `device_id` 标签值

2. **端口标识映射**:
   - 使用 `dci_logic_port_device` 表中的 `if_index` 字段匹配SNMP采集的接口索引
   - 将 `dci_logic_port_device` 表中的 `id` 字段作为标准化的 `port_id` 标签值

3. **VNI关联**:
   - 通过 `dci_node_business` 表获取端口关联的VNI信息
   - 确保VNI标签与流量指标中的VNI标签保持一致

### 4.5.2 指标命名与标签一致性

为确保与现有流量指标体系的一致性，需要注意以下几点：

1. **指标前缀统一**:
   - 所有SNMP采集的指标统一使用 `dci_snmp_` 前缀
   - 设备级指标使用 `device_` 子前缀
   - 接口级指标使用 `interface_` 子前缀

2. **标签命名统一**:
   - 设备标识统一使用 `device_id`、`device_name`、`device_ip` 标签
   - 端口标识统一使用 `port_id`、`port_name`、`if_index` 标签
   - 采集代理统一使用 `agent_id`、`agent_ip` 标签

3. **特殊标签处理**:
   - 确保 `entPhysicalClass` 标签值为字符串类型，便于查询筛选
   - 接口状态值应保持为数值，便于比较和告警规则设置

### 4.5.3 数据类型识别优先级

在实现3.10节中的数据类型识别逻辑时，应遵循以下优先级：

1. **最高优先级**: `data_source_type` 标签值
2. **次高优先级**: Telegraf表名 (`name` 字段)
3. **最低优先级**: 指标字段名特征匹配

这样可以确保在配置正确的情况下，系统能够准确区分不同类型的数据。

### 4.5.4 性能优化考虑

为确保状态监测功能不影响现有流量监测性能，应注意以下几点：

1. **缓存共享与隔离**:
   - 复用相同的缓存实现，但使用不同的命名空间
   - 确保缓存键的唯一性，避免不同类型数据之间的冲突

2. **批量处理**:
   - 对于接口状态数据，应支持批量处理多个接口的状态
   - 复用现有的批量数据库查询机制，减少数据库连接开销

3. **异步处理**:
   - 考虑将状态数据处理与流量数据处理分离为独立的goroutine
   - 使用工作池模式，控制并发处理的数量

### 4.5.5 告警规则部署

在实现完成后，需要将3.5节中定义的告警规则部署到Prometheus中：

1. 更新 `prometheus-rules-configmap.yaml` 文件，添加状态监测相关的告警规则
2. 应用更新后的ConfigMap到Kubernetes集群
3. 验证告警规则是否正常生效

### 4.5.6 指标注册与初始化

为支持交换机状态监测功能，需要在现有的 `flowdata/metrics.go` 文件中添加相关指标的注册代码。这些修改保持了与流量指标相同的结构和风格，同时增加了对设备状态和接口状态的支持。

#### 指标注册函数扩展

在 `metrics.go` 文件中的 `RegisterMetrics` 函数中添加设备状态和接口状态指标的注册：

```go
// RegisterMetrics 注册所有预定义指标
func (m *MetricExporter) RegisterMetrics(prefix string) {
    // 记录指标前缀
    m.logger.Info("注册Prometheus指标", zap.String("prefix", prefix))

    // 定义所有流量相关的指标名称列表
    flowMetricFields := []string{
        "ifHCInOctets",
        "ifHCOutOctets",
    }

    // 定义设备状态指标列表 (新增)
    deviceStatusMetricFields := []string{
        "device_cpu_usage",
        "device_memory_usage",
    }

    // 定义接口状态指标列表 (新增)
    interfaceStatusMetricFields := []string{
        "interface_admin_status",
        "interface_oper_status",
    }

    // 定义设备级指标标签 (新增)
    deviceMetricLabels := []string{
        "device_id", "device_name", "device_ip", "agent_id", "agent_ip",
        "entPhysicalName", "entPhysicalClass",
    }

    // 定义接口级指标标签
    interfaceMetricLabels := []string{
        "device_id", "device_name", "device_ip", "agent_id", "agent_ip",
        "port_id", "logic_port_id", "port_name", "if_index", "vni_id",
        "ifName", "interface_speed",
    }

    // 注册所有流量指标 (保持不变)
    for _, field := range flowMetricFields {
        metricName := prefix + "flow_" + field
        m.RegisterGauge(
            metricName,
            "SNMP interface metric for " + field,
            interfaceMetricLabels,
        )
    }

    // 注册设备状态指标 (新增)
    for _, field := range deviceStatusMetricFields {
        metricName := prefix + field
        m.RegisterGauge(
            metricName,
            "SNMP device status metric for " + field,
            deviceMetricLabels,
        )
    }

    // 注册接口状态指标 (新增)
    for _, field := range interfaceStatusMetricFields {
        metricName := prefix + field
        m.RegisterGauge(
            metricName,
            "SNMP interface status metric for " + field,
            interfaceMetricLabels,
        )
    }

    // 注册处理器统计指标 (保持不变)
    procMsgTotalName := prefix + "processor_messages_total"
    m.RegisterCounter(
        procMsgTotalName,
        "Total number of messages processed",
        []string{},
    )

    // ... 其他处理器统计指标 ...

    // 打印注册的所有指标名称摘要 (更新)
    m.logger.Info("已注册核心Prometheus指标")
    m.logger.Info("已预注册所有流量相关指标", zap.Int("count", len(flowMetricFields)))
    m.logger.Info("已预注册所有设备状态指标", zap.Int("count", len(deviceStatusMetricFields)))
    m.logger.Info("已预注册所有接口状态指标", zap.Int("count", len(interfaceStatusMetricFields)))
    
    // 添加设备状态和接口状态查询示例
    m.logger.Info("可用的PromQL查询示例",
        zap.String("入向流量(bps)", "rate("+prefix+"flow_ifHCInOctets[5m]) * 8"),
        zap.String("出向流量(bps)", "rate("+prefix+"flow_ifHCOutOctets[5m]) * 8"),
        zap.String("CPU使用率(%)", prefix+"device_cpu_usage"),
        zap.String("内存使用率(%)", prefix+"device_memory_usage"),
        zap.String("接口状态不一致", prefix+"interface_admin_status == 1 and "+prefix+"interface_oper_status == 2"))
}
```

#### 初始化新指标

由于需要使用统一的指标前缀，应在实例化 `FlowDataProcessor` 时使用一致的前缀配置：

```go
// NewDeviceStatusProcessor 创建设备状态处理器
func NewDeviceStatusProcessor(config *ProcessorConfig, mapper DeviceMapper, exporter MetricsExporter, zapLogger *zap.Logger) *DeviceStatusProcessor {
    if config == nil {
        config = &ProcessorConfig{
            MetricPrefix:           "dci_snmp_",  // 使用统一的前缀
            EnableDeviceCPU:        true,
            EnableDeviceMemory:     true,
            EnableInterfaceStatus:  true,
            MetricLabels:           make(map[string]string),
        }
    }
    
    // ... 其他初始化代码 ...
}
```

### 4.5.7 Kafka主题隔离与配置

为了确保数据流的独立性和可维护性，需要为设备状态监控创建独立的Kafka主题，与流量主题隔离。

#### 新Kafka主题定义

为设备状态监控定义以下主题：

```
dci.monitor.v1.defaultchannel.status.device     # 设备级状态数据 (CPU/内存)
dci.monitor.v1.defaultchannel.status.interface  # 接口状态数据
```

与现有的流量数据主题隔离：

```
dci.monitor.v1.defaultchannel.flows.snmp        # 流量数据 (现有)
```

#### Telegraf配置样例

下面是新的Telegraf配置样例，根据数据类型将指标路由到不同的Kafka主题：

```toml
# telegraf.d/inputs/snmp-device-status.conf
[[inputs.snmp]]
  agents = ["10.36.46.188:161"]
  version = 2
  community = "public"
  interval = "60s"
  timeout = "10s"
  retries = 3
  
  # CPU和内存状态采集
  [[inputs.snmp.table]]
    name = "dci_switch_entity_status"
    oid = "*******.2.1.47.1.1.1" # ENTITY-MIB::entPhysicalTable

    [[inputs.snmp.table.field]]
      name = "entPhysicalName"
      oid = "*******.2.1.47.1.1.1.1.7"
      is_tag = true

    [[inputs.snmp.table.field]]
      name = "entPhysicalClass"
      oid = "*******.2.1.47.1.1.1.1.5"
      is_tag = true
    
    [[inputs.snmp.table.field]]
      name = "hwEntityCpuUsage"
      oid = "*******.4.1.2011.5.25.31.1.1.1.1.5"

    [[inputs.snmp.table.field]]
      name = "hwEntityMemUsage"
      oid = "*******.4.1.2011.5.25.31.1.1.1.1.7"

  # 添加数据源类型标签
  [inputs.snmp.tags]
    data_source_type = "device_status"

# telegraf.d/inputs/snmp-interface-status.conf
[[inputs.snmp]]
  agents = ["10.36.46.188:161"]
  version = 2
  community = "public"
  interval = "60s"
  
  # 接口状态采集
  [[inputs.snmp.table]]
    name = "dci_switch_interface_status"
    oid = "*******.2.1.2.2"  # IF-MIB::ifTable
    
    [[inputs.snmp.table.field]]
      name = "ifName"
      oid = "*******.2.1.31.1.1.1.1"
      is_tag = true
      
    [[inputs.snmp.table.field]]
      name = "ifIndex"
      oid = "*******.2.1.2.2.1.1"
      is_tag = true

    [[inputs.snmp.table.field]]
      name = "ifAdminStatus"
      oid = "*******.2.1.2.2.1.7"

    [[inputs.snmp.table.field]]
      name = "ifOperStatus"
      oid = "*******.2.1.2.2.1.8"

  # 添加数据源类型标签
  [inputs.snmp.tags]
    data_source_type = "interface_status"

# telegraf.d/outputs/kafka-outputs.conf
# 设备状态数据输出到 device_status 主题
[[outputs.kafka]]
  brokers = ["dcikafka.intra.citic-x.com:30010", "dcikafka.intra.citic-x.com:30011", "dcikafka.intra.citic-x.com:30012"]
  topic = "dci.monitor.v1.defaultchannel.status.device"
  
  # 仅处理设备状态数据
  [outputs.kafka.tagpass]
    data_source_type = ["device_status"]
  
  # 安全配置 (与流量数据使用相同配置)
  sasl_username = "dci-telegraf"
  sasl_password = "telegraf-secret"
  sasl_mechanism = "PLAIN"
  # ...其他TLS配置...

# 接口状态数据输出到 interface_status 主题
[[outputs.kafka]]
  brokers = ["dcikafka.intra.citic-x.com:30010", "dcikafka.intra.citic-x.com:30011", "dcikafka.intra.citic-x.com:30012"]
  topic = "dci.monitor.v1.defaultchannel.status.interface"
  
  # 仅处理接口状态数据
  [outputs.kafka.tagpass]
    data_source_type = ["interface_status"]
  
  # 安全配置 (与流量数据使用相同配置)
  sasl_username = "dci-telegraf"
  sasl_password = "telegraf-secret"
  sasl_mechanism = "PLAIN"
  # ...其他TLS配置...

# 流量数据输出配置保持不变
[[outputs.kafka]]
  brokers = ["dcikafka.intra.citic-x.com:30010", "dcikafka.intra.citic-x.com:30011", "dcikafka.intra.citic-x.com:30012"]
  topic = "dci.monitor.v1.defaultchannel.flows.snmp"
  
  # 仅处理流量数据
  [outputs.kafka.tagpass]
    data_source_type = ["snmp_flow"]
  
  # ...安全配置...
```

### 4.5.8 代码命名一致性调整

为了提高代码的一致性和可维护性，针对新增的交换机状态监测功能，将进行以下命名调整：

#### 1. 包名统一调整

将现有的 `flowdata` 包更名为更通用和准确的 `snmp_processor` 包：

```
dcimonitor/internal/flowdata → dcimonitor/internal/snmp_processor
```

#### 2. 类型名称调整

调整主要类型名称，从特定的"Flow"前缀改为基于"SNMP"的命名：

```go
// 原名称
type FlowDataProcessor struct {...}
type FlowDataHandler struct {...}

// 新名称
type SNMPProcessor struct {...}  // 基类
type SNMPFlowProcessor struct {...}    // 子类，用于流量处理
type SNMPStatusProcessor struct {...}  // 子类，用于状态处理
type SNMPDataHandler struct {...}    // 通用处理器
```

#### 3. 配置结构调整

重新设计配置结构，使其更具包容性：

```go
// 原配置
type ProcessorConfig struct {
    MetricPrefix    string
    EnableInOctets  bool
    EnableOutOctets bool
    EnableErrors    bool
    EnableDiscards  bool
    MetricLabels    map[string]string
}

// 新配置
type SNMPProcessorConfig struct {
    // 通用配置
    MetricPrefix    string
    MetricLabels    map[string]string
    
    // 流量指标配置
    EnableInOctets  bool
    EnableOutOctets bool
    EnableErrors    bool
    EnableDiscards  bool
    
    // 设备状态配置
    EnableDeviceCPU     bool
    EnableDeviceMemory  bool
    
    // 接口状态配置
    EnableInterfaceStatus bool
}
```

#### 4. 文件命名调整

在实施过程中，逐步调整相关文件名，以反映更广泛的功能范围：

```
flowdata_processor.go → snmp_processor.go
flowdata_handler.go → snmp_handler.go
flowdata_metrics.go → snmp_metrics.go
```

#### 5. 服务命名调整

在Kubernetes配置和服务注册中，使用更准确的名称：

```yaml
# 原名称
name: dcimonitor-flowdata-service

# 新名称
name: dcimonitor-snmp-processor-service
```

#### 6. 命令行与启动参数调整

更新命令行子命令和启动参数名称：

```go
// 原命令
cmd.AddCommand(flowdata.NewCommand())

// 新命令
cmd.AddCommand(snmp_processor.NewCommand())
```

这种命名调整将使代码更加统一和语义明确，同时保持与项目的整体命名一致。`snmp_processor`的命名直接反映了组件的主要职责：处理SNMP协议采集的数据，包括流量、CPU、内存和接口状态等各类指标。

通过以上补充内容，交换机状态监测技术设计文档更加完整，可以为开发人员提供清晰的实现指导。
