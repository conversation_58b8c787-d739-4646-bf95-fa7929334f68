---
title: |
  DCI-流量类数据Prometheus指标设计

subtitle: |
  技术设计方案
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-06-12 | 顾铠羟 | 初始版本           |

# 1 文档介绍

## 1.1 文档目的

本文档详细描述 DCI 流量类数据的 Prometheus 指标设计，包括指标命名、标签体系、查询模式和常用查询示例。该设计为流量数据的监控、分析和可视化提供标准化基础，确保各系统组件使用统一的指标体系。

## 1.2 文档范围

本文档涵盖的内容包括：
- 流量类指标的命名规范和含义
- 指标标签体系设计
- 原始计数器与流速计算方法
- 常用 PromQL 查询示例
- 指标查询与可视化建议

## 1.3 文档关联

- 《19-DCI-流量类数据接收及存储技术设计.md》：流量数据处理与存储的整体技术方案
- 《18-DCI-设备端口流量查询服务设计方案.md》：使用这些指标的查询服务设计
- 《11-DCI-Telegraf-SNMP采集实现方案.md》：流量数据的来源与采集方式

# 2 指标设计总体说明

## 2.1 设计原则

1. **原始数据优先**：存储原始计数器值而非计算值，通过 PromQL 动态计算流速
2. **标签完整性**：为每个指标提供完整的元数据标签，支持多维度查询和分析
3. **命名规范性**：采用统一的指标命名前缀和规范，确保指标含义明确
4. **最小化存储**：避免存储重复或冗余数据，减少时间序列数量
5. **灵活性**：支持不同时间窗口的流速计算和多种聚合方式

## 2.2 指标命名规范

所有流量类指标使用统一前缀 `dci_snmp_flow_`，后跟具体指标类型。指标命名遵循以下规则：

- 使用小写字母和下划线
- 指标名称应清晰表达指标含义
- 避免使用特殊字符和空格
- 使用统一的单位命名约定（如 octets、errors、discards）

## 2.3 标签设计

每个流量指标包含以下标签：

| 标签名称    | 说明                    | 示例值               |
|-------------|-------------------------|---------------------|
| device_id   | 设备唯一标识符          | d-12345             |
| device_name | 设备名称                | SW1                 |
| device_ip   | 设备管理IP地址          | ************        |
| agent_id    | 采集代理标识符          | dciagent-01         |
| agent_ip    | 采集代理IP地址          | ********            |
| port_id     | 端口唯一标识符          | p-67890             |
| port_name   | 端口名称                | 10GE1/0/1           |
| if_index    | 接口索引                | 11                  |
| vni_id      | VNI标识符(可选)         | 6005002             |

## 2.4 指标汇总列表

下表汇总了当前系统暴露的所有 Prometheus 指标：

| 指标名称 | 类型 | 说明 | 标签 |
|---------|------|------|------|
| dci_snmp_flow_in_octets | Gauge | 入向流量字节计数器 | device_id, device_name, device_ip, agent_id, agent_ip, port_id, port_name, if_index, vni_id |
| dci_snmp_flow_out_octets | Gauge | 出向流量字节计数器 | device_id, device_name, device_ip, agent_id, agent_ip, port_id, port_name, if_index, vni_id |
| dci_snmp_flow_in_errors | Gauge | 入向错误计数器 | device_id, device_name, device_ip, agent_id, agent_ip, port_id, port_name, if_index, vni_id |
| dci_snmp_flow_out_errors | Gauge | 出向错误计数器 | device_id, device_name, device_ip, agent_id, agent_ip, port_id, port_name, if_index, vni_id |
| dci_snmp_flow_in_discards | Gauge | 入向丢弃计数器 | device_id, device_name, device_ip, agent_id, agent_ip, port_id, port_name, if_index, vni_id |
| dci_snmp_flow_out_discards | Gauge | 出向丢弃计数器 | device_id, device_name, device_ip, agent_id, agent_ip, port_id, port_name, if_index, vni_id |
| dci_snmp_flow_processor_messages_total | Counter | 处理消息总数 | 无 |
| dci_snmp_flow_processor_messages_success | Counter | 处理成功消息数 | 无 |
| dci_snmp_flow_processor_messages_error | Counter | 处理错误消息数 | 无 |

# 3 指标详细定义

## 3.1 流量计数器指标

### 3.1.1 入向流量计数器

```
dci_snmp_flow_in_octets
```

**说明**：记录接口上接收的总字节数原始计数器值（对应 SNMP 的 ifHCInOctets）。

**类型**：Gauge（存储原始计数器值）

**标签**：device_id, device_name, device_ip, agent_id, agent_ip, port_id, port_name, if_index, vni_id

**用途**：通过 rate() 函数计算入向流速，单位为比特每秒(bps)。

### 3.1.2 出向流量计数器

```
dci_snmp_flow_out_octets
```

**说明**：记录接口上发送的总字节数原始计数器值（对应 SNMP 的 ifHCOutOctets）。

**类型**：Gauge（存储原始计数器值）

**标签**：device_id, device_name, device_ip, agent_id, agent_ip, port_id, port_name, if_index, vni_id

**用途**：通过 rate() 函数计算出向流速，单位为比特每秒(bps)。

### 3.1.3 入向错误计数器

```
dci_snmp_flow_in_errors
```

**说明**：记录接口上接收错误的数据包总数原始计数器值（对应 SNMP 的 ifInErrors）。

**类型**：Gauge（存储原始计数器值）

**标签**：device_id, device_name, device_ip, agent_id, agent_ip, port_id, port_name, if_index, vni_id

**用途**：监控接口接收错误，评估接口健康状态。

### 3.1.4 出向错误计数器

```
dci_snmp_flow_out_errors
```

**说明**：记录接口上发送错误的数据包总数原始计数器值（对应 SNMP 的 ifOutErrors）。

**类型**：Gauge（存储原始计数器值）

**标签**：device_id, device_name, device_ip, agent_id, agent_ip, port_id, port_name, if_index, vni_id

**用途**：监控接口发送错误，评估接口健康状态。

### 3.1.5 入向丢弃计数器

```
dci_snmp_flow_in_discards
```

**说明**：记录接口上丢弃的入向数据包总数原始计数器值（对应 SNMP 的 ifInDiscards）。

**类型**：Gauge（存储原始计数器值）

**标签**：device_id, device_name, device_ip, agent_id, agent_ip, port_id, port_name, if_index, vni_id

**用途**：监控接口拥塞和缓冲区溢出情况。

### 3.1.6 出向丢弃计数器

```
dci_snmp_flow_out_discards
```

**说明**：记录接口上丢弃的出向数据包总数原始计数器值（对应 SNMP 的 ifOutDiscards）。

**类型**：Gauge（存储原始计数器值）

**标签**：device_id, device_name, device_ip, agent_id, agent_ip, port_id, port_name, if_index, vni_id

**用途**：监控接口拥塞和缓冲区溢出情况。

## 3.2 处理器统计指标

### 3.2.1 处理消息总数

```
dci_snmp_flow_processor_messages_total
```

**说明**：记录流量数据处理器处理的消息总数。

**类型**：Counter（单调递增计数器）

**标签**：无

**用途**：监控消息处理吞吐量。

### 3.2.2 处理成功消息数

```
dci_snmp_flow_processor_messages_success
```

**说明**：记录流量数据处理器成功处理的消息数。

**类型**：Counter（单调递增计数器）

**标签**：无

**用途**：监控消息处理成功率。

### 3.2.3 处理错误消息数

```
dci_snmp_flow_processor_messages_error
```

**说明**：记录流量数据处理器处理失败的消息数。

**类型**：Counter（单调递增计数器）

**标签**：无

**用途**：监控消息处理错误率。

# 4 流速计算与查询

## 4.1 流速计算原理

流量类数据处理服务不计算流速，而是存储原始计数器值。流速通过 Prometheus 的 rate() 函数动态计算，将字节计数器转换为比特每秒(bps)的流速值。

计算公式：
```
流速(bps) = rate(计数器[时间窗口]) * 8
```

这种设计提供多项优势：

1. **灵活性**：支持在查询时选择不同的时间窗口
2. **准确性**：rate() 函数自动处理计数器重置和采样点缺失
3. **高效性**：减少存储开销，只存储原始计数器值
4. **简化实现**：服务专注于数据采集和标准化处理

## 4.2 常用 PromQL 查询示例

### 4.2.1 单端口流速查询

入向流速(bps)：
```promql
rate(dci_snmp_flow_in_octets{device_id="d-12345", port_name="10GE1/0/1"}[5m]) * 8
```

出向流速(bps)：
```promql
rate(dci_snmp_flow_out_octets{device_id="d-12345", port_name="10GE1/0/1"}[5m]) * 8
```

### 4.2.2 设备总流量查询

设备入向总流量(bps)：
```promql
sum by(device_name) (rate(dci_snmp_flow_in_octets{device_id="d-12345"}[5m]) * 8)
```

设备出向总流量(bps)：
```promql
sum by(device_name) (rate(dci_snmp_flow_out_octets{device_id="d-12345"}[5m]) * 8)
```

### 4.2.3 高流量端口排序

入向流量最高的前10个端口：
```promql
topk(10, sum by(device_name, port_name) (rate(dci_snmp_flow_in_octets[5m]) * 8))
```

出向流量最高的前10个端口：
```promql
topk(10, sum by(device_name, port_name) (rate(dci_snmp_flow_out_octets[5m]) * 8))
```

### 4.2.4 错误率计算

入向错误率(每秒)：
```promql
rate(dci_snmp_flow_in_errors{device_id="d-12345", port_name="10GE1/0/1"}[5m])
```

出向错误率(每秒)：
```promql
rate(dci_snmp_flow_out_errors{device_id="d-12345", port_name="10GE1/0/1"}[5m])
```

### 4.2.5 处理性能指标

消息处理速率(每秒)：
```promql
rate(dci_snmp_flow_processor_messages_total[1m])
```

错误率百分比：
```promql
rate(dci_snmp_flow_processor_messages_error[5m]) / rate(dci_snmp_flow_processor_messages_total[5m]) * 100
```

## 4.3 不同时间窗口的选择

Prometheus 的 rate() 函数支持不同的时间窗口参数，适用于不同的监控场景：

| 时间窗口 | 适用场景                           | 特点                           |
|----------|-----------------------------------|--------------------------------|
| [1m]     | 实时监控，流量突变检测             | 灵敏度高，但易受采样点波动影响  |
| [5m]     | 一般监控，平衡灵敏度和稳定性       | 推荐的默认窗口，适合大多数场景  |
| [15m]    | 趋势分析，减少短期波动影响         | 更稳定，但对突变反应较慢        |
| [1h]     | 长期趋势，小时级别的流量模式分析   | 高度平滑，适合长期趋势图表      |

# 5 应用场景示例

## 5.1 设备流量监控面板

典型的设备流量监控面板包括以下 PromQL 查询：

```promql
# 设备总入向流量(bps)
sum by(device_name) (rate(dci_snmp_flow_in_octets{device_id="$device_id"}[5m]) * 8)

# 设备总出向流量(bps)
sum by(device_name) (rate(dci_snmp_flow_out_octets{device_id="$device_id"}[5m]) * 8)

# 各端口入向流量(bps) - 前10名
topk(10, sum by(port_name) (rate(dci_snmp_flow_in_octets{device_id="$device_id"}[5m]) * 8))

# 各端口出向流量(bps) - 前10名
topk(10, sum by(port_name) (rate(dci_snmp_flow_out_octets{device_id="$device_id"}[5m]) * 8))
```

## 5.2 VNI流量分析

基于 VNI 标签的流量分析查询：

```promql
# VNI入向流量(bps)
sum by(vni_id) (rate(dci_snmp_flow_in_octets{vni_id="$vni_id"}[5m]) * 8)

# VNI出向流量(bps)
sum by(vni_id) (rate(dci_snmp_flow_out_octets{vni_id="$vni_id"}[5m]) * 8)

# 按VNI分组的流量趋势
sum by(vni_id) (rate(dci_snmp_flow_in_octets[5m]) * 8)
```

## 5.3 处理性能监控

监控流量数据处理服务性能的查询：

```promql
# 消息处理速率(每秒)
rate(dci_snmp_flow_processor_messages_total[1m])

# 处理成功率(百分比)
rate(dci_snmp_flow_processor_messages_success[5m]) / rate(dci_snmp_flow_processor_messages_total[5m]) * 100

# 处理错误率(百分比)
rate(dci_snmp_flow_processor_messages_error[5m]) / rate(dci_snmp_flow_processor_messages_total[5m]) * 100
```

# 6 大规模环境下的指标优化

## 6.1 性能挑战

在大规模环境下(如200台交换机)，当前指标设计可能面临以下挑战：

1. **数据量爆炸**：
   - 假设200台交换机，每台48个端口，每个端口6个指标，会产生约57,600个时间序列
   - 指标数量呈线性增长，大量时间序列会影响查询效率
   - 标签基数(label cardinality)高会导致内存使用激增

2. **查询性能下降**：
   - 使用`rate()`函数计算流速时，不带过滤条件将处理所有匹配的时间序列
   - 复杂查询处理时间可能从毫秒级增长到秒级甚至分钟级
   - 高基数查询(如按port_name分组)在大规模环境下尤其消耗资源

3. **数据精度与存储平衡**：
   - 高频采集(如15秒一次)产生大量样本点，增加存储需求
   - 长期保留高精度数据会占用大量存储空间

## 6.2 指标与查询优化策略

### 6.2.1 查询效率优化

1. **查询模式优化**：
   - 使用"先过滤，后计算"的查询模式：
   ```promql
   # 优化前
   rate(dci_snmp_flow_in_octets[5m]) * 8 > 1e9
   
   # 优化后
   rate(dci_snmp_flow_in_octets{device_id=~"core-.*"}[5m]) * 8 > 1e9
   ```
   
   - 避免大范围正则表达式：
   ```promql
   # 低效查询
   dci_snmp_flow_in_octets{device_name=~".*switch.*"}
   
   # 高效查询
   dci_snmp_flow_in_octets{device_id=~"sw-1|sw-2|sw-3"}
   ```

### 6.2.2 使用记录规则(Recording Rules)预计算

对于频繁查询或计算成本较高的指标（如全网流量速率），可以通过记录规则进行预计算，将结果保存为新的时间序列。这可以极大提升仪表盘加载速度和查询性能。

**设计思路**：

1.  **创建规则文件**：创建一个专门的YAML文件（如 `dci_rules.yml`）来定义记录规则。
2.  **定义新指标**：为入向/出向流量、错误、丢弃等指标的速率分别创建新的预计算指标。
3.  **加载规则**：在Prometheus的主配置文件中加载此规则文件。

**规则文件示例 (`dci_rules.yml`)**：

```yaml
groups:
- name: dci_port_flow_rules
  interval: 1m  # 每分钟计算一次
  rules:
  - record: port:in_bps
    expr: rate(dci_snmp_flow_in_octets[5m]) * 8
    labels:
      job: dci-snmp # 可选，为新指标添加额外标签

  - record: port:out_bps
    expr: rate(dci_snmp_flow_out_octets[5m]) * 8
    labels:
      job: dci-snmp

  - record: port:in_errors_rate
    expr: rate(dci_snmp_flow_in_errors[5m])
    labels:
      job: dci-snmp

  - record: port:out_errors_rate
    expr: rate(dci_snmp_flow_out_errors[5m])
    labels:
      job: dci-snmp

  - record: port:in_discards_rate
    expr: rate(dci_snmp_flow_in_discards[5m])
    labels:
      job: dci-snmp

  - record: port:out_discards_rate
    expr: rate(dci_snmp_flow_out_discards[5m])
    labels:
      job: dci-snmp
```

**Prometheus配置加载规则**：

在Prometheus的主配置文件（如`prometheus.yml`）中，通过`rule_files`字段加载上述规则文件。

```yaml
global:
  scrape_interval: 15s

rule_files:
  - "dci_rules.yml" # 加载记录规则文件

scrape_configs:
  # ... 其他抓取配置 ...
```

**使用预计算指标**：

配置完成后，查询流量速率将变得非常简单和高效：

```promql
# 直接查询预计算好的入向流量，单位已是bps
port:in_bps{device_id="d-12345", port_name="10GE1/0/1"}

# 查询出向流量最高的前10个端口
topk(10, port:out_bps)
```
