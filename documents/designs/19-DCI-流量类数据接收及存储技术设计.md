---
title: |
  DCI-流量类数据接收及存储技术设计

subtitle: |
  技术设计方案
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-31 | 顾铠羟 | 初始版本           |
| V2.0 | 2025-06-01 | 顾铠羟 | 强化 Kafka 接收层模块化设计 |
| V2.1 | 2025-06-02 | 顾铠羟 | 根据现有 Kafka 模块调整设计 |
| V2.2 | 2025-06-03 | 顾铠羟 | 完善 SNMP 数据采集与存储流程 |
| V2.3 | 2025-06-04 | 顾铠羟 | 优化数据流逻辑详解 |
| V2.4 | 2025-06-05 | 顾铠羟 | 精简文档内容，消除重复，增加引用 |
| V3.0 | 2025-06-08 | 顾铠羟 | 根据代码实现更新设计文档，调整处理逻辑和数据流程 |

# 1 文档介绍

## 1.1 关键服务器

- **flowdata本地服务**: 在本地端口(localhost:30006)暴露指标
- **云端Prometheus服务器**: dciprometheus.intro.citic-x.com:30006
- **Prometheus抓取模型**: 云端服务器主动从本地服务抓取指标数据

## 1.2 文档目的

本文档描述 DCI 数据监测系统中的流量类数据接收及存储技术方案，旨在详细说明如何从 Telegraf 采集的 SNMP 流量数据通过 Kafka 传输，并最终存储至 Prometheus 的技术实现方案。该方案通过设备接口映射和流量计算，确保流量数据能够被设备端口流量查询服务有效利用。本设计基于系统中现有的模块化 Kafka 组件，通过扩展其功能以满足流量数据处理的需求。

## 1.3 文档范围

本文档涵盖的内容包括：
- 流量类数据从 Telegraf 到 Prometheus 的完整数据链路设计
- SNMP 接口标识符与流量数据采集流程
- 基于现有 Kafka 模块的消息接收扩展设计
- 数据处理与转换逻辑及流速计算方法
- Prometheus 数据存储格式与标签策略
- Prometheus 的拉取模型与指标抓取配置
- 与设备端口流量查询服务的接口设计

## 1.4 文档关联

- 《00-DCI数据监测系统项目文档路书.md》：本文档是项目路书中提到的流量类数据技术设计部分
- 《02-网络自动化平台-数据监测系统技术概要设计.md》：提供系统总体架构，本文档中的方案需与其保持一致
- 《11-DCI-Telegraf-SNMP采集实现方案.md》：描述 SNMP 数据采集的具体实现
- 《18-DCI-设备端口流量查询服务设计方案.md》：本文档提供的数据将被该服务使用
- 《13-DCI-Kafka主题规划及负载均衡连接设计.md》：定义流量数据相关的 Kafka 主题规划
- 《20-DCI-MySql数据库同步-设备端口VNI基础信息.md》：定义数据库表结构及数据同步机制
- 《19-01-DCI-流量类数据prometheus指标设计.md》：详细描述流量数据的Prometheus指标设计，包括指标命名、标签体系和查询模式

# 2 总体设计

## 2.1 数据流逻辑详解

系统数据流分为以下几个关键环节：

1. **数据采集阶段**：
   - Telegraf 通过 SNMP 协议采集网络设备接口信息和流量数据
   - 接口信息包括：接口名称(ifName)、接口索引(ifIndex)、设备名称(sysName)等
   - 流量数据包括：入向流量计数器(ifHCInOctets)、出向流量计数器(ifHCOutOctets)
   - 采集数据带有原始时间戳(timestamp)，确保流速计算准确性

2. **数据传输阶段**：
   - 采集数据通过 Kafka 消息队列传输，使用JSON格式
   - 主要 Kafka 主题为 `dci.monitor.v1.defaultchannel.flows.snmp`
   - 消息包含设备标识(device_ip)、接口标识(ifIndex/ifName)和流量数据

3. **消息处理阶段**：
   - FlowDataHandler 接收 Kafka 消息并交给 FlowDataProcessor 处理
   - 提取设备IP和接口索引，从数据库查询完整映射信息
   - 实现错误处理和统计信息收集，确保消息处理可靠性

4. **数据处理阶段**：
   - 解析接口名称中的 VNI 信息（如 "10GE1/0/1.6005002" 中的 "6005002"）
   - 使用 DevicePortMapper 从 MySQL 获取设备-端口-VNI 映射关系
   - 将原始计数器值存储到 Prometheus，不在服务内部计算流速
   - 使用 MetricExporter 将原始计数器导出到 Prometheus，带有完整标签体系

5. **数据存储阶段**：
   - 流量计数器数据以时间序列形式存储在 Prometheus 中
   - 使用标准的标签体系，包括 device_id、port_name、if_index、vni_id 等
   - 保留原始采集时间戳，确保数据的时间连续性
   - 通过拉取模型，云端 Prometheus 服务器定期抓取本地指标

6. **数据查询阶段**：
   - 流量查询服务通过 PromQL 查询 Prometheus 获取流量数据
   - 使用 Prometheus 的 rate 函数动态计算流速，如 `rate(dci_snmp_flow_in_octets[5m]) * 8`
   - 提供标准化的 RESTful API 接口给多云系统使用

下面是典型的流量数据消息格式示例：

```json
{
	"fields": {
		"ifAdminStatus": 1,
		"ifDescr": "10GE1/0/1",
		"ifInOctets": 116314713,
		"ifOutOctets": 21501364,
		"ifInErrors": 0,
		"ifOutErrors": 0,
	},
	"name": "interface",
	"tags": {
		"device_ip": "************",
		"host": "gukqdeMacBook-Pro.local",
		"ifDescr": "10GE1/0/1",
		"ifIndex": "11",
		"sysName": "SW1"
	},
	"timestamp": 1749002925
}
```

整个数据流程设计充分利用现有组件和标准协议，实现高效可靠的流量数据处理和存储。

## 2.2 设计目标

1. 构建高性能、可靠的流量类数据接收与存储服务
2. 通过 SNMP 协议采集设备接口信息及流量数据
3. 确保 SNMP 流量数据从 Kafka 到 Prometheus 的高效传输和处理
4. 实现设备接口与 VNI 的映射关系管理
5. 支持基于流量原始数据的流速计算
6. 提供标准化的数据格式和标签体系，便于流量查询服务使用
7. 支持水平扩展，满足大规模流量数据处理需求
8. 实现与现有系统组件的无缝集成
9. 利用现有 Kafka 模块，实现代码复用，减少重复开发

## 2.3 架构设计

### 2.3.1 详细架构图

```mermaid
graph TB
    subgraph 数据采集层
        A1[SNMP 设备]
        A2[SNMP 设备]
        A3[SNMP 设备]
        
        B[Telegraf]
        
        A1 -->|ifHCInOctets/ifHCOutOctets| B
        A2 -->|ifHCInOctets/ifHCOutOctets| B
        A3 -->|ifHCInOctets/ifHCOutOctets| B
    end
    
    subgraph 数据传输层
        B -->|JSON消息| C[Kafka]
    end
    
    subgraph 数据处理层
        C -->|消费消息| D[Consumer]
        D -->|处理回调| E[FlowDataHandler]
        E -->|解析消息| F[FlowDataProcessor]
        
        I[(MySQL)]
        I -->|查询映射关系| F
        
        F -->|提取计数器值| G2[MetricExporter]
        G2 -->|暴露HTTP端点| K[本地HTTP服务]
    end
    
    subgraph 数据存储与查询层
        K -. "定期抓取/metrics" .-> H[云端Prometheus]
        J[流量查询服务] -->|PromQL查询| H
    end
    
    classDef http fill:#f9f,stroke:#333,stroke-width:2px;
    classDef pull stroke-dasharray: 5 5;
    class K http;
    class H,K pull;
```

### 2.3.2 极简架构图

```mermaid
graph LR
    A[Telegraf] -->|SNMP采集| B[Kafka]
    B --> C[FlowData服务]
    C -->|端口映射查询| D[(MySQL)]
    C -->|暴露指标| E[HTTP服务]
    F[云端Prometheus] -. "定期抓取" .-> E
    G[流量查询服务] -->|PromQL| F
    
    style E fill:#f9f,stroke:#333
    linkStyle 3 stroke-dasharray: 5 5
```

## 2.4 数据流图

```mermaid
sequenceDiagram
    participant S as SNMP设备
    participant T as Telegraf
    participant K as Kafka
    participant C as Consumer
    participant H as FlowDataHandler
    participant P as FlowDataProcessor
    participant DB as MySQL
    participant M as MetricExporter
    participant HTTP as 本地HTTP服务
    participant Prom as 云端Prometheus
    participant Q as 查询服务
    
    S->>T: 采集ifHCInOctets/ifHCOutOctets流量数据
    Note over T: 记录采集时间戳
    T->>K: 发送JSON格式流量数据
    K->>C: 消费消息(ConsumeClaim)
    C->>H: 处理消息(HandleMessage)
    H->>P: 解析处理(ProcessData)
    
    P->>P: 提取设备IP和接口索引
    P->>DB: 查询设备端口映射(GetPortMapping)
    DB-->>P: 返回映射信息(device_id,port_name等)
    
    P->>P: 提取流量计数器数据
    P->>M: 导出原始计数器指标(SetGaugeValue)
    M->>HTTP: 注册/metrics端点(Handler)
    
    loop 每15秒
        Prom->>HTTP: GET /metrics
        HTTP-->>Prom: 返回所有指标数据
        Note over Prom: 存储时间序列数据
    end
    
    Q->>Prom: PromQL查询(使用rate函数计算流速)
    Note over Prom: 使用rate(metric[5m]) * 8计算bps
    Prom-->>Q: 返回流速数据
```

## 2.5 模块化设计

流量数据接收及存储服务（flowdata）作为 dcimonitor 的一个独立模块实现，充分利用现有的 Kafka 组件，包含以下核心部分：

1. **消息处理模块**:
   - **Consumer**: 实现 Kafka 消费者逻辑，从 Kafka 主题消费消息
   - **FlowDataHandler**: 实现消息处理接口，负责解析消息并传递给处理器
   - **Config**: 加载和验证配置，提供服务参数管理

2. **数据处理模块**:
   - **FlowDataProcessor**: 核心处理逻辑，解析流量数据并计算流速
   - **DevicePortMapper**: 设备端口映射服务，提供设备IP和接口索引到完整映射的转换
   - **Models**: 定义数据结构和类型，如 FlowData、PortMapping 等

3. **指标导出模块**:
   - **MetricExporter**: Prometheus 指标导出器，管理指标注册和更新
   - **HTTP Service**: 提供 HTTP 服务端点，暴露 Prometheus 指标

4. **数据连接模块**:
   - **MySQL Connector**: 管理与 MySQL 数据库的连接和查询
   - **Prometheus Client**: 提供 Prometheus 客户端功能，实现指标数据暴露

该服务的模块化设计确保各组件之间的松耦合和高内聚，便于功能扩展和维护。每个模块具有明确职责，通过接口进行交互，提高系统灵活性和可测试性。

## 2.6 技术选型

| 技术组件     | 版本   | 用途                     | 选型理由                             |
|--------------|--------|--------------------------|-----------------------------------|
| Go           | 1.19+  | 服务开发语言             | 高性能、并发支持强、与现有系统一致    |
| Sarama       | 1.32.0 | Kafka 客户端库           | 成熟稳定、功能完善、社区活跃        |
| Telegraf     | 1.35.0 | SNMP 数据采集            | 支持多种协议采集、插件丰富          |
| SNMP         | v2c    | 网络设备数据采集协议     | 设备普遍支持、标准协议              |
| MySQL Driver | 1.6.0  | MySQL 数据库驱动         | 高性能、符合标准、广泛使用          |
| Prometheus Client | 1.11.1 | 指标暴露与存储       | 与 Prometheus 完美集成、生态丰富    |
| Zap          | 1.21.0 | 日志框架                 | 高性能、结构化日志、与现有系统一致   |

# 3 详细设计

## 3.1 SNMP 数据采集设计

### 3.1.1 SNMP OID 设计

基于 IF-MIB 标准 MIB，采集以下关键 OID 数据：

| 数据类型 | OID | 说明 |
|---------|-----|------|
| ifName | .*******.********.1.1.1 | 接口名称，格式如"10GE1/0/1" |
| ifHCInOctets | .*******.********.1.1.6 | 接口入向总字节数(64位计数器) |
| ifHCOutOctets | .*******.********.1.1.10 | 接口出向总字节数原始计数器(64位) |
| ifIndex | .*******.*******.1.1 | 接口索引，用于关联各个OID的数据 |

### 3.1.2 接口名称与 VNI 解析

接口名称中可能包含 VNI 信息，需要按照以下规则进行解析：

1. 标准物理接口：如 "10GE1/0/1"
2. VNI 子接口：如 "10GE1/0/1.10"（VNI ID 为 10）；如 "10GE1/0/1.6005002"（VNI ID 为 6005002）

VNI 解析规则参考《18-DCI-设备端口流量查询服务设计方案.md》中 5.3 节"VNI解析处理"的实现。当接口名称包含点号(.)时，点号后的数字若大于 0 通常表示 VNI ID。

### 3.1.3 接口索引映射机制

接口索引映射用于将SNMP设备的接口索引与接口名称正确关联。本系统利用《20-DCI-MySql数据库同步-设备端口VNI基础信息.md》文档中定义的接口索引映射机制，通过Telegraf周期性采集、Kafka消息传输和数据库同步服务处理，实现接口索引映射的准确性和实时性。详细设计参考该文档的3.2.3节和3.2.4节。

## 3.2 数据库设计

本系统使用的数据库表结构由《20-DCI-MySql数据库同步-设备端口VNI基础信息.md》文档定义。流量数据处理服务主要使用`dci_monitor`数据库中的以下表：

- `dci_device` - 设备信息表
- `dci_port` - 端口信息表，包含接口索引映射
- `dci_node` - 节点表
- `dci_node_business` - 节点业务关系表，包含VNI信息
- `dci_vni` - VNI信息表

数据库同步机制和接口映射逻辑请参考《20-DCI-MySql数据库同步-设备端口VNI基础信息.md》文档，本服务不再实现重复功能，仅消费已同步的数据。

## 3.3 Kafka 数据传输设计

### 3.3.1 Telegraf 配置

Telegraf 配置分为两种模式，分别用于采集接口信息和流量数据：

#### ******* 接口信息采集配置

```toml
# 接口信息采集配置
[[inputs.snmp]]
  name = "interface_info"
  agents = ["************:161"]
  version = 2
  community = "dcilab2025"
  
  # 采集接口名称信息
  [[inputs.snmp.field]]
    name = "ifName"
    oid = ".*******.********.1.1.1"
    is_tag = true
  
  # 设备IP标签设置
  agent_host_tag = "device_ip"
  
  [inputs.snmp.tags]
    data_type = "interface_info"

# Kafka 输出插件 - 接口信息
[[outputs.kafka]]
  brokers = ["dcikafka.intra.citic-x.com:30010","dcikafka.intra.citic-x.com:30011","dcikafka.intra.citic-x.com:30012"]
  topic = "dci.monitor.v1.defaultchannel.interface.snmp"
  
  [outputs.kafka.tagpass]
    data_type = ["interface_info"]
```

#### ******* 流量数据采集配置

测试中使用的且有效的telegraf配置：
```toml
# Telegraf测试配置 - 实际SNMP采集
[agent]
  interval = "10s"
  round_interval = true
  metric_batch_size = 1000
  metric_buffer_limit = 10000
  collection_jitter = "0s"
  flush_interval = "2s"    # 更快的flush间隔
  flush_jitter = "0s"
  precision = ""
  debug = true
  quiet = false
  logfile = "/Users/<USER>/code/dci/dci-workspace/dci_self_test/results/telegraf-flowdata-test.log"
  hostname = ""
  omit_hostname = false
  
[[inputs.snmp]]
  # 设备列表 - 测试环境的交换机
  agents = [
    "udp://************:161"  # 测试交换机1
  ]
  
  # 基本配置
  timeout = "5s"        # 更短的超时
  retries = 1           # 减少重试次数
  version = 2
  community = "dcilab2025"
  agent_host_tag = "device_ip"
  
  # 高级配置
  max_repetitions = 10  # 根据记录数量限制调整
  name = "snmp"
  
  # 系统基本信息
  [[inputs.snmp.field]]
    name = "sysName"
    oid = ".*******.*******.0"
    is_tag = true
    
  # 接口表信息
  [[inputs.snmp.table]]
    name = "interface"
    inherit_tags = ["sysName"]
    oid = ".*******.*******"  # IF-MIB::ifTable
    
    # ifTable 字段
    [[inputs.snmp.table.field]]
      name = "ifIndex"
      oid = ".*******.*******.1.1"  # IF-MIB::ifIndex
      is_tag = true
      
    [[inputs.snmp.table.field]]
      name = "ifDescr"
      oid = ".*******.*******.1.2"  # IF-MIB::ifDescr
      is_tag = true
  
  # 接口扩展表信息 - 重点采集流量数据
  [[inputs.snmp.table]]
    name = "interfaceX"
    inherit_tags = ["sysName"]
    oid = ".*******.********.1"  # IF-MIB::ifXTable
    
    # ifXTable 字段
    [[inputs.snmp.table.field]]
      name = "ifName"
      oid = ".*******.********.1.1.1"  # IF-MIB::ifName
      is_tag = true
      
    [[inputs.snmp.table.field]]
      name = "ifHCInOctets"
      oid = ".*******.********.1.1.6"  # IF-MIB::ifHCInOctets
      
    [[inputs.snmp.table.field]]
      name = "ifHCOutOctets"
      oid = ".*******.********.1.1.10"  # IF-MIB::ifHCOutOctets
      
    [[inputs.snmp.table.field]]
      name = "ifInDiscards"
      oid = ".*******.*******.1.13"  # IF-MIB::ifInDiscards
      
    [[inputs.snmp.table.field]]
      name = "ifOutDiscards"
      oid = ".*******.*******.1.19"  # IF-MIB::ifOutDiscards
      
    [[inputs.snmp.table.field]]
      name = "ifInErrors"
      oid = ".*******.*******.1.14"  # IF-MIB::ifInErrors
      
    [[inputs.snmp.table.field]]
      name = "ifOutErrors"
      oid = ".*******.*******.1.20"  # IF-MIB::ifOutErrors
# 添加处理器，确保agent_host标签转换为device_ip字段
[[processors.rename]]
  [[processors.rename.replace]]
    tag = "agent_host"
    dest = "device_ip"

# 添加Kafka输出插件 - 用于将数据注入Kafka
[[outputs.kafka]]
  # Kafka Broker连接信息
  brokers = ["dcikafka.intra.citic-x.com:30002"]
  
  # 主题配置 - 使用设计文档中指定的主题
  topic = "dci.monitor.v1.defaultchannel.flows.snmp"
  
  # 消息格式与压缩
  data_format = "json"
  compression_codec = 2  # 2 = snappy
  
  # 可靠性设置
  max_retry = 3
  max_message_bytes = 1000000
  
  # 确保包含必要的标签
  [outputs.kafka.tags]
    data_source_type = "snmp_flow"
# 文件输出插件 - 用于保存完整数据
[[outputs.file]]
  files = ["/Users/<USER>/code/dci/dci-workspace/dci_self_test/results/telegraf-flowdata-test-full.out"]
  data_format = "json"
  flush_interval = "2s"
  
# 标准输出插件 - 用于实时查看数据
[[outputs.file]]
  files = ["stdout"]
  data_format = "json"
```


### 3.3.2 Kafka 消息格式

#### ******* 流量数据消息格式
```json
{
	"fields": {
		"ifAdminStatus": 1,
		"ifDescr": "10GE1/0/1.6005002",
		"ifInDiscards": 0,
		"ifInErrors": 0,
		"ifInNUcastPkts": 0,
		"ifInOctets": 0,
		"ifInUcastPkts": 0,
		"ifInUnknownProtos": 0,
		"ifLastChange": 0,
		"ifMtu": 1500,
		"ifOperStatus": 2,
		"ifOutDiscards": 0,
		"ifOutErrors": 0,
		"ifOutNUcastPkts": 0,
		"ifOutOctets": 0,
		"ifOutQLen": 0,
		"ifOutUcastPkts": 0,
		"ifPhysAddress": "44:9b:c1:07:8f:52",
		"ifSpecific": ".0.0",
		"ifSpeed": 4294967295,
		"ifType": 135
	},
	"name": "interface",
	"tags": {
		"device_ip": "************",
		"host": "gukqdeMacBook-Pro.local",
		"ifDescr": "10GE1/0/1.6005002",
		"ifIndex": "70",
		"sysName": "SW1"
	},
	"timestamp": 1749002925
}
```

## 3.4 流量数据处理

### 3.4.1 FlowDataHandler

FlowDataHandler 实现处理流量相关消息，用于配合现有的 Consumer 使用：

```go
// FlowDataHandler 流量数据处理器
type FlowDataHandler struct {
    processor *FlowDataProcessor
    logger    *zap.Logger
    stats     struct {
        TotalMessages    int64
        SuccessMessages  int64
        FailedMessages   int64
        ProcessingTimeNs int64
    }
}

// HandleMessage 处理消息
func (h *FlowDataHandler) HandleMessage(msg *sarama.ConsumerMessage) error {
    h.logger.Debug("处理流量数据消息",
        zap.String("topic", msg.Topic),
        zap.Int32("partition", msg.Partition),
        zap.Int64("offset", msg.Offset))
    
    startTime := time.Now()
    err := h.processor.ProcessData(msg.Value)
    h.stats.ProcessingTimeNs += time.Since(startTime).Nanoseconds()
    
    if err != nil {
        h.stats.FailedMessages++
        h.logger.Error("处理流量数据失败",
            zap.Error(err))
        return err
    }
    
    h.stats.SuccessMessages++
    return nil
}
```

### 3.4.2 DevicePortMapper

DevicePortMapper 负责管理设备端口映射关系，是流量数据处理的核心组件之一。该组件的核心职责是根据流量数据中的设备IP和接口名称，从`dci_monitor`数据库中查询出完整的、结构化的端口映射信息。为了提升性能并降低数据库负载，DevicePortMapper 内部实现了一套高效的内存缓存机制。

该组件从 `dcimonitor-flowdata.yaml` 配置文件中读取 `mapper.cache_ttl_minutes` 来设置缓存的有效期。

#### ******* 缓存数据结构

缓存的核心是 `PortMapping` 结构，它包含了处理器所需的所有映射字段。每次查询的结果都将以此结构体形式缓存。

```go
// PortMapping 包含设备和端口的完整映射信息
type PortMapping struct {
    DeviceID      string         // 设备在CMDB中的唯一ID
    DeviceName    string         // 设备名称 (sysName)
    DeviceIP      string         // 设备管理IP
    PortID        string         // 物理端口在CMDB中的唯一ID
    LogicPortID   string         // 业务逻辑端口ID
    PortName      string         // 端口名称 (ifName/ifDescr)
    IfIndex       sql.NullString // 接口的SNMP索引
    VNIID         string         // 关联的VNI ID
}
```

`DevicePortMapper` 的实现包含以下关键部分：

```go
// DevicePortMapper 设备端口映射器
type DevicePortMapper struct {
    db              *sql.DB        // 数据库连接
    logger          *zap.Logger    // 日志记录器
    cache           map[string]*PortMapping  // 映射缓存，键为 "deviceIP:portName"
    cacheMtx        sync.RWMutex   // 缓存读写锁
    cacheTTL        time.Duration  // 缓存有效期
    cacheExpiration map[string]time.Time // 缓存过期时间
    stats           struct {       // 统计信息
        CacheHits      int64       // 缓存命中次数
        CacheMisses    int64       // 缓存未命中次数
        DBQueries      int64       // 数据库查询次数
        DBErrors       int64       // 数据库错误次数
    }
}
```

#### ******* 缓存工作流程

缓存机制的设计旨在避免对每一条流量消息都进行数据库查询，通过在内存中缓存查询结果，极大地提升数据处理吞吐量，并有效保护后端数据库。

**工作流程**:
1.  当 `GetPortMapping` 被调用时，它首先根据设备IP和接口名称（如`************:10GE1/0/1`）生成一个唯一的缓存键。
2.  **缓存查询**：使用读锁保护，查询内部缓存。
    - **缓存命中 (Cache Hit)**：如果该键存在于缓存中且尚未过期，则直接返回缓存中的 `*PortMapping` 数据，并增加缓存命中计数器。
    - **缓存未命中 (Cache Miss)**：如果键不存在或已过期，则增加缓存未命中计数器，并进入数据库查询阶段。
3.  **数据库查询**：执行SQL查询，关联 `dci_device`, `dci_logic_port_device`, `dci_node`, `dci_node_business` 等表，获取构建 `PortMapping` 结构所需的全部字段。
4.  **缓存更新**：查询成功后，使用写锁保护，将查询结果存入缓存，并设置一个**可配置的过期时间(TTL)**。
5.  **缓存清理**: 一个独立的后台 goroutine 会周期性地扫描并清理已过期的缓存项，防止内存无限增长。

以下时序图详细描述了包含缓存逻辑的查询流程：

```mermaid
sequenceDiagram
    participant FlowDataProcessor as "FlowData Processor"
    participant DevicePortMapper as "DevicePortMapper"
    participant Cache as "Cache (内存)"
    participant CMDB as "CMDB (数据库)"

    FlowDataProcessor->>DevicePortMapper: GetPortMapping(deviceIP, portName)

    activate DevicePortMapper
    DevicePortMapper->>Cache: 检查缓存 (加读锁)
    
    alt 缓存命中 (数据存在且未过期)
        Cache-->>DevicePortMapper: 返回缓存的PortMapping
        DevicePortMapper-->>FlowDataProcessor: 返回端口映射
    else 缓存未命中或已过期
        DevicePortMapper->>CMDB: 查询端口映射 (执行SQL)
        activate CMDB
        CMDB-->>DevicePortMapper: 返回数据库查询结果
        deactivate CMDB

        alt 查询成功
            DevicePortMapper->>Cache: 更新缓存 (加写锁)
            activate Cache
            Cache-->>DevicePortMapper: (缓存已更新)
            deactivate Cache
            DevicePortMapper-->>FlowDataProcessor: 返回新查询到的端口映射
        else 查询失败
            DevicePortMapper-->>FlowDataProcessor: 返回错误
        end
    end
    deactivate DevicePortMapper
```

该组件与《20-DCI-MySql数据库同步-设备端口VNI基础信息.md》文档中定义的数据同步机制配合使用，保证流量数据处理时获取准确的设备-端口-VNI映射关系。当映射关系在数据库中不存在时，`GetPortMapping` 返回 `nil` 和 `nil` 错误，由上游的 `FlowDataProcessor` 负责创建临时映射。

### 3.4.3 FlowDataProcessor

FlowDataProcessor 是流量数据处理的核心组件，负责解析、处理和转换从 Kafka 接收的原始 SNMP 流量数据。

```go
// FlowDataProcessor 流量数据处理器
type FlowDataProcessor struct {
    mapper      DeviceMapper      // 设备映射接口
    exporter    MetricsExporter   // 指标导出接口
    logger      *zap.Logger       // 日志记录器
    config      *ProcessorConfig  // 处理器配置
    countersMtx sync.RWMutex      // 计数器锁
    counters    struct {          // 统计计数器
        total        int64        // 总处理消息数
        success      int64        // 成功处理消息数
        parseError   int64        // 解析错误数
        processError int64        // 处理错误数
        noMapping    int64        // 映射未找到数
    }
}
```

主要处理流程：

1. **消息解析与验证**：解析JSON格式消息，提取设备IP和接口索引
2. **映射查询**：通过DeviceMapper查询设备端口映射关系
3. **计数器提取**：从消息中提取入向和出向流量计数器值
4. **指标导出**：将原始计数器值导出到Prometheus，附带完整的标签体系

处理器仅提取和转换数据，不计算流速：

```go
// 处理流量计数器部分代码示例（简化版）
// 从ProcessData方法中截取
if inOk && p.config.EnableInOctets {
    metricName := p.config.MetricPrefix + "in_octets"
    p.exporter.SetGaugeValue(metricName, float64(inOctets), labels, telegrafTimestamp)
    p.logger.Debug("存储入向字节计数器",
        zap.Uint64("value", inOctets),
        zap.String("device", portMapping.DeviceName),
        zap.String("port", portMapping.PortName))
}

if outOk && p.config.EnableOutOctets {
    metricName := p.config.MetricPrefix + "out_octets"
    p.exporter.SetGaugeValue(metricName, float64(outOctets), labels, telegrafTimestamp)
    p.logger.Debug("存储出向字节计数器",
        zap.Uint64("value", outOctets),
        zap.String("device", portMapping.DeviceName),
        zap.String("port", portMapping.PortName))
}
```

### 3.4.4 VNI 映射与处理逻辑

VNI (Virtual Network Identifier) 的准确映射是确保流量数据归属正确的关键环节。本系统设计了一套灵活的 VNI 处理机制，优先采纳来自实时遥测数据的信息，以应对配置变更或临时映射场景。

#### 3.4.4.1 核心处理原则
- **实时性优先**：从接口名称 (`ifName`) 中提取的 VNI 信息被认为是最新的，具有最高优先级。
- **数据库为基准**：数据库 (`dci_monitor`) 中存储的 VNI 映射作为基础和默认值。
- **日志记录差异**：当实时 VNI 与数据库 VNI 不一致时，系统记录警告日志，但继续使用实时 VNI 进行处理，确保数据流不中断。

#### ******* 详细处理流程

1.  **数据库映射查询**：
    - `FlowDataProcessor` 首先调用 `DevicePortMapper` 查询数据库。`DevicePortMapper` 会根据设备 IP 和物理端口名称（如从 "10GE1/0/1.6005002" 中分离出的 "10GE1/0/1"）查询 `dci_node_business` 等表，获取已配置的 VNI ID。

2.  **接口名 VNI 提取**：
    - 无论数据库查询结果如何，`FlowDataProcessor` 都会调用 `extractVNIFromPortName` 函数，尝试从完整的 `ifName` 字符串（如 "10GE1/0/1.6005002"）中解析 VNI。
    - 解析规则为：取 `ifName` 中最后一个点号 (`.`) 之后的部分，若该部分为大于 0 的纯数字，则视为有效的 VNI。

3.  **VNI 决策逻辑**：
    - **情况一：数据库无 VNI，接口名有 VNI**：直接使用从接口名中提取的 VNI。
    - **情况二：数据库有 VNI，接口名无 VNI**：使用数据库中的 VNI。
    - **情况三：数据库与接口名 VNI 均存在且一致**：使用该 VNI。
    - **情况四：数据库与接口名 VNI 均存在但不一致**：系统采纳从接口名中提取的 VNI，并记录一条 `WARN` 级别的日志，指出两者存在的差异。

#### ******* 设计理由
该设计允许系统在 CMDB 数据库信息更新不及时的情况下，依然能够处理带有正确 VNI 的流量数据。例如，当网络管理员临时更改了端口的 VNI 配置，即使 CMDB 未同步，本系统也能基于 Telegraf 采集到的最新 `ifName` 进行正确处理。

#### ******* VNI 决策时序图

以下时序图展示了 VNI 的决策流程：

```mermaid
sequenceDiagram
    participant P as "FlowDataProcessor"
    participant M as "DeviceMapper"
    participant DB as "数据库"

    P->>M: GetPortMapping(deviceIP, basePortName)
    activate M
    M->>DB: 查询端口映射(含数据库VNI)
    DB-->>M: 返回PortMapping(含数据库VNI)
    M-->>P: 返回PortMapping
    deactivate M

    P->>P: 从ifName提取VNI (ifName_vni)
    note right of P: 例如：从 "10GE1/0/1.6005002" 提取 "6005002"
    
    alt 数据库VNI与接口名VNI不一致
        P->>P: 记录WARN日志
        P->>P: 决策: 最终VNI = ifName_vni (优先)
    else 数据库VNI为空
        P->>P: 决策: 最终VNI = ifName_vni
    else 其它情况(一致或接口名VNI为空)
        P->>P: 决策: 最终VNI = 数据库VNI
    end

    P->>P: 使用最终VNI准备指标标签
```

### 3.4.5 接口设计

flowdata 服务采用接口设计模式，通过定义清晰的接口边界以增强模块化和可测试性。主要接口定义如下：

```go
// DeviceMapper 设备映射接口
type DeviceMapper interface {
    // GetPortMapping 根据设备IP和接口索引获取端口映射
    GetPortMapping(deviceIP, ifIndex string) (*PortMapping, error)
    
    // GetDeviceIDByIP 根据设备IP获取设备ID
    GetDeviceIDByIP(deviceIP, sysName, ifIndex string) (string, string, error)
    
    // ClearCache 清除缓存
    ClearCache()
}

// DataProcessor 数据处理器接口
type DataProcessor interface {
    // ProcessData 处理流量数据
    ProcessData(data []byte) error
    
    // GetStats 获取处理统计信息
    GetStats() map[string]interface{}
}

// MetricsExporter 指标导出接口
type MetricsExporter interface {
    // SetGaugeValue 设置仪表盘值
    SetGaugeValue(name string, value float64, labels map[string]string, timestamp ...time.Time)
    
    // SetCounterValue 设置计数器值
    SetCounterValue(name string, value float64, labels map[string]string, timestamp ...time.Time)
    
    // IncrementCounter 增加计数器值
    IncrementCounter(name string, value float64, labels map[string]string)
    
    // Handler 返回HTTP处理程序
    Handler() http.Handler
}

// MessageHandler Kafka消息处理接口
type MessageHandler interface {
    // HandleMessage 处理Kafka消息
    HandleMessage(msg *sarama.ConsumerMessage) error
}
```

接口模式的优势：

1. **关注点分离**：各组件只关注自己的职责，如DeviceMapper只关注映射管理
2. **依赖注入**：组件通过接口注入依赖，而非直接依赖具体实现
3. **模拟测试**：可以通过模拟实现接口进行单元测试，无需真实环境
4. **可替换性**：可以替换组件实现而不影响其他部分，如更换存储引擎

在主程序中，通过构建器模式将各组件组装起来：

```go
// 组件装配示例
func buildFlowDataService(cfg *config.Config) (*Service, error) {
    // 创建设备端口映射器
    mapper, err := NewDevicePortMapper(cfg.Database, logger)
    if err != nil {
        return nil, fmt.Errorf("创建端口映射器失败: %w", err)
    }
    
    // 创建指标导出器
    exporter := NewMetricExporter(zapLogger)
    exporter.RegisterMetrics(cfg.Metrics.Prefix)
    
    // 创建数据处理器
    processor := NewFlowDataProcessor(
        mapper,              // 注入DeviceMapper
        exporter,            // 注入MetricsExporter
        &cfg.Processor,      // 配置
        zapLogger,           // 日志
    )
    
    // 创建消息处理器
    handler := NewFlowDataHandler(processor, zapLogger)
    
    // 创建Kafka消费者
    consumer, err := kafka.NewConsumer(
        &cfg.Kafka,
        handler,            // 注入MessageHandler
        zapLogger,
    )
    
    // 创建HTTP服务器，暴露Prometheus指标
    srv := &http.Server{
        Addr:    cfg.Metrics.ListenAddress,
        Handler: exporter.Handler(),
    }
    
    // 返回完整服务
    return &Service{
        consumer: consumer,
        server:   srv,
        mapper:   mapper,
        logger:   zapLogger,
    }, nil
}
```

这种设计提供高度的模块化和可扩展性，同时便于单元测试和集成测试。

## 3.5 Prometheus 数据存储

### 3.5.1 指标设计概述

本系统的Prometheus指标设计详细内容请参考《19-01-DCI-流量类数据prometheus指标设计.md》文档，该文档全面描述了指标命名规范、标签体系、查询模式和应用场景。

本系统采用存储原始计数器值并通过Prometheus内置函数动态计算流速的设计模式：

1. **核心设计理念**：
   - 存储原始计数器值而非计算值，通过PromQL动态计算流速
   - 为每个指标提供完整的元数据标签，支持多维度查询和分析
   - 使用Telegraf原始采集时间戳，保证数据的时间准确性

2. **主要指标**：
   - 流量计数器指标：`dci_snmp_flow_in_octets`、`dci_snmp_flow_out_octets`等
   - 错误与丢弃指标：`dci_snmp_flow_in_errors`、`dci_snmp_flow_out_discards`等
   - 处理器统计指标：`dci_snmp_flow_processor_messages_total`等

3. **流速计算**：
   - 流速通过PromQL表达式动态计算：`rate(dci_snmp_flow_in_octets[5m]) * 8`
   - 支持不同时间窗口的灵活查询：`[5m]`、`[15m]`、`[1h]`等

### 3.5.2 标签体系

本系统的标签体系设计详见《19-01-DCI-流量类数据prometheus指标设计.md》中的"2.3 标签设计"章节。

系统为每个流量指标提供以下核心标签：

```
device_id="d-12345"
device_name="SW1"
device_ip="************"
port_id="p-67890"
port_name="10GE1/0/45"
if_index="45"
vni_id="6005002" (可选)
```

这些标签与《18-DCI-设备端口流量查询服务设计方案.md》中定义的查询接口完全兼容，确保数据一致性和查询效率。

### 3.5.3 MetricExporter

MetricExporter 组件负责将流量指标数据导出到 Prometheus，是连接数据处理和监控存储的桥梁。该组件实现《19-01-DCI-流量类数据prometheus指标设计.md》中定义的指标规范。

```go
// MetricExporter 指标导出器
type MetricExporter struct {
    registry   *prometheus.Registry   // Prometheus注册表
    gauges     map[string]*prometheus.GaugeVec    // 仪表盘指标集合
    counters   map[string]*prometheus.CounterVec  // 计数器指标集合
    histograms map[string]*prometheus.HistogramVec // 直方图指标集合
    mutex      sync.RWMutex           // 并发锁
    logger     *zap.Logger            // 日志记录器
    labelNames map[string][]string    // 每个指标的标签名称集
}
```

MetricExporter 提供以下核心功能：

1. **指标注册与管理**：负责注册和管理《19-01-DCI-流量类数据prometheus指标设计.md》中定义的所有指标
2. **指标更新**：提供线程安全的指标值更新方法，支持带标签的指标更新
3. **HTTP端点暴露**：通过标准的Prometheus HTTP Handler暴露指标端点
4. **时间戳管理**：支持使用Telegraf原始采集时间戳更新指标

指标注册示例：

```go
// RegisterMetrics 注册所有预定义指标
func (m *MetricExporter) RegisterMetrics(prefix string) {
    // 按照《19-01-DCI-流量类数据prometheus指标设计.md》中的指标定义注册指标
    inOctetsName := prefix + "in_octets"
    outOctetsName := prefix + "out_octets"
    
    // 注册流量计数器指标
    m.RegisterGauge(
        inOctetsName,
        "Input octets on interface (ifHCInOctets raw counter value)",
        []string{"device_id", "device_name", "device_ip", "port_id", "port_name", "if_index", "vni_id"}
    )
    
    // ... 注册其他指标
    
    // 日志输出可用的PromQL示例
    m.logger.Info("可用的PromQL查询示例",
        zap.String("入向流量(bps)", "rate("+inOctetsName+"[5m]) * 8"),
        zap.String("出向流量(bps)", "rate("+outOctetsName+"[5m]) * 8"))
}
```

MetricExporter 负责实现《19-01-DCI-流量类数据prometheus指标设计.md》中定义的所有指标，确保指标命名、类型和标签符合规范。详细的指标定义、查询模式和使用示例请参考该文档。

### 3.5.4 Prometheus 抓取模型

本系统采用Prometheus标准的"拉取"(pull)模型收集指标数据。抓取模型的详细说明请参考《19-01-DCI-流量类数据prometheus指标设计.md》中的相关章节。

系统实现方式概述：

1. **本地指标暴露**：
   - flowdata服务在本地启动HTTP服务器（默认端口30006）
   - 在`/metrics`路径上暴露所有指标数据
   - 使用Prometheus客户端库提供标准格式的指标数据

2. **云端Prometheus抓取**：
   - 云端Prometheus服务器（dciprometheus.intro.citic-x.com:30006）配置抓取任务
   - 定期（通常每15秒）抓取flowdata服务的指标端点
   - 存储时间序列数据并提供查询功能

```mermaid
sequenceDiagram
    participant F as FlowData服务
    participant L as 本地HTTP服务(localhost:30006)
    participant P as 云端Prometheus服务器
    participant Q as 查询服务
    
    F->>L: 启动HTTP服务器
    F->>L: 注册/metrics端点
    F->>L: 实时更新指标数据
    
    loop 每15秒
        P->>L: GET /metrics
        L->>P: 返回所有指标数据
        P->>P: 存储时间序列数据
    end
    
    Q->>P: PromQL查询
    P->>Q: 返回查询结果
```

### 3.5.5 配置与部署

flowdata服务的Prometheus相关配置示例：

```yaml
# Prometheus配置
prometheus:
  # Prometheus服务器地址(仅用于日志记录和文档目的)
  server: "dciprometheus.intro.citic-x.com:30006"
  # 指标路径
  metrics_path: "/metrics"
  # 本地指标HTTP服务端口
  metrics_port: 30006
```

## 3.6 查询接口设计

流量查询服务通过《18-DCI-设备端口流量查询服务设计方案.md》中定义的接口实现。本文档中的数据存储设计确保符合该查询服务的需求，不再重复描述具体的查询API实现。

查询服务将使用《19-01-DCI-流量类数据prometheus指标设计.md》中定义的PromQL查询模式，从Prometheus获取流量数据并进行流速计算。该文档的第4章"流速计算与查询"和第5章"应用场景示例"提供了详细的查询示例和最佳实践。

## 3.7 接口映射同步机制

本设计利用《20-DCI-MySql数据库同步-设备端口VNI基础信息.md》文档中定义的同步机制，避免重复实现接口映射同步功能。流量数据处理服务作为该机制的消费方，通过查询同步后的数据库获取必要的映射信息。

## 4 实施计划

   - 实现DevicePortMapper模块：设备接口映射管理
   - 实现FlowDataProcessor模块：流量数据处理与流速计算
   - 实现FlowDataHandler模块：Kafka消息处理接口
   - 实现MetricExporter模块：Prometheus指标导出
   - 利用现有的Kafka Consumer组件实现消息接收
