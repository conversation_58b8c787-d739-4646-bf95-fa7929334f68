---
title: |
  Prometheus查询接口适配层设计

subtitle: |
  DCI数据监测系统查询接口从SQL到PromQL的转换适配方案
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-14 | 顾铠羟 | 初始版本           |

# 1 方案概述

## 1.1 背景与目标

DCI数据监测系统正在将时序数据库从TDengine迁移到Prometheus+Thanos架构。在此过程中，原有应用系统使用基于SQL的API查询时序数据，而Prometheus生态采用PromQL查询语言。为确保现有应用在架构迁移过程中的平稳过渡，需要设计并实现一个查询接口适配层，将SQL风格的查询转换为PromQL查询，并将结果转换回与原API兼容的格式。

本设计文档旨在：

1. 定义查询接口适配层的架构和工作机制
2. 规范SQL查询到PromQL的转换规则和映射关系
3. 设计查询结果的格式转换策略
4. 确保与现有权限控制系统的无缝集成
5. 提供错误处理和性能优化方案

## 1.2 适用范围

本设计文档适用于：

1. DCI数据监测系统中的查询接口适配层实现
2. 原有应用系统与Prometheus+Thanos架构的集成
3. 系统开发、测试和运维人员，用于理解查询接口转换规则

## 1.3 关键指标

查询适配层需要满足以下关键指标：

1. **性能指标**：
   - 简单查询适配层处理延迟<50ms
   - 复杂查询适配层处理延迟<200ms
   - 支持并发查询处理能力>100 QPS

2. **兼容性指标**：
   - 支持>95%的现有SQL查询场景
   - 保持与现有API的返回格式完全兼容

3. **可靠性指标**：
   - 服务可用性>99.9%
   - 查询错误率<0.1%

4. **安全性指标**：
   - 与现有权限体系100%集成
   - 所有查询均需身份验证和授权

# 2 技术架构

## 2.1 整体架构

查询接口适配层作为现有应用与Prometheus+Thanos架构的桥梁，采用API网关模式：

```mermaid
graph LR
    subgraph "现有应用"
        App1[应用1]
        App2[应用2]
        App3[应用3]
    end
    
    subgraph "查询接口适配层"
        Gateway[API网关]
        Parser[SQL解析器]
        Converter[PromQL转换器]
        ResultAdapter[结果适配器]
        ErrorHandler[错误处理]
        Cache[查询缓存]
    end
    
    subgraph "Prometheus+Thanos"
        Thanos[Thanos Query]
    end
    
    subgraph "权限控制"
        Auth[认证授权]
        TenantFilter[租户过滤]
    end
    
    App1 & App2 & App3 -->|SQL查询| Gateway
    Gateway -->|身份验证| Auth
    Auth -->|授权查询| Parser
    Parser -->|AST| Converter
    Converter -->|检查缓存| Cache
    Cache -->|缓存未命中| Thanos
    Converter -->|PromQL| Thanos
    Thanos -->|查询结果| ResultAdapter
    ResultAdapter -->|格式化结果| Gateway
    Gateway -->|SQL结果| App1 & App2 & App3
    ErrorHandler -.->|错误处理| Gateway
    TenantFilter -.->|数据隔离| Converter
```

## 2.2 关键组件

### 2.2.1 API网关

API网关是适配层的入口，负责：

1. 接收和验证来自应用的HTTP请求
2. 进行身份验证和授权检查
3. 路由请求到适当的处理组件
4. 返回处理结果给客户端
5. 处理异常和错误情况
6. 执行API速率限制和监控

### 2.2.2 SQL解析器

SQL解析器负责：

1. 解析SQL查询语句的语法
2. 构建抽象语法树(AST)
3. 验证查询语句的合法性
4. 提取查询中的关键元素（表名、列名、条件等）

示例实现方案：
- 使用go-sqlparser或其他SQL解析库
- 构建自定义验证规则，限制支持的SQL语法子集
- 针对时序数据查询的特殊处理

### 2.2.3 PromQL转换器

PromQL转换器是核心组件，负责：

1. 将SQL AST转换为PromQL表达式
2. 处理指标名称的映射
3. 应用租户过滤条件
4. 处理聚合函数转换
5. 处理时间范围和分辨率

### 2.2.4 结果适配器

结果适配器负责：

1. 接收Prometheus/Thanos返回的数据
2. 将数据转换为与原SQL API兼容的格式
3. 处理数据类型转换
4. 应用必要的后处理（如排序、分页等）

### 2.2.5 错误处理

错误处理组件负责：

1. 捕获和分类处理过程中的错误
2. 转换Prometheus错误为与原API兼容的错误码和消息
3. 提供详细的错误日志和诊断信息
4. 处理查询超时和降级策略

### 2.2.6 查询缓存

查询缓存组件负责：

1. 缓存常用查询的结果
2. 根据缓存策略决定是否使用缓存
3. 维护缓存的一致性和过期策略
4. 优化高频查询的性能

## 2.3 数据流

以下序列图展示了一个典型查询的数据流：

```mermaid
sequenceDiagram
    participant App as 应用
    participant Gateway as API网关
    participant Auth as 认证授权
    participant Parser as SQL解析器
    participant Converter as PromQL转换器
    participant Cache as 查询缓存
    participant Thanos as Thanos Query
    participant ResultAdapter as 结果适配器
    
    App->>Gateway: 发送SQL查询
    Gateway->>Auth: 验证请求权限
    Auth->>Gateway: 授权结果
    
    alt 授权失败
        Gateway->>App: 返回401/403错误
    else 授权成功
        Gateway->>Parser: 解析SQL
        Parser->>Converter: 传递AST
        
        Converter->>Cache: 检查缓存
        
        alt 缓存命中
            Cache->>ResultAdapter: 返回缓存结果
        else 缓存未命中
            Converter->>Thanos: 发送PromQL查询
            Thanos->>ResultAdapter: 返回查询结果
            ResultAdapter->>Cache: 更新缓存
        end
        
        ResultAdapter->>Gateway: 返回格式化结果
        Gateway->>App: 返回SQL格式结果
    end
```

# 3 实现细节

## 3.1 SQL到PromQL的转换规则

### 3.1.1 基本查询转换

| SQL查询模式 | PromQL转换 | 说明 |
|------------|-----------|------|
| `SELECT value FROM metric WHERE time > ? AND time < ?` | `metric{job="..."}[时间范围]` | 基本时间序列查询 |
| `SELECT AVG(value) FROM metric WHERE time > ? GROUP BY time(5m)` | `avg_over_time(metric[5m])` | 时间聚合查询 |
| `SELECT SUM(value) FROM metric WHERE device_id = ? GROUP BY time(5m)` | `sum(metric{device_id="..."})` | 带标签过滤的聚合查询 |

### 3.1.2 时间函数映射

| SQL时间函数 | PromQL等价函数 | 说明 |
|------------|--------------|------|
| `NOW()` | `now()` | 当前时间 |
| `TIME_DIFF(t1, t2)` | 由适配层计算 | 时间差 |
| `DATE_FORMAT(time, format)` | 由适配层转换 | 格式化时间 |

### 3.1.3 聚合函数映射

| SQL聚合函数 | PromQL等价函数 | 说明 |
|------------|--------------|------|
| `AVG(value)` | `avg(metric)` | 平均值 |
| `SUM(value)` | `sum(metric)` | 总和 |
| `COUNT(value)` | `count(metric)` | 计数 |
| `MAX(value)` | `max(metric)` | 最大值 |
| `MIN(value)` | `min(metric)` | 最小值 |
| `PERCENTILE(value, 95)` | `histogram_quantile(0.95, sum(rate(metric_bucket[5m])) by (le))` | 百分位数 |

### 3.1.4 复杂查询转换示例

**原SQL查询**：
```sql
SELECT 
    device_id, 
    interface, 
    AVG(rx_bytes) as avg_rx,
    MAX(rx_bytes) as max_rx 
FROM network_interface 
WHERE 
    time > '2025-01-01 00:00:00' AND 
    time < '2025-01-02 00:00:00' AND 
    device_id IN ('dev1', 'dev2') 
GROUP BY device_id, interface, time(1h)
```

**转换后的PromQL**：
```
avg_over_time(dci_network_interface_received_bytes_total{device_id=~"dev1|dev2"}[1h])
max_over_time(dci_network_interface_received_bytes_total{device_id=~"dev1|dev2"}[1h])
```

**处理说明**：
1. 将表名`network_interface`映射到Prometheus指标名`dci_network_interface_received_bytes_total`
2. 将`WHERE`子句中的设备过滤条件转换为标签选择器`{device_id=~"dev1|dev2"}`
3. 将`GROUP BY time(1h)`转换为`[1h]`时间窗口
4. 将`AVG`和`MAX`聚合函数转换为`avg_over_time`和`max_over_time`
5. 查询结果会包含`device_id`和`interface`标签

## 3.2 查询结果格式转换

### 3.2.1 Prometheus结果格式

Prometheus API返回的典型JSON格式：

```json
{
  "status": "success",
  "data": {
    "resultType": "matrix",
    "result": [
      {
        "metric": {
          "device_id": "dev1",
          "interface": "eth0"
        },
        "values": [
          [1609459200, "1024"],
          [1609462800, "2048"],
          ...
        ]
      },
      ...
    ]
  }
}
```

### 3.2.2 SQL查询结果格式

SQL查询期望的结果格式：

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "device_id": "dev1",
      "interface": "eth0",
      "time": "2025-01-01T00:00:00Z",
      "avg_rx": 1024
    },
    {
      "device_id": "dev1",
      "interface": "eth0",
      "time": "2025-01-01T01:00:00Z",
      "avg_rx": 2048
    },
    ...
  ],
  "total": 24
}
```

### 3.2.3 转换规则

结果适配器将执行以下转换：

1. 将Prometheus数据点数组转换为记录数组
2. 将标签值作为记录字段
3. 时间戳转换为ISO 8601格式的时间字符串
4. 数值类型转换（字符串→数字）
5. 添加分页信息（总记录数等）
6. 修改状态码和消息格式以匹配原API

### 3.2.4 错误映射

| Prometheus错误 | 适配层错误码 | 错误消息 |
|--------------|------------|--------|
| 执行超时 | 5001 | "查询执行超时" |
| 解析错误 | 4001 | "查询语法错误" |
| 服务器错误 | 5000 | "服务器内部错误" |
| 内存溢出 | 5002 | "查询内存超限" |

## 3.3 权限控制集成

### 3.3.1 认证机制

适配层将沿用现有认证系统：

1. 支持Bearer Token认证
2. JWT令牌验证
3. 与现有身份提供者集成

### 3.3.2 租户数据隔离

基于`monitor_tenant_map`表的租户数据隔离：

1. 在解析SQL后，从认证上下文获取租户ID
2. 查询`monitor_tenant_map`表，获取租户可访问的资源列表
3. 将租户筛选条件作为附加标签选择器添加到PromQL查询中

示例伪代码：
```go
func addTenantFilter(promQuery string, tenantID string) string {
    // 获取租户可访问的设备列表
    allowedDevices := getDevicesForTenant(tenantID)
    
    // 构建设备ID正则表达式
    deviceFilter := strings.Join(allowedDevices, "|")
    
    // 添加到查询中
    if strings.Contains(promQuery, "{") {
        // 已有标签选择器，添加设备过滤条件
        return strings.Replace(promQuery, "{", "{device_id=~\""+deviceFilter+"\",", 1)
    } else {
        // 没有标签选择器，创建新的
        return promQuery + "{device_id=~\"" + deviceFilter + "\"}"
    }
}
```

### 3.3.3 操作权限控制

适配层将维护操作权限映射表：

| 操作类型 | 权限标识 | 说明 |
|---------|---------|------|
| 读取指标 | metrics:read | 允许查询基本指标 |
| 查询告警 | alerts:read | 允许查询告警状态 |
| 执行聚合 | metrics:aggregate | 允许执行聚合查询 |
| 查询历史数据 | metrics:history | 允许查询长期历史数据 |

## 3.4 缓存策略

### 3.4.1 缓存级别

适配层实现两级缓存：

1. **查询缓存**：缓存解析后的PromQL查询和参数
2. **结果缓存**：缓存查询结果

### 3.4.2 缓存键生成

缓存键生成策略：

```
cache_key = hash(SQL查询 + 用户上下文 + 参数值)
```

### 3.4.3 缓存失效策略

1. **基于时间**：设置缓存TTL，默认60秒
2. **主动刷新**：数据更新时主动失效相关缓存
3. **按租户隔离**：不同租户的缓存相互隔离

## 3.5 性能优化

### 3.5.1 查询优化

1. **限制时间范围**：防止超大范围查询
2. **添加分辨率参数**：长时间范围查询使用较低分辨率
3. **分解复杂查询**：将复杂SQL拆分为多个简单PromQL查询

### 3.5.2 资源控制

1. **查询超时控制**：设置默认超时时间（30秒）
2. **并发限制**：限制单IP并发查询数量
3. **请求队列**：实现请求排队机制

# 4 部署架构

## 4.1 部署拓扑

查询接口适配层的部署架构：

```mermaid
graph TD
    subgraph "Kubernetes集群"
        subgraph "查询适配层"
            Adapter1[适配层Pod1]
            Adapter2[适配层Pod2]
            Adapter3[适配层Pod3]
            AdapterSvc[适配层Service]
        end
        
        subgraph "Thanos层"
            Thanos1[Thanos Query1]
            Thanos2[Thanos Query2]
            Thanos3[Thanos Query3]
            ThanosSvc[Thanos Service]
        end
        
        subgraph "缓存层"
            Redis1[Redis主]
            Redis2[Redis从]
            RedisSvc[Redis Service]
        end
        
        subgraph "配置层"
            Config[ConfigMap]
            Secret[Secret]
        end
    end
    
    subgraph "外部应用"
        App[应用系统]
        Ingress[Ingress]
    end
    
    App -->|HTTP请求| Ingress
    Ingress -->|路由| AdapterSvc
    AdapterSvc --> Adapter1 & Adapter2 & Adapter3
    Adapter1 & Adapter2 & Adapter3 -->|PromQL查询| ThanosSvc
    ThanosSvc --> Thanos1 & Thanos2 & Thanos3
    Adapter1 & Adapter2 & Adapter3 -->|缓存| RedisSvc
    RedisSvc --> Redis1
    Redis1 --> Redis2
    
    Config -.->|配置| Adapter1 & Adapter2 & Adapter3
    Secret -.->|密钥| Adapter1 & Adapter2 & Adapter3
```

## 4.2 容量规划

| 组件 | 副本数 | CPU请求 | 内存请求 | 存储 |
|------|-------|---------|---------|------|
| 适配层服务 | 3 | 1 | 2GB | - |
| Redis缓存 | 2 (1主1从) | 1 | 4GB | 10GB |

## 4.3 高可用设计

适配层的高可用设计包括：

1. **多副本部署**：至少3个适配层Pod确保服务可用性
2. **负载均衡**：通过K8s Service和反亲和性规则分散负载
3. **故障检测**：健康检查和就绪探针确保流量只发送到健康实例
4. **限流和熔断**：防止过载和级联故障
5. **Redis哨兵**：确保缓存高可用

# 5 运维与监控

## 5.1 监控指标

适配层将暴露以下关键指标：

| 指标名称 | 类型 | 描述 |
|---------|------|------|
| `dci_adapter_requests_total` | Counter | 请求总数 |
| `dci_adapter_request_duration_seconds` | Histogram | 请求处理时间 |
| `dci_adapter_errors_total` | Counter | 错误总数 |
| `dci_adapter_cache_hits_total` | Counter | 缓存命中次数 |
| `dci_adapter_cache_misses_total` | Counter | 缓存未命中次数 |
| `dci_adapter_active_requests` | Gauge | 当前活跃请求数 |
| `dci_adapter_queue_size` | Gauge | 查询队列大小 |
| `dci_adapter_memory_usage_bytes` | Gauge | 内存使用量 |

## 5.2 告警策略

| 告警名称 | 触发条件 | 严重性 | 处理方式 |
|---------|---------|-------|---------|
| 高错误率 | `sum(rate(dci_adapter_errors_total[5m])) / sum(rate(dci_adapter_requests_total[5m])) > 0.05` | 严重 | 通知负责人，自动扩容 |
| 高延迟 | `histogram_quantile(0.95, sum(rate(dci_adapter_request_duration_seconds_bucket[5m])) by (le)) > 1` | 警告 | 监控性能，可能需要优化 |
| 内存使用过高 | `max(dci_adapter_memory_usage_bytes) / 2e9 > 0.8` | 警告 | 检查内存泄漏，考虑扩容 |

## 5.3 日常维护

### 5.3.1 配置管理

适配层配置通过ConfigMap管理，包括：

1. SQL解析规则配置
2. 映射关系配置
3. 缓存策略配置
4. 超时和限流配置

### 5.3.2 日志管理

日志采集策略：

1. 标准输出/错误流采集到ELK
2. 结构化日志格式（JSON）
3. 按级别分类（ERROR, WARN, INFO, DEBUG）
4. 关键操作审计日志

### 5.3.3 版本升级

升级策略：

1. 蓝绿部署模式
2. 版本兼容性测试
3. 灰度发布流程
4. 回滚计划

# 6 安全设计

## 6.1 数据安全

1. **传输加密**：所有API通信使用TLS加密
2. **数据脱敏**：敏感数据脱敏处理
3. **查询参数验证**：防止注入攻击

## 6.2 访问控制

1. **多租户隔离**：租户数据严格隔离
2. **最小权限原则**：适配层仅具有必要的Thanos访问权限
3. **审计日志**：记录所有查询和认证操作

# 7 实施计划

## 7.1 实现步骤

| 阶段 | 工作内容 | 时间估计 |
|------|---------|---------|
| 设计完善 | 细化转换规则和API定义 | 1周 |
| 核心开发 | 实现SQL解析和PromQL转换 | 3周 |
| 集成开发 | 实现权限控制和结果适配 | 2周 |
| 测试验证 | 单元测试和集成测试 | 2周 |
| 性能优化 | 缓存实现和性能调优 | 1周 |
| 部署上线 | 环境配置和部署 | 1周 |

# 8 测试方案

## 8.1 测试策略

测试将覆盖以下方面：

1. **功能测试**：验证SQL转换和结果适配的正确性
2. **性能测试**：验证适配层的吞吐量和延迟
3. **兼容性测试**：验证与现有应用的兼容性
4. **安全测试**：验证权限控制和数据隔离

## 8.2 测试用例

| 测试场景 | 测试内容 | 验收标准 |
|---------|---------|---------|
| 基本查询转换 | 验证简单SQL到PromQL的转换 | 查询结果正确且格式兼容 |
| 聚合查询转换 | 验证带聚合函数的SQL转换 | 聚合结果正确且格式兼容 |
| 租户数据隔离 | 验证不同租户只能查询授权数据 | 严格按权限返回数据，无越权访问 |
| 高并发查询 | 模拟多用户并发查询 | 满足性能指标，无错误 |
| 缓存效果 | 验证缓存命中率和性能提升 | 缓存命中率>80%，性能提升明显 |

# 9 风险与应对

| 风险点 | 影响程度 | 应对措施 |
| ------ | -------- | -------- |
| SQL解析复杂度高 | 高 | 限制支持的SQL语法子集，为常用查询提供模板 |
| 查询性能不达标 | 高 | 实现多级缓存，优化转换逻辑，增加并发处理能力 |
| PromQL表达能力限制 | 中 | 明确不支持功能的边界，提供替代方案 |
| 现有应用兼容性问题 | 高 | 全面测试现有查询场景，建立应用改造指南 |
| 数据授权模型复杂 | 中 | 简化授权逻辑，建立明确的数据访问规则 |

# 10 附录

## 10.1 参考文档

1. [Prometheus查询API文档](https://prometheus.io/docs/prometheus/latest/querying/api/)
2. [PromQL查询语言文档](https://prometheus.io/docs/prometheus/latest/querying/basics/)
3. [Thanos查询API文档](https://thanos.io/tip/components/query.md/)
4. [SQL解析库go-sqlparser](https://github.com/pingcap/parser)
5. [DCI监测系统Prometheus+Thanos架构设计文档](../07-DCI-Prometheus和Thanos架构设计.md)
6. [DCI监测系统指标和标签规范](../08-DCI-Prometheus指标和标签规范.md)

## 10.2 API规范

### 查询接口示例

```
GET /api/v1/query

请求参数：
{
  "sql": "SELECT device_id, AVG(cpu_usage) FROM server_metrics WHERE time > now() - 1h GROUP BY device_id, time(5m)",
  "format": "json",
  "timeout": "30s"
}

响应：
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "device_id": "server-001",
      "time": "2025-01-01T12:00:00Z",
      "avg": 78.5
    },
    ...
  ],
  "total": 12
}
``` 