---
title: |
  DCI-统一告警模型设计

subtitle: |
  跨数据源的统一告警数据模型与处理机制
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-06-24 | 顾铠羟 | 初始版本           |
| V1.1 | 2025-07-04 | 顾铠羟 | 优化告警时间维度设计，增加通知时间与解决时间字段 |

# 1 文档介绍

## 1.1 文档目的

本文档详细描述DCI监测系统中统一告警模型的设计方案，该模型旨在标准化来自不同数据源（如Prometheus指标和Elasticsearch日志）的告警信息，实现统一管理和处理。该设计是《24-DCI-异常告警检测技术方案设计》的补充文档，重点关注告警数据模型的设计与实现。

## 1.2 文档范围

本文档涵盖以下内容：
- 统一告警数据模型的结构设计
- 告警数据的存储方案
- 不同数据源告警的转换机制
- 告警状态管理与生命周期
- 告警分组与聚合策略
- 告警关联与抑制机制
- 告警通知路由设计

## 1.3 文档关联

本文档与以下文档相关联：
- 《24-DCI-异常告警检测技术方案设计.md》：作为其补充文档，提供告警数据模型的详细设计
- 《24-01-DCI-prometheus指标类数据AlertManager技术方案设计.md》：提供Prometheus告警机制的设计
- 《08-DCI-Prometheus指标和标签规范.md》：提供指标命名和标签规范

# 2 总体设计

## 2.1 设计目标

1. 设计统一的告警数据模型，支持不同来源的告警信息
2. 实现灵活的告警状态管理机制，支持告警的完整生命周期
3. 提供高效的告警存储和查询能力
4. 设计告警分组和关联机制，减少告警风暴
5. 支持基于告警属性的通知路由策略

## 2.2 架构设计

### 2.2.1 详细架构图

```mermaid
graph TD
    subgraph "数据源层"
        Prometheus["Prometheus<br/>指标告警"]
        ES["Elasticsearch<br/>日志告警"]
        Other["其他告警源<br/>(可扩展)"]
    end
    
    subgraph "转换层"
        PromConverter["Prometheus<br/>告警转换器"]
        ESConverter["ES告警<br/>转换器"]
        OtherConverter["其他告警<br/>转换器"]
    end
    
    subgraph "统一告警模型"
        AlertModel["统一告警模型<br/>(Alert)"]
        AlertRule["告警规则模型<br/>(AlertRule)"]
        AlertNotification["告警通知模型<br/>(AlertNotification)"]
    end
    
    subgraph "处理层"
        StateManager["状态管理器"]
        GroupingEngine["分组引擎"]
        CorrelationEngine["关联引擎"]
        RoutingEngine["路由引擎"]
    end
    
    subgraph "存储层"
        MySQL[(MySQL数据库)]
    end
    
    Prometheus --> PromConverter
    ES --> ESConverter
    Other --> OtherConverter
    
    PromConverter --> AlertModel
    ESConverter --> AlertModel
    OtherConverter --> AlertModel
    
    AlertModel --> StateManager
    AlertModel --> GroupingEngine
    AlertModel --> CorrelationEngine
    
    StateManager --> AlertModel
    GroupingEngine --> AlertModel
    CorrelationEngine --> AlertModel
    
    AlertModel --> RoutingEngine
    AlertRule --> RoutingEngine
    AlertNotification --> RoutingEngine
    
    AlertModel --> MySQL
    AlertRule --> MySQL
    AlertNotification --> MySQL
```

### 2.2.2 极简架构图

```mermaid
sequenceDiagram
    participant DataSources as 告警数据源
    participant Converters as 告警转换器
    participant Model as 统一告警模型
    participant Processor as 告警处理器
    participant Storage as 告警存储
    
    DataSources->>Converters: 原始告警数据
    Converters->>Model: 标准化告警
    Model->>Processor: 告警处理
    Processor->>Model: 更新状态
    Model->>Storage: 存储告警
```

## 2.3 数据流/流程图

```mermaid
sequenceDiagram
    participant Source as 告警源
    participant Converter as 转换器
    participant Model as 告警模型
    participant StateManager as 状态管理器
    participant Storage as 数据库
    participant Notifier as 通知服务
    
    Source->>Converter: 产生告警
    Converter->>Model: 转换为统一模型
    Model->>StateManager: 处理告警状态
    
    alt 新告警
        StateManager->>Storage: 创建告警记录
        StateManager->>Notifier: 发送通知
    else 已存在告警更新
        StateManager->>Storage: 更新告警状态
        alt 状态变化
            StateManager->>Notifier: 发送状态变更通知
        end
    else 告警解决
        StateManager->>Storage: 更新为已解决
        StateManager->>Notifier: 发送解决通知
    end
```

## 2.4 模块化设计

统一告警模型系统包含以下主要模块：

1. **告警转换器**：负责将不同来源的告警转换为统一模型
2. **告警状态管理器**：管理告警的生命周期和状态转换
3. **告警分组引擎**：根据配置的规则对告警进行分组
4. **告警关联引擎**：分析告警之间的关联关系，实现抑制和关联
5. **告警路由引擎**：根据告警属性确定通知渠道
6. **告警存储管理器**：负责告警数据的持久化和查询

## 2.5 技术选型

1. **数据存储**：MySQL数据库，与现有系统共用
2. **JSON处理**：利用MySQL的JSON类型存储动态标签和注释
3. **查询优化**：使用适当的索引和JSON路径查询优化性能
4. **开发语言**：Go语言，与现有dcimonitor系统保持一致

# 3 详细设计

## 3.1 功能模块

### 3.1.1 统一告警数据模型

统一告警模型的核心数据结构设计：

```mermaid
classDiagram
    class Alert {
        +string ID
        +string Name
        +string Source
        +string Level
        +string Status
        +string Description
        +map[string]string Labels
        +map[string]string Annotations
        +time.Time StartsAt
        +time.Time EndsAt
        +time.Time NotificationDueAt
        +time.Time ResolvedAt
        +time.Time UpdatedAt
        +string GeneratorURL
        +string RuleID
        +string AcknowledgedBy
        +time.Time AcknowledgedAt
        +string Note
    }
    
    class AlertRule {
        +string ID
        +string Name
        +string Description
        +string Source
        +string Expression
        +string Level
        +int Duration
        +map[string]string Labels
        +map[string]string Annotations
        +bool Enabled
        +time.Time CreatedAt
        +time.Time UpdatedAt
    }
    
    class AlertNotification {
        +string ID
        +string Name
        +string Type
        +Object Settings
        +bool Enabled
        +time.Time CreatedAt
        +time.Time UpdatedAt
    }
    
    Alert --> AlertRule: 由规则生成
    Alert --> AlertNotification: 通过通知发送
```

### 3.1.2 告警转换机制

设计不同数据源告警的转换逻辑：

```mermaid
sequenceDiagram
    participant PromAlert as Prometheus告警
    participant ESAlert as ES告警
    participant Converter as 转换器
    participant UnifiedAlert as 统一告警模型
    
    PromAlert->>Converter: Webhook回调数据
    Converter->>Converter: 提取告警属性
    Converter->>Converter: 映射标签和注释
    Converter->>Converter: 生成唯一标识符
    Converter->>UnifiedAlert: 创建统一告警实例
    
    ESAlert->>Converter: ES查询结果
    Converter->>Converter: 构建告警属性
    Converter->>Converter: 设置标签和注释
    Converter->>Converter: 生成唯一标识符
    Converter->>UnifiedAlert: 创建统一告警实例
```

#### Prometheus告警转换示例

```go
// 将Prometheus告警转换为统一告警模型
func ConvertPrometheusAlert(promAlert *PrometheusWebhookAlert) *Alert {
    alert := &Alert{
        ID:          generateAlertID(promAlert),
        Name:        promAlert.Labels["alertname"],
        Source:      "prometheus",
        Level:       promAlert.Labels["severity"],
        Status:      promAlert.Status,
        Description: promAlert.Annotations["description"],
        Labels:      promAlert.Labels,
        Annotations: promAlert.Annotations,
        StartsAt:    promAlert.StartsAt,
        EndsAt:      promAlert.EndsAt,
        GeneratorURL: promAlert.GeneratorURL,
    }
    
    // 查找关联的规则ID
    alert.RuleID = findRuleIDByName(promAlert.Labels["alertname"])
    
    return alert
}
```

#### Elasticsearch告警转换示例

```go
// 将ES告警转换为统一告警模型
func ConvertESAlert(esAlert *ESAlert) *Alert {
    alert := &Alert{
        ID:          esAlert.ID,
        Name:        esAlert.Name,
        Source:      "elasticsearch",
        Level:       esAlert.Level,
        Status:      "firing",
        Description: esAlert.Description,
        Labels:      esAlert.Labels,
        Annotations: map[string]string{
            "query": esAlert.Query,
            "hits": fmt.Sprintf("%d", esAlert.HitCount),
        },
        StartsAt:    time.Now(),
        RuleID:      esAlert.RuleID,
    }
    
    return alert
}
```

### 3.1.3 告警状态管理

告警状态管理机制设计：

```mermaid
stateDiagram-v2
    [*] --> Firing: 触发告警
    Firing --> Acknowledged: 确认告警
    Firing --> Resolved: 自动解决
    Acknowledged --> Resolved: 手动解决
    Resolved --> [*]: 归档(可选)
```

状态转换处理逻辑：

```go
// 处理告警状态变更
func HandleAlertStateChange(alert *Alert, existingAlert *Alert) {
    // 当前本地时间
    now := time.Now().In(time.Local)
    
    // 新告警
    if existingAlert == nil {
        alert.Status = "firing"
        // 确保所有时间字段使用本地时区
        alert.StartsAt = alert.StartsAt.In(time.Local)
        
        if alert.EndsAt.After(now) {
            // 对于未解决的告警，EndsAt表示下次通知时间，将其保存到NotificationDueAt
            alert.NotificationDueAt = alert.EndsAt.In(time.Local)
            alert.EndsAt = time.Time{} // 清空EndsAt，因为告警尚未解决
        }
        
        alert.UpdatedAt = now
        return
    }
    
    // 已存在的告警
    switch {
    // 告警解决
    case alert.Status == "resolved":
        alert.EndsAt = now            // 设置结束时间为当前时间
        alert.ResolvedAt = now        // 设置解决时间
        alert.NotificationDueAt = time.Time{} // 清空通知截止时间，因为已解决
        
    // 告警仍在触发，但已被确认
    case existingAlert.Status == "acknowledged":
        alert.Status = "acknowledged"
        alert.AcknowledgedBy = existingAlert.AcknowledgedBy
        alert.AcknowledgedAt = existingAlert.AcknowledgedAt
        alert.Note = existingAlert.Note
        
        // 更新通知截止时间，保留原始触发时间
        alert.StartsAt = existingAlert.StartsAt
        alert.NotificationDueAt = alert.EndsAt.In(time.Local) // 保存AlertManager的下次通知时间
        alert.EndsAt = time.Time{} // 清空EndsAt，因为告警尚未解决
        
    // 默认保持firing状态
    default:
        alert.Status = "firing"
        
        // 保留原始触发时间
        alert.StartsAt = existingAlert.StartsAt
        
        // 更新通知截止时间
        alert.NotificationDueAt = alert.EndsAt.In(time.Local)
        alert.EndsAt = time.Time{} // 清空EndsAt，因为告警尚未解决
    }
    
    alert.UpdatedAt = now
}
```

### 3.1.4 告警分组与聚合

告警分组策略设计：

```mermaid
graph TD
    Alert["告警"] --> ByName["按名称分组"]
    Alert --> ByDevice["按设备分组"]
    Alert --> ByLevel["按级别分组"]
    Alert --> ByStatus["按状态分组"]
    
    ByDevice --> DeviceGroup["设备组"]
    DeviceGroup --> DeviceCount["告警计数"]
    DeviceGroup --> HighestSeverity["最高级别"]
    DeviceGroup --> LatestAlert["最新告警"]
```

聚合查询示例：

```sql
-- 按设备分组统计告警数量
SELECT 
    JSON_EXTRACT(labels, '$.device_id') AS device_id,
    level,
    status,
    COUNT(*) AS alert_count
FROM 
    alert
WHERE 
    status != 'resolved'
GROUP BY 
    JSON_EXTRACT(labels, '$.device_id'), level, status
ORDER BY 
    alert_count DESC;
```

### 3.1.5 告警关联与抑制【优先级低，暂不开发】

告警关联规则设计：

```mermaid
graph TD
    subgraph "抑制规则示例"
        DeviceDown["设备离线告警"] -->|抑制| ServiceAlerts["设备服务告警"]
        InterfaceDown["接口Down告警"] -->|抑制| TrafficAlerts["接口流量告警"]
        HighSeverity["高级别告警"] -->|抑制| LowSeverity["同设备低级别告警"]
    end
    
    subgraph "关联规则示例"
        CPUAlert["CPU告警"] -->|关联| MemoryUsage["内存使用情况"]
        TrafficSpike["流量尖峰"] -->|关联| ErrorLogs["错误日志"]
        InterfaceFlap["接口抖动"] -->|关联| ConfigChanges["配置变更"]
    end
```

抑制规则实现示例：

```go
// 检查告警是否应该被抑制
func ShouldSuppressAlert(alert *Alert, activeAlerts []*Alert) bool {
    // 如果存在同一设备的更高级别告警，则抑制当前告警
    deviceID := alert.Labels["device_id"]
    if deviceID == "" {
        return false
    }
    
    for _, activeAlert := range activeAlerts {
        // 同一设备的更高级别告警
        if activeAlert.Labels["device_id"] == deviceID && 
           isHigherSeverity(activeAlert.Level, alert.Level) {
            return true
        }
        
        // 设备离线告警抑制该设备的其他告警
        if activeAlert.Labels["device_id"] == deviceID && 
           activeAlert.Name == "DeviceDown" {
            return true
        }
        
        // 接口Down告警抑制该接口的流量告警
        if activeAlert.Labels["device_id"] == deviceID &&
           activeAlert.Labels["ifName"] == alert.Labels["ifName"] &&
           activeAlert.Name == "InterfaceDown" &&
           (alert.Name == "InterfaceTrafficHigh" || alert.Name == "InterfaceTrafficLow") {
            return true
        }
    }
    
    return false
}
```

### 3.1.6 设备ID提取与任务关联机制

#### 设备ID提取策略

告警处理过程中需要从告警Labels中提取设备标识信息，用于设备维度的告警统计和任务协同监控。

**设备ID提取逻辑**：

```go
// 从告警Labels中提取设备ID
func ExtractDeviceID(labels map[string]string) string {
    // 优先级1: 直接的device_id标签
    if deviceID, exists := labels["device_id"]; exists && deviceID != "" {
        return deviceID
    }
    
    // 优先级2: 从instance标签解析设备信息
    if instance, exists := labels["instance"]; exists && instance != "" {
        // 如果instance是IP地址格式，直接返回
        if isValidIP(instance) {
            return instance
        }
        
        // 如果instance包含端口，去除端口部分
        if strings.Contains(instance, ":") {
            parts := strings.Split(instance, ":")
            if len(parts) > 0 && isValidIP(parts[0]) {
                return parts[0]
            }
        }
    }
    
    // 优先级3: 其他可能的设备标识标签
    for _, field := range []string{"device_ip", "host", "node"} {
        if value, exists := labels[field]; exists && value != "" {
            return value
        }
    }
    
    return ""
}

// 验证IP地址有效性
func isValidIP(ip string) bool {
    return net.ParseIP(ip) != nil
}
```

**设备ID提取示例**：

```go
// Prometheus告警Labels示例
labels1 := map[string]string{
    "device_id": "switch001",
    "device_ip": "************", 
    "instance": "************:9100",
}
// 提取结果: "switch001"

labels2 := map[string]string{
    "instance": "************:9100",
    "job": "snmp-devices",
}
// 提取结果: "************"

labels3 := map[string]string{
    "host": "core-switch-01", 
    "job": "network-monitoring",
}
// 提取结果: "core-switch-01"
```

#### 任务关联机制

告警处理过程中，基于提取的设备ID查询当前活跃的网络自动化任务，实现告警与任务的自动关联。

**关联查询逻辑**：

```go
// 任务关联查询结果
type TaskAssociationResult struct {
    TaskID    string `json:"taskId"`
    AlertType string `json:"alertType"` // "active" 或 "post_task"
}

// 查询设备相关的任务(包含延展监测)
func GetTaskByDeviceWithExtendedMonitoring(deviceID string) *TaskAssociationResult {
    if deviceID == "" {
        return &TaskAssociationResult{}
    }
    
    // 查询活跃任务会话
    activeTask := queryActiveTaskSession(deviceID)
    if activeTask != nil {
        return &TaskAssociationResult{
            TaskID:    activeTask.TaskID,
            AlertType: "active",
        }
    }
    
    // 查询任务结束后延展监测期
    extendedTask := queryExtendedMonitoringSession(deviceID)
    if extendedTask != nil {
        return &TaskAssociationResult{
            TaskID:    extendedTask.TaskID,
            AlertType: "post_task",
        }
    }
    
    return &TaskAssociationResult{}
}

// 查询活跃任务会话
func queryActiveTaskSession(deviceID string) *TaskSession {
    query := `
        SELECT task_id, device_ids, start_time 
        FROM monitor_network_auto_task_monitoring_sessions 
        WHERE status = 'active' 
        AND JSON_CONTAINS(device_ids, ?)
        AND start_time <= NOW()
    `
    // 执行查询逻辑...
    return nil
}

// 查询延展监测期任务会话
func queryExtendedMonitoringSession(deviceID string) *TaskSession {
    query := `
        SELECT task_id, device_ids, end_time 
        FROM monitor_network_auto_task_monitoring_sessions 
        WHERE status = 'completed' 
        AND JSON_CONTAINS(device_ids, ?)
        AND end_time IS NOT NULL
        AND NOW() BETWEEN end_time AND DATE_ADD(end_time, INTERVAL 1 HOUR)
    `
    // 执行查询逻辑...
    return nil
}
```

#### 告警元数据增强

基于设备ID和任务关联结果，增强告警的元数据信息。

**元数据增强逻辑**：

```go
// 增强告警元数据
func EnhanceAlertMetadata(alert *Alert, deviceID string, taskResult *TaskAssociationResult) {
    // 确保annotations字段存在
    if alert.Annotations == nil {
        alert.Annotations = make(map[string]string)
    }
    
    // 设置设备ID字段(直接存储到表字段中)
    if deviceID != "" {
        alert.DeviceID = deviceID  // 直接设置device_id字段
        alert.Annotations["device_id"] = deviceID
        alert.Annotations["extracted_device"] = "true"
    }
    
    // 添加任务关联信息(通过关联表管理)
    if taskResult.TaskID != "" {
        alert.Annotations["task_associated"] = "true"
        
        // 标记任务后续影响
        if taskResult.AlertType == "post_task" {
            alert.Annotations["task_relation"] = "任务后续影响"
            alert.Annotations["monitoring_phase"] = "post_completion"
        } else {
            alert.Annotations["task_relation"] = "任务执行期间"
            alert.Annotations["monitoring_phase"] = "active_execution"
        }
        
        // 保存告警后，在关联表中创建关联记录
        // 这将在AlertService中处理
        alert.TaskAssociation = &TaskAssociation{
            TaskID: taskResult.TaskID,
            AssociationType: taskResult.AlertType,
        }
    }
}
```

### 3.1.7 告警通知路由【优先级低，暂不开发】

通知路由设计：

```mermaid
graph TD
    Alert["告警"] --> Router["路由引擎"]
    
    Router --> CriticalRoute["严重告警路由"]
    Router --> WarningRoute["警告告警路由"]
    Router --> InfoRoute["信息告警路由"]
    
    CriticalRoute --> SMS["短信通知"]
    CriticalRoute --> Email["邮件通知"]
    CriticalRoute --> Webhook["Webhook通知"]
    
    WarningRoute --> Email
    WarningRoute --> Webhook
    
    InfoRoute --> Email
    
    Router --> DeviceTypeRoute["设备类型路由"]
    DeviceTypeRoute --> NetworkTeam["网络团队"]
    DeviceTypeRoute --> ServerTeam["服务器团队"]
```

路由逻辑实现示例：

```go
// 确定告警应该发送到哪些通知渠道
func DetermineNotificationTargets(alert *Alert) []string {
    var targets []string
    
    // 根据告警级别选择通知渠道
    switch alert.Level {
    case "critical":
        targets = append(targets, "sms", "email", "webhook")
    case "warning":
        targets = append(targets, "email", "webhook")
    case "info":
        targets = append(targets, "email")
    }
    
    // 根据设备类型添加特定通知渠道
    if deviceType, ok := alert.Labels["device_type"]; ok {
        if deviceType == "core-switch" {
            targets = append(targets, "network-team-channel")
        } else if deviceType == "server" {
            targets = append(targets, "server-team-channel")
        }
    }
    
    return targets
}
```

## 3.2 数据模型

### 3.2.1 告警记录表

```sql
CREATE TABLE `monitor_alert` (
  `id` varchar(36) NOT NULL COMMENT '告警ID',
  `name` varchar(255) NOT NULL COMMENT '告警名称',
  `source` varchar(50) NOT NULL COMMENT '告警来源(prometheus/elasticsearch)',
  `level` varchar(20) NOT NULL COMMENT '告警级别(critical/warning/info)',
  `status` varchar(20) NOT NULL COMMENT '告警状态(firing/resolved/acknowledged/suppressed)',
  `description` text NOT NULL COMMENT '告警描述',
  `labels` json DEFAULT NULL COMMENT '告警标签',
  `annotations` json DEFAULT NULL COMMENT '告警注释',
  `starts_at` timestamp NOT NULL COMMENT '告警首次触发时间',
  `ends_at` timestamp NULL DEFAULT NULL COMMENT '告警结束时间（仅当告警已解决时有效）',
  `notification_due_at` timestamp NULL DEFAULT NULL COMMENT 'AlertManager下次通知时间（未解决告警的静默截止时间）',
  `resolved_at` timestamp NULL DEFAULT NULL COMMENT '告警实际解决时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `generator_url` varchar(255) DEFAULT NULL COMMENT '告警生成URL',
  `rule_id` varchar(36) DEFAULT NULL COMMENT '关联的告警规则ID',
  
  -- 确认相关字段
  `acknowledged_by` varchar(100) DEFAULT NULL COMMENT '确认人',
  `acknowledged_at` timestamp NULL DEFAULT NULL COMMENT '确认时间',
  `acknowledgement_type` varchar(20) DEFAULT NULL COMMENT '确认方式(manual/auto/api)',
  `note` text DEFAULT NULL COMMENT '处理备注',
  
  -- 任务协同字段(网络自动化任务关联)
  `device_id` varchar(128) NULL COMMENT '设备ID(从labels中提取并存储，便于查询优化)',
  
  PRIMARY KEY (`id`),
  KEY `idx_source_status` (`source`, `status`),
  KEY `idx_starts_at` (`starts_at`),
  KEY `idx_level` (`level`),
  KEY `idx_rule_id` (`rule_id`),
  KEY `idx_labels_device_id` ((json_extract(`labels`, '$.device_id'))),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警记录表';
```

### 3.2.2 告警规则表

```sql
CREATE TABLE `monitor_alert_rule` (
  `id` varchar(36) NOT NULL COMMENT '规则ID',
  `name` varchar(255) NOT NULL COMMENT '规则名称',
  `description` text COMMENT '规则描述',
  `source` varchar(50) NOT NULL COMMENT '规则来源(prometheus/elasticsearch)',
  `expression` text NOT NULL COMMENT '规则表达式',
  `level` varchar(20) NOT NULL COMMENT '告警级别(critical/warning/info)',
  `duration` int NOT NULL DEFAULT '0' COMMENT '持续时间(秒)',
  `labels` json DEFAULT NULL COMMENT '规则标签',
  `annotations` json DEFAULT NULL COMMENT '规则注释',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name_source` (`name`, `source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警规则表';
```

### 3.2.3 告警通知表

```sql
CREATE TABLE `monitor_alert_notification` (
  `id` varchar(36) NOT NULL COMMENT '通知ID',
  `name` varchar(255) NOT NULL COMMENT '通知名称',
  `type` varchar(50) NOT NULL COMMENT '通知类型(email/webhook/kafka/sms)',
  `settings` json NOT NULL COMMENT '通知设置',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警通知表';

-- 任务告警关联表(作为索引入口)
CREATE TABLE `monitor_task_alert_associations` (
  `id` varchar(36) NOT NULL COMMENT '关联记录ID',
  `task_id` varchar(64) NOT NULL COMMENT '任务ID',
  `alert_id` varchar(36) NOT NULL COMMENT '告警ID(关联monitor_alert.id)',
  `association_type` enum('active','post_task') NOT NULL COMMENT '关联类型(active=任务执行期间,post_task=任务后续影响)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '关联时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_alert_id` (`alert_id`),
  KEY `idx_association_type` (`association_type`),
  KEY `idx_task_type` (`task_id`, `association_type`),
  CONSTRAINT `fk_task_alert_alert_id` FOREIGN KEY (`alert_id`) REFERENCES `monitor_alert` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务告警关联表';
```

### 3.2.4 告警关联规则表【优先级低，暂不开发】

```sql
CREATE TABLE `monitor_alert_correlation_rule` (
  `id` varchar(36) NOT NULL COMMENT '关联规则ID',
  `name` varchar(255) NOT NULL COMMENT '规则名称',
  `description` text COMMENT '规则描述',
  `source_alert_pattern` json NOT NULL COMMENT '源告警匹配模式',
  `target_alert_pattern` json NOT NULL COMMENT '目标告警匹配模式',
  `correlation_type` varchar(20) NOT NULL COMMENT '关联类型(suppress/correlate)',
  `equal_labels` json DEFAULT NULL COMMENT '需相等的标签',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警关联规则表';
```

## 3.3 接口设计

### 3.3.1 告警管理API

```shell
# 获取告警列表
GET /api/v1/alerts
参数:
  - source: 告警来源(prometheus/elasticsearch)
  - level: 告警级别(critical/warning/info)
  - status: 告警状态(firing/resolved/acknowledged)
  - device_id: 设备ID
  - start_time: 开始时间
  - end_time: 结束时间
  - limit: 每页数量
  - offset: 偏移量

# 获取指定告警
GET /api/v1/alerts/:id

# 确认告警
POST /api/v1/alerts/:id/acknowledge
请求体:
  {
    "acknowledged_by": "用户名",
    "note": "处理备注"
  }

# 解决告警
POST /api/v1/alerts/:id/resolve
请求体:
  {
    "resolved_by": "用户名",
    "resolution": "解决方案"
  }
```

### 3.3.2 告警统计API

```shell
# 获取告警统计信息
GET /api/v1/alerts/stats
参数:
  - group_by: 分组字段(level/source/device_id)
  - start_time: 开始时间
  - end_time: 结束时间

# 获取告警趋势
GET /api/v1/alerts/trend
参数:
  - interval: 时间间隔(hour/day/week)
  - start_time: 开始时间
  - end_time: 结束时间
```

### 3.3.3 告警关联API【优先级低，暂不开发】

```shell
# 获取关联告警
GET /api/v1/alerts/:id/related
参数:
  - relation_type: 关联类型(suppressed/correlated)

# 获取告警关联规则
GET /api/v1/alerts/correlation-rules

# 创建告警关联规则
POST /api/v1/alerts/correlation-rules
请求体:
  {
    "name": "规则名称",
    "description": "规则描述",
    "source_alert_pattern": {...},
    "target_alert_pattern": {...},
    "correlation_type": "suppress",
    "equal_labels": ["device_id"]
  }
```

# 4 安全设计

统一告警模型的安全设计包括：

1. **数据访问控制**：
   - 告警API遵循现有dcimonitor系统的认证和授权机制
   - 敏感操作（如确认、解决告警）需要适当的权限

2. **数据验证**：
   - 对输入的告警数据进行严格验证，防止注入攻击
   - 验证JSON格式的标签和注释，防止恶意数据

3. **敏感信息保护**：
   - 通知配置中的敏感信息（如密码、令牌）加密存储
   - 告警描述和注释中过滤敏感信息

4. **审计日志**：
   - 记录对告警的所有操作，包括确认、解决和删除
   - 记录告警规则的创建和修改

# 5 告警时间维度设计

## 5.1 告警时间维度模型

### 5.1.1 时间维度字段设计

在统一告警模型中，我们对告警的时间维度进行了精细化设计，确保准确记录告警的完整生命周期：

| 字段名 | 数据类型 | 描述 |
|-------|--------|------|
| starts_at | timestamp | 告警首次触发时间，表示问题最初发生的时间点 |
| notification_due_at | timestamp | AlertManager下次通知时间（未解决告警的静默截止时间），仅对未解决告警有意义 |
| ends_at | timestamp | 告警结束时间，仅当告警已解决时有效，表示问题实际结束的时间点 |
| resolved_at | timestamp | 告警实际解决时间，可能是系统自动解决或人工手动解决 |
| updated_at | timestamp | 记录最后一次更新的时间 |
| acknowledged_at | timestamp | 告警被确认的时间 |

### 5.1.2 时间维度处理流程

```mermaid
sequenceDiagram
    participant AM as AlertManager
    participant Conv as 转换器
    participant Alert as 统一告警模型
    participant DB as 数据库
    
    AM->>Conv: 发送告警通知
    Conv->>Conv: 转换时区(UTC->本地)
    
    alt 新告警
        Conv->>Alert: 创建告警
        Note over Alert: starts_at: 首次触发时间<br/>notification_due_at: 下次通知时间<br/>ends_at: 空(未解决)
        Alert->>DB: 存储告警
    else 更新告警
        Conv->>Alert: 更新告警
        Note over Alert: starts_at: 保持不变<br/>notification_due_at: 更新为下次通知时间<br/>ends_at: 空(未解决)
        Alert->>DB: 更新记录
    else 告警解决
        Conv->>Alert: 解决告警
        Note over Alert: starts_at: 保持不变<br/>notification_due_at: 空<br/>ends_at: 当前时间<br/>resolved_at: 当前时间
        Alert->>DB: 更新记录
    end
```

### 5.1.3 时间字段语义说明

1. **首次触发时间(starts_at)**
   - 表示问题最初被检测到的时间点
   - 对于持续触发的同一告警，此时间保持不变
   - 确保历史准确性，记录问题的真实起始

2. **下次通知时间(notification_due_at)**
   - 源自AlertManager的`endsAt`字段，但语义完全不同
   - 表示AlertManager将在何时重新发送该告警的通知
   - 用于实现告警通知的静默期，避免通知风暴
   - 仅对未解决的告警有意义

3. **结束时间(ends_at)**
   - 仅当告警已解决时有效
   - 表示问题实际结束的时间点
   - 与未解决告警的notification_due_at明确区分

4. **解决时间(resolved_at)**
   - 记录告警被解决的具体时间
   - 可用于统计告警的平均解决时间
   - 与ends_at可能相同，但语义更明确

### 5.1.4 时区处理

所有时间字段在存储到数据库前统一转换为本地时区，确保：
- 从不同时区的告警源（如Prometheus默认使用UTC）接收数据时保持一致性
- UI展示时间无需额外转换
- 报表统计更加准确

### 5.1.5 未解决告警与已解决告警的区别

```mermaid
stateDiagram-v2
    [*] --> 触发
    触发 --> 未解决: starts_at设置<br/>notification_due_at设置<br/>ends_at为空
    未解决 --> 已解决: resolved_at设置<br/>ends_at设置<br/>notification_due_at清空
    未解决 --> 未解决: 告警重发<br/>notification_due_at更新
```

对于未解决告警，我们使用notification_due_at而非ends_at来表示AlertManager的下次通知时间，避免语义混淆。这样，ends_at字段只在告警实际解决时才有值，确保其语义清晰。

# 6 安全设计

统一告警模型的安全设计包括：

1. **数据访问控制**：
   - 告警API遵循现有dcimonitor系统的认证和授权机制
   - 敏感操作（如确认、解决告警）需要适当的权限

2. **数据验证**：
   - 对输入的告警数据进行严格验证，防止注入攻击
   - 验证JSON格式的标签和注释，防止恶意数据

3. **敏感信息保护**：
   - 通知配置中的敏感信息（如密码、令牌）加密存储
   - 告警描述和注释中过滤敏感信息

4. **审计日志**：
   - 记录对告警的所有操作，包括确认、解决和删除
   - 记录告警规则的创建和修改

# 5 本设计的代码实现文件列表

```
dci-monitor/src/dcimonitor/
├── internal/
│   ├── models/
│   │   ├── alert.go           # 告警数据模型
│   │   ├── alert_rule.go      # 告警规则模型
│   │   ├── notification.go    # 通知配置模型
│   │   └── correlation.go     # 告警关联模型
│   ├── services/
│   │   ├── alert_dao.go       # 告警数据访问层
│   │   ├── alert_service.go   # 告警服务实现
│   │   ├── alert_converter.go # 告警转换器
│   │   ├── alert_time.go      # 告警时间处理工具
│   │   ├── state_manager.go   # 状态管理器
│   │   ├── correlation.go     # 关联引擎
│   │   └── notification.go    # 通知服务
│   └── monitors/
│       └── alert_monitor.go   # 告警API控制器
└── pkg/
    └── alertmodel/
        ├── types.go           # 通用告警类型定义
        └── converter.go       # 通用转换工具
```

## 6 实施计划

1. 设计并实现统一告警数据模型和数据库表结构
   - 修改数据库表结构，添加notification_due_at和resolved_at字段
   - 更新Go模型定义，支持新增时间字段

2. 开发告警时间处理模块
   - 实现时区转换功能
   - 完善时间字段语义处理
   - 开发告警时间可视化组件

3. 开发告警转换器，支持Prometheus和ES告警的转换
   - 更新Prometheus告警处理逻辑，区分notification_due_at和ends_at

4. 实现告警状态管理机制
   - 根据新的时间维度模型，优化状态转换逻辑

5. 开发告警分组和关联引擎
6. 实现告警通知路由功能
7. 开发告警管理API
   - 支持告警时间维度的查询和过滤
   - 提供告警持续时间统计接口

8. 集成到现有dcimonitor系统
9. 编写单元测试和集成测试
10. 数据迁移和兼容性处理
    - 编写数据迁移脚本，将现有数据转换为新模型
    - 确保API向后兼容
