---
title: |
  DCI-dciagent客户端架构设计

subtitle: |
  DCI数据监测系统客户端总体架构与技术选型
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-21 | 顾铠羟 | 从原《15-DCI-dciagent客户端技术方案设计》拆分而来 |

# 1 文档介绍

## 1.1 文档目的

本文档详细描述DCI数据监测系统中 dciagent 的总体架构设计，包括系统架构图、模块化设计、技术选型等内容。dciagent 是 DCI 数据监测系统的 Agent 端，涵盖Management Agent和Telegraf等各种采集组件，本文档作为DCI数据监测系统Agent端总体架构设计的指导文档，确保设计的一致性、可靠性和可维护性。

## 1.2 文档范围

本文档涵盖DCI数据监测系统中dciagent客户端架构设计相关的内容，包括：

1. 客户端总体架构设计
2. 设计目标
3. 架构设计图
4. 模块化设计
5. 技术选型

# 2 总体设计

## 2.1 设计目标

dciagent架构设计旨在实现以下目标：

1. 确保部署的一致性和可复制性
2. 建立安全、可靠的运行环境
3. 简化配置管理和版本控制
4. 提供灵活的扩展和升级路径
5. 实现高可用性和故障恢复能力
6. 支持自动化部署和配置
7. 与DCI监测系统其他组件无缝集成
8. 确保遵循DCI监测系统"Agent端各类采集数据均通过Kafka Topic发送"的核心设计原则

## 2.2 架构设计

### 2.2.1 Agent端总体架构

Agent端由两个核心组件构成，通常同机部署：

1. **采集组件，以Telegraf Agent (TA)为代表**: 作为数据采集执行单元，运行标准的Telegraf进程。负责根据配置执行具体的采集任务（如SNMP、Syslog）并将数据发送到指定目的地（如Kafka）。其配置主要由Management Agent动态管理。

2. **Management Agent (MA)**: Go语言服务。负责在本机管理Telegraf Agent的生命周期、配置更新，并与dcimonitor服务端的MgmtAPI进行通信，上报状态、获取指令。

```mermaid
graph TD
    subgraph "物理/虚拟服务器环境"
        subgraph "dciagent"
            MA[Management Agent]
            TA[Telegraf Agent]
        end
        
        subgraph "资源管理"
            Config[配置文件]
            Logs[日志目录]
            Data[数据缓冲]
        end
        
        subgraph "系统服务"
            SystemD[Systemd服务]
        end
    end
    
    subgraph "服务端组件"
        API[DCI监测系统API]
            Kafka[Kafka集群]
        TSDB[时序数据库]
    end
    
    MA -- 管理 --> TA
    API -- 配置下发 --> MA
    
    TA -- 读取配置 --> Config
    TA -- 写入日志 --> Logs
    TA -- 缓存数据 --> Data
    
    SystemD -- 控制 --> TA
    
    TA -- 数据写入 --> Kafka
    Kafka -- 数据流转 --> TSDB
    
    %% 样式定义
    classDef primary fill:#d9f,stroke:#333,stroke-width:1px
    classDef secondary fill:#9cf,stroke:#333,stroke-width:1px
    
    class MA,TA primary
    class Kafka,API secondary
```

### 2.2.2 采集组件

#### SNMP采集插件

SNMP采集是DCI数据监测系统的核心功能之一，通过Telegraf的inputs.snmp插件实现。

#### Syslog采集插件

Syslog采集用于接收网络设备和系统发送的日志数据。通过Telegraf的inputs.syslog插件实现，主要用于接收网络设备和系统发送的日志数据。同时支持UDP和TCP协议接收Syslog。

#### 主机指标采集

用于采集 DCIAgent 所在主机的系统指标，如CPU、内存、磁盘等。

### 2.2.3 Management Agent

Management Agent作为Agent端的核心控制器，具有以下核心职责：

* **注册与认证**: 向服务端MgmtAPI注册自身（如果本地没有持久化的Agent ID），获取唯一Agent ID，并使用预置Token进行认证。注册成功后将Agent ID持久化。
* **心跳与状态上报**: 使用本地持久化的Agent ID定期向服务端发送心跳，报告自身及Telegraf Agent的运行状态。
* **配置拉取与应用**: 使用Agent ID定期从服务端拉取分配给本Agent的最终生效的Telegraf配置片段，写入本地telegraf.d目录，并触发Telegraf重载配置。
* **命令拉取与执行**: 从服务端拉取特定命令（如重启Telegraf、执行诊断脚本）并执行。
* **Telegraf进程管理**: 监控Telegraf进程的运行状态，在必要时尝试启动或重启。
* **日志记录**: 记录自身运行日志及与服务端交互的关键信息。

### 2.2.4 Dciagent与服务端通信API

Management Agent与服务端MgmtAPI的通信采用以下API交互方式：

* **协议**: 使用**HTTPS**保证传输安全
* **数据格式**: 请求体和响应体均使用**JSON**格式
* **认证**: MA在每个请求的Authorization HTTP Header中携带预置的agent_token

主要API交互包括：

* **注册 (Agent -> Server)**
* **心跳 (Agent -> Server)**
* **拉取配置 (Agent -> Server)**
* **上报状态/日志 (Agent -> Server)**

## 2.3 模块化设计

dciagent采用模块化设计，各模块之间通过明确的接口进行交互。以下是系统的模块结构：

```mermaid
graph TD
    subgraph "dciagent主程序"
        CLI[命令行界面]
        Config[配置管理]
        
        subgraph "核心模块"
            Core[核心服务]
            Registry[模块注册中心]
            LifeCycle[生命周期管理]
            
            subgraph "Management Agent"
                MA[MA服务]
                MAConfig[配置管理]
                MAControl[进程控制]
            end
        end
        
        subgraph "采集模块管理器"
            CollectorMgr[采集模块管理器]
            Version[版本管理]
            Backup[备份恢复]
        end
    end
    
    subgraph "独立进程模块"
        Telegraf[Telegraf进程]
        OtherCollector[其他采集器进程]
    end
    
    %% 模块间关系
    CLI --> Config
    CLI --> Core
    Core --> Registry
    Core --> LifeCycle
    Core <--> MA
    MA --> MAConfig
    MA --> MAControl
    Registry --> CollectorMgr
    CollectorMgr --> Version
    CollectorMgr --> Backup
    
    %% 进程控制关系
    MAControl -.监测控制.-> CollectorMgr
    CollectorMgr -.监测控制.-> Telegraf
    CollectorMgr -.监测控制.-> OtherCollector
    
    %% 配置管理关系
    MAConfig -.配置下发.-> Config
    Config -.配置应用.-> Telegraf
    Config -.配置应用.-> OtherCollector
    
    %% 样式定义
    classDef primary fill:#d9f,stroke:#333,stroke-width:2px
    classDef secondary fill:#9cf,stroke:#333,stroke-width:2px
    classDef process fill:#faa,stroke:#333,stroke-width:2px
    classDef ma fill:#afd,stroke:#333,stroke-width:2px
    
    class Core,Registry,LifeCycle primary
    class CollectorMgr,Version,Backup secondary
    class Telegraf,OtherCollector process
    class MA,MAConfig,MAControl ma
```

## 2.4 技术选型

1. **部署方式**：
   - 客户端（本设计中涉及到的 dciagent、Management Agent、Telegraf）基于操作系统直接安装部署，不依赖Kubernetes或Docker，未来可能兼容K8S/Docker方式。服务端（Prometheus、Kafka、Management Agent等）基于K8S部署。
   - dciagent 是 DCI 数据监测系统的 Agent 端，涵盖Telegraf等各种采集组件。
   - 使用系统服务管理（如systemd）确保Telegraf进程自动启动和监控

2. **源码与开发**：
   - **相关工具开发采用Go语言实现**，dciagent 是 DCI 数据监测系统的 Agent 端，代码位于dci-monitor/src/dciagent
   - Telegraf二进制应从 dcimonitor 服务通过 API 下载，而非从 GitHub 获取。
   - 模块化设计，采用统一的子命令结构而非独立工具

3. **配置管理**：
   - 采用集中式文件配置管理
   - 版本化配置并支持回滚
   - 利用Management Agent远程分发和管理配置
   - 使用配置文件进行客户端参数配置

4. **存储选择**：
   - 使用本地文件系统存储配置、日志和缓冲数据
   - 支持NFS或网络存储用于备份和共享

5. **运维工具**：
   - 使用系统服务管理工具（如systemd），运行并监测 dciagent，由 dciagent 运维监测各独立进程模块（如Telegraf）
   - 使用Prometheus监控Telegraf自身
   - 使用统一的子命令结构进行管理和维护

# 3 数据流向设计

## 3.1 标准数据流向

DCI数据监测系统的Agent端采集数据遵循以下标准数据流向：

```mermaid
sequenceDiagram
    participant Device as 网络设备
    participant Host as 主机系统
    participant Telegraf as Telegraf
    participant Kafka as Kafka集群
    participant MA as Management Agent
    participant API as 服务端API
    
    Device->>Telegraf: SNMP/Syslog数据
    Host->>Telegraf: 系统指标数据
    
    Telegraf->>Telegraf: 数据处理与转换
    Telegraf->>Kafka: 发送指标数据
    Telegraf->>Kafka: 发送日志数据
    
    MA->>API: 发送状态/心跳
    API->>MA: 下发配置/命令
    MA->>Telegraf: 更新配置
```

## 3.2 数据处理流程

1. **数据采集**: Telegraf根据配置从各种数据源（网络设备、主机系统）采集原始数据
2. **数据预处理**: 
   * 格式转换: 将不同来源的数据转换为统一的指标格式
   * 数据过滤: 根据配置过滤不需要的数据点
   * 数据聚合: 对高频数据进行本地聚合，减少传输量
3. **数据传输**: 处理后的数据通过outputs插件发送到Kafka
4. **状态反馈**: Management Agent定期向服务端报告数据采集状态和统计信息

# 4 安全设计

## 4.1 通信安全

* **TLS加密**: Management Agent与服务端之间的所有API通信使用HTTPS/TLS加密，防止数据传输过程中被窃听。
* **证书验证**: 服务端证书由可信CA签发，Agent端验证服务端证书有效性，防止中间人攻击。
* **Token认证**: 每个Management Agent使用预置的唯一Token进行身份认证，Token可定期轮换。

## 4.2 权限隔离

* **最小权限原则**: Agent端独立进程组件（如Telegraf进程）仅使用必要的系统权限运行，避免使用root权限。
* **专用用户**: 创建专用的非特权系统用户dciagent运行Telegraf进程。
* **文件权限**: 配置文件采用严格的权限控制，确保只有授权用户可读写。

## 4.3 数据安全

* **敏感信息保护**: 配置文件中的敏感信息（如SNMP团体字符串、认证凭据）采用加密存储。
* **数据完整性**: 使用校验和（md5sum）机制确保配置文件和采集数据的完整性。 