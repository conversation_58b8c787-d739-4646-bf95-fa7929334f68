1、dci_node_business  表中字段 VNI，可以直接获取使用
备注：dci_node_business  表中字段 VNI，其实是关联的表 dci_vni的vni_id字段

2、设备ID如何获取？
先从dci_node_business 表中获取node_id节点字段
然后这个node_id字段，跟dci_node表的id字段关联，
通过这个id，就可以拿到dci_node的每一条记录，这个记录里面的device_id字段，就是设备的id

备注：dci_node表中的device_id跟设备表dci_device表的id关联

3、物理端口号如何获取？
先从dci_node_business 表中获取node_id节点字段
然后这个node_id字段，跟dci_node表的id字段关联，
通过这个id，就可以拿到dci_node的每一条记录，这个记录里面的device_id字段，就是设备的id

备注：dci_node表中的device_id跟设备表dci_device表的id关联
