---
title: |
  华为测试交换机 SNMP 数据采集技术方案
subtitle: |
  DCI-Monitor 交换机离线开发测试
---

# 1. 引言

## 1.1 文档目的

本文档阐述为 DCI-Monitor 项目进行离线开发和测试，从华为测试交换机 (SW1: 10.36.46.188, SW2: 10.36.46.189) 采集 SNMP 数据的技术方案、过程和发现。

## 1.2 背景

在无法直接连接测试交换机的情况下，采集其 SNMP 数据作为静态数据源，可以支持 DCI-Monitor 系统的数据处理、存储和 API 功能的持续开发与验证。

# 2. 数据采集需求

## 2.1 目标设备

*   SW1: 10.36.46.188
*   SW2: 10.36.46.189
*   用户名huawei 密码 DCILab@2025
*   SNMPv2c Community: `dcilab2025`

## 2.2 采集数据类别

根据 DCI-Monitor 系统设计（参考 `02-网络自动化平台-数据监测系统技术概要设计.md` 等文档），采集以下类别数据：

*   设备基本信息 (System MIB)
*   接口信息 (IF-MIB)
*   接口流量计数器 (IF-MIB, 64位 HC MIB)
*   LLDP 邻居信息 (LLDP-MIB)
*   设备健康状态 (HUAWEI-ENTITY-EXTENT-MIB)

# 3. 关键 OID 识别

通过查阅标准 MIB 和华为 MIB 文档（如 `CloudEngine-9800-8800-6800-5800-V200R024C00-mib参考.md`），识别出以下核心 OID：

## 3.1 System MIB (`.*******.2.1.1`)

*   `sysDescr`: `.*******.2.1.1.1.0`
*   `sysObjectID`: `.*******.2.1.1.2.0`
*   `sysUpTimeInstance`: `.*******.2.1.1.3.0` (或 `sysUpTime.0`)
*   `sysName`: `.*******.2.1.1.5.0`

## 3.2 IF-MIB (`.*******.2.1.2`, `.*******.2.1.31`)

*   `ifIndex`: `.*******.2.1.2.2.1.1`
*   `ifDescr`: `.*******.2.1.2.2.1.2`
*   `ifAdminStatus`: `.*******.2.1.2.2.1.7`
*   `ifOperStatus`: `.*******.2.1.2.2.1.8`
*   `ifName`: `.*******.2.1.31.1.1.1.1`
*   `ifHCInOctets`: `.*******.2.1.31.1.1.1.6`
*   `ifHCOutOctets`: `.*******.2.1.31.1.1.1.10`
*   `ifHCInUcastPkts`: `.*******.2.1.31.*******`
*   `ifHCOutUcastPkts`: `.*******.2.1.31.********`

## 3.3 LLDP-MIB (`.1.0.8802.1.1.2`)

*   远端信息表 (`lldpRemTable`): `.1.0.8802.*******.4.1`
    *   `lldpRemChassisId`: `.1.0.8802.*******.4.1.1.5`
    *   `lldpRemPortId`: `.1.0.8802.*******.4.1.1.7`
    *   `lldpRemSysName`: `.1.0.8802.*******.4.1.1.9`
    *   (其他相关字段如 Subtype 等也在此表下)

## 3.4 设备健康状态 (HUAWEI-ENTITY-EXTENT-MIB, `.*******.4.1.2011.*********.1`)

*   CPU 使用率 (`hwEntityCpuUsage`): `.*******.4.1.2011.*********.*******.<index>`
*   内存使用率 (`hwEntityMemUsage`): `.*******.4.1.2011.*********.*******.<index>`
*   温度 (`hwEntityTemperature`): `.*******.4.1.2011.*********.********.<index>`
*   相关实体索引确定: 通过查询 `entPhysicalClass` (`.*******.********.*******.<index>`)，找到类型为 9 (设备/主控板) 的实体索引 (如 `16842753`)。

# 4. 数据采集方法

## 4.1 工具

使用标准的 `snmpwalk` 命令行工具。

## 4.2 命令示例

```bash
# 采集 System MIB
snmpwalk -v 2c -c dcilab2025 <ip_address> .*******.2.1.1 > <output_file>

# 采集 ifTable
snmpwalk -v 2c -c dcilab2025 <ip_address> .*******.2.1.2 > <output_file>

# 采集 ifXTable (含 64 位计数器)
snmpwalk -v 2c -c dcilab2025 <ip_address> .*******.2.1.31 > <output_file>

# 采集 LLDP 远端信息 (建议使用具体表 OID)
snmpwalk -v 2c -c dcilab2025 <ip_address> .1.0.8802.*******.4.1 > <output_file>

# 采集 CPU 使用率
snmpwalk -v 2c -c dcilab2025 <ip_address> .*******.4.1.2011.*********.******* > <output_file>

# 采集内存使用率
snmpwalk -v 2c -c dcilab2025 <ip_address> .*******.4.1.2011.*********.******* > <output_file>

# 采集温度
snmpwalk -v 2c -c dcilab2025 <ip_address> .*******.4.1.2011.*********.******** > <output_file>
```

## 4.3 时序数据采集 (CPU/内存)

为模拟时序数据，采用循环结合 `sleep` 的方式进行多次采样。

```bash
# 示例：采集 3 次，间隔 60 秒
dir=snmp_data_$(date +%Y%m%d)
mkdir -p $dir
for i in 1 2 3; do
  timestamp=$(date +%Y%m%d%H%M%S)
  echo "[Sample $i @ $timestamp] Collecting..."
  snmpwalk -v 2c -c dcilab2025 <ip_address> .*******.4.1.2011.*********.******* > "$dir/<hostname>_cpu_usage_$timestamp.txt"
  snmpwalk -v 2c -c dcilab2025 <ip_address> .*******.4.1.2011.*********.******* > "$dir/<hostname>_mem_usage_$timestamp.txt"
  echo "[Sample $i @ $timestamp] Done."
  if [ $i -lt 3 ]; then
    echo "Sleeping for 60 seconds..."
    sleep 60
  fi
done
echo "Time series collection complete."
```

## 4.4 数据存储

采集结果以文本文件形式存储在工作区内的 `snmp_data_YYYYMMDD` 目录中，文件名包含设备标识、数据类型和时间戳。

# 5. 采集过程问题与分析

## 5.1 LLDP 数据采集失败

*   现象: 对 SW1 执行 LLDP MIB (`.1.0.8802.1.1.2` 或 `.1.0.8802.*******.4.1`) 的 `snmpwalk` 返回空或 "No Such Object"。
*   原因分析:
    *   物理连接: 通过 SSH 登录 SW1 执行 `display interface brief`，发现所有物理数据接口状态均为 `down/down`。物理链路不通导致 LLDP 协议无法工作。
    *   SNMP MIB 视图限制: 执行 `display snmp-agent mib-view` 发现 `dcilab2025` community 使用的默认视图 `ViewDefault` 仅包含 `internet` 子树 (`.*******`)，未包含 LLDP MIB 所在的 `iso` 根 (`.1`)。SNMP Agent 配置阻止了对 LLDP OID 的访问。
    *   命令兼容性: 在 SW1 (VRP V200R022C00SPC500) 上，`display lldp global` 命令无法识别，但 `display lldp neighbor` 可用。

针对原因2(初始化交换机LLDP后如果执行  ./snmpwalk_test.sh 命令 lldp 不通，基本是这个原因)，可执行以下命令进行修复：

```shell
# 进入系统视图
system-view

# 创建包含完整iso树的新MIB视图
snmp-agent mib-view included DCIView iso

# 将dcilab2025 community与新视图关联
snmp-agent community read dcilab2025 mib-view DCIView

# 退出并保存配置
quit
save
```

## 5.2 健康状态数据解析

*   现象: 查询 `hwEntityMemUsage` OID (`.*******.4.1.2011.*********.*******`) 返回多行结果，大部分值为 0。
*   原因分析: 该 OID 返回设备上多个实体（主控板、接口板、电源等）的内存使用率。每个实体由其索引号区分。
*   关键值定位: 通过查询 `entPhysicalClass` (`.*******.********.*******`) 确定类型为 9 (设备/主控板) 的实体索引 (例如 `16842753`)。该索引对应的 `hwEntityMemUsage.<index>` 值 (如 34) 即为核心内存使用率。

# 6. 后续操作建议

## 6.1 物理连接与配置修复

*   检查并修复 SW1 与 SW2 之间的物理线缆连接，确保两端接口状态为 `up/up`。
*   在 SW1 和 SW2 上配置 SNMP MIB 视图，将 `iso` 树加入 `dcilab2025` community 使用的视图：
```bash
system-view
# 创建包含完整iso树的新MIB视图
snmp-agent mib-view included DCIView iso
# 将dcilab2025 community与新视图关联
snmp-agent community read dcilab2025 mib-view DCIView
quit
save
```
*   确认 SW1 和 SW2 互连接口上启用了 LLDP：
  ```bash
  system-view
  interface <interface-type> <interface-number>
  lldp enable
  quit
  quit
  save
  ```

## 6.2 LLDP 数据重新采集

在完成上述修复后，重新执行 `snmpwalk` 采集 LLDP 数据：
```bash
snmpwalk -v 2c -c dcilab2025 10.36.46.188 .1.0.8802.*******.4.1 > snmp_data_YYYYMMDD/sw1_lldp_$(date +%Y%m%d%H%M%S).txt
snmpwalk -v 2c -c dcilab2025 10.36.46.189 .1.0.8802.*******.4.1 > snmp_data_YYYYMMDD/sw2_lldp_$(date +%Y%m%d%H%M%S).txt
```

# 7. 离线数据使用

已采集的 System、IF-MIB 和健康状态数据可用于模拟 SNMP 输入，支持 DCI-Monitor 的基础数据处理和指标监控功能开发。LLDP 相关功能（如拓扑发现）的测试需等待 LLDP 数据成功采集后进行。

# 8. 已执行采集命令及生成文件列表

本节记录为生成 `snmp_data_20250430` 目录中当前文件所实际执行的命令。

## 8.1 SW1 (10.36.46.188) 基础数据采集 (约 15:09)

```bash
timestamp=20250430150922 # 实际时间戳根据生成文件推断
dir=snmp_data_20250430
mkdir -p $dir
echo "采集 SW1 (10.36.46.188) 的 System MIB..."
snmpwalk -v 2c -c dcilab2025 10.36.46.188 .*******.2.1.1 > $dir/sw1_system_$timestamp.txt
echo "采集 SW1 (10.36.46.188) 的 IF-MIB (标准接口表)..."
snmpwalk -v 2c -c dcilab2025 10.36.46.188 .*******.2.1.2 > $dir/sw1_ifTable_$timestamp.txt
echo "采集 SW1 (10.36.46.188) 的 IF-MIB (扩展接口表)..."
snmpwalk -v 2c -c dcilab2025 10.36.46.188 .*******.2.1.31 > $dir/sw1_ifXTable_$timestamp.txt
echo "采集 SW1 (10.36.46.188) 的 LLDP-MIB..."
snmpwalk -v 2c -c dcilab2025 10.36.46.188 .1.0.8802.1.1.2 > $dir/sw1_lldp_$timestamp.txt
echo "SW1 采集完成，文件保存在 $dir 目录"
```

*   生成文件:
    *   `sw1_system_20250430150922.txt`
    *   `sw1_ifTable_20250430150922.txt`
    *   `sw1_ifXTable_20250430150922.txt`
    *   `sw1_lldp_20250430150922.txt` (内容为空)

## 8.2 SW1 (10.36.46.188) 健康数据采集 (约 15:51 及后续)

(注：目录中存在多个时间戳的健康数据文件，以下命令反映了其中一次采集和时序采集过程)

```bash
# 单次健康数据采集 (类似命令生成了 15:51 左右的文件)
timestamp=20250430155147 # 示例时间戳
dir=snmp_data_20250430
mkdir -p $dir
echo "采集 SW1 (10.36.46.188) 的 CPU 使用率..."
snmpwalk -v 2c -c dcilab2025 10.36.46.188 .*******.4.1.2011.*********.******* > $dir/sw1_cpu_usage_$timestamp.txt
echo "采集 SW1 (10.36.46.188) 的内存使用率..."
snmpwalk -v 2c -c dcilab2025 10.36.46.188 .*******.4.1.2011.*********.******* > $dir/sw1_mem_usage_$timestamp.txt
echo "采集 SW1 (10.36.46.188) 的温度..."
snmpwalk -v 2c -c dcilab2025 10.36.46.188 .*******.4.1.2011.*********.******** > $dir/sw1_temperature_$timestamp.txt
echo "SW1 健康数据采集完成"

# 时序数据采集 (生成了 16:01, 16:02 的 CPU/内存文件)
dir=snmp_data_20250430
mkdir -p $dir
for i in 1 2 3; do # 循环被中断，实际可能只完成了部分
  timestamp=$(date +%Y%m%d%H%M%S)
  echo "[SW1 Sample $i @ $timestamp] 采集中..."
  snmpwalk -v 2c -c dcilab2025 10.36.46.188 .*******.4.1.2011.*********.******* > "$dir/sw1_cpu_usage_$timestamp.txt"
  snmpwalk -v 2c -c dcilab2025 10.36.46.188 .*******.4.1.2011.*********.******* > "$dir/sw1_mem_usage_$timestamp.txt"
  echo "[SW1 Sample $i @ $timestamp] 采集完成."
  if [ $i -lt 3 ]; then
    echo "等待 60 秒..."
    sleep 60
  fi
done
echo "SW1 时序数据采集完成."
```

*   生成文件 (部分列表):
    *   `sw1_cpu_usage_20250430155147.txt`
    *   `sw1_mem_usage_20250430155147.txt`
    *   `sw1_temperature_20250430155147.txt`
    *   `sw1_cpu_usage_20250430160146.txt`
    *   `sw1_mem_usage_20250430160146.txt`
    *   `sw1_cpu_usage_20250430160247.txt`
    *   `sw1_mem_usage_20250430160247.txt`

## 8.3 SW2 (10.36.46.189) 数据采集 (约 15:52)

```bash
timestamp=20250430155211 # 实际时间戳根据生成文件推断
dir=snmp_data_20250430
mkdir -p $dir
echo "采集 SW2 (10.36.46.189) 的 System MIB..."
snmpwalk -v 2c -c dcilab2025 10.36.46.189 .*******.2.1.1 > $dir/sw2_system_$timestamp.txt
echo "采集 SW2 (10.36.46.189) 的 IF-MIB (标准接口表)..."
snmpwalk -v 2c -c dcilab2025 10.36.46.189 .*******.2.1.2 > $dir/sw2_ifTable_$timestamp.txt
echo "采集 SW2 (10.36.46.189) 的 IF-MIB (扩展接口表)..."
snmpwalk -v 2c -c dcilab2025 10.36.46.189 .*******.2.1.31 > $dir/sw2_ifXTable_$timestamp.txt
echo "采集 SW2 (10.36.46.189) 的 CPU 使用率..."
snmpwalk -v 2c -c dcilab2025 10.36.46.189 .*******.4.1.2011.*********.******* > $dir/sw2_cpu_usage_$timestamp.txt
echo "采集 SW2 (10.36.46.189) 的内存使用率..."
snmpwalk -v 2c -c dcilab2025 10.36.46.189 .*******.4.1.2011.*********.******* > $dir/sw2_mem_usage_$timestamp.txt
echo "采集 SW2 (10.36.46.189) 的温度..."
snmpwalk -v 2c -c dcilab2025 10.36.46.189 .*******.4.1.2011.*********.******** > $dir/sw2_temperature_$timestamp.txt
echo "SW2 数据采集完成，文件保存在 $dir 目录"
```

*   生成文件:
    *   `sw2_system_20250430155211.txt`
    *   `sw2_ifTable_20250430155211.txt`
    *   `sw2_ifXTable_20250430155211.txt`
    *   `sw2_cpu_usage_20250430155211.txt`
    *   `sw2_mem_usage_20250430155211.txt`
    *   `sw2_temperature_20250430155211.txt`
