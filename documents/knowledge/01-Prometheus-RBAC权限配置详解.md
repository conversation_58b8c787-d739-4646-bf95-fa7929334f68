# Prometheus RBAC 配置详解 (`prometheus-rbac.yaml`)

本文档详细解释了 DCI 监控项目中 `dci-monitor/prometheus/prometheus-rbac.yaml` 文件的作用和内容。该文件定义了 Prometheus 在 Kubernetes 集群中运行所需的**基于角色的访问控制 (RBAC)** 配置。

RBAC 是 Kubernetes 用来管理用户和服务对集群资源访问权限的一种机制。简单来说，`prometheus-rbac.yaml` 文件主要做了三件事：

1.  **创建了一个身份 (ServiceAccount)**：为 Prometheus Pod 提供一个在集群内部可以识别的身份。
2.  **定义了一组权限 (ClusterRole)**：明确规定了这个身份被允许对哪些集群资源执行哪些操作。
3.  **将身份与权限绑定 (ClusterRoleBinding)**：将上面创建的身份和定义的权限关联起来，使得拥有该身份的Pod（即 Prometheus Pod）能够实际使用这些权限。

## 文件内容解析

### 1. ServiceAccount

```yaml
---
apiVersion: v1
kind: ServiceAccount # 资源类型：服务账户
metadata:
  name: prometheus   # 服务账户的名称，将在后面被引用
  namespace: dci      # 服务账户所在的命名空间，必须与Prometheus Pod在同一个namespace
---
```

*   **作用**: 创建一个名为 `prometheus` 的服务账户，位于 `dci` 命名空间。
*   **目的**: 为 Prometheus Pod 提供一个唯一的身份。当 Pod 的 YAML 文件中指定 `serviceAccountName: prometheus` 时，该 Pod 就以这个身份与 Kubernetes API 交互。

### 2. ClusterRole

```yaml
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole # 资源类型：集群角色
metadata:
  name: prometheus # 集群角色的名称，将在后面被引用
rules: # 定义权限规则列表
# 允许对核心API组("")中的以下资源进行 "get", "list", "watch" 操作
- apiGroups: [""] 
  resources:
  - nodes         # 访问节点信息 (用于节点监控)
  - nodes/proxy   # 访问节点代理 (有时用于访问节点上的kubelet指标)
  - nodes/metrics # 访问节点指标 (用于/metrics/cadvisor等)
  - services      # 访问服务信息 (用于基于Service的服务发现)
  - endpoints     # 访问端点信息 (服务发现需要知道Service对应的Pod IP和端口)
  - pods          # 访问Pod信息 (用于基于Pod的服务发现)
  verbs: ["get", "list", "watch"] # 允许的操作：获取单个资源、列出资源列表、监听资源变化
# 允许对核心API组("")中的ConfigMap进行 "get" 操作
- apiGroups: [""]
  resources:
  - configmaps
  verbs: ["get"] # Prometheus有时需要读取ConfigMap (虽然此配置中不直接体现)
# 允许对 networking.k8s.io API组中的Ingress进行 "get", "list", "watch" 操作
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses
  verbs: ["get", "list", "watch"] # 用于基于Ingress的服务发现 (如果配置了相关job)
# 允许访问非资源路径 "/metrics" 和 "/metrics/cadvisor"
- nonResourceURLs: ["/metrics", "/metrics/cadvisor"]
  verbs: ["get"] # 允许Prometheus直接访问Kubelet等组件暴露的/metrics端点
---
```

*   **作用**: 定义了一个名为 `prometheus` 的**集群**角色。集群角色意味着其权限范围是整个集群，而非限定于某个命名空间。
*   **目的**: 明确 Prometheus 服务账户需要哪些权限才能完成其监控任务，特别是**服务发现**。
*   **核心权限**:
    *   对 `nodes`, `services`, `endpoints`, `pods` 的 `get`, `list`, `watch` 权限是实现 Kubernetes 服务发现（自动找到监控目标）的关键。
    *   对 `nodes/proxy`, `nodes/metrics` 以及非资源 URL `/metrics`, `/metrics/cadvisor` 的访问权限是为了抓取节点和 Kubelet 相关指标。
    *   对 `ingresses` 的权限用于基于 Ingress 的服务发现（如果配置了）。

### 3. ClusterRoleBinding

```yaml
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding # 资源类型：集群角色绑定
metadata:
  name: prometheus # 绑定的名称
roleRef: # 引用要绑定的角色
  apiGroup: rbac.authorization.k8s.io # 角色的API组
  kind: ClusterRole                 # 角色类型 (必须与上面定义的ClusterRole匹配)
  name: prometheus                   # 角色名称 (必须与上面定义的ClusterRole名称匹配)
subjects: # 指定将角色绑定给哪些主体 (用户、组或ServiceAccount)
- kind: ServiceAccount             # 主体类型：服务账户
  name: prometheus                 # 服务账户名称 (必须与上面定义的ServiceAccount名称匹配)
  namespace: dci                   # 服务账户所在的命名空间 (必须与上面定义的ServiceAccount命名空间匹配)
---
```

*   **作用**: 创建一个名为 `prometheus` 的**集群**角色绑定。
*   **目的**: 将之前定义的 `prometheus` ClusterRole 授予（绑定到） `dci` 命名空间下的 `prometheus` ServiceAccount。
*   **效果**: 使得运行在 `dci` 命名空间、使用 `prometheus` ServiceAccount 的 Prometheus Pod 能够实际行使 `prometheus` ClusterRole 中定义的**所有权限**。

## 总结

`prometheus-rbac.yaml` 文件通过定义 ServiceAccount、ClusterRole 和 ClusterRoleBinding，遵循 Kubernetes 的 RBAC 最佳实践，为 Prometheus 提供了必要的权限，使其能够安全、自动地发现并监控 Kubernetes 集群中的各种资源（节点、服务、Pod等），是 Prometheus 在 Kubernetes 环境中实现服务发现功能的基础。 