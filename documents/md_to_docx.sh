#!/bin/bash

# md_to_docx.sh - Converts a Markdown file to DOCX using Pandoc.

# --- Usage Instructions ---
usage() {
  echo "Usage: $0 [-r <reference.docx>] <input_markdown_file.md>"
  echo "  Converts the specified Markdown file to a DOCX file with the same base name."
  echo "  Output file will be saved in the current working directory."
  echo
  echo "  Options:"
  echo "    -r <reference.docx> : Optional. Use a reference DOCX file for styling."
  echo "                          To set default fonts (e.g., 中文仿宋, 英文Times New Roman),"
  echo "                          create this file in Word/LibreOffice, modify the 'Normal' style"
  echo "                          to use these fonts, save it, and pass it with this option."
  echo
  echo "  Example 1 (default Pandoc styles): $0 my_document.md"
  echo "  Example 2 (custom fonts/styles):   $0 -r reference.docx my_document.md"
  exit 1
}

# --- Argument Parsing ---
reference_doc=""
while getopts ":r:" opt; do
  case $opt in
    r)
      reference_doc="$OPTARG"
      ;;
    \?)
      echo "Invalid option: -$OPTARG" >&2
      usage
      ;;
    :)
      echo "Option -$OPTARG requires an argument." >&2
      usage
      ;;
  esac
done
shift $((OPTIND-1)) # Shift processed options away

# --- Robustness Checks ---

# 1. Check if pandoc is installed
if ! command -v pandoc &> /dev/null; then
  echo "Error: 'pandoc' command not found." >&2
  echo "Please install Pandoc (https://pandoc.org/installing.html) and ensure it's in your PATH." >&2
  exit 1
fi

# 2. Check if input file argument exists after options
if [ "$#" -ne 1 ]; then
  echo "Error: Input Markdown file is required." >&2
  usage
fi

input_file="$1"

# 3. Check if the input file exists and is a regular file
if [ ! -f "$input_file" ]; then
  echo "Error: Input file '$input_file' not found or is not a regular file." >&2
  exit 1
fi

# 4. Check if the input file has a .md extension
if [[ "${input_file##*.}" != "md" ]]; then
  echo "Error: Input file '$input_file' does not seem to be a Markdown file (missing .md extension)." >&2
  exit 1
fi

# 5. Check if reference doc exists if provided
pandoc_options=() # Use an array for options
if [ -n "$reference_doc" ]; then
  if [ ! -f "$reference_doc" ]; then
    echo "Error: Reference DOCX file '$reference_doc' not found." >&2
    exit 1
  fi
  # Add reference doc option to the array
  pandoc_options+=(--reference-doc "$reference_doc")
  echo "Using reference document: $reference_doc"
fi

# --- Conversion Process ---

# Extract just the basename of the input file
input_basename=$(basename "$input_file")

# Construct the output filename in the current directory by replacing .md with .docx
output_file="${input_basename%.md}.docx"

echo "Attempting to convert '$input_file' to '$output_file' in current directory..."

# Execute pandoc conversion with optional reference doc
# Using an array handles spaces in filenames correctly
# Add -f gfm to explicitly specify GitHub Flavored Markdown input
pandoc -f gfm "${pandoc_options[@]}" "$input_file" -o "$output_file"

# Check pandoc exit status
if [ $? -eq 0 ]; then
  echo "Successfully converted '$input_file' to '$output_file'."
  exit 0
else
  echo "Error: Pandoc failed to convert '$input_file'." >&2
  # Optional: Clean up potentially incomplete output file?
  # rm -f "$output_file"
  exit 1
fi 