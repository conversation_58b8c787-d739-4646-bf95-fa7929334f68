// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://example.com/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.example.com/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/switches/{switchID}": {
            "get": {
                "description": "根据提供的交换机 ID，获取该交换机的当前详细状态信息，包括 CPU 利用率、内存利用率以及所有端口的基本状态列表。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "交换机监控"
                ],
                "summary": "获取交换机完整状态",
                "parameters": [
                    {
                        "type": "string",
                        "example": "CE1",
                        "description": "要查询状态的交换机 ID",
                        "name": "switchID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功获取交换机状态",
                        "schema": {
                            "$ref": "#/definitions/models.SwitchStatus"
                        }
                    },
                    "404": {
                        "description": "交换机未找到 (指定的 switchID 不存在)",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误 (例如, 获取状态失败, JSON 序列化失败)",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/switches/{switchID}/ports/{portID}": {
            "get": {
                "description": "根据路径参数 ` + "`" + `switchID` + "`" + `, ` + "`" + `portID` + "`" + ` 和查询参数 ` + "`" + `data` + "`" + ` 获取特定端口的信息。\n- ` + "`" + `data=status` + "`" + ` (或省略): 返回端口的完整状态信息 (结构: ` + "`" + `models.PortStatusResponse` + "`" + `)。\n- ` + "`" + `data=upstream` + "`" + `: 返回端口的上行流量信息 (结构: ` + "`" + `models.PortUpstreamTraffic` + "`" + `)。\n- ` + "`" + `data=downstream` + "`" + `: 返回端口的下行流量信息 (结构: ` + "`" + `models.PortDownstreamTraffic` + "`" + `)。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "端口监控"
                ],
                "summary": "获取端口数据 (状态/上行/下行)",
                "parameters": [
                    {
                        "type": "string",
                        "example": "CE1",
                        "description": "端口所属的交换机 ID",
                        "name": "switchID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F1",
                        "description": "要查询的端口 ID (注意: 路径中的特殊字符如 '/' 需要进行 URL 编码)",
                        "name": "portID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "enum": [
                            "status",
                            "upstream",
                            "downstream"
                        ],
                        "type": "string",
                        "default": "status",
                        "example": "status",
                        "description": "请求的数据类型 (status, upstream, downstream)。如果省略或提供无效值, 默认为 status。",
                        "name": "data",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功响应。响应体结构取决于 'data' 参数的值 (可能是 ` + "`" + `models.PortStatusResponse` + "`" + `, ` + "`" + `models.PortUpstreamTraffic` + "`" + ` 或 ` + "`" + `models.PortDownstreamTraffic` + "`" + `)",
                        "schema": {
                            "type": "object"
                        }
                    },
                    "400": {
                        "description": "无效的查询参数或端口ID格式错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "指定的交换机或端口未找到",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误 (例如, 获取数据失败, JSON 序列化失败)",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/traffic/chart/average": {
            "get": {
                "description": "根据查询参数（时间范围、粒度、A/Z 端设备端口）获取指定时间窗口内聚合后的 **平均** 流量数据。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "06-性能管理-流量图展示"
                ],
                "summary": "获取平均流量图数据",
                "parameters": [
                    {
                        "enum": [
                            "1m",
                            "5m",
                            "1h",
                            "1d"
                        ],
                        "type": "string",
                        "example": "5m",
                        "description": "数据聚合的时间粒度",
                        "name": "granularity",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "1h",
                            "2h",
                            "12h",
                            "1d",
                            "7d",
                            "1M",
                            "1Y"
                        ],
                        "type": "string",
                        "example": "1h",
                        "description": "预设时间范围 (结束时间为当前, 与 start_time/end_time 互斥)",
                        "name": "time_range",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2023-10-27T10:00:00Z",
                        "description": "自定义查询范围的开始时间 (RFC3339 UTC 格式, 若提供, 必须同时提供 end_time)",
                        "name": "start_time",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2023-10-27T11:00:00Z",
                        "description": "自定义查询范围的结束时间 (RFC3339 UTC 格式, 若提供, 必须同时提供 start_time)",
                        "name": "end_time",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "CE1",
                        "description": "连接 A 端的交换机 ID",
                        "name": "a_switch_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F1",
                        "description": "连接 A 端的端口 ID (需要进行 URL 编码)",
                        "name": "a_port_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "6005002",
                        "description": "连接 A 端的 VNI ID",
                        "name": "a_vni_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "CE2",
                        "description": "连接 Z 端的交换机 ID",
                        "name": "z_switch_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F2",
                        "description": "连接 Z 端的端口 ID (需要进行 URL 编码)",
                        "name": "z_port_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "6005002",
                        "description": "连接 Z 端的 VNI ID",
                        "name": "z_vni_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功获取平均流量数据",
                        "schema": {
                            "$ref": "#/definitions/models.TrafficChartResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误 (例如, 缺少参数, 参数冲突, 格式错误, 时间范围与粒度冲突)",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "资源未找到 (当前模拟数据下不会触发, 实际可能因设备/端口不存在而触发)",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误 (例如, 数据查询失败, JSON 序列化失败)",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/traffic/chart/maximum": {
            "get": {
                "description": "根据查询参数（时间范围、粒度、A/Z 端设备端口）获取指定时间窗口内聚合后的 **最大(峰值)** 流量数据。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "06-性能管理-流量图展示"
                ],
                "summary": "获取最大(峰值)流量图数据",
                "parameters": [
                    {
                        "enum": [
                            "1m",
                            "5m",
                            "1h",
                            "1d"
                        ],
                        "type": "string",
                        "example": "1h",
                        "description": "数据聚合的时间粒度",
                        "name": "granularity",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "1h",
                            "2h",
                            "12h",
                            "1d",
                            "7d",
                            "1M",
                            "1Y"
                        ],
                        "type": "string",
                        "example": "7d",
                        "description": "预设时间范围 (结束时间为当前, 与 start_time/end_time 互斥)",
                        "name": "time_range",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2023-10-20T00:00:00Z",
                        "description": "自定义查询范围的开始时间 (RFC3339 UTC 格式, 若提供, 必须同时提供 end_time)",
                        "name": "start_time",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2023-10-27T00:00:00Z",
                        "description": "自定义查询范围的结束时间 (RFC3339 UTC 格式, 若提供, 必须同时提供 start_time)",
                        "name": "end_time",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "CE1",
                        "description": "连接 A 端的交换机 ID",
                        "name": "a_switch_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F1",
                        "description": "连接 A 端的端口 ID (需要进行 URL 编码)",
                        "name": "a_port_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "6005002",
                        "description": "连接 A 端的 VNI ID",
                        "name": "a_vni_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "CE2",
                        "description": "连接 Z 端的交换机 ID",
                        "name": "z_switch_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F2",
                        "description": "连接 Z 端的端口 ID (需要进行 URL 编码)",
                        "name": "z_port_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "6005002",
                        "description": "连接 Z 端的 VNI ID",
                        "name": "z_vni_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功获取最大流量数据",
                        "schema": {
                            "$ref": "#/definitions/models.TrafficChartResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误 (例如, 缺少参数, 参数冲突, 格式错误, 时间范围与粒度冲突)",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "资源未找到 (当前模拟数据下不会触发, 实际可能因设备/端口不存在而触发)",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误 (例如, 数据查询失败, JSON 序列化失败)",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/traffic/chart/minimum": {
            "get": {
                "description": "根据查询参数（时间范围、粒度、A/Z 端设备端口）获取指定时间窗口内聚合后的 **最小(谷值)** 流量数据。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "06-性能管理-流量图展示"
                ],
                "summary": "获取最小(谷值)流量图数据",
                "parameters": [
                    {
                        "enum": [
                            "1m",
                            "5m",
                            "1h",
                            "1d"
                        ],
                        "type": "string",
                        "example": "1d",
                        "description": "数据聚合的时间粒度",
                        "name": "granularity",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "1h",
                            "2h",
                            "12h",
                            "1d",
                            "7d",
                            "1M",
                            "1Y"
                        ],
                        "type": "string",
                        "example": "1Y",
                        "description": "预设时间范围 (结束时间为当前, 与 start_time/end_time 互斥)",
                        "name": "time_range",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2022-10-27T00:00:00Z",
                        "description": "自定义查询范围的开始时间 (RFC3339 UTC 格式, 若提供, 必须同时提供 end_time)",
                        "name": "start_time",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2023-10-27T00:00:00Z",
                        "description": "自定义查询范围的结束时间 (RFC3339 UTC 格式, 若提供, 必须同时提供 start_time)",
                        "name": "end_time",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "CE1",
                        "description": "连接 A 端的交换机 ID",
                        "name": "a_switch_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F1",
                        "description": "连接 A 端的端口 ID (需要进行 URL 编码)",
                        "name": "a_port_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "6005002",
                        "description": "连接 A 端的 VNI ID",
                        "name": "a_vni_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "CE2",
                        "description": "连接 Z 端的交换机 ID",
                        "name": "z_switch_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F2",
                        "description": "连接 Z 端的端口 ID (需要进行 URL 编码)",
                        "name": "z_port_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "6005002",
                        "description": "连接 Z 端的 VNI ID",
                        "name": "z_vni_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功获取最小流量数据",
                        "schema": {
                            "$ref": "#/definitions/models.TrafficChartResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误 (例如, 缺少参数, 参数冲突, 格式错误, 时间范围与粒度冲突)",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "资源未找到 (当前模拟数据下不会触发, 实际可能因设备/端口不存在而触发)",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误 (例如, 数据查询失败, JSON 序列化失败)",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/traffic/summary": {
            "get": {
                "description": "根据查询参数（时间范围、粒度、A/Z 端设备端口）获取指定时间窗口内的流量摘要统计信息 (最小/平均/最大)。\n**注意:** \"最近流量\" (` + "`" + `latest` + "`" + ` 字段) 仅在请求使用 ` + "`" + `time_range` + "`" + ` 参数时才会包含在响应的 ` + "`" + `summary_data.items` + "`" + ` 中；若使用 ` + "`" + `start_time` + "`" + `/` + "`" + `end_time` + "`" + `，则 ` + "`" + `latest` + "`" + ` 字段会被省略。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "06-性能管理-流量图展示"
                ],
                "summary": "获取流量摘要数据",
                "parameters": [
                    {
                        "enum": [
                            "1m",
                            "5m",
                            "1h",
                            "1d"
                        ],
                        "type": "string",
                        "example": "1h",
                        "description": "数据聚合的时间粒度 (影响后台计算方式，但响应中不体现)",
                        "name": "granularity",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "1h",
                            "2h",
                            "12h",
                            "1d",
                            "7d",
                            "1M",
                            "1Y"
                        ],
                        "type": "string",
                        "example": "2h",
                        "description": "预设时间范围 (结束时间为当前, 与 start_time/end_time 互斥)。**若提供此参数，响应包含 'latest' 字段。**",
                        "name": "time_range",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2023-10-27T08:00:00Z",
                        "description": "自定义查询范围的开始时间 (RFC3339 UTC 格式, 若提供, 必须同时提供 end_time)。**若提供此参数，响应不含 'latest' 字段。**",
                        "name": "start_time",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2023-10-27T10:00:00Z",
                        "description": "自定义查询范围的结束时间 (RFC3339 UTC 格式, 若提供, 必须同时提供 start_time)。",
                        "name": "end_time",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "CE1",
                        "description": "连接 A 端的交换机 ID",
                        "name": "a_switch_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F1",
                        "description": "连接 A 端的端口 ID (需要进行 URL 编码)",
                        "name": "a_port_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "6005002",
                        "description": "连接 A 端的 VNI ID",
                        "name": "a_vni_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "CE2",
                        "description": "连接 Z 端的交换机 ID",
                        "name": "z_switch_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F2",
                        "description": "连接 Z 端的端口 ID (需要进行 URL 编码)",
                        "name": "z_port_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "6005002",
                        "description": "连接 Z 端的 VNI ID",
                        "name": "z_vni_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功获取流量摘要数据。注意 ` + "`" + `summary_data.items` + "`" + ` 中的 ` + "`" + `latest` + "`" + ` 字段仅在请求使用 ` + "`" + `time_range` + "`" + ` 时存在。",
                        "schema": {
                            "$ref": "#/definitions/models.TrafficSummaryResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误 (例如, 缺少参数, 参数冲突, 格式错误, 时间范围与粒度冲突)",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "资源未找到 (当前模拟数据下不会触发)",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "models.ErrorResponse": {
            "type": "object",
            "properties": {
                "details": {
                    "type": "string"
                },
                "error": {
                    "type": "string"
                },
                "request_id": {
                    "type": "string"
                }
            }
        },
        "models.Port": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "inErrors": {
                    "type": "integer"
                },
                "inUtil": {
                    "description": "上行使用率 (指针以区分 0 和 未提供)",
                    "type": "number"
                },
                "name": {
                    "description": "例如 \"GigabitEthernet1/0/0\"",
                    "type": "string"
                },
                "outErrors": {
                    "type": "integer"
                },
                "outUtil": {
                    "description": "下行使用率 (指针以区分 0 和 未提供)",
                    "type": "number"
                },
                "physicalState": {
                    "description": "物理状态: \"up\", \"down\", \"admin_down\"",
                    "type": "string"
                },
                "portId": {
                    "description": "Changed field name to PortID",
                    "type": "string"
                },
                "protocolState": {
                    "description": "协议状态: \"up\", \"down\"",
                    "type": "string"
                },
                "timestamp": {
                    "description": "流量数据获取时间",
                    "type": "string"
                },
                "totalInBytes": {
                    "description": "上行字节总量",
                    "type": "integer"
                },
                "totalInPkts": {
                    "description": "上行包总量",
                    "type": "integer"
                },
                "totalOutBytes": {
                    "description": "下行字节总量",
                    "type": "integer"
                },
                "totalOutPkts": {
                    "description": "下行包总量",
                    "type": "integer"
                }
            }
        },
        "models.SwitchStatus": {
            "type": "object",
            "properties": {
                "community": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "ip": {
                    "type": "string"
                },
                "lastUpdated": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "ports": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Port"
                    }
                },
                "status": {
                    "description": "整体状态: \"online\", \"offline\", \"unknown\"",
                    "type": "string"
                }
            }
        },
        "models.TrafficChartData": {
            "type": "object",
            "properties": {
                "series": {
                    "description": "数据系列数组",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.TrafficSeries"
                    }
                },
                "timestamps": {
                    "description": "RFC3339 UTC 格式的时间戳数组",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "unit": {
                    "description": "数据单位 (e.g., \"Mbps\", \"Kbps\")",
                    "type": "string"
                }
            }
        },
        "models.TrafficChartResponse": {
            "type": "object",
            "properties": {
                "chart_data": {
                    "$ref": "#/definitions/models.TrafficChartData"
                },
                "query_details": {
                    "$ref": "#/definitions/models.TrafficQueryDetails"
                },
                "request_id": {
                    "type": "string"
                }
            }
        },
        "models.TrafficQueryDetails": {
            "type": "object",
            "properties": {
                "a_port_id": {
                    "type": "string"
                },
                "a_switch_id": {
                    "type": "string"
                },
                "a_vni_id": {
                    "description": "A端的VNI ID (可选)",
                    "type": "string"
                },
                "end_time": {
                    "description": "RFC3339 UTC 格式的实际查询结束时间",
                    "type": "string"
                },
                "granularity": {
                    "type": "string"
                },
                "start_time": {
                    "description": "RFC3339 UTC 格式的实际查询开始时间",
                    "type": "string"
                },
                "z_port_id": {
                    "type": "string"
                },
                "z_switch_id": {
                    "type": "string"
                },
                "z_vni_id": {
                    "description": "Z端的VNI ID (可选)",
                    "type": "string"
                }
            }
        },
        "models.TrafficSeries": {
            "type": "object",
            "properties": {
                "data": {
                    "description": "与 Timestamps 对应的数值数组",
                    "type": "array",
                    "items": {
                        "type": "number"
                    }
                },
                "name": {
                    "description": "系列名称 (e.g., \"A端 (CE1-GE1/0/1) 平均入流量\")",
                    "type": "string"
                }
            }
        },
        "models.TrafficSummaryData": {
            "type": "object",
            "properties": {
                "items": {
                    "description": "摘要信息条目数组",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.TrafficSummaryItem"
                    }
                },
                "unit": {
                    "description": "所有流量值的单位 (e.g., \"Mbps\")",
                    "type": "string"
                }
            }
        },
        "models.TrafficSummaryItem": {
            "type": "object",
            "properties": {
                "average": {
                    "description": "时间段内平均值",
                    "type": "number"
                },
                "latest": {
                    "description": "最近流量速率 (nullable)",
                    "type": "number"
                },
                "maximum": {
                    "description": "时间段内最大值",
                    "type": "number"
                },
                "minimum": {
                    "description": "时间段内最小值",
                    "type": "number"
                },
                "name": {
                    "description": "行名称 (e.g., \"A端入流量\")",
                    "type": "string"
                }
            }
        },
        "models.TrafficSummaryResponse": {
            "type": "object",
            "properties": {
                "query_details": {
                    "$ref": "#/definitions/models.TrafficQueryDetails"
                },
                "request_id": {
                    "type": "string"
                },
                "summary_data": {
                    "$ref": "#/definitions/models.TrafficSummaryData"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080  // 请根据实际部署修改主机和端口",
	BasePath:         "/api/v1     // API 基础路径",
	Schemes:          []string{"http", "https"},
	Title:            "DCI Monitor API",
	Description:      "这是 DCI Monitor 项目的 API 文档，提供交换机状态和端口流量监控功能。",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
