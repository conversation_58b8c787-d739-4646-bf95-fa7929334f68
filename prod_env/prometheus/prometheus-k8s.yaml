---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: prometheus
  namespace: dci
  labels:
    app: prometheus
spec:
  serviceName: prometheus
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      serviceAccountName: prometheus
      securityContext:
        runAsUser: 65534
        runAsGroup: 65534
      initContainers:
      - name: fix-permissions
        image: cr.registry.pcloud.citic.com/dci/busybox:latest
        imagePullPolicy: IfNotPresent
        command: ["sh", "-c", "mkdir -p /prometheus/data && chown -R 65534:65534 /prometheus/data"]
        securityContext:
          runAsUser: 0
        volumeMounts:
        - name: prometheus-data
          mountPath: /prometheus
      containers:
      - name: prometheus
        image: cr.registry.pcloud.citic.com/dci/prometheus:v3.3.1
        imagePullPolicy: IfNotPresent
        args:
        - "--config.file=/etc/prometheus/prometheus.yml"
        - "--storage.tsdb.path=/prometheus/data"
        - "--storage.tsdb.retention.time=60d"
        - "--storage.tsdb.no-lockfile"
        - "--storage.tsdb.wal-compression"
        - "--storage.tsdb.max-block-duration=4h"
        - "--storage.tsdb.min-block-duration=2h"
        - "--web.console.libraries=/etc/prometheus/console_libraries"
        - "--web.console.templates=/etc/prometheus/consoles"
        - "--web.enable-lifecycle"
        - "--web.enable-admin-api"
        - "--web.external-url=http://prometheus.dci.svc.cluster.local:9090"
        - "--web.enable-remote-write-receiver"
        - "--web.config.file=/etc/prometheus/web-config.yml"
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        ports:
        - name: web
          containerPort: 9090
        readinessProbe:
          tcpSocket:
            port: 9090
          initialDelaySeconds: 90
          timeoutSeconds: 30
          periodSeconds: 15
        livenessProbe:
          tcpSocket:
            port: 9090
          initialDelaySeconds: 90
          timeoutSeconds: 30
          periodSeconds: 15
        resources:
          requests:
            cpu: "100m"
            memory: 2Gi
          limits:
            cpu: 2
            memory: 8Gi
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus/prometheus.yml
          subPath: prometheus.yml
        - name: prometheus-rules
          mountPath: /etc/prometheus/prometheus-rules.yaml
          subPath: prometheus-rules.yaml
        - name: prometheus-web-config
          mountPath: /etc/prometheus/web-config.yml
          subPath: web-config.yml
        - name: prometheus-data
          mountPath: /prometheus
      imagePullSecrets:
      - name: dci-images-key
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
          items:
          - key: prometheus.yml
            path: prometheus.yml
      - name: prometheus-rules
        configMap:
          name: prometheus-rules
          items:
          - key: prometheus-rules.yaml
            path: prometheus-rules.yaml
      - name: prometheus-web-config
        configMap:
          name: prometheus-web-config
          items:
          - key: web-config.yml
            path: web-config.yml
  volumeClaimTemplates:
  - metadata:
      name: prometheus-data
      annotations:
        volume.beta.kubernetes.io/storage-class: "dci-nfs-storage"
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: "dci-nfs-storage"
      resources:
        requests:
          storage: 30Gi 