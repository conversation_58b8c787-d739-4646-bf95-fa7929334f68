---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: dci
data:
  prometheus.yml: |
    global:
      scrape_interval: 30s
      evaluation_interval: 30s
      scrape_timeout: 10s
    remote_write:
      - url: "http://10.247.20.206:30006/api/v1/write"
        remote_timeout: 120s
        basic_auth:
          username: "dciadmin"
          password: "c8PI3huRud8tW"  # 从web-config.yml注释中获取的密码
        queue_config:
          capacity: 5000
          max_shards: 10

    # 告警规则文件配置
    rule_files:
      - "/etc/prometheus/prometheus-rules.yaml"

    # 告警配置
    alerting:
      alertmanagers:
      - static_configs:
        - targets:
          # 将在以后的阶段部署AlertManager
          # - "alertmanager.dci.svc.cluster.local:9093"

    # 抓取配置
    scrape_configs:
      # 监控Prometheus自身
      - job_name: 'prometheus'
        static_configs:
          - targets: ['localhost:9090']

      # 基于Kubernetes Service的服务发现
      - job_name: 'kubernetes-services'
        kubernetes_sd_configs:
          - role: service
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
            action: replace
            target_label: __scheme__
            regex: (https?)
          - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_service_annotation_prometheus_io_port]
            action: replace
            target_label: __address__
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
          - action: labelmap
            regex: __meta_kubernetes_service_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_service_name]
            action: replace
            target_label: kubernetes_service_name
      
      # 监控Kubernetes节点
      - job_name: 'kubernetes-nodes'
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
          insecure_skip_verify: true
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        kubernetes_sd_configs:
          - role: node
        relabel_configs:
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)
          - target_label: __address__
            replacement: kubernetes.default.svc:443
          - source_labels: [__meta_kubernetes_node_name]
            regex: (.+)
            target_label: __metrics_path__
            replacement: /api/v1/nodes/${1}/proxy/metrics
      
      # 监控Kubernetes Pod
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: kubernetes_pod_name

      # Telegraf指标抓取配置
      - job_name: 'telegraf'
        honor_labels: true  # 保留原始标签
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_app]
            action: keep
            regex: telegraf
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: (\d+)
            replacement: $1
            target_label: __meta_kubernetes_pod_container_port_number
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: kubernetes_pod_name

      # 华为云指标抓取配置
      - job_name: 'huaweicloud'
        static_configs:
          - targets: ['dci-huawei-cloudeye-exporter-cn-north4-svc:8087']
          - targets: ['dci-huawei-cloudeye-exporter-cn-east-2-svc:8087']
          - targets: ['dci-huawei-cloudeye-exporter-cn-east3-svc:8087']
          - targets: ['dci-huawei-cloudeye-exporter-cn-south1-svc:8087']
        params:
          services: ['SYS.DCAAS']

      # 额外的Prometheus端点抓取
      - job_name: 'prometheus-eps'
        metrics_path: '/default'
