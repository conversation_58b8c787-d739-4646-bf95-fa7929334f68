# Prometheus基础组件部署说明

本目录包含DCI监测系统Prometheus基础组件的Kubernetes部署配置文件，用于在K8S集群中部署Prometheus监控系统。

## 目录结构

```
prometheus/
├── prometheus-configmap.yaml         # Prometheus服务器配置
├── prometheus-rules-configmap.yaml   # 告警规则配置
├── prometheus.yaml                   # Prometheus StatefulSet定义
├── prometheus-service.yaml           # Prometheus服务定义
├── prometheus-rbac.yaml              # Prometheus权限配置
├── prometheus-web-config.yaml        # Prometheus Web界面认证配置
├── prometheus-basic-auth-secret.yaml # Basic Auth认证Secret
└── README.md                         # 本文档
```

## 部署步骤

1. 确认Kubernetes集群可用并已配置`kubectl`
2. 应用RBAC配置：
   ```bash
   kubectl apply -f prometheus-rbac.yaml
   ```
3. 应用ConfigMap配置：
   ```bash
   kubectl apply -f prometheus-configmap.yaml
   kubectl apply -f prometheus-rules-configmap.yaml
   kubectl apply -f prometheus-web-config.yaml
   ```
4. 创建认证Secret：
   ```bash
   kubectl apply -f prometheus-basic-auth-secret.yaml
   ```
5. 部署Prometheus实例：
   ```bash
   kubectl apply -f prometheus-k8s.yaml
   ```
6. 部署Service：
   ```bash
   kubectl apply -f prometheus-service.yaml
   ```
7. 验证部署状态：
   ```bash
   kubectl get pods -n dci -l app=prometheus
   kubectl get svc -n dci -l app=prometheus
   ```

## 配置说明

### 资源需求

Prometheus实例配置了以下资源需求：
- CPU请求：2核
- 内存请求：8GB
- 存储：可根据实际数据量调整，当前数据保留期为60天
- SLB地址：10.247.20.206

### 数据保留

当前配置的数据保留期为60天（通过`--storage.tsdb.retention.time=60d`参数设置）。

### 扩展性

部署采用StatefulSet，可以方便地进行扩容：
```bash
kubectl scale statefulset prometheus -n dci --replicas=2
```

## 注意事项

1. 首次部署前，请确保存储类`dci-nfs-storage`已在集群中创建
2. Prometheus配置变更后，可以通过API端点重新加载：
   ```bash
   kubectl port-forward svc/prometheus -n dci 9090
   curl -X POST http://localhost:9090/-/reload
   ```
3. 对于生产环境，建议配置额外的备份策略 

# Prometheus 安全配置说明

本文档描述了DCI监控系统中Prometheus的安全配置，以保护监控数据的安全性和隐私性。

## 安全措施概述

我们采用了以下安全措施来保护Prometheus服务：

1. **认证授权**
   - 基本HTTP认证 (Basic Auth) 限制UI访问 
     - 使用web-config.yml配置文件启用
     - 默认用户名：admin
     - 默认密码：prometheus（已加密存储）
   - 加密的密码存储
     - 使用bcrypt哈希算法存储密码
     - 密码不以明文形式存在于配置中
   - 基于角色的访问控制 (RBAC)
     - 使用Kubernetes RBAC限制Prometheus访问集群资源的权限
     - 仅允许Prometheus获取监控所需的最小权限

## 访问控制

### 用户访问

- Prometheus UI通过Basic Auth进行保护
- 默认凭据：
  - 用户名: `admin`
  - 密码: 请联系管理员获取
  
### API访问

- 使用白名单限制只有特定IP可以访问API
- 内部服务通过标签选择器获得访问权限

## 网络策略

NetworkPolicy配置确保：

- 只有同命名空间中的Pod可以访问Prometheus
- 只有带有`role: monitoring-access`标签的Pod可以访问
- 出站流量仅限于必要的服务 (如Kubernetes API和DNS)

## 部署说明

部署Prometheus安全配置需按照以下顺序应用:

```bash
kubectl apply -f prometheus-rbac.yaml
kubectl apply -f prometheus-web-config.yaml
kubectl apply -f prometheus-podsecuritypolicy.yaml
kubectl apply -f prometheus-networkpolicy.yaml
kubectl apply -f prometheus-configmap.yaml
kubectl apply -f prometheus-rules-configmap.yaml
kubectl apply -f prometheus.yaml
kubectl apply -f prometheus-service.yaml
kubectl apply -f prometheus-ingress.yaml
```

## 安全最佳实践

1. 定期更换Basic Auth密码
2. 定期审查NetworkPolicy配置
3. 使用Pod Security Context限制权限
4. 监控访问日志发现异常行为

## 故障排除

如果遇到访问问题，请检查：

1. 基本认证凭据是否正确
2. IP是否在白名单内
3. NetworkPolicy配置是否正确
4. Pod标签是否正确设置 