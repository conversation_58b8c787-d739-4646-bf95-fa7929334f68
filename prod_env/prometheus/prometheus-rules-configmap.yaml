apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules
  namespace: dci
data:
  prometheus-rules.yaml: |
    groups:
    # DCI流量数据预计算规则 (Recording Rules)
    - name: dci_port_flow_rules
      interval: 1m
      rules:
      - record: dci_snmp_flow_ifHCInOctets_rate
        expr: rate(dci_snmp_flow_ifHCInOctets[5m]) * 8
        labels:
          job: dci-snmp-rate-rules
          unit: bps
      - record: dci_snmp_flow_ifHCOutOctets_rate
        expr: rate(dci_snmp_flow_ifHCOutOctets[5m]) * 8
        labels:
          job: dci-snmp-rate-rules
          unit: bps
    # 系统基础设施告警
    - name: infrastructure
      rules:
      - alert: InstanceDown
        expr: up == 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "实例不可用: {{ $labels.instance }}"
          description: "{{ $labels.job }} 的实例 {{ $labels.instance }} 已经不可用超过5分钟"
          
      - alert: HighCPULoad
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 85
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "CPU负载过高: {{ $labels.instance }}"
          description: "实例 {{ $labels.instance }} 的CPU使用率超过85%，持续10分钟"
          
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用过高: {{ $labels.instance }}"
          description: "实例 {{ $labels.instance }} 的内存使用率超过90%"
          
      - alert: LowDiskSpace
        expr: node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"} * 100 < 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "磁盘空间不足: {{ $labels.instance }}"
          description: "实例 {{ $labels.instance }} 的挂载点 {{ $labels.mountpoint }} 剩余空间不足10%"
          
    # 网络设备监控告警
    - name: network_devices
      rules:
      - alert: InterfaceHighUtilization
        expr: rate(if_in_octets[5m]) * 8 / if_speed * 100 > 85 or rate(if_out_octets[5m]) * 8 / if_speed * 100 > 85
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "接口利用率过高: {{ $labels.instance }} - {{ $labels.interface }}"
          description: "设备 {{ $labels.device }} 上的接口 {{ $labels.interface }} 利用率超过85%，持续15分钟"
          
      - alert: InterfaceErrorsIncreasing
        expr: rate(if_in_errors[5m]) > 0 or rate(if_out_errors[5m]) > 0
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "接口错误增加: {{ $labels.instance }} - {{ $labels.interface }}"
          description: "设备 {{ $labels.device }} 上的接口 {{ $labels.interface }} 出现错误增长"
          
    # Prometheus自监控告警
    - name: prometheus_self_monitoring
      rules:
      - alert: PrometheusHighQueryLoad
        expr: rate(prometheus_engine_query_duration_seconds_count[5m]) > 10
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Prometheus查询负载高: {{ $labels.instance }}"
          description: "Prometheus实例 {{ $labels.instance }} 的查询负载超过每秒10次，持续15分钟"
          
      - alert: PrometheusTSDBReloadsFailing
        expr: increase(prometheus_tsdb_reloads_failures_total[1h]) > 0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Prometheus TSDB重载失败: {{ $labels.instance }}"
          description: "Prometheus {{ $labels.instance }} 的时序数据库重载失败，可能导致数据丢失"
          
      - alert: PrometheusNotIngestingSamples
        expr: rate(prometheus_tsdb_head_samples_appended_total[5m]) <= 0
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Prometheus不再接收样本: {{ $labels.instance }}"
          description: "Prometheus {{ $labels.instance }} 在过去10分钟内未接收任何样本"
          
      - alert: PrometheusHighMemoryUsage
        expr: (process_resident_memory_bytes{job="prometheus"} / (1024 * 1024 * 1024)) > (container_spec_memory_limit_bytes{job="prometheus"} / (1.5 * 1024 * 1024 * 1024))
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Prometheus内存使用过高: {{ $labels.instance }}"
          description: "Prometheus {{ $labels.instance }} 的内存使用超过限制的66%，持续15分钟" 