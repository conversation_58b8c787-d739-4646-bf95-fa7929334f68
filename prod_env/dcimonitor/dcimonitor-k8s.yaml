# -----------------------------------------------------------------------------
# Secret for dci-monitor service credentials
#
# IMPORTANT: Before applying, you must create the 'dci-monitor' client
# certificate and password using the provided script:
#
#   cd dci-monitor/kafka/scripts
#   ./issue_new_client_cert.sh dci-monitor
#
# Then, use the generated password to create the secret. It's recommended to create
# one single secret for all credentials of this service.
#
#   kubectl create secret generic dcimonitor-credentials -n dci \
#     --from-literal=db-username='root' \
#     --from-literal=db-password='YOUR_DB_PASSWORD' \
#     --from-literal=kafka-username='dci-monitor' \
#     --from-literal=kafka-password='<password_from_issue_script>'
#
# -----------------------------------------------------------------------------
---
apiVersion: apps/v1
kind: StatefulSet  # 改为StatefulSet以支持持久化存储
metadata:
  name: dcimonitor
  namespace: dci
  labels:
    app: dcimonitor
    environment: production
spec:
  serviceName: dcimonitor-headless  # StatefulSet需要一个Headless Service
  replicas: 2  # 生产环境增加到2个副本
  selector:
    matchLabels:
      app: dcimonitor
  template:
    metadata:
      labels:
        app: dcimonitor
        environment: production
    spec:
      affinity:  # 添加Pod反亲和性，确保副本分布在不同节点
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - dcimonitor
              topologyKey: "kubernetes.io/hostname"
      imagePullSecrets:
        - name: dci-images-key
      containers:
      - name: dcimonitor
        image: cr.registry.pcloud.citic.com/dci/dcimonitor:dcimonitor-0.1.1
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: TZ
          value: "Asia/Shanghai"
        volumeMounts:
        - name: config-volume
          mountPath: /lib/dci/config
        - name: kafka-client-certs
          mountPath: /etc/kafka/certs
          readOnly: true
        - name: log-volume
          mountPath: /var/log/dcimonitor
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 20
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            cpu: "200m"       # 生产环境增加CPU请求
            memory: "256Mi"    # 生产环境增加内存请求
          limits:
            cpu: "2000m"      # 2 vCPU
            memory: "4Gi"     # 生产环境增加内存限制
      volumes:
      - name: config-volume
        configMap:
          name: dcimonitor-config
      - name: kafka-client-certs
        secret:
          secretName: kafka-client-certs # This secret is created by 'apply_secrets.sh'
  # 使用volumeClaimTemplates替代直接的卷定义，为每个实例提供独立存储
  volumeClaimTemplates:
  - metadata:
      name: log-volume
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: dci-nfs-storage
      resources:
        requests:
          storage: 9Gi
