---
apiVersion: v1
kind: Service
metadata:
  name: dcimonitor-service
  namespace: dci
  labels:
    app: dcimonitor
spec:
  selector:
    app: dcimonitor # 选择带有 'app: dcimonitor' 标签的 Pod
  # 生产环境使用集群内部访问，移除NodePort
  type: ClusterIP  # 集群内部访问更安全
  ports:
  - protocol: TCP
    port: 8080
    targetPort: 8080
    name: http
  # ports:
  # - protocol: TCP
  #   port: 80 # Service 端口
  #   targetPort: 8080 # Pod 端口
  # type: ClusterIP # 默认类型，只在集群内部可访问
  # 如果需要从集群外部访问（例如通过 NodePort），可以取消注释并修改下面的行
  # type: NodePort
  # ports:
  # - protocol: TCP
  #   port: 8080
  #   targetPort: 8080
  #   nodePort: 30000 # 指定一个节点端口 (范围通常是 30000-32767) 

---
# 添加Headless Service以支持StatefulSet
apiVersion: v1
kind: Service
metadata:
  name: dcimonitor-headless
  namespace: dci
  labels:
    app: dcimonitor
    environment: production
spec:
  clusterIP: None
  selector:
    app: dcimonitor
  ports:
  - port: 8080
    targetPort: 8080
    name: http
