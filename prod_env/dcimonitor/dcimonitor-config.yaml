---
apiVersion: v1
kind: ConfigMap
metadata:
  name: dcimonitor-config
  namespace: dci
data:
  config.yaml: |
    # DCI监控系统通用配置文件 - 生产环境版本
    # Credentials are injected via environment variables from secrets for security.

    database:
      driver: mysql
      host: "rm-el5i5532025ja5639.mysql.rds.pcloud-ops.citic.com"
      port: 3306
      # username & password will be injected from secret 'dcimonitor-credentials'
      username: root
      password: FJkhJ0GBPKZva1w
      dbname: dci_monitor
      charset: utf8mb4

    topology:
      snapshotInterval: 1h
      healthCheckInterval: 30s

    server:
      port: 8080
      mode: "release" # Use 'release' in production to disable Swagger UI

    logger:
      level: "info" # debug, info, warn, error - 生产环境使用info级别
      dir: "/var/log/dcimonitor"
      maxSize: 100
      maxBackups: 30
      maxAge: 30
      compress: true

    cron:
      refreshInterval: "@every 1m"

    prometheus:
      address: "http://prometheus.dci.svc.cluster.local:9090" # Prometheus 服务器地址
      timeout: 30s # 查询超时时间
      username: "dciadmin"
      password: "c8PI3huRud8tW" 
