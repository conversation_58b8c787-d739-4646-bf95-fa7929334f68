# DCI Monitor 生产环境部署

本目录包含DCI Monitor服务在生产环境的部署配置。

## 部署前准备

1. 确保Kafka集群已正确部署，并且已为dci-monitor服务创建了客户端证书：

```bash
# 在Kafka证书目录执行
./issue_new_client_cert.sh dci-monitor
```

2. 创建包含服务认证信息的密钥：

```bash
kubectl create secret generic dcimonitor-credentials -n dci \
  --from-literal=db-username='root' \
  --from-literal=db-password='FJkhJ0GBPKZva1w' \
  --from-literal=kafka-username='dci-monitor' \
  --from-literal=kafka-password='<从kafka_credentials.txt获取的密码>'
```

## 部署步骤

按照以下顺序部署服务：

```bash
# 1. 部署配置文件
kubectl apply -f dcimonitor-config.yaml

# 2. 部署服务
kubectl apply -f dcimonitor-k8s.yaml

# 3. 部署对外服务
kubectl apply -f dcimonitor-service.yaml
```

## 验证部署

```bash
# 检查Pod是否正常运行
kubectl get pods -n dci -l app=dcimonitor

# 检查服务是否可用
kubectl get svc -n dci -l app=dcimonitor

# 查看服务日志
kubectl logs -f -n dci -l app=dcimonitor
```

## 生产环境配置说明

- **高可用性**：使用2个副本，通过Pod反亲和性确保部署在不同节点
- **StatefulSet**：配置为StatefulSet以支持持久化存储
- **持久存储**：每个Pod使用独立的PVC存储日志
- **资源配置**：增加了CPU和内存资源限制
- **Kafka连接**：配置使用正确的Kafka集群内部通信端口(9092)
- **日志级别**：使用info级别，适合生产环境
- **服务访问**：配置为ClusterIP，仅允许集群内部访问，增强安全性

## 注意事项

- 服务使用持久卷存储日志，确保NFS存储正常工作
- Kafka客户端证书必须正确配置，否则将无法连接到Kafka
- 数据库连接信息存储在配置中，确保密码安全 