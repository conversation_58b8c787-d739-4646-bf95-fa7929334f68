#!/bin/bash
#
# Kafka多端口配置验证测试脚本
# 用于验证基于多端口的Kafka广播地址配置是否生效
#

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置参数
SLB_DOMAIN="dcikafka.intra.citic-x.com"
PORTS=("30010" "30011" "30012")
TEST_TOPIC="test-multiport-$(date +%s)"

# 使用方法
usage() {
    echo -e "${BLUE}使用方法:${NC} $0 [选项]"
    echo "选项:"
    echo "  -d, --domain DOMAIN     指定SLB域名 (默认: $SLB_DOMAIN)"
    echo "  -p, --ports PORT1,PORT2,PORT3  指定端口列表 (默认: ${PORTS[*]})"
    echo "  -t, --topic TOPIC       指定测试主题名 (默认: $TEST_TOPIC)"
    echo "  -h, --help              显示此帮助信息"
    exit 1
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -d|--domain)
            SLB_DOMAIN="$2"
            shift 2
            ;;
        -p|--ports)
            IFS=',' read -r -a PORTS <<< "$2"
            shift 2
            ;;
        -t|--topic)
            TEST_TOPIC="$2"
            shift 2
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo -e "${RED}错误: 未知参数 $1${NC}"
            usage
            ;;
    esac
done

# 检查kafka-topics.sh命令是否存在
if ! command -v kafka-topics.sh &> /dev/null; then
    echo -e "${RED}错误: 未找到kafka-topics.sh命令${NC}"
    echo "请确保Kafka客户端工具已安装并在PATH中"
    exit 1
fi

echo -e "${BLUE}=== Kafka多端口配置验证测试 ===${NC}"
echo -e "SLB域名: ${YELLOW}$SLB_DOMAIN${NC}"
echo -e "测试端口: ${YELLOW}${PORTS[*]}${NC}"
echo -e "测试主题: ${YELLOW}$TEST_TOPIC${NC}"
echo

# 第1步: 测试每个端口连接
echo -e "${BLUE}第1步: 测试端口连接性${NC}"
for port in "${PORTS[@]}"; do
    echo -ne "测试连接 $SLB_DOMAIN:$port ... "
    if timeout 5 bash -c "cat < /dev/null > /dev/tcp/$SLB_DOMAIN/$port"; then
        echo -e "${GREEN}成功${NC}"
    else
        echo -e "${RED}失败${NC}"
        echo -e "${RED}错误: 无法连接到 $SLB_DOMAIN:$port${NC}"
        echo "请检查以下内容:"
        echo "  - 确保SLB域名解析正确"
        echo "  - 确保端口在SLB上正确配置"
        echo "  - 确保Kafka服务正在运行"
        echo "  - 检查网络防火墙是否允许此端口"
        exit 1
    fi
done
echo

# 第2步: 获取元数据信息
echo -e "${BLUE}第2步: 获取Kafka集群元数据${NC}"
for port in "${PORTS[@]}"; do
    echo -e "${YELLOW}从 $SLB_DOMAIN:$port 获取集群元数据:${NC}"
    if ! kafka-topics.sh --bootstrap-server "$SLB_DOMAIN:$port" --list > /dev/null 2>&1; then
        echo -e "${RED}错误: 无法从 $SLB_DOMAIN:$port 获取元数据${NC}"
        echo "请检查:"
        echo "  - Kafka broker的advertised.listeners配置"
        echo "  - 集群是否健康运行"
        exit 1
    fi
    
    # 获取broker信息
    echo -e "${YELLOW}broker列表:${NC}"
    kafka-broker-api-versions.sh --bootstrap-server "$SLB_DOMAIN:$port" 2>&1 | grep "id"
    echo
done

# 第3步: 创建测试主题
echo -e "${BLUE}第3步: 创建测试主题${NC}"
echo -ne "创建主题 $TEST_TOPIC ... "
if kafka-topics.sh --bootstrap-server "$SLB_DOMAIN:${PORTS[0]}" --create --topic "$TEST_TOPIC" --partitions 3 --replication-factor 3 > /dev/null 2>&1; then
    echo -e "${GREEN}成功${NC}"
else
    echo -e "${RED}失败${NC}"
    echo -e "${RED}错误: 无法创建测试主题 $TEST_TOPIC${NC}"
    echo "请检查:"
    echo "  - Kafka集群健康状态"
    echo "  - 确保主题名称唯一"
    exit 1
fi

# 获取主题详情
echo -e "${YELLOW}主题详情:${NC}"
kafka-topics.sh --bootstrap-server "$SLB_DOMAIN:${PORTS[0]}" --describe --topic "$TEST_TOPIC"
echo

# 第4步: 测试消息发送和接收
echo -e "${BLUE}第4步: 测试消息发送和接收${NC}"
echo -e "${YELLOW}发送测试消息:${NC}"

# 生成测试消息
MESSAGE="测试消息 $(date)"
TEMP_MSG_FILE="/tmp/kafka_test_msg_$$.txt"
echo "$MESSAGE" > "$TEMP_MSG_FILE"

# 发送消息
if ! kafka-console-producer.sh --bootstrap-server "$SLB_DOMAIN:${PORTS[1]}" --topic "$TEST_TOPIC" < "$TEMP_MSG_FILE" > /dev/null 2>&1; then
    echo -e "${RED}错误: 无法发送消息${NC}"
    rm -f "$TEMP_MSG_FILE"
    exit 1
fi

echo -e "${YELLOW}接收测试消息:${NC}"
# 接收消息
RECEIVED_MESSAGE=$(timeout 10 kafka-console-consumer.sh --bootstrap-server "$SLB_DOMAIN:${PORTS[2]}" --topic "$TEST_TOPIC" --from-beginning --max-messages 1 2>/dev/null)

if [[ -n "$RECEIVED_MESSAGE" ]]; then
    echo -e "收到消息: ${GREEN}$RECEIVED_MESSAGE${NC}"
else
    echo -e "${RED}错误: 未能接收到消息${NC}"
    rm -f "$TEMP_MSG_FILE"
    exit 1
fi

rm -f "$TEMP_MSG_FILE"
echo

# 第5步: 通过不同端口获取相同数据
echo -e "${BLUE}第5步: 验证不同端口获取相同数据${NC}"
for port in "${PORTS[@]}"; do
    echo -ne "通过 $SLB_DOMAIN:$port 获取主题列表 ... "
    TOPICS=$(kafka-topics.sh --bootstrap-server "$SLB_DOMAIN:$port" --list 2>/dev/null)
    if [[ $? -eq 0 && "$TOPICS" == *"$TEST_TOPIC"* ]]; then
        echo -e "${GREEN}成功${NC}"
    else
        echo -e "${RED}失败${NC}"
        echo -e "${RED}错误: 无法通过 $SLB_DOMAIN:$port 获取正确的主题列表${NC}"
        exit 1
    fi
done
echo

# 清理测试主题
echo -e "${BLUE}清理测试资源${NC}"
echo -ne "删除测试主题 $TEST_TOPIC ... "
if kafka-topics.sh --bootstrap-server "$SLB_DOMAIN:${PORTS[0]}" --delete --topic "$TEST_TOPIC" > /dev/null 2>&1; then
    echo -e "${GREEN}成功${NC}"
else
    echo -e "${YELLOW}警告: 无法删除测试主题，这可能是因为Kafka配置了禁止删除主题${NC}"
fi
echo

# 测试结果
echo -e "${BLUE}测试结果${NC}"
echo -e "${GREEN}✓ 所有测试通过!${NC}"
echo -e "${GREEN}✓ 多端口配置验证成功!${NC}"
echo
echo -e "测试摘要:"
echo -e "  - ${GREEN}✓${NC} 所有端口 (${PORTS[*]}) 均可连接"
echo -e "  - ${GREEN}✓${NC} 能够从每个端口获取集群元数据"
echo -e "  - ${GREEN}✓${NC} 能够成功创建和访问测试主题"
echo -e "  - ${GREEN}✓${NC} 能够通过不同端口发送和接收消息"
echo -e "  - ${GREEN}✓${NC} 配置能够正确处理broker发现和路由"
echo

exit 0 