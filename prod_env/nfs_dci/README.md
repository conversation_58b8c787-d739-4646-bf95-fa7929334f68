## NFS存储配置说明

### 概述
`nfs_dci`目录包含NFS存储相关配置，为Elasticsearch提供持久化存储能力：

1. **存储架构**：
   - 使用NFS服务器(10.247.32.130)提供集中式存储
   - 基础路径：`/mnt`
   - 支持多节点同时挂载，适合Elasticsearch集群

2. **配置文件**：
   - `class.yaml`: 存储类配置
   - `deployment.yaml`: NFS客户端部署配置
   - `rbac.yaml`: 权限配置

3. **存储路径**：
   - Elasticsearch数据: `/mnt/dci-test-pvc-pvc-*`
   - 每个ES节点对应一个独立PV

### 部署存储
```bash
# 验证YAML语法
yamllint nfs_dci/*.yaml

# 应用存储类
kubectl apply -f nfs_dci/class.yaml
kubectl apply -f nfs_dci/rbac.yaml
kubectl apply -f nfs_dci/deployment.yaml

# 查看存储类
kubectl get storageclass
```

### 存储监控
```bash
# 查看所有PV状态
kubectl get pv -n dci

# 查看ES使用的PVC
kubectl get pvc -l app=elasticsearch -n dci

# 查看具体PVC详情
kubectl describe pvc elasticsearch-data-elasticsearch-0 -n dci
```

### 存储维护
```bash
# NFS服务器检查
ssh root@10.247.32.130 "df -h /mnt"

# 查看NFS具体数据目录
ssh root@10.247.32.130 "ls -la /mnt/dci-test-pvc-*"

# 清理旧数据(谨慎操作)
ssh root@10.247.32.130 "rm -rf /mnt/dci-test-pvc-<specific-pv-id>"
```

### 查看存储
```bash
kubectl get pvc -n dci
kubectl get pv -n dci
```

### 生产环境K8S集群信息
- 10.247.32.130
- 10.247.32.131
- 10.247.32.132
- 10.247.32.133

### 生产环境挂载点
10.247.32.130:/mnt (493G可用空间)

# 在DCI云平台部署

在保密字典中添加：

```bash
dci-images-key，存储dci镜像的key
```

yaml中需要添加：

```yaml
imagePullSecrets:
  - name: dci-images-key
```


