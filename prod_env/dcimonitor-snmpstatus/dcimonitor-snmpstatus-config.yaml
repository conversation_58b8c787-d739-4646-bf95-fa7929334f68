---
apiVersion: v1
kind: ConfigMap
metadata:
  name: dcimonitor-snmpstatus-config
  namespace: dci
data:
  dcimonitor-snmpstatus.yaml: |
    # 基本服务配置
    service_name: "dci-monitor-snmp-processor"
    version: "1.0.0"
    
    # 处理器配置
    processor:
      # 指标前缀
      metric_prefix: "dci_snmp_"
      
      # 流量指标配置
      enable_in_octets: true
      enable_out_octets: true
      enable_errors: true
      enable_discards: true
      
      # 设备状态指标配置
      enable_device_cpu: true
      enable_device_memory: true
      
      # 接口状态指标配置
      enable_interface_status: true
      
      # 指标全局标签
      metric_labels:
        environment: "production"
        region: "default"
    
    # Kafka配置
    kafka:
      # Kafka服务器地址列表
      brokers:
        - "kafka-0.kafka-headless.dci.svc.cluster.local:30002"
        - "kafka-1.kafka-headless.dci.svc.cluster.local:30002"
        - "kafka-2.kafka-headless.dci.svc.cluster.local:30002"
      
      # Kafka主题列表 (多个主题)
      topics:
        - "dci.monitor.v1.defaultchannel.flows.snmp"           # 流量数据主题
        - "dci.monitor.v1.defaultchannel.status.devices.snmp"  # 设备状态主题
        - "dci.monitor.v1.defaultchannel.status.interface.snmp" # 接口状态主题
      
      # Kafka消费者组
      consumer_group: "dci-monitor-snmp-processor"
      
      # Kafka安全配置
      security:
        tls:
          enabled: true
          # 证书将从挂载的 Secret 中获取
          ca_file: "/etc/kafka/certs/ca-chain.crt"
          cert_file: "/etc/kafka/certs/dci-flowdata.client.crt"
          key_file: "/etc/kafka/certs/dci-flowdata.client.key"
        sasl:
          enabled: true
          mechanism: "PLAIN"
          username: "dci-flowdata"
          # 密码将从环境变量 KAFKA_SASL_PASSWORD 中读取
          password: ""
    
    # Prometheus配置
    prometheus:
      # Prometheus服务器地址
      server: "prometheus.dci.svc.cluster.local:9090"
      # 指标路径
      metrics_path: "/metrics"
      # 指标端口
      metrics_port: 9090
     
    # 数据库配置 - 生产环境数据库
    database:
      driver: "mysql"
      # 数据库主机
      host: "rm-el54b22i28k3pcmt8.mysql.rds.pcloud-ops.citic.com"
      # 数据库端口
      port: 3306
      # 数据库用户名
      username: "dcimonitor"
      # 数据库密码
      password: "ovH$7A3xUdYP"
      # 数据库名称
      database: "dci"
      # 字符集
      charset: "utf8mb4"
      # 最大连接数
      max_open_conns: 10
      # 最大空闲连接数
      max_idle_conns: 5
      # 连接最大生命周期（秒）
      conn_max_lifetime: 3600
    
    # 日志配置
    logger:
      # 日志级别: debug, info, warn, error - 生产环境使用info
      level: "info"
      # 日志编码格式: json, console
      encoding: "json"
      # 日志目录
      dir: "/var/log/dcimonitor-snmpstatus"
      # 单个日志文件最大大小(MB)
      maxSize: 100
      # 最大日志文件保留天数
      maxAge: 30
      # 是否压缩旧日志文件
      compress: true
    
    # Mapper配置 端口映射查询Mysql缓存TTL，若空缺则默认5min
    mapper:
      cache_ttl_minutes: 30
