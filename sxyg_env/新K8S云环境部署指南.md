
# DCI监测系统新K8s云环境部署适配教程

## 概述

本教程基于DCI监测系统在生产环境(prod_env)和首信云港环境(sxyg_env)的部署经验，指导如何将系统适配到新的K8s云环境中。DCI监测系统包含以下核心组件：

- **dcimonitor**: 主监控服务
- **dcimonitor-snmpstatus**: SNMP状态处理服务  
- **kafka**: 消息队列集群
- **prometheus**: 监控告警系统
- **mysql**: 数据库(可选外部或内部)
- **nfs_dci**: 网络存储支持

## 第一阶段：环境信息收集

### 1.1 K8s集群基础信息

在开始部署前，需要收集以下关键信息：

```bash
# 1. 集群版本信息
kubectl version --short

# 2. 节点信息和标签
kubectl get nodes -o wide --show-labels

# 3. 存储类信息
kubectl get storageclass

# 4. 网络策略和服务类型支持
kubectl get svc --all-namespaces

# 5. 检查是否有污点限制
kubectl describe nodes | grep -i taint
```

**记录清单**：
- [ ] K8s版本：_____________
- [ ] 节点IP地址段：_____________  
- [ ] 是否存在控制平面污点：Y/N
- [ ] 默认存储类：_____________
- [ ] 支持的服务类型：ClusterIP/NodePort/LoadBalancer

### 1.2 网络规划信息

```bash
# 外部访问地址规划
EXTERNAL_KAFKA_HOST="<kafka外部访问域名或IP>"     # 例如：kafka.yourdomain.com 或 10.x.x.x
KAFKA_NODEPORT_BASE=30010                        # Kafka外部端口基础值(30010-30012)

# 内部服务发现地址
INTERNAL_MYSQL_HOST="mysql.dci.svc.cluster.local"           # 内部MySQL或外部地址
INTERNAL_PROMETHEUS_HOST="prometheus.dci.svc.cluster.local" # Prometheus地址
```

**记录清单**：
- [ ] 外部访问入口IP/域名：_____________
- [ ] 可用的NodePort端口范围：_____________
- [ ] 是否使用外部数据库：Y/N，地址：_____________
- [ ] DNS解析是否正常：Y/N

### 1.3 存储配置信息

```bash
# NFS存储信息(如果使用NFS)
NFS_SERVER_IP="<NFS服务器IP>"
NFS_MOUNT_PATH="<NFS挂载路径>"

# 示例：
# NFS_SERVER_IP="**********"  
# NFS_MOUNT_PATH="/share/nfs4/data"
```

**记录清单**：
- [ ] NFS服务器地址：_____________
- [ ] NFS挂载路径：_____________
- [ ] 存储容量规划：_____________

### 1.4 镜像仓库信息

```bash
# 镜像仓库配置
REGISTRY_HOST="<镜像仓库地址>"
REGISTRY_USERNAME="<用户名>"  
REGISTRY_PASSWORD="<密码>"

# 检查镜像仓库连通性
docker login $REGISTRY_HOST -u $REGISTRY_USERNAME -p $REGISTRY_PASSWORD
```

**记录清单**：
- [ ] 镜像仓库地址：_____________
- [ ] 认证信息是否配置：Y/N
- [ ] 是否需要跳过SSL验证：Y/N

## 第二阶段：基础环境适配

### 2.1 命名空间和认证配置

```bash
# 1. 创建命名空间
kubectl create namespace dci

# 2. 配置镜像拉取凭证
kubectl create secret docker-registry dci-images-key \
  --docker-server=$REGISTRY_HOST \
  --docker-username=$REGISTRY_USERNAME \
  --docker-password=$REGISTRY_PASSWORD \
  -n dci
```

### 2.2 存储配置适配

#### 方案A：使用NFS存储

```yaml
# nfs_dci/class.yaml - 需要适配NFS服务器地址
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: dci-nfs-storage
  namespace: dci
provisioner: dci-nfs-storage
parameters:
  archiveOnDelete: "false"
```

```yaml
# nfs_dci/deployment.yaml - 适配要点
spec:
  template:
    spec:
      # 根据环境添加污点容忍(如首信云港环境)
      tolerations:
      - key: "node-role.kubernetes.io/control-plane"
        operator: "Exists"
        effect: "NoSchedule"  # 如果集群所有节点都有控制平面污点
      
      containers:
      - name: nfs-client-provisioner
        image: ${REGISTRY_HOST}/nfs-subdir-external-provisioner:v4.0.2
        env:
        - name: NFS_SERVER
          value: "${NFS_SERVER_IP}"      # 适配实际NFS服务器IP
        - name: NFS_PATH  
          value: "${NFS_MOUNT_PATH}"     # 适配实际挂载路径
```

#### 方案B：使用云存储

根据云厂商提供的存储类进行配置，修改所有PVC的`storageClassName`。

### 2.3 容器运行时适配

#### containerd环境配置(如首信云港)

```bash
# 编辑 /etc/containerd/config.toml (在每个节点上)
[plugins."io.containerd.grpc.v1.cri".registry.mirrors."${REGISTRY_HOST}"]
  endpoint=["http://${REGISTRY_HOST}"]

[plugins."io.containerd.grpc.v1.cri".registry.configs."${REGISTRY_HOST}".tls]
  insecure_skip_verify = true

# 重启containerd
systemctl restart containerd
```

## 第三阶段：核心组件配置适配

### 3.1 Kafka集群适配

**关键适配点**：

```yaml
# kafka/kafka.yaml 主要修改项
spec:
  template:
    spec:
      # 1. 污点容忍配置(根据环境决定是否添加)
      tolerations:
      - key: "node-role.kubernetes.io/control-plane"
        operator: "Exists"
        effect: "NoSchedule"
      
      containers:
      - name: kafka
        # 2. 镜像地址适配
        image: ${REGISTRY_HOST}/kafka:4.0.0
        
        env:
        # 3. 外部访问地址适配
        - name: EXTERNAL_ADVERTISED_HOST
          value: "${EXTERNAL_KAFKA_HOST}"  # 替换为实际外部访问地址
```

**广播地址配置函数适配**：

```bash
# 在kafka.yaml的启动脚本中修改
get_external_advertised_address() {
    local BASE_PORT=30010
    local ORDINAL_NUM=$(echo ${ORDINAL} | sed 's/[^0-9]//g')
    local EXTERNAL_NODE_PORT=$((BASE_PORT + ORDINAL_NUM))
    
    # 适配实际外部地址
    echo "${EXTERNAL_KAFKA_HOST}:${EXTERNAL_NODE_PORT}"
    return 0
}
```

### 3.2 DCIMonitor服务适配

**配置文件适配**：

```yaml
# dcimonitor/dcimonitor-config.yaml
data:
  config.yaml: |
    database:
      driver: mysql
      host: "${MYSQL_HOST}"                    # 适配数据库地址
      port: 3306
      username: root
      password: "${MYSQL_PASSWORD}"            # 适配数据库密码
      dbname: dci_monitor
      charset: utf8mb4

    kafka:
      brokers:
        - "kafka-0.kafka-headless.dci.svc.cluster.local:30002"
        - "kafka-1.kafka-headless.dci.svc.cluster.local:30002"  
        - "kafka-2.kafka-headless.dci.svc.cluster.local:30002"
      # SSL和SASL配置保持不变，证书路径统一

    prometheus:
      address: "http://prometheus.dci.svc.cluster.local:9090"
      username: "dciadmin"
      password: "${PROMETHEUS_PASSWORD}"        # 适配Prometheus密码
```

**部署配置适配**：

```yaml
# dcimonitor/dcimonitor-k8s.yaml
spec:
  template:
    spec:
      # 污点容忍配置(根据环境)
      tolerations:
      - key: "node-role.kubernetes.io/control-plane"
        operator: "Exists"
        effect: "NoSchedule"
      
      containers:
      - name: dcimonitor
        # 镜像地址适配
        image: ${REGISTRY_HOST}/dcimonitor:dcimonitor-0.1.1
```

### 3.3 SNMP状态处理服务适配

```yaml
# dcimonitor-snmpstatus/dcimonitor-snmpstatus-k8s.yaml
spec:
  template:
    spec:
      # 同样的污点容忍和镜像适配
      tolerations:
      - key: "node-role.kubernetes.io/control-plane"
        operator: "Exists"
        effect: "NoSchedule"
      
      containers:
      - name: dcimonitor-snmpstatus
        image: ${REGISTRY_HOST}/dcimonitor-snmpstatus:dcimonitor-snmpstatus-0.2.0
```

## 第四阶段：部署执行步骤

### 4.1 按顺序部署基础组件

```bash
# 1. 部署存储支持
kubectl apply -f nfs_dci/namespace.yaml              # 如果命名空间还未创建
kubectl apply -f nfs_dci/docker-secret.yaml -n dci   # 镜像拉取凭证
kubectl apply -f nfs_dci/rbac.yaml -n dci
kubectl apply -f nfs_dci/class.yaml
kubectl apply -f nfs_dci/deployment.yaml -n dci

# 等待NFS Provisioner运行正常
kubectl get pods -n dci -l app=nfs-client-provisioner

# 2. 部署MySQL(如果使用内部MySQL)
kubectl apply -f mysql/ -n dci

# 3. 部署Kafka集群
kubectl apply -f kafka/kafka-service.yaml -n dci
kubectl apply -f kafka/kafka.yaml -n dci

# 等待Kafka集群启动完成
kubectl get pods -n dci -l app=kafka -w
```

### 4.2 部署应用服务

```bash
# 4. 部署Prometheus监控
kubectl apply -f prometheus/ -n dci

# 5. 部署DCIMonitor主服务
kubectl apply -f dcimonitor/dcimonitor-config.yaml -n dci
kubectl apply -f dcimonitor/dcimonitor-k8s.yaml -n dci

# 6. 部署SNMP状态处理服务
kubectl apply -f dcimonitor-snmpstatus/dcimonitor-snmpstatus-config.yaml -n dci
kubectl apply -f dcimonitor-snmpstatus/dcimonitor-snmpstatus-k8s.yaml -n dci
```

### 4.3 验证部署状态

```bash
# 检查所有Pod状态
kubectl get pods -n dci -o wide

# 检查服务状态  
kubectl get svc -n dci

# 检查存储卷
kubectl get pvc -n dci

# 检查关键日志
kubectl logs -f deployment/dcimonitor -n dci
kubectl logs -f kafka-0 -n dci
```

## 第五阶段：常见问题和解决方案

### 5.1 污点和容忍问题

**问题症状**：Pod一直处于Pending状态
```bash
# 诊断命令
kubectl describe pod <pod-name> -n dci
kubectl get nodes -o json | jq '.items[].spec.taints'
```

**解决方案**：在deployment中添加相应的容忍配置
```yaml
tolerations:
- key: "node-role.kubernetes.io/control-plane"
  operator: "Exists"
  effect: "NoSchedule"
- key: "node-role.kubernetes.io/master"  # 旧版本K8s
  operator: "Exists"
  effect: "NoSchedule"
```

### 5.2 镜像拉取问题

**问题症状**：ImagePullBackOff错误
```bash
# 诊断命令
kubectl describe pod <pod-name> -n dci
kubectl get events -n dci --sort-by='.lastTimestamp'
```

**解决方案**：
1. 确认镜像拉取凭证正确配置
2. 检查镜像地址是否正确
3. 配置容器运行时跳过SSL验证(如需要)

### 5.3 存储卷绑定问题

**问题症状**：PVC处于Pending状态
```bash
# 诊断命令
kubectl describe pvc <pvc-name> -n dci
kubectl get storageclass
```

**解决方案**：
1. 确认存储类配置正确
2. 检查NFS服务器连通性
3. 验证NFS Client Provisioner运行状态

### 5.4 网络连接问题

**问题症状**：服务间无法通信
```bash
# 诊断命令
kubectl exec -it <pod-name> -n dci -- nslookup kafka-headless.dci.svc.cluster.local
kubectl exec -it <pod-name> -n dci -- telnet kafka-0.kafka-headless.dci.svc.cluster.local 30002
```

**解决方案**：
1. 检查DNS解析配置
2. 验证服务端口配置
3. 检查网络策略限制

### 5.5 Kafka外部访问问题

**问题症状**：外部客户端无法连接Kafka
```bash
# 诊断命令
nc -zv ${EXTERNAL_KAFKA_HOST} 30010
kubectl logs kafka-0 -n dci | grep "advertised.listeners"
```

**解决方案**：
1. 确认外部地址配置正确
2. 检查NodePort服务配置
3. 验证防火墙和网络策略

## 第六阶段：性能调优建议

### 6.1 资源配置优化

```yaml
# 生产环境资源建议
resources:
  requests:
    cpu: "200m"
    memory: "1Gi"
  limits:
    cpu: "2"
    memory: "4Gi"
```

### 6.2 副本和容灾配置

```yaml
# 高可用配置
spec:
  replicas: 2  # 生产环境增加副本数
  
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
            - key: app
              operator: In
              values:
              - dcimonitor
          topologyKey: "kubernetes.io/hostname"
```

## 检查清单

部署完成后，请确认以下项目：

- [ ] 所有Pod运行状态正常
- [ ] 存储卷成功绑定
- [ ] 服务间网络通信正常
- [ ] Kafka Topic创建成功
- [ ] 外部访问地址可达
- [ ] 监控指标正常采集
- [ ] 日志输出正常
- [ ] 数据库连接正常
- [ ] 证书和认证配置有效

---
*本教程基于DCI监测系统在生产环境和首信云港环境的成功部署经验总结，涵盖了主要的适配要点和常见问题解决方案。*