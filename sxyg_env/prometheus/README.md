# Prometheus 部署说明 - 首信云岗环境

本文档描述了如何在首信云岗K8S环境中部署Prometheus监控系统。

## 部署文件清单

- `prometheus-rbac.yaml`: ServiceAccount, ClusterRole, ClusterRoleBinding
- `prometheus-configmap.yaml`: Prometheus主配置文件
- `prometheus-rules-configmap.yaml`: 告警和记录规则
- `prometheus-web-config.yaml`: Web UI认证配置
- `prometheus-basic-auth-secret.yaml`: Prometheus访问密钥
- `prometheus-k8s.yaml`: Prometheus StatefulSet
- `prometheus-service.yaml`: Prometheus Service (ClusterIP and NodePort)

## 环境要求

- Kubernetes v1.28+
- NFS存储类 `dci-nfs-storage` 已可用（参见 `sxyg_env/nfs_dci/` 目录的部署）
- 命名空间 `dci` 已创建
- 镜像拉取密钥 `dci-images-key` 已创建并配置在 `dci` 命名空间
- `prometheus:v3.3.1` 和 `busybox:latest` 镜像已推送到 `**********/zhongxinsdn/` 仓库

## 关键配置修改

为适配首信云岗环境，已执行以下修改：

1. **`prometheus-k8s.yaml`**:
   - 镜像地址已更新为指向私有仓库 `**********/zhongxinsdn/`
   - 添加了对 `node-role.kubernetes.io/control-plane` 污点的容忍
   - 添加了 `fsGroup: 65534` 以匹配容器用户组，确保NFS卷权限正确
   - 配置了存储使用 `dci-nfs-storage` StorageClass

2. **`prometheus-configmap.yaml`**:
   - 启用了 `remote_write` 配置，指向集群内的Prometheus服务
   - 保留了Kubernetes服务发现配置

## 部署步骤

请先确保已部署NFS存储（见 `sxyg_env/nfs_dci/README.md`），然后按以下顺序在`dci`命名空间中应用配置文件：

```bash
# 0. 确保命名空间和存储已创建
kubectl apply -f sxyg_env/nfs_dci/namespace.yaml
kubectl apply -f sxyg_env/nfs_dci/docker-secret.yaml

# 1. 应用RBAC权限
kubectl apply -f sxyg_env/prometheus/prometheus-rbac.yaml

# 2. 应用所有配置
kubectl apply -f sxyg_env/prometheus/prometheus-configmap.yaml
kubectl apply -f sxyg_env/prometheus/prometheus-rules-configmap.yaml
kubectl apply -f sxyg_env/prometheus/prometheus-web-config.yaml
kubectl apply -f sxyg_env/prometheus/prometheus-basic-auth-secret.yaml

# 3. 部署Prometheus StatefulSet
kubectl apply -f sxyg_env/prometheus/prometheus-k8s.yaml

# 4. 暴露Prometheus服务
kubectl apply -f sxyg_env/prometheus/prometheus-service.yaml
```

## 验证部署

部署完成后，使用以下命令检查状态：

```bash
# 检查Pod是否正常运行 (状态应为 Running)
kubectl get pods -n dci -l app=prometheus -w

# 检查服务是否已创建
kubectl get svc -n dci -l app=prometheus

# 检查持久卷申领是否已绑定 (状态应为 Bound)
kubectl get pvc -n dci -l app=prometheus
```

## 访问Prometheus UI

部署完成后，可以通过`NodePort`服务从外部访问Prometheus UI。

- **NodePort**: `30006`
- **访问地址**: `http://<任意K8S节点IP>:30006`
  - 可以使用：**********, ********** 或 **********
- **登录凭证**:
  - 用户名: `dciadmin`
  - 密码: `SHOUXINYUNGANG@k8s123` (在`prometheus-web-config.yaml`中定义)

## 维护

### 重新加载配置
如果修改了`prometheus-configmap.yaml`或`prometheus-rules-configmap.yaml`，可以通过以下命令热加载配置，无需重启Pod：

```bash
# 1. 应用修改后的ConfigMap
kubectl apply -f sxyg_env/prometheus/prometheus-configmap.yaml

# 2. 触发重载
kubectl exec -it prometheus-0 -n dci -- /bin/sh -c "kill -HUP 1"
```

### 查看日志
```bash
# 查看Prometheus日志
kubectl logs -f prometheus-0 -n dci
```

### 数据持久化
Prometheus数据存储在NFS服务器(**********)的以下路径：
```
/share/nfs4/data/dci-nfs-storage-prometheus-data-prometheus-0-pvc-*
```

## 创建密码的命令

htpasswd -nBC 12 dciadmin SHOUXINYUNGANG@k8s123

