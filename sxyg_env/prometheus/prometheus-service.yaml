apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: dci
  labels:
    app: prometheus
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
spec:
  type: ClusterIP
  ports:
  - name: web
    port: 9090
    targetPort: web
    protocol: TCP
  selector:
    app: prometheus
---
# 添加NodePort服务，用于开发和测试阶段访问Prometheus UI
apiVersion: v1
kind: Service
metadata:
  name: prometheus-nodeport
  namespace: dci
  labels:
    app: prometheus
spec:
  type: NodePort
  ports:
  - name: web
    port: 9090
    targetPort: web
    nodePort: 30006
    protocol: TCP
  selector:
    app: prometheus 