---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: nfs-client-provisioner
  namespace: dci  # 命名空间修改
---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: nfs-client-provisioner
  namespace: dci  # 命名空间
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: nfs-client-provisioner
  template:
    metadata:
      labels:
        app: nfs-client-provisioner
    spec:
      serviceAccount: nfs-client-provisioner
      securityContext:
        runAsUser: 0  # 以 root 用户运行
        fsGroup: 0
      tolerations:
      - key: "node-role.kubernetes.io/control-plane"
        operator: "Exists"
      containers:
        - name: nfs-client-provisioner
          image: 10.7.22.20/zhongxinsdn/nfs-subdir-external-provisioner:v4.0.2
          resources:
            limits:
              cpu: "200m"
              memory: "256Mi"
          volumeMounts:
            - name: nfs-client-root
              mountPath: /mnt 
          env:
            - name: PROVISIONER_NAME
              value: dci-nfs-storage  # 与StorageClass一致
            - name: NFS_SERVER
              value: 10.7.22.20    # 更新为生产环境NFS服务器
            - name: NFS_PATH
              value: /share/nfs4/data  # 更新为生产环境挂载路径
      imagePullSecrets:
        - name: dci-images-key
      volumes:
        - name: nfs-client-root
          nfs:
            server: 10.7.22.20     # 更新为生产环境NFS服务器
            path: /share/nfs4/data  # 更新为生产环境挂载路径
