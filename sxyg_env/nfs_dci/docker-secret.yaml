apiVersion: v1
kind: Secret
metadata:
  name: dci-images-key
  namespace: dci
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ****************************************************************************************************************************************************************
# 上面的data是通过以下命令生成的：
# kubectl create secret docker-registry dci-images-key --docker-server=10.7.22.20 --docker-username=zhongxink8s --docker-password=Z<PERSON><PERSON>@k8s -n dci --dry-run=client -o yaml 