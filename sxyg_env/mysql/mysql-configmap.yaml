---
apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-config
  namespace: dci
data:
  my.cnf: |
    [mysqld]
    character-set-server=utf8mb4
    collation-server=utf8mb4_unicode_ci
    default-time-zone='+8:00'
    max_connections=1000
    
    # 优化配置
    innodb_buffer_pool_size=1G
    innodb_log_file_size=256M
    innodb_flush_log_at_trx_commit=2
    
    # 日志配置
    slow_query_log=1
    slow_query_log_file=/var/log/mysql/mysql-slow.log
    long_query_time=2
    
    # 允许远程连接
    bind-address=0.0.0.0
    
    [client]
    default-character-set=utf8mb4
    
  init.sql: |
    CREATE DATABASE IF NOT EXISTS dci CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    USE dci; 