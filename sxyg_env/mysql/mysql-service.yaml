---
apiVersion: v1
kind: Service
metadata:
  name: mysql
  namespace: dci
  labels:
    app: mysql
spec:
  selector:
    app: mysql
  type: ClusterIP
  ports:
  - protocol: TCP
    port: 3306
    targetPort: 3306
    name: mysql

---
# Headless Service用于StatefulSet
apiVersion: v1
kind: Service
metadata:
  name: mysql-headless
  namespace: dci
  labels:
    app: mysql
    environment: production
spec:
  clusterIP: None
  selector:
    app: mysql
  ports:
  - port: 3306
    targetPort: 3306
    name: mysql 