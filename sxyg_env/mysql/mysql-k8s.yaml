---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mysql
  namespace: dci
  labels:
    app: mysql
    environment: production
spec:
  serviceName: mysql-headless
  replicas: 1 # 单节点MySQL
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
        environment: production
    spec:
      # 添加控制平面污点容忍
      tolerations:
      - key: "node-role.kubernetes.io/control-plane"
        operator: "Exists"
      imagePullSecrets:
        - name: dci-images-key
      securityContext:
        fsGroup: 999 # mysql用户组ID
      containers:
      - name: mysql
        image: 10.7.22.20/zhongxinsdn/mysql:5.7.42
        imagePullPolicy: IfNotPresent
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mysql-credentials
              key: mysql-root-password
        - name: MYSQL_DATABASE
          value: "dci"
        - name: TZ
          value: "Asia/Shanghai"
        ports:
        - containerPort: 3306
          name: mysql
        volumeMounts:
        - name: mysql-data
          mountPath: /var/lib/mysql
        - name: mysql-config
          mountPath: /etc/mysql/conf.d/custom.cnf
          subPath: my.cnf
        - name: mysql-init
          mountPath: /docker-entrypoint-initdb.d/init.sql
          subPath: init.sql
        - name: mysql-logs
          mountPath: /var/log/mysql
        readinessProbe:
          exec:
            command:
            - bash
            - -c
            - "mysql -uroot -p${MYSQL_ROOT_PASSWORD} -e 'SELECT 1'"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        livenessProbe:
          exec:
            command:
            - bash
            - -c
            - "mysqladmin ping -uroot -p${MYSQL_ROOT_PASSWORD}"
          initialDelaySeconds: 120
          periodSeconds: 20
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            cpu: "1000m"
            memory: "2Gi"
          limits:
            cpu: "4000m"
            memory: "4Gi"
      volumes:
      - name: mysql-config
        configMap:
          name: mysql-config
          items:
          - key: my.cnf
            path: my.cnf
      - name: mysql-init
        configMap:
          name: mysql-config
          items:
          - key: init.sql
            path: init.sql
  volumeClaimTemplates:
  - metadata:
      name: mysql-data
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: dci-nfs-storage
      resources:
        requests:
          storage: 500Gi
  - metadata:
      name: mysql-logs
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: dci-nfs-storage
      resources:
        requests:
          storage: 5Gi 