# MySQL在首信云岗环境部署说明

本文档描述了如何在首信云岗K8S环境中部署MySQL服务，用于支持dcimonitor服务。

## 部署文件说明

1. `mysql-secret.yaml`: MySQL密码等敏感信息
2. `mysql-configmap.yaml`: MySQL配置和初始化SQL
3. `mysql-statefulset.yaml`: MySQL StatefulSet定义
4. `mysql-service.yaml`: MySQL服务定义（包含普通Service和Headless Service）
5. `dcimonitor-config.yaml`: 已更新为使用内部MySQL服务

## 环境要求

- 已成功部署NFS Client Provisioner（使用dci-nfs-storage存储类）
- 已创建命名空间dci
- 已创建镜像拉取密钥dci-images-key

## 部署步骤

按照以下顺序执行命令：

```bash
# 1. 创建MySQL配置和密钥
kubectl apply -f mysql-secret.yaml -n dci
kubectl apply -f mysql-configmap.yaml -n dci

# 2. 创建MySQL服务
kubectl apply -f mysql-service.yaml -n dci

# 3. 部署MySQL StatefulSet
kubectl apply -f mysql-statefulset.yaml -n dci

# 4. 等待MySQL Pod就绪
kubectl wait --for=condition=Ready pod/mysql-0 -n dci --timeout=300s

# 5. 更新dcimonitor配置
kubectl apply -f dcimonitor-config.yaml -n dci

# 6. 部署dcimonitor服务
kubectl apply -f dcimonitor-service.yaml -n dci
kubectl apply -f dcimonitor-k8s.yaml -n dci
```

## 验证部署

### 检查MySQL服务状态

```bash
# 检查MySQL Pod状态
kubectl get pods -n dci -l app=mysql

# 检查MySQL服务
kubectl get svc -n dci -l app=mysql

# 检查MySQL持久卷
kubectl get pvc -n dci | grep mysql
```

### 验证数据库连接

```bash
# 进入MySQL Pod
kubectl exec -it mysql-0 -n dci -- bash

# 连接MySQL
mysql -uroot -pSHOUXINYUNGANG@k8s123

# 检查数据库
mysql> SHOW DATABASES;
mysql> USE dci;
mysql> SHOW TABLES;
```

### 配置dcimonitor使用MySQL

dcimonitor-config.yaml已更新为使用内部MySQL服务。配置包括：

```yaml
database:
  driver: mysql
  host: "mysql.dci.svc.cluster.local"
  port: 3306
  username: root
  password: SHOUXINYUNGANG@k8s123
  dbname: dci
  charset: utf8mb4
```

## 维护说明

### 数据备份

执行以下命令创建MySQL数据库备份：

```bash
kubectl exec mysql-0 -n dci -- mysqldump -uroot -pSHOUXINYUNGANG@k8s123 --all-databases > backup_$(date +%Y%m%d).sql
```

### 恢复数据

将备份SQL文件复制到Pod中并执行恢复：

```bash
kubectl cp backup.sql dci/mysql-0:/tmp/
kubectl exec -it mysql-0 -n dci -- mysql -uroot -pSHOUXINYUNGANG@k8s123 < /tmp/backup.sql
```

### 扩展存储

如需扩展存储空间，请编辑PVC资源：

```bash
kubectl edit pvc mysql-data-mysql-0 -n dci
# 修改 spec.resources.requests.storage 的值
``` 