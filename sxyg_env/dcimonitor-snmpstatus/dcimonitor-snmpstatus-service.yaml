---
apiVersion: v1
kind: Service
metadata:
  name: dcimonitor-snmpstatus-service
  namespace: dci
  labels:
    app: dcimonitor-snmpstatus
    environment: production
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
    prometheus.io/path: "/metrics"
spec:
  selector:
    app: dcimonitor-snmpstatus
  ports:
  - protocol: TCP
    port: 9090
    targetPort: 9090
    name: metrics

---
apiVersion: v1
kind: Service
metadata:
  name: dcimonitor-snmpstatus-headless
  namespace: dci
  labels:
    app: dcimonitor-snmpstatus
    environment: production
spec:
  clusterIP: None
  selector:
    app: dcimonitor-snmpstatus
  ports:
  - port: 9090
    targetPort: 9090 