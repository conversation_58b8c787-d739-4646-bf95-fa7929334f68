---
apiVersion: v1
kind: Secret
metadata:
  name: dcimonitor-snmpstatus-sasl-secret
  namespace: dci
type: Opaque
stringData:
  # Production environment flow data client password
  password: "LwHbvANbJx1oS+RwEzvmrwMqgQOB6MxN"

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: dcimonitor-snmpstatus
  namespace: dci
  labels:
    app: dcimonitor-snmpstatus
    environment: production
spec:
  # Production environment uses StatefulSet with Headless Service
  serviceName: dcimonitor-snmpstatus-headless
  replicas: 1
  selector:
    matchLabels:
      app: dcimonitor-snmpstatus
  template:
    metadata:
      labels:
        app: dcimonitor-snmpstatus
        environment: production
    spec:
      # Add control-plane taint toleration
      tolerations:
      - key: "node-role.kubernetes.io/control-plane"
        operator: "Exists"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - dcimonitor-snmpstatus
              topologyKey: "kubernetes.io/hostname"
      imagePullSecrets:
        - name: dci-images-key
      containers:
      - name: dcimonitor-snmpstatus
        image: **********/zhongxinsdn/dcimonitor-snmpstatus:dcimonitor-snmpstatus-0.2.0
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 9090
          name: metrics
        env:
        - name: TZ
          value: "Asia/Shanghai"
        - name: KAFKA_SASL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dcimonitor-snmpstatus-sasl-secret
              key: password
        volumeMounts:
        - name: config-volume
          mountPath: /app/config  # Mount config to /app/config
        - name: log-volume
          mountPath: /var/log/dcimonitor-snmpstatus
        - name: kafka-client-certs-volume
          mountPath: "/etc/kafka/certs"
          readOnly: true
        command: ["/app/dcimonitor-snmpstatus", "--config", "/app/config/dcimonitor-snmpstatus.yaml"] # Specify config file path
        readinessProbe:
          httpGet:
            path: /metrics
            port: 9090
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /metrics
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 20
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            # Production environment resource requests
            cpu: "100m"
            memory: "256Mi"
          limits:
            cpu: "2000m"
            memory: "4Gi"
      volumes:
      - name: config-volume
        configMap:
          name: dcimonitor-snmpstatus-config
      - name: kafka-client-certs-volume
        secret:
          secretName: kafka-client-certs
  # Use volumeClaimTemplates instead of separate PVC
  volumeClaimTemplates:
  - metadata:
      name: log-volume
    spec:
      accessModes:
        - ReadWriteOnce
      storageClassName: dci-nfs-storage
      resources:
        requests:
          storage: 8Gi 