# Prod Env

DCI 首信云岗环境 K8S 配置文件

## K8S环境相关信息

### 集群登录

跳板机：************** 端口2222 ，通过ssh工具登入
用户名：zhongxinsdn
密码：zyscu9-howXyv-rocxaf

```bash
ssh zhongxinsdn@************** -p 2222
```

### K8S集群节点

三个节点网络IP：

```bash
**********
**********
**********
```

三个节点IP直接可以访问、与各客户端、终端互联。

无负载均衡。

存储：5T （**********:/share/nfs4/data）

[需额外添加]容忍配置：
```bash
spec:
      # 添加控制平面污点容忍
      tolerations:
      - key: "node-role.kubernetes.io/control-plane"
        operator: "Exists"
```

### 镜像仓库地址

私有仓库地址：

```bash
 https://**************:8443 #（仅web管理，无法用于docker push传输）
 用户名： zhongxink8s
 密码： Zhongxin@k8s
```

点击 `zhongxinsdn` 查看当前项目镜像

docker 登录、PUSH镜像 （可以用docker push），需要先登录OPENVPN，见：[OPENVPN配置](TAIJ-01-DJYUN_zhongxin_BJ-TAIJ-01-DJYUN-VPN.ovpn)

示例：
```bash
docker login http://********** -u zhongxink8s -p Zhongxin@k8s
docker tag abc:1.0.0 **********/zhongxinsdn/abc:1.0.0
docker push **********/zhongxinsdn/abc:1.0.0
```

登录 VPN 后私有镜像仓库地址为：**********

### 数据库相关 

MySQL 数据库
用户名：root
密码：SHOUXINJ0GBPKZva1w 

prometheus 数据库
用户名：prometheus
密码：SHOUXINJ0GBPKZva1w

### kafka 

kafka与订阅客户端之间的出入口IP即为节点IP（**********、**********、**********）

kafka 证书认证信息（kafka_credentials.txt）：
```
# DCI Kafka Client Credentials
# 这个文件由 generate_certs.sh 自动生成，请妥善保管。
# 它包含了用于 SASL/PLAIN 认证的用户名和密码。

DCI_KAFKA_ADMIN_PASSWORD=zZgvSR/5NM8ekiO/wziqSRI0SJnmJIK1
DCI_TELEGRAF_PASSWORD=leEp7pTI5HpGG7KTJzIY9dzQsGDpaZhD
DCI_FLOWDATA_PASSWORD=LwHbvANbJx1oS+RwEzvmrwMqgQOB6MxN
```

客户端连接配置示例：
```bash
# 通过30010端口连接kafka-0
kafka-console-producer.sh --bootstrap-server **********:30010 \
  --topic test \
  --producer.config client.properties

# client.properties文件内容：
security.protocol=SASL_SSL
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="dci-telegraf" password="leEp7pTI5HpGG7KTJzIY9dzQsGDpaZhD";
ssl.truststore.location=ca-chain.crt
ssl.truststore.type=PEM
ssl.endpoint.identification.algorithm=
```

