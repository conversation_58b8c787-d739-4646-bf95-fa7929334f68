# Prod Env

DCI 首信云岗环境 K8S 配置文件

## 生产环境相关信息

### 生产环境K8S集群信息

跳板机：117.128.34.150 端口2222 ，通过ssh工具登入
用户名：zhongxinsdn
密码：zyscu9-howXyv-rocxaf

ssh zhongxinsdn@117.128.34.150 -p 2222

```bash
**********
**********
**********
```

### NSF存储挂载点

**********:/share/nfs4/data        4.9T   35G  4.9T   1% /data1

### 生产环境SLB地址

### K8S集群信息

```bash
root@bj-taij-01-sdn-k8s-master-s01:~# kubectl version
Client Version: v1.28.2
Kustomize Version: v5.0.4-0.20230601165947-6ce0bf390ce3
Server Version: v1.28.2
```

### 镜像仓库地址

私有仓库https://117.128.34.150:8443 （仅web管理，无法用于docker push传输）用户名： zhongxink8s ，密码：Zhongxin@k8s

docker 登录 （可以用docker push）：
docker login http://********** -u zhongxink8s -p Zhongxin@k8s

### 镜像管理工具

没有docker命令，有 containerd 命令

## NFS存储配置部署说明

在首信云岗环境中成功部署NFS Client Provisioner的关键经验总结：

### 环境特点与解决方案

1. **控制平面节点调度问题**
   - 环境中所有节点都有污点`node-role.kubernetes.io/control-plane`
   - **解决方案**：在deployment.yaml中添加污点容忍配置
   ```yaml
   tolerations:
   - key: "node-role.kubernetes.io/control-plane"
     operator: "Exists"
   ```

2. **镜像仓库SSL证书问题**
   - 错误：`failed to verify certificate: x509: cannot validate certificate for ********** because it doesn't contain any IP SANs`
   - **解决方案**：在containerd配置中使用HTTP协议并跳过SSL验证

3. **镜像仓库认证问题**
   - 错误：`authorization failed: no basic auth credentials`
   - **解决方案**：使用`imagePullSecrets`引用Kubernetes Secret

### 配置步骤

#### 1. 修改containerd配置（所有节点）

在每个K8S节点（**********、**********、**********）上编辑`/etc/containerd/config.toml`，添加以下配置：

```toml
# 添加镜像仓库HTTP协议支持
[plugins."io.containerd.grpc.v1.cri".registry.mirrors."**********"]
  endpoint=["http://**********"]

# 跳过SSL证书验证
[plugins."io.containerd.grpc.v1.cri".registry.configs."**********".tls]
  insecure_skip_verify = true
```

修改后重启containerd服务：
```bash
systemctl restart containerd
```

#### 2. 准备部署文件

- `namespace.yaml`: 定义dci命名空间

sxyg_env/nfs_dci/namespace.yaml 是部署所有POD的命名空间，需要先创建

- `docker-secret.yaml`: 配置镜像拉取凭证
  ```yaml
  apiVersion: v1
  kind: Secret
  metadata:
    name: dci-images-key
    namespace: dci
  type: kubernetes.io/dockerconfigjson
  data:
    .dockerconfigjson: ****************************************************************************************************************************************************************
  ```
sxyg_env/nfs_dci/docker-secret.yaml 是部署所有POD的镜像拉取凭证，需要先创建

- `deployment.yaml`: 确保包含污点容忍和imagePullSecrets配置
  ```yaml
  tolerations:
  - key: "node-role.kubernetes.io/control-plane"
    operator: "Exists"
  ...
  imagePullSecrets:
  - name: dci-images-key
  ```

### 部署步骤

按照以下顺序执行部署命令：

```bash
# 1. 创建命名空间
kubectl apply -f namespace.yaml

# 2. 创建Docker镜像拉取密钥
kubectl apply -f docker-secret.yaml -n dci

# 3. 创建RBAC权限
kubectl apply -f rbac.yaml -n dci

# 4. 创建StorageClass
kubectl apply -f class.yaml 

# 5. 部署NFS Client Provisioner
kubectl apply -f deployment.yaml -n dci
```
