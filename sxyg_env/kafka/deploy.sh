#!/bin/bash
#
# Kafka 首信云岗环境部署脚本
#

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}===== DCI Kafka 首信云岗环境部署脚本 =====${NC}"
echo

# 检查kubectl命令
if ! command -v kubectl &> /dev/null; then
  echo -e "${RED}错误: 找不到kubectl命令，请先安装${NC}"
  exit 1
fi

# 检查kubectl连接
echo -e "${BLUE}1. 检查Kubernetes连接${NC}"
if ! kubectl get nodes &> /dev/null; then
  echo -e "${RED}错误: 无法连接到Kubernetes集群，请检查配置${NC}"
  exit 1
fi
echo -e "${GREEN}Kubernetes连接正常${NC}"
echo

# 检查命名空间
echo -e "${BLUE}2. 检查命名空间${NC}"
if ! kubectl get namespace dci &> /dev/null; then
  echo -e "${YELLOW}命名空间'dci'不存在，正在创建...${NC}"
  if ! kubectl apply -f ../nfs_dci/namespace.yaml; then
    echo -e "${RED}错误: 无法创建命名空间'dci'${NC}"
    exit 1
  fi
fi
echo -e "${GREEN}命名空间'dci'已就绪${NC}"
echo

# 检查镜像拉取密钥
echo -e "${BLUE}3. 检查镜像拉取密钥${NC}"
if ! kubectl get secret dci-images-key -n dci &> /dev/null; then
  echo -e "${YELLOW}镜像拉取密钥'dci-images-key'不存在，正在创建...${NC}"
  if ! kubectl apply -f ../nfs_dci/docker-secret.yaml; then
    echo -e "${RED}错误: 无法创建镜像拉取密钥${NC}"
    exit 1
  fi
fi
echo -e "${GREEN}镜像拉取密钥'dci-images-key'已就绪${NC}"
echo

# 检查存储类
echo -e "${BLUE}4. 检查存储类${NC}"
if ! kubectl get storageclass dci-nfs-storage &> /dev/null; then
  echo -e "${YELLOW}存储类'dci-nfs-storage'不存在，正在创建...${NC}"
  
  echo -e "${YELLOW}4.1. 应用RBAC配置${NC}"
  kubectl apply -f ../nfs_dci/rbac.yaml
  
  echo -e "${YELLOW}4.2. 应用存储类配置${NC}"
  kubectl apply -f ../nfs_dci/class.yaml
  
  echo -e "${YELLOW}4.3. 部署NFS Client Provisioner${NC}"
  kubectl apply -f ../nfs_dci/deployment.yaml
  
  # 等待NFS Client Provisioner就绪
  echo -e "${YELLOW}等待NFS Client Provisioner就绪...${NC}"
  kubectl wait --for=condition=available --timeout=120s deployment/nfs-client-provisioner -n dci || echo -e "${YELLOW}警告: NFS Client Provisioner可能尚未就绪${NC}"
fi

if kubectl get storageclass dci-nfs-storage &> /dev/null; then
  echo -e "${GREEN}存储类'dci-nfs-storage'已就绪${NC}"
else
  echo -e "${RED}错误: 存储类'dci-nfs-storage'未就绪，无法继续${NC}"
  exit 1
fi
echo

# 生成证书
echo -e "${BLUE}5. 生成Kafka证书${NC}"
if [ ! -d "./scripts/dci-kafka-certs" ]; then
  echo -e "${YELLOW}正在生成证书...${NC}"
  (cd scripts && ./generate_certs.sh) || { echo -e "${RED}错误: 证书生成失败${NC}"; exit 1; }
fi

if [ -d "./scripts/dci-kafka-certs" ]; then
  echo -e "${GREEN}证书已生成${NC}"
  
  # 应用证书到Kubernetes
  echo -e "${YELLOW}正在应用证书Secrets...${NC}"
  (cd scripts/dci-kafka-certs && ./apply_secrets.sh) || { echo -e "${RED}错误: 证书Secret应用失败${NC}"; exit 1; }
  echo -e "${GREEN}证书Secret已应用${NC}"
else
  echo -e "${RED}错误: 证书目录不存在${NC}"
  exit 1
fi
echo

# 部署Kafka
echo -e "${BLUE}6. 部署Kafka${NC}"
echo -e "${YELLOW}6.1. 部署Kafka配置和StatefulSet${NC}"
kubectl apply -f kafka.yaml || { echo -e "${RED}错误: Kafka配置和StatefulSet部署失败${NC}"; exit 1; }

echo -e "${YELLOW}6.2. 部署Kafka服务${NC}"
kubectl apply -f kafka-service.yaml || { echo -e "${RED}错误: Kafka服务部署失败${NC}"; exit 1; }

# 等待Kafka Pod启动
echo -e "${YELLOW}等待Kafka Pod启动...${NC}"
for i in 0 1 2; do
  kubectl wait --for=condition=ready --timeout=300s pod/kafka-$i -n dci || echo -e "${YELLOW}警告: kafka-$i Pod可能尚未就绪${NC}"
done

# 检查Kafka Pod状态
PODS_READY=true
for i in 0 1 2; do
  if ! kubectl get pod kafka-$i -n dci | grep -q "Running"; then
    PODS_READY=false
    break
  fi
done

if [ "$PODS_READY" = true ]; then
  echo -e "${GREEN}Kafka Pod已就绪${NC}"
else
  echo -e "${YELLOW}警告: 部分Kafka Pod可能尚未就绪，但部署过程将继续${NC}"
fi
echo

# 等待一段时间，让Kafka集群完全启动
echo -e "${YELLOW}等待60秒让Kafka集群完全启动...${NC}"
sleep 60

# 创建Kafka主题
echo -e "${BLUE}7. 创建Kafka主题${NC}"
echo -e "${YELLOW}正在创建默认主题...${NC}"

echo -e "${YELLOW}检查Kafka主题列表...${NC}"
kubectl exec -it kafka-0 -n dci -- /opt/bitnami/kafka/bin/kafka-topics.sh --bootstrap-server localhost:9092 --list || { echo -e "${YELLOW}警告: 无法列出Kafka主题，可能Kafka尚未完全就绪${NC}"; }

echo -e "${GREEN}部署完成！${NC}"
echo
echo -e "您现在可以：${BLUE}kubectl get pods -n dci${NC} 检查Pod状态"
echo -e "使用：${BLUE}kubectl logs kafka-0 -n dci${NC} 查看Kafka日志"
echo -e "使用：${BLUE}kubectl exec -it kafka-0 -n dci -- /opt/bitnami/kafka/bin/kafka-topics.sh --bootstrap-server localhost:9092 --list${NC} 查看主题列表"
echo

exit 0 