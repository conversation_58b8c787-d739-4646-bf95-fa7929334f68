# 首信云港环境Kafka SSL修复部署指南

## 修复内容概述

本次修复解决了Kafka Topic初始化过程中的SSL握手失败问题，主要变更包括：

1. **修改Topic初始化脚本连接端口**：从9092改为30002
2. **添加SASL认证配置**：为初始化脚本提供正确的认证信息
3. **动态密码获取**：从JAAS配置文件中自动提取管理员密码
4. **修复JAAS配置语法**：简化shell转义，避免语法错误

## 端口设计理念说明

### 为什么Topic初始化使用30002端口？

**正确的端口分工**：
- **9092 (REPLICATION)**: 专用于Broker间复制，SSL协议，需要客户端证书
- **30002 (INTERNAL_CLIENT)**: Pod内部客户端连接，SASL_SSL协议，需要用户名密码
- **30003 (EXTERNAL_CLIENT)**: 外部客户端连接，SASL_SSL协议，需要用户名密码
- **30010-30012 (NodePort)**: 通过K8s节点IP对外暴露30003端口

**Topic初始化脚本本质上是客户端操作**，因此使用30002端口是合理的：
- 它运行在Pod内部，属于内部客户端
- 需要执行创建Topic、列出Topic等管理操作
- 9092端口仅用于Broker间的内部复制，不接受客户端连接

## 部署步骤

### 1. 应用修复后的配置

```bash
# 进入kafka配置目录
cd /data/DCI/siem/kafka

# 应用更新后的kafka配置
kubectl apply -f kafka.yaml

# 重启Kafka StatefulSet以加载新配置
kubectl rollout restart statefulset/kafka -n dci
```

### 2. 等待Pod重启完成

```bash
# 监控Pod重启状态
kubectl get pods -n dci -w

# 等待所有kafka pod变为Running状态
# kafka-0, kafka-1, kafka-2 都应该显示 1/1 Running
```

### 3. 验证修复效果

```bash
# 运行验证脚本
cd scripts
./verify_kafka_fix.sh
```

### 4. 检查关键指标

#### 4.1 SSL握手失败错误
```bash
# 检查最近10分钟内是否还有SSL握手失败
kubectl logs kafka-0 -n dci --since=10m | grep "SSL handshake failed" | wc -l
# 应该返回 0
```

#### 4.2 Topic初始化状态
```bash
# 检查初始化是否成功
kubectl logs kafka-0 -n dci | grep "Topic initialization completed successfully"
# 应该看到成功消息
```

#### 4.3 Topic列表
```bash
# 列出所有创建的Topic
kubectl exec -it kafka-0 -n dci -- kafka-topics.sh --bootstrap-server localhost:30002 --command-config /tmp/client.properties --list
```

预期应该看到以下Topic：
- `test`
- `dci.monitor.v1.defaultchannel.topology.lldp`
- `dci.monitor.v1.defaultchannel.metrics.telegraf`
- `dci.monitor.v1.defaultchannel.logs.syslog`
- `dci.monitor.v1.defaultchannel.tasks.control`

## 客户端连接测试

### 1. 创建客户端配置文件

```bash
# 在客户端机器上创建client.properties
cat > client.properties << EOF
security.protocol=SASL_SSL
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="dci-telegraf" password="leEp7pTI5HpGG7KTJzIY9dzQsGDpaZhD";
ssl.truststore.location=ca-chain.crt
ssl.truststore.type=PEM
ssl.endpoint.identification.algorithm=
EOF
```

### 2. 获取CA证书链

```bash
# 从Pod中复制证书文件
kubectl cp dci/kafka-0:/etc/kafka/tls/ca-chain.crt ./ca-chain.crt
```

### 3. 测试生产和消费消息

```bash
# 生产测试消息
kafka-console-producer.sh --bootstrap-server **********:30010 --topic test --producer.config client.properties

# 消费测试消息
kafka-console-consumer.sh --bootstrap-server **********:30010 --topic test --consumer.config client.properties --from-beginning
```

## 故障排除

### 问题1：Pod无法启动
```bash
# 检查Pod状态和事件
kubectl describe pod kafka-0 -n dci
kubectl get events -n dci --sort-by='.lastTimestamp'
```

### 问题2：仍有SSL握手失败
```bash
# 检查配置是否正确应用
kubectl exec -it kafka-0 -n dci -- cat /tmp/client.properties

# 检查JAAS配置文件
kubectl exec -it kafka-0 -n dci -- cat /etc/kafka/tls/kafka_server_jaas.conf
```

### 问题3：Topic创建失败
```bash
# 查看详细的初始化日志
kubectl exec -it kafka-0 -n dci -- cat /tmp/init_topics.log

# 手动创建Topic测试
kubectl exec -it kafka-0 -n dci -- kafka-topics.sh --bootstrap-server localhost:30002 --command-config /tmp/client.properties --create --topic manual-test --partitions 3 --replication-factor 3
```

### 问题4：客户端连接失败
```bash
# 测试端口连通性
nc -zv ********** 30010
nc -zv ********** 30011  
nc -zv ********** 30012

# 检查证书文件是否存在
ls -la ca-chain.crt client.properties
```

## 验证清单

- [ ] 所有Kafka Pod运行正常
- [ ] SSL握手失败错误已消失
- [ ] Topic初始化成功完成
- [ ] 预期的Topic都已创建
- [ ] 外部端口(30010-30012)可访问
- [ ] 客户端能够成功连接和收发消息
- [ ] 证书文件和配置文件完整

## 回滚方案

如果修复后出现问题，可以按以下步骤回滚：

```bash
# 1. 恢复到之前的配置（如果有备份）
# kubectl apply -f kafka.yaml.backup

# 2. 或者重新生成证书并重新部署
cd scripts
./generate_certs.sh
cd dci-kafka-certs
./apply_secrets.sh

# 3. 重新部署kafka
kubectl rollout restart statefulset/kafka -n dci
```

## 联系支持

如果遇到无法解决的问题，请提供以下信息：

1. Pod状态：`kubectl get pods -n dci`
2. Pod日志：`kubectl logs kafka-0 -n dci --tail=100`
3. 配置检查：`kubectl exec -it kafka-0 -n dci -- cat /tmp/client.properties`
4. 验证脚本输出：`./verify_kafka_fix.sh`

---
**修复完成时间**: $(date)  
**环境**: 首信云港云K8S环境  
**修复版本**: sxyg_env v1.1 