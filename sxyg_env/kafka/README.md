# Kafka 部署指南 - 首信云岗环境

本文档描述了如何在首信云岗K8S环境中部署安全的Kafka集群。

## 环境信息

- **K8s集群**：首信云岗环境（**********, **********, **********）
- **存储**：NFS存储 (**********:/share/nfs4/data)
- **命名空间**：dci
- **镜像仓库**：**********/zhongxinsdn/

## 部署文件清单

- `kafka.yaml`：Kafka StatefulSet及ConfigMap配置
- `kafka-service.yaml`：Kafka服务配置
- `scripts/`：
  - `generate_certs.sh`：证书生成脚本 
  - `apply_secrets.sh`：证书Secret应用脚本
  - `create_kafka_topics.sh`：主题创建工具
  - `check_config_consistency.sh`：配置一致性检查工具
  - `test_kafka_multiport.sh`：多端口测试工具
  - `issue_new_client_cert.sh`：客户端证书签发工具

## 部署前准备

1. 确保已创建`dci`命名空间：
```bash
kubectl apply -f ../nfs_dci/namespace.yaml
```

2. 确保已创建镜像拉取Secret：
```bash
kubectl apply -f ../nfs_dci/docker-secret.yaml
```

3. 确保`dci-nfs-storage`存储类已部署并正常工作：
```bash
kubectl apply -f ../nfs_dci/rbac.yaml
kubectl apply -f ../nfs_dci/class.yaml
kubectl apply -f ../nfs_dci/deployment.yaml

# 验证存储类是否可用
kubectl get storageclass dci-nfs-storage
```

## 部署步骤

### 1. 生成安全证书和密钥

```bash
cd scripts
./generate_certs.sh
cd dci-kafka-certs
./apply_secrets.sh
cd ../..
```

### 2. 部署Kafka集群和服务

```bash
# 应用Kafka配置和StatefulSet
kubectl apply -f kafka.yaml

# 应用Kafka服务
kubectl apply -f kafka-service.yaml
```

### 3. 验证部署状态

```bash
# 检查Pod状态
kubectl get pods -n dci -l app=kafka -w

# 检查PVC状态
kubectl get pvc -n dci -l app=kafka

# 检查服务状态
kubectl get services -n dci -l app=kafka
```

### 4. 创建所需的Topic

```bash
cd scripts
./create_kafka_topics.sh -a
```

## 证书管理

### 为新客户端生成证书

当需要为新的系统或服务接入Kafka时，可以使用以下步骤：

```bash
cd scripts
# 例如，为名为"new-client"的服务生成证书
./issue_new_client_cert.sh new-client

# 应用生成的证书
cd dci-kafka-certs
./apply_secrets.sh

# 重启Kafka集群以加载新的认证配置
kubectl rollout restart statefulset/kafka -n dci
```

## 故障排查

### 1. Pod无法调度

如果Pod无法调度且显示污点相关错误，检查是否在`kafka.yaml`中正确配置了污点容忍：

```yaml
tolerations:
- key: "node-role.kubernetes.io/control-plane"
  operator: "Exists"
```

### 2. 镜像拉取失败

检查以下内容：
- 确认镜像已推送到`**********/zhongxinsdn/`仓库
- 确认`imagePullSecrets`配置正确
- 检查镜像名称和标签是否匹配

### 3. 配置一致性问题

如果Kafka集群启动后客户端连接出现问题，可以使用配置一致性检查工具验证配置：

```bash
cd scripts
./check_config_consistency.sh
```

### 4. 客户端连接测试

使用多端口测试工具验证客户端连接：

```bash
cd scripts
./test_kafka_multiport.sh
```

## 安全配置说明

此Kafka部署使用TLS和SASL进行安全防护：

1. **传输加密**：使用TLS加密所有通信
2. **认证**：使用SASL/PLAIN进行客户端身份验证
3. **授权**：可以启用ACL进行访问控制

## 维护操作

### 重启Kafka集群

```bash
kubectl rollout restart statefulset/kafka -n dci
```

### 查看Kafka日志

```bash
kubectl logs kafka-0 -n dci
```

### 检查配置

```bash
kubectl exec -it kafka-0 -n dci -- cat /opt/bitnami/kafka/config/server-0.properties
```

### 清理旧数据

如需清理旧数据，可以删除PVC并重新创建：

```bash
kubectl delete pvc kafka-data-kafka-0 -n dci
kubectl delete pvc kafka-data-kafka-1 -n dci
kubectl delete pvc kafka-data-kafka-2 -n dci

# 然后重新部署Kafka
kubectl apply -f kafka.yaml
```

## SSL问题排障

如果在部署过程中遇到SSL握手失败的问题，可以尝试以下步骤：

1. **检查证书生成**：
   ```bash
   # 重新生成证书
   cd scripts
   ./generate_certs.sh
   ./apply_secrets.sh
   ```

2. **使用PLAINTEXT协议测试**：
   ```bash
   # 运行临时测试脚本
   ./scripts/test_plaintext.sh
   ```

3. **查看Kafka日志**：
   ```bash
   kubectl -n dci logs kafka-0
   ```

4. **检查证书挂载情况**：
   ```bash
   kubectl -n dci exec kafka-0 -- ls -la /etc/kafka/tls/
   ```

5. **更新外部域名**：
   如果使用的是外部DNS，确保已更新为首信云岗环境地址:
   ```bash
   kubectl -n dci exec kafka-0 -- sed -i 's/dcikafka.citic-x.com/**********/g' /opt/bitnami/kafka/config/server-0.properties
   ```

## 常见问题

1. **SSL握手失败**：
   - 检查证书路径是否正确
   - 确认证书包含了正确的IP和域名
   - 尝试降低SSL客户端认证要求

2. **无法连接到Kafka**：
   - 检查网络策略和防火墙规则
   - 确认服务暴露了正确的端口
   - 验证NodePort是否已正确配置

3. **主题创建失败**：
   - 检查Kafka日志中的错误信息
   - 确认Kafka集群已正确形成仲裁
   - 尝试使用更低的副本因子创建测试主题 