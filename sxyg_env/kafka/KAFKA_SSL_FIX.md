# Kafka SSL握手失败问题解决方案

## 问题描述

在首信云港环境部署Kafka后，出现大量SSL握手失败错误：

```
[2025-07-28 02:52:53,340] INFO [SocketServer listenerType=BROKER, nodeId=0] Failed authentication with /127.0.0.1 (channelId=127.0.0.1:9092-127.0.0.1:58106-0-0) (SSL handshake failed)
```

## 问题分析

1. **根本原因**: Topic初始化脚本使用 `localhost:9092` 连接Kafka
2. **配置冲突**: 9092端口是`REPLICATION`监听器，使用SSL协议，要求客户端证书
3. **认证缺失**: 初始化脚本没有提供SSL客户端证书，导致握手失败
4. **JAAS语法错误**: 客户端配置文件中JAAS配置的shell转义语法有误

### 监听器配置分析
```yaml
REPLICATION (9092): SSL - Broker间元数据同步和复制，需要客户端证书
INTERNAL_CLIENT (30002): SASL_SSL - Pod内部客户端连接，需要用户名密码
EXTERNAL_CLIENT (30003): SASL_SSL - 外部客户端连接，需要用户名密码  
CONTROLLER (9093): SSL - KRaft控制器通信，仅用于控制器
```

### 端口设计理念
- **9092端口专用于Broker间通信**：节点间元数据同步、日志复制等内部操作
- **30002端口用于Pod内部客户端**：如Topic初始化脚本、内部服务连接
- **30003端口用于外部客户端**：如外部应用、监控工具等
- **30010-30012端口用于NodePort**：通过K8s节点IP对外暴露服务

## 解决方案

### 1. 修改连接端口
将初始化脚本的连接端口从9092改为30002（INTERNAL_CLIENT）：

**修改前**:
```bash
kafka-topics.sh --bootstrap-server localhost:9092 --list
```

**修改后**:
```bash
kafka-topics.sh --bootstrap-server localhost:30002 --command-config /tmp/client.properties --list
```

### 2. 添加SASL认证配置
在初始化脚本中创建客户端认证配置文件：

```bash
# 创建客户端配置文件
cat > /tmp/client.properties << EOF
security.protocol=SASL_SSL
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="kafka-admin" password="${ADMIN_PASSWORD}";
ssl.truststore.location=/etc/kafka/tls/intermediate-ca.crt
ssl.truststore.type=PEM
ssl.endpoint.identification.algorithm=
EOF
```

### 3. 动态获取管理员密码
从JAAS配置文件中提取kafka-admin密码：

```bash
ADMIN_PASSWORD=$(grep "user_kafka-admin" /etc/kafka/tls/kafka_server_jaas.conf | sed -n 's/.*user_kafka-admin="\([^"]*\)".*/\1/p')
```

### 4. 修复JAAS配置语法
简化shell转义，避免复杂的引号嵌套：

**修改前**:
```bash
echo 'sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="kafka-admin" password="'\''${ADMIN_PASSWORD}'\'';'
```

**修改后**:
```bash
echo 'sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="kafka-admin" password="${ADMIN_PASSWORD}";'
```

## 修改的文件位置

### sxyg_env/kafka/kafka.yaml
修改了三个地方的连接配置：
1. **第394行**: 集群就绪检查
2. **第405行**: Topic创建命令  
3. **第455行**: Topic列表命令

### sxyg_env/首信云港云K8S信息.md
添加了客户端连接配置示例和证书认证信息。

## 验证方法

### 1. 部署更新后的配置
```bash
kubectl apply -f kafka.yaml
kubectl rollout restart statefulset/kafka -n dci
```

### 2. 检查Pod日志
```bash
kubectl logs kafka-0 -n dci -f
```

### 3. 验证Topic创建
```bash
kubectl exec -it kafka-0 -n dci -- kafka-topics.sh --bootstrap-server localhost:30002 --command-config /tmp/client.properties --list
```

### 4. 测试外部连接
```bash
# 使用client.properties文件连接
kafka-console-producer.sh --bootstrap-server **********:30010 --topic test --producer.config client.properties
```

## 客户端配置模板

### client.properties
```ini
security.protocol=SASL_SSL
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="dci-telegraf" password="leEp7pTI5HpGG7KTJzIY9dzQsGDpaZhD";
ssl.truststore.location=ca-chain.crt
ssl.truststore.type=PEM
ssl.endpoint.identification.algorithm=
```

### 连接测试
```bash
# 生产消息
kafka-console-producer.sh --bootstrap-server **********:30010 --topic test --producer.config client.properties

# 消费消息  
kafka-console-consumer.sh --bootstrap-server **********:30010 --topic test --consumer.config client.properties --from-beginning
```

## 注意事项

1. **证书文件**: 确保ca-chain.crt文件在客户端可访问路径
2. **密码安全**: 妥善保管kafka_credentials.txt文件中的密码
3. **网络连通性**: 确认客户端能够访问**********的30010-30012端口
4. **Topic权限**: kafka-admin用户拥有所有Topic的读写权限

## 预期结果

修复后应该看到：
- SSL握手失败错误消失
- Topic成功创建（test、dci.monitor.v1.defaultchannel.*等）
- 客户端能够正常连接和收发消息
- Pod日志中显示"Topic initialization completed successfully" 