# 首信云港云K8S环境配置修改总结

## 环境信息变更

### 旧环境
- 外部访问域名: `dcikafka.citic-x.com`
- 负载均衡器: 使用SLB

### 新环境（首信云港）
- 节点IP: `**********`, `**********`, `**********`
- 外部访问IP: `**********` (直接使用节点IP，无SLB)
- 存储: `**********:/share/nfs4/data` (5T)
- 镜像仓库: `**********/zhongxinsdn/`

## 已修改的配置文件

### 1. 证书生成脚本
**文件**: `sxyg_env/kafka/scripts/generate_certs.sh`
**修改内容**:
- `SERVER_CERT_CN`: `dcikafka.citic-x.com` → `**********`
- SAN配置: 移除重复的DNS条目，保留节点IP

**文件**: `prod_env/kafka/scripts/generate_certs.sh`
**修改内容**:
- `SERVER_CERT_CN`: `dcikafka.citic-x.com` → `**********`

### 2. Kafka配置
**文件**: `sxyg_env/kafka/kafka.yaml`
**修改内容**:
- `EXTERNAL_ADVERTISED_HOST`: `**********` → `**********`
- `get_external_advertised_address()`: `dcikafka.citic-x.com` → `**********`

### 3. 测试脚本
**文件**: `sxyg_env/kafka/scripts/test_kafka_multiport.sh`
**修改内容**:
- `SLB_DOMAIN`: `dcikafka.citic-x.com` → `**********`

**文件**: `sxyg_env/kafka/scripts/issue_new_client_cert.sh`
**修改内容**:
- kcat示例命令中的地址: `dcikafka.intra.citic-x.com:30010` → `**********:30010`

## 无需修改的配置

### 存储配置
- NFS服务器地址 `**********` 和路径 `/share/nfs4/data` 已经正确
- 存储类、RBAC和部署配置与环境匹配

### 服务配置
- `kafka-service.yaml` 中的NodePort配置(30010-30012)保持不变
- 容忍配置已正确添加

### 镜像配置
- 镜像地址 `**********/zhongxinsdn/` 已经正确
- Docker registry secret已正确配置

## 网络配置说明

### 端口映射
- Kafka Broker 0: `**********:30010`
- Kafka Broker 1: `**********:30011`
- Kafka Broker 2: `**********:30012`

### 客户端连接配置
```bash
# Bootstrap servers
**********:30010,**********:30011,**********:30012

# 测试连接
nc -zv ********** 30010
nc -zv ********** 30011
nc -zv ********** 30012
```

## 部署步骤

### 1. 准备环境
```bash
cd sxyg_env/kafka
```

### 2. 生成证书
```bash
cd scripts
./generate_certs.sh
```

### 3. 应用证书
```bash
cd dci-kafka-certs
./apply_secrets.sh
```

### 4. 部署Kafka
```bash
cd ..
./deploy.sh
```

### 5. 验证部署
```bash
# 检查Pod状态
kubectl get pods -n dci

# 测试连接
cd scripts
./test_kafka_multiport.sh

# 创建测试主题
./create_kafka_topics.sh -l
```

## 注意事项

1. **无负载均衡器**: 首信云港环境不使用SLB，直接通过节点IP访问
2. **证书更新**: 重新生成的证书包含新的IP地址配置
3. **客户端配置**: 所有客户端程序需要更新bootstrap servers地址
4. **监控**: 需要更新监控配置中的Kafka地址

## 验证清单

- [ ] 证书生成成功且包含正确的IP地址
- [ ] Kafka集群启动正常
- [ ] 所有端口(30010-30012)可访问
- [ ] 主题创建和消息收发正常
- [ ] 客户端可以正常连接并获取元数据 