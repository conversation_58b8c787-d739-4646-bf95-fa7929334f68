#!/bin/bash
#
# Kafka SSL握手失败修复验证脚本
# 用于验证首信云港环境的Kafka集群是否正常运行
#

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

NAMESPACE="dci"
BROKERS=("**********:30010" "**********:30011" "**********:30012")

echo -e "${BLUE}=== Kafka SSL修复验证脚本 ===${NC}"
echo

# 1. 检查Pod状态
echo -e "${BLUE}1. 检查Kafka Pod状态${NC}"
for i in 0 1 2; do
    POD_STATUS=$(kubectl get pod kafka-$i -n $NAMESPACE -o jsonpath='{.status.phase}' 2>/dev/null || echo "NotFound")
    if [[ "$POD_STATUS" == "Running" ]]; then
        echo -e "  kafka-$i: ${GREEN}Running${NC}"
    else
        echo -e "  kafka-$i: ${RED}$POD_STATUS${NC}"
    fi
done
echo

# 2. 检查SSL握手失败错误
echo -e "${BLUE}2. 检查SSL握手失败错误（最近10分钟）${NC}"
SSL_ERRORS=$(kubectl logs kafka-0 -n $NAMESPACE --since=10m 2>/dev/null | grep "SSL handshake failed" | wc -l || echo "0")
if [[ "$SSL_ERRORS" -eq 0 ]]; then
    echo -e "  SSL握手失败错误: ${GREEN}0次 (修复成功)${NC}"
else
    echo -e "  SSL握手失败错误: ${RED}${SSL_ERRORS}次 (仍有问题)${NC}"
fi
echo

# 3. 检查Topic初始化状态
echo -e "${BLUE}3. 检查Topic初始化状态${NC}"
INIT_SUCCESS=$(kubectl logs kafka-0 -n $NAMESPACE 2>/dev/null | grep "Topic initialization completed successfully" | wc -l || echo "0")
if [[ "$INIT_SUCCESS" -gt 0 ]]; then
    echo -e "  Topic初始化: ${GREEN}成功${NC}"
else
    echo -e "  Topic初始化: ${YELLOW}可能仍在进行或失败${NC}"
fi
echo

# 4. 列出现有Topic
echo -e "${BLUE}4. 列出现有Topic${NC}"
TOPICS=$(kubectl exec -it kafka-0 -n $NAMESPACE -- kafka-topics.sh --bootstrap-server localhost:30002 --command-config /tmp/client.properties --list 2>/dev/null || echo "")
if [[ -n "$TOPICS" ]]; then
    echo -e "${GREEN}发现以下Topic:${NC}"
    echo "$TOPICS" | while read -r topic; do
        if [[ -n "$topic" ]]; then
            echo "  - $topic"
        fi
    done
else
    echo -e "${RED}无法获取Topic列表${NC}"
fi
echo

# 5. 测试端口连通性
echo -e "${BLUE}5. 测试外部端口连通性${NC}"
for broker in "${BROKERS[@]}"; do
    host="${broker%%:*}"
    port="${broker##*:}"
    
    if timeout 5 bash -c "cat < /dev/null > /dev/tcp/$host/$port" 2>/dev/null; then
        echo -e "  $broker: ${GREEN}可访问${NC}"
    else
        echo -e "  $broker: ${RED}不可访问${NC}"
    fi
done
echo

# 6. 检查证书配置
echo -e "${BLUE}6. 检查证书配置${NC}"
CERT_CHECK=$(kubectl exec -it kafka-0 -n $NAMESPACE -- ls -la /etc/kafka/tls/ 2>/dev/null | grep -E "(kafka.server.keystore.pem|intermediate-ca.crt|kafka_server_jaas.conf)" | wc -l || echo "0")
if [[ "$CERT_CHECK" -ge 3 ]]; then
    echo -e "  证书文件: ${GREEN}完整${NC}"
else
    echo -e "  证书文件: ${RED}缺失${NC}"
fi

# 检查客户端配置文件
CLIENT_CONFIG=$(kubectl exec -it kafka-0 -n $NAMESPACE -- ls /tmp/client.properties 2>/dev/null || echo "")
if [[ -n "$CLIENT_CONFIG" ]]; then
    echo -e "  客户端配置: ${GREEN}已创建${NC}"
    
    # 检查JAAS配置语法
    JAAS_CHECK=$(kubectl exec -it kafka-0 -n $NAMESPACE -- grep "sasl.jaas.config" /tmp/client.properties | grep ";" | wc -l 2>/dev/null || echo "0")
    if [[ "$JAAS_CHECK" -gt 0 ]]; then
        echo -e "  JAAS语法: ${GREEN}正确${NC}"
    else
        echo -e "  JAAS语法: ${RED}可能有误${NC}"
    fi
else
    echo -e "  客户端配置: ${RED}未找到${NC}"
fi
echo

# 7. 检查关键日志
echo -e "${BLUE}7. 检查关键日志（最近5分钟）${NC}"
echo -e "${YELLOW}启动状态:${NC}"
kubectl logs kafka-0 -n $NAMESPACE --since=5m 2>/dev/null | grep -E "Kafka Server started|STARTED" | tail -1 || echo "  未找到启动成功日志"

echo -e "${YELLOW}监听器状态:${NC}"
kubectl logs kafka-0 -n $NAMESPACE --since=5m 2>/dev/null | grep "Awaiting socket connections" | tail -3 || echo "  未找到监听器启动日志"

echo -e "${YELLOW}广播地址:${NC}"
kubectl logs kafka-0 -n $NAMESPACE --since=5m 2>/dev/null | grep "Using External Advertised Address" | tail -1 || echo "  未找到广播地址配置"
echo

# 总结
echo -e "${BLUE}=== 验证总结 ===${NC}"
if [[ "$SSL_ERRORS" -eq 0 && "$INIT_SUCCESS" -gt 0 && "$CERT_CHECK" -ge 3 ]]; then
    echo -e "${GREEN}✅ Kafka SSL修复验证通过！${NC}"
    echo -e "${GREEN}✅ 集群已正常运行，可以进行客户端连接测试${NC}"
    echo
    echo -e "${BLUE}端口使用说明:${NC}"
    echo "  - 9092: Broker间复制 (SSL，不用于客户端)"
    echo "  - 30002: Pod内部客户端 (SASL_SSL，Topic初始化等)"
    echo "  - 30003: 外部客户端 (SASL_SSL，应用连接)"
    echo "  - 30010-30012: NodePort外部访问"
    echo
    echo -e "${BLUE}客户端连接示例:${NC}"
    echo "# Pod内部连接"
    echo "kubectl exec -it kafka-0 -n dci -- kafka-console-producer.sh --bootstrap-server localhost:30002 --topic test --producer.config /tmp/client.properties"
    echo
    echo "# 外部连接"
    echo "kafka-console-producer.sh --bootstrap-server **********:30010 --topic test --producer.config client.properties"
else
    echo -e "${RED}❌ 验证发现问题，请检查上述输出${NC}"
    echo
    echo -e "${YELLOW}建议操作:${NC}"
    echo "1. 重新应用配置: kubectl apply -f kafka.yaml"
    echo "2. 重启Pod: kubectl rollout restart statefulset/kafka -n dci"
    echo "3. 查看详细日志: kubectl logs kafka-0 -n dci -f"
fi
echo

exit 0 