#!/bin/bash
#
# DCI Kafka 安全相关的Secret创建脚本
#
# **警告: 此脚本由 generate_certs.sh 自动生成，请勿手动修改！**
#
# 使用方法: ./apply_secrets.sh
#
# 此脚本将 dci-kafka-certs 目录下的所有证书和配置文件打包成 Kubernetes Secrets,
# 并将其应用到 '${KAFKA_NAMESPACE}' 命名空间。

set -e
set -o pipefail

KAFKA_NAMESPACE="dci"
SECRET_TLS_NAME="kafka-tls-secret"
SECRET_CLIENT_CERTS_NAME="kafka-client-certs"

echo "在命名空间 '${KAFKA_NAMESPACE}' 中创建Kafka安全相关的Secret..."

# 删除旧的Secrets以确保更新
kubectl delete secret "${SECRET_TLS_NAME}" -n "${KAFKA_NAMESPACE}" --ignore-not-found=true
kubectl delete secret "${SECRET_CLIENT_CERTS_NAME}" -n "${KAFKA_NAMESPACE}" --ignore-not-found=true

# 创建包含服务器证书和JAAS配置的Secret
kubectl create secret generic "${SECRET_TLS_NAME}" \
    --from-file=kafka.server.keystore.pem \
    --from-file=intermediate-ca.crt \
    --from-file=kafka_server_jaas.conf \
    -n "${KAFKA_NAMESPACE}"

echo "--> 正在为所有客户端证书和密钥打包Secret..."
# 创建包含所有客户端证书、密钥和CA链的Secret
# 使用循环和-exec来构建参数，确保文件名正确
SECRET_ARGS=""
for f in $(find . -name "*.client.crt" -o -name "*.client.key"); do
    SECRET_ARGS="${SECRET_ARGS} --from-file=${f}"
done

kubectl create secret generic "${SECRET_CLIENT_CERTS_NAME}" \
    --from-file=ca-chain.crt \
    ${SECRET_ARGS} \
    -n "${KAFKA_NAMESPACE}"

echo
echo "Secret创建成功."
kubectl get secret "${SECRET_TLS_NAME}" -n "${KAFKA_NAMESPACE}"
kubectl get secret "${SECRET_CLIENT_CERTS_NAME}" -n "${KAFKA_NAMESPACE}"

