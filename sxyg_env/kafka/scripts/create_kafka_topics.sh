#!/bin/bash

# Kafka主题创建脚本 - Kubernetes版
# 根据DCI监测系统设计文档，创建所需的Kafka主题
# 使用kubectl执行主题创建操作

KAFKA_TOPICS_CMD="/opt/bitnami/kafka/bin/kafka-topics.sh"

# 默认配置
NAMESPACE="dci"
KAFKA_POD=""
CREATE_ALL=false
TOPIC=""
DRY_RUN=false
EXTERNAL_ACCESS=false
BROKER_HOST="kafka-headless.dci.svc.cluster.local"
BROKER_PORT="9092"

# 主题配置
# 格式：主题名称:分区数:副本因子
TOPICS=(
  "dci.monitor.v1.defaultchannel.topology.lldp:3:2"
  "dci.monitor.v1.defaultchannel.metrics.telegraf:6:2"
  "dci.monitor.v1.defaultchannel.logs.syslog:3:2"
  "dci.monitor.v1.defaultchannel.tasks.control:3:2"
)

# 帮助信息
function show_help {
  echo "使用方法："
  echo "  $0 [选项]"
  echo ""
  echo "选项："
  echo "  -n, --namespace <namespace>    Kubernetes命名空间，默认为'dci'"
  echo "  -p, --pod <pod_name>          指定Kafka Pod名称（如不指定则自动选择第一个可用的）"
  echo "  -a, --all                     创建所有主题"
  echo "  -t, --topic <topic_name>      创建指定的主题"
  echo "  -l, --list                    列出所有topic及其状态"
  echo "  -d, --dry-run                 演示运行，不实际创建主题"
  echo "  -h, --help                    显示帮助信息"
  echo ""
  echo "示例："
  echo "  ./create_kafka_topics.sh -a"
  echo "  ./create_kafka_topics.sh -t dci.monitor.v1.defaultchannel.metrics.telegraf"
  echo "  ./create_kafka_topics.sh -l"
}

# 新增：列出所有topic及状态的函数
function list_topics_status {
  echo "获取Kafka集群所有Topic及状态..."
  if [[ "$EXTERNAL_ACCESS" == "true" ]]; then
    BROKERS="${BROKER_HOST}:${BROKER_PORT}"
    $KAFKA_TOPICS_CMD --bootstrap-server $BROKERS --describe
  else
    kubectl exec -n $NAMESPACE $KAFKA_POD -- $KAFKA_TOPICS_CMD --bootstrap-server localhost:9092 --describe
  fi
}

# 参数解析
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    -n|--namespace)
      NAMESPACE="$2"
      shift
      shift
      ;;
    -p|--pod)
      KAFKA_POD="$2"
      shift
      shift
      ;;
    -a|--all)
      CREATE_ALL=true
      shift
      ;;
    -t|--topic)
      TOPIC="$2"
      shift
      shift
      ;;
    -d|--dry-run)
      DRY_RUN=true
      shift
      ;;
    -e|--external)
      EXTERNAL_ACCESS=true
      shift
      ;;
    -l|--list)
      LIST_TOPICS=true
      shift
      ;;
    -h|--help)
      show_help
      exit 0
      ;;
    *)
      echo "错误：未知选项 $1"
      show_help
      exit 1
      ;;
  esac
done

# 新增：如指定-l参数，直接列出topic状态并退出
if [[ "$LIST_TOPICS" == "true" ]]; then
  # 自动选择Kafka Pod（如果未指定）
  if [[ -z "$KAFKA_POD" && "$EXTERNAL_ACCESS" == "false" ]]; then
    echo "未指定Kafka Pod，尝试自动选择..."
    KAFKA_POD=$(kubectl get pods -n "$NAMESPACE" -l app=kafka -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    if [[ -z "$KAFKA_POD" ]]; then
      echo "错误：无法自动找到Kafka Pod，请手动指定-p选项"
      exit 1
    fi
    echo "自动选择的Kafka Pod: $KAFKA_POD"
  fi
  list_topics_status
  exit 0
fi

# 验证必需参数（必须放在-l检查之后）
if [[ "$CREATE_ALL" == "false" && -z "$TOPIC" ]]; then
  echo "错误：必须指定-a或-t选项"
  show_help
  exit 1
fi

# 自动选择Kafka Pod（如果未指定）
if [[ -z "$KAFKA_POD" && "$EXTERNAL_ACCESS" == "false" ]]; then
  echo "未指定Kafka Pod，尝试自动选择..."
  KAFKA_POD=$(kubectl get pods -n "$NAMESPACE" -l app=kafka -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
  if [[ -z "$KAFKA_POD" ]]; then
    echo "错误：无法自动找到Kafka Pod，请手动指定-p选项"
    exit 1
  fi
  echo "自动选择的Kafka Pod: $KAFKA_POD"
fi

# 检查kubectl命令
if [[ "$EXTERNAL_ACCESS" == "false" && ! $(command -v kubectl) ]]; then
  echo "错误：找不到kubectl命令。请安装kubectl，或使用-e选项通过外部方式连接"
  exit 1
fi

# 创建主题函数
function create_topic {
  local topic_config=$1
  IFS=':' read -r name partitions replicas <<< "$topic_config"
  
  # 选择执行方式（kubectl或外部连接）
  local check_cmd=""
  local create_cmd=""
  
  if [[ "$EXTERNAL_ACCESS" == "true" ]]; then
    # 使用外部连接方式（需要安装Kafka命令行工具）
    if ! command -v $KAFKA_TOPICS_CMD &> /dev/null; then
      echo "错误：外部连接模式需要安装Kafka命令行工具"
      exit 1
    fi
    BROKERS="${BROKER_HOST}:${BROKER_PORT}"
    check_cmd="$KAFKA_TOPICS_CMD --bootstrap-server $BROKERS --describe --topic $name 2>/dev/null | grep -q \"Topic: $name\""
    create_cmd="$KAFKA_TOPICS_CMD --bootstrap-server $BROKERS --create --topic $name --partitions $partitions --replication-factor $replicas"
  else
    # 使用kubectl方式
    check_cmd="kubectl exec -n $NAMESPACE $KAFKA_POD -- $KAFKA_TOPICS_CMD --bootstrap-server localhost:9092 --describe --topic $name 2>/dev/null | grep -q \"Topic: $name\""
    create_cmd="kubectl exec -n $NAMESPACE $KAFKA_POD -- $KAFKA_TOPICS_CMD --bootstrap-server localhost:9092 --create --topic $name --partitions $partitions --replication-factor $replicas"
  fi
  
  echo "检查主题 $name 是否存在..."
  if eval "$check_cmd"; then
    echo "主题 $name 已存在，跳过创建"
    return 0
  fi
  
  echo "创建主题 $name (分区数: $partitions, 副本因子: $replicas)..."
  if [[ "$DRY_RUN" == "true" ]]; then
    echo "[演示] 将执行: $create_cmd"
  else
    if eval "$create_cmd"; then
      echo "主题 $name 创建成功"
    else
      echo "错误：创建主题 $name 失败"
      return 1
    fi
  fi
}

# 主逻辑
if [[ "$CREATE_ALL" == "true" ]]; then
  echo "准备创建所有主题..."
  for topic_config in "${TOPICS[@]}"; do
    create_topic "$topic_config" || exit 1
  done
  echo "所有主题创建完成"
elif [[ -n "$TOPIC" ]]; then
  found=false
  for topic_config in "${TOPICS[@]}"; do
    IFS=':' read -r name partitions replicas <<< "$topic_config"
    if [[ "$name" == "$TOPIC" ]]; then
      create_topic "$topic_config" || exit 1
      found=true
      break
    fi
  done
  
  if [[ "$found" == "false" ]]; then
    echo "错误：未找到主题 $TOPIC 的配置"
    echo "可用的主题有："
    for topic_config in "${TOPICS[@]}"; do
      IFS=':' read -r name _ _ <<< "$topic_config"
      echo "  $name"
    done
    exit 1
  fi
fi

echo "操作完成"
exit 0 