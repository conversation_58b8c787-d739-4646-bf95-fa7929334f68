#!/bin/bash
#
# Kafka 配置一致性检查工具
# 用于验证多个Kafka Broker的配置一致性，特别是广播地址配置
#

set -e

# 默认配置
NAMESPACE="dci"
CONFIG_PATH="/opt/bitnami/kafka/config"
CONFIG_FILE_PATTERN="server-*.properties"
POD_PREFIX="kafka"
BROKER_COUNT=3
VERBOSE=false
CHECK_FILES=true
CHECK_CONFIG=true

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 用法帮助
function usage() {
  echo -e "${BLUE}Kafka 配置一致性检查工具${NC}"
  echo
  echo "用法: $0 [选项]"
  echo
  echo "选项:"
  echo "  -n, --namespace NAMESPACE  指定Kafka Pod所在的命名空间 (默认: dci)"
  echo "  -p, --pod-prefix PREFIX    指定Kafka Pod名称前缀 (默认: kafka)"
  echo "  -c, --count COUNT          指定Broker数量 (默认: 3)"
  echo "  -f, --files-only           仅检查配置文件存在，不验证内容"
  echo "  -C, --config-only          仅验证配置内容，不检查文件存在"
  echo "  -v, --verbose              显示详细输出"
  echo "  -h, --help                 显示此帮助信息"
  echo
  echo "示例:"
  echo "  $0 --namespace dci --verbose"
  echo "  $0 --config-only"
}

# 参数解析
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    -n|--namespace)
      NAMESPACE="$2"
      shift
      shift
      ;;
    -p|--pod-prefix)
      POD_PREFIX="$2"
      shift
      shift
      ;;
    -c|--count)
      BROKER_COUNT="$2"
      shift
      shift
      ;;
    -f|--files-only)
      CHECK_FILES=true
      CHECK_CONFIG=false
      shift
      ;;
    -C|--config-only)
      CHECK_FILES=false
      CHECK_CONFIG=true
      shift
      ;;
    -v|--verbose)
      VERBOSE=true
      shift
      ;;
    -h|--help)
      usage
      exit 0
      ;;
    *)
      echo -e "${RED}错误: 未知选项 $key${NC}" >&2
      usage
      exit 1
      ;;
  esac
done

# 检查kubectl命令
if ! command -v kubectl &>/dev/null; then
  echo -e "${RED}错误: 未找到kubectl命令${NC}" >&2
  exit 1
fi

echo -e "${BLUE}开始Kafka配置一致性检查...${NC}"
echo -e "${BLUE}命名空间: ${NAMESPACE}, Pod前缀: ${POD_PREFIX}, Broker数量: ${BROKER_COUNT}${NC}"

# 检查pod是否存在
function check_pods_exist() {
  local missing_pods=0
  
  echo -e "${BLUE}检查Kafka Pod是否存在...${NC}"
  
  for i in $(seq 0 $((BROKER_COUNT-1))); do
    local pod="${POD_PREFIX}-${i}"
    
    if kubectl get pod "$pod" -n "$NAMESPACE" &>/dev/null; then
      echo -e "  Pod ${pod}: ${GREEN}存在${NC}"
    else
      echo -e "  Pod ${pod}: ${RED}不存在${NC}"
      missing_pods=$((missing_pods+1))
    fi
  done
  
  if [[ $missing_pods -gt 0 ]]; then
    echo -e "${YELLOW}警告: 有 $missing_pods 个Pod不存在${NC}"
    return 1
  else
    echo -e "${GREEN}所有Pod都已存在${NC}"
    return 0
  fi
}

# 检查配置文件是否存在
function check_config_files() {
  local missing_files=0
  
  echo -e "${BLUE}检查配置文件是否存在...${NC}"
  
  for i in $(seq 0 $((BROKER_COUNT-1))); do
    local pod="${POD_PREFIX}-${i}"
    local config_file="${CONFIG_PATH}/server-${i}.properties"
    
    if kubectl exec "$pod" -n "$NAMESPACE" -- test -f "$config_file" 2>/dev/null; then
      echo -e "  配置文件 ${config_file} 在 ${pod}: ${GREEN}存在${NC}"
    else
      echo -e "  配置文件 ${config_file} 在 ${pod}: ${RED}不存在${NC}"
      missing_files=$((missing_files+1))
    fi
  done
  
  if [[ $missing_files -gt 0 ]]; then
    echo -e "${YELLOW}警告: 有 $missing_files 个配置文件不存在${NC}"
    return 1
  else
    echo -e "${GREEN}所有配置文件都已存在${NC}"
    return 0
  fi
}

# 检查advertised.listeners配置一致性
function check_advertised_listeners() {
  local inconsistent=0
  local all_configs=()
  local all_hostnames=()
  local all_ports=()
  
  echo -e "${BLUE}检查advertised.listeners配置一致性...${NC}"
  
  # 收集所有广播地址配置
  for i in $(seq 0 $((BROKER_COUNT-1))); do
    local pod="${POD_PREFIX}-${i}"
    local config_file="${CONFIG_PATH}/server-${i}.properties"
    
    # 提取advertised.listeners配置
    local advertised_listeners=$(kubectl exec "$pod" -n "$NAMESPACE" -- grep "^advertised.listeners=" "$config_file" 2>/dev/null || echo "")
    
    if [[ -z "$advertised_listeners" ]]; then
      echo -e "  Pod ${pod}: ${RED}未找到advertised.listeners配置${NC}"
      inconsistent=$((inconsistent+1))
      all_configs[$i]="未找到"
      all_hostnames[$i]="未知"
      all_ports[$i]="未知"
      continue
    fi
    
    # 提取EXTERNAL部分
    local external_part=$(echo "$advertised_listeners" | grep -o "EXTERNAL://[^,]*" || echo "")
    
    if [[ -z "$external_part" ]]; then
      echo -e "  Pod ${pod}: ${RED}未找到EXTERNAL监听器配置${NC}"
      inconsistent=$((inconsistent+1))
      all_configs[$i]="无EXTERNAL部分"
      all_hostnames[$i]="未知"
      all_ports[$i]="未知"
      continue
    fi
    
    # 提取主机名和端口
    local address="${external_part#EXTERNAL://}"
    local hostname="${address%%:*}"
    local port="${address##*:}"
    
    all_configs[$i]="$external_part"
    all_hostnames[$i]="$hostname"
    all_ports[$i]="$port"
    
    if [[ "$VERBOSE" == "true" ]]; then
      echo -e "  Pod ${pod}: EXTERNAL监听器 = ${BLUE}${address}${NC} (主机名: ${hostname}, 端口: ${port})"
    fi
  done
  
  # 检查主机名一致性
  local first_hostname="${all_hostnames[0]}"
  for i in $(seq 1 $((BROKER_COUNT-1))); do
    if [[ "${all_hostnames[$i]}" != "$first_hostname" && "${all_hostnames[$i]}" != "未知" ]]; then
      echo -e "${RED}错误: 主机名不一致 - Pod ${POD_PREFIX}-0 使用 '${first_hostname}', Pod ${POD_PREFIX}-${i} 使用 '${all_hostnames[$i]}'${NC}"
      inconsistent=$((inconsistent+1))
    fi
  done
  
  # 检查端口与Pod序号的关系
  for i in $(seq 0 $((BROKER_COUNT-1))); do
    local expected_port=$((30010 + i))
    if [[ "${all_ports[$i]}" != "$expected_port" && "${all_ports[$i]}" != "未知" ]]; then
      echo -e "${RED}错误: 端口不符合预期 - Pod ${POD_PREFIX}-${i} 使用端口 '${all_ports[$i]}', 预期端口 '${expected_port}'${NC}"
      inconsistent=$((inconsistent+1))
    fi
  done
  
  if [[ $inconsistent -gt 0 ]]; then
    echo -e "${RED}发现 $inconsistent 个配置不一致${NC}"
    return 1
  else
    echo -e "${GREEN}所有广播地址配置一致${NC}"
    return 0
  fi
}

# 检查服务端口映射一致性
function check_service_ports() {
  local inconsistent=0
  
  echo -e "${BLUE}检查服务端口映射一致性...${NC}"
  
  # 检查每个Broker对应的服务
  for i in $(seq 0 $((BROKER_COUNT-1))); do
    local service_name="${POD_PREFIX}-${i}-external"
    local expected_port=$((30010 + i))
    
    # 检查服务是否存在
    if ! kubectl get service "$service_name" -n "$NAMESPACE" &>/dev/null; then
      echo -e "  服务 ${service_name}: ${RED}不存在${NC}"
      inconsistent=$((inconsistent+1))
      continue
    fi
    
    # 检查服务端口映射
    local nodeport=$(kubectl get service "$service_name" -n "$NAMESPACE" -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "")
    
    if [[ -z "$nodeport" ]]; then
      echo -e "  服务 ${service_name}: ${RED}未找到NodePort${NC}"
      inconsistent=$((inconsistent+1))
      continue
    fi
    
    if [[ "$nodeport" != "$expected_port" ]]; then
      echo -e "  服务 ${service_name}: ${RED}端口不匹配 - 当前值: ${nodeport}, 预期值: ${expected_port}${NC}"
      inconsistent=$((inconsistent+1))
      continue
    fi
    
    echo -e "  服务 ${service_name}: ${GREEN}端口映射正确 (${nodeport})${NC}"
  done
  
  if [[ $inconsistent -gt 0 ]]; then
    echo -e "${RED}发现 $inconsistent 个服务端口映射不一致${NC}"
    return 1
  else
    echo -e "${GREEN}所有服务端口映射一致${NC}"
    return 0
  fi
}

# 执行检查
check_pods_exist
PODS_EXIST=$?

if [[ $CHECK_FILES == "true" && $PODS_EXIST -eq 0 ]]; then
  check_config_files
  CONFIG_FILES_OK=$?
else
  CONFIG_FILES_OK=0
fi

if [[ $CHECK_CONFIG == "true" && $PODS_EXIST -eq 0 ]]; then
  check_advertised_listeners
  ADVERTISED_OK=$?
  
  check_service_ports
  PORTS_OK=$?
else
  ADVERTISED_OK=0
  PORTS_OK=0
fi

# 输出总结
echo
echo -e "${BLUE}检查结果摘要:${NC}"
if [[ $PODS_EXIST -eq 0 ]]; then
  echo -e "  Pod状态: ${GREEN}所有Pod存在${NC}"
else
  echo -e "  Pod状态: ${RED}缺少部分Pod${NC}"
fi

if [[ $CHECK_FILES == "true" ]]; then
  if [[ $CONFIG_FILES_OK -eq 0 ]]; then
    echo -e "  配置文件: ${GREEN}所有配置文件存在${NC}"
  else
    echo -e "  配置文件: ${RED}缺少部分配置文件${NC}"
  fi
fi

if [[ $CHECK_CONFIG == "true" ]]; then
  if [[ $ADVERTISED_OK -eq 0 ]]; then
    echo -e "  广播地址配置: ${GREEN}所有广播地址配置一致${NC}"
  else
    echo -e "  广播地址配置: ${RED}广播地址配置不一致${NC}"
  fi
  
  if [[ $PORTS_OK -eq 0 ]]; then
    echo -e "  服务端口映射: ${GREEN}所有服务端口映射正确${NC}"
  else
    echo -e "  服务端口映射: ${RED}服务端口映射不正确${NC}"
  fi
fi

# 退出码
if [[ $PODS_EXIST -eq 0 && $CONFIG_FILES_OK -eq 0 && $ADVERTISED_OK -eq 0 && $PORTS_OK -eq 0 ]]; then
  echo -e "${GREEN}所有检查通过!${NC}"
  exit 0
else
  echo -e "${RED}检查发现问题，请查看详细输出${NC}"
  exit 1
fi 