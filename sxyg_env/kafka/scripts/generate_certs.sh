#!/bin/bash
#
# Kafka 安全配置证书生成脚本 - 生产环境版本
#
# 本脚本根据《21-DCI-kafka安全配置技术方案.md》文档，执行以下操作:
# 1. 创建一个三级证书体系:
#    - 自签名的根CA (Root CA)
#    - 由根CA签名的中间CA (Intermediate CA)
#    - 由中间CA签名的Kafka Broker服务器证书 (包含所有SANs)
#    - 由中间CA签名的客户端证书 (telegraf, flowdata)
# 2. 生成Kafka服务器端所需的JAAS配置文件。
# 3. 生成用于创建Kubernetes Secret的命令。
# 4. 在生成的证书目录中创建一个README.md文件说明。
#
# 阶段一：首次设置 (只需做一次)
# 运行 cd prod_env/kafka/scripts/ 进入脚本目录。
# 运行 ./generate_certs.sh。这将生成CA、初始证书，以及我们新的、更智能的 apply_secrets.sh 和 issue_new_client_cert.sh 脚本。
# cd dci-kafka-certs 然后运行 ./apply_secrets.sh 将证书部署到Kubernetes。
# 滚动重启Kafka集群: kubectl rollout restart statefulset/kafka -n dci。
# 阶段二：为新客户端授权 (日常操作)
# 当您需要为任何新的服务（比如 new-app）接入Kafka时：
# 运行 cd prod_env/kafka/scripts/ 进入脚本目录。
# 执行签发脚本：./issue_new_client_cert.sh new-app。
# 这会在 dci-kafka-certs 目录中生成 new-app.client.crt 和 new-app.client.key。
# 它还会自动更新 kafka_server_jaas.conf 和 kafka_credentials.txt。
# cd dci-kafka-certs 然后运行 ./apply_secrets.sh。
# 它会自动找到 new-app 的证书并连同所有旧证书一起更新到 kafka-client-certs Secret。
# 它也会把更新后的JAAS配置文件更新到 kafka-tls-secret。
# 滚动重启Kafka集群：kubectl rollout restart statefulset/kafka -n dci。
# 这是为了让Broker加载新的JAAS配置以识别新用户。此操作不会影响任何正在运行的旧客户端，它们会平滑地重连到重启后的Broker。

set -e
set -o pipefail

# --- 生命周期配置 (生产环境值) ---
ROOT_CA_DAYS=4150 # 根CA证书有效期
INTERMEDIATE_CA_DAYS=3650 # 中间CA证书有效期
LEAF_CERT_DAYS=1800 # 服务器和客户端证书有效期

# --- 其他配置 ---
CERTS_DIR="./dci-kafka-certs"
KAFKA_NAMESPACE="dci"

# 证书主题信息 - 生产环境配置
ROOT_CA_SUBJ="/C=CN/O=CITIC/OU=DCI-PROD/CN=DCI-Kafka-Root-CA-PROD"
INTERMEDIATE_CA_SUBJ="/C=CN/O=CITIC/OU=DCI-PROD/CN=DCI-Kafka-Intermediate-CA-PROD"
SERVER_CERT_CN="**********" # 使用首信云港环境节点IP
TELEGRAF_CLIENT_SUBJ="/C=CN/O=CITIC/OU=DCI-PROD/CN=dci-telegraf"
FLOWDATA_CLIENT_SUBJ="/C=CN/O=CITIC/OU=DCI-PROD/CN=dci-flowdata"

# 为每个客户端生成密码 - 生产环境使用更复杂密码
KAFKA_ADMIN_PASSWORD="$(openssl rand -base64 24)" # 管理员密码
TELEGRAF_PASSWORD="$(openssl rand -base64 24)" # 监控密码
FLOWDATA_PASSWORD="$(openssl rand -base64 24)" # 流量数据密码

# --- 辅助函数 ---
log_info() {
    echo -e "\033[0;32m[INFO]\033[0m $1"
}

log_warn() {
    echo -e "\033[0;33m[WARN]\033[0m $1"
}

log_error() {
    echo -e "\033[0;31m[ERROR]\033[0m $1" >&2
}

show_help() {
  cat << EOF
用法: ./generate_certs.sh [选项]

本脚本用于为DCI Kafka集群一键生成完整的安全证书体系和相关配置文件。

它会创建一个名为 '${CERTS_DIR}' 的目录，并包含以下内容:
  - Root CA, Intermediate CA, Server, and Client 证书和私钥。
  - Kafka Broker 所需的 JAAS 配置文件。
  - 一个名为 'apply_secrets.sh' 的脚本，用于将证书部署到Kubernetes。
  - 一个 'README.md' 文件，用于解释生成的文件。

选项:
  --root-days DAYS          设置根CA证书的有效期 (默认: ${ROOT_CA_DAYS})
  --intermediate-days DAYS  设置中间CA证书的有效期 (默认: ${INTERMEDIATE_CA_DAYS})
  --leaf-days DAYS          设置服务器和客户端证书的有效期 (默认: ${LEAF_CERT_DAYS})
  -h, --help                显示此帮助信息并退出。

何时使用此脚本:
  1. 首次部署: 在初次为Kafka集群配置TLS/SSL加密和SASL认证时。
  2. 证书更新: 当任何证书即将过期时，需要重新运行此脚本生成全新的证书。
  3. 配置变更:
     - 当Kafka集群的访问地址(SANs)发生变化时。
     - 当需要添加、删除或重命名客户端时 (例如，新增一个服务需要连接Kafka)。
  4. 密钥泄露: 当怀疑任何私钥已经泄露时，必须立即执行此脚本以更换所有证书和密钥。

EOF
}

# --- 参数解析 ---
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --root-days) ROOT_CA_DAYS="$2"; shift ;;
        --intermediate-days) INTERMEDIATE_CA_DAYS="$2"; shift ;;
        --leaf-days) LEAF_CERT_DAYS="$2"; shift ;;
        -h|--help) show_help; exit 0 ;;
        *) echo "未知参数: $1"; show_help; exit 1 ;;
    esac
    shift
done


# --- 检查依赖 ---
if ! command -v openssl &> /dev/null; then
  log_error "openssl 命令未找到, 请先安装."
  exit 1
fi

if ! command -v kubectl &> /dev/null; then
  log_warn "kubectl 命令未找到. 脚本将无法验证命名空间, 但会继续生成证书和secret配置."
fi

# --- 主逻辑 ---
log_info "开始为DCI Kafka集群生成安全证书..."

# 检查命名空间是否存在
if command -v kubectl &> /dev/null; then
    if ! kubectl get namespace "${KAFKA_NAMESPACE}" > /dev/null 2>&1; then
        log_warn "命名空间 '${KAFKA_NAMESPACE}' 不存在. 您可能需要先创建它: kubectl create namespace ${KAFKA_NAMESPACE}"
    fi
fi

# 清理并创建工作目录
rm -rf "${CERTS_DIR}"
mkdir -p "${CERTS_DIR}"
cd "${CERTS_DIR}"
log_info "证书将生成在: $(pwd)"

# --- 创建 README.md ---
log_info "步骤 1/8: 创建README.md说明文件..."
cat > README.md <<-EOF
# DCI Kafka 安全证书

**警告: 此目录及其所有内容均为自动生成，请勿手动修改！**

此目录包含由 \`generate_certs.sh\` 脚本为 DCI Kafka 集群生成的全部安全证书、私钥和配置文件。
如果需要进行任何变更，请修改该脚本后重新运行，而不是直接编辑此目录下的文件。

## 何时需要重新生成?

在以下场景下，您必须重新运行 \`generate_certs.sh\` 脚本以生成一套全新的证书：

1.  **证书即将过期**: 所有证书均有有效期 (服务器/客户端证书当前设置为${LEAF_CERT_DAYS}天)。在过期前，需要重新生成并部署。
2.  **私钥泄露**: 如果怀疑任何私钥 (尤其是 \`root-ca.key\` 或 \`intermediate-ca.key\`) 已被泄露，必须立即重新生成。
3.  **服务地址变更**: 如果 Kafka Broker 的服务地址或 Kubernetes SAN (Subject Alternative Names) 发生变化，需要更新服务器证书。
4.  **客户端变更**: 当需要为新的客户端服务授权，或移除现有客户端时，需要更新客户端证书和JAAS配置。

## 如何重新生成?

1.  返回上一级目录 (\`cd ..\`)。
2.  直接运行 \`./generate_certs.sh\`。
3.  脚本会自动删除当前的 \`${CERTS_DIR}\` 目录，并创建一个全新的。
4.  进入新生成的目录，并执行 \`./apply_secrets.sh\` 将新证书应用到 Kubernetes。
5.  **重要**: 应用Secret后，需要滚动重启相关的Pod (如Kafka, flowdata等) 来加载新的证书。

EOF
log_info "README.md创建成功."


# --- 1. 创建根CA ---
log_info "步骤 2/8: 创建根CA..."
openssl genrsa -out root-ca.key 4096
openssl req -new -x509 -sha256 \
    -key root-ca.key \
    -out root-ca.crt \
    -days ${ROOT_CA_DAYS} \
    -subj "${ROOT_CA_SUBJ}"
log_info "根CA创建成功: root-ca.crt"

# --- 2. 创建中间CA ---
log_info "步骤 3/8: 创建中间CA..."
openssl genrsa -out intermediate-ca.key 4096
openssl req -new -sha256 \
    -key intermediate-ca.key \
    -out intermediate-ca.csr \
    -subj "${INTERMEDIATE_CA_SUBJ}"

# 为中间CA创建扩展配置文件
cat > intermediate_ca.cnf <<-EOF
[ v3_intermediate_ca ]
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always,issuer
basicConstraints = critical, CA:true
keyUsage = critical, digitalSignature, cRLSign, keyCertSign
EOF

openssl x509 -req -sha256 \
    -in intermediate-ca.csr \
    -CA root-ca.crt \
    -CAkey root-ca.key \
    -CAcreateserial \
    -out intermediate-ca.crt \
    -days ${INTERMEDIATE_CA_DAYS} \
    -extfile intermediate_ca.cnf \
    -extensions v3_intermediate_ca
log_info "中间CA创建成功: intermediate-ca.crt"

# --- 3. 创建服务器证书 (使用SAN) ---
log_info "步骤 4/8: 创建Kafka服务器证书..."
openssl genpkey -algorithm RSA -pkeyopt rsa_keygen_bits:2048 -out kafka.server.key
echo "--> Converting server private key to PKCS#8 format for Java compatibility..."
mv kafka.server.key kafka.server.key.original
openssl pkcs8 -topk8 -inform PEM -in kafka.server.key.original -outform PEM -nocrypt -out kafka.server.key
rm kafka.server.key.original

# 创建SAN配置文件
cat > server_cert.cnf <<-EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no
[req_distinguished_name]
CN = ${SERVER_CERT_CN}
[v3_req]
subjectAltName = @alt_names
[alt_names]
DNS.1 = ${SERVER_CERT_CN}
DNS.2 = *.kafka-headless.${KAFKA_NAMESPACE}.svc.cluster.local
DNS.3 = kafka-0.kafka-headless.${KAFKA_NAMESPACE}.svc.cluster.local
DNS.4 = kafka-1.kafka-headless.${KAFKA_NAMESPACE}.svc.cluster.local
DNS.5 = kafka-2.kafka-headless.${KAFKA_NAMESPACE}.svc.cluster.local
DNS.6 = localhost
IP.1 = 127.0.0.1
IP.2 = **********
IP.3 = **********
IP.4 = **********
IP.5 = **********
EOF

openssl req -new -sha256 \
    -key kafka.server.key \
    -out kafka.server.csr \
    -config server_cert.cnf
openssl x509 -req -sha256 \
    -in kafka.server.csr \
    -CA intermediate-ca.crt \
    -CAkey intermediate-ca.key \
    -CAcreateserial \
    -out kafka.server.crt \
    -days ${LEAF_CERT_DAYS} \
    -extensions v3_req \
    -extfile server_cert.cnf
log_info "服务器证书创建成功: kafka.server.crt"

log_info "创建服务器组合 PEM 密钥库 (私钥 + 服务器证书 + 中间CA证书)..."
cat kafka.server.key kafka.server.crt intermediate-ca.crt > kafka.server.keystore.pem
log_info "已创建: kafka.server.keystore.pem"

# --- 4. 创建客户端证书 ---
log_info "步骤 5/8: 创建客户端证书..."

# 内部函数用于生成客户端证书，避免代码重复
generate_client_cert() {
    local client_name="$1"
    local client_subj="$2"
    log_info "--- Generating Client Certificate for ${client_name} ---"
    openssl genpkey -algorithm RSA -pkeyopt rsa_keygen_bits:2048 -out "${client_name}.client.key"
    echo "--> Converting ${client_name} client private key to PKCS#8 format..."
    mv "${client_name}.client.key" "${client_name}.client.key.original"
    openssl pkcs8 -topk8 -inform PEM -in "${client_name}.client.key.original" -outform PEM -nocrypt -out "${client_name}.client.key"
    rm "${client_name}.client.key.original"
    
    openssl req -new -key "${client_name}.client.key" -out "${client_name}.client.csr" -subj "${client_subj}"
    openssl x509 -req -in "${client_name}.client.csr" -CA intermediate-ca.crt -CAkey intermediate-ca.key -CAcreateserial -out "${client_name}.client.crt" -days ${LEAF_CERT_DAYS}
    rm "${client_name}.client.csr"
    log_info "客户端 '${client_name}' 证书创建成功: ${client_name}.client.crt"
}

generate_client_cert "dci-telegraf" "${TELEGRAF_CLIENT_SUBJ}"
generate_client_cert "dci-flowdata" "${FLOWDATA_CLIENT_SUBJ}"

# --- 5. 创建CA证书链 ---
log_info "步骤 6/8: 创建CA证书链文件..."
cat intermediate-ca.crt root-ca.crt > ca-chain.crt
log_info "CA证书链创建成功: ca-chain.crt"

# --- 6. 创建JAAS配置文件和密码 ---
log_info "步骤 7/8: 创建JAAS配置文件..."

# 创建 kafka_server_jaas.conf
cat > kafka_server_jaas.conf <<-EOF
KafkaServer {
    org.apache.kafka.common.security.plain.PlainLoginModule required
    user_kafka-admin="${KAFKA_ADMIN_PASSWORD}"
    user_dci-telegraf="${TELEGRAF_PASSWORD}"
    user_dci-flowdata="${FLOWDATA_PASSWORD}";
};

KafkaClient {
    org.apache.kafka.common.security.plain.PlainLoginModule required
    username="kafka-admin"
    password="${KAFKA_ADMIN_PASSWORD}";
};
EOF
log_info "JAAS配置文件创建成功: kafka_server_jaas.conf"

# 创建 kafka_credentials.txt 以记录密码
cat > kafka_credentials.txt <<-EOF
# DCI Kafka Client Credentials
# 这个文件由 generate_certs.sh 自动生成，请妥善保管。
# 它包含了用于 SASL/PLAIN 认证的用户名和密码。

DCI_KAFKA_ADMIN_PASSWORD=${KAFKA_ADMIN_PASSWORD}
DCI_TELEGRAF_PASSWORD=${TELEGRAF_PASSWORD}
DCI_FLOWDATA_PASSWORD=${FLOWDATA_PASSWORD}
EOF

# --- 7. 创建 Kubernetes Secret 应用脚本 ---
log_info "步骤 8/8: 生成Kubernetes Secret创建命令..."

cat > apply_secrets.sh <<-'EOF'
#!/bin/bash
#
# DCI Kafka 安全相关的Secret创建脚本
#
# **警告: 此脚本由 generate_certs.sh 自动生成，请勿手动修改！**
#
# 使用方法: ./apply_secrets.sh
#
# 此脚本将 dci-kafka-certs 目录下的所有证书和配置文件打包成 Kubernetes Secrets,
# 并将其应用到 '${KAFKA_NAMESPACE}' 命名空间。

set -e
set -o pipefail

KAFKA_NAMESPACE="dci"
SECRET_TLS_NAME="kafka-tls-secret"
SECRET_CLIENT_CERTS_NAME="kafka-client-certs"

echo "在命名空间 '${KAFKA_NAMESPACE}' 中创建Kafka安全相关的Secret..."

# 删除旧的Secrets以确保更新
kubectl delete secret "${SECRET_TLS_NAME}" -n "${KAFKA_NAMESPACE}" --ignore-not-found=true
kubectl delete secret "${SECRET_CLIENT_CERTS_NAME}" -n "${KAFKA_NAMESPACE}" --ignore-not-found=true

# 创建包含服务器证书和JAAS配置的Secret
kubectl create secret generic "${SECRET_TLS_NAME}" \
    --from-file=kafka.server.keystore.pem \
    --from-file=intermediate-ca.crt \
    --from-file=kafka_server_jaas.conf \
    -n "${KAFKA_NAMESPACE}"

echo "--> 正在为所有客户端证书和密钥打包Secret..."
# 创建包含所有客户端证书、密钥和CA链的Secret
# 使用循环和-exec来构建参数，确保文件名正确
SECRET_ARGS=""
for f in $(find . -name "*.client.crt" -o -name "*.client.key"); do
    SECRET_ARGS="${SECRET_ARGS} --from-file=${f}"
done

kubectl create secret generic "${SECRET_CLIENT_CERTS_NAME}" \
    --from-file=ca-chain.crt \
    ${SECRET_ARGS} \
    -n "${KAFKA_NAMESPACE}"

echo
echo "Secret创建成功."
kubectl get secret "${SECRET_TLS_NAME}" -n "${KAFKA_NAMESPACE}"
kubectl get secret "${SECRET_CLIENT_CERTS_NAME}" -n "${KAFKA_NAMESPACE}"


EOF
chmod +x apply_secrets.sh

# --- 清理工作 ---
# 删除中间文件
rm ./*.csr
rm ./*.cnf
rm ./*.srl

cd ..

# --- 完成 ---
log_info "证书和脚本生成完毕!"
echo
cat << EOF
--- 下一步操作 ---
1. **检查并备份**: 证书和密码会生成在 $(pwd)/${CERTS_DIR}/ 目录下.
   - 特别注意 **$(pwd)/${CERTS_DIR}/kafka_credentials.txt** 文件, 它包含了生成的密码, 请妥善保管.

2. **应用Secrets**: 请您 在云端环境中, 进入 ${CERTS_DIR}/ 目录, 然后执行:
   ./apply_secrets.sh
   这将会把证书和密码安全地存储为Kubernetes Secrets, 供Kafka集群和客户端使用.

3. **重启服务**: 为了让Kafka集群和客户端加载新的证书和配置, 您需要滚动重启它们:
   kubectl rollout restart statefulset/kafka -n ${KAFKA_NAMESPACE}
   # 如果其他服务(如flowdata)也配置为客户端, 同样需要重启.

4. **添加新客户端**: 如果您需要为新的服务授权, 请使用新生成的 'issue_new_client_cert.sh' 脚本。

5. **获取帮助**: 运行 ./generate_certs.sh --help 查看详细用法。
EOF