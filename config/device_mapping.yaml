# 设备ID映射规则配置
# 按照25-01文档第3.2.2节设计要求
device_id_mapping:
  # SNMP采集的设备
  snmp_devices:
    source_pattern: "dci_snmp_.*"
    device_id_labels:
      - "device_id"         # 优先使用device_id标签
      - "device_ip"         # 其次使用device_ip标签
    instance_pattern: "^(\\d+\\.\\d+\\.\\d+\\.\\d+):(\\d+)$"
    instance_device_group: 1   # 正则表达式中设备IP的组号

  # 网络设备Agent采集
  network_agents:
    source_pattern: "network_.*"
    device_id_labels:
      - "device"
      - "host"
    instance_pattern: "^([^:]+):(\\d+)$"
    instance_device_group: 1

  # 系统监控指标
  system_metrics:
    source_pattern: "node_.*"
    device_id_labels:
      - "instance"         # 对于node_exporter，instance就是设备标识
      - "hostname" 
    instance_pattern: "^([^:]+):(\\d+)$"
    instance_device_group: 1

# 全局设备ID提取配置
global_device_extraction:
  # 设备ID标签优先级
  priority_labels:
    - "device_id"
    - "device"
    - "device_ip"
  # instance标签解析
  instance_parsing:
    enabled: true
    remove_port: true       # 从instance中移除端口号
  # 其他设备标识标签
  fallback_labels:
    - "host"
    - "hostname"
    - "node"

# 任务协同监控配置
task_monitoring:
  # 延展监测配置
  extended_monitoring:
    enabled: true
    duration: "1h"        # 任务结束后延展监测时长
  # 会话管理配置
  session_management:
    cleanup_interval: "24h"    # 清理已完成会话的间隔
    max_sessions: 1000         # 最大并发会话数
  # 任务关联查询优化
  association_query:
    cache_enabled: true
    cache_ttl: "5m"          # 活跃会话缓存时间
    batch_size: 100          # 批量查询大小 